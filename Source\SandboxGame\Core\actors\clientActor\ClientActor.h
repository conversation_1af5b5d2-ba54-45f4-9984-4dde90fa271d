
#ifndef __CLIENTACTOR_H__
#define __CLIENTACTOR_H__

#include "SandboxGame.h"
#include "IClientActor.h"
#include "ChunkSave_generated.h"
#include "CorePrerequisites.h"
#include "ClientMacrosConfig.h"
#include "ActorTypes.h"
#include "world_types.h"
//#include "defdata.h"
#include "ClientActorDef.h"
#include "FindComponent.h"
#include "TriggerComponent.h"
#include "proto_pb/proto_ch.pb.h"

namespace Rainbow {
  class ISound;
  class Entity;
  class Model;
  class MovableObject;
  class BoxSphereBound;
  class GameObject;
  class Transform;
  class IActorBody;
  class MeshRenderer;
}

namespace MNSandbox {
	class Stream;
	class Component;
}

namespace game
{
	namespace hc 
	{
		class PB_GeneralEnterAOIHC;
	}
	namespace common
	{
		class PB_ActorCommon;
		class PB_ActorPhysicsCom;
		class PB_EffectComParticleInfo;
		class PB_SoundComInfo;
	}
}

#ifndef SAVE_BUFFER_BUILDER
typedef flatbuffers::FlatBufferBuilder SAVE_BUFFER_BUILDER; //tolua_export
#endif

class ActorLocoMotion;
class IActorLocoMotion;
class ActorBody;
class ActorVision;
class ActorAttrib;
class ClientPlayer;
class Chunk;
class BaseItemMesh;
class ThornBallComponent;
// 主机同步位置信息给客机的标记
enum NET_ACTOR_MOVE_FLAG {
	MF_POSITION  = 0x01, // 包含位置信息 
	MF_YAW_PITCH = 0x02, // 包含头部角度
	MF_FULL      = 0xff  // 全量值
};
class ActorEventListen;
class ActorManager;
class SwarmComponent;
class NavigationPath;
class ClientActorFuncWrapper;
class RiddenComponent;
class FireBurnComponent;
class CarryComponent;
class BindActorComponent;
class ActorBindVehicle;
class ActorInPortal;
class ActorUpdateFrequency;
struct RoleSkinDef;
struct MonsterDef;
struct ItemDef;
struct BlockDef;
class PackContainer;
class PhysicsComponent;
class SoundComponent;
class EffectComponent;
class ScriptComponent;
class ClientMob;
class ActorHanlde;
class ActorChunkPos;
class ModelComponent;
class ChargeJumpComponent;
class AttackedComponent;
class ActionAttrStateComponent;
class FallComponent;
class SkillComponent;
class ActorModelLerpComponent;
class EquipGridContainer;
class GridContainer;
class BackPackGrid;
struct GridCopyData;
class DieComponent;
class AIUpdateFrequency;
class ActorManagerInterface;
class IClientActorFuncWrapper;
class IActorAttrib;
class LivingAttrib;
class PlayerAttrib;
class FishingComponent;
class FindComponent;
class BlockEnvEffectsComponent;
class TemperatureComponent;
class RadiationComponent;
class ViewerComponentActor;
class TriggerComponent;
class ClientFlyComponent;
class ToAttackTargetComponent;

//GetComponent<...>开销比较大，常用的组件可以定义成成员变量来cache
//特别是在tick和update中调用的组件
#define DECLARE_CACHE_COMPONENT(type) \
    public:\
	type* getCached##type() { if (m_##type == nullptr) m_##type = GetComponent<type>(); return m_##type; }\
	void clearCached##type() { m_##type = nullptr; }\
    type* get##type(){return m_##type;}\
    void cache##type(type* comp){/*Assert(!m_##type);*/m_##type = comp;}\
    protected:\
    type* m_##type=nullptr;

class EXPORT_SANDBOXGAME ClientActor;
class ClientActor : public IClientActor //tolua_exports
{//tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientActor)
public:
	virtual long long GetCompatibleSaveID()  const override { return m_ObjId; };

	virtual ~ClientActor();

	Rainbow::AABB GetAABB() override;
	void PrepareAABB();
	static void getMergeCollideBox(Rainbow::AABB& boxMerge, Rainbow::AABB& box);

	int GetMGTNodeType() override {
		return getObjType();
	}
	void CreateEvent2();
	//tolua_begin
	ClientActor();
	ClientActor(bool empty);

	virtual void aiTick() {}
	void tickForFlyObject();
	void tickForAquaticObject();
	virtual bool  haveBtree() { return false; };
	virtual void prepareTick() override;
	virtual void tick() override;
	virtual void update(float dtime) override;
	virtual bool castShadow();
	virtual void enterWorld(World* pworld);
	virtual void leaveWorld(bool keep_inchunk);
	//tolua_end
	virtual void preMoveTick() {}
	virtual void afterMoveTick() {}

	virtual void release() { DecrementRef(); }
	virtual void addRef() { IncrementRef(); }
	int getRefCount() { return (int)GetRefCount(); }
	virtual void OnDirty() override;

	virtual void SetObjId(long long objid);

	virtual bool ProcessDie() { return false; }

	static long long GenNextObjId();
	static void ResetObjId(long long id = 0x100000000LL);
	static long long GetCurObjId();
	static long long PackObjId(long long id);
	static long long UnpackObjId(long long id);

	flatbuffers::Offset<FBSave::SectionActor> saveSectionActor(SAVE_BUFFER_BUILDER& builder, FBSave::SectionActorUnion actor_type, flatbuffers::Offset<void> actor);
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder);// = 0; actor从抽象类变成普通类
	virtual flatbuffers::Offset<FBSave::SectionActor> saveToNet(SAVE_BUFFER_BUILDER& builder);
	virtual void legacyLoad(bool& find, bool& ret, const FBSave::SectionActor* s, int version) override;
	virtual bool load(const void* srcdata, int version);// = 0; actor从抽象类变成普通类
	virtual bool loadFromNet(const void* srcdata, int version);

	virtual bool needSaveInChunk()
	{
		return true;
	}

	virtual void SetNeedSaveInChunk(bool needSaveInChunk)
	{
		return;
	}

	virtual void* saveToBuffer(size_t& buflen);
	static ClientActor* createFromBuffer(const void* buffer, int version);

	virtual void onAfterTick();
	virtual bool checkNeedClear();
	
	virtual void onCollideWithPlayer(ClientActor* player);
	virtual void collideWithActor(ClientActor* actor);
	virtual void onCollideWithActor(ClientActor* pActor);

	virtual void applyActorCollision(ClientActor* actor);
	virtual bool applyActorElasticCollision(ClientActor* actor);
	virtual bool applyActorObstructCollision(ClientActor* actor, ClientActor* player);
	virtual void setRotaionLimitAngle(float Ts) { m_TurningSpeed = Ts; }
	virtual float getRotaionLimitAngle() const { return m_TurningSpeed; }
	virtual bool canAttackWithItem()
	{
		return true;
	}
	virtual bool needCheckVisible()
	{
		return true;
	}
	virtual bool needWalkEffects() //移动对方块环境产生效果
	{
		return false;
	}
	virtual ACTOR_MOVEMODE_T getMoveMode()
	{
		return ACTORMOVE_NORMAL;
	}
	virtual bool preventActorSpawning()
	{
		return false;
	}
	virtual ATTACK_TARGET_TYPE getAttackTargetType()
	{
		return ATTACK_TARGET_OTHERS;
	}
	virtual bool canBeCollidedWith()
	{
		return m_bCanBeInteracted;
	}
	virtual bool managedByChunk()
	{
		return true;
	}

	virtual bool attackedFrom(OneAttackData& atkdata, ClientActor* attacker);

	virtual bool needSyncAttribChanges() override
	{
		return true;
	}

	virtual bool canBePushed();
	virtual void onClear()
	{
	}

	virtual void moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks);
	virtual void moveToPosition(const WCoord& pos, Rainbow::Quaternionf& rot, int interpol_ticks);

	virtual bool leftClickInteract(ClientActor* player)
	{
		return false;
	}
	
	virtual bool canDismount()
	{
		return true;
	}
	virtual bool canNavigation() override
	{
		return true;
	}
	virtual void syncAttr(int attrtype, float val);
	virtual float getRunWalkFactor() //>1.0表示在跑, 越大越快
	{
		return 1.0f;
	}
	virtual bool interact(ClientActor* pPlayer, bool onshift = false, bool isMobile = false);

	virtual bool rightClickUpInteract(ClientActor* pPlayer);

	//PC鼠标事件
	virtual bool mouseEventTrigger(game::ch::PCMouseKeyType keyType, game::ch::PCMouseEventType eventType);

	bool AttackedFromType(ATTACK_TYPE atktype, float atkpoints, ClientActor* attacker = NULL, bool bhit = false);

	//空间管理 update tick 时的顺序  actor->player->boss
	virtual int GetSpaceTickPriority() override{
		return 0;
	}

	virtual void SetLastAcotrTick(int count) override {
		m_LastActorTick = count;
	}

	virtual int GetLastAcotrTick() override {
		return m_LastActorTick;
	}

	virtual bool NeedTickForever() {
		return false;
	}

	virtual int GetJumpHighest()
	{
		return 1;
	}

	virtual int GetJumpLongest()
	{
		return 1;
	}

	void SetAsActorViewer(int r);//主动设置作为viewer 灯塔
	int GetAsActorViewerDist();

	virtual inline int GetLastAcotrUpdate() override {
		return m_LastActorUpdate;
	}

	virtual void SetLastAcotrUpdate(int count) override {
		m_LastActorUpdate = count;
	}

	virtual int GetLastAcotrSelect() override {
		return m_LastActorSelect;
	}

	virtual void SetLastAcotrSelect(int count) override {
		m_LastActorSelect = count;
	}

	virtual bool IsAutoClear()
	{
		return true;
	}

	virtual bool IsInWorld() override
	{
		return m_pWorld != NULL;
	}

	bool HandleLavaMovement();
	void SetAIJumping(bool b);
	void CreateNavPath();
	virtual int GetPathHideRange();
	virtual bool IsPotionApplicable(int buffid);
	virtual void SyncPosition(const WCoord& pos, int yaw, int pitch, float angle = 30.0f);
	void UpdateFireBurning();
	void NotifyPlayAnim2Tracking(int anim, bool include_me = false);
	void AddSkillCom();
	void activeSkill(const Rainbow::FixedString& id,long long targerid = -1);
	virtual bool IsExtremis()
	{
		return false;
	}
	bool OnHurtByActor()
	{
		return m_BeHurtTarget > 0 && getAge() < getBeHurtTimer() + 5 * 20;
	}
	void SetMoveDir(float x, float y, float z);				// 设置面向
	bool IsAbleGetPath(int x, int y, int z, int boundSize = 0);	//去除在受重力等情况下，是否能够寻找到 x y z点路线

	virtual void updateBodyByFullyCustomModel()
	{
	}
	virtual void updateBodyByImportModel()
	{
	}

	virtual void getCollideBox(CollideAABB& box);
	virtual void getMultiCollideBox(std::vector<CollideAABB>& boxs) override;
	void getBoundingBoxLocal(Rainbow::BoxSphereBound& bound);
	virtual void getHitCollideBox(CollideAABB& box);
	virtual void getViewBox(CollideAABB& box);
	virtual int getStepHeight() { return 0; }
	virtual int getNetSyncPeriod();

	virtual bool canAttackByItemSkill(int skillId, ClientPlayer* player) { return false; }
	virtual void resetRound() {};

	// 设置碰撞信息
	void setCollideBlockState(const WCoord& blockpos, int blockid);
	//IClientActor
	virtual bool isExistInCollide(const WCoord& blockpos) override;
	void clearCollideBlock();

	bool IsTriggerProjectile(); // 是否是投掷物
	bool IsTriggerCreature(); // 是否是触发器生物(包含NPC，怪物，动物)

	virtual void MotionHasChange() {}

	virtual void addHPEffect(float hp) {}
	virtual void addArmorEffect(float armor) {};
	virtual void changeBaseModel(std::string& strModelId, float fScale);

	virtual void handleTouch(ClientPlayer* player) {}; //没有产生其它交互后的处理
	virtual int  getAnimBodyId() { return -1; }
	//是否可以使用ActorLocoMotion code-by:hanyunqiang
	virtual bool canUseActorLocoMotion() { return false; }

	//重构版本新加
	virtual bool isSupportSaveExt() { return false; }
	virtual bool isSupportSyncExt() { return false; }
	virtual std::string getActorType() { return "ClientActor"; }
	virtual void save(jsonxx::Object& obj) {}
	virtual bool load(const jsonxx::Object& obj, int version) { return true; }
	virtual ClientActor* clone();

	virtual bool getOnRailState(WCoord& railknot, int& outindex, float& t, int& flags);

	virtual bool supportSaveToPB()
	{
		if (getObjType() == OBJ_TYPE_GAMEOBJECT)
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	virtual	int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb);
	
	//tolua_begin
	virtual bool isSleeping()
	{
		return false;
	}
	virtual bool isRestInBed()
	{
		return false;
	}
	virtual void teleportMap(int targetmap)
	{
	}
	virtual bool isInvulnerable(ClientActor* attacker)
	{
		return m_Invulnerable;
	}
	void setInvulnerable(bool b)
	{
		m_Invulnerable = b;
	}
	bool getInvulnerable()
	{
		return m_Invulnerable;
	}
	bool isUnsighted();
	void setUnsighted(bool isUnsighted);
	virtual bool isDead();
	virtual bool isOffLine();

	virtual bool needClear()
	{
		return m_NeedClearTicks >= 0;
	}

	bool alive()
	{
		return !isDead() && !needClear();
	}
	//IClientActor
	virtual void setNeedClear(int delay_ticks = 0) override;

	ActorManager* getActorMgr();
	virtual WCoord& getPosition() override;
	bool GetScreenPosition(int &x, int &y,bool inbox); // 获取对象在屏幕中的2D位置 只在客机生效

	virtual World* getWorld()
	{
		return m_pWorld;
	}

	World* GetWorld()
	{
		return m_pWorld;
	}

	virtual void setPosition(const WCoord& pos);
	float getBrightness(float t);

	void setBeHurtTarget(ClientActor* pActor);
	ClientActor* getBeHurtTarget();
	int getBeHurtTimer()
	{
		return m_BeHurtTimer;
	}

	void setBeAtk(ClientActor* pActor);
	ClientActor* getBeAtk();
	int getBeAtkTimer()
	{
		return m_BeAtkTime;
	}
	int getLiveTicks()
	{
		return m_LiveTicks;
	}

	virtual WCoord getEyePosition();
	virtual WCoord getChestPosition();

	virtual int getViewDist() override { return 3200; }
	virtual void setViewDist(int dist) {}

	virtual bool isInWater() override;
	bool isInLava();

	virtual NavigationPath* getNavigator() override;
	
	void rotateBodyToActor(ClientActor* pActor);
	void setLookAt(ClientActor* pActor, float deltaLookYaw, float deltaLookPitch);
	void setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/, bool isSync = true);
	virtual double getSquareDistToPos(double x, double y, double z); //距离的平方
	double getSquareDistToActor(ClientActor* pActor); //距离的平方
	void	setLookAt(int x, int y, int z, float deltaLookYaw, float deltaLookPitch);
	void	setLookAt(float yaw, float pitch, float deltaLookYaw, float deltaLookPitch);
	void	setHeadLookAt(float yaw, float pitch, float deltayaw, float deltapitch);
	//============= 有且仅有，头部转向 =============
	void keepLookAt(const ClientActor& pActor, float deltaYaw, float deltaPitch);
	void keepLookAt(int x, int y, int z, float deltaYaw, float deltaPitch);
	void keepLookAt(float yaw, float pitch, float deltaYaw, float deltaPitch);
	void keepLookAtByDirection(float x, float y, float z, float deltaYaw, float deltaPitch);
	void stopLookAt();
	//============= 有且仅有，头部转向 =============
	virtual float getVerticalFaceSpeed();
	virtual int getChestHeight() { return 0; };
	void leapTarget(WCoord& pos, float motionY);
	void faceActor(ClientActor* target, float yawrange, float pitchrange);
	void faceWorldPos(WCoord pos, float yawpseed, float pitchspeed);

	virtual void setScale(float s) {}
	virtual float getScale() { return 1.0f; }
	virtual void updateScale() {}
	virtual void setCustomScale(float s) {}
	virtual float getCustomScale() { return 1.0f; }

	virtual long long getObjId() override { return m_ObjId; }
	virtual int getObjType() const; //= 0;

	virtual bool isBurning();
	bool isWet();

	virtual int getEyeHeight();
	virtual void getPosition(int& x, int& y, int& z) override;

	virtual void jumpOnce();
	void syncJump(); //同步跳跃动作
	virtual void kill() override;

	virtual void playAnim(int seq, bool include_me = false, int loop = -1) override;
	virtual bool playAnimById(int id, int inputloopmode = -1, int playLayer = -1) override;
	virtual bool playAnimByIdNew(int id, int inputloopmode = -1, int playLayer = -1, float crossfade = -1);

	void setPathHide(int type)
	{
		m_PathHide = type;
	}

	int getAge()
	{
		return m_LiveTicks;
	}

	virtual void resetAllFlags(unsigned int flags)
	{
		m_Flags = flags;
	}
	virtual void setFlagBit(int ibit, bool b)
	{
		if (ibit < 32) {
			// 使用原有m_Flags
			if (b) m_Flags = m_Flags | (1 << ibit);
			else m_Flags = m_Flags & ~(1 << ibit);
		} else {
			// 使用扩展的m_FlagsEx，并调整位索引
			int adjustedBit = ibit - 32;
			if (b) m_FlagsEx = m_FlagsEx | (1 << adjustedBit);
			else m_FlagsEx = m_FlagsEx & ~(1 << adjustedBit);
		}
	}
	inline bool getFlagBit(int ibit)
	{
		if (ibit < 32) {
			return (m_Flags & (1 << ibit)) != 0;
		} else {
			int adjustedBit = ibit - 32;
			return (m_FlagsEx & (1 << adjustedBit)) != 0;
		}
	}
	inline void setAnimFlagBit(int ibit, bool b)
	{
		if (b) m_AnimFlags = m_AnimFlags | (1 << ibit);
		else m_AnimFlags = m_AnimFlags & ~(1 << ibit);
	}
	inline bool getAnimFlagBit(int ibit)
	{
		return (m_AnimFlags & (1 << ibit)) != 0;
	}
	inline void clearAnimFlatBit()
	{
		m_AnimFlags = 0;
	}

	inline void setSitting(bool b)
	{
		setFlagBit(ACTORFLAG_SIT, b);
	}
	virtual bool getSitting()
	{
		return getFlagBit(ACTORFLAG_SIT);
	}
	virtual void setSneaking(bool b)
	{
		setFlagBit(ACTORFLAG_SNEAK, b);
	}
	bool getSneaking()
	{
		return getFlagBit(ACTORFLAG_SNEAK);
	}
	virtual void setFlying(bool b)
	{
		setFlagBit(ACTORFLAG_FLY, b);
	}
	
	bool getRun()
	{
		return getFlagBit(ACTORFLAG_RUN);
	}
	virtual void setRun(bool b)
	{
		setFlagBit(ACTORFLAG_RUN, b);
	}
	bool isRun();

	virtual bool getFlying()
	{
		return getFlagBit(ACTORFLAG_FLY);
	}
	
	int getCurPlaceDir();
	void setFaceDir(float x, float y, float z);
	void getFaceDir(float& x, float& y, float& z);

	/*param：
			needSync [true:需要同步给其他人，false:不需要同步]
	*/
	virtual void setFaceYaw(float yaw, bool needSync = false);

	virtual float getFaceYaw() override;
	void setFacePitch(float pitch);
	virtual float getFacePitch() override;

	//m_NavigationRotationPitch
	virtual float getNavigationFacePitch() override;

	int getAiInvulnerableProb() { return m_nAiInvulnerableProb; }
	void setAiInvulnerableProb(int prob) { m_nAiInvulnerableProb = prob; }
	void setDodge(int dodge);//仅限开发者使用，正常游戏逻辑请使用setAiInvulnerableProb dodge[0, 100]
	virtual void setPosition(int x, int y, int z);
	virtual void tryMoveToActor(long long targetobj, float speed);
	virtual void tryMoveToPos(int x, int y, int z, float speed);

	virtual bool isVehicleController();	//坐在驾驶座或者坐在可控制部件的乘客座上
	virtual void tryMoveTo(int x, int y, int z, float speed, bool canControl, bool needSync = false, bool showtip = false);
	virtual int GetItemId() override
	{
		return 0;
	}

	virtual int getMass();
	//IClientActor
	virtual void setMotionChange(float x, float y, float z, bool addmotion = false, bool changepos = false) override
	{
		setMotionChange(Rainbow::Vector3f(x, y, z), addmotion, changepos);
	}

	// 获取actor尺寸
	virtual void getBodySize(float& width, float& height) override;

	virtual bool playActForTrigger(int act) { return true; } // 播放动画

	virtual PackContainer* getBags()
	{
		return NULL;
	}
	virtual bool haveBags();

	virtual int getBagsCount();

	virtual BackPackGrid* getBagsGrid(int index);
	virtual BackPackGrid* getEquipGrid(EQUIP_SLOT_TYPE t);
	virtual BackPackGrid* getEquipGridWithType(EQUIP_SLOT_TYPE t);

	virtual int getItemCount(int itemid);

	virtual BackPackGrid* getGridByItemID(int itemid);
	virtual int addItemByGridCopyData(const GridCopyData* data);
	virtual int addItemByGrid(const BackPackGrid* data);

	virtual bool checkBagPutItem(int resid, int num);
	virtual void applyEquips(EQUIP_SLOT_TYPE t);
	virtual void removeBagItemByCount(int itemid, int num);
	virtual GridContainer* getGridContainer()
	{
		return m_pGridContainer;
	}
	virtual EquipGridContainer* getEquipGridContainer()
	{
		return m_pEquipGridContainer;
	}
	virtual void showNickName(bool bshow);
	virtual void setNickName(char* nickname);
	void addIdleAnimal(int animal, int prop);

	virtual void setFallGround(bool b);
	virtual bool getFallGround();

	virtual unsigned short getCurMapID()
	{
		return m_CurMapID;
	}
	unsigned short GetCurMapID()
	{
		return m_CurMapID;
	}
	virtual void setMotionChange(const Rainbow::Vector3f& motion, bool addmotion = false, bool changepos = false, bool sync_pos=true);
	void setxyzMotionChange(float x, float y, float z, bool addmotion , bool changepos);
	virtual bool isMotionChange()
	{
		return m_MotionChange;
	}
	virtual bool isChangePos() override
	{
		return m_isChangePos;
	}
	virtual void clearMotionChange() override
	{
		m_MotionChange = false;
	}

	void setReverse(bool reverse);

	virtual bool getReverse()
	{
		return m_bReverse;
	}

	virtual int getDefID() override
	{
		return 0;
	}

	void addAttChangeFlag(int flag)
	{
		m_iAttChangeFlag |= flag;
	}

	bool hasAttChanged(int flag)
	{
		return ((m_iAttChangeFlag & flag) > 0);
	}

	virtual bool canControl()
	{
		return true;
	}

	virtual void setCanControl(bool b)
	{
		return;
	}

	virtual ClientActor* getShootingActor() { return NULL; }

	void setMasterObjId(long long id) { m_llMasterObjId = id; }
	long long getMasterObjId() { return m_llMasterObjId; }
	virtual void playEffect(ACTORBODY_EFFECT fx);
	virtual void stopEffect(ACTORBODY_EFFECT fx);
	
	/*
		添加播放特效接口
	*/
	bool	playMotion(const char* name, int motion_class = 0, bool force_play = false, float loopPlayTime = -1.0f);
	/*
		添加停止特效接口
	*/
	virtual void	stopMotion(const char* name) override;
	void	stopMotion(int motion_class);
	std::string getActorName();

	virtual bool getDirectSwitchAni() { return false; }
	virtual void setDirectSwitchAni(bool b) {}

	virtual bool isPlayer() { return false; } // 返回actor是否是玩家 2021.12.24 by huanglin
	MonsterDef* getMonsterDef() const { return m_monsterDef; }
	virtual void SetBindChunk(bool toggle);

	//本来是玩家特有的,但是很多地方都是clientactor调用,为了减少转换指针次数,这个方法改成继承的
	virtual bool getUsingEmitter() { return false; }

	void GetAABBInfo(Rainbow::Vector3f& size, Rainbow::Vector3f& anchor);
	Rainbow::Vector3f GetAABBCenter();
	bool TryGetPathToPos(int x, int y, int z, int boundSize = 0);	//尝试获得到达目标位置的路线
	//技能编辑器
	// 播放动画
	virtual bool skillPlayAnim(int tpsanimid, int fpsanimid, int isLoop, int playLayer = -1);
	virtual bool skillStopAnim(int tpsanimid, int fpsanimid, bool isReset = false);
	void SetFirstSearch(bool firstSearch);
	bool GetGunInfo(int& gunQuality, int& gunLevel, int& featureCount);
	/*
		获取缓存的FindComponent（这个组件lua调用非常频繁）
	*/
	FindComponent* GetFindComponent() { return m_pFindComp; }
	//tolua_end
	bool GetFirstSearch();
	bool findNearestCustomBlock(WCoord& blockpos, int blockRange, bool(*valid)(ClientActor*, WCoord&, int, std::string) = NULL, std::string extend = "", bool randomsort = false);
	bool findNearestSPBlock(WCoord& blockpos, int blockRange, int SPid, bool(*valid)(ClientActor*, WCoord&, int, std::string) = NULL, std::string extend = "", bool randomsort = false);
	void setNetMoveFlag(int flag) { m_NetMoveFlag = flag; }
	int getNetMoveFlag() { return m_NetMoveFlag; }
	void clearNetMoveFlag() { m_NetMoveFlag = MF_FULL; }

	virtual void onEnterSection(Section* section);
	virtual void onLeaveSection(Section* section);
	virtual void updateSectionIsDisplay(Section* section);
	/*
		进行Actorbody裁剪
		Mob类单独 设置为视野范围
	*/
	virtual void setBodyCull();
	void setWorld(World* world) { m_pWorld = world; }

	void setBeHurtTargetID(WORLD_ID n);
	virtual WORLD_ID getBeHurtTargetID();

    //刷新对象在八叉树中的位置，不刷新的话花会导致对象在用八叉树查找的时候找不到
    void UpdateOctree();

	void SetOutline(UInt32 value);

	bool HasWeaponPlaying(int animid);
	void PlayWeaponAnim(int animid, int loop = -1, float speed = 1.f);
	void StopWeaponAnim(int animid);
	void PlayWeaponMotion(const char* name, bool reset = true, int mclass = 0, float scale = 1.0f);
	void StopWeaponMotion(int mclass);

	//播放武器动画
	virtual bool skillPlayToolAnim(int animid);
	virtual bool skillStopToolAnim(int animid);

	virtual bool SetLocoMotionType(int locotype, bool isNo);
	
	bool PlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class = 0);
	bool HasPlayingEffect(const char* path);
	virtual bool skillPlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale,bool isLoop, int motion_class = 0);
	virtual bool skillStopBodyEffect(const char* path);

	DieComponent* SureDieComponent();
	
#pragma region IClientActor
	virtual ClientActor* GetActor() override
	{
		return this;
	}
	virtual IClientPlayer* CastToPlayer() override;
	virtual IClientActor* IClone() override;
	virtual ActorManagerInterface* GetActorMgrInterface() override;
	virtual bool CanNavigate() override;
	virtual void ClearMoveForward() override;
	virtual IClientActorFuncWrapper* getActorFuncWrapper() override;
	virtual WCoord GetActorPosition() override;
	virtual int GetPathableYPos() override;
	virtual bool HasLivingLocoMotion() override;
	virtual bool HasTrixenieLocomotion() override;
	virtual bool CanAttackByItemSkill(int skillId, IClientActor* player) override;
	/// <summary>
	/// 
	/// </summary>
	/// <param name="type"> 1代表LivingLocoMotion 2代表TrixenieLocomotion </param>
	virtual void HandleLocomotionForNavigation(int type, bool noPath) override;

	virtual void SetJumpToTarget(const WCoord& target) override;

	virtual void SetTarget(const WCoord& target, float speed) override;

	virtual IActorLocoMotion* getILocoMotion() override;
	virtual bool HasPhysActor() override;
	virtual IActorAttrib* GetIActorAttrib() override;

	virtual MNSandbox::Component* getActorComponent(ComponentType type) override;

	virtual LivingAttrib* getLivingAttrib()
	{
		return nullptr;
	}
	virtual PlayerAttrib* getPlayerAttrib()
	{
		return nullptr;
	}

	virtual bool IsClientActorProjectile() override;
	virtual bool IsActorVehicleAssemble() override;	
	virtual bool AttackedFrom(OneAttackData& atkdata, IClientActor* inputattacker) override;
	virtual void TriggerScriptComponent(const std::vector<int>& enterList) override;
#pragma endregion

	//开启半透效果
	virtual void SetXrayEffectEnable(bool enable) { 
		m_bXrayEffectEnable = enable; 
		UpdateXrayEffectEnable(); 
	}
	virtual bool IsXrayEffectEnable() { return m_bXrayEffectEnable; }
	virtual void UpdateXrayEffectEnable() { }
	void SetIsEdit(bool isEdit);
	bool GetIsEdit()
	{
		return m_bUgcSelect;
	}

	bool HasMeshRender();
#ifdef ENABLE_PLAYER_CMD_COMMAND
	//gm调试绘制人物包围盒线框所使用的
public:
	static bool ms_enableDebugWrapBpx;
	Rainbow::Vector4f m_selfAtkBoxInfo = Rainbow::Vector4f(0 ,0, 0, 0) ; //每个角色自己的攻击包围盒
private:
	virtual void gmDrawWrapBox();
#endif
public:
	//刺球
	ThornBallComponent* sureThornBallComponent();
	ThornBallComponent* getThornBallComponent();
protected:
	int m_NetMoveFlag;  // 主机同步位置信息给客机的标记, 使用 NET_ACTOR_MOVE_FLAG 的位组合, 2022.07.12 by huanglin
public:
	virtual void getsyncData(jsonxx::Object& data);
	virtual void setsyncData(jsonxx::Object& data);
	
	virtual bool isActorVehicleAssemble() override{ return false; }
	virtual void AttackFromSharkBite(ClientActor* actor) {}
	//tolua_begin
	void setBehitDisable(bool b) { m_BehitDisable = b; }
	bool getBehitDisable() { return m_BehitDisable; }
	void setCanCollide(bool b) { m_CanCollide = b; }
	bool getCanCollide() { return m_CanCollide; }
	//tolua_end
	//const char* getSearchName() {	return m_searchName; }
	void setMovingLightEnable(bool value);
	void setRoleSkinId(const int id);
	virtual void setMonsterId(const int id);
	void setItemId(const int id);
	void setBlockId(const int id);
	virtual bool isStopBodyRotation() { return false; }
	virtual int getLastHeadIconItemID() { return 0; }
	virtual void setLastHeadIconItemID(int itemID) {}
	virtual bool getClimbing() { return false; }
	virtual void setClimbing(bool b) {}
	virtual int getBufferId() { return 0; }
	virtual float GetAccumulateDtime() override
	{
		return m_AccumulateDtime;
	}
	virtual void SetAccumulateDtime(float value) override
	{
		m_AccumulateDtime = value;
	}
public:
	//tolua_begin
	//是否在预制场景中创建的actor
	bool m_IsInPrefabScene;
	//tolua_end

	static unsigned int m_CurActorFrame;
	static bool s_AABBUseHitBox;
	int m_Mass;
	int m_NeedClearTicks;
	ActorBody* m_Body;
	int m_LiveTicks = 0;
	float m_AccumulateDtime;

private:
	int  m_nAiInvulnerableProb;
	WORLD_ID m_BeHurtTarget;
	//被什么生物攻击
	WORLD_ID m_BeAtkTarget;
	//被攻击时间
	int m_BeAtkTime;
	bool m_Invulnerable;
	bool m_BehitDisable;	//是否不可被击打
	bool m_CanCollide;//是否可以被推动
	bool LocomotionPosGet(Rainbow::Vector3f& value) const;
	void LocomotionPosSet(const Rainbow::Vector3f& value);
	void SaveToPBActorObj(const void* actorObj);
	bool LoadFromPBChildren(char *buff, int buffLen, std::vector<ClientActor*>& childrenVec, std::vector<ClientActor*>& childrenVecFail);
protected:
	virtual void OnClearNotify() override;
	virtual void bePushWithPlayer(ClientActor* player);
	void initBornBuff();
	flatbuffers::Offset<FBSave::ActorCommon> saveActorCommon(SAVE_BUFFER_BUILDER& builder);
	bool loadActorCommon(const FBSave::ActorCommon* srcdata);
	//新版本增加
	void saveActorCommon(jsonxx::Object& obj);
	bool loadActorCommon(const jsonxx::Object& obj);
	int savePBActorCommon(game::common::PB_ActorCommon* actorCommon);
	int loadPBActorCommon(const game::common::PB_ActorCommon& actorCommon);
	// 加载、释放行动者事件监听器
	virtual void LoadActorEventListen(); // 用于继承，不同对象使用不同类型的监听器
	//组件内容
	
	flatbuffers::Offset<FBSave::ActorObj> SaveActor(SAVE_BUFFER_BUILDER& builder);
	bool LoadChildren(char *buf, int bufLen, std::vector<ClientActor*>& children, std::vector<ClientActor*>& childrenFail);
	bool LoadActor(const FBSave::ActorObj * actorObj, const ClientActor* pParentActor = nullptr);

	void SaveObjectChildAndComponent(SAVE_BUFFER_BUILDER& builder, std::string& scriptComponent, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& ugccomponentsOffset, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& childrenOffset);
	void LoadObjectChildAndComponent(const flatbuffers::String* scriptcomponent, const flatbuffers::Vector<int8_t>* ugccomponents, const flatbuffers::Vector<int8_t>* children);
	void SaveToPBChildAndComponent(std::string& modelcomponent); 
	void LoadFromPBChildAndComponent(const std::string* modelcomponent);
	Rainbow::AABB PrepareAABBInner();
public:
	static MNSandbox::ReflexClassParam<ClientActor, Rainbow::Vector3f>  R_LocomotionPos;
	static MNSandbox::ReflexClassParam<ClientActor, bool> R_Visible;
	Rainbow::Entity *getEntity();
	//IClientActor
	virtual Rainbow::IActorBody* GetIBody() override;
	virtual MNSandbox::Component* getActorSoundComponent() override;
	virtual MNSandbox::Component* getActorAttackedComponent() override;
	virtual MNSandbox::Component* getLocoMotionComponent() override;
	virtual MNSandbox::Component* getAttribComponent() override;
	//tolua_begin
	ActorBody *getBody();
	ActorVision *getVision();
	ActorLocoMotion *getLocoMotion();
	const ActorLocoMotion* getLocoMotion() const;
	ActorAttrib *getAttrib();
	ClientActorFuncWrapper *getFuncWrapper();
	RiddenComponent *getRiddenComponent();
	FireBurnComponent* getFireBurnComponent();
	PhysicsComponent* getPhysicsComponent() { return m_pPhysicsComponent; }
	SoundComponent* getSoundComponent() { return m_pSoundComponent; }
	EffectComponent* getEffectComponent() { return m_pEffectComponent; }
	//FallComponent*   getFallComponent() { return m_pFallComponent; }
	SkillComponent* getSkillComponent() { return m_pSkillComponent; }
	ScriptComponent* getScriptComponent() { return m_pScriptComponent; }
	ModelComponent* getModelComponent() { return m_pModelComponent; }
	std::string GetComponentFromC(); //从c++组件获取lua组件信息
	FireBurnComponent* sureFireBurnComponent();
	RiddenComponent* sureRiddenComponent();
	//tolua_end

	SwarmComponent* getSwarmComponent();
	ActorChunkPos* getChunkPosComponent();
	ChargeJumpComponent* getChargeJumpComponent() { return m_pChargeJumpComponent; }
	ActionAttrStateComponent* getActionAttrStateComponent() { return m_pActionAttrStateComponent; }

	AttackedComponent* getAttackedComponent();
	CarryComponent* getCarryComponent() { return m_pComponentCarry; }
	CarryComponent* sureCarryComponent();

	//给子类重新挂载
	virtual ActorInPortal*  getActorInPortal();
	virtual ActorInPortal* sureActorInPortal();
	virtual ActorBindVehicle* getActorBindVehicle();
	ActorBindVehicle* m_pActorBindVehicle;
	virtual BindActorComponent* getBindActorCom();
	BindActorComponent* m_pBindActorComponent;
	virtual ActorUpdateFrequency* getUpdateFrequencyCom();
	virtual AIUpdateFrequency* getAIUpdateFrequencyCom();
	bool IsSectionDisplay() const { return m_bSectionIsDisplay; };
	bool IsOnViewFrustum();

	void BindLocoMotion(MNSandbox::SceneComponent* pComponent);
	void BindVision(MNSandbox::SceneComponent* pComponent);
	virtual void BindNavigationPath(MNSandbox::SceneComponent* pComponent) override;
	void BindAttrib(MNSandbox::SceneComponent* pComponent);
	void BindFuncWrapper(MNSandbox::SceneComponent* pComponent);
	void BindRiddenComponent(MNSandbox::SceneComponent* pComponent);
	void BindFireBurnComponent(MNSandbox::SceneComponent* pComponent);
	void BindCarryComponent(MNSandbox::SceneComponent* pComponent);
	void BindSwarmComponent(MNSandbox::SceneComponent* pComponent);
	void BindSoundComponent(MNSandbox::SceneComponent* pComponent);
	void BindEffectComponent(MNSandbox::SceneComponent* pComponent);
	//void BindFallComponent(MNSandbox::SceneComponent* pComponent);
	void BindSkillComponent(MNSandbox::SceneComponent* pComponent);
	void BindActionAttrStateComponent(MNSandbox::SceneComponent* pComponent);
	void BindAttackedComponent(MNSandbox::SceneComponent* pComponent);
	void BindPhysicsComponent(MNSandbox::SceneComponent* pComponent);
	void BindChargeJumpComponent(MNSandbox::SceneComponent* pComponent);
	void BindChunkPosComponent(MNSandbox::SceneComponent* pComponent);
	void BindScriptComponent(MNSandbox::SceneComponent* pComponent);
	void BindModelComponent(MNSandbox::SceneComponent* pComponent);
	void BindActorModelLerpComponent(MNSandbox::SceneComponent* pComponent);
	void BindActorGridComponent(MNSandbox::SceneComponent* pComponent);
	void BindActorEquiptComponent(MNSandbox::SceneComponent* pComponent);
	
	/**
	@brief	实体对象节点接口
	*/
	// 实例化一个actor
	static ClientActor* Instantiate(const jsonxx::Object& obj);
	//初始化ClientActor
	void InitActor(int iModelType, const std::string& sModelPath, int extraData = 0);
	//获取引擎对象
	virtual Rainbow::GameObject* GetGameObject();
	//获取引擎Transform
	virtual Rainbow::Transform* GetTransform();
	//是否由物理引擎驱动
	bool IsPhysics();
	//平台上的角色
	void GetPlatformPlayers(std::vector<long long>& vPlayers);
	//更新actor的Transform
	void UpdateGameObjectTransform();
	//重置碰撞
	void ResetActorCollider(bool saveMesh = false);

	bool SetNodeParentReal(ClientActor* parent, bool bIsLoad);
	//绑定模型父子关系（运动时，平滑处理用）
	void BindModelParent();
	//获取顶层actor
	ClientActor* GetAncestor(); 
	Rainbow::AABB GetMergeAABB();
	void SetMergeAABB(const Rainbow::AABB& aabb) { m_mergeAABB = aabb; }
	virtual void Destroy();

	//tolua_begin
	//设置节点的父子关系
	bool SetNodeParent(ClientActor* parent, bool bIsLoad = false);
	//是否空节点
	void SetEmpty(bool empty);
	virtual bool IsEmpty() override{ return m_bEmpty; }
	
	bool IsMeshRenderEnabled();
	void EnableMeshRender(bool bEnable = true);
	bool IsBaseModelType(int modeltype, int extraData);
	void EnableBatch(bool bEnable);
	bool IsEnableBatch() {
		return m_bEnableBatch;
	}
	void SetLogicVisible(bool bVisible);
	bool IsLogicVisible() {
		return m_LoginVisible;
	}
	Rainbow::MeshRenderer* GetMeshRender();
	Rainbow::GameObject* GetMeshRenderGO();
	bool CanBatch();
	std::string GetModelPath();
	int GetModelType(); 
	int GetModelExtraData();
	void rotateBodyTo(const WCoord& target, bool sync);
	/**
	@brief	模型动画相关
	*/
	//设置模型 iModelType:ModelType_Normal
	void SetModel(int iModelType, const std::string& sModelPath, int iExtraData = -1, bool saveMesh = false);
	virtual void SetModelActorBody(int iModelType, const std::string& sModelPath, int iExtraData = -1, bool saveMesh = false);
	//移除actorbody
	void RemoveActorBody(bool removeModelComp = true);
	//设置模型纹理，obj类型除外
	void SetModelTexture(const char* sType, const char* sTextPath, int nSubMeshIndex = -1, bool forceMainTex = false, bool isCubeMap = false);
	//设置材质，transparencyShadowFlag反复设置会引起崩溃，暂时屏蔽设置
	void SetModelMaterial(const std::string& sPath, bool transparencyShadowFlag = false, int nSubMeshIndex = -1);
	//设置材质,接收一个matType枚举，0，不透明材质，1，半透材质， 2，遮罩材质
	void SetModelMaterial(int matType, bool transparencyShadowFlag = false, int nSubMeshIndex = -1);
	//设置覆盖色
	void SetOverlayColor(float r, float g, float b);
	//设置材质中的向量参数（如位置、颜色）
	void SetModelMatVector(const char* sType, const Rainbow::Vector4f &vec4, int nSubMeshIndex = -1);
	//设置材质中的float参数
	void SetModelMatFloat(const char* sType, float val, int nSubMeshIndex = -1);
	//设置材质中关键字参数的开启关闭
	void SetModelMatKeyword(const char* sType, bool flag);
	//设置材质的裁剪模式 双面0，正面1，背面2		
	void SetModelMatCullMode(int type);
	//设置实体的自动平铺参数
	void SetModelMatAutoTiling(bool flag, int type, int nSubMeshIndex = -1);
	//更新材质的uv参数，保证自动平铺正确
	void UpdateAutoTiling();
	//更新材质的明暗信息（主要用于夜晚的变暗效果）
	void UpdateModelMatLightData();
	//设置透明度
	void SetModelMatTransparent(float val);
	// IClientActor
	//判断模型是否加载完成
	virtual bool IsLoadModelFinished() override;

	/**
	@brief	Transform相关
	*/
	//设置实体的位置
	virtual void SetWorldPosition(float x, float y, float z, bool igoreLerp=false) override;
	//获取实体的位置
	virtual const Rainbow::Vector3f GetWorldPosition();
	//设置实体相对父节点位置
	void SetLocalPostion(const Rainbow::Vector3f& pos);
	void SetLocalPostion(float x, float y, float z);
	//获取实体相对父节点位置
	const Rainbow::Vector3f GetLocalPosition();
	//设置实体相对父节点位置（相对父节点坐标轴）
	void SetLocalPostionFromParentAxis(float x, float y, float z);
	//获取实体相对父节点位置（相对父节点坐标轴）
	const Rainbow::Vector3f GetLocalPositionFromParentAxis();

	//设置实体相对父节点的旋转
	void SetLocalRotation(const Rainbow::Quaternionf& rotation);
	void SetLocalRotationEuler(float x, float y, float z);
	//获取实体相对父节点的旋转
	const Rainbow::Quaternionf GetLocalRotation();
	Rainbow::Vector3f GetLocalRotationEuler();
	//设置实体的旋转
	void SetWorldRotation(const Rainbow::Quaternionf& rotation);
	void SetWorldRotationEuler(float x, float y, float z);
	//获取实体的旋转
	const Rainbow::Quaternionf GetWorldRotation();
	Rainbow::Vector3f GetWorldRotationEuler();

	//设置实体相对于父节点的缩放
	void SetLocalScale(const Rainbow::Vector3f& scale);
	void SetLocalScale(float x, float y, float z);
	//获取实体相对于父节点的缩放
	const Rainbow::Vector3f GetLocalScale();
	//设置实体的缩放
	void SetWorldScale(const Rainbow::Vector3f& scale);
	void SetWorldScale(float x, float y, float z);
	//获取实体的缩放
	const Rainbow::Vector3f& GetWorldScale();
	//父Actor的WorldScale
	bool GetParentActorWorldScale(Rainbow::Vector3f& worldscale);

	//绕着某个点旋转
	void SetRotationAroundPosition(float pitch, float yaw, float roll, int posx, int posy, int posz);
	void SetLocalAroundPosition(float pitch, float yaw, float roll, int posx, int posy, int posz);


	bool CalLocalRotAndPosByAroundMove(float pitch, float yaw, float roll, const Rainbow::Vector3f posrelatively, Rainbow::Quaternionf &retrot, Rainbow::Vector3f &retpos);
	bool CalRotAndPosByAroundMove(float pitch, float yaw, float roll, const Rainbow::Vector3f posrelatively, Rainbow::Quaternionf& retrot, Rainbow::Vector3f& retpos);
	bool useNewLerpModel();
	static bool isNewLerpModel();

	const Rainbow::Vector3f InverseTransformPoint(float posx, float posy, float posz);
	//Transform同步Locomotion
	void OnTransformUpdate();

	//面朝某个位置,pos(世界坐标)
	void FaceWorldPos(const Rainbow::Vector3f& pos);

	/**
	@brief	编辑器暂停恢复
	*/
	void Pause();
	void Resume();
	bool IsPause() { return m_bPause; }

	/**
	@brief	沙盒逻辑相关
	*/
	//是否是静态物（静态物不更新locomotion）
	void SetStatic(bool b) { m_bStatic = b; }
	bool IsStatic() { return m_bStatic; }

	/**
	@brief	是否被运动器驱动
	*/
	void SetControlByScript(bool b);
	virtual bool IsControlByScript();

	//新老actor区分
	virtual bool IsObject() { return getObjType() == OBJ_TYPE_GAMEOBJECT; }

	//设置是否能碰撞和交互
	void SetCanBeInteracted(bool b);

	ModelComponent* SureModelComponent();

	//获取actor的尺寸
	Rainbow::Vector3f GetlocalAABBExtent();

	void SetIgnoreUpdateFrequencyCtrl(bool b);

	void SendActorObjMsg(const char* script);
	//是否运动器相关接口
	void SetAncestorMoveable(bool isAncestorMoveable);
	void SetSelfMoveable(bool isMoveable);
	//自己是否运动器
	bool IsSelfMoveable();
	//祖先是否运动器
	bool IsAncestorMoveable();
	//不管祖先还是自己，有一个是就是运动器
	bool IsMoveable();
	virtual bool IsTickForever() { return false; }
	virtual bool SetTickForever(bool bForever) { return false; }
	//tolua_end

	void SetIgnoreUpdateFrequencyCtrlInner(bool b, int flag);
	bool IsIgonreUpdateFrequencyCtrl() { return m_bIgnoreUpdateFrequencyCtrl; }

	bool LoadFromPBActorObj(const void* actorObj);
	void LoadFromPBActorObjSimple(const void* actorObj);
	void SetIsParent(bool flag);
	bool GetIsParent() { return m_bParent; }
	void SetParentWID(long long wid);
	virtual long long GetParentWID() { return m_ParentWID; }
	void OnEnterChildCull();
	void CheckChildCull(bool bCull);

	virtual ClientFlyComponent* getClientFlyComponent() { return nullptr; }
	virtual ToAttackTargetComponent* getToAttackTargetComponent() { return nullptr; }
	virtual TemperatureComponent* getTemperatureComponent() { return nullptr; }
	virtual BlockEnvEffectsComponent* getBlockEnvEffectsComponent() { return nullptr; }
	virtual RadiationComponent* getRadiationComponent() { return nullptr; }
	
	//获取模型的世界包围盒
	void GetModelWorldBox(Rainbow::AABB& box);
	//重置盒子
	void ResetHitBox();
	bool IsDelayInited() { return m_bDelayInit; }
	game::common::PB_ActorPhysicsCom* GetPhysicsComPB() { return m_pPBActorPhysicsCom; }

	//脚本组件、子节点展开等处理
	void DelayInitData(bool force);
	void DelayInitDataRecursively();

	void SetNoDieComponent(bool flag) { m_bNoDieComponent = flag; }
public:
	DECLARE_CACHE_COMPONENT(FindComponent)
	DECLARE_CACHE_COMPONENT(TriggerComponent)

	virtual FishingComponent* getFishingComponent();
private:
	/**
	@brief	实体对象节点接口
	*/
	//创建actorbody
	ActorBody* NewActorBody(int iModelType, const std::string& sModelPath, int iExtraData);
	//初始化actorbody
	void InitActorBody(ActorBody* body, int iModelType, const std::string& sModelPath, int iExtraData, bool saveMesh = false);
	//设置物理盒子大小
	void ResetShapeSize();
	//刷新子节点scale
	void UpdateChildrenWorldScale();
	//设置特效缩放
	void ResetEffectComScale();

	void UpdateLocalPosition();

	void AddUgcComponent(void *fbs, bool &needLuaComponent);

	/**
	@brief	实体对象节点新增属性
	*/
	Rainbow::AABB m_preAABB = Rainbow::AABB::zero; //监视AABB变化
	Rainbow::AABB m_mergeAABB = Rainbow::AABB::zero; 
	Rainbow::GameObject* m_pGameObject;//实体对象的GameObject
	long long m_ParentWID = 0; //父节点wid, 0 表示无父节点，-1 区分是否在prefab scene
	Rainbow::Vector3f m_Scale = Rainbow::Vector3f::one;//模型缩放
	Rainbow::Vector3f m_LocalScale = Rainbow::Vector3f::one;
	Rainbow::Vector3f m_LocalPosition = Rainbow::Vector3f::one;

	//实体建模的子节点SimpleActor容器
	//std::vector<SimpleActor*> m_simpleActors;


	UInt32 m_bMoveable : 8; //是否是运动的Actor（祖先）
	UInt32 m_bStatic : 1;//是否是静态物
	UInt32 m_bControlByScript : 1;//是否被运动器驱动
	UInt32 m_bPause : 1; //暂停
	UInt32 m_bEmpty : 1;  //是否空节点
	UInt32 m_bCanBeInteracted : 1;//是否能被点击
	UInt32 m_bParent : 1 ; //是否是其他对象的父节点
	UInt32 m_bDelayInit : 1; //是否已经延迟初始化 
	UInt32 m_bEnableBatch : 1; //选择模式选中的actor不合
	UInt32 m_LoginVisible : 1;
	UInt32 m_bIgnoreUpdateFrequencyCtrl : 1;  //是否忽略不受UpdateFrequency组件控制；
	UInt32 m_bUgcSelect : 1;
	UInt32 m_bNoDieComponent : 1;

protected:
	/**
	@brief	使用多种Def来确定子类的类型。可弃用子类的blockId、monsterDef等各自的ID或Def的成员变量
	*/
	//const char* m_searchName;
	MonsterDef* m_monsterDef;
	static long long mg_cur_id;
	std::set<WCoord> m_CollideBlockPos;
	//VectorNoFree<WCoord> m_CollideBlockPos;
	// std::vector<int> m_CollideBlockID;
	//用来比对
	WCoord m_ServerPosCmp;
	unsigned int m_ServerYawCmp;
	unsigned int m_ServerPitchCmp;

	World *m_pWorld;
	unsigned int m_Flags;
	unsigned int m_FlagsEx;
	unsigned int m_AnimFlags;
	unsigned short m_CurMapID;
	char m_DoJumping; //-1: 无操作, 1: 做,  0: 清除
	char m_PathHide; // 1:jj怪
	int m_AsViewerRadius;
	unsigned char m_iAttChangeFlag;//m_iAttChangeFlag&1 表示最大血量变化了 m_iAttChangeFlag&2 表示闪避变化了 
	
	int m_BeHurtTimer;

	WORLD_ID m_ObjId;
	long long m_llMasterObjId;
	
	ActorLocoMotion*		m_pComponentLocoMotion;
	AttackedComponent*      m_pAttackedComponent;
	ActorVision*			m_pComponentVision;
	NavigationPath*			m_pComponentNaviPath;
	ActorAttrib*			m_pComponentAttrib;
	ClientActorFuncWrapper* m_pComponentFunc;
	RiddenComponent*		m_pComponentRidden;
	FireBurnComponent*		m_pComponentFireBurn;
	CarryComponent*			m_pComponentCarry;
	ActorInPortal *			m_pActorInPortal;
	ActorUpdateFrequency *	m_pActorUpdateFrequency;
	AIUpdateFrequency*      m_pAIUpdateFrequency;
	SwarmComponent*         m_pSwarmComponent;
	ThornBallComponent*     m_pThornBallComponent;
	SoundComponent*         m_pSoundComponent;
	EffectComponent*        m_pEffectComponent;
	//FallComponent*          m_pFallComponent;
	ActionAttrStateComponent* m_pActionAttrStateComponent;
	PhysicsComponent*       m_pPhysicsComponent;
	ActorChunkPos*			m_pChunkPosComponent;
	ChargeJumpComponent*	m_pChargeJumpComponent;
	ScriptComponent*		m_pScriptComponent;
	ModelComponent*         m_pModelComponent;
	ActorModelLerpComponent* m_pModelLerpComponent;
	EquipGridContainer*     m_pEquipGridContainer;
	GridContainer*          m_pGridContainer;
	SkillComponent* m_pSkillComponent;              //技能组件
	//ActorMovingLight* m_pActorMovingLight = nullptr;
	ViewerComponentActor* m_pViewerComponentActor = nullptr;
	FindComponent*			m_pFindComp;
	//ModelTransformLerpComponent* m_pModelTransformLerpComponent;

	int m_LastActorTick;
	int m_LastActorUpdate;
	int m_LastActorSelect;
	int m_childLen;
	int m_scriptcomLen = -1;
	int m_modelcomLen;
	int m_ugcComponentLen;
	WCoord m_LastActorUpdatePos;
	ActorHanlde* m_SpaceActorHandle;
	char* m_childBin = nullptr;
	char* m_scriptcomBin = nullptr;
	char* m_modelcomBin = nullptr;
	char* m_ugcComponentBin = nullptr;
	game::common::PB_ActorPhysicsCom* m_pPBActorPhysicsCom;
	game::common::PB_EffectComParticleInfo* m_pPBActorEffectCom;
	game::common::PB_SoundComInfo* m_pPBActorSoundCom;
	
	UInt32 m_MotionChange : 1;
	UInt32 m_isChangePos : 1;
	UInt32 m_IsFallGround : 1;
	UInt32 m_bReverse : 1; //是否颠倒反转包括寻路等
	UInt32 m_bSectionIsDisplay : 1; //actor 所在的section是否已经显示出来
	UInt32 m_AsActorViewer : 1;
	UInt32 m_fromPB : 1;
	UInt32 m_bXrayEffectEnable : 1;
	UInt32 m_IsLoadedModel : 1; //模型资源是否正在加载	
	int m_triggerRightCount;
public:
	//tolua_begin
	virtual void SetVisible(const bool& value) override;
	//tolua_end
	//反射
	//void VisibleGet(bool& value) const;
	//void VisibleSet(const bool& value);

protected:
	/* 绑定到场景 */
	virtual void BindToScene(MNSandbox::Scene* scene);
	/* 从场景解绑 */
	virtual void UnbindFromScene(MNSandbox::Scene* scene);

	/********** 判断野指针相关逻辑 start *******************/
public:
	static bool IsValid(ClientActor* pActor);
	static void ClearPtr2IDCache(); //在worldmananger 构造和析构时调用
private:
	static int genInstanceID();
	static std::unordered_map<ClientActor*,int > ms_ID2Ptr;
	int m_InstanceID;
	/********** 判断野指针相关逻辑 end *******************/
	
public:
	virtual void addCollisionDetect(CollisionDetect& cd, const CollideAABB& rangebox) {}
	virtual bool intersectBox(const CollideAABB& box) { return false; }

private:
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
public:
	virtual bool NeedSupportExSandboxsInfo() const;
protected:
	bool UnserializeForSyncFromStream(MNSandbox::AutoRef<MNSandbox::Stream>& stream);
	bool SerializeForSyncToStream(MNSandbox::AutoRef<MNSandbox::Stream>& stream) const;
	void filterAllSyncNodes(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& out) const;
	float m_TurningSpeed;//转身速度
private:
	static bool s_SaveActorCommonForSyncFlag;//
#endif // SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX	
};//tolua_exports

const float ACTOR_COLLIDE_DEC = 0.0f;

#endif