
#ifndef __ACTORHORSE_H__
#define __ACTORHORSE_H__

#include "ActorContainerMob.h"
#include "actors/locoMotionComponents/LivingLocoMotion.h"
#include "actors/actorComponents/HorseSkillComponent.h"
#include "SandboxGame.h"

namespace FBSave
{
	struct ActorHorse;
}

#define CHECK_ACCOUNT_HORSE_CD  20*60  //1分钟检测一次
// 坐骑技能
//tolua_begin
enum
{
	HORSE_SKILL_BREED = 1,      //孵化蛋
	HORSE_SKILL_BURST = 2,     //蓄力期间加速
	HORSE_SKILL_WATERWALK = 3, //水上行走
	HORSE_SKILL_JUMP = 4,  //超级跳跃
	HORSE_SKILL_GLIDE = 5,  //滑翔
	HORSE_SKILL_LAVAWALK = 6, //岩浆行走
	HORSE_SKILL_BENTENG = 7,  //奔腾
	HORSE_SKILL_BOUNCE = 8,   //弹跳移动
	HORSE_SKILL_TWO_PEOPLE = 9, //双人坐骑
	HORSE_SKILL_MUTATE_FLY = 10, //飞鸡坐骑这类
	HORSE_SKILL_FLOAT = 12,   //浮水
	HORSE_SKILL_DIVING = 13, //潜泳
	HORSE_SKILL_WATER_RUSH = 14, //水中突进
	HORSE_SKILL_TRANSFORMERS_RUSH = 15, //冲撞
	HORSE_SKILL_TRANSFORMERS_MISSILE = 16, //导弹
	HORSE_SKILL_TRANSFORMERS_SPRINT = 17,		//冲刺
	HORSE_SKILL_CAT_PUFF = 18,					//猫扑
	HORSE_SKILL_DETER = 19,						//威慑
	HORSE_SKILL_FLOATAGE = 20,					//漂浮
	HORSE_SKILL_FLY = 21,					//飞行
	HORSE_SKILL_HORIZONTAL_TWO_PEOPLE = 22, //双人横向坐骑
	HORSE_SKILL_SHIELD = 23, //护盾
	HORSE_SKILL_SINGLE_RIDE = 24, //双人坐骑单骑
	HORSE_SKILL_AIR_ALERT_FLOATAGE = 25, //空中警告（漂浮
	HORSE_SKILL_RED_LINGHTNING_SPRINT = 26, //红色闪电（冲刺
	HORSE_SKILL_FUSION_LASER_MISSILE = 27, //聚变激光（无畏导弹
	HORSE_SKILL_YANYU_FIRE = 28, //炎狱火
	HORSE_SKILL_YOUMIN_FIRE = 29, //幽冥火
	HORSE_SKILL_ZHENGCHI = 30, //振翅
	HORSE_SKILL_ZHANYI = 31, //展翼
	HORSE_SKILL_TYRANNOSAURUS_ROAR = 32,//暴龙咆哮
	HORSE_SKILL_SWIFT_WINGS =  33,//迅捷之翼
	HORSE_SKILL_SUPR_SWEEP = 34,//骨刺横扫
	HORSE_SKILL_STARLIGHT = 35,//星光
	HORSE_SKILL_MOONRISE = 36,//月升
	HORSE_SKILL_RABBIT_SPRINT = 37,//兔子冲刺
	HORSE_SKILL_SLEIGH_FLY = 39,	//雪橇飞行
	HORSE_SKILL_DOUDURUSH = 40, //嘟嘟冲啊
	HORSE_SKILL_FORTUNEMOO = 41, //福运哞哞
	HORSE_SKILL_FOURPEOPLE = 42, //四人同骑
	HORSE_SKILL_GIANTWHALE_FLAY = 43, //巨鲸飞天
	HORSE_SKILL_GIANTWHALE_SWIM = 44,
	//HORSE_SKILL_TRANSPORT, //传送
	//HORSE_SKILL_BACKPACK,  //增加背包空间

	HORSE_SKILL_BULL_PUFF = 45,//蛮牛之力震慑
	HORSE_SKILL_BULL_DETER = 46,//蛮牛之力强击
	HORSE_SKILL_FLYINGFLOWER = 47,//飞花
	HORSE_SKILL_GLANCE = 48,//惊鸿
	HORSE_SKILL_YE_WU = 49,//叶舞
	HORSE_SKILL_SOLAR_DISK = 50,//日轮
	HORSE_SKILL_GOD_LIGHT = 51,//神光

	HORSE_SKILL_PUMKKIN_TREAD_MAGIC = 52, //魔法烟花
	HORSE_SKILL_PUMKKIN_TREAD_GORGEOUS = 53, //华丽烟花
	HORSE_SKILL_PUMKKIN_SPRINT_LV1 = 54, //南瓜冲锋
	HORSE_SKILL_PUMKKIN_SPRINT_LV2 = 55, //南瓜冲击
	HORSE_SKILL_BAMBOO_DRAGONFLY_FLY = 56,//竹蜻蜓悬空被动
	HORSE_SKILL_BAMBOO_DRAGONFLY_FLY1 = 57,//竹蜻蜓贴地飞行
	HORSE_SKILL_BAMBOO_DRAGONFLY_FLY2 = 58,//竹蜻蜓离地飞行
	HORSE_SKILL_FLOLIGHT_FEATHER = 59,		//天鹅浮光掠羽特效技能
	HORSE_SKILL_SWAN_DIVING = 60,			//天鹅潜水
	HORSE_SKILL_SWAN_FLY = 61,				//天鹅飞行
	HORSE_SKILL_PLANE_FLY1 = 66,			//飞机飞行-凌云
	HORSE_SKILL_PLANE_FLY2 = 67,			//飞机飞行-破空
	HORSE_SKILL_PLANE_FLASH = 68,			//飞机闪现-时空跳跃
	HORSE_SKILL_CYCLONE = 69,				//小汽车-旋风	// 20210710：增加新技能  codeby： keguanqiang
	HORSE_SKILL_MAGIC_BUBBLES = 70,			//环海洛洛-魔法泡泡	// 20210715：增加新技能  codeby： keguanqiang
	HORSE_SKILL_WIND_WING = 71,				//环海洛洛-风之翼	// 20210715：增加新技能  codeby： keguanqiang
	HORSE_SKILL_MAGIC_TEARS = 73,			//尼东洛洛-神奇眼泪	// 20210728：增加新技能  codeby： keguanqiang
	HORSE_SKILL_SAIL = 80,					//画舫-扬帆	// 20210818：增加新技能  codeby： keguanqiang
	HORSE_SKILL_SUMMONLV1 = 81,				//魔毯-初级召唤	// ********：增加新技能  codeby： keguanqiang
	HORSE_SKILL_SUMMONLV2 = 82,				//魔毯-中级召唤	// ********：增加新技能  codeby： keguanqiang
	HORSE_SKILL_SUMMONLV3 = 83,				//魔毯-高级召唤	// ********：增加新技能  codeby： keguanqiang
	HORSE_SKILL_INVISIBLE = 84,				//魔毯-隐身	// ********：增加新技能  codeby： keguanqiang
	HORSE_SKILL_KUANGFENG = 87,				//机械鸟-狂风	// ********：增加新技能  codeby： keguanqiang
	HORSE_SKILL_FUYAO = 88,					//机械鸟-扶摇	// ********：增加新技能  codeby： keguanqiang

	HORSE_STEP_DUSTSTORM = 135,             //骆驼-踏沙     // 20220708：增加新技能  codeby：wangyu
	HORSE_SKILL_TWO_PEOPLE_STAND = 166,     //庆典之书-双骑
	HORSE_SKILL_DOUBLE_JUMP = 185,			//虚空角鹿 二次跳跃
};
//tolua_end
//tolua_begin
enum HORSE_MOEDL_DEF_ID
{
	SANTA_SLEIGH0 = 3483,	//圣诞坐骑0级暖冬祝福
	SANTA_SLEIGH1 = 3484,	//圣诞坐骑1级圣夜惊喜
	SANTA_SLEIGH2 = 3485,	//圣诞坐骑2级冰雪传说

	BAMBOO_DRAGONFLY0 = 4508, //竹蜻蜓0级
	BAMBOO_DRAGONFLY1 = 4509, //竹蜻蜓1级
	BAMBOO_DRAGONFLY2 = 4510, //竹蜻蜓2级

	HORSE_CYGNUS_LV1 = 4512, //灰甜绒绒 
	HORSE_CYGNUS_LV2 = 4513, //星愿雪霜 
	HORSE_CYGNUS_LV3 = 4514, //绮幻奇旅 
	HORSE_CYGNUS_LV4 = 4515, //曦光绘梦
	HORSE_HIPPOCAMPUS = 3625,//海马

	HORSE_LUOTUO_LV2 = 4603, //沙漠之舟
};
//tolua_end
//tolua_begin
enum HORSE_FLAG
{
	FLASHING = 0,		//闪现  // 20210902：增加闪现状态 codeby： keguanqiang
	INVISIBLE = 1,		//隐身	// ********：增加隐身状态 codeby： keguanqiang
	FLYING = 2,		    //飞行	// 20220422：增加飞行状态 codeby： huangrui
};

//tolua_end
//tolua_begin
enum CatHorseSkillState
{
	CAT_NORMAL,
	CAT_ENOUGH_CHARGE,
	CAT_CAN_TRIGGER_SKILL,
};
//tolua_end

#define MAX_HORSE_CHARGE  (4*20)
#define TRIGGER_SKILL_CHAGER_PRO 0.67 
#define SRC_MAX_RIDDERS 2

struct HorseDef;
struct BuffDef;
class HorseActorBody : public ActorBody
{
public:
	HorseActorBody(ClientActor* owner) : ActorBody(owner)
	{
		m_ShowNecklace = 1;
	}
	virtual void setEquipItem(EQUIP_SLOT_TYPE slot, int itemid) override;
	void setEquipItemNoOwner(EQUIP_SLOT_TYPE slot, int itemid, const HorseDef* def);
};

class EXPORT_SANDBOXGAME ActorHorse;
class ActorHorse : public ActorContainerMob{ //tolua_exports
public:
	//tolua_begin
	ActorHorse();
	virtual ~ActorHorse();

	void createEvent();//事件注册
	virtual bool init(int monsterid);

	virtual WCoord getRiderPosition(ClientActor* ridden);

	virtual int getItemAndAttrib(RepeatedPtrField<PB_ItemData>* pItemInfos, RepeatedField<float>* pAttrInfos);
	//tolua_end
	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual ActorBody *newActorBody();
	virtual void enterWorld(World *pworld);

	virtual bool managedByChunk()
	{
		return false;
	}

	virtual bool needSaveInChunk()
	{
		return false;
	}

	virtual bool interact(ClientActor *player, bool onshift=false, bool isMobile=false);
	virtual void tick();
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker /* = NULL */);
	virtual void update(float dtime);
	virtual bool canBeRided(ClientPlayer *player) ;

	virtual int getStepHeight();
	virtual bool needUpdateAI() override;
	virtual float getRunWalkFactor() override;
	virtual void onDie() override;

	virtual BackPackGrid *index2Grid(int index)override;
	virtual bool canPutItem(int index);
	virtual void afterChangeGrid(int index);
	virtual bool needCheckVisible();
	virtual void collideWithActor(ClientActor *actor);
	virtual void moveToPosition(const WCoord& pos, float yaw, float pitch, int interpol_ticks);
	int getObjType() const override;
	//tolua_begin
	virtual void onEnterWater();

	virtual void startCharge();
	virtual void endCharge();
	virtual void doSkillAttack(ClientActor* target) {};
	virtual void useSkill();
	virtual const char* getSkillParticleName(int skillid);
	virtual void playSkillEffect(int skillid);
	virtual void setSkillCD(int index, float cd);
	virtual float getSkillCD(int index);
	void tickByClient();
	bool showUseBtn();   //显示使用按钮，用于使用技能
	void syncSkillCD2Client(int index, float cd);
	bool canUseSkill();
	void logicByCanNotUseSkill();   //不能使用技能时一些逻辑处理
	bool isInCharge()
	{
		return m_CurCharge >= 0;
	}
	float getChargeProgress()
	{
		int t = m_CurCharge;
		if (t < 0) t = 0;
		return float(t) / MAX_HORSE_CHARGE;
	}
	//tolua_end
	virtual bool supportSaveToPB()
	{
		return false;
	}	
	//tolua_begin
	void equipSaddle(BackPackGrid &src);
	void equipSaddle(int itemid);
	void equipRake(BackPackGrid &src);
	void equipRake(int itemid);
	void equipByIndex(size_t index, int itemid);
	void setAccountBind(int uin);
	bool getHorseSkill(int id, float *vals=NULL);
	virtual bool getHorseFlySkills(float *skillvals = NULL);
	bool isDiving() { return m_bDoingDiving; }
	int  checkHasSkill(int skillid1, int skillid2, float* skillvals);
	bool canRriddenByNoOxygen();
	bool needSetZoomByJump();

	// 清理装备栏
	void clearEquip(int index);

	void setHorseFlag(int flag)
	{
		m_HorseFlags = flag;
	}
	void setHorseFlagBit(int ibit, bool b);

	bool getHorseFlagBit(int ibit)
	{
		return (m_HorseFlags & (1 << ibit)) != 0;
	}

	bool isInvisioning()
	{
		return getHorseFlagBit(HORSE_FLAG::INVISIBLE); // ********：是否处于坐骑隐身技能中  codeby： keguanqiang
	}
	LivingLocoMotion* getRiddenByLivingLocoMotion();
	std::vector<float> getHorseSkill2(int id);
	const HorseDef *getHorseDef()
	{
		return m_HorseDef;
	}
	int getRiddenLandSpeed()
	{
		return m_LandSpeed;
	}
	int getRiddenFlySpeed()
	{
		return m_FlySpeed;
	}
	int getRiddenSwimSpeed() { return m_SwimSpeed; }
	bool isUseSwimSpeed() { return (m_iUseSwimSpeed > 0); }
	bool isDoingRush() { return m_bDoingRush; }
	bool hasWaterSkill(int bit) { return ((m_iUseSwimSpeed & bit) > 0); }//1浮水 2潜泳 4水中突进

	bool isFloatageing() { return m_bFloatageing; }
	void setFloatingStatus(bool bFloating) { m_bFloatageing = bFloating; }

	float getMaxJumpHeight()
	{
		return float(m_JumpHeight);
	}

	int getCurCharge()
	{
		return m_CurCharge;
	}

	int getMaxCharge()
	{
		return MAX_HORSE_CHARGE;
	}

	void resetCurCharge()
	{
		m_CurCharge = -1;
	}

	void setCurCharge(int charge)
	{
		m_CurCharge = charge;
	}
	int getShieldCoolingTicks();
	void setShieldCoolingTicks(int ticks);

	bool isAccountBind()
	{
		return m_BindUIN > 0;
	}

	int getAccountBindId()
	{
		return m_BindUIN;
	}

	bool hasHorseSkill(int id)
	{
		return getHorseSkill(id, NULL);
	}

	bool armorSlotOpen()
	{
		return m_ArmorSlotOpen;
	}

	float getEnergy()
	{
		return m_fEnergy;
	}

	bool isTired()
	{
		return m_bTired;
	}

	bool isRakeToolLiving()
	{
		return m_bRakeToolLiving;
	}

	void getHorseSkillList(int &skill1, int &skill2, int &skill3,int &skill4);
	int getHorseCanAgeTick();
	virtual bool isTriggerSkillCharge();

	void onCheckAccountHorseExpireTime(std::string tips);
	int getBamboDragonFlyState();
	void setSkillScript(std::string path);
	void toDoMoveStep();
	void playLoopSound(int state, const char *name);

	void OnHorseMounted();
	void OnHorseDismounted();
	//virtual int getNumRiddenPos() override
	//{
	//	return m_NumRidePos;
	//}
	virtual WORLD_ID getRiddenByActorID(int i=0) ;
	virtual ClientActor* getRiddenByActor(int i = 0) ;
	virtual void setRiddenByActor(ClientActor *p, int i=0) override;
	virtual Rainbow::Vector3f getRiddenBindPos(ClientActor *ridden) ;
	virtual int findRiddenIndex(ClientActor *ridden) ;
	virtual int findEmptyRiddenIndex(int index = 0) ;
	virtual bool getRiddenChangeFPSView() ;
	virtual void setRiddenByActorObjId(WORLD_ID objId, int i = 0) ;
	virtual bool CanDriver() { return true; }  //可以骑，但是不能随意驱使它
	
	virtual float getFallHurtRate();
	void setEnergy(float energy) { m_fEnergy = energy;}
	void setTired(bool tired) { m_bTired = tired;}
	void setRakeToolLiving(bool bRakeToolLiving) { m_bRakeToolLiving = bRakeToolLiving;}
	float getBuffAttrValues(const BuffDef *buffDef,MODATTRIB_TYPE type);
	virtual void checkBindPlayer(bool isinit);

	bool checkDecayLoopSound();		//循环音效做淡出
	// 坐骑新增设置接口 code-by:lizb
	void setMaxJumpHeight(int jumpHeight)
	{
		m_JumpHeight = jumpHeight;
	}
	void setRiddenLandSpeed(int landSpeed)
	{
		m_LandSpeed = landSpeed;
	}
	void changeGunBulletSpawnPos(WCoord& pos);

	void playFlyEffect();
	void stopFlyEffect();
	void playMoveEffect();
	void stopMoveEffect();

	bool getDragonFlyIsFly() {
		return m_bDragonFlyIsFly;
	}
	int getRiddenAnim();
	//以后尽量使用这接口控制玩家和坐骑动作变化
	void updateHorseBodyAnim(int& anim);//更新坐骑动作
	void updateRiddenBodyAnim(int& anim, ClientActor *ridden = NULL);//更新骑乘者动作

	//设置身体旋转 code-by:zhanghongxuan
	void setBodyRotation(float yaw, float pitch, float roll);
	//攻击前方范围内生物 code-by:zhanghongxuan
	void AttackFacedActors(const Rainbow::Vector3f &dir, int range, int width, OneAttackData atkdata);
	//刷新坐骑状态 提供给lua调用 code-by:zhanghongxuan
	void RefreshRiddenActor();
	//调用父类attackedFrom函数 提供给lua调用
	bool attackedFromSuper(OneAttackData &atkdata, ClientActor *attacker /* = NULL */)
	{
		return ActorLiving::attackedFrom(atkdata, attacker);
	}
	bool moveLocoMotionEntityWithHeading(float strafing, float forward);

	void breakInvisible();	// ********：打断隐身  codeby： keguanqiang
	void startInvisible();	// ********: 开始隐身  codeby： keguanqiang
	void syncInvisibleInfo(bool invisible);	// ********: 同步隐身信息  codeby： keguanqiang
	virtual bool isInvisible() { return getHorseFlagBit(HORSE_FLAG::INVISIBLE); }	// ********：是否隐身  codeby： keguanqiang
	int getFlyAnim();		// ********：获取飞行动作  codeby： keguanqiang

	bool hasSaddle();

	static bool mobCanRake(int mobDefId);
	//tolua_end

	bool playWalkOnLiquidEffect(bool iswater);
	bool canWalkOnLiquid(bool iswater);

	flatbuffers::Offset<FBSave::ActorHorse> saveMob(SAVE_BUFFER_BUILDER& builder);

	bool getRiddenControlPriority() { return m_riddenControlPriority; }
	bool getRiddenControl();

private:
	int getAccoutHorseOwnerUin();
	void doKuangFeng();	// ********：狂风技能  codeby： keguanqiang

protected:
	int m_LandSpeed;
	int m_SwimSpeed;
	int m_FlySpeed;
	int m_JumpHeight; //最大跳起来的高度, 1方块为100

	int m_BindUIN; //帐号坐骑, 属于哪个uin,  不是为0
	bool m_ArmorSlotOpen;
	bool m_RakeSlotOpen;	//耙(存放在EQUIP_LEGGING装备栏上)
	WORLD_ID m_OtherRiddens[SRC_MAX_RIDDERS]; //双人坐骑的另一个人,本来1就够了写2是预留

	float m_fEnergy;
	bool m_bTired;
	bool m_bRakeToolLiving;	//耙工具生物
	int m_iUseSwimSpeed;//浮水 潜泳 水中突进技能在水中要用m_SwimSpeed  1浮水 2潜泳 4水中突进
	int m_iChargeAddSpeed;
	bool m_bIsFirstLoadOverEntity;	//模型首次加载完成
	enum
	{
		MAX_EQUIPS = 2,
		MAX_SKILLS = 3,
		MAX_EQUIPS_EX = 3,
	};
	BackPackGrid m_EquipGrids[MAX_EQUIPS_EX];
	struct HorseSkill
	{
		short id;
		short active;
	};
	HorseSkill m_Skills[MAX_SKILLS];

	std::vector<HorseSkill> m_ExtendSkills;
	std::vector<int64_t> m_ExtendOtherRiddens;
	const HorseDef *m_HorseDef;
	std::vector<HorseSkillComponent*> m_skillComList;//坐骑技能组件列表
private:
	virtual ActorLocoMotion *newLocoMotion() override;
	virtual float getFallHurtSubtract() ;
	virtual float getUIModelViewScale() override;
	virtual bool canDespawn()
	{
		return false;
	}
	
	void sendFlyState();

	void getSkillEffectedActors(std::vector<ClientActor *> &actors, float skillRange);
	
	bool checkIsInvalidTarget(ClientActor* target,ClientPlayer *player);
	void doSkillAddBufToTarget(ClientActor *target, float skillVal);
	void doSkillAttackTarget(ClientActor *target, float skillVal);
	void checkDoCatSkill(int skillId,std::vector<ClientActor *> &actors, float* skillvals,ClientPlayer *player,std::function<void(ClientActor*, float)> callback);

	void doCatSkill();
	void doFloatageSkill();
	void doFlash();		//闪现
	
	int m_CurCharge; //当前蓄力值, <0表示不在蓄力状态
	int m_ChargePeakTicks; //在最大值的时间
	int m_iChargeRushTicks;

	bool m_DoingBenteng; //正在奔腾的技能
	bool m_bDoingRush;//水中突进中
	bool m_bDoingDiving;//深潜中
	bool m_bPlayDivingEff;
	bool m_bPreInWater;
	bool m_bChangeRide;
	bool m_bFloatageing;  //漂浮中

	CatHorseSkillState m_CatSkillState;

	int m_PosRotationIncrements;
	WCoord m_HorsePos;
	float m_HorseYaw;
	float m_HorsePitch;

	int m_CheckUINTicks; //一定时间找不到uin对应的player, 消失掉
	//int m_NumRidePos;
	int m_shieldLife;
	int m_shieldCoolingTicks; //护盾冷却时间
	bool m_aureoleIsShow; //光環是否显示
	bool  m_shieldIsShow; //护盾是否显示
	int m_nLastJumpTick;
	Rainbow::ISound *m_CurPlaySnd;

	int m_iDragonFlyHeight; // 竹蜻蜓飞行高度
	bool m_bDragonFlyIsFly; // 竹蜻蜓是否长按空格飞行
	int m_iDragonFlyState; // 0: 不在飞行， 1：在飞行
	float m_fDragonFlyBoneHeight; //角色头部绑点离坐骑绑点高度

private:
	float m_fSkillCD[3];
	int	m_iCheckAccountHorseCD;		 //每隔一段时间检测账号坐骑时效性 

	int m_iPreFlashTick;			//闪现技能前摇时间
	bool m_bStopFlySound;			//停止飞行音效
	unsigned int m_HorseFlags;		//坐骑的状态HORSE_FLAG

	int m_iInvisibleTick;			//********：隐身时长  codeby： keguanqiang
	bool m_bPreInvisible;			//********：隐身前置跳跃  codeby： keguanqiang

	int m_iPreJumpSkill;			//********：技能前置跳跃  codeby： keguanqiang

	bool m_riddenControlPriority;   //优先权限
	///*
	//	id-马鞍
	//*/
	//unsigned char m_ShowSaddle;
	///*
	//	id-项链
	//*/
	//unsigned char m_ShowNecklace;
	///*
	//	id-耙状工具
	//*/
	//unsigned char m_ShowRake;
}; //tolua_exports

#endif
