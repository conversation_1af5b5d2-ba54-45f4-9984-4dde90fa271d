
#include "BlockChestSimple.h"
#include "section.h"
#include "container_world.h"
#include "world.h"
#include "IClientPlayer.h"
#include "special_blockid.h"
#include "ClientInfoProxy.h"
#include "WorldManager.h"
#include "container_erosion_storage.h"
#include "BlockErosionHelper.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockSimpleChest)
IMPLEMENT_BLOCKMATERIAL(BlockLockedChest)
IMPLEMENT_BLOCKMATERIAL(ChestPasswordMaterial)
IMPLEMENT_BLOCKMATERIAL(ChestMultiChest)

void BlockSimpleChest::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//���Գ�ʼ��
	SetToggle(BlockToggle_HasContainer, true);

	
}

void BlockSimpleChest::initGeomName()
{
	if (m_Def->Texture2.size() > 0)
		m_geomName = m_Def->Texture2.c_str();
	else
		m_geomName = "box";
}

//const char *BlockSimpleChest::getGeomName()
//{
//	return "box";
//}

void BlockSimpleChest::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	int blockdata = player->GetPlayerCurPlaceDir();
	pworld->setBlockData(blockpos, blockdata, 3);
}

bool BlockSimpleChest::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	reportData();
	if (pworld->isRemoteMode())
	{
		return true;
	}

	if(pworld->getContainerMgr() == NULL) return true;

	WorldContainer* container = pworld->getContainerMgr()->getContainer(blockpos);
	if (container)
	{
		// 是否禁止多人操作
		if (GetBlockDef()->UserData[0] == 1 && container->isOpenning())
		{
			return false;
		}
		player->openContainer(container);
	}

	return true;
}

WorldContainer *BlockSimpleChest::createContainer(World *pworld, const WCoord &blockpos)
{
	return SANDBOX_NEW(WorldStorageBox, blockpos);
}

int BlockSimpleChest::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

int BlockSimpleChest::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();

	dirbuf[0] = blockdata & 3;
	idbuf[0] = (blockdata&4)>0 ? 1 : 0;
	return 1;
}

int BlockSimpleChest::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;

	return 1;
}

void BlockSimpleChest::reportData() {
	double trueId = 0;
	int worldType = 0;
	int gameLabel = 0;
	double realOwner = 0;
	bool isMulti = false;
	bool find = false;
	MINIW::ScriptVM::game()->callFunction("userTaskReportGetWorldParam", ">ddiibb", &trueId, &realOwner, &worldType, &gameLabel, &isMulti, &find);

	if (find) {

		std::string str = "	local since_create =  0 \
                        	if AccountManager.get_time_since_create ~= nil then \
			                  since_create = AccountManager:get_time_since_create() or 0 \
			                end \
                            local playType = \"0\" \
		                    if since_create < 86400 then \
			                   playType = \"1\" \
			                end \
                            standReportEvent(\"405\", \"ADVENTURE_CHEST\",\"-\", \"click\", {cid=";

		str.append(std::to_string((long long)trueId));


		str.append(",standby1=730,standby2=");
#ifdef IWORLD_SERVER_BUILD
		str.append("\"multi");
#else
		if (isMulti)
			str.append("\"multi");
		else
			str.append("\"single");
#endif


		switch (worldType)
		{
		case 0:
			str.append("_adventure");
			break;
		case 2:
			str.append("_extreme-adventure");
			break;
		case 6:
			str.append("_advanced-adventure");
			break;
		default: //������ͼ���ϱ�
			return;
		}

		if ((int)realOwner == GetClientInfoProxy()->getUin())
			str.append("_1_\"");
		else
			str.append("_0_\"");

		str.append("..playType})");
		MINIW::ScriptVM::game()->callString(str.c_str());
	}
}
//--------------------------------------------------------------------------------------------------------------------------------------------
//const char *BlockLockedChest::getGeomName()
//{
//	return "lockbox";
//}

//void BlockLockedChest::init(int resid)
//{
//	BlockSimpleChest::init(resid);
//}

void BlockLockedChest::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}

void BlockLockedChest::initGeomName()
{
	m_geomName = "lockbox";
}

WorldContainer *BlockLockedChest::createContainer(World *pworld, const WCoord &blockpos)
{
	WorldStorageBox *box = SANDBOX_NEW(WorldStorageBox, blockpos);
	box->m_GridCount = m_BlockResID==BLOCK_CHESTLOCKED_S ? 10 : 30;

	return box;
}

float BlockLockedChest::getDestroyHardness(int blockdata, IClientPlayer *player)
{
	if (!player || !player->isHost())
	{
		return -1;
	}
	else
	{
		return getBlockHardness();//GetBlockDef()->Hardness;
	}
}

void BlockLockedChest::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	int blockdata = player->GetPlayerCurPlaceDir();
	pworld->setBlockData(blockpos, blockdata, 3);
}

bool BlockLockedChest::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	//if(player->hasUIControl() || player->isCloudRoomServerOwner()) //isCloudRoomServerOwner()����Ʒ�����
	//20211111 �ж��Ʒ���������ʹ�� isCloudRoomServerOwner codeby:liushuxin
	if (player->hasUIControl() || (GetWorldManagerPtr()->getGameLeaderUin() == player->getUin()))
	{
		if(pworld->getContainerMgr() == NULL) return true;
		WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);
		if(container) player->openContainer(container);
	}
	else
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 122);
	}

	return true;
}

ChestPasswordMaterial::ChestPasswordMaterial()
{
}

ChestPasswordMaterial::~ChestPasswordMaterial()
{
}

void ChestPasswordMaterial::init(int resid)
{
	BlockLockedChest::init(resid);
}

void ChestPasswordMaterial::initGeomName()
{
	m_geomName = "chest_password";
}
WorldContainer *ChestPasswordMaterial::createContainer(World *pworld, const WCoord &blockpos)
{
	WorldStorageBoxPassword *box = SANDBOX_NEW(WorldStorageBoxPassword, blockpos);
	box->m_GridCount = m_BlockResID==BLOCK_CHEST_PASSWORD_S ? 10 : 30;
	return box;
}

//const char *ChestPasswordMaterial::getGeomName()
//{
//	return "chest_password";
//}

bool ChestPasswordMaterial::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	if(pworld->getContainerMgr() == NULL) return true;
	auto Container = dynamic_cast<WorldStorageBoxPassword*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (Container && player)
	{
		int password = player->getContainersPassword(Container->m_BlockPos);
		if (Container->m_nPassWord == -1 || Container->m_nPassWord != password)
		{
			player->setContainersPassword(blockpos, -1);  
		}
		player->openContainer(Container);
	}


	return true;
}

//-------占地多格的宝箱---------
void ChestMultiChest::init(int resid)
{
	BlockSimpleChest::init(resid);
	setMultiBlockSize(WCoord(m_Def->BlockSize[0] - 1, m_Def->BlockSize[1] - 1, m_Def->BlockSize[2] - 1));
}

void ChestMultiChest::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	// 只有核心方块才进行处理
	if (isCoreBlock(pworld, blockpos))
	{
		int blockData = pworld->getBlockData(blockpos);
		int placeDir = blockData & 3;
		std::vector<WCoord> poslist;
		getMultiBlockRange(poslist, placeDir, blockpos, false, true);
		for (auto& pos : poslist)
		{
			pworld->setBlockAll(pos, m_BlockResID, 4 | placeDir);
		}
	}
}

void ChestMultiChest::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);
}

void ChestMultiChest::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
		pworld->getContainerMgr()->destroyContainer(blockpos);
		std::vector<WCoord> poslist;
		getMultiBlockRange(poslist, placeDir, blockpos);
		for (auto& pos : poslist)
		{
			WCoord curPos = pos + blockpos;
			int blockid = pworld->getBlockID(curPos);
			if (blockid == m_BlockResID && !isCoreBlock(pworld, curPos))
			{
				pworld->setBlockAir(curPos);
			}
		}
	}

}

// 使用宏来减少重复代码
IMPLEMENT_MULTIBLOCK_EROSION_DAMAGE_FUNCTIONS(ChestMultiChest)

void ChestMultiChest::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	//if (data.m_SharedSectionData->getBlock(blockpos).getData() & 8)
	//	return;
	auto corePos = blockpos;
	if (!isCoreBlock(data.m_World, corePos)) corePos = getCoreBlockPos(data.m_World, blockpos);
	Super::createBlockMesh(data, corePos, poutmesh);
}

bool ChestMultiChest::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	auto corePos = getCoreBlockPos(pworld, blockpos);
	return BlockSimpleChest::onTrigger(pworld, corePos, face, player, colpoint);
}

WorldContainer* ChestMultiChest::createContainer(World* pworld, const WCoord& blockpos)
{
	if (pworld->isRemoteMode())
	{
		return nullptr;
	}
	if (isCoreBlock(pworld, blockpos))
	{
		//return SANDBOX_NEW(WorldStorageBox, blockpos);
		return SANDBOX_NEW(ErosionStorageBox, blockpos, m_BlockResID);
	}
	else
	{
		return nullptr;
	}
}

void ChestMultiChest::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

//void ChestMultiChest::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
//{
//	int blockdata = player->GetPlayerCurPlaceDir();
//	pworld->setBlockData(blockpos, blockdata, 3);
//}
// 根据玩家朝向获取方块数据
int ChestMultiChest::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	if (player)
	{
		return player->GetPlayerCurPlaceDir();
	}
	return 0;
}

bool ChestMultiChest::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
	auto corepos = getCoreBlockPos(pworld, blockpos);
	if (corepos.y < 0) return false;
	auto placedir = pworld->getBlockData(corepos) & 3;
	getMultiBlockRange(blockList, placedir, corepos, includeSelf, true);
	return blockList.size() != 0;
}

//int ChestMultiChest::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
//{
//	return getPlaceBlockDataByPlayer(pworld, player);
//}

WorldContainer* ChestMultiChest::repairContainer(const WCoord& blockpos, const int& data)
{
	if ((data & 4) == 0 || m_BlockResID == 801)
	{
		return SANDBOX_NEW(ErosionStorageBox, blockpos, m_BlockResID);
	}
	return NULL;
}