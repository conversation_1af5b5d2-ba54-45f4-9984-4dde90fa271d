#include "PlayerInputHelper.h"
#include "PlayerControl.h"

#include "TouchControl.h"
#include "PCControl.h"
#include "InputInfo.h"
#include "OgreTimer.h"
#include "IClientGameInterface.h"
#include "OgreScriptLuaVM.h"
#include "GameSettings.h"
#include "KeyBinding.h"
//#include "GameEvent.h"
//#include "OgreMaterialTemplate.h"
//#include "OgreRoot.h"
#include "VehicleWorld.h"
#include "Platforms/PlatformInterface.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "GameCamera.h"
#include "Input/OgreInputManager.h"
#include "OgrePhysXManager.h"
#include "VehicleControlInputs.h"
#include "container_driverseat.h"

#include "IClientGameManagerInterface.h"
//#include "SurviveGame.h"
#include "IRecordInterface.h"
#include "SandboxCoreDriver.h"

#include "SandboxCfg.h"
#ifdef DEBUG_RENDER
//#include "OgreMaterialManager.h"
#endif
#include <string>
#include <cmath>
#include "Input/InputManager.h"
#include "ClientInfoProxy.h"
#include "ClientAppProxy.h"
#include "RiddenComponent.h"
#include "ActorVehicleAssemble.h"
#include "CameraManager.h"
#include "AdventureGuideMgrProxy.h"

#ifdef TestScriptCamera
//镜头测试代码
#include "CameraManager.h"
int testCameraCounter = 0;
#endif // TestScriptCamera

//调试添加数据  --- by charles xie
#include "backpack.h"


using namespace MINIW;
using namespace MNSandbox;
using namespace Rainbow;
//#define F7_SAVE_SHADERS
const unsigned char MAX_SPEEDUP_TIMES = 4;

PlayerInputHelper::PlayerInputHelper():m_eKeyDetector(SDLK_e), m_rKeyDetector(SDLK_r), m_rightClickDetector(SDLK_RBUTTON)
{
	m_RunKeyDownButton = -1000;
	for (int i = 0; i < 6; i++)
	{
		m_KeyDownMark[i] = -1000;
	}
	m_keyGuideStep = 0;
	m_SpeedUpTimes = 1;
	
	// 设置E键的长按阈值
	m_eKeyDetector.setLongPressThreshold(0.5f);
	// 设置R键的长按阈值
	m_rKeyDetector.setLongPressThreshold(0.5f);
	// 设置鼠标右键的长按阈值
	m_rightClickDetector.setLongPressThreshold(0.5f);
}

bool PlayerInputHelper::getVehiclePhysActorPos(VehicleAssembleLocoMotion* loc, Rainbow::Vector3f& pos, Quaternionf& q)
{
	pos.x = 0.0f;
	pos.y = 0.0f;
	pos.z = 0.0f;
	q.w = 0.0f;
	q.x = 0.0f;
	q.y = 0.0f;
	q.z = 0.0f;

	if (loc && loc->m_PhysActor)
	{
		loc->m_PhysActor->GetPos(pos, q);
		return true;
	}

	return false;
}

void PlayerInputHelper::triggerPCVehicleControlKeys(const Rainbow::InputEvent& inevent)
{	
	auto RidComp = g_pPlayerCtrl->getRiddenComponent();
	if (RidComp && RidComp->isVehicleController())
	{
		ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
		if (vehicle)
		{
			WCoord seatpos = vehicle->getRiddenBindSeatPos(g_pPlayerCtrl);
			ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(seatpos));
			if (container && container->m_BindKeyData.size() > 0)
			{
				auto keyiter = container->m_BindKeyData.begin();
				while (keyiter != container->m_BindKeyData.end())
				{
					int keyVal = container->ConvertIdtoKeyVal(keyiter->first);
					int keycode = inevent.keycode;
					if (keycode == SDLK_RSHIFT || keycode == SDLK_LSHIFT) keycode = SDLK_SHIFT;
					if (keycode == SDLK_RCTRL || keycode == SDLK_LCTRL) keycode = SDLK_CONTROL;
					//if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(container->ConvertIdtoKeyVal(keyiter->first)))
					if (inevent.type == InputEvent::kKeyDown && keycode == keyVal)
					{
						vehicle->TriggerControlKeys(g_pPlayerCtrl->getUin(), keyiter->first, 1, true);
						vehicle->TriggerControlKeys(g_pPlayerCtrl->getUin(), keyiter->first, 0, true);
					}
					//if (g_pPlayerCtrl->m_PCCtrl->GetKeyUp(container->ConvertIdtoKeyVal(keyiter->first)))
					if (inevent.type == InputEvent::kKeyUp && keycode == keyVal)
					{
						vehicle->TriggerControlKeys(g_pPlayerCtrl->getUin(), keyiter->first, 0, false);
					}
					keyiter++;
				}
			}
		}
	}
}

int PlayerInputHelper::getCurForwardKeyCode()
{
	// 侧视角下，会调整移动方向的按键
	int curForwardKeyCode = GameSettings::GetInstance().keyBindForward->getCurrentKeyCode();
	if (g_pPlayerCtrl && g_pPlayerCtrl->getViewMode() == CAMERA_CUSTOM_VIEW)
	{
		if (g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_FLATVIEW)
		{
			int delta = int(abs(g_pPlayerCtrl->getLocoMotion()->m_RotateYaw - g_pPlayerCtrl->m_CurrentCameraConfig.InitPlayerYaw)) % 360;
			if (delta < 1.0f)
			{
				curForwardKeyCode = GameSettings::GetInstance().keyBindRight->getCurrentKeyCode();
			}
			else
			{
				curForwardKeyCode = GameSettings::GetInstance().keyBindLeft->getCurrentKeyCode();
			}
		}
	}

	return curForwardKeyCode;
}

void PlayerInputHelper::debugRender()
{
#ifdef DEBUG_RENDER
	//MaterialManager::GetInstancePtr()->debugReloadShader();
#endif
}

void PlayerInputHelper::SwitchChannelEngine()
{
	MINIW::PhysXManager::GetInstance().SwitchChannelEngine();
}

void PlayerInputHelper::SwitchChannelWheel()
{
	MINIW::PhysXManager::GetInstance().SwitchChannelWheel();
}



void PlayerInputHelper::onUpdate(float dtime)
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	// 更新按键检测器（它会自动处理所有E键相关逻辑）
	m_eKeyDetector.update(dtime);
	// 更新R键检测器
	m_rKeyDetector.update(dtime);
	// 更新鼠标右键检测器
	m_rightClickDetector.update(dtime);

	bool canControl = g_pPlayerCtrl->canControl();

	if (GetClientInfoProxy()->isMobile())
	{
		g_pPlayerCtrl->m_TouchCtrl->update(dtime);

		//--------------get InputInfo start ------------------------------
		g_pPlayerCtrl->m_TouchCtrl->GetDpadValueRaw(g_pPlayerCtrl->m_InputInfo->moveForward, g_pPlayerCtrl->m_InputInfo->moveStrafe);

		if (!canControl)
			g_pPlayerCtrl->m_InputInfo->moveForward = g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;

		//LOG_INFO("g_pPlayerCtrl->m_InputInfo->dpadValueX %f", g_pPlayerCtrl->m_InputInfo->moveForward);
		//LOG_INFO("g_pPlayerCtrl->m_InputInfo->dpadValueY %f", g_pPlayerCtrl->m_InputInfo->moveStrafe);
		g_pPlayerCtrl->m_InputInfo->jump = g_pPlayerCtrl->m_TouchCtrl->GetKey(SDLK_SPACE) || g_pPlayerCtrl->m_TouchCtrl->GetButton(BUTTON_JUMP) || g_pPlayerCtrl->m_TouchCtrl->joystickJump;
		g_pPlayerCtrl->m_InputInfo->triggerJump = g_pPlayerCtrl->m_TouchCtrl->GetButtonDown(BUTTON_JUMP);
		g_pPlayerCtrl->m_InputInfo->jumpRelease = g_pPlayerCtrl->m_TouchCtrl->GetButtonUp(BUTTON_JUMP);
		if (!canControl)
			g_pPlayerCtrl->m_InputInfo->jump = g_pPlayerCtrl->m_InputInfo->triggerJump = g_pPlayerCtrl->m_InputInfo->jumpRelease = false;


		//--------------get InputInfo start ------------------------------
		if (g_pPlayerCtrl->m_TouchCtrl->isRockerMode())
		{
			g_pPlayerCtrl->m_TouchCtrl->GetDpadValue(g_pPlayerCtrl->m_InputInfo->dpadValueY, g_pPlayerCtrl->m_InputInfo->dpadValueX);

			//if (g_pPlayerCtrl->m_TouchCtrl->GetRockerTouchDy() < -100 * Root::getSingleton().getScreenDpi() * 0.0098f/* ->1.0/102*/)
			if (g_pPlayerCtrl->m_InputInfo->dpadValueY > 1.8f && !g_pPlayerCtrl->getRun())
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = true;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}

			/*if (g_pPlayerCtrl->m_TouchCtrl->GetRockerTouchDy() != 0)
			{
				LOG_INFO("g_pPlayerCtrl->m_InputInfo->dpadValueY  %f", g_pPlayerCtrl->m_InputInfo->dpadValueY);
				//LOG_INFO("g_pPlayerCtrl->m_TouchCtrl->GetRockerTouchDy() %f", g_pPlayerCtrl->m_TouchCtrl->GetRockerTouchDy());
				//LOG_INFO("-200 * Root::getSingleton().getScreenDpi() * 0.0098f %f", -100 * Root::getSingleton().getScreenDpi() * 0.0098f);
			}*/

			//	LOG_INFO("g_pPlayerCtrl->m_InputInfo->dpadValueX %f", g_pPlayerCtrl->m_InputInfo->dpadValueX);
				//LOG_INFO("g_pPlayerCtrl->m_InputInfo->dpadValueY %f", g_pPlayerCtrl->m_InputInfo->dpadValueY);
		}
		else
		{
			if (g_pPlayerCtrl->m_TouchCtrl->GetButtonDown(BUTTON_FORWARD))
			{
				if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[0] < 250)
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = true;
					m_RunKeyDownButton = BUTTON_FORWARD;
				}
				else
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = false;
				}
				m_KeyDownMark[0] = Rainbow::Timer::getSystemTick();
			}
			else if (g_pPlayerCtrl->getOPWay() != PLAYEROP_WAY_BASKETBALLER || m_RunKeyDownButton == BUTTON_FORWARD)
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}

			if (g_pPlayerCtrl->m_ViewMode == CameraControlMode::CAMERA_TPS_BACK_2)
			{
				for (int keycode = BUTTON_BACK; keycode <= BUTTON_FORWARDRIGHT; keycode++)
				{
					if (g_pPlayerCtrl->m_TouchCtrl->GetButtonDown((UIButtonKey)keycode))
					{
						if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[keycode - BUTTON_BACK + 1] < 250)
						{
							g_pPlayerCtrl->m_InputInfo->triggerRun = true;
							m_RunKeyDownButton = keycode;
						}
						else
						{
							g_pPlayerCtrl->m_InputInfo->triggerRun = false;
						}
						m_KeyDownMark[keycode - BUTTON_BACK + 1] = Rainbow::Timer::getSystemTick();
					}
					else if (m_RunKeyDownButton == keycode)
					{
						g_pPlayerCtrl->m_InputInfo->triggerRun = false;
					}
				}
			}
		}

		/*
		载具：指北时yaw为0，往东递增至180，往西递减至-180；
		镜头：取m_RotateY,N为0，沿着顺时针0到360
		摇杆: 根据摇杆位置取atan2值
		以镜头为参考系，角度均转换为0~360，载具方向和摇杆方向保持一致。
		摇杆动，镜头不动->载具转向；镜头动，摇杆不动->载具转向
		摇杆角度：镜头偏转角+摇杆偏转角；载具角度：直接获取。
		*/

		//物理机械键位控制
		//摇杆模式
		auto RidComp = g_pPlayerCtrl->getRiddenComponent();
		if (RidComp && RidComp->isVehicleController())
		{
			ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();

			if (g_pPlayerCtrl->m_TouchCtrl->isRockerMode() && vehicle != NULL)
			{
				VehicleAssembleLocoMotion* loc = static_cast<VehicleAssembleLocoMotion*>(vehicle->getLocoMotion());
				float dx = g_pPlayerCtrl->m_InputInfo->dpadValueX;
				float dy = g_pPlayerCtrl->m_InputInfo->dpadValueY;

				if (loc && (dx != 0 || dy != 0))
				{	/*
					DIR_NEG_X = 0,
					DIR_POS_X = 1
					DIR_NEG_Z = 2
					DIR_POS_Z = 3
					*/

					int dir = vehicle->getRiddenBindSeatDir(g_pPlayerCtrl);
					Rainbow::Vector3f pos;
					Rainbow::Quaternionf quat;
					getVehiclePhysActorPos(loc, pos, quat);
					//Rainbow::Vector3f euler = quat.EulerAngle();
					float w = quat.w;
					float x = quat.y;
					float y = quat.x;
					float z = quat.z;
					float vehicle_Yaw = atan2(2 * (w * x + z * y), 1 - 2 * (x * x + y * y)) * DEGS_PER_RAD;
					//LOG_INFO("%f, %f, %f", euler.x, euler.y, vehicle_Yaw);
					float camera_Yaw = g_pPlayerCtrl->getCamera()->m_RotateYaw;
					float rocker_Yaw = ATan2ToAngle(g_pPlayerCtrl->m_InputInfo->dpadValueY, g_pPlayerCtrl->m_InputInfo->dpadValueX);

					//根据车头方向调整角度，保持为指北顺时针0~360
					switch (dir)
					{
					case DIR_NEG_X:
						if (vehicle_Yaw >= 90 && vehicle_Yaw <= 180)
							vehicle_Yaw -= 90.0f;
						else
							vehicle_Yaw += 270.0f;
						break;
					case DIR_POS_X:
						if (vehicle_Yaw >= -90.0f && vehicle_Yaw <= 180.0f)
							vehicle_Yaw += 90.0f;
						else
							vehicle_Yaw += 450.0f;
						break;
					case DIR_NEG_Z:
						vehicle_Yaw += 180.0f;
						break;
					case DIR_POS_Z:
						if (vehicle_Yaw < 0)
							vehicle_Yaw = 360.0f + vehicle_Yaw;
						break;

					}

					if (rocker_Yaw <= 90.0f && rocker_Yaw >= -180.0f)
						rocker_Yaw = 90.0f - rocker_Yaw;
					else
						rocker_Yaw = 450.0f - rocker_Yaw;

					if (rocker_Yaw > (360.0f - camera_Yaw) && rocker_Yaw <= 360.0f)
						rocker_Yaw = camera_Yaw - 360.0f + rocker_Yaw;
					else
						rocker_Yaw = camera_Yaw + rocker_Yaw;



					float offset = rocker_Yaw - vehicle_Yaw;
					float forward = sqrt(dx * dx + dy * dy);
					float strafe = 0;
					if (offset >= 0)
					{
						//夹角小于90，转弯前进
						if (offset <= 90.0f || offset >= 270.0f)
						{
							//forward = fabs(dy);
							strafe = (offset <= 90.0f ? offset : (offset - 360.0f)) / 90.0f;
						}
						else
						{
							forward = -forward;
							strafe = (offset - 180.0f) / 90.0f;
						}
					}
					else
					{
						if (offset >= -90.0f || offset <= -270.0f)
						{
							//forward = fabs(dy);
							strafe = (offset >= -90.0f ? offset : (offset + 360)) / 90.0f;
						}
						else
						{
							forward = -forward;
							strafe = (180.0f + offset) / 90.0f;
						}
					}

					int speedFactor = (int)(vehicle->getCurSpeedShow() / 10);
					if (fabs(offset) / speedFactor <= 2)
						strafe = 0;
					//LOG_INFO("vehicle:%f....rocker:%f....strafe:%f", vehicle_Yaw, rocker_Yaw, strafe);

					if (forward >= 0)
					{
						if (forward > 1.0f) forward = 1.0f;
						if (vehicle->hasFuel() == false) forward = 0;
						g_pPlayerCtrl->m_VehicleControlInputs->setAccel(forward);
						g_pPlayerCtrl->m_VehicleControlInputs->setBrake(0.0f);
					}
					else
					{
						if (forward < -1.0f) forward = -1.0f;
						if (vehicle->hasFuel() == false) forward = 0;
						g_pPlayerCtrl->m_VehicleControlInputs->setAccel(0.0f);
						g_pPlayerCtrl->m_VehicleControlInputs->setBrake(-forward);
						strafe = 0 - strafe;
					}

					//船跟车的方向是相反的
					if (vehicle->isRudder())
						strafe = -strafe;

					g_pPlayerCtrl->m_VehicleControlInputs->setSteer(strafe);

				}
				else
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccel(0);
					g_pPlayerCtrl->m_VehicleControlInputs->setBrake(0);
					g_pPlayerCtrl->m_VehicleControlInputs->setSteer(0);
				}

				//原来的摇杆转向逻辑(不考虑车头和摇杆夹角）
				//float dx = g_pPlayerCtrl->m_InputInfo->dpadValueX;
				//float dy = g_pPlayerCtrl->m_InputInfo->dpadValueY;
				//g_pPlayerCtrl->m_TouchCtrl->GetDpadValue(dy, dx);
				//LOG_INFO("rocker:horizontal %f, vertical%f", dx, dy);

				/*if (dx < -1)
					dx = -1.0f;
				else if (dx > 1)
					dx = 1.0f;

				if (dy > 1)
					dy = 1.0f;
				else if (dy < -1)
					dy = -1.0f;

				//前
				if (dy >= 0)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccel(dy);
					g_pPlayerCtrl->m_VehicleControlInputs->setBrake(0.0f);
				}
				if (dy <= 0)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccel(0.0f);
					g_pPlayerCtrl->m_VehicleControlInputs->setBrake(-dy);
				}

				g_pPlayerCtrl->m_VehicleControlInputs->setSteer(dx);*/
			}
			//方向键模式
			else
			{
				float dx = g_pPlayerCtrl->m_InputInfo->moveStrafe;
				float dy = g_pPlayerCtrl->m_InputInfo->moveForward;
				if (vehicle->hasFuel() == false) dy = 0;
				//LOG_INFO("keyboard:horizontal %f, vertical%f", dx, dy);
				if (dy >= 0)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccel(dy);
					g_pPlayerCtrl->m_VehicleControlInputs->setBrake(0.0f);
				}
				if (dy <= 0)
				{
					g_pPlayerCtrl->m_VehicleControlInputs->setAccel(0.0f);
					g_pPlayerCtrl->m_VehicleControlInputs->setBrake(-dy);
				}

				//船跟车的方向是相反的
				if (vehicle->isRudder())
					dx = -dx;

				g_pPlayerCtrl->m_VehicleControlInputs->setSteer(dx);
			}

		}


		if (g_pPlayerCtrl->m_TouchCtrl->hasTap)
		{
			g_pPlayerCtrl->m_InputInfo->tap = true;
			g_pPlayerCtrl->m_InputInfo->clickPosX = g_pPlayerCtrl->m_TouchCtrl->tapPosX;
			g_pPlayerCtrl->m_InputInfo->clickPosY = g_pPlayerCtrl->m_TouchCtrl->tapPosY;
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->tap = false;
			g_pPlayerCtrl->m_InputInfo->clickPosX = -1;
			g_pPlayerCtrl->m_InputInfo->clickPosY = -1;
		}

		g_pPlayerCtrl->m_InputInfo->flyUp = 0;

		if (g_pPlayerCtrl->m_TouchCtrl->GetButton(BUTTON_FLYUP) || g_pPlayerCtrl->m_TouchCtrl->GetKey(SDLK_SPACE))
		{
			g_pPlayerCtrl->m_InputInfo->flyUp = 1;
		}
		else if (g_pPlayerCtrl->m_TouchCtrl->GetButton(BUTTON_FLYDOWN) || g_pPlayerCtrl->m_TouchCtrl->GetKey(SDLK_ALT))
		{
			g_pPlayerCtrl->m_InputInfo->flyUp = -1;
		}
		//g_pPlayerCtrl->m_InputInfo->useAction = m_ButtonDownMap[BUTTON_ACTION];

		g_pPlayerCtrl->m_InputInfo->useAction = g_pPlayerCtrl->m_TouchCtrl->GetButton(BUTTON_ACTION);
		g_pPlayerCtrl->m_InputInfo->isLongPress = g_pPlayerCtrl->m_TouchCtrl->isLongPress;
		g_pPlayerCtrl->m_InputInfo->triggerLongPress = g_pPlayerCtrl->m_TouchCtrl->triggerLongPress;
		g_pPlayerCtrl->m_InputInfo->longPressEnd = g_pPlayerCtrl->m_TouchCtrl->longPressEnd;
		g_pPlayerCtrl->m_InputInfo->longPressX = g_pPlayerCtrl->m_TouchCtrl->longPressX;
		g_pPlayerCtrl->m_InputInfo->longPressY = g_pPlayerCtrl->m_TouchCtrl->longPressY;
		g_pPlayerCtrl->m_InputInfo->useActionTrigger = g_pPlayerCtrl->m_TouchCtrl->GetButtonDown(BUTTON_ACTION);
		g_pPlayerCtrl->m_InputInfo->useActionEnd = g_pPlayerCtrl->m_TouchCtrl->GetButtonUp(BUTTON_ACTION);
		g_pPlayerCtrl->m_InputInfo->reload = g_pPlayerCtrl->m_TouchCtrl->GetButtonDown(BUTTON_GUNRELOAD) || g_pPlayerCtrl->m_isReloadMagazine;
		g_pPlayerCtrl->m_InputInfo->usePassOrCatchBall = g_pPlayerCtrl->m_TouchCtrl->GetButton(BUTTON__PASSBALL_OR_CATCHBALL);
		g_pPlayerCtrl->m_InputInfo->usePassOrCatchBallEnd = g_pPlayerCtrl->m_TouchCtrl->GetButtonUp(BUTTON__PASSBALL_OR_CATCHBALL);
		g_pPlayerCtrl->m_isReloadMagazine = false;

		if (!canControl)
			g_pPlayerCtrl->m_InputInfo->useAction = g_pPlayerCtrl->m_InputInfo->isLongPress = g_pPlayerCtrl->m_InputInfo->tap = false;


	}
	else
	{
		//int safeWidth, safeHeight = 0;
		//GetClientInfoProxy()->getClientWindowSize( safeWidth, safeHeight );

		if (g_pPlayerCtrl && g_pPlayerCtrl->m_PCCtrl)
			g_pPlayerCtrl->m_PCCtrl->update(dtime);

		//triggerPCVehicleControlKeys();

		//突发恶疾 失去控制
		/*SandboxResult result = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().
			Emit("buff_playerIsCanCtr", SandboxContext(nullptr));
		if (result.IsSuccessed()) {
			return;
		}*/

		LivingAttrib* attrib = g_pPlayerCtrl->getLivingAttrib();
		if (attrib && attrib->hasBuff(SUDDEN_ILLNESS_BUFF)) {
			return ;
		}

		// 侧视角下，会调整移动方向的按键
		int curForwardKeyCode = getCurForwardKeyCode();
		//其他3个方向
		int curLeftKeyCode = GameSettings::GetInstance().keyBindLeft->getCurrentKeyCode();
		int curBackKeyCode = GameSettings::GetInstance().keyBindBack->getCurrentKeyCode();
		int curRightKeyCode = GameSettings::GetInstance().keyBindRight->getCurrentKeyCode();

		//--------------get InputInfo start ------------------------------
		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(curForwardKeyCode))
		{
			if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[0] < 250)
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = true;
				m_RunKeyDownButton = curForwardKeyCode;
			}
			else
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}
			m_KeyDownMark[0] = Rainbow::Timer::getSystemTick();
		}
		else if (g_pPlayerCtrl->getOPWay() != PLAYEROP_WAY_BASKETBALLER || m_RunKeyDownButton == curForwardKeyCode)
		{
			g_pPlayerCtrl->m_InputInfo->triggerRun = false;
		}

		if (g_pPlayerCtrl->m_ViewMode == CameraControlMode::CAMERA_TPS_BACK_2)
		{
			if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(curLeftKeyCode))
			{
				if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[1] < 250)
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = true;
					m_RunKeyDownButton = curLeftKeyCode;
				}
				else
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = false;
				}
				m_KeyDownMark[1] = Rainbow::Timer::getSystemTick();
			}
			else if (m_RunKeyDownButton == curLeftKeyCode)
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}

			if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(curBackKeyCode))
			{
				if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[2] < 250)
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = true;
					m_RunKeyDownButton = curBackKeyCode;
				}
				else
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = false;
				}
				m_KeyDownMark[2] = Rainbow::Timer::getSystemTick();
			}
			else if (m_RunKeyDownButton == curBackKeyCode)
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}

			if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(curRightKeyCode))
			{
				if (Rainbow::Timer::getSystemTick() - m_KeyDownMark[3] < 250)
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = true;
					m_RunKeyDownButton = curRightKeyCode;
				}
				else
				{
					g_pPlayerCtrl->m_InputInfo->triggerRun = false;
				}
				m_KeyDownMark[3] = Rainbow::Timer::getSystemTick();
			}
			else if (m_RunKeyDownButton == curRightKeyCode)
			{
				g_pPlayerCtrl->m_InputInfo->triggerRun = false;
			}
		}


		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindSprint->getCurrentKeyCode()))
		{
			g_pPlayerCtrl->m_InputInfo->triggerRun = true;
		}
		//篮球 阻挡操作不能跑
		if (!canControl || (g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_BASKETBALLER && g_pPlayerCtrl->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT))
		{
			g_pPlayerCtrl->m_InputInfo->triggerRun = false;
		}

		if (g_pPlayerCtrl->canControl())
		{
			g_pPlayerCtrl->m_InputInfo->jump = g_pPlayerCtrl->m_PCCtrl->GetKey(GameSettings::GetInstance().keyBindJump->getCurrentKeyCode());
			g_pPlayerCtrl->m_InputInfo->triggerJump = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindJump->getCurrentKeyCode());
			g_pPlayerCtrl->m_InputInfo->jumpRelease = g_pPlayerCtrl->m_PCCtrl->GetKeyUp(GameSettings::GetInstance().keyBindJump->getCurrentKeyCode());
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->jump = g_pPlayerCtrl->m_InputInfo->triggerJump = g_pPlayerCtrl->m_InputInfo->jumpRelease = false;
			g_pPlayerCtrl->m_InputInfo->moveForward = g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}
		g_pPlayerCtrl->m_InputInfo->flyUp = 0;
		g_pPlayerCtrl->m_InputInfo->reload = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindChangeMagazine->getCurrentKeyCode()) || g_pPlayerCtrl->m_isReloadMagazine;
		g_pPlayerCtrl->m_isReloadMagazine = false;

		if (g_pPlayerCtrl->m_PCCtrl->GetKey(GameSettings::GetInstance().keyBindJump->getCurrentKeyCode()))
		{
			g_pPlayerCtrl->m_InputInfo->flyUp = 1;
		}
		else if (!GetIClientGameManagerInterface()->getICurGame()->isOperateUI() && g_pPlayerCtrl->m_PCCtrl->GetKey(GameSettings::GetInstance().keyBindSneak->getCurrentKeyCode()))
		{
			g_pPlayerCtrl->m_InputInfo->flyUp = -1;
		}


#ifdef	IWORLD_TARGET_PC
		if (GetAsyncKeyState(SDLK_LBUTTON) || g_pPlayerCtrl->m_PCCtrl->GetXInputKey(SDLK_LBUTTON))
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_LBUTTON);
			g_pPlayerCtrl->m_InputInfo->leftClick = g_pPlayerCtrl->m_PCCtrl->GetKey(SDLK_LBUTTON);
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->leftClick = false;
		}

		if (GetAsyncKeyState(SDLK_RBUTTON) || g_pPlayerCtrl->m_PCCtrl->GetXInputKey(SDLK_RBUTTON))
		{
			g_pPlayerCtrl->m_InputInfo->rightClickDown = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_RBUTTON);
			g_pPlayerCtrl->m_InputInfo->rightClick = g_pPlayerCtrl->m_PCCtrl->GetKey(SDLK_RBUTTON);
		}
		else if (GetAsyncKeyState(69/* 微软的69(0x45)对应 SDLK_e */) || g_pPlayerCtrl->m_PCCtrl->GetXInputKey(SDLK_e))
		{
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_e);
		}
		// 用于调试枪械 的lua逻辑进行热重载 --- by charles xie
		else if (GetAsyncKeyState(SDLK_F2) || g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_F2))
		{
			g_pPlayerCtrl->ResetAllStates();
		}
		// 用于调试枪械 一键装备枪械子弹 --- by charles xie
		else if (GetAsyncKeyState(SDLK_F3) || g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_F3))
		{
			//g_pPlayerCtrl->getBackPack()->addItem(15014, 1);
			//g_pPlayerCtrl->getBackPack()->addItem(3101101,1);
			//g_pPlayerCtrl->getBackPack()->addItem(15003,120);
			//g_pPlayerCtrl->getBackPack()->addItem(4090124, 1);
			//g_pPlayerCtrl->getBackPack()->addItem(4090108, 1);
			//g_pPlayerCtrl->getBackPack()->addItem(5300014, 1);
			//g_pPlayerCtrl->getBackPack()->addItem(3070002, 1);
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = false;
		}
#else
		g_pPlayerCtrl->m_InputInfo->rightClickDown = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_RBUTTON);
		g_pPlayerCtrl->m_InputInfo->leftClickDown = g_pPlayerCtrl->m_PCCtrl->GetKeyDown(SDLK_LBUTTON);
		g_pPlayerCtrl->m_InputInfo->leftClick = g_pPlayerCtrl->m_PCCtrl->GetKey(SDLK_LBUTTON);
		g_pPlayerCtrl->m_InputInfo->rightClick = g_pPlayerCtrl->m_PCCtrl->GetKey(SDLK_RBUTTON);
#endif

		g_pPlayerCtrl->m_InputInfo->leftClickUp = g_pPlayerCtrl->m_PCCtrl->GetKeyUp(SDLK_LBUTTON);
		g_pPlayerCtrl->m_InputInfo->rightClickUp = g_pPlayerCtrl->m_PCCtrl->GetKeyUp(SDLK_RBUTTON);
		g_pPlayerCtrl->m_InputInfo->checkRightClickUp = g_pPlayerCtrl->m_InputInfo->rightClickUp;

		g_pPlayerCtrl->m_InputInfo->clickPosX = 0.5f;
		g_pPlayerCtrl->m_InputInfo->clickPosY = 0.5f;

		if (!canControl)
		{
			g_pPlayerCtrl->m_InputInfo->leftClick = g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
		}


		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindJump->getCurrentKeyCode()))
		{
			if (GetWorldManagerPtr()->isRemote())
				return;
#ifdef IWORLD_DEV_BUILD

			if (Timer::getSystemTick() - g_pPlayerCtrl->m_LastJumpMark < 250)
			{
				if (g_pPlayerCtrl->isNewMoveSyncSwitchOn())
				{
					if (!g_pPlayerCtrl->getFlying())
						g_pPlayerCtrl->changeMoveFlag(IFC_Fly, true);
					else
						g_pPlayerCtrl->changeMoveFlag(IFC_Fly, false);
				}
				else
					g_pPlayerCtrl->setFlying(!g_pPlayerCtrl->getFlying());
				return;
			}
			g_pPlayerCtrl->m_LastJumpMark = Timer::getSystemTick();
#else
			if ((GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()) ||
				(g_pPlayerCtrl->isInSpectatorMode() && g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FREE) || 
				GetClientInfoProxy()->IsCurrentUserOuterChecker() ||
				GetClientAppProxy()->checkIsGMWhiteListMember())
			{
				if (Timer::getSystemTick() - g_pPlayerCtrl->m_LastJumpMark < 250)
				{
					if (g_pPlayerCtrl->isNewMoveSyncSwitchOn())
					{
						if (!g_pPlayerCtrl->getFlying())
							g_pPlayerCtrl->changeMoveFlag(IFC_Fly, true);
						else
							g_pPlayerCtrl->changeMoveFlag(IFC_Fly, false);
					}
					else
						g_pPlayerCtrl->setFlying(!g_pPlayerCtrl->getFlying());
				}
			}
			g_pPlayerCtrl->m_LastJumpMark = Timer::getSystemTick();
#endif		
		}
#ifdef TestScriptCamera
		//镜头测试代码
		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(187))
		{
			CameraManager::GetInstance().addScriptCamera("test");
			CameraManager::GetInstance().changeScriptCamera("test");
		}
		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(9))
		{
			if (CameraManager::GetInstance().getSpCameraCount())
			{
				testCameraCounter = testCameraCounter % CameraManager::GetInstance().getSpCameraCount();
				char tmpName[128];
				sprintf(tmpName, "testcamera%d", testCameraCounter);
				CameraManager::GetInstance().changeScriptCamera(tmpName);
				testCameraCounter++;
			}
		}
		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(75))
		{
			CameraManager::GetInstance().changeMainGameCamera();
		}
#endif // TestScriptCamera

		if (GetIClientGameManagerInterface()->getICurGame()->isOperateUI() || !g_pPlayerCtrl->m_EnableInput)
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = false;
			g_pPlayerCtrl->m_InputInfo->leftClick = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = false;
			g_pPlayerCtrl->m_InputInfo->leftClickUp = false;
			g_pPlayerCtrl->m_InputInfo->rightClickUp = false;
			g_pPlayerCtrl->m_InputInfo->checkRightClickUp = false;
			g_pPlayerCtrl->m_InputInfo->sneak = false;
			g_pPlayerCtrl->m_InputInfo->dropItem = false;
			g_pPlayerCtrl->m_InputInfo->jump = false;
			g_pPlayerCtrl->m_InputInfo->triggerJump = false;
			g_pPlayerCtrl->m_InputInfo->jumpRelease = false;

			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}

		if (!g_pPlayerCtrl->m_EnableMoveInput)
		{
			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}

		if (!g_pPlayerCtrl->m_EnableActionInput)
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = false;
			g_pPlayerCtrl->m_InputInfo->leftClick = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = false;
			g_pPlayerCtrl->m_InputInfo->leftClickUp = false;
			g_pPlayerCtrl->m_InputInfo->rightClickUp = false;
			g_pPlayerCtrl->m_InputInfo->checkRightClickUp = false;
		}

		if (!g_pPlayerCtrl->m_PCCtrl->isSightMode())
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = false;
			g_pPlayerCtrl->m_InputInfo->leftClick = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = false;
			g_pPlayerCtrl->m_InputInfo->leftClickUp = false;
			g_pPlayerCtrl->m_InputInfo->rightClickUp = true;
		}

		g_pPlayerCtrl->m_InputInfo->useActionEnd = g_pPlayerCtrl->m_InputInfo->rightClickUp;
	}

	// todo the code above s dreadful! and this code is for UI penetration as same as PC
	if (GetClientInfoProxy()->isMobile())
	{
		if (GetIClientGameManagerInterface()->getICurGame()->isOperateUI() || !g_pPlayerCtrl->m_EnableInput)
		{
			g_pPlayerCtrl->m_InputInfo->triggerLongPress = false;
			g_pPlayerCtrl->m_InputInfo->tap = false;
			g_pPlayerCtrl->m_InputInfo->useAction = false;
			g_pPlayerCtrl->m_InputInfo->useActionTrigger = false;
		}
	}

	if (GetIClientGameManagerInterface()->getICurGame()->isOperateUI())
	{
		g_pPlayerCtrl->m_InputInfo->useActionEnd = true;
	}

	auto guideMgr = GetAdventureGuideMgrProxy();
	// 冒险强制新手引导控制跳跃飞起
	if (!guideMgr->canPlayerJump())
	{
		g_pPlayerCtrl->m_InputInfo->jump = false;
		g_pPlayerCtrl->m_InputInfo->flyUp = 0;
	}
	// 冒险强制新手引导控制跳跃飞起
	if (!guideMgr->canPlayerSneak())
	{
		g_pPlayerCtrl->m_InputInfo->sneak = false;
	}

	// 冒险强制新手引导控制移动
	if (guideMgr->isEnterInGuide())
	{
		if (guideMgr->canPlayerMoveForward())
		{
			if (g_pPlayerCtrl->m_InputInfo->moveForward != 1)
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			}
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}
		else if (guideMgr->canPlayerMoveBack())
		{
			if (g_pPlayerCtrl->m_InputInfo->moveForward != -1)
			{
				g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			}
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}
		else if (guideMgr->canPlayerMoveRight())
		{
			if (g_pPlayerCtrl->m_InputInfo->moveStrafe != 1)
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
			}
			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
		}
		else if (guideMgr->canPlayerMoveLeft())
		{
			if (g_pPlayerCtrl->m_InputInfo->moveStrafe != -1)
			{
				g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
			}
			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
		}
		else if(!guideMgr->canPlayerMove())
		{
			g_pPlayerCtrl->m_InputInfo->moveForward = 0;
			g_pPlayerCtrl->m_InputInfo->moveStrafe = 0;
		}
	}

	// 冒险强制新手引导控制道具使用
	if (!GetAdventureGuideMgrProxy()->canUseItem())
	{
		if (GetClientInfoProxy()->isMobile())
		{
			g_pPlayerCtrl->m_InputInfo->useAction = false;
			g_pPlayerCtrl->m_InputInfo->useActionTrigger = false;
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->rightClickDown = false;
			g_pPlayerCtrl->m_InputInfo->keyEClickDown = false;
			g_pPlayerCtrl->m_InputInfo->rightClick = false;
			g_pPlayerCtrl->m_InputInfo->rightClickUp = false;
			g_pPlayerCtrl->m_InputInfo->checkRightClickUp = false;
		}
	}

	// 冒险强制新手引导控制破坏方块等
	if (!GetAdventureGuideMgrProxy()->canInput())
	{
		g_pPlayerCtrl->m_InputInfo->dropItem = false;
		if (GetClientInfoProxy()->isMobile())
		{
			g_pPlayerCtrl->m_InputInfo->triggerLongPress = false;
			g_pPlayerCtrl->m_InputInfo->tap = false;
		}
		else
		{
			g_pPlayerCtrl->m_InputInfo->leftClickDown = false;
			g_pPlayerCtrl->m_InputInfo->leftClick = false;
			g_pPlayerCtrl->m_InputInfo->leftClickUp = false;
		}
	}
}

void PlayerInputHelper::tick()
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	// 调用按键检测器的tick方法
	m_eKeyDetector.tick();
	// 调用R键检测器的tick方法
	m_rKeyDetector.tick();
	// 调用鼠标右键检测器的tick方法
	m_rightClickDetector.tick();
}

//bool PlayerInputHelper::KeyBanInVehicleMode()
//{
//	if (g_pPlayerCtrl == NULL)
//	{
//		return false;
//	}
//
//	if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindDrop->getCurrentKeyCode()))	//丢弃物品
//		return true;
//	else if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindMount->getCurrentKeyCode()))	//坐骑
//		return true;
//	else if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(Rainbow::SDLK_x))		//表情
//		return true;
//
//	ActorVehicleAssemble* vehicle = g_pPlayerCtrl->getDrivingVehicle();
//	WCoord seatpos = vehicle->getRiddenBindSeatPos(g_pPlayerCtrl);
//	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(seatpos));
//	if (container == NULL)
//		return false;
//
//	for (auto iter = container->m_BindKeyData.begin(); iter != container->m_BindKeyData.end(); iter++)
//	{
//		if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(container->ConvertIdtoKeyVal(iter->first)))
//			return true;
//	}
//
//#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
//	for (int i = 0; i < 8; i++)
//	{
//		if (GetClientGameManagerPtr()->getCurGame() && !GetClientGameManagerPtr()->getCurGame()->isInSetting())
//		{
//			if (GetInputManager().IsControlHold())
//			{
//				if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(i + SDLK_1))
//				{
//					return true;
//				}
//			}
//			else
//			{
//				if (g_pPlayerCtrl->m_PCCtrl->GetKeyDown(GameSettings::GetInstance().keyBindShortcut[i]->getCurrentKeyCode()))
//				{
//					return true;
//				}
//			}
//		}
//	}
//#endif	
//
//	return false;
//}
