﻿#include "LinuxGameApp.h"
#include "Core/GameEngine.h"

#include "AssetPipeline/AssetManager.h"
#include "Misc/FrameTimeManager.h"

#include "File/Packages/PackageAsset.h"
#include "BaseClass/SharePtr.h"

#include "Core/EngineClassRegister.h"
#include "Allocator/MemoryManager.h"
#include "Utilities/Logs/LogSystem.h"
#include "Misc/PlayerSettings.h"

#include "Common/GameStatic.h"
#include "IWorldConfig.h"
#include "Misc/GameSetting.h"

#include "MiniCloud/sim/SimulateMgr.h"
#include "MiniCloud/sim/LiveStreamMgr.h"
#include "ClientInfo.h"
#include "GameInfo.h"
#include "AssetPipeline/AssetProjectSetting.h"
#include "GetClientInfo.h"
#include "LuaInterface.h"
#include "Platforms/PlatformInterface.h"
#include "ZmqProxy.h"
#include "ActionLogger.h"
#include "ClientGameStandaloneServer.h"
#include "OgreTimer.h"
#include <Core/CallBacks/GlobalCallbacks.h>
#include "EffectManager.h"
#include "WorldManager.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "SandboxGameNode.h"
#include "fps/statistics/SandboxStatisticsNode.h"
#include "GeoJsonParser.h"
#include "IClientPlayer.h"
#include "ClientPlayer.h"
#include "DebugJsonCmd.h"
#include "ChunkGenerator.h"
#include "ClientMob.h"
#include "worldEvent/AirDrop/AirDropEvent.h"
#include "Core/worldEvent/WorldEventManager.h"
#include "ChestMgr.h"
#include "sign/HttpSign.h"
#include "MapGeoReport.h"

using namespace MINIW;
 

bool g_bGameQuit = false;

#if PLATFORM_LINUX
#include <unistd.h>
#include <fcntl.h>
#include <sys/file.h>
#include <sys/stat.h>

#include "OgreStringUtil.h"
#include "ClientInfo.h"
#include "client/linux/handler/exception_handler.h"
#include "jemalloc/jemalloc.h"

extern unsigned long long getProcessMemoryBytes();
extern unsigned long long getMemoryUsage();
extern double getCpuUsage();

static int check_pid(const char* pidfile) {
	int pid = 0;
	FILE* f = fopen(pidfile, "r");
	if (f == NULL)
		return 0;
	int n = fscanf(f, "%d", &pid);
	fclose(f);

	if (n != 1 || pid == 0 || pid == getpid())
	{
		return 0;
	}

	if (kill(pid, 0) && errno == ESRCH)
		return 0;

	return pid;
}

static int write_pid(const char* pidfile) {
	FILE* f;
	int pid = 0;
	int fd = open(pidfile, O_RDWR | O_CREAT, 0644);
	if (fd == -1)
	{
		printf("Can't create pidfile [%s].\n", pidfile);
		return 0;
	}
	f = fdopen(fd, "r+");
	if (f == NULL)
	{
		printf("Can't open pidfile [%s].\n", pidfile);
		return 0;
	}

	if (flock(fd, LOCK_EX | LOCK_NB) == -1)
	{
		int n = fscanf(f, "%d", &pid);
		fclose(f);
		if (n != 1)
		{
			printf("Can't lock and read pidfile.\n");
		}
		else
		{
			printf("Can't lock pidfile, lock is held by pid %d.\n", pid);
		}
		return 0;
	}

	pid = getpid();
	if (!fprintf(f, "%d\n", pid))
	{
		printf("Can't write pid.\n");
		close(fd);
		return 0;
	}
	fflush(f);
	//close(fd);

	return pid;
}

static int redirect_fds()
{
	int nfd = open("/dev/null", O_RDWR);
	if (nfd == -1)
	{
		printf("Unable to open /dev/null: \n");
		return -1;
	}
	if (dup2(nfd, 0) < 0)
	{
		printf("Unable to dup2 stdin(0): \n");
		return -1;
	}
	if (dup2(nfd, 1) < 0)
	{
		printf("Unable to dup2 stdout(1): \n");
		return -1;
	}
	if (dup2(nfd, 2) < 0)
	{
		printf("Unable to dup2 stderr(2): \n");
		return -1;
	}

	close(nfd);

	return 0;
}

int daemon_init(const char* pidfile)
{
	int pid = check_pid(pidfile);

	if (pid)
	{
		printf("miniworldserver is already running, pid = %d.\n", pid);
		return 1;
	}

	/*if (daemon(1,1))
	{
		printf("Can't daemonize.\n");
		return 1;
	}*/

	pid = write_pid(pidfile);
	if (pid == 0)
	{
		return 1;
	}

	/*if (redirect_fds())
	{
		return 1;
	}*/

	return 0;
}

void sign_handler(int sig)
{
	GetClientApp().handleSIGUSR2();
}

void check_param_handler(int sig)
{
	printf("==== check_param_handler get SIGUSR1\n");
	bool active = true;
	mallctl("opt.prof_active", NULL, NULL, &active, sizeof(bool));
	active = true;
	mallctl("prof.active", NULL, NULL, &active, sizeof(bool));
	mallctl("prof.dump", NULL, NULL, NULL, 0);

	// tinyxml2::XMLDocument  doc;
	// if (tinyxml2::XML_SUCCESS != doc.LoadFile("../serverroom.xml"))
	// {
	// 	return;
	// }
	// tinyxml2::XMLElement* root = doc.FirstChildElement("ServerRoom");
	// if (root)
	// {
	// 	tinyxml2::XMLElement* server = root->FirstChildElement("server");
	// 	while (server)
	// 	{
	// 		tinyxml2::XMLElement* room = server->FirstChildElement("room");
	// 		while (room)
	// 		{
	// 			const tinyxml2::XMLAttribute* AddrAttr = room->FirstAttribute();
	// 			std::string account;
	// 			HashMap<std::string, std::string> roomParam;

	// 			std::string account_left;
	// 			std::string account_right;
	// 			int iport = 0;

	// 			while (AddrAttr)
	// 			{
	// 				const char* name = AddrAttr->Name();
	// 				const char* value = AddrAttr->Value();

	// 				if (strcmp(name, "account") == 0)
	// 				{
	// 					std::vector<std::string> kv;
	// 					Rainbow::StringUtil::split(kv, value, "-");
	// 					if (kv.size() == 2)
	// 					{
	// 						account_left = kv[0];
	// 						account_right = kv[1];
	// 					}
	// 					else
	// 					{
	// 						account = value;
	// 						roomParam["account"] = value;
	// 					}
	// 				}
	// 				else if (strcmp(name, "type") == 0 && strcmp(value, "office") == 0)
	// 				{
	// 					roomParam["owindex"] = "0";
	// 				}
	// 				else if (strcmp(name, "id") == 0)
	// 				{
	// 					char port[16];
	// 					sprintf(port, "%d", 8700 + atoi(value));
	// 					iport = atoi(value);
	// 					roomParam["port"] = port;
	// 				}
	// 				else if (strcmp(name, "mapname") == 0)
	// 				{
	// 					AddrAttr = AddrAttr->Next();
	// 					continue;
	// 				}
	// 				else
	// 					roomParam[name] = value;
	// 				AddrAttr = AddrAttr->Next();
	// 			}
	// 			auto clientmgr = GetClientInfo();
	// 			if (account_left.size())
	// 			{
	// 				int left = atoi(account_left.c_str());
	// 				int right = atoi(account_right.c_str());
	// 				int mini = atoi(clientmgr->getAccount().c_str());
	// 				if (mini >= left && mini <= right)
	// 				{
	// 					roomParam["account"] = clientmgr->getAccount();
	// 					char port[16];
	// 					sprintf(port, "%d", 8700 + iport + mini - left);
	// 					roomParam["port"] = port;
	// 					account = clientmgr->getAccount();
	// 				}
	// 			}

	// 			if (clientmgr->getAccount() == account)
	// 			{
	// 				HashMap<std::string, std::string>::iterator iter = roomParam.begin();
	// 				while (iter != roomParam.end())
	// 				{
	// 					std::string name_ = clientmgr->getEnterParam(iter->first.c_str());
	// 					//if(clientmgr->getEnterHashValue(iter->first) != iter->second)
	// 					if (name_ != iter->second)
	// 					{
	// 						GetClientApp().handleSIGUSR2();
	// 						break;
	// 					}
	// 					iter++;
	// 				}
	// 				break;
	// 			}

	// 			room = room->NextSiblingElement();
	// 		}
	// 		server = server->NextSiblingElement();
	// 	}
	// }
}


void check_param_handler2(int sig)
{
	printf("==== check_param_handler2 get SIGUSR2\n");
	GetClientApp().setSIGUSR2();
}

namespace {
	bool breakpad_callback(const google_breakpad::MinidumpDescriptor& descriptor,
		void* context,
		bool succeeded) {
		// if succeeded is true, descriptor.path() contains a path
		// to the minidump file. Context is the context passed to
		// the exception handler's constructor.
		// std::cout << "on callback " << succeeded << std::endl;
		return succeeded;
	}
}

#endif

// 显示服务器fps 用于性能优化   by:liusijia
namespace TestOutPut {
	unsigned int m_Frames = 0;
	unsigned int m_FramesPS = 0;
	unsigned long long m_LastTick = 0;
	unsigned long long m_StartTick = 0;

	unsigned int m_Frames10Sec = 0;
	unsigned long long m_StartTick10Sec = 0;
	unsigned int m_FramesLow10Sec = 0;
	unsigned int m_FramesHigh10Sec = 0;

	void ShowServerFps(double tick) {
		unsigned long long currentTick = Rainbow::Timer::getSystemTick();
		if (m_LastTick == 0) { // First call
			m_StartTick10Sec = m_StartTick = m_LastTick = currentTick;
		}
		unsigned long long elapsed = currentTick - m_LastTick;
		if (elapsed > 500) { // More than 500 ms since last call
			Rainbow::GetICloudProxyPtr()->SimpleErrLog(0, 0, "tick_slow", std::string("server tick slow cost:") + to_string(elapsed));
		}
		m_LastTick = currentTick;

		++m_Frames;
		++m_Frames10Sec;

		unsigned long long totalElapsed = currentTick - m_StartTick;
		if (totalElapsed >= 1000) { // More than 1000 ms since start
			if (m_Frames < 10)
				SimpleSLOG("Server run slow, fps is:%d  time: %lld ms", m_Frames, totalElapsed);
			else
				LOG_INFO("ServerFPS:%d ms:%d", m_Frames, totalElapsed);

			if (m_Frames > m_FramesHigh10Sec)
				m_FramesHigh10Sec = m_Frames;
			if (m_Frames < m_FramesLow10Sec || m_FramesLow10Sec == 0)
				m_FramesLow10Sec = m_Frames;

			m_FramesPS = m_Frames;
			m_Frames = 0;
			m_StartTick = currentTick;

		}
		totalElapsed = currentTick - m_StartTick10Sec;
		if (totalElapsed >= 10000) { // More than 10000 ms since start
		    auto   s_Uin = GetClientInfoProxy()->getEnterParam("account");
		    auto   s_RoomId = GetClientInfoProxy()->getEnterParam("room_id");
		    auto   s_MapId  = GetClientInfoProxy()->getEnterParam("toloadmapid");
		    auto   s_HostIp  = GetClientInfoProxy()->getEnterParam("ip");
		    time_t now;
		    time(&now);
		    char tmstr[32] = {0};
		    strftime(tmstr, 32, "%Y-%m-%d %H:%M:%S", localtime(&now));
		    jsonxx::Object log;
		    log << "tm" << now;
		    log << "event" << "fps_10sec";
		    log << "aid" << s_MapId;
		    log << "uin" << s_Uin;
		    log << "roomid" << s_RoomId;
		    log << "hostip" << s_HostIp;

		    log << "low_fps" << m_FramesLow10Sec;
			log << "high_fps" << m_FramesHigh10Sec;
			log << "avg_fps" << (int)((m_Frames10Sec / (totalElapsed / 1000.0)) + 0.5);

			ClientActorMgr* actor_mgr = g_WorldMgr->getWorld(0)->getActorMgr()->ToCastMgr();
			if (actor_mgr) {
				log << "player_num" << actor_mgr->getNumPlayer();
			}

#if PLATFORM_LINUX
		    double    CpuUsage = getCpuUsage();
		    long long memUsage = getMemoryUsage();
		    auto      memBytes = getProcessMemoryBytes();
		    log << "cpu_usage" << CpuUsage;
		    log << "mem_usage" << memUsage;
		    log << "mem_bytes" << memBytes;
#endif

			int actors = 0;
			int players = 0;
			EffectManager::EffectStat effStat;
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->GetStatics(actors, players, effStat) == 0)
			{
			    log << "actors" << actors;
			    log << "effects" << effStat.particles;

				auto* p_world = GetWorldManagerPtr()->getWorld(0);
				std::vector<ClientActor*> actors_list;
				p_world->getActorMgr()->ToCastMgr()->getAllLiveActors(actors_list);
				int cnt = actors_list.size();

				std::map < int, int > actors_type_num;
				for(auto actor : actors_list){
					if (actor )
					{
						actors_type_num[actor->getObjType()]++;
					}
				}

				// 将统计数据添加到日志
				for(auto& pair : actors_type_num){
					std::string key = "actor_type_" + std::to_string(pair.first) + "_count";
					log << key << pair.second;
				}
		    }



			int triggerCount;
			int curTriggerSize;
			if (GetObserverEventManagerPtr() && GetObserverEventManagerPtr()->GetStatics(triggerCount, curTriggerSize) == 0)
			{
				GetObserverEventManagerPtr()->ResetStatics();
			    log << "trigger_count" << triggerCount;
			    log << "left_trigger_size" << curTriggerSize;
			}

		    // auto gameroot = MNSandbox::GetCurrentGameRoot();
			// if (gameroot)
			// {
			// 	int nodecount = gameroot->GetDescendantCount();
			//     log << "sandbox_node_count" << nodecount;
			// }
			int nodecount = MNSandbox::Statistics::NodeCount::GetInstance().GetTotal();
			log << "sandbox_node_count" << nodecount;

		    snprintf(tmstr, sizeof(tmstr), "%llu", Rainbow::GetICloudProxyPtr()->genLogID(now));
		    log << "log_id" << std::string(tmstr);
		    std::string logstr = log.json_nospace();
		    FPSLOG(INFO) << logstr;
		    m_Frames10Sec = 0;
			m_FramesLow10Sec = 0;
			m_FramesHigh10Sec = 0;
			m_StartTick10Sec = currentTick;
		}
	}
}


// 地图地理信息上报器
MapGeoReporter g_MapGeoReporter;

#if DEDICATED_SERVER
void checkAndSetLogPath()
{
	#if   PLATFORM_LINUX
	static unsigned tick_count = 0;
	static time_t last_set_timestamp = 0;

	++ tick_count;
	if (last_set_timestamp != 0 && tick_count % 1200 != 0)
		return;

	time_t now = MINIW::GetTimeStamp();
	#define DIFF_FILE_INTERVAL (3600 * 8)
	if (now / DIFF_FILE_INTERVAL != last_set_timestamp / DIFF_FILE_INTERVAL)  // 小时数不同, 未考虑半个时区的情况
	{
		tm * timeinfo = localtime(&now);

		char log_file_path[1024] = {0x00};
			char *currentDir = get_current_dir_name();
			// 房主uin_房间ID_月日_时
			snprintf(log_file_path, sizeof(log_file_path), "%s/logs/%s_%s_%02d%02d_%02d%02d.log"
				, get_current_dir_name()
				, GetClientInfoProxy()->getEnterParam("account")
				, GetClientInfoProxy()->getEnterParam("room_id")
				, timeinfo->tm_mon + 1
				, timeinfo->tm_mday
				, timeinfo->tm_hour
				, timeinfo->tm_min
			);
			free(currentDir);

		ResetConsoleLog(log_file_path);
		last_set_timestamp = now;
	}
	#endif
}
#endif // DEDICATED_SERVER

namespace Rainbow
{
	void LinuxGameApp::SystemInit(const char* cmdStr)
	{
		ClientApp::SystemInit(cmdStr);

		auto cliInfo = GetClientInfo();
		m_WriteLog = strcmp(GetClientInfo()->getEnterParam("rent_lua_log"), "1") == 0;
		GetLogSystem().SetLogType(kLogTypeDebug);

#if DEDICATED_SERVER
		if (m_WriteLog)
			checkAndSetLogPath();
#endif
		const char* assetPath = GetAssetProjectSetting().GetAssetPath();

		GetGameInfo().SetRoomHostType(ROOM_SERVER_RENT);
#if PLATFORM_LINUX
		printf("lpCmdLine = %s\n", cmdStr);
		printf("maptag=%s\n", cliInfo->getEnterParam("maptag"));
		std::string pid = std::string("./pid/") + cliInfo->getAccount();
		std::string room_id_ = cliInfo->getEnterParam("room_id");
		if (room_id_.length() > 0) {
			pid += "_";
			pid += room_id_;
			//clientmgr->setRoomHostType(ROOM_SERVER_RENT);         //玩家租赁服
		}
		pid += ".pid";

		std::string path = "mini";
		std::string data_file_dir_ = cliInfo->getEnterParam("data_file_dir");
		if (data_file_dir_.length() > 0) {
			path = data_file_dir_ + "mini";    //是否有data_file_dir
		}

		path += cliInfo->getAccount();

		if (room_id_.length() > 0) {
			path += "_";
			path += room_id_;
		}
		path += "/";
		mkdir(path.c_str(), 0755);
		m_WritePathDir = path;

		auto clientmgr = GetClientInfo();
		if (daemon_init(pid.c_str()) == 0)
		{
			//if (!clientmgr->onInitialize(NULL, path.c_str(), 0, 0, NULL))
			//{
			//	printf("clientmgr->create error.\n");
			//	return 0;
			//}
			m_pid = pid;
			signal(SIGPIPE, SIG_IGN);
			signal(SIGINT, sign_handler);
			signal(SIGTERM, sign_handler);
			signal(SIGUSR1, check_param_handler);
			signal(SIGUSR2, check_param_handler2);

		}
		else
		{
			printf("miniworldserver already start.\n");
		}
#endif

#ifdef WINDOWS_SERVER
		// windows上服务器跟客户端可能在一起，避免数据冲突，修改以下写入目录
		m_WritePathDir = m_WritePathDir + "winserver/";
#endif

		// linux 需要使用房间id做目录，否则同地图会有数据冲突
		SimpleSLOG("LinuxGameApp use writepath :%s", m_WritePathDir.c_str());
		// systeminit已经添加，这里要删除再重新添加
		GetFileManager().RemovePackage(BUILTIN_WRITE_PATH_PKGNAME);
		FileSystemEntry fileEntry(m_WritePathDir.c_str());
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, BUILTIN_WRITE_PATH_PKGNAME, fileEntry.Path(), 10001);
	}

	void LinuxGameApp::GameExit(bool restart)
	{
		ClientApp::GameExit(restart);
#if PLATFORM_LINUX
		MINIW::GameExit(restart, m_pid.c_str());
#endif
	}

	//每一帧的后面执行,在渲染之后
	void LinuxGameApp::AppFrameTickEnd()
	{
		if (m_DoFrame) {
#if DEBUG_MODE
			GetTransformManager().m_DEBUG_NoDirtyAnyMore = false;
#endif
			Rainbow::GlobalCallbacks::Get().endFrameTick.Invoke();
			//Object对象的GC
//资源GC
			AssetManager& resMgr = GetAssetManager();
			resMgr.GCResouces();
			resMgr.ChkBatchGCResouces();

#if ENABLE_MEMORY_MANAGER
			GetMemoryManager().FrameMaintenance();
#endif
		}

		//帧率的时间等待
		GetFrameTimeManager().Sync(FrameTimeManager::AfterPlayerLoop);
	}

//#define USING_DEBUG_BUILD_PKG
	void LinuxGameApp::OnPKGInit()
	{
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "root", "", 1, FILETYPE_OUTSIDE_ASSET);
		GetIWorldConfig().LoadWorldCfg();
#ifdef STUDIO_SERVER
		core::string assetScriptPath;
		int nGameEnv = GetIWorldConfig().getGameData("game_env");
		if (nGameEnv >= 10)
		{
			assetScriptPath = "../OverseaScript/Script/";
			Rainbow::GetAssetProjectSetting().SetAdditionalAssetPath("../OverseaAssets/Assets/");
		}
		else
		{
			assetScriptPath = "../DomesticScript/Script/";
			Rainbow::GetAssetProjectSetting().SetAdditionalAssetPath("../DomesticAssets/Assets/");
		}

		const char* assetPath = Rainbow::GetAssetProjectSetting().GetAdditionalAssetPath();
#else
		const char* assetPath = GetAssetProjectSetting().GetAssetPath();
		core::string assetScriptPath = AppendPathName(assetPath, "../Script/");
#endif
		core::string engineRes = GetAssetProjectSetting().GetEngineResourcePath();
		core::string engineRes1 = engineRes + "Assets/";
		core::string engineRes2 = engineRes + "Assets/Resources/";
		core::string resourcePath = assetPath;
		resourcePath = resourcePath + "Resources/";

	    core::string miniGameRes       = AppendPathName(GetAssetProjectSetting().GetEngineResourcePath(), "Assets/Resources/minigame");
	    core::string miniGamePrefabRes = AppendPathName(GetAssetProjectSetting().GetEngineResourcePath(), "Assets/Resources/minigame/prefab");
	    core::string prefabPath        = resourcePath + "prefab/";

	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets", assetPath, 5, FILETYPE_SERIALIZE_FILE);

	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources", resourcePath.c_str(), 2, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/", engineRes1.c_str(), 3, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources", engineRes2.c_str(), 4, FILETYPE_SERIALIZE_FILE);
	    GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/minigame", miniGameRes.c_str(), 4, Rainbow::FILETYPE_SERIALIZE_FILE);
        GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/minigame/prefab", miniGamePrefabRes.c_str(), 4, Rainbow::FILETYPE_SERIALIZE_FILE);

		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "EngineResource/Assets/", engineRes1.c_str(), 3, FILETYPE_SERIALIZE_FILE);
		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "EngineResource/Assets/Resources", engineRes2.c_str(), 2, FILETYPE_SERIALIZE_FILE);
		// GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Script", scriptPath.c_str(), 6, FILETYPE_NONE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/prefab", prefabPath.c_str(), 10, FILETYPE_SERIALIZE_FILE);
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "AssetScript", assetScriptPath.c_str(), 6, FILETYPE_OUTSIDE_ASSET);

		//子项目路径
		GameSetting& gameSetting = GetGameSetting();
		gameSetting.InitCurSubprojectInfo(GetIWorldConfig().getGameData("game_env"));
		core::string subprojectScript = assetScriptPath + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Script/Sub", subprojectScript.c_str(), 7, FILETYPE_SERIALIZE_FILE);
		core::string subprojectAssets = resourcePath + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/Sub", subprojectAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
		core::string engineRes3 = engineRes + "Script/";
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Script", engineRes3.c_str(), 6, FILETYPE_SERIALIZE_FILE);
#ifdef STUDIO_SERVER
		engineRes2 += "/ministudio";
		GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "CommonResource/Assets/Resources/ministudio", engineRes2.c_str(), 4, FILETYPE_SERIALIZE_FILE);
		const char* firstpkgPath = gameSetting.m_FirstPkg.pkgFilePath.c_str();
		if (GetFileManager().IsFileExist(firstpkgPath))
		{
			FilePkgBase* first = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_FirstPkg.name.c_str(), firstpkgPath, gameSetting.m_FirstPkg.priority, FILETYPE_SERIALIZE_FILE);
			first->SetFilePrefix(gameSetting.m_FirstPkg.filePrefix);
		}

		if (GetFileManager().IsFileExist(gameSetting.m_EnginePkg.pkgFilePath.c_str()))
		{
			FilePkgBase* engine = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_EnginePkg.name.c_str(), gameSetting.m_EnginePkg.pkgFilePath.c_str(), gameSetting.m_EnginePkg.priority, FILETYPE_SERIALIZE_FILE);
			engine->SetFilePrefix(gameSetting.m_EnginePkg.filePrefix);
		}

		if (GetFileManager().IsFileExist(gameSetting.m_StudioPkg.pkgFilePath.c_str()))
		{
			FilePkgBase* studioPkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, gameSetting.m_StudioPkg.name.c_str(), gameSetting.m_StudioPkg.pkgFilePath.c_str(), gameSetting.m_StudioPkg.priority, FILETYPE_SERIALIZE_FILE);
			studioPkg->SetFilePrefix(gameSetting.m_StudioPkg.filePrefix);
		}

		dynamic_array<GamePackageInfo>& infoList = gameSetting.m_PkgLists;
		for (int i = 0; i < infoList.size(); i++) {
			GamePackageInfo& info = infoList[i];
			if (GetFileManager().IsFileExist(info.pkgFilePath.c_str()))
			{
				FilePkgBase* studioPkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), info.pkgFilePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
				studioPkg->SetFilePrefix(info.filePrefix);
			}
		}

#endif
#ifdef IWORLD_UNIVERSE_BUILD
			core::string modulesSubAssets = modulesAssets + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
			GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/modules_sub", modulesSubAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
			core::string remotesSubAssets = remotesAssets + gameSetting.m_CurSubprojectInfo.pkgPathPrefix;
			GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, "Assets/Resources/remotes_sub", remotesSubAssets.c_str(), 8, FILETYPE_SERIALIZE_FILE);
#endif
    }

	void LinuxGameApp::AppInit()
	{
		StandaloneServer::s_LoadBeginTime = Rainbow::Timer::getTimeUS();
#ifdef WINDOWS_SERVER
		// 检测启动参数
		std::string mapid = GetClientInfoProxy()->getEnterParam("toloadmapid");
		std::string gameenv = GetClientInfoProxy()->getEnterParam("game_env");
		if (mapid.empty() || gameenv.empty())
		{
			WarningStringMsg("you need setup VS debug cmd param");
			MINIW::PopMessageBox("you need setup VS debug cmd param", "server error");
			exit(1);
			return;
	}
#endif
		//RegisterShaderLibraryClass();

		ClientApp::AppInit();

#ifdef OGRE_USE_CEF3_LIB
		MINIW::GetCef3ManagerPtr()->InitializeCef();
#endif

	}

	void LinuxGameApp::BeginPlay()
	{
		ClientApp::BeginPlay();
	}


	void LinuxGameApp::AppFrameTick()
	{
		static bool quit = false;
		if (quit)
			return;

		MINIW::ClientApp::AppFrameTick();

		if (g_bGameQuit)
		{
			GameExit(false);
			Rainbow::GetGameApp().AppExit();
			quit = true;
			//exit(0);
		}
	}

	void LinuxGameApp::OnTick(float dt)
	{
		unsigned dtick = dt * 1000;


		MINIW::ClientApp::OnTick(dt);

#if DEDICATED_SERVER
		if (m_WriteLog)
			checkAndSetLogPath();
#endif
		if (IsServerInitOk())
		{
			SimulateMgr::getInstance()->Tick(dtick);
			LiveStreamMgr::getInstance()->Tick(dtick);
			TestOutPut::ShowServerFps(dt);
			g_MapGeoReporter.Tick(dt);
		}
	}

	void LinuxGameApp::AppExit()
	{
		SimpleSLOG("LinuxGameApp start AppExit");
		ClientApp::AppExit();
		SimpleSLOG("LinuxGameApp AppExit finish");
	}

	void LinuxGameApp::setSIGUSR2() {
		m_HasSIGUSR2 = true;
	}
	void LinuxGameApp::handleSIGUSR2() {
		if (g_zmqMgr)
			g_zmqMgr->CloseRoom(3, "");
	}
	bool LinuxGameApp::hasSIGUSR2() {
		return m_HasSIGUSR2;
	}

	void LinuxGameApp::OnServerFrameTick()
	{
		if (hasSIGUSR2()) {
			m_HasSIGUSR2 = false;
#if PLATFORM_LINUX
			if (getuid() == 0)
			{
				LOG_INFO("get sig usr2, root ignore it");
				return;
			}
#endif

			GetLuaInterface().callLuaString("__handle_SIGUSR2__()");
		}
		if (g_zmqMgr && g_zmqMgr->CheckAutoExit())
		{
			GetLuaInterface().callLuaString("__handle_autoexit__()");
			ClientInfo_Service* pClientInfo = dynamic_cast<ClientInfo_Service*>(GetClientInfo());
			if (pClientInfo)
			{
				pClientInfo->setClosing();
			}
		}
	}
}
