#pragma once

#include <string>
#include <vector>
#include "GeoJsonParser.h"

namespace MINIW {

/**
 * 地图地理信息上报器
 * 负责收集和上报地图中的各种地理信息，包括玩家位置、NPC位置、建筑、空投、宝箱等
 */
class MapGeoReporter {
public:
    MapGeoReporter();
    ~MapGeoReporter();

    /**
     * 执行地理信息收集和上报
     * @param deltaTime 帧间隔时间
     */
    void Tick(double deltaTime);

    /**
     * 重置定时器
     */
    void Reset();

    /**
     * 设置上报间隔（毫秒）
     * @param intervalMs 间隔时间，默认5000ms
     */
    void SetReportInterval(unsigned long long intervalMs);

private:
    /**
     * 收集所有地理信息并上报
     */
    void CollectAndReportGeoInfo();

    /**
     * 收集玩家位置信息
     */
    void CollectPlayerPositions(std::vector<Feature>& features);

    /**
     * 收集NPC/Actor位置信息
     */
    void CollectActorPositions(std::vector<Feature>& features);

    /**
     * 收集建筑信息
     */
    void CollectBuildingInfo(std::vector<Feature>& features);

    /**
     * 收集空投信息
     */
    void CollectAirdropInfo(std::vector<Feature>& features);

    /**
     * 收集宝箱信息
     */
    void CollectChestInfo(std::vector<Feature>& features);

    /**
     * 获取房间基本信息
     */
    void GetRoomInfo(std::string& roomId, std::string& roomName, std::string& mapId, 
                     std::string& mapVer, std::string& serverIP, int& serverPort);

    /**
     * 获取地图边界信息
     */
    void GetMapBounds(int& mapMaxWidth, int& mapMaxHeight, 
                      int& mapMaxX, int& mapMaxZ, int& mapMinX, int& mapMinZ);

private:
    unsigned long long m_LastTick;       // 上次执行时间
    unsigned long long m_StartTick;      // 开始时间
    unsigned long long m_ReportInterval; // 上报间隔（毫秒）
};

} // namespace MINIW
