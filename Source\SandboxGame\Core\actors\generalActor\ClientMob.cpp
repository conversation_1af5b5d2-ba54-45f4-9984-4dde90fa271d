#include "ClientMob.h"
#include "ActorVision.h"
#include "File/FileManager.h"
#include "backpack.h"
#include "special_blockid.h"
#include "ActorEarthCoreMan.h"
#include "ActorSandMan.h"
#include "ActorDragonMount.h"
#include "ActorNpc.h"
#include "ActorDesertBusinessman.h"
#include "ActorPatrolMob.h"
#include "ActorIslandBusinessman.h"
#include "ActorPearl.h"
#include "MpActorManager.h"
#include "ActorGhost.h"
#include "GameNetManager.h"
#include "ActorVillager.h"
#include "MpGameSurviveCdnResMgr.h"
#include "AIArrowAttack.h"
#include "AIEatFeedBlock.h"
#include "Text3D/NameText3D.h"
#include "BehaviorTreeManager.h"
#include "MobEventListen.h"
#include "ActorMoonMount.h"
#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"
#include "ActorPackHorse.h"
#include "ActorStorageBoxHorse.h"
#include "ActorSavagePriest.h"
#include "ScriptComponent.h"
#include "DropItemComponent.h"
#include "AttackingTargetComponent.h"
#include "HPProgressComponent.h"
#include "TeamComponent.h"
#include "GrowComponent.h"
#include "BreedComponent.h"
#include "QuiteComponent.h"
#include "AngryComponent.h"
#include "TameComponent.h"
#include "PlotComponent.h"
#include "DialogueComponent.h"
#include "ClientActorFuncWrapper.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "ClientActorHelper.h"
#include "TriggerComponent.h"
#include "ToAttackTargetComponent.h"
#include "ActorSandworm.h"
#include "ActorFrostWyrm.h"
#include "SandboxNodeBoundingBox.h"
#include "AttackedComponent.h"
#include "ActorDesertVillager.h"
#include "ActorFishingVillager.h"
#include "ActorFisherMan.h"
#include "ActorSeaSpiritGuarding.h"
#include "ActorCrab.h"
#include "ActorDriftBottle.h"
#include "ActorHippocampus.h"
#include "ActorPirateShip.h"
#include "RiddenComponent.h"
#include "ActorYak.h"
#include "ActorSnowMan.h"
#include "ActorSnowHare.h"
#include "Optick/optick.h"
#include "Core/blocks/container_world.h"
#include "WorldManager.h"
#include "Entity/OgreEntity.h"
#include "SandBoxManager.h"
#include "world.h"
#include "EffectManager.h"
#include "AIAttractBlock.h"
#include "ClientFlyComponent.h"
#include "ClientAquaticComponent.h"
#include "ActorVortex.h"
#include "VacantComponent.h"
#include "VacantVortexComponent.h"
#include "SunHurtComponent.h"
#include "ActorTravelingTrader.h"
#include "GameMode.h"
#include "SandboxTLManager.h"
#include "TLFrameEventCallFunc.h"
#include "SandboxTimeline.h"
#include "ClientInfoProxy.h"
#include "UgcAssetMgr.h"
#include "UgcAssetPrefab.h"
#include "CustomModelMgr.h"
#include "ModPackMgr.h"
#include "EquipGridContainer.h"
#include "GridContainer.h"
#include "SkillComponent.h"
#include "UGCSerialize.h"
#include "AIUpdateFrequency.h"

#define _UPDATE_BOUND_BY_SCALE_
using namespace Rainbow;
using namespace MNSandbox;

int ClientMob::m_DropItemCallCount = 1;

/*远离需要自动clear
3107	蝙蝠
3885	红薇蝶
3886	兰青蝶
3887	香粉蝶
3888	向阳蝶
3889	龙信蝶
3890	舌钟蝶
3419	萤火虫
3270	虚空萤火虫
3255	虚空蝴蝶
3254	虚空雪兔
3263	虚空水母
3264	虚空沙漠狼
3266	虚空鲨鱼
3267	虚空蝙蝠
3268	虚空豹子
3269	虚空小型海盗船
3271	虚空漩涡
3272	虚空中型海盗船
3273	虚空大型海盗船
*/
static const std::map<int, bool> needClearMobByDist = {
	{3107, true},
	{3885, true},
	{3886, true},
	{3887, true},
	{3888, true},
	{3889, true},
	{3890, true},
	{3419, true},
	{3270, true},
	{3255, true},
	{3254, true},
	{3263, true},
	{3264, true},
	{3266, true},
	{3267, true},
	{3268, true},
	{3269, true},
	{3271, true},
	{3272, true},
	{3273, true}
};


IMPLEMENT_SCENEOBJECTCLASS(ClientMob)

MNSandbox::ReflexClassParam<ClientMob, int> ClientMob::R_MonsterId(0, "Mob_ID", "base", &ClientMob::MonsterIdGet, &ClientMob::MonsterIdSet, ReflexConfig::REG_LOCAL);
//MNSandbox::ReflexClassParam<ClientMob, std::string> ClientMob::ReflexClassParamDisplayName("Display_Name", "base", &ClientMob::DisplayNameGet, &ClientMob::DisplayNameSet);
//ReflexClassMember<ClientMob, std::string> ClientMob::ReflexClassMemberDesc("Description", "base", &ClientMob::m_description);

void ClientMob::initMobBody(ActorBody* body, const MonsterDef* def, const bool initModel, bool playEffect /* = true */)
{
	OPTICK_EVENT();
	if (def == NULL || body == NULL) return;
	UgcModelParam* modelinfo = UgcAssetMgr::GetInstancePtr()->ParseModelStr(def->Model.c_str());

	if (def->ModelType == MONSTER_CUSTOM_MODEL)
	{
		body->initCustomActor(DEFAUT_MODEL, def->Model.c_str(), def->ModelScale);
	}
	else if (def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		body->initFullyCustomActor(MAP_MODEL_CLASS, NULL, def->Model.c_str(), false, def->ModelScale);
	}
	else if (def->ModelType == MONSTER_IMPORT_MODEL)
	{
		body->initOffcialModel(def->Model.c_str(), def->ModelScale);

	}
	else
	{
		int monsterid = def->ID;
		bool has_avatar = def->HasAvatar;
		//Hard code
		if (def->Model == "100026" || def->Model == "100028")
		{
			has_avatar = true;
		}
		//野生 屠夫
		if (def->ID == 3509 || def->ID == 3122 || def->ID == 3882 || def->ID == 3884)
		{
			body->showNecklace(1);
		}

		if ((def->ID == 3435 || def->ID == 10035 || def->ID == 10036 || def->ID == 10003) && body) //这些id有半透明贴图
		{
			body->setNeedAlpha(true);
		}

		if (def->ID >= 3873 && def->ID <= 3877)
		{
			//祭司不同法杖
			char saddleName[128];
			for (int i = 0; i < 5; ++i)
			{
				sprintf(saddleName, "ball0%d", i + 1);
				body->showSkin(saddleName, (def->ID - 3873) == i);
			}
		}

		// 蝴蝶
		if ((def->ID >= 3885 && def->ID <= 3890) || (def->ID == 3255))
		{
			body->setIsMonsterSkin(true);
		}

		//狐狸 虚空狐狸（1 2 3尾）
		if (def->ID == 3407 || def->ID == 3248 || def->ID == 3250 || def->ID == 3252)
		{
			body->showNecklace(2);
		}
		//灵狐 驯服的虚空狐狸（1 2 3尾）
		if (def->ID == 3408 || def->ID == 3249 || def->ID == 3251 || def->ID == 3253)
		{
			body->showNecklace(1);
		}

		const char* replacetex = NULL;
		const char* replaceModel = NULL;
		char path[256];
		char path2[256];

		//优先加载mod贴图
		if (!def->Texture.empty())
		{
			replacetex = def->Texture.c_str();
			if (replacetex[0] == '$' && def->gamemod)
			{
				const char* _dir = "";
				if (def->gamemod)
				{
					def->gamemod->Event2().EmitThreadSafe<const char*&>("Mod_getModDir", _dir);
				}
				sprintf(path, "%s/resource/textures/entity/%s.png_", _dir, replacetex + 1);
				if (!GetFileManager().IsFileExistWritePath(path))
				{
					sprintf(path, "%s/resource/textures/entity/%s.png", _dir, replacetex + 1);
				}
				replacetex = path;
			}
		}
		else if (def->TextureID > 0)
		{
			sprintf(path, "entity/%s/male%d.png", def->Model.c_str(), def->TextureID);
			replacetex = path;
		}


		const char* pModeName = def->Model.c_str();
		if (pModeName && pModeName[0] != 0)
		{
			//玩家自定义模型
			if (pModeName[0] == '$')
			{
				const char* _dir = "";
				if (def->gamemod)
				{
					def->gamemod->Event2().EmitThreadSafe<const char*&>("Mod_getModDir", _dir);
				}
				sprintf(path2, "%s/resource/model/models/entity/%s.omod", _dir, pModeName + 1);
				replaceModel = path2;
			}
			//玩家自定义选择现有模型
			else if (pModeName[0] == '@')
			{
				sprintf(path2, "entity/%s/body.omod", pModeName + 1);
				replaceModel = path2;
			}
			//玩家模型
			else if (pModeName[0] == 'p')
			{
				sprintf(path2, "%s", pModeName + 1);
				int model = atoi(path2);
				int playerindex = ComposePlayerIndex(model, 0, 0);


				body->initPlayer(playerindex);
				if (def->ModelScale != 1.0f)
				{
					body->setRealScale(def->ModelScale);
				}
				return;
			}
			//Avatar模型
			else if (pModeName[0] == 'a')
			{
				sprintf(path2, "entity/player/player12/boy1/boy1.prefab");
				replaceModel = path2;
			}
			//表格定义的模型
			else
			{
				sprintf(path2, "entity/%s/body.omod", pModeName);
				replaceModel = path2;
			}
		}

		//加一点随机缩放
		if (replaceModel)
		{
			//bool hasAvatar = (pModeName && pModeName[0] != 0 && pModeName[0] == 'a');
			body->initMonster(replaceModel, def->ModelScale, has_avatar,
				playEffect ? def->Effect.c_str() : nullptr, replacetex, initModel);
			if (pModeName && pModeName[0] != 0 && pModeName[0] == 'a')
			{
				//Avatar模型，用默认裸模加上默认部件
				body->setBodyType(3);
				//LOG_INFO("addAvatarPartModel loc1:%p ",body);
				body->addDefaultAvatar(!initModel);
				body->addAnimModel(2);
				//回调到LUA部分根据保存的部件参数设置部件
				//MINIW::ScriptVM::game()->callFunction("AvatarTryOnAllClothes", "su[ActorBody]",pModeName,body);
			}

			if (def->ModelType / 10000 == MONSTER_SKIN_MODEL)
				body->setIsMonsterSkin(true);
		}
		else// if (body)
		{
			body->initMonster("entity/100026/body.omod", def->ModelScale, has_avatar,
				playEffect ? def->Effect.c_str() : nullptr, replacetex, initModel);
		}
	}
}

int ClientMob::getDefID()
{
	if (m_Def)
		return m_Def->ID;
	return 0;
}

float ClientMob::getAttackDist()
{
	return m_Def->AttackDistance * BLOCK_FSIZE;
}

std::string ClientMob::getFullyCustomModelKey()
{
	if (m_Def && m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
		return m_Def->Model.c_str();

	return "";
}

void ClientMob::updateBodyByFullyCustomModel()
{
	if (getBody() && m_Def && m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		getBody()->initFullyCustomActor(MAP_MODEL_CLASS, NULL, m_Def->Model.c_str(), false, m_Def->ModelScale);
		setScale(0);
	}
}

void ClientMob::updateBodyByImportModel()
{
	if (!getBody() || !m_Def)
		return;

	if (m_Def->ModelType != MONSTER_IMPORT_MODEL)
		return;

	if (!MpGameSurviveCdnResMgr::GetInstancePtr())
		return;

	if (!MpGameSurviveCdnResMgr::GetInstancePtr()->hasRes(2, m_Def->Model.c_str()))
		return;

	getBody()->initOffcialModel(m_Def->Model.c_str(), m_Def->ModelScale);
	setScale(0);
}

ClientMob::ClientMob() : m_Def(NULL),/* m_GrowingAge(0),*/ m_CollarColor(-1), m_CanPickUpLoot(false), m_TimeSinceIgnited(0),
m_SaySoundTimer(0), m_DespawnTicks(0), m_DieTicks(0), m_Color(-1), m_AITask(NULL),
m_AITaskTarget(NULL), m_nDanceState(0), m_MilkingTimes(0), m_EventListen(nullptr),
m_BonFirePos(WCoord(0, -1, 0)), m_iTickAnimAwake(0), m_SpawnPoint(0, -1, 0)
, m_btree(NULL), m_bb(NULL), m_isStandSleeping(false), m_directSwitchAni(false), IsStopBodyRotation(false), m_LastHeadIconItemID(0), m_Data(0)
, m_Climbing(false)/*, m_listenBoundBoxChanged(this, &ClientMob::OnUpdateBoundBoxChanged)*/, m_defaultSyncLoadMonsterModel(false), m_isSkinned(false),
m_isSkinning(false), m_skinningTime(0.0f), m_corpseRemainTime(0.0f), m_skinningElapsedTime(0), m_skinningPlayerID(0), m_MobPresetPosKey(0)
{
	m_NeedOxygen = true;
	m_TraceDist = 3200;
	m_HomeDist = -1;
	m_AISitting = false;
	//m_TamedOwnerUin = 0;
	//m_InLove = 0;
	//m_InQuite = 0;
	//m_Angry = false;
	m_Panic = false;
	//m_PlayClearFX = true;
	m_NeedUpdateAI = true;
	m_RandomScale = 1.0f;
	m_bcanRideByPlayer = true;
	m_bAiFire = false;
	m_bMale = true;
	m_combineID = 0;
	m_Mass = 1000;
	if (this && this->m_Def)
	{
		m_TurningSpeed = this->m_Def->TurningSpeed;
	}
	else
	{
		m_TurningSpeed = 30.0f;
	}
	//m_bInitInteractFunc = false;
	m_aiTempt = NULL;
	m_MotionType = IDLE;
	m_ActorAttribTrigger = NULL;
	m_iTickTriggerCount = 0;
	//m_bDShopDisplay = false;
	m_displayName = "";
	m_modName = "";
	m_iEvadeCDTick = 0;
	m_ServerWid = "";
	m_isUseAILua = false;
	m_bNeedRunAway = false;
	//m_bEaten = false;
	//m_iReadyToQuit = 0;
	//m_iRealQuitTick = 0;
	//m_iCurNeedFeedNum = 0;
	m_iStayBlockTime = 0;
	m_RunAICDList.clear();
	//m_bWaitToAdult = false;
	//m_pGrowComponent = NULL;
	//m_pBreedComponent = NULL;
	//m_pQuiteComponent = NULL;
	//m_pAngryComponent = NULL;
	//m_pTameComponent = NULL;

	m_addBuffCount = 0;
	m_isAttackStatus = false;
	m_isHideStatus = false;
	m_isHurtDropItem = false;
	m_isBrokenTail = false;
	m_scorpionHideTick = -1;
	m_scorpionSyncTick = 0;
	m_suddenIllnessBuffTraceTick = 0;
	m_isSuddenIllnessFindMob = false;

	m_SaySound = "";
	m_Evicted = false;
	m_needSaveInChunk = false;
	m_ObjType = OBJ_TYPE_MONSTER;
	createEvent();

	CreateComponent<DialogueComponent>("DialogueComponent");
	CreateComponent<PlotComponent>("PlotComponent");

	CreateComponent<AngryComponent>("AngryComponent");
	cacheBreedComponent(CreateComponent<BreedComponent>("BreedComponent"));
	cacheGrowComponent(CreateComponent<GrowComponent>("GrowComponent"));
	cacheQuiteComponent(CreateComponent<QuiteComponent>("QuiteComponent"));
	//CreateComponent<MobTeamComponent>("TeamComponent");
	CreateComponent<MobHPProgressComponent>("HPProgressComponent");
	m_pAttackingTargetComponent = CreateComponent<MobAttackingTargetComponent>("AttackingTargetComponent");

	//m_pTameComponent = CreateComponent<TameComponent>("TameComponent");

	m_pEquipGridContainer = nullptr;
	m_pGridContainer = nullptr;

	m_isFirstCreate = true;
	m_bInitChildrenNode = false;
	m_mobtick = 0;
	m_noAttackedTicks = 0;
	m_recoverToughnessTicks = -1;
	m_needClearBtree = nullptr;
	m_needClearBb = nullptr;
}

ClientMob::~ClientMob()
{
	char szScriptFun[256];
	if (getDef())
	{
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Release", getDef()->ID);
		MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
	}
	WorldManager* worldmanger = GetWorldManagerPtr();
	if (worldmanger ) {//autorelease pool的存在  可能GetWorldManagerPtr()先释放
		BTManager* btmanager = static_cast<BTManager*>(worldmanger->GetBTManager());
		if (btmanager)
		{
			if (m_needClearBtree)
			{
				if (m_needClearBb)// 行为树AI
					btmanager->DestoryBlackboard(m_needClearBb);

				btmanager->DestroyBTree(m_needClearBtree);

				m_needClearBtree = nullptr;
				m_needClearBb = nullptr;
			}
			if (m_bb)// 行为树AI
				btmanager->DestoryBlackboard(m_bb);
			if (m_btree)
				btmanager->DestroyBTree(m_btree);
		}
		
	}//如果GetWorldManagerPtr()已经释放  其析构的时候 就把所有的bb，btree全部释放了
	m_bb = NULL;
	m_btree = NULL;

	if (m_isUseAILua)
	{
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaDelete", "i", m_AILuaKey);
	}

	ENG_DELETE(m_AITask);
	ENG_DELETE(m_AITaskTarget);
	//getBody() = NULL;
	ENG_DELETE(m_ActorAttribTrigger);
	//SANDBOX_DELETE(m_pGrowComponent);
	//SANDBOX_DELETE(m_pBreedComponent);
	//SANDBOX_DELETE(m_pQuiteComponent);
	//SANDBOX_DELETE(m_pAngryComponent);
	//SANDBOX_DELETE(m_pTameComponent);

	tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "ClientMob");

	deleteEvent();

	m_Def = NULL;

	if (m_tlMgr)
	{
		m_tlMgr->Release();
		ENG_DELETE(m_tlMgr);
		m_tlMgr = nullptr;
	}
	m_timeline = nullptr;
	if (m_stData)
	{
		ENG_DELETE(m_stData);
	}
}

TameComponent* ClientMob::getTameComponent()
{
	return m_pTameComponent;
}

TameComponent* ClientMob::sureTameComponent()
{
	if (!m_pTameComponent)
	{
		m_pTameComponent = CreateComponent<TameComponent>("TameComponent");
	}

	return m_pTameComponent;
}

void ClientMob::deleteEvent()
{
	//void setRiddenByActor(ClientActor *p, int i)

	//Event2().Unsubscribe("setRiddenByActor", m_listenerSetRiddenByActor);
	SANDBOX_DELETE(m_listenerSetRiddenByActor);
}

void ClientMob::createEvent()
{
	//void setRiddenByActor(ClientActor *p, int i)
	typedef ListenerFunction<ClientActor*, int> ListenerSetRiddenByActor;
	m_listenerSetRiddenByActor = SANDBOX_NEW(ListenerSetRiddenByActor, [&](ClientActor* ridden, int i) -> void {
		this->setRiddenByActor(ridden, i);
		});
	Event2().Subscribe("setRiddenByActor", m_listenerSetRiddenByActor->GetListener());
}

void ClientMob::updateModelName()
{
	auto functionWrapper = getFuncWrapper();
	if (m_Def == NULL)
	{
		if (functionWrapper)
		{
			functionWrapper->setActorFacade("");
		}
		return;
	}
	std::stringstream str;
	if (m_Def->ModelType == MONSTER_CUSTOM_MODEL)
	{

		str << "custom_" << m_Def->Model.c_str();
	}
	else if (m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	{
		str << "fullycustom_" << m_Def->Model.c_str();
	}
	else if (m_Def->ModelType == MONSTER_IMPORT_MODEL)
	{
		str << "importmodel_" << m_Def->Model.c_str();
	}
	else
	{
		str << "mob_" << m_Def->ID;

	}
	if (functionWrapper)
	{
		functionWrapper->setActorFacade(str.str());
	}
	return;
}


void ClientMob::updateActorBodySkin()
{
	auto def = getMonsterDef();
	if (!def)
		return;

	int mosterId = def->ID;
	if (isVacantFox(mosterId) && getBody())
	{
		char path[256];
		if (def->TextureID > 0)
			sprintf(path, "entity/%s/male%d.png", def->Model.c_str(), def->TextureID);
		else
			sprintf(path, "entity/%s/male.png", def->Model.c_str());

		auto model = getBody()->getModel();
		if (model)
		{
			getBody()->changeBodyTex(path, "rtexbody");

			int tailIdx = 0;
			if (mosterId >= 3250 && mosterId <= 3251)
				tailIdx = 1;
			else if (mosterId >= 3252 && mosterId <= 3253)
				tailIdx = 2;

			char* s_vacantFoxTailNames[3] = { "tail1", "tail2", "tail3" };
			for (int i = 0; i < 3; i++)
			{
				model->ShowSkin(s_vacantFoxTailNames[i], i == tailIdx);
			}
		}
	}
}

ActorBody* ClientMob::newActorBody()
{
	OPTICK_EVENT();
	ActorBody* actorBody = ENG_NEW(ActorBody)(this);
    //actorBody->m_notifyPositionChanged.Subscribe(m_listenBoundBoxChanged);
    actorBody->m_notifyPositionChanged = std::bind(&ClientMob::OnUpdateBoundBoxChanged, this);

	bool initModel = isSyncLoadModelWhileNewActorBody();
	initMobBody(actorBody, m_Def, initModel);

	if (m_Def == NULL)
	{
		auto functionWrapper = getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setActorFacade("");
		}
		return actorBody;
	}
	m_RandomScale = m_Def->MinRandomScale + (1 - m_Def->MinRandomScale) * GenRandomFloat();
	actorBody->setScale(m_Def->ModelScale * m_RandomScale);

	//setScale(m_Def->ModelScale * m_RandomScale);
	updateModelName();
	return actorBody;
}

inline bool IsMobHorse(int id)
{
	return GetDefManagerProxy()->getHorseDef(id) != NULL;
}

inline bool IsWaterMob(int id)
{
	const MonsterDef* monsterDef = GetDefManagerProxy()->getMonsterDef(id);
	if (monsterDef) {
		return monsterDef->Type == 3;
	}
	return false;
}

inline bool IsFlyMob(int id)
{
	const MonsterDef* monsterDef = GetDefManagerProxy()->getMonsterDef(id);
	if (monsterDef) {
		return monsterDef->Type == 8 || id == 3897 || id == 3107 || id == 3267;
	}
	return false;
}

inline bool IsBallMob(int id)
{
	const MonsterDef* monsterDef = GetDefManagerProxy()->getMonsterDef(id);
	if (monsterDef) {
		return monsterDef->Type == 9;
	}
	return false;
}

inline bool IsPlotNpc(int id)
{
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
	if (def)	return def->Type == 7;

	return false;
}

inline bool IsVillager(int id)
{
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
	if (def)
		return def->Type == 10;

	return false;
}

inline bool IsTrixenie(int id)
{
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(id);
	if (def)
		return def->Type == 11;

	return false;
}

ClientMob* ClientMob::createFromDef(int monsterid, int mobtype/*= 0*/, bool trigger, bool init, bool bodyAsyncLoad, bool needInit)
{
	ClientMob* mob = NULL;

	SandboxResult createResult = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("ClientMob_createMobFromDef",
			SandboxContext(nullptr).
			SetData_Number("monsterid", monsterid).
			SetData_Number("mobtype", mobtype).
			SetData_Bool("trigger", trigger).
			SetData_Usertype("mobptrptr", &mob));
	if (createResult.IsExecSuccessed() && nullptr != mob)
	{
		//if (trigger)
		//	mob->ActorCreateOnTrigger(monsterid, mob->getObjType());

		return  (ClientMob*)mob;
	}

	if (monsterid == 3501) mob = SANDBOX_NEW(ActorEarthCoreMan);
	else if (monsterid == 3203) mob = SANDBOX_NEW(ClientSouvenir);
	else if (monsterid >= 3010 && monsterid < 3020) mob = SANDBOX_NEW(ActorTrader);
	else if (monsterid == 3022) mob = SANDBOX_NEW(ActorTravelingTrader);
	else if (monsterid == 3210 || monsterid == 3211) mob = SANDBOX_NEW(ActorDesertBusInessMan);
	else if (monsterid == 3220 || monsterid == 3221) mob = SANDBOX_NEW(ActorPatrolMob);
	else if (monsterid == 3230) mob = SANDBOX_NEW(ActorDriftBottle);
	else if (monsterid == 3222 || monsterid == 3223) mob = SANDBOX_NEW(ActorIslandBusInessMan);
	else if (monsterid == 3213) mob = SANDBOX_NEW(ActorDesertBusInessManGuard);
	else if (monsterid == 3621) mob = SANDBOX_NEW(ActorCrab);
	else if (monsterid == 3242 || monsterid == 3243) mob = SANDBOX_NEW(ActorVortex);
	else if (monsterid == 3623 || monsterid == 3624) mob = SANDBOX_NEW(ActorHippocampus);
	else if (monsterid == 3625) mob = SANDBOX_NEW(ActorHippocampusHorse);
	else if (monsterid == 3212) mob = SANDBOX_NEW(ActorDesertBusInessMan, true);
	else if (monsterid == 3826 || monsterid == 3827) mob = SANDBOX_NEW(ActorSandMan);
	else if (monsterid >= 3511 && monsterid <= 3513) mob = SANDBOX_NEW(ActorGhost);
	else if (monsterid == 3521) mob = SANDBOX_NEW(ActorPearl);
	//else if (monsterid >= 3600 && monsterid <= 3619) mob = SANDBOX_NEW(ClientAquaticMob);
	else if (monsterid == 3825) mob = SANDBOX_NEW(ActorSandworm);
	else if (monsterid == 3517) mob = SANDBOX_NEW(ActorFrostWyrm);
	else if (monsterid >= 3204 && monsterid <= 3209) mob = SANDBOX_NEW(ActorDesertVillager);
	else if (monsterid >= 3873 && monsterid <= 3877) mob = SANDBOX_NEW(ActorSavagePriest);
	else if (monsterid >= 3214 && monsterid <= 3219) mob = SANDBOX_NEW(ActorFishingVillager);
	else if (monsterid == 3229) mob = SANDBOX_NEW(ActorFisherMan);
	else if (monsterid == SEA_SPIRIT_GUARDING) mob = SANDBOX_NEW(ActorSeaSpiritGuarding);
	else if (monsterid == 3916) mob = SANDBOX_NEW(ActorSnowMan);
	else if (monsterid == 3910 || monsterid == 3911 || monsterid == 3254) mob = SANDBOX_NEW(ActorSnowHare);
	else if (monsterid >= DRAGON_MOUNT_ID_LEVEL_0 && monsterid <= DRAGON_MOUNT_ID_LEVEL_2)
	{
		mob = SANDBOX_NEW(ActorDragonMount);
	}
	else if (monsterid >= STAR_MOUNT_ID_LEVEL_0 && monsterid <= STAR_MOUNT_ID_LEVEL_2)
	{
		mob = SANDBOX_NEW(ActorMoonMount);
	}
	else if ((monsterid >= DOUDU_MOUNT_ID_LEVEL_0 && monsterid <= MUMU_MOUNT_ID_LEVEL_1) || (monsterid >= NINECOLORDEER_MOUNT_ID_LEVEL_0 && monsterid <= NINECOLORDEER_MOUNT_ID_LEVEL_1))
	{
		mob = SANDBOX_NEW(ActorDouDuMount);
	}
	else if (::ActorPumpkinHorse::isPumpkinHorse(monsterid)) { // 如果是南瓜车
		mob = SANDBOX_NEW(ActorPumpkinHorse);
	}
	else if (::ActorPackHorse::isPackHorse(monsterid)) { // 如果是驯服骆驼
		mob = SANDBOX_NEW(ActorPackHorse);
	}
	else if (::ActorStorageBoxHorse::isStorageBoxHorse(monsterid)) { //储物箱坐骑
		mob = SANDBOX_NEW(ActorStorageBoxHorse);
	}
	else if ((monsterid >= 3225 && monsterid <= 3228) || (monsterid == 3269 || monsterid == 3272 || monsterid == 3273)) { // 如果是海盗船
		//(monsterid == 3269 || monsterid == 3272 || monsterid == 3273) //虚空海盗船 小中大
		mob = SANDBOX_NEW(ActorPirateShip);
	}
	else if (monsterid == 3912) { //牦牛
		mob = SANDBOX_NEW(ActorYak);
	}
	else if (IsWaterMob(monsterid))
	{
		//mob = SANDBOX_NEW(ClientAquaticMob);
		mob = SANDBOX_NEW(ClientMob);
		mob->cacheClientAquaticComponent(mob->CreateComponent<ClientAquaticComponent>("ClientAquaticComponent"));
	}
	else if (IsMobHorse(monsterid))
	{
		mob = SANDBOX_NEW(ActorHorse);
	}
	else if (IsPlotNpc(monsterid)) mob = SANDBOX_NEW(ActorNpc);
	else if (IsFlyMob(monsterid)) {
		//mob = SANDBOX_NEW(ClientFlyMob);
		mob = SANDBOX_NEW(ClientMob);
		mob->cacheClientFlyComponent(mob->CreateComponent<ClientFlyComponent>("ClientFlyComponent"));
	}
	else if (IsVillager(monsterid)) mob = SANDBOX_NEW(ActorVillager);
	else if (IsTrixenie(monsterid)) mob = SANDBOX_NEW(ClientTrixenieMob);
	else
	{
		char scriptName[256] = { 0 };
		MINIW::ScriptVM::game()->callFunction("CreateMobsManagerGetConfig", "i>s", monsterid, &scriptName);
		mob = ActorBase::Produce(scriptName).ToCast<ClientMob>();
		if (mob == NULL)
		{
			//LOG_WARNING("mob NULL: monsterid = %d", monsterid);
			//mob = SANDBOX_NEW(ClientMob);
			mob = ClientMob::NewInstance();
		}
	}

	if (mob == NULL) return NULL;

	if (needInit)
	{
		mob->setSyncLoadModelWhileNewActorBody(!bodyAsyncLoad);
		if (init && !mob->init(monsterid))
		{
			LOG_SEVERE("mob init failed: monsterid = %d", monsterid);
			//SANDBOX_DELETE(mob);
			mob->Release();
			return NULL;
		}
	}
	//20210812: 新增是否触发触发器参数 codeby:wangshuai
	if (trigger)
	{
		auto triggerComponent = mob->getTriggerComponent();
		if (triggerComponent)
		{
			triggerComponent->ActorCreateOnTrigger(monsterid, mob->getObjType());
		}
	}
	return mob;
}

void ClientMob::setRangeSameTypeMobRunAway(ClientMob* mob)
{
	//不会攻击的生物才会逃跑   ActiveAtk策划让bool改成int
	if (!mob && m_Def->ActiveAtk == 0)
	{
		return;
	}
	OPTICK_EVENT();
	ActorLocoMotion* motion = getLocoMotion();

	std::vector<ClientMob*> nearMob;
	if (!m_pWorld) return;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	actorMgr->selectNearAllMobs(nearMob, motion->getPosition(), 6 * BLOCK_SIZE);
	for (unsigned int i = 0; i < nearMob.size(); i++)
	{
		ClientMob* cm = nearMob[i];
		if (cm && cm->getDef()->BreedType == mob->getDef()->BreedType && m_Def->ActiveAtk != 0 && cm != mob)
		{
			cm->setInLove(0);
			cm->setQuiteTick(0);
			cm->setReadyToQuit(0);
			cm->setRealQuit(0);
			cm->setNeedRunAway(true);
		}
	}
}
ClientMob* ClientMob::createFromDef(jsonxx::Object mobJson)
{
	if (mobJson.has<jsonxx::Number>("monsterid"))
	{
		int monsterid = mobJson.get<jsonxx::Number>("monsterid");
		return ClientMob::createFromDef(monsterid, 0, false);
	}

	//TODO
	//其他属性

	return nullptr;
}
void ClientMob::handleTouch(ClientPlayer* player)
{
	ClientPlayer* owner = getTamedOwner();
	if (owner && player && owner->getObjId() == player->getObjId() && m_AITask)
	{
		if (owner->isInTamedFollows(this))
		{
			//移除跟随列表并停止所有AI

			owner->removeMobFromTamedFollows(this);
			if (m_isUseAILua)
			{
				GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaDelete", "i", m_AILuaKey);
			}
			else
			{
				m_AITask->clearAllTasks();
			}
		}
		else
		{
			//重新加入跟随列表并添加所有AI
			owner->addMobToTamedFollows(this);

			if (m_isUseAILua)
			{
				char szScriptFun[256];
				snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAILua", getDef()->ID);
				GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCreate", "u[ClientMob]s>i", this, szScriptFun, &m_AILuaKey);
			}
			else
			{
				char szScriptFun[256];
				snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", getDef()->ID);
				if (m_Def->Fodder[0] == 1) //草食生物AI
				{
					addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
					addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
				}
				else if (m_Def->Fodder[0] == 2) //肉食生物AI
				{
					addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
					addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
				}
				else if (m_Def->Fodder[0] == 3) //杂食生物AI
				{
					addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
					addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
					addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
				}
				MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
			}

		}
	}
}

bool ClientMob::needUpdateAI()
{
	if (needClear()) return false;
	return m_NeedUpdateAI;
}

void ClientMob::setImmuneToFire(int i)
{
	getAttrib()->setImmuneToFire(i);
}

void ClientMob::setImmuneAttackType(int i, bool isAdd)
{
	getAttrib()->setImmuneAttackType(i, isAdd);
}

void ClientMob::getFeedInfo(int& feedId, int& feedCount)
{
	feedId = getDef()->Fodder[0];
	feedCount = getDef()->Fodder[1];

	if (feedId == 1) feedId = BLOCK_PLANT_FEED_TROUGH; //草食生物对应的草饲料
	if (feedId == 2) feedId = BLOCK_MEAT_FEED_TROUGH;
	if (feedId == 3) feedId = -1; //杂食，即可以被草食也可以被肉食饲料槽吸引

}

ClientMob* ClientMob::getNearbyItem(const std::vector<int>& ids, int range)
{
	OPTICK_EVENT();
	//const int RANGE = 3*BLOCK_SIZE;
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range, range);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_THROWABLE, ids);


	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getLocoMotion()->getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		WCoord vec = mob->getLocoMotion()->getPosition() - pos;
		float dist = vec.length();
		if (dist < fDist)
		{
			fDist = dist;
			pTarget = mob;
		}
	}

	return pTarget;
}

ClientMob* ClientMob::getNearbyMobForCombine(const std::vector<int>& ids, int range)
{
	OPTICK_EVENT();
	//const int RANGE = 8*BLOCK_SIZE;
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range, range);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER, ids);


	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getLocoMotion()->getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		if (mob->getCombineID() == 0 && getCombineID() == 0 && !mob->getPanic() && !getPanic())
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;
			float dist = vec.length();
			if (dist < fDist)
			{
				fDist = dist;
				pTarget = mob;
			}
		}
	}

	return pTarget;
}

ClientMob* ClientMob::getNearbyMob()
{
	OPTICK_EVENT();
	const int RANGE = 8 * BLOCK_SIZE;
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(RANGE, RANGE, RANGE);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER, getDef()->ID);

	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getLocoMotion()->getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		//if(mob->isInLove()&&isInLove() || mob->isInHate()&&isInHate())
		//{
		WCoord vec = mob->getLocoMotion()->getPosition() - pos;
		float dist = vec.length();
		if (dist < fDist)
		{
			fDist = dist;
			pTarget = mob;
		}
		//}
	}

	return pTarget;
}

ClientMob* ClientMob::getNearbyMate()
{
	OPTICK_EVENT();
	const int RANGE = 10 * BLOCK_SIZE;
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(RANGE, RANGE, RANGE);

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, getObjType(), getDef()->ID);

	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getLocoMotion()->getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		if (mob->isInLove() && isInLove() || mob->isInHate() && isInHate())
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;
			float dist = vec.length();
			if (dist < fDist)
			{
				fDist = dist;
				pTarget = mob;
			}
		}
	}

	return pTarget;
}

std::vector<ClientActor*> ClientMob::selectAllMobs(int iMobDefID, int grouptype, int range, int monsterType)
{
	OPTICK_EVENT();
	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range / 2, range);

	std::vector<ClientActor*>actorsfind;

	std::vector<IClientActor *>actors;
	m_pWorld->getActorsOfTypeInBox(actors, box, monsterType);

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		if ((mob->getDef()->ID == iMobDefID) || (grouptype && mob->getDef()->BabyID == iMobDefID))
		{
			actorsfind.push_back(mob);
		}
	}

	return actorsfind;
}

std::vector<ClientActor*> ClientMob::selectAllMobs(int range) {
	OPTICK_EVENT();
	std::vector<ClientActor*> actors;
	CollideAABB box;
	if (!getLocoMotion() || !m_pWorld)
	{
		return actors;
	}
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range / 2, range);
	std::vector<IClientActor*> iactors;
	if(m_pWorld) m_pWorld->getActorsInBoxExclude(iactors, box, this);
	size_t i = 0;
	while (i < iactors.size())
	{
		ClientActor* actor = static_cast<ClientActor*>(iactors[i]);
		if (actor->getObjType() == OBJ_TYPE_AQUATICMONSTER || actor->getObjType() == OBJ_TYPE_BOAT)
		{
			actors.push_back(actor);
		}
		i++;
		/*if (actor->getObjType() != OBJ_TYPE_AQUATICMONSTER && actor->getObjType() != OBJ_TYPE_BOAT)
		{
			actors[i] = actors.back();
			actors.pop_back();
		}
		else i++;*/
	}
	return std::move(actors);
}

static bool DefaultFilterActorFunc(ClientActor* actor, void* userdata)
{
	return !actor->isDead();
}

ClientMob* ClientMob::selectNearMob(int iMobDefID, int grouptype, int range, void* pfunc, void* userdata)
{
	OPTICK_EVENT();
	if (!m_pWorld)
		return NULL;
	FilterActorFunc filterFunc = (FilterActorFunc)pfunc;
	if (filterFunc == NULL) {
		filterFunc = DefaultFilterActorFunc;
	}

	CollideAABB box;
	getLocoMotion()->getCollideBox(box);
	box.expand(range, range / 2, range);

	std::vector<IClientActor *>actors;
	if (iMobDefID > 0) { //当iMobDefID>0时，只针对目标ID，等于0时，针对所有生物 codeby：renjie 2022/09/14
		int ActorType = OBJ_TYPE_MONSTER;
		const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(iMobDefID);
		if (!def)
			return NULL;
		if (def->Type == 3) {
			ActorType = OBJ_TYPE_AQUATICMONSTER;//加入水生生物的判断 
		}
		else if (def->Type == 10) {
			ActorType = OBJ_TYPE_VILLAGER;
		}

		m_pWorld->getActorsOfTypeInBox(actors, box, ActorType, def->ID);
	}
	else {
		m_pWorld->getActorsInBox(actors, box);

		size_t i = 0;
		size_t last = actors.size();
		while (i < last)
		{
			ClientActor* actor = actors[i]->GetActor();
			bool needremove = false;


			ClientMob* mob = dynamic_cast<ClientMob*>(actor);
			if (mob == NULL || !mob->getDef() || !mob->getDef()->ID) needremove = true;


			if (needremove)
			{
				//20211014 用队尾元素覆盖当前元素 codeby:liushuxin
				actors[i] = actors[last - 1];
				last--;
			}
			else i++;
		}
		if (last != actors.size())
		{
			actors.erase(actors.begin() + last, actors.end());
		}

	}

	float fDist = 99999999.0f;
	ClientMob* pTarget = NULL;
	WCoord pos = getPosition();

	for (size_t i = 0; i < actors.size(); i++)
	{
		ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		if (mob == this)
		{
			continue;
		}

		//iMobDefID等于0为任意生物
		if ((mob->getDef()->ID == iMobDefID) || (grouptype && mob->getDef()->BabyID == iMobDefID) || (iMobDefID == 0))
		{
			WCoord vec = mob->getLocoMotion()->getPosition() - pos;
			float dist = vec.length();
			if (dist < range && dist < fDist && filterFunc(mob, userdata))
			{
				fDist = dist;
				pTarget = mob;
			}
		}
	}

	return pTarget;
}

ClientMob* ClientMob::getOccupyMob(const WCoord& blockpos)
{
	OPTICK_EVENT();
	CollideAABB box;
	box.setPosDim(blockpos * BLOCK_SIZE, WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));

	std::vector<ClientActor*>actors;
	ClientMob* foundMob = nullptr;
	auto mobID = getDef()->ID;
	m_pWorld->getActorMgr()->GetActorMGT()->QueryFirstNodes(box.ToAABB(), [&mobID, &foundMob](IClientActor* actor) {
		if (actor->getObjType() != OBJ_TYPE_MONSTER)return false;
		ClientMob* mob = dynamic_cast<ClientMob*>(actor);
		if (mob) {
			if (mob->getDefID() == mobID) {
				foundMob = mob;
				return true;
			}
		}
		return false;
		});
	return foundMob;
	// 	size_t n = m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER, getDef()->ID);
	// 
	// 	for (size_t i = 0; i < n; i++)
	// 	{
	// 		if (actors[i] != this) return dynamic_cast<ClientMob *>(actors[i]);
	// 	}
	// 
	// 	return NULL;
}

bool ClientMob::findNearRandomBlock(int blockid, WCoord& blockpos, int radius_xz, int radius_y, int trynum)
{
	OPTICK_EVENT();
	WCoord center = CoordDivBlock(getPosition());
	for (int itry = 0; itry < trynum; itry++)
	{
		WCoord pos(center.x + GenRandomInt(-radius_xz, radius_xz), center.y, center.z + GenRandomInt(-radius_xz, radius_xz));

		int n = radius_y * 2 + 1;
		for (int i = 0; i < n; i++)
		{
			int dy = (i + 1) / 2;
			if (i & 1) dy = -dy;

			WCoord curpos = pos + WCoord(0, dy, 0);
			if (m_pWorld->getBlockID(curpos) == blockid)
			{
				blockpos = curpos;
				return true;
			}
		}
	}

	return false;
}

void ClientMob::setSleeping(bool b)
{
	setFlagBit(ACTORFLAG_SLEEP, b);
	//生物睡觉状态同步给客机
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object object;
		object << "actorId" << getObjId();
		object << "state" << b;
		int size = 0;
		unsigned char* p = NULL;
		object.saveBinary(p, size);
		GetSandBoxManager().sendBroadCast((char*)("SYN_SLEEP_STATE"), p, size);
		free(p);
	}
}

void ClientMob::enterWorld(World* pworld)
{
	if (pworld == NULL) return;
	ActorLiving::enterWorld(pworld);
	if (m_Def == NULL) return;

	//Fire<int>(MOB_SCRIPTNOTIFY_ENTERWORLD);
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return ;
	actorMgr->addMobSpawnNum(m_Def->Type, 1);
	actorMgr->addMobSpawnNumByID(m_Def->ID, 1);

	WCoord tmppos = getLocoMotion()->getPosition();
	LOG_INFO("ClientMob::enterWorld: defid = %d, pos=(%f, %f, %f)", m_Def->ID, tmppos.x, tmppos.y, tmppos.z);

	// 行为树active
	if (m_btree)
	{
		m_btree->SetActive(true);
	}

	if (isSleeping()) {
		WCoord pos = getLocoMotion()->getPosition();
		int blockid = pworld->getBlockID(CoordDivBlock(pos));
		if (IsSleepingbagBlock(blockid)) {
			wakeUp();//在睡袋重新进入世界,先醒一下
		}
	}

	//怪物初始生成
	if (m_Def)
	{
		MNSandbox::GetGlobalEvent().Emit<int, ClientActor*>("StatisticTerrgen_PlayerGenerateMonster", m_Def->ID, this);
	}

	updateActorBodySkin();
}

void ClientMob::leaveWorld(bool keep_inchunk)
{
	//Fire<int>(MOB_SCRIPTNOTIFY_LEAVEWORLD);
	// 行为树deactive
	if (m_btree)
	{
		m_btree->SetActive(false);
	}

	//closeDialogue();
	EXEC_USEMODULE(closeDialogue);
	if(m_pWorld == NULL || m_Def == NULL) return; 
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return;
	actorMgr->addMobSpawnNum(m_Def->Type, -1);
	actorMgr->addMobSpawnNumByID(m_Def->ID, -1);

	if (isDead())
		actorMgr->onMobDead(this);

	ActorLiving::leaveWorld(keep_inchunk);
	if (!keep_inchunk)
	{
		ObserverEvent obevent;
		obevent.SetData_EventObj(m_ObjId);
		ObserverEventManager::getSingleton().OnTriggerEvent("dev.remove", &obevent);
	}

	ReleaseMobEventListen();
}

void ClientMob::ReleaseMobEventListen()
{
	SANDBOX_DELETE(m_EventListen);
}

//void ClientMob::closeDialogue()
//{
//	if (m_OpenDialogueUINs.empty()) return;
//
//	auto tuins = m_OpenDialogueUINs;
//	if (m_pWorld && !m_pWorld->isRemoteMode() && m_pWorld->getActorMgr())
//	{
//		auto am = m_pWorld->getActorMgr();
//		for (auto it = tuins.begin(); it != tuins.end(); ++it)
//		{
//			ClientPlayer *player = am->findPlayerByUin(*it);
//			if (player == NULL) continue;
//
//			player->closeContainer();
//			if (player->hasUIControl())
//				GetGameEventQue().postClosePlotDialogue();
//			else
//			{
//				PB_CloseDialogueHC closeDialogueHC;
//
//				GetGameNetManagerPtr()->sendToClient(*it, PB_CLOSEDIALOGUE_HC, closeDialogueHC);
//			}
//		}
//	}
//
//	stopBodyEffect("NPC_talk");
//	m_OpenDialogueUINs.clear();
//}
//
//bool ClientMob::isInteracting()
//{
//	return !m_OpenDialogueUINs.empty();
//}

bool ClientMob::mobInit(int monsterid)
{
	setMonsterId(monsterid);
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterid, true, true);
	//Dynamic mod load. Wait to be fixed. rivershen 20170227
	if (def == NULL)
	{
		LOG_SEVERE("monsterid = %d |def = %p", monsterid, def);
		return false;
	}
	if (!MINIW::ScriptVM::game()->isCallFunctionEnabled()) {
		LOG_SEVERE("scriptvm canot call ,monsterid = %d |def = %p", monsterid, def);
		return false;
	}

	//MonsterDef *def = GetDefManagerProxy()->getMonsters().GetRecord(monsterid);
	m_Def = def;
	if (m_Def)
	{
		m_SaySound = m_Def->SaySound;
	}

	m_corpseRemainTime = m_Def->CorpseTime;

	//m_displayName = m_Def->Name;
	//设置描述
	std::string desc_string = m_Def->Desc.c_str();
	SetDescription(desc_string);
	//m_iCurNeedFeedNum = m_Def->Fodder[1];
	setCurNeedFeedNum(m_Def->Fodder[1]);
	this->setTeam(m_Def->TeamID);
	// 交互时是否显示开发者商店入口
	//this->m_bDShopDisplay = m_Def->DevShopDisPlay;
	EXEC_USEMODULE(setShopDisplay, m_Def->DevShopDisPlay);
	// 开发者商店配置
	EXEC_USEMODULE(setOpenStoreConfig, m_Def->OpenStoreConfig);

	newLocoMotion();
	getLocoMotion()->setBound((int)(def->Height * def->ModelScale), (int)(def->Width * def->ModelScale));

	getLocoMotion()->setAttackBound(
		(int)(def->HitHeight * def->ModelScale),
		(int)(def->HitWidth * def->ModelScale),
		(int)(def->HitThickness * def->ModelScale));
	if (def->CollideBoxs.size() > 0)
	{
		getLocoMotion()->setBaseCollideBoxs(def->CollideBoxs, def->CollideBoxs);
	}

	CreateComponent<ActorVision>("ActorVision");

	MobAttrib* attrib = NULL;
	if (IsVillager(monsterid))
	{
		attrib = CreateComponent<VillagerAttrib>("VillagerAttrib");
	}
	else
	{
		attrib = CreateComponent<MobAttrib>("MobAttrib");
	}

	if (attrib) {
		attrib->init(def);
		do // 绑定效果事件
		{
			auto callBackAppend = [this](int buffid, int bufflvl) {
				this->onBuffAppend(buffid, bufflvl);
			};
			attrib->setDelegateBuffAppend(callBackAppend);

			auto callBackRemove = [this](int buffid, int bufflvl) {
				this->onBuffRemove(buffid, bufflvl);
			};
			attrib->setDelegateBuffRemove(callBackRemove);
		} while (false);
	}

	if (def->ParentID > 0)
	{
		setGrowingAge(getMobGrowTime(m_Def->ID));
		setGrowingDValue(getMobGrowDValue(m_Def->ID));
		setLoveCD(0);
	}
	else
	{
		setGrowingAge(0);
		setGrowingDValue(0);
		setLoveCD(0);
	}

	CreateComponent<NavigationPath>("NavigationPath");
	return true;
}

bool ClientMob::init(int monsterid)
{
	OPTICK_EVENT();
	if (!mobInit(monsterid))
		return false;

	ENG_DELETE(m_Body);
	//可变部分用脚本实现
	InitModify();
	//如果在json中定义了avatar数据
	if (getDef()->gamemod)
	{
		jsonxx::Object* pobj = nullptr;
		getDef()->gamemod->Event2().Emit<jsonxx::Object*&, int>("Mod_GetAvatarJsonObjById", pobj, m_monsterId);
		//jsonxx::Object* pobj = def->gamemod->GetAvatarJsonObjById(monsterid);
		if (pobj)
		{
			//解析avatar数据
			ParseAvatarInfo(*pobj);
		}
	}
	applyDisplayName();

	//检查头顶信息是否需要特殊处理
	checkActorDisplayName(monsterid, false);
	if (monsterid == 3824 || monsterid == 3829)
	{
		if (m_Body)
		{
			m_Body->showNecklace(1);
		}
	}

	initActorAttribTrigger();
	if (VacantComponent::IsVacantId(monsterid))
	{
		cacheVacantComponent(CreateComponent<VacantComponent>("VacantComponent"));
	}

	if (monsterid == 3271) {
		m_pVacantVortexComponent = CreateComponent<VacantVortexComponent>("VacantVortexComponent");
	}

	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->init();
	}
	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->init();
	}

	return true;
}

bool ClientMob::init(int monsterid, const jsonxx::Object& obj)
{
	OPTICK_EVENT();
	if (!mobInit(monsterid))
		return false;

		ENG_DELETE(m_Body);
	//可变部分用脚本实现
	InitModify(obj);
	if (getDef()->gamemod)
	{
		jsonxx::Object* pobj = nullptr;
		getDef()->gamemod->Event2().Emit<jsonxx::Object*&, int>("Mod_GetAvatarJsonObjById", pobj, m_monsterId);
		if (pobj)
		{
			//解析avatar数据
			ParseAvatarInfo(*pobj);
		}
	}
	applyDisplayName();

	//检查头顶信息是否需要特殊处理
	checkActorDisplayName(monsterid, false);
	if (monsterid == 3824 || monsterid == 3829)
	{
		if (m_Body)
		{
			m_Body->showNecklace(1);
		}
	}

	initActorAttribTrigger();
	if (VacantComponent::IsVacantId(monsterid))
	{
		cacheVacantComponent(CreateComponent<VacantComponent>("VacantComponent"));
	}

	if (monsterid == 3271)
	{
		m_pVacantVortexComponent = CreateComponent<VacantVortexComponent>("VacantVortexComponent");
	}

	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->init();
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->init();
	}

	return true;
}

void ClientMob::InitModify()
{
	const MonsterDef* def = getDef();
	if (def)
	{
		UgcAssetPrefab* pPrefab = UgcAssetMgr::GetInstancePtr()->GetPrefabAsset(ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Monster, def->ID));
		if (pPrefab)
		{
			const jsonxx::Object& obj = pPrefab->GetJsonObject();
			if (obj.has<jsonxx::Object>("root"))
			{
				InitModify(obj.get<jsonxx::Object>("root"), false);
				return;
			}
		}
	}

	jsonxx::Object rootobj;
	InitModify(rootobj, false);
}

void ClientMob::InitModify(const jsonxx::Object& obj, bool isCreat)
{
	if (!obj.empty())
	{
		const jsonxx::Array& vComponents = obj.get<jsonxx::Array>("components");
		
		jsonxx::Object monsterEditObj;
		jsonxx::Object fightEditObj;
		jsonxx::Object bTreeEditObj;
		for (size_t i = 0; i < vComponents.size(); i++)
		{
			const jsonxx::Object& cmpObj = vComponents.get<jsonxx::Object>(i);
			if (cmpObj.has<jsonxx::String>("name"))
			{
				const std::string& sCmpName = cmpObj.get<jsonxx::String>("name");
				if (isCreat)
				{
					if (!m_stData)
					{
						m_stData = ENG_NEW(ScriptCmpData);
					}
					ActorComponentBase* pComponent = (ActorComponentBase*)CreateComponentByFactory(sCmpName, sCmpName);
					if (pComponent)
					{
						pComponent->LoadComponentData(cmpObj, false);

						jsonxx::Object obj;
						obj << "name" << sCmpName;
						m_stData->vObj.emplace_back(obj);
					}
					else if(sCmpName.compare("MonsterEdit") != 0 && sCmpName.compare("FightEdit") != 0 && sCmpName.compare("BTree") != 0)
					{
						m_stData->vObj.emplace_back(cmpObj);
					}
				}
				else
				{
					if (!m_Body)
					{
						m_Body = ENG_NEW(ActorBody)(this);
					}
				}
			}
		}
		m_bLoadBTree = true;
	}

	if (!m_Body)
	{
		m_Body = newActorBody();
	}

	if (!m_bLoadBTree)
	{
		loadAI();
	}
}
void ClientMob::LoadScriptComponent()
{
	if (m_stData)
	{
		if (m_stData->vObj.size() > 0)
		{
			auto pScriptComponent = CreateComponent<ScriptComponent>("ScriptComponent");
			if (pScriptComponent)
			{
				pScriptComponent->AddScriptComponent(*m_stData);
			}
		}
		SetWorldScale(m_Def->ModelScale, m_Def->ModelScale, m_Def->ModelScale);
		ENG_DELETE(m_stData);
	}
}

bool ClientMob::changeBtree(const char* uuid, const char* treepath, const char* bbpath)
{
	if (m_bLoadBTree)
	{
		m_bLoadBTree = false;
	}
	//已经加载的行为树一致不重复加载
	if (m_btree)
	{
		std::string curuuid = m_btree->GetUUID();
		if (curuuid.compare(uuid) == 0)
		{
			return true;
		}
	}
	//if (m_AITask)
	//{
	//	m_AITask->clearAllRunningTasks();
	//	ENG_DELETE(m_AITask);
	//}
	//if (m_AITaskTarget)
	//{
	//	m_AITaskTarget->clearAllRunningTasks();
	//	ENG_DELETE(m_AITaskTarget);
	//}

	m_mobtick = 0;
	if (m_btree)
	{
		m_btree->SetActive(false);
		m_needClearBtree = m_btree;
		m_needClearBb = m_bb;
		m_btree = nullptr;
		m_bb = nullptr;
	}

	//加入清理 下一帧清理
	//clearAI();
	if (treepath && strlen(treepath) > 0)
	{
		m_btree = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->AIEditCreateBTreeForTargetByPath(treepath, uuid, getDef()->ID, this);
		if (m_btree)
		{
			m_btree->SetUUID(uuid);
			m_bb = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->AIEditCreateBlackboardByPath(bbpath, uuid, getDef()->ID);
			if (m_bb)
			{
				static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->BTreeBindBlackboard(m_btree, m_bb);
			}

			//如果在地图中则设置为激活状态
			if (m_pWorld)
			{
				m_btree->SetActive(true);
			}
			//需要通知配置切换了uuid
			double id = getObjId();
			int treeid = m_btree->GetInstanceID();
			ScriptVM* LuaVM = MINIW::ScriptVM::game();
			if (LuaVM)
			{
				std::string suuid = uuid;
				LuaVM->callFunction("BTActorChangeTree", "isd", treeid, suuid.c_str(), id);
			}

			return true;
		}
	}
	return false;
}
void ClientMob::reloadAI()
{
	//解除ai
	clearAI();
	//然后重新加载
	m_bLoadBTree = true;
	LoadBTree();
}
void ClientMob::clearAI()
{
	if (m_btree)
	{
		m_btree->SetActive(false);
	}
	//清理 AI
	if (GetWorldManagerPtr()) {//autorelease pool的存在  可能GetWorldManagerPtr()先释放
		if (m_bb)// 行为树AI
			static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestoryBlackboard(m_bb);
		if (m_btree)
			static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestroyBTree(m_btree);
	}//如果GetWorldManagerPtr()已经释放  其析构的时候 就把所有的bb，btree全部释放了
	m_bb = NULL;
	m_btree = NULL;


	if (m_isUseAILua)
	{
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "clearAllRunningTasks", "i", m_AILuaKey);
	}
	else
	{
		if (m_AITask)
		{
			m_AITask->clearAllRunningTasks();
			ENG_DELETE(m_AITask);
		}
		if (m_AITaskTarget)
		{
			m_AITaskTarget->clearAllRunningTasks();
			ENG_DELETE(m_AITaskTarget);
		}
	}
}

void ClientMob::loadAI()
{
	char szScriptFun[256] = { 0 };
	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Init", getDef()->ID);
	MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
	bool ai = false;
	//加载AI编辑器地图编辑的ai
	unsigned int curMobId = getDef()->ID;
	bool loadai = false;
	const MonsterDef* def = getDef();
	if (!ai)
	{
		ai = loadAIEditTree(true);
	}
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->GetBTManager() && !ai) // 行为树AI
	{
		unsigned int curMobId = getDef()->ID;
		const unsigned validMobIds[] = { // 参考lua注册（MobEventConfig.lua），这里在C++ 层先过滤一遍，比callfunction 效率更高
					3101, //原版野人
					3200, //新野人
					3201, //新野人，与3200共用
					3202, //新野人，与3200共用
					3210, //普通沙漠商人1
					3211, //普通沙漠商人2
					3212, //特殊商人
					3213, //沙漠商人护卫
					3204, //沙漠村民男1
					3205, //沙漠村民男2
					3206, //沙漠村民女1
					3207, //沙漠村民女2
					3208, //沙漠村民小孩1
					3209, //沙漠村民小孩2
					3214, //渔村村民男1
					3215, //渔村村民男2
					3216, //渔村村民女1
					3217, //渔村村民女2
					3218, //渔村村民小孩1
					3219, //渔村村民小孩2
					3220, //海盗1
					3221, //海盗2
					3222, //海盗商人
					3223, //海岛商人
					3229, //渔村钓手
					3912, //牦牛
					3913, //小牦牛
					3915, //氷怪
					3233,//冰原村民             与3200共用
					3234,//冰原野萌宝		   与3200共用
					3235,//野生冰原护卫村民	   与3200共用
					3236,//野生冰原猎人村民	   与3200共用
					3237,//野生冰原樵夫村民	   与3200共用
					3238,//野生冰原工人村民	   与3200共用
					3239,//野生冰原助手村民	   与3200共用
					3022,//游商商人

		};
		size_t cnt = sizeof(validMobIds) / sizeof(validMobIds[0]);
		bool valid = false;

		for (size_t i = 0; i < cnt; ++i)
		{
			if (validMobIds[i] == curMobId)
			{
				valid = true;
				break;
			}
		}

		if (valid)
		{
			OPTICK_EVENT("AI:Create from BTree");
			snprintf(szScriptFun, sizeof(szScriptFun) - 1, "MOB_%d", getDef()->ID);
			auto btManager = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager());
			if (m_bb)// 行为树AI
				btManager->DestoryBlackboard(m_bb);
			if (m_btree)
				btManager->DestroyBTree(m_btree);
			m_btree = btManager->CreateBTreeForTarget(szScriptFun, this);
			snprintf(szScriptFun, sizeof(szScriptFun) - 1, "MOB_%d", getDef()->ID);
			m_bb = btManager->CreateBlackboard(szScriptFun);
			if (m_btree && m_bb)
			{

				btManager->BTreeBindBlackboard(m_btree, m_bb); // 绑定行为树和黑板

				ai = true; // ai绑定成功
				//如果在地图中则设置为激活状态
				if (m_pWorld)
				{
					m_btree->SetActive(true);
				}
			}
			else
			{

				auto btmanager = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager());
				if (m_bb)// 行为树AI
					btmanager->DestoryBlackboard(m_bb);
				if (m_btree)
					btmanager->DestroyBTree(m_btree);
				m_bb = NULL;
				m_btree = NULL;
			}

		}

	}
	if (!ai && getDef()->gamemod)
	{
		OPTICK_EVENT("AI:Create from Json");
		//如果在json中定义了AI
		jsonxx::Array jsonArray;
		getDef()->gamemod->Event2().Emit<jsonxx::Array&, int>("Mod_GetAIJsonArrayByMonsterId", jsonArray, m_monsterId);

		//jsonxx::Array jsonArray = def->gamemod->GetAIJsonArrayByMonsterId(monsterid);
		if (jsonArray.size() > 0)
		{
			ai = true; // ai绑定成功
			ParseAI(jsonArray);
			//从配置文件中读取的三个属性。lua中已经调用的相应的函数，不需要重复调用
			//setSunHurt(def->SunHurt);
			//setCanPassClosedWoodenDoors(def->CanPassClosedWoodenDoors);
			//setAvoidWater(def->AvoidWater);
			//setPathHide(def->PathHide);
		}
	}
	if (!ai)
	{
		//加载AI编辑器官方ai
		ai = loadAIEditTree(false);
	}
	if (!ai)//使用lua中定义的ai脚本
	{
		OPTICK_EVENT("AI:Create from Lua");
		if (m_Def->ModelType == MONSTER_CUSTOM_MODEL || m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
			snprintf(szScriptFun, sizeof(szScriptFun), "FCustomActor_SetAi");
		else
		{
			if (getIsHomeLandPet()) //是宠物共用同一套
			{
				snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", 10001);
			}
			else
			{
				snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", getDef()->ID);
			}

			//code-by：dengpeng
			if (m_Def->Fodder[0] == 1) //草食生物AI
			{
				addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
				addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
			}
			if (m_Def->Fodder[0] == 2) //肉食生物AI
			{
				addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
				addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
			}

			if (m_Def->Fodder[0] == 3) //杂食生物AI
			{
				addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
				addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
				addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
			}
		}
		if (MINIW::ScriptVM::game()->isFunction(szScriptFun))
		{
			MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
			ai = true;
		}
		else
		{
			ai = false;
		}
	}
	if (!ai)//AILua方式管理
	{
		OPTICK_EVENT("AI:Create from AILua");
		snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAILua", getDef()->ID);
		GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCreate", "u[ClientMob]s>i", this, szScriptFun, &m_AILuaKey);
	}
}


void ClientMob::ParseAvatarInfo(jsonxx::Object avatarInfoJson)
{
	if (avatarInfoJson.has<jsonxx::Array>("skin"))
	{
		jsonxx::Array skinArray = avatarInfoJson.get<jsonxx::Array>("skin");
		for (size_t i = 0; i < skinArray.size(); i++)
		{
			if (skinArray.values().at(i)->is<jsonxx::Object>())
			{
				float h, s, b = 0;
				int blockID = -1;

				jsonxx::Object aSkin = skinArray.get<jsonxx::Object>(i);
				if (aSkin.has<jsonxx::Object>("cfg"))
				{
					jsonxx::Object aSkinCfg = aSkin.get<jsonxx::Object>("cfg");
					if (aSkinCfg.has<jsonxx::Number>("ModelID") && aSkinCfg.has<jsonxx::Number>("Part"))
					{
						int modelId = (int)aSkinCfg.get<jsonxx::Number>("ModelID");
						int part = (int)aSkinCfg.get<jsonxx::Number>("Part");

						//异步装扮
						getBody()->addAvatarPartModel(modelId, part, true);
						MNSandbox::alterAvatarPartColorAsyn(getBody(), modelId, part, aSkin, h, s, b, blockID);
					}
				}
			}
		}
	}
}

bool ClientMob::isLoadAiEditNode()
{
	if (m_btree && m_btree->isLoadAiEditNode())
	{
		return true;
	}
	return false;
}


static int SavePBActorBuffs(PB_ActorMob* actorMob, LivingAttrib* attrib)
{
	if (attrib == NULL) return 0;
	for (size_t i = 0; i < attrib->m_Buffs.size(); i++)
	{
		ActorBuff& b = attrib->m_Buffs[i];
		PB_AOIActorBuff* buffer = actorMob->add_buffs();
		buffer->set_buffid(b.buffid);
		buffer->set_bufflv(b.bufflv);
		buffer->set_ticks(b.ticks);
	}
	return 0;
}
static void LoadPBActorBuffs(const PB_ActorMob& actorMob, LivingAttrib* attrib)
{
	if (!attrib) { return; }
#if 0
	if (attrib->isNewStatus()) {
		for (size_t i = 0; i < actorMob.buffs_size(); i++) {
			const PB_AOIActorBuff& srcbuff = actorMob.buffs(i);
			attrib->addBuff(srcbuff.buffid(), srcbuff.bufflv(), srcbuff.ticks());
		}
	}
	else {
		attrib->m_Buffs.resize(actorMob.buffs_size());

		for (size_t i = 0; i < actorMob.buffs_size(); i++)
		{
			const PB_AOIActorBuff& srcbuff = actorMob.buffs(i);
			ActorBuff& dest = attrib->m_Buffs[i];

			dest.buffid = srcbuff.buffid();
			dest.bufflv = srcbuff.bufflv();
			dest.ticks = srcbuff.ticks();
			dest.def = GetDefManagerProxy()->getBuffDef(dest.buffid, dest.bufflv);
		}
	}
#else
	for (size_t i = 0; i < actorMob.buffs_size(); i++) {
		const PB_AOIActorBuff& srcbuff = actorMob.buffs(i);
		attrib->addBuff(srcbuff.buffid(), srcbuff.bufflv(), srcbuff.ticks());
	}
#endif
}

EXPORT_SANDBOXGAME flatbuffers::Offset<flatbuffers::Vector<const FBSave::ActorBuff *>> SaveActorBuffs(SAVE_BUFFER_BUILDER &builder, LivingAttrib *attrib)
{

	std::vector<FBSave::ActorBuff>buffarray;
	if (attrib == NULL) return builder.CreateVectorOfStructs(buffarray);
	buffarray.reserve(attrib->m_Buffs.size());

	for (size_t i = 0; i < attrib->m_Buffs.size(); i++)
	{
		ActorBuff& b = attrib->m_Buffs[i];
		buffarray.push_back(FBSave::ActorBuff(b.buffid, b.bufflv, b.ticks));
	}

	return builder.CreateVectorOfStructs(buffarray);
}

static void LoadActorBuffs(const FBSave::ActorMob* src, LivingAttrib* attrib)
{
	if (!attrib) { return; }
	auto buffs = src->buffs();
	if (!buffs)
		return;

#if 0
	if (attrib->isNewStatus()) {
		for (size_t i = 0; i < buffs->size(); i++) {
			const FBSave::ActorBuff* srcbuff = buffs->Get(i);
			if (srcbuff == nullptr) continue;

			attrib->addBuff(srcbuff->buffid(), srcbuff->bufflv(), srcbuff->ticks());
		}
	}
	else {
		attrib->m_Buffs.resize(buffs->size());

		for (size_t i = 0; i < buffs->size(); i++)
		{
			const FBSave::ActorBuff* srcbuff = buffs->Get(i);
			if (srcbuff == nullptr) continue;

			ActorBuff& dest = attrib->m_Buffs[i];
			dest.buffid = srcbuff->buffid();
			dest.bufflv = srcbuff->bufflv();
			dest.ticks = srcbuff->ticks();
			dest.def = GetDefManagerProxy()->getBuffDef(dest.buffid, dest.bufflv);
		}
	}
#else
	for (size_t i = 0; i < buffs->size(); i++) {
		const FBSave::ActorBuff* srcbuff = buffs->Get(i);
		if (srcbuff == nullptr) continue;

		attrib->addBuff(srcbuff->buffid(), srcbuff->bufflv(), srcbuff->ticks());
	}
#endif
}

int SavePBActorMods(PB_ActorMob* actorMob, LivingAttrib* attrib)
{
	if (attrib == NULL) return 0;
	for (size_t i = 0; i < attrib->m_Attribs.size(); i++)
	{
		AttribModified& attr = attrib->m_Attribs[i];
		if (attr.value != 0)
		{
			PB_AttribMod* mod = actorMob->add_mods();
			mod->set_attr(i);
			mod->set_val(attr.value * 1000);
		}
	}
	return 0;
}
static void LoadPBActorMods(const PB_ActorMob& actorMob, LivingAttrib* attrib)
{
	if (attrib == NULL) return;

	for (size_t i = 0; i < actorMob.mods_size(); i++)
	{
		const PB_AttribMod& srcmod = actorMob.mods(i);

		assert(srcmod.attr() < int(attrib->m_Attribs.size()));
		attrib->m_Attribs[srcmod.attr()].value = srcmod.val() / 1000.0;
	}
}
flatbuffers::Offset<flatbuffers::Vector<const FBSave::AttribMod*>> SaveActorMods(SAVE_BUFFER_BUILDER& builder, LivingAttrib* attrib)
{

	std::vector<FBSave::AttribMod>modarray;
	if (attrib != NULL)
	{
		modarray.reserve(attrib->m_Attribs.size());
		for (size_t i = 0; i < attrib->m_Attribs.size(); i++)
		{
			AttribModified& attr = attrib->m_Attribs[i];
			if (attr.value != 0)
			{
				modarray.push_back(FBSave::AttribMod(i, attr.value));
			}
		}
	}
	return builder.CreateVectorOfStructs(modarray);
}

static void LoadActorMods(const FBSave::ActorMob* src, LivingAttrib* attrib)
{
	auto mods = src->mods();
	if (!mods)
		return;

	for (size_t i = 0; i < mods->size(); i++)
	{
		const FBSave::AttribMod* srcmod = mods->Get(i);

		assert(srcmod->attr() < int(attrib->m_Attribs.size()));
		attrib->m_Attribs[srcmod->attr()].value = srcmod->val();
	}
}

std::string ClientMob::createComponentSaveData()
{
	jsonxx::Object componentData;

	auto& comps = GetAllComponents();
	comps.for_each([&componentData](::MNSandbox::Component* comp) -> void {
		ActorComponentBase* compBase = comp->ToCast<ActorComponentBase>();
		if (compBase && compBase->GetUgcComponentFactoryName() == nullptr)
		{
			compBase->CreateComponentData(componentData);
		}
		});

	if (componentData.size() > 0)
	{
		return componentData.json_nospace();
	}
	else
	{
		return "";
	}
}

void ClientMob::loadComponentSaveData(const std::string& componentDataStr)
{
	jsonxx::Object componentData;
	componentData.parse(componentDataStr);

	if (componentData.size() <= 0)
	{
		return;
	}

	auto& comps = GetAllComponents();
	comps.for_each([&componentData](::MNSandbox::Component* comp) -> void {
		ActorComponentBase* compBase = comp->ToCast<ActorComponentBase>();
		if (compBase)
		{
			compBase->LoadComponentData(componentData, true);
		}
		});
}

int ClientMob::getBufferId()
{
	return nullptr != m_Def ? m_Def->BuffId : 0;
}

flatbuffers::Offset<FBSave::ActorMob> ClientMob::saveMob(SAVE_BUFFER_BUILDER& builder)
{
	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());

	auto basedata = saveActorCommon(builder);
	auto buffs = SaveActorBuffs(builder, attrib);
	auto mods = SaveActorMods(builder, attrib);

	string scriptComponent = "";
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> childrenOffset = 0;
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> ugccomponentsOffset = 0;
	SaveObjectChildAndComponent(builder, scriptComponent, ugccomponentsOffset, childrenOffset);

	flatbuffers::Offset<FBSave::ItemIndexGrid> grids[MAX_EQUIP_SLOTS];
	int count = 0;
	if (m_pEquipGridContainer)
	{
		int equipcount = m_pEquipGridContainer->getGridCount();
		for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
		{
			BackPackGrid* itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;
			grids[count++] = itemgrid->saveWithIndex(builder);
		}
	}
	else
	{
		for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
		{
			BackPackGrid* itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;

			grids[count++] = itemgrid->saveWithIndex(builder);
		}
	}
	
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> equips = 0;
	if (count > 0) equips = builder.CreateVector(grids, count);


	flatbuffers::Offset<FBSave::ItemIndexGrid> baggrids[MAX_BAGS_GRID];
	int bagcount = 0;

	auto bagsContainer = attrib->getBags();
	if (bagsContainer)
	{
		for (int i = 0; i < MAX_BAGS_GRID; i++)
		{
			BackPackGrid* itemgrid = bagsContainer->index2Grid(i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;

			baggrids[bagcount++] = itemgrid->saveWithIndex(builder);
		}
	}
	else
	{
		if (m_pGridContainer)
		{
			int count = m_pGridContainer->getGridCount();
			for (int i = 0; i < count; i++)
			{
				BackPackGrid* itemgrid = m_pGridContainer->index2Grid(i);
				if (itemgrid == NULL || itemgrid->isEmpty()) continue;

				baggrids[bagcount++] = itemgrid->saveWithIndex(builder);
			}
		}

	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ItemIndexGrid>>> bags = 0;
	if (bagcount > 0) bags = builder.CreateVector(baggrids, bagcount);

	auto functionWrapper = getFuncWrapper();
	bool Climbing = getClimbing();
	FBSave::Coord3 tmpBonFirePos = WCoordToCoord3(m_BonFirePos);

	float temperature = 0.0f; // ChunkSave.fbs下有添加这个字段，但是没有用到.

	std::string componentDataStr = createComponentSaveData();
	std::string bbstr = "";
	if (m_bb)
	{
		bbstr = m_bb->ToString();
	}
	flatbuffers::Offset<flatbuffers::Vector<float>> extendattr = 0;
	SaveMobTriggerAttr(builder,extendattr);
	return FBSave::CreateActorMob(builder, basedata, m_Def->ID, getAttrib()->getHP(), getTamedOwnerID(), m_Color, buffs, mods, equips, getGrowingAge(), m_RandomScale, m_DieTicks, bags, getMobAttrib()->getFood(), getCustomScale(),
		&tmpBonFirePos, m_iTickAnimAwake, builder.CreateString(m_displayName.c_str()), Climbing, builder.CreateString(m_ServerWid.c_str()), isEaten(), getCurNeedFeedNum(), temperature, getGrowingDValue(), getGrowingTime(), builder.CreateString(componentDataStr.c_str()), getTeam(),
		builder.CreateString(bbstr.c_str()), builder.CreateString(scriptComponent), ugccomponentsOffset, childrenOffset, extendattr, m_MobPresetPosKey);
}

void ClientMob::saveMob(jsonxx::Object& obj)
{
	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
	saveActorCommon(obj);
	if (attrib && attrib->m_Buffs.size())
	{
		obj << "buff" << jsonxx::Array();
		jsonxx::Array& obj_ = (jsonxx::Array&)obj.get<jsonxx::Array>("buff");
		for (size_t i = 0; i < attrib->m_Buffs.size(); i++)
		{
			ActorBuff& b = attrib->m_Buffs[i];
			//obj_<<(jsonxx::Array()<<b.buffid<<b.bufflv<<b.ticks);
			jsonxx::Value* temp = new jsonxx::Value();
			temp->type_ = jsonxx::Value::ARRAY_;
			temp->array_value_ = new jsonxx::Array();
			*temp->array_value_ << b.buffid << b.bufflv << b.ticks;
			obj_ << temp;
			temp->release();
		}
	}
	if (attrib && attrib->m_Attribs.size())
	{
		obj << "modattrib" << jsonxx::Array();
		jsonxx::Array& obj_ = (jsonxx::Array&)obj.get<jsonxx::Array>("modattrib");
		for (size_t i = 0; i < attrib->m_Attribs.size(); i++)
		{
			AttribModified& attr = attrib->m_Attribs[i];
			if (attr.value != 0)
			{
				jsonxx::Value* temp = new jsonxx::Value();
				temp->type_ = jsonxx::Value::ARRAY_;
				temp->array_value_ = new jsonxx::Array();
				*temp->array_value_ << i << attr.value;
				obj_ << temp;
				temp->release();
			}

		}
	}
	obj << "equips" << jsonxx::Array();
	jsonxx::Array& obj_ = (jsonxx::Array&)obj.get<jsonxx::Array>("equips");
	int count = 0;
	if (m_pEquipGridContainer)
	{
		int equipcount = m_pEquipGridContainer->getGridCount();
		for (int i = 0; i < equipcount; i++)
		{
			BackPackGrid* itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;
			obj_.addObject();
			jsonxx::Object& obj_itemgrid = (jsonxx::Object&)obj_.get<jsonxx::Object>(count);
			itemgrid->saveWithIndex(obj_itemgrid);
			count++;
		}
	}
	else
	{
		for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
		{
			BackPackGrid* itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;
			obj_.addObject();
			jsonxx::Object& obj_itemgrid = (jsonxx::Object&)obj_.get<jsonxx::Object>(count);
			itemgrid->saveWithIndex(obj_itemgrid);
			count++;
		}
	}
	flatbuffers::Offset<FBSave::ItemIndexGrid> baggrids[MAX_BAGS_GRID];
	int bagcount = 0;
	auto bagsContainer = attrib->getBags();
	if (bagsContainer)
	{
		obj << "bags" << jsonxx::Array();
		jsonxx::Array& obj_ = (jsonxx::Array&)obj.get<jsonxx::Array>("bags");
		int count = 0;
		for (int i = 0; i < MAX_BAGS_GRID; i++)
		{
			BackPackGrid* itemgrid = bagsContainer->index2Grid(i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;
			if (!obj_.has<jsonxx::Object>(count))
			{
				jsonxx::Value v_obj;
				v_obj.type_ = jsonxx::Value::OBJECT_;
				v_obj.object_value_ = new jsonxx::Object();
				obj_ << v_obj;
			}
			jsonxx::Object& obj_itemgrid = (jsonxx::Object&)obj_.get<jsonxx::Object>(count);
			itemgrid->saveWithIndex(obj_itemgrid);
			count++;
		}
	}
	else {
		if (m_pGridContainer)
		{
			jsonxx::Array& obj_ = (jsonxx::Array&)obj.get<jsonxx::Array>("bags");
			int count = 0;
			int bagcount = m_pGridContainer->getGridCount();
			for (int i = 0; i < bagcount; i++)
			{
				BackPackGrid* itemgrid = m_pGridContainer->index2Grid(i);
				if (itemgrid == NULL || itemgrid->isEmpty()) continue;
				if (!obj_.has<jsonxx::Object>(count))
				{
					jsonxx::Value v_obj;
					v_obj.type_ = jsonxx::Value::OBJECT_;
					v_obj.object_value_ = new jsonxx::Object();
					obj_ << v_obj;
				}
				jsonxx::Object& obj_itemgrid = (jsonxx::Object&)obj_.get<jsonxx::Object>(count);
				itemgrid->saveWithIndex(obj_itemgrid);
				count++;
			}
		}
	}

	//	FBSave::Coord3 tmpBonFirePos = WCoordToCoord3(m_BonFirePos);
		//return FBSave::CreateActorMob(builder, basedata, m_Def->ID, getAttrib()->getHP(), m_TamedOwnerUin, m_Color, buffs, mods, equips, m_GrowingAge, m_RandomScale, m_DieTicks, bags, getMobAttrib()->m_Food, getCustomScale(),
		//	&tmpBonFirePos, m_iTickAnimAwake, builder.CreateString(m_displayName.c_str()), m_Climbing, builder.CreateString(m_ServerWid.c_str()), m_bEaten, m_iCurNeedFeedNum);
	obj << "hp" << getAttrib()->getHP();
	obj << "id" << m_Def->ID;
	obj << "tamedowneruin" << getTamedOwnerID();
	obj << "color" << m_Color;
	//obj<<"growage"<<m_GrowingAge;
	obj << "growage" << getGrowingAge();
	obj << "randomscle" << m_RandomScale;
	obj << "dietick" << m_DieTicks;
	obj << "food" << getMobAttrib()->getFood();
	obj << "customscle" << getCustomScale();
	obj << "baggrids" << jsonxx::Array();
	obj << "bonfirepos" << WCoordToCoord3Json(m_BonFirePos);
	obj << "tmpbonfirepos" << WCoordToCoord3Json(m_BonFirePos);
	obj << "displayname" << m_displayName;
	obj << "tickanimawake" << m_iTickAnimAwake;

	bool Climbing = getClimbing();
	obj << "climbing" << Climbing;
	//obj<<"eaten"<<m_bEaten;
	obj << "eaten" << isEaten();
	//obj<<"curneedfeednum"<<m_iCurNeedFeedNum;
	obj << "curneedfeednum" << getCurNeedFeedNum();
	obj << "severwid" << m_ServerWid;
	obj << "growdvalue" << getGrowingDValue();
	obj << "growtime" << getGrowingTime();
	obj << "iskinned" << getSkinned();
	obj << "presetposkey" << m_MobPresetPosKey;
}

void ClientMob::save(jsonxx::Object& obj)
{
	saveMob(obj);
}

bool ClientMob::load(const jsonxx::Object& obj, int version)
{
	if (!obj.has<jsonxx::Number>("id"))
	{
		return false;
	}
	if (!init(obj.get<jsonxx::Number>("id")))
	{
		return false;
	}
	//getBody()->setScale(src->scale() * m_Def->ModelScale);
	if (obj.has<jsonxx::Number>("randomscle"))
		m_RandomScale = obj.get<jsonxx::Number>("randomscle");
	if (obj.has<jsonxx::Number>("customscle"))
		setCustomScale(obj.get<jsonxx::Number>("customscle"));
	if (obj.has<jsonxx::Number>("randomscle"))
		setScale(obj.get<jsonxx::Number>("randomscle") * m_Def->ModelScale);
	loadActorCommon(obj);

	getAttrib()->initHP(obj.get<jsonxx::Number>("hp"));
	if (version == 0)
		getAttrib()->initHP(getAttrib()->getHP() * 5.0f);
	//m_TamedOwnerUin = obj.get<jsonxx::Number>("tamedowneruin");
	setTamedOwnerID(obj.get<jsonxx::Number>("tamedowneruin"));
	m_Color = obj.get<jsonxx::Number>("color");
	if (m_Color >= 0 || getSheared())
	{
		getBody()->setBodyColor(m_Color, getSheared());
	}
	//m_GrowingAge = obj.get<jsonxx::Number>("growage");
	setGrowingAge(obj.get<jsonxx::Number>("growage"));
	setGrowingTime(obj.get<jsonxx::Number>("growtime"));
	setGrowingDValue(obj.get<jsonxx::Number>("growdvalue"));
	if (m_Def->ParentID > 0 && getGrowingAge() == 0)
	{
		//m_GrowingAge = -24000;
		setGrowingAge(-24000);
	}
	setLoveCD(6000);
	m_DieTicks = obj.get<jsonxx::Number>("dietick");
	m_iTickAnimAwake = obj.get<jsonxx::Number>("tickanimawake");
	//m_Climbing = obj.get<jsonxx::Boolean>("climbing");
	setClimbing(obj.get<jsonxx::Boolean>("climbing"));
	m_ServerWid = obj.get<jsonxx::String>("severwid");
	//m_bEaten = obj.get<jsonxx::Boolean>("eaten");
	setEaten(obj.get<jsonxx::Boolean>("eaten"));
	//m_iCurNeedFeedNum = obj.get<jsonxx::Number>("curneedfeednum");
	setCurNeedFeedNum(obj.get<jsonxx::Number>("curneedfeednum"));
	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
	if (attrib != NULL) {
		if (obj.has<jsonxx::Array>("buff"))
		{
			auto buffs = obj.get<jsonxx::Array>("buff");
#if 0
			if (attrib->isNewStatus()) {
				for (size_t i = 0; i < buffs.size(); i++) {
					auto buff_ = buffs.get<jsonxx::Array>(i);
					attrib->addBuff(buff_.get<jsonxx::Number>(0), buff_.get<jsonxx::Number>(1), buff_.get<jsonxx::Number>(2));
				}
			}
			else {
				attrib->m_Buffs.resize(buffs.size());
				for (size_t i = 0; i < buffs.size(); i++)
				{
					auto buff_ = buffs.get<jsonxx::Array>(i);
					ActorBuff& dest = attrib->m_Buffs[i];
					dest.buffid = buff_.get<jsonxx::Number>(0);
					dest.bufflv = buff_.get<jsonxx::Number>(1);
					dest.ticks = buff_.get<jsonxx::Number>(2);
					dest.def = GetDefManagerProxy()->getBuffDef(dest.buffid, dest.bufflv);
				}
			}
#else
			for (size_t i = 0; i < buffs.size(); i++) {
				auto buff_ = buffs.get<jsonxx::Array>(i);
				attrib->addBuff(buff_.get<jsonxx::Number>(0), buff_.get<jsonxx::Number>(1), buff_.get<jsonxx::Number>(2));
			}
#endif
		}
		if (obj.has<jsonxx::Array>("modattrib"))
		{
			auto mods = obj.get<jsonxx::Array>("modattrib");
			for (size_t i = 0; i < mods.size(); i++)
			{
				auto mod = mods.get<jsonxx::Array>(i);
				attrib->m_Attribs[mod.get<jsonxx::Number>(0)].value = mod.get<jsonxx::Number>(1);
			}
		}
		

		if (obj.has<jsonxx::Array>("equips"))
		{

			if (m_pEquipGridContainer)
			{
				auto equips = obj.get<jsonxx::Array>("equips");
				BackPackGrid itemgrid;
				for (size_t i = 0; i < equips.size(); i++)
				{
					itemgrid.load(equips.get<jsonxx::Object>(i));
					m_pEquipGridContainer->equip((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
				}
			}
			else 
			{
				auto equips = obj.get<jsonxx::Array>("equips");
				BackPackGrid itemgrid;
				for (size_t i = 0; i < equips.size(); i++)
				{
					itemgrid.load(equips.get<jsonxx::Object>(i));
					attrib->equip((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
				}
			}

			
		}

		if (obj.has<jsonxx::Array>("bags"))
		{
			auto bags = obj.get<jsonxx::Array>("bags");
			if (m_pGridContainer)
			{
				BackPackGrid itemgrid;
				for (size_t i = 0; i < bags.size(); i++)
				{
					itemgrid.load(bags.get<jsonxx::Object>(i));
					m_pGridContainer->setGridInfo(itemgrid.getIndex(), bags.get<jsonxx::Object>(i));
				}
			}
			else
			{
				BackPackGrid itemgrid;
				for (size_t i = 0; i < bags.size(); i++)
				{
					itemgrid.load(bags.get<jsonxx::Object>(i));
					attrib->setBagItem(itemgrid.getIndex(), &itemgrid);
				}
			}
			
		}
		attrib->setFood(obj.get<jsonxx::Number>("food"));
		if (obj.has<jsonxx::Array>("bonfirepos"))
		{
			m_BonFirePos = Coord3ToWCoordJson(obj.get<jsonxx::Array>("bonfirepos"));
		}
		if (obj.has<jsonxx::Array>("displayname"))
		{
			if (obj.get<jsonxx::String>("displayname") != "") {
				setDisplayName(obj.get<jsonxx::String>("displayname"));
			}
		}
	}

	if (obj.has<jsonxx::Boolean>("iskinned"))
	{
		setSkinned(obj.get<jsonxx::Boolean>("iskinned"));
	}

	if (obj.has<jsonxx::Number>("presetposkey"))
	{
		m_MobPresetPosKey = obj.get<jsonxx::Number>("presetposkey");
	}

    // 重置剥皮进行中状态
    m_isSkinning = false;
    m_skinningElapsedTime = 0.0f;
    m_skinningPlayerID = 0;	

	//家园地图定制的生物类型
	applyDisplayHomeBillBoard();
	return true;
}

int ClientMob::LoadFromMobPB(const game::common::PB_ActorMob& actorMob)
{
	m_isFirstCreate = false;
	if (!init(actorMob.defid()))
	{
		return -1;
	}
	if (getAttrib() == NULL) return false;
	m_RandomScale = actorMob.scale() / 1000.0;
	setCustomScale(actorMob.bodyscale() / 1000.0);
	setScale(actorMob.scale() / 1000.0 * m_Def->ModelScale);


	const PB_ActorCommon& actorCommon = actorMob.basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;
	getAttrib()->initHP(actorMob.hp() / 1000.0);
	if (actorMob.has_maxhp())
	{
		getAttrib()->initMaxHP(actorMob.maxhp() / 1000.0);
	}

	//	if (version == 0) m_Attrib->initHP(m_Attrib->getHP()*5.0f);

	setTamedOwnerID(actorMob.owner());

	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
	if (attrib != NULL)
	{
		LoadPBActorBuffs(actorMob, attrib);
		LoadPBActorMods(actorMob, attrib);
		BackPackGrid itemgrid;
		for (size_t i = 0; i < actorMob.equips_size(); ++i)
		{
			itemgrid.loadPB(actorMob.equips(i));
			if (attrib == NULL)
				continue;
			attrib->equip((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
		}
	}
	m_Color = actorMob.color();
	if (m_Color >= 0 || getSheared())
	{
		m_Body->setBodyColor(m_Color, getSheared());
	}
	setGrowingAge(actorMob.growage());
	setGrowingTime(actorMob.growtime());
	setGrowingDValue(actorMob.growdvalue());
	if (m_Def->ParentID > 0 && getGrowingAge() == 0)
	{
		//m_GrowingAge = -24000;
		setGrowingAge(-24000);
	}
	setLoveCD(6000);
	m_DieTicks = actorMob.dieticks();
	if (attrib)
	{
		BackPackGrid itemgrid;
		for (size_t i = 0; i < actorMob.bags_size(); ++i)
		{
			itemgrid.loadPB(actorMob.bags(i));
			if (attrib == NULL)
				continue;
			attrib->setBagItem(itemgrid.getIndex(), &itemgrid);
		}
	}
	if (attrib)
		attrib->setFood(actorMob.food() / 1000.0);
	const PB_Vector3& tmpBonFirePos = actorMob.bonfirepos();
	m_BonFirePos.x = tmpBonFirePos.x();
	m_BonFirePos.y = tmpBonFirePos.y();
	m_BonFirePos.z = tmpBonFirePos.z();

	if (actorMob.has_displayname())
	{
		setDisplayName(actorMob.displayname());
	}
	{
		if (actorMob.has_climbing())
		{
			setClimbing(true);
		}
		else
		{
			setClimbing(false);
		}
	}
	m_ServerWid = actorMob.serverid();
	if (actorMob.has_eaten())
	{
		setEaten(true);
	}
	else
	{
		setEaten(false);
	}

	if (actorMob.has_rideactorid())
	{
		auto RidComp = sureRiddenComponent();
		if (RidComp)
		{
			RidComp->setRidingActorObjId(actorMob.rideactorid());
		}
	}

	setCurNeedFeedNum(actorMob.needeat());
	//家园地图定制的生物类型
	applyDisplayHomeBillBoard();

	if (actorMob.has_componentdata())
	{
		loadComponentSaveData(actorMob.componentdata());
	}

	const std::string* modelcomponent = nullptr;
	if (actorMob.has_modelcomponent())
	{
		modelcomponent = &actorMob.modelcomponent();
	}
	LoadFromPBChildAndComponent(modelcomponent);
	ActorLocoMotion* loc =  getLocoMotion();
	if (loc)
	{
		LivingLocoMotion* locc = loc->ToCast<LivingLocoMotion>();
		if (locc)
		{
			if (actorMob.has_loctype())
			{
				locc->initMoveAbility(static_cast<MoveAbilityType>(actorMob.loctype()));
			}
		}
	}

	return 0;
}

int ClientMob::LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb)
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->LoadFromPB(pb);
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->LoadFromPB(pb);
	}

	const PB_ActorMob& actorMob = pb.actormob();
	return LoadFromMobPB(actorMob);
}

bool ClientMob::supportSaveToPB()
{
	return true;
}

int ClientMob::saveToMobPB(game::common::PB_ActorMob* actorMob)
{
	PB_ActorCommon* actorCommon = actorMob->mutable_basedata();
	savePBActorCommon(actorCommon);

	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
	SavePBActorBuffs(actorMob, attrib);
	SavePBActorMods(actorMob, attrib);

	for (int i = 0; i < MAX_EQUIP_SLOTS; i++)
	{
		BackPackGrid* itemgrid = attrib->getEquipGrid((EQUIP_SLOT_TYPE)i);
		if (itemgrid == NULL || itemgrid->isEmpty()) continue;
		PB_ItemIndexGrid* equip = actorMob->add_equips();
		itemgrid->savePB(equip);
	}

	auto bagsContainer = attrib->getBags();
	if (bagsContainer)
	{
		for (int i = 0; i < MAX_BAGS_GRID; i++)
		{
			BackPackGrid* itemgrid = bagsContainer->index2Grid(i);
			if (itemgrid == NULL || itemgrid->isEmpty()) continue;
			PB_ItemIndexGrid* equip = actorMob->add_equips();
			itemgrid->savePB(equip);
		}
	}

	// todo: 血量溢出先处理为uint32最大值,后续修改协议类型
	float hp = getAttrib()->getHP() * 1000;
	float maxhp = getAttrib()->getMaxHP() * 1000;
	unsigned int nHp = static_cast<unsigned int>(hp);
	unsigned int nMaxHp = static_cast<unsigned int>(maxhp);
#ifdef IWORLD_SERVER_BUILD
	if (hp >= UINT32_MAX)
		nHp = UINT32_MAX;
	if (maxhp >= UINT32_MAX)
		nMaxHp = UINT32_MAX;
#endif

	actorMob->set_defid(m_Def->ID);
	actorMob->set_hp(nHp);
	actorMob->set_maxhp(nMaxHp);
	actorMob->set_owner(getTamedOwnerID());
	actorMob->set_color(m_Color);
	actorMob->set_growage(getGrowingAge());
	actorMob->set_scale(m_RandomScale * 1000);
	actorMob->set_dieticks(m_DieTicks);
	actorMob->set_food(getMobAttrib()->getFood() * 1000);
	actorMob->set_bodyscale(getCustomScale() * 1000);
	actorMob->set_growdvalue(getGrowingDValue());
	actorMob->set_growtime(getGrowingTime());
	PB_Vector3* tmpBonFirePos = actorMob->mutable_bonfirepos();
	tmpBonFirePos->set_x(m_BonFirePos.x);
	tmpBonFirePos->set_y(m_BonFirePos.y);
	tmpBonFirePos->set_z(m_BonFirePos.z);
	actorMob->set_animwaketicks(m_iTickAnimAwake);
	if (m_displayName != "")
	{
		actorMob->set_displayname(m_displayName);
	}
	if (getClimbing())
		actorMob->set_climbing(getClimbing());
	actorMob->set_serverid(m_ServerWid);
	if (isEaten())
		actorMob->set_eaten(true);
	actorMob->set_needeat(getCurNeedFeedNum());

	actorMob->set_componentdata(createComponentSaveData());
	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		actorMob->set_rideactorid(RidComp->getRidingActorObjId());
	}

	std::string modelcomponent;
	SaveToPBChildAndComponent(modelcomponent);
	if (!modelcomponent.empty())
	{
		actorMob->set_modelcomponent(modelcomponent);
	}
	
	ActorLocoMotion*loc =  getLocoMotion();
	if (loc)
	{
		LivingLocoMotion* locc = loc->ToCast<LivingLocoMotion>();
		if (locc)
		{
			actorMob->set_loctype(locc->getLocoMotionType());
		}
	}
	return 0;
}

int ClientMob::saveToPB(game::hc::PB_GeneralEnterAOIHC* pb)
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->saveToPB(pb);
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->saveToPB(pb);
	}

	PB_ActorMob* actorMob = pb->mutable_actormob();
	return saveToMobPB(actorMob);
}
flatbuffers::Offset<FBSave::SectionActor> ClientMob::save(SAVE_BUFFER_BUILDER& builder)
{
	auto mob = saveMob(builder);

	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->save(builder, mob);
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->save(builder, mob);
	}

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorMob, mob.Union());
}

bool ClientMob::load(const void* srcdata, int version)
{
	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		return pClientFlyComponent->load(srcdata, version);
	}

	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		return pClientAquaticComponent->load(srcdata, version);
	}

	return todoLoad(srcdata, version);
}

bool ClientMob::todoLoad(const void* srcdata, int version)
{
	m_isFirstCreate = false;
	auto src = reinterpret_cast<const FBSave::ActorMob*>(srcdata);
	if (!src || !init(src->defid()))
	{
		return false;
	}

	if (getAttrib() == NULL) return false;
	//m_Body->setScale(src->scale() * m_Def->ModelScale);	setCustomScale(src->bodyscale());
	m_RandomScale = src->scale();
	setCustomScale(src->bodyscale());
	setScale(src->scale() * m_Def->ModelScale);

	loadActorCommon(src->basedata());

	getAttrib()->initHP(src->hp());
	if (version == 0) getAttrib()->initHP(getAttrib()->getHP() * 5.0f);

	setTamedOwnerID(src->owner());

	MobAttrib* attrib = dynamic_cast<MobAttrib*>(getAttrib());
	if (attrib != NULL) {
		LoadActorBuffs(src, attrib);
		LoadActorMods(src, attrib);

		auto equips = src->equips();
		if (equips)
		{
			if (m_pGridContainer)
			{
				BackPackGrid itemgrid;
				for (size_t i = 0; i < equips->size(); i++)
				{
					itemgrid.load(equips->Get(i));
					if (attrib == NULL) continue;
					m_pGridContainer->setGridInfo((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
				}
			}
			else
			{

				BackPackGrid itemgrid;
				for (size_t i = 0; i < equips->size(); i++)
				{
					itemgrid.load(equips->Get(i));
					if (attrib == NULL) continue;
					attrib->equip((EQUIP_SLOT_TYPE)itemgrid.getIndex(), &itemgrid);
				}
			}
		}	
	}
	m_Color = src->color();
	if (m_Color >= 0 || getSheared())
	{
		getBody()->setBodyColor(m_Color, getSheared());
	}

	//m_GrowingAge = src->growage();
	setGrowingAge(src->growage());
	setGrowingTime(src->growtime());
	setGrowingDValue(src->growdvalue());
	if (m_Def->ParentID > 0 && getGrowingAge() == 0)
	{
		//m_GrowingAge = -24000;
		setGrowingAge(-24000);
	}
	setLoveCD(6000);
	m_DieTicks = src->dieticks();

	if (attrib)
	{
		auto bags = src->bags();
		if (bags)
		{
			if (m_pGridContainer)
			{
				BackPackGrid itemgrid;
				for (size_t i = 0; i < bags->size(); i++)
				{
					itemgrid.load(bags->Get(i));
					m_pGridContainer->setGridInfo(itemgrid.getIndex(), &itemgrid);
				}
			}
			else
			{
				BackPackGrid itemgrid;
				for (size_t i = 0; i < bags->size(); i++)
				{
					itemgrid.load(bags->Get(i));

					attrib->setBagItem(itemgrid.getIndex(), &itemgrid);
				}
			}
			
		}

		attrib->setFood(src->food());
	}

	if (src->bonfirepos())
	{
		m_BonFirePos = Coord3ToWCoord(src->bonfirepos());
	}

	m_iTickAnimAwake = src->animwaketicks();

	if (src->displayname())
	{
		if (src->displayname()->str() != "") {
			setDisplayName(src->displayname()->str());
		}
	}
	//m_Climbing = src->climbing();
	setClimbing(src->climbing());

	if (src->serverid())
	{
		m_ServerWid = src->serverid()->str();
	}

	//m_bEaten = src->eaten();
	setEaten(src->eaten());
	//m_iCurNeedFeedNum = src->needeat();
	setCurNeedFeedNum(src->needeat());
	//家园地图定制的生物类型
	applyDisplayHomeBillBoard();

	if (src->componentData() && src->componentData()->str() != "")
	{
		loadComponentSaveData(src->componentData()->str());
	}

	//只有monsterdef获取成功，才加载component，否则不加载
	if (m_Def && m_Def->ID == m_monsterId)
	{
		LoadObjectChildAndComponent(src->scriptcomponent(), src->ugccomponents(), src->children());
	}
	
	if (src->teamid())
	{
		setTeam(src->teamid());
	}
	if (src->bb() && src->bb()->str() != "")
	{
		if (m_bb)
		{
			m_bb->InitByString(src->bb()->str());
		}
	}

	if (src->extendattr())
	{
		LoadMobTriggerAttr(src->extendattr());
	}

	if (src->presetposkey() > 0)
	{
		m_MobPresetPosKey = src->presetposkey();
	}

	return true;
}

/*
void ClientMob::changeData(MONSTER *pMon)
{
	ANIMAL &animal = pMon->Animal;
	animal.WID = getWorldID();
	animal.ID = getDef()->ID;
	animal.Pos.map = getCurMapID();
	WCoord pos = getLocoMotion()->getPosition();
	storePos(&animal.Pos, &pos);
	storeDir(&animal.Dir, getLocoMotion());
	animal.HP = getAttrib()->getHP();
	storeBuff(&animal.Buff, getLivingAttrib());
	pMon->Misc.TamedOwnerUin = getTamedOwnerID();
	pMon->Misc.SpecialFlag = getSpecialFlag();
	pMon->Misc.CollarColor = getCollarColor();
	pMon->Misc.Color = getColor();
	pMon->Misc.FallDistance = getLocoMotion()->m_FallDistance;
}

void ClientMob::loadData(MONSTER *pMon)
{
	ANIMAL &animal = pMon->Animal;

	m_WID = animal.WID;
	getAttrib()->m_Life = animal.HP;
	restoreDir(&animal.Dir, getLocoMotion());
	restoreBuff(&animal.Buff, getLivingAttrib());
	m_TamedOwnerUin = pMon->Misc.TamedOwnerUin;
	setSpecialFlag(pMon->Misc.SpecialFlag);
	setColor(pMon->Misc.Color);
	setCollarColor(pMon->Misc.CollarColor);

	WCoord pos;
	restorePos(&animal.Pos, &pos);
	getLocoMotion()->gotoPosition(pos, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	getLocoMotion()->m_FallDistance = pMon->Misc.FallDistance;
}*/

void ClientMob::ModBeHitTick()
{
	m_ModExplodeBeHitActTick--;
	if (m_ModExplodeBeHitActTick < 0)
	{
		ModBeHitConf* beHitConf = ModPackMgr::GetInstancePtr()->GetModBeHitConf();
		if (!beHitConf)
		{
			m_ModExplodeBeHitState = 0;
			if (m_btree)
			{
				m_btree->SetActive(true);
			}
			setAIActive(true);
		}
		else
		{
			if (m_ModExplodeBeHitState == 1)
			{
				m_ModExplodeBeHitState = 2;
				m_ModExplodeBeHitActTick = beHitConf->m_actUpTime / 0.05f;
				playAnimById(beHitConf->m_explosionActUp);
			}
			else
			{
				m_ModExplodeBeHitState = 0;
				if (m_btree)
				{
					m_btree->SetActive(true);
				}
				setAIActive(true);
			}
		}
	}
}

void ClientMob::MobTick()
{
	OPTICK_EVENT();
	if (m_pWorld == NULL) return;
	if (m_pWorld->isRemoteMode()) return;

	if (m_ModExplodeBeHitState) //处理僵尸被爆炸击倒爬起
	{
		ModBeHitTick();
	}

	//由于生物刚出来状态并没变会导致寻路有问题，等待5个tick后再运行btree
	if (m_needClearBtree && GetWorldManagerPtr())
	{
		if (m_needClearBb)// 行为树AI
			static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestoryBlackboard(m_needClearBb);
		static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestroyBTree(m_needClearBtree);
	}
	m_needClearBtree = nullptr;
	m_needClearBb = nullptr;

	//SANDBOXPROFILING_FUNC("ClientMob::MobTick");
	if(m_NeedUpdateAI)
		AITick();//将AI相关Tick逻辑单独拿出来

	suddenIllnessBuffAttack();

	//SANDBOXPROFILING_FUNC("ClientMob_MobTick_02");
	if (isDead())
	{
		if (m_isUseAILua)
		{
			GetCoreLuaDirector().CallFunctionM("AILuaManager", "clearAllRunningTasks", "i", m_AILuaKey);
		}
		else
		{
			if (m_AITask)
			{
				m_AITask->clearAllRunningTasks();
				ENG_DELETE(m_AITask);
			}
			if (m_AITaskTarget)
			{
				m_AITaskTarget->clearAllRunningTasks();
				ENG_DELETE(m_AITaskTarget);
			}
		}
	}

	bool needUpdateFace = true;
	//如果成为坐骑，也不能转头看目标（如果目标是角色），要看向角色的目标
	if (g_pPlayerCtrl && g_pPlayerCtrl->getBody())
	{
		auto RidComp = g_pPlayerCtrl->getRiddenComponent();
		if (RidComp && RidComp->getRidingActor() == this)
		{
			needUpdateFace = false;

			//朝向和角色朝向一致
			if (getBody())
			{
				getBody()->setLookTargetYaw(g_pPlayerCtrl->getBody()->getLookTargetPitch());
				getBody()->setLookTargetPitch(g_pPlayerCtrl->getBody()->getLookTargetPitch());
			}
		}
	}

	if (getDefID() == 3825)
	{
		ActorSandworm* sandworm = dynamic_cast<ActorSandworm*>(this);
		if (sandworm && (sandworm->getAIState() == AI_ATTACK_NIBBLE || sandworm->getAIState() == AI_AUTO_DIE || sandworm->getAIState() == AI_ATTACK_BITE || sandworm->getAIState() == AI_AUTO_LEAVE || sandworm->getAIState() == AI_LEAVE_OASIS))
		{
			needUpdateFace = false;
		}
	}
	if (getDefID() == 3226 || getDefID() == 3227 || getDefID() == 3272 || getDefID() == 3273)
	{
		needUpdateFace = false;
	}
	if (getDefID() == 3416 || getDefID() == 3417)//对熊猫做特殊处理 2秒内被打两次直接消失
	{
		if (m_pandaBeenAttacked > 0 && getDefID() == 3416)
		{
			m_pandaBeenAttackedTick = m_pandaBeenAttackedTick + 1;
			if (m_pandaBeenAttackedTick > 40)
			{
				m_pandaBeenAttackedTick = 0;
				m_pandaBeenAttacked = 0;
			}
		}
		if (m_pandaRunAwayTick > 0)//熊猫跑开消失
		{
			m_pandaRunAwayTick = m_pandaRunAwayTick - 1;
			if (getLocoMotion() && m_pandaRunAwayTick >= 59)
			{
				WCoord PandaPos = getLocoMotion()->getPosition();
				int PposX = 1000;
				int PposZ = 1000;
				if (getActorMgr())
				{
					auto actor = getActorMgr()->selectNearPlayerDefault(PandaPos, 1000);
					WCoord actorPos = WCoord(0, -1, 0);
					if (actor)
					{
						actorPos = actor->getPosition();
					}
					if (actorPos.x - PandaPos.x > 0)
					{
						PposX *= -1;
					}
					if (actorPos.z - PandaPos.z > 0)
					{
						PposZ *= -1;
					}
				}
				if (getNavigator())
				{
					getNavigator()->tryMoveTo(PandaPos.x + PposX, PandaPos.y, PandaPos.z + PposZ, 2);
				}
			}
			if (getBody()->getNowPlaySeqID() != 100101)
			{
				playAnimById(100101);
			}
			if (m_pandaRunAwayTick == 0)
			{
				setNeedClear();
			}
		}
		else
		{
			m_pandaRunAwayTick = 0;
		}

	}
	if (needUpdateFace)
	{
		auto targetComponent = getToAttackTargetComponent();
		if (targetComponent)
		{
			float limitAngle = getRotaionLimitAngle();
			targetComponent->updateFaceToTarget(limitAngle, 30.0f);
		}
	}

	if (!isDead() && !needClear() && m_CanPickUpLoot)
	{
		pickUpLoot();
	}

	if (!isDead() && !needClear() && GenRandomInt(6000) < m_SaySoundTimer++) //1000
	{
		m_SaySoundTimer -= 120 * 6;

		//排除商人
		if (m_Def && (m_Def->ID < 3010 || m_Def->ID > 3016))
		{
			auto funcWrapper = getFuncWrapper();
			bool isRole = funcWrapper ? funcWrapper->isMinicodeRole() : false;
			if (!isRole)
			{
				playSaySound();
			}
		}
	}

	m_DespawnTicks++;
	if (m_pWorld->getCurMapID() < MAPID_MENGYANSTAR && getLocoMotion()->getBrightness() > 0.5f)
	{
		m_DespawnTicks += 2;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return ;
	int dist = actorMgr->minDistToPlayer(getPosition()) / BLOCK_SIZE;
	// 是否执行生物消失逻辑（动物不执行）：这里会判断生物是否远离玩家
	if (canDespawn() || needClearMobByDist.find(getDefID()) != needClearMobByDist.end())
	{
		if (getDefID() != 3021 && getDefID() != 3825)
		{
			if (dist >= MOB_DESPAWN_MAXDIST)
			{
				setNeedClear();
			}
			else if (dist >= MOB_DESPAWN_MINDIST && m_DespawnTicks > 600 && GenRandomInt(800) == 0)
			{
				setNeedClear();
			}
		}
	}
	//时间到了消失
	if (m_DieTicks > 0)
	{
		m_DieTicks--;
		// 如果是可交互尸体，则不清除
		if (!m_DieTicks && getDefID() != 3825 && !getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE))
		{
			setNeedClear();
			if (getAttrib()->getHP() == 1)
			{
				onDie();
			}
		}
	}
	if (dist < MOB_DESPAWN_MINDIST) m_DespawnTicks = 0;

	if (getMobAttrib()->getFood() > 0)
	{
		getMobAttrib()->setFood(getMobAttrib()->getFood() - (getMobAttrib()->getFoodReduce() / 20.0f));
		if (getMobAttrib()->getFood() < 0) getMobAttrib()->setFood(0);
	}

	//饥饿的狼
	if (m_Def->ID == 3407)
	{
		auto def = GetDefManagerProxy()->getParticleDef(1614);
		if (def && getMobAttrib()->getHungryStatus())
		{
			playMotion(def->EffectName.c_str(), 1);
		}
		else if (def && !getMobAttrib()->getHungryStatus())
		{
			stopMotion(def->EffectName.c_str());
		}
	}

	m_scorpionSyncTick++;
	//小蝎子水中
	if (m_Def->ID == 3824 && !isDead())
	{
		if (getLocoMotion()->isInsideNoOxygenBlock() || isInWater())
		{
			auto component = getAttackedComponent();
			if (component)
			{
				component->attackedFromType_Base(ATTACK_DROWN, 5.0f);
			}
		}

		if (getIsHideStatus())
		{
			auto targetComponent = getToAttackTargetComponent();
			if (targetComponent)
			{
				if (targetComponent->hasTarget() || (getMobAttrib() != nullptr && getMobAttrib()->HasBadBuff()))
				{
					char szScriptFun[256];
					snprintf(szScriptFun, sizeof(szScriptFun), "F%d_OnShow", m_Def->ID);
					MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
				}
			}
		}

		if (m_pWorld && !m_pWorld->isRemoteMode())
		{
			if (m_scorpionSyncTick % 20 == 0)
			{
				int index = 0;
				if (getIsHideStatus())
				{
					index = 1;
				}
				jsonxx::Object object;
				char objid_str[128];
				sprintf(objid_str, "%lld", getObjId());
				object << "objid" << objid_str;
				object << "playAnim" << index;
				GetSandBoxManager().sendBroadCast("MOB_SCORPION_HIDE_CHANGE", object.bin(), object.binLen());
			}
		}
	}

	int loveAmbassadorId = 3021;      //ID
	if (m_Def->ID == loveAmbassadorId)
	{
		//是爱心大使
		int spawnTime = 0;	           //出生时间
		int liveTime = 0;			   //存活时间
		int attachmentId = 0;	       //伴随出现的附属品ID,从指定ID列表中随机获取
		int testId = 0;
		char tmpbuf1[64];
		sprintf(tmpbuf1, "F%d_GetInfo", loveAmbassadorId);
		MINIW::ScriptVM::game()->callFunction(tmpbuf1, "i>iiii", testId, &loveAmbassadorId, &spawnTime, &liveTime, &attachmentId);
		liveTime = liveTime * 1000; //转换为世界时间
		int worldTime = GetWorldManagerPtr()->getWorldTime();
		int loveAmbassadorSpawnTime = GetWorldManagerPtr()->getLoveAmbassadorSpawnTime();
		if (worldTime >= loveAmbassadorSpawnTime + liveTime)
		{
			//指定的生存时间后消失
			setNeedClear();
			char tmpbuf2[64];
			char tmpbuf3[64];
			sprintf(tmpbuf2, "F%d_SetCurNeedItemId", loveAmbassadorId);
			sprintf(tmpbuf3, "F%d_SetCurNeedItemCount", loveAmbassadorId);
			MINIW::ScriptVM::game()->callFunction(tmpbuf2, "i", 0);
			MINIW::ScriptVM::game()->callFunction(tmpbuf3, "i", 0);
			GetWorldManagerPtr()->setLoveAmbassadorSpawnTime(0);
		}
	}

	if (m_iTickAnimAwake > 0)
	{
		m_iTickAnimAwake--;
		if (m_iTickAnimAwake == 25)
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("ent.3200.freedom1", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
			}
		}
		else if (m_iTickAnimAwake == 0 && checkIfSavageSleeping())
		{
			transToNewSavage();
		}
	}
	if (m_iEvadeCDTick > 0)
	{
		//躲避cd
		m_iEvadeCDTick--;
	}
	for (std::map<AI_MOTION_TYPE, int>::iterator it = m_RunAICDList.begin(); it != m_RunAICDList.end(); it++)
	{
		if (it->second > 0)
		{
			// 改变方块cd
			it->second--;
		}
	}

	if (m_iStayBlockTime > 0)
	{
		// 停留在花上时间
		m_iStayBlockTime--;
	}
	m_mobtick++;

	// 韧性恢复
	if (!isDead())
	{
		if (getMobAttrib() && getMobAttrib()->needRecoverToughness())
		{
			m_noAttackedTicks++;
			// 100ticks未受到攻击开始恢复韧性
			if (m_noAttackedTicks >= 100)
			{
				if (m_recoverToughnessTicks < 0)
				{
					m_recoverToughnessTicks = 0;
				}
			}
			if (m_recoverToughnessTicks >= 0)
			{
				m_recoverToughnessTicks++;
				// 每秒（20ticks）恢复韧性
				if (m_recoverToughnessTicks >= 20)
				{
					m_recoverToughnessTicks = 0;
					if (getMobAttrib())
					{
						getMobAttrib()->recoverToughness();
					}
				}
			}
		}
	}
}

void ClientMob::tick()
{
    SANDBOXPROFILING_FUNC(OPTICK_FUNC);
    OPTICK_EVENT();
    OPTICK_TAG("id", getDefID());

	LoadBTree();

	auto pClientFlyComponent = getClientFlyComponent();
	if (pClientFlyComponent)
	{
		pClientFlyComponent->tick();

		ClientActor::tickForFlyObject();

		return;
	}
	auto pClientAquaticComponent = getClientAquaticComponent();
	if (pClientAquaticComponent)
	{
		pClientAquaticComponent->tick();
		ClientActor::tickForAquaticObject();
		return;
	}

	if (!getLocoMotion())
	{
		if (m_btree) {
			m_btree->Tick();
		}
		return;
	}
	ActorLiving::tick();
	MobTick();
	mobHPTick();

	if (m_iTickTriggerCount >= 10)
	{
		m_iTickTriggerCount = 0;
		triggerActorAttribChunk();
	}

	m_iTickTriggerCount++;

	if (m_tlMgr)
		m_tlMgr->Update(GetTimeSinceStartup() - m_lastTickTime);

	m_lastTickTime = GetTimeSinceStartup();
}

void ClientMob::aiTick()
{
	OPTICK_EVENT();

	if (m_mobtick > 5 && m_NeedUpdateAI)
	{
		if (m_btree) {
			m_btree->Tick();
		}
	}
}

bool  ClientMob::haveBtree()
{
	if (m_btree)
	{
		return true;
	}
	return false;
}

void ClientMob::update(float dtime)
{
	ActorLiving::update(dtime);

	if (isDead())
	{
		m_corpseRemainTime -= dtime;
		if (m_corpseRemainTime <= 0)
		{
			setNeedClear(true);
		}
	}

	// 更新剥皮进度
    if (m_isSkinning && m_skinningPlayerID != 0)
    {
        ClientPlayer* player = dynamic_cast<ClientPlayer*>(GetWorldManagerPtr()->findActorByWID(m_skinningPlayerID));
        if (player)
        {
            // 检查玩家是否仍然在附近且持有正确的工具
            float distance = getDistanceTo(player);
            if (distance > 400.0f) // 如果玩家距离太远（4格外），取消剥皮
            {
                cancelSkinningClient(player);
            }
            else
            {
				// 如果玩家移动或跳跃，取消剥皮
				// InputInfo* inputInfo = player->getInputInfo();
				// if (inputInfo && (inputInfo->movex != 0 || inputInfo->movey != 0 || inputInfo->movez != 0 || inputInfo->jump))
				// {
				// 	cancelSkinning();
				// }
				// else
				{
					// 正常更新剥皮进度
					updateSkinning(dtime, player);
				}
            }
        }
        else
        {
            // 如果玩家不存在，取消剥皮
            cancelSkinningClient(nullptr);
        }
    }
}

void ClientMob::triggerActorAttribChunk()
{
	ActorAttribut* actorAttribut = m_ActorAttribTrigger;
	ActorAttrib* attrib = getAttrib();
	if (attrib && actorAttribut)
	{
		MobAttrib* mobAct = getMobAttrib();
		LivingAttrib* livAtt = getLivingAttrib();
		if (getAttrib() && getAttrib()->getMaxHP() != actorAttribut->MaxHP)
		{
			actorAttribut->MaxHP = getAttrib()->getMaxHP();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAMAXHP, actorAttribut->MaxHP);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (getAttrib() && getAttrib()->getHP() != actorAttribut->NowHP)
		{
			actorAttribut->NowHP = getAttrib()->getHP();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAACURHP, actorAttribut->NowHP);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (getAttrib() && getAttrib()->getExtraHP() != actorAttribut->ExtraHP)
		{
			actorAttribut->ExtraHP = getAttrib()->getExtraHP();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAEXTRAHP, actorAttribut->ExtraHP);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}
		

		if(getAttrib() && getAttrib()->getHPRecover() != actorAttribut->HPRecover)
		{
			actorAttribut->HPRecover = getAttrib()->getHPRecover();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAHPRECOVER, actorAttribut->HPRecover);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (mobAct && mobAct->getMaxFood() != actorAttribut->MaxHunger)
		{
			actorAttribut->MaxHunger = mobAct->getMaxFood();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAMAXHUNGER, actorAttribut->MaxHunger);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (mobAct && mobAct->getFood() != actorAttribut->NowHunger)
		{
			actorAttribut->NowHunger = (int)mobAct->getFood();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAACURHUNGER, actorAttribut->NowHunger);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (livAtt && livAtt->getMaxOxygen() != actorAttribut->MaxOxygen)
		{
			actorAttribut->MaxOxygen = livAtt->getMaxOxygen();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAMAXOXYGEN, actorAttribut->MaxOxygen);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (livAtt && livAtt->getOxygen() != actorAttribut->NowOxygen)
		{
			actorAttribut->NowOxygen = livAtt->getOxygen();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAACUROXYGEN, actorAttribut->NowOxygen);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		/*if (attrib->getSpeedAtt(Actor_Walk_Speed) == -1 || attrib->getSpeedAtt(Actor_Swim_Speed) == -1 || attrib->getSpeedAtt(Actor_Jump_Speed) == -1)//影响较大，暂时注释
		{
			auto defTmp = this->getMonsterDef();
			if (defTmp && defTmp->Speed >= 0)
			{
				attrib->setSpeedAtt(Actor_Walk_Speed, defTmp->Speed);
				//和策划对了先只应用到行走上 防止出现跳到天上等问题
				attrib->setSpeedAtt(Actor_Swim_Speed, defTmp->Speed);
				attrib->setSpeedAtt(Actor_Jump_Speed, defTmp->Speed);
			}
		}*/

		if (attrib->getSpeedAtt(Actor_Walk_Speed) != actorAttribut->MoveSpeed)
		{
			actorAttribut->MoveSpeed = attrib->getSpeedAtt(Actor_Walk_Speed);

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAWALKSPEED, actorAttribut->MoveSpeed);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (attrib->getSpeedAtt(Actor_Walk_Speed) != actorAttribut->RunSpeed)
		{
			actorAttribut->RunSpeed = attrib->getSpeedAtt(Actor_Walk_Speed);

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAARUNSPEED, actorAttribut->RunSpeed);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (attrib->getSpeedAtt(Actor_Swim_Speed) != actorAttribut->SwimSpeed)
		{
			actorAttribut->SwimSpeed = attrib->getSpeedAtt(Actor_Swim_Speed);

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAASWINSPEED, actorAttribut->SwimSpeed);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (attrib->getSpeedAtt(Actor_Jump_Speed) != actorAttribut->JumpSpeed)
		{
			actorAttribut->JumpSpeed = attrib->getSpeedAtt(Actor_Jump_Speed);

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAJUMPPOWER, actorAttribut->JumpSpeed);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (getMass() != actorAttribut->Weight)
		{
			actorAttribut->Weight = (float)getMass();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAWEIGHT, actorAttribut->Weight);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		if (getAiInvulnerableProb() != actorAttribut->Dodge)
		{
			actorAttribut->Dodge = (float)getAiInvulnerableProb();

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAADODGE, actorAttribut->Dodge);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		float narmor = livAtt->getAttackBaseLua(ATTACK_PUNCH);
		if (narmor != actorAttribut->NearArmor)
		{
			actorAttribut->NearArmor = narmor;

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAATKMELEE, actorAttribut->NearArmor);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		float rattack = livAtt->getAttackBaseLua(ATTACK_RANGE);
		if (rattack != actorAttribut->RemoteAttack)
		{
			actorAttribut->RemoteAttack = rattack;

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAAATKREMOTE, actorAttribut->RemoteAttack);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		float parmor = livAtt->getArmorBaseLua(ATTACK_PUNCH);
		if (narmor != actorAttribut->NearArmor)
		{
			actorAttribut->NearArmor = parmor;

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAADEFMELEE, actorAttribut->NearArmor);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}

		float rarmor = livAtt->getArmorBaseLua(ATTACK_RANGE);
		if (rarmor != actorAttribut->RemoteArmor)
		{
			actorAttribut->RemoteArmor = rarmor;

			// 观察者事件接口
			ObserverEvent obevent;
			obevent.SetData_EventObj(getObjId());
			obevent.SetData_ActorAttr(OBAADEFREMOTE, actorAttribut->RemoteArmor);
			obevent.SetData_Actor(this->getDefID());
			GetObserverEventManager().OnTriggerEvent("Actor.ChangeAttr", &obevent);
		}
	}
}

//void ClientMob::mobAdult()
//{
//	if(!m_Def) return;
//	if(m_Def && m_Def->ParentID == 0) return;
//	if(IsMobHorse(m_Def->ParentID))
//	{
//		ClientMob *newmob = ClientMob::createFromDef(m_Def->ParentID);
//		newmob->setTamedOwnerUin(m_TamedOwnerUin);
//		newmob->getLocoMotion()->gotoPosition(getLocoMotion()->m_Position, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
//		m_pWorld->getActorMgr()->spawnActor(newmob);
//
//		setNeedClear();
//		return;
//	}
//
//	World *pworld = m_pWorld;
//	leaveWorld(false);
//
//	m_Def = GetDefManagerProxy()->getMonsterDef(m_Def->ParentID);
//
//	m_RandomScale = m_Def->MinRandomScale + (1 - m_Def->MinRandomScale)*GenRandomFloat();
//	initMobBody(getBody(), m_Def);
//	updateModelName();
//	//getBody()->setScale(m_Def->ModelScale * m_RandomScale);
//	setScale(m_Def->ModelScale * m_RandomScale);
//#ifndef _UPDATE_BOUND_BY_SCALE_
//	getLocoMotion()->setBound(m_Def->Height*m_Def->ModelScale, m_Def->Width*m_Def->ModelScale);
//#endif
//
//	float oldLife = getAttrib()->m_Life;
//	getMobAttrib()->init(m_Def);
//	getAttrib()->m_Life = oldLife;
//	
//	enterWorld(pworld);
//}

void ClientMob::playInteractionEffect()
{
	WCoord pos = getLocoMotion()->getPosition();
	auto effectComponent = getEffectComponent();
	if (effectComponent)
	{
		effectComponent->playBodyEffect(BODYFX_INTERACTION);
	}
}

void ClientMob::playTameEffect(bool flag)
{
	WCoord pos = getLocoMotion()->getPosition();

	if (flag)
	{
		//m_pWorld->getEffectMgr()->playParticleEffect("particles/34072.ent", pos, 40);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_TAME_SUCCEED);
		}
		m_pWorld->getEffectMgr()->playSoundAtActor(this, "ui.info.tame_success", 1.0f, 1.0f);

	}
	else
	{
		//m_pWorld->getEffectMgr()->playParticleEffect("particles/34071.ent", pos, 40);
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_TAME_FAILED);
		}
		m_pWorld->getEffectMgr()->playSoundAtActor(this, "ui.info.tame_failed", 1.0f, 1.0f);
	}
}

//void ClientMob::addOpenDialogueUIN(int uin)
//{
//	if (std::find(m_OpenDialogueUINs.begin(), m_OpenDialogueUINs.end(), uin) == m_OpenDialogueUINs.end())
//	{
//		m_OpenDialogueUINs.push_back(uin);
//		if(m_OpenDialogueUINs.size() == 1)
//			playBodyEffect("NPC_talk");
//
//		if (m_Def)
//		{
//			int plotNum = GetDefManagerProxy()->getNpcPlotDefNum();
//			std::vector<int> &npcPlotIds = GetDefManagerProxy()->getNpcPlotIds();
//			for (int i = 0; i < plotNum; i++)
//			{
//				auto def = GetDefManagerProxy()->getNpcPlotDef(npcPlotIds[i]);
//				if (def && def->InteractID == m_Def->ID)
//				{
//					ObserverEvent obevent;
//					obevent.SetData_EventObj(uin);
//					obevent.SetData_ToObj(getObjId());
//					obevent.SetData_Plot(def->ID);
//					obevent.SetData_TargetActorID(getDefID());
//					GetObserverEventManager().OnTriggerEvent("Plot.begin", &obevent);
//					break;
//				}
//			}
//		}
//	}
//}
//
//void ClientMob::removeOpenDialogueUIN(int uin)
//{
//	m_OpenDialogueUINs.erase(std::remove(m_OpenDialogueUINs.begin(), m_OpenDialogueUINs.end(), uin), m_OpenDialogueUINs.end());
//	if(m_OpenDialogueUINs.size() <= 0)
//		stopBodyEffect("NPC_talk");
//
//	if (m_Def)
//	{
//		int plotNum = GetDefManagerProxy()->getNpcPlotDefNum();
//		std::vector<int> &npcPlotIds = GetDefManagerProxy()->getNpcPlotIds();
//		for (int i = 0; i < plotNum; i++)
//		{
//			auto def = GetDefManagerProxy()->getNpcPlotDef(npcPlotIds[i]);
//			if (def && def->InteractID == m_Def->ID)
//			{
//				ObserverEvent obevent;
//				obevent.SetData_EventObj(uin);
//				obevent.SetData_ToObj(getObjId());
//				obevent.SetData_Plot(def->ID);
//				obevent.SetData_TargetActorID(getDefID());
//				GetObserverEventManager().OnTriggerEvent("Plot.end", &obevent);
//				break;
//			}
//		}
//	}
//}

inline bool CanSoundBeReversed(const MonsterDef* def)
{
	return (def->ID == 3101 || def->ID == 3105);
}
extern float getMusicPitch(ClientMob* actor);
void ClientMob::playHurtSound()
{
	if (!m_Def)
	{
		return;
	}
	if (m_Def->ID == 3424)
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->HurtSound.c_str(), 1.0f, getMusicPitch(this));
		}
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_3424_scream");
		}
		return;
	}
	else
	{
		if (!m_Def->HurtSound.empty())
		{
			float pitch = getSoundPitch();
			if (CanSoundBeReversed(m_Def)) pitch = -pitch;
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound(m_Def->HurtSound.c_str(), getSoundVolume(), pitch);
			}
		}
		else ActorLiving::playHurtSound();
	}
}

void ClientMob::playDeathSound()
{
	if (m_Def && !m_Def->DeathSound.empty())
	{
		float pitch = getSoundPitch();
		if (CanSoundBeReversed(m_Def)) pitch = -pitch;
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->DeathSound.c_str(), getSoundVolume(), pitch);
		}
	}
	else ActorLiving::playDeathSound();
}

void ClientMob::playSaySound()
{
	if (getObjType() == OBJ_TYPE_VILLAGER && isSleeping())
		return;

	if (m_Def) {
#if defined(ANDROID) || defined(__ANDROID__)
		//WarningStringMsg(" ClientMob::playSaySound1:%d", m_Def->ID);
#endif //
	}

	if (m_Def)
	{
		//FixedString  strSaySound(m_SaySound.c_str());
		if (!m_SaySound.empty() && !checkIfSavageSleeping()) {
#if defined(ANDROID) || defined(__ANDROID__)
			//WarningStringMsg(" ClientMob::playSaySound2:%s", m_Def->SaySound.c_str());
#endif //	
			float pitch = getSoundPitch();
			if (CanSoundBeReversed(m_Def)) pitch = -pitch;
			auto sound = getSoundComponent();
			if (sound && !m_Def->SaySound.empty())
			{
				sound->playSound(m_SaySound.c_str(), getSoundVolume(), pitch);
			}
		}
	}
}

void ClientMob::playAttackSound()
{
	if (m_Def && !m_Def->AttackSound.empty())
	{
		float pitch = getSoundPitch();
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->AttackSound.c_str(), getSoundVolume(), pitch);
		}
	}
}

void ClientMob::playStepSound()
{
	//StepSound std::string
	if (m_Def && !m_Def->StepSound.empty())
	{
		auto sound = getSoundComponent();
		if (sound)
		{
			sound->playSound(m_Def->StepSound.c_str(), getSoundVolume(), getSoundPitch());
		}
	}
	else
	{
		ActorLiving::playStepSound();
	}
}

/*
void ClientMob::tryGrow()
{
	if(!isAdult() && (rand()%100) < 35)
	{
		m_GrowStage++;
		if(isAdult()) getBody()->initMonster(m_Def->Model);
	}
}*/


MobAttrib* ClientMob::getMobAttrib()
{
	return static_cast<MobAttrib*>(getAttrib());
}

//bool ClientMob::interactPlot(ClientPlayer *player)
//{
//	if (!m_pWorld->isRemoteMode() && (checkInteractPlot(player) || m_bDShopDisplay))
//	{
//		faceWorldPos(player->getPosition() + WCoord(0, BLOCK_SIZE / 2, 0), 180.0f, 180.0f);
//		return player->openPlotDialogue(this);
//	}
//
//	return false;
//}
//
//bool ClientMob::checkInteractPlot(ClientPlayer *player)
//{
//	if (m_AITask && !m_AITask->canInterruptedByInteract())
//		return false;
//
//	if (!player)
//		return false;
//
//	int interactNum = 0;
//	if (!m_bInitInteractFunc)
//	{
//		m_bInitInteractFunc = true;
//		//剧情互动
//		int plotNum = GetDefManagerProxy()->getNpcPlotDefNum();
//		for (int i = 0; i<plotNum; i++)
//		{
//			std::vector<int> &npcPlotIds = GetDefManagerProxy()->getNpcPlotIds();
//			auto def = GetDefManagerProxy()->getNpcPlotDef(npcPlotIds[i]);
//			if (def && def->InteractID == m_Def->ID)
//			{
//				InteractFuncDef interactFunc;
//				interactFunc.InteractionType = 1;
//				interactFunc.ID = def->ID;
//				interactFunc.Show = true;
//				m_InteractFuncs.push_back(interactFunc);
//			}
//		}
//
//		//任务互动
//		int taskNum = GetDefManagerProxy()->getNpcTaskDefNum();
//		for (int i = 0; i<taskNum; i++)
//		{	
//			std::vector<int> &npcTaskIds = GetDefManagerProxy()->getNpcTaskIds();
//			auto def = GetDefManagerProxy()->getNpcTaskDef(npcTaskIds[i]);
//			if (def && def->IsDeliver && def->InteractID == m_Def->ID)
//			{
//				InteractFuncDef interactFunc;
//				interactFunc.InteractionType = 2;
//				interactFunc.ID = def->ID;
//				interactFunc.Show = true;
//				m_InteractFuncs.push_back(interactFunc);
//			}
//		}
//
//		//NPC商店
//		int shopNum = GetDefManagerProxy()->getNpcShopDefNum();
//		for (int i = 0; i < shopNum; i++)
//		{
//			auto def = GetDefManagerProxy()->getNpcShopDef(GetDefManagerProxy()->getNpcShopIds()[i]);
//			if (def && GetDefManagerProxy()->getNpcShopNpcInnerId(def->sInnerKey, def->iShopID / 100, def->iShopID / 100) == m_Def->ID)
//			{
//				InteractFuncDef interactFunc;
//				interactFunc.InteractionType = 4;
//				interactFunc.ID = def->iShopID;
//				interactFunc.Show = true;
//				m_InteractFuncs.push_back(interactFunc);
//			}
//		}
//	}
//
//
//	if (m_InteractFuncs.size() > 0)
//	{
//		for (int i = 0; i < (int)m_InteractFuncs.size(); i++)
//		{
//			if (player->canShowInteract(m_InteractFuncs[i].InteractionType, m_InteractFuncs[i].ID))
//			{
//				m_InteractFuncs[i].Show = true;
//				interactNum++;
//			}
//			else
//				m_InteractFuncs[i].Show = false;
//				
//		}
//	}
//
//	return interactNum > 0;
//}
//
//void ClientMob::getInteractData(RepeatedPtrField<PB_IntertactData>* pdatas)
//{
//	if (pdatas)
//	{
//		for (int i = 0; i < (int)m_InteractFuncs.size(); i++)
//		{
//			PB_IntertactData *pinteract = pdatas->Add();
//
//			pinteract->set_type(m_InteractFuncs[i].InteractionType);
//			pinteract->set_id(m_InteractFuncs[i].ID);
//			pinteract->set_show(m_InteractFuncs[i].Show ? 1 : 0);
//		}
//	}
//}
//
//void ClientMob::resetInteractData(const RepeatedPtrField<PB_IntertactData>* pdatas)
//{
//	m_InteractFuncs.clear();
//	for (int i = 0; i < pdatas->size(); i++)
//	{
//		const PB_IntertactData &src = pdatas->Get(i);
//
//		InteractFuncDef interactFuncDef;
//		interactFuncDef.InteractionType = src.type();
//		interactFuncDef.ID = src.id();
//		interactFuncDef.Show = src.show() > 0;
//
//		m_InteractFuncs.push_back(interactFuncDef);
//	}
//}

void ClientMob::setSheared(bool flag)
{
	if (flag != getSheared())
	{
		setFlagBit(ACTORFLAG_SHEARED, flag);
		getBody()->setBodyColor(m_Color, getSheared());

		notifyBodyChange();
	}
}
void ClientMob::setBodyColorAndNotify(unsigned int color)
{
	m_Color = color;
	m_Body->setBodyColor(color, getSheared());
	notifyBodyChange();
}
void ClientMob::setColor(int color)
{
	if (m_Color != color)
	{
		getBody()->setBodyColor(color, getSheared());
		m_Color = color;

		notifyBodyChange();
	}
}

void ClientMob::setColorEx(unsigned int color)
{
	if (m_Color != color)
	{
		getBody()->setBodyColor(color, getSheared());
		m_Color = color;

		notifyBodyChange();
	}
}

void ClientMob::updateBound()
{
#ifdef _UPDATE_BOUND_BY_SCALE_
	if (m_Def && getLocoMotion())
	{
		float scale = m_Def->ModelScale * getCustomScale();

		getLocoMotion()->setBound((int)(m_Def->Height * scale), (int)(m_Def->Width * scale));
		getLocoMotion()->setAttackBound(
			(int)(m_Def->HitHeight * scale),
			(int)(m_Def->HitWidth * scale),
			(int)(m_Def->HitThickness * scale));
		if (m_Def->CollideBoxs.size() > 0)
		{
			getLocoMotion()->setBaseCollideBoxs(m_Def->CollideBoxs, m_Def->CollideBoxs);
		}
	}
#endif
}

void ClientMob::notifyBodyChange()
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_MobBodyChangeHC mobBodyChangeHC;
		mobBodyChangeHC.set_objid(getObjId());
		mobBodyChangeHC.set_bodycolor(getColor());
		mobBodyChangeHC.set_sheared(getSheared() ? 1 : 0);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_MOB_BODY_CHANGE_HC, mobBodyChangeHC, this);
	}
}


void  ClientMob::notifyVillagerChange(int changeType, unsigned int changeValue, std::string otherValue)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_VillagerBodyChangeHC bodyChangeHC;
		bodyChangeHC.set_objid(getObjId());
		bodyChangeHC.set_changetype(changeType);
		bodyChangeHC.set_changevalue(changeValue);
		bodyChangeHC.set_othervalue(otherValue);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VILLAGER_BODY_CHANGE_HC, bodyChangeHC, this);
	}
}
void  ClientMob::notifyVillagerClothChange(std::string name, bool bshow)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_VillagerCloth Villager;
		Villager.set_actorid(this->getObjId());
		Villager.set_bshow(bshow);
		Villager.set_modlename(name);
		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VILLAGER_CLOTH_HC, Villager, this);
	}
}

void ClientMob::setCollarColor(int collarColor)
{
	m_CollarColor = collarColor;
}

void ClientMob::showSkin(const char* skinname, bool show, bool isNeedSync /*= false*/)
{
	if (getBody())
		getBody()->showSkin(skinname, show);
	if (isNeedSync) {
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "partName" << skinname;
		context << "show" << show;
		SandBoxManager::getSingleton().sendBroadCast("PB_MOB_PART_SHOW", context.bin(), context.binLen());
	}
}

void ClientMob::showAllSkins(bool show)
{
	if (getBody())
		getBody()->showAllSkins(show);
}

void ClientMob::playBlockPlaceSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	const char* placesound = def->PlaceSound.c_str();
	if (placesound[0] == 0) placesound = def->DigSound.c_str();
	if (placesound[0] == 0) placesound = "blockd.grass";

	m_pWorld->getEffectMgr()->playSound(centerpos, placesound, GSOUND_PLACE);
}

void ClientMob::playBlockDigSound(int blockid, int x, int y, int z)
{
	const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	WCoord centerpos = BlockCenterCoord(WCoord(x, y, z));

	const char* digsound = def->DigSound.c_str();
	if (digsound[0] == 0) digsound = "blockd.grass";

	m_pWorld->getEffectMgr()->playSound(centerpos, digsound, GSOUND_DESTROY);
}


void ClientMob::applyDisplayName()
{
#ifndef IWORLD_SERVER_BUILD
	if (getBody() && m_Def)
	{
		std::string nameStr = (m_modName == "" ? m_Def->Name.c_str() : m_modName);
		bool nameDisPlay = m_Def->NameDisPlay;
		if (!nameDisPlay && m_displayName != "")
		{
			nameStr = m_displayName;
			nameDisPlay = true;
		}
		getBody()->setDispayName(nameStr.c_str(), 0);

		if (OBJ_TYPE_ACTOR_PET == getObjType())
		{
			if (getTamedOwner())
			{
				char subName[512] = { 0 };
				//sprintf(subName, "#cffffff%s#n%s", getTamedOwner()->getNickname(),GetDefManagerProxy()->getStringDef(41696));
				sprintf(subName, "#c30c22c<%s>#n", getTamedOwner()->getNickname());
				getBody()->getNameDispObj()->setDescExtraOneText(subName);
			}
			else
			{
				getBody()->getNameDispObj()->setDescExtraOneText(NULL);
			}
		}
		else
		{
			getBody()->getNameDispObj()->setDescExtraOneText(NULL);
			getBody()->getNameDispObj()->setDescVisible(m_Def->DescDisplay);
			getBody()->getNameDispObj()->setDescText(m_Def->Desc.c_str());
		}

		getBody()->getNameDispObj()->setVisible(nameDisPlay);

		getBody()->getNameDispObj()->setTextColor(239, 128, 18);
		getBody()->getNameDispObj()->setZTest(true);
	}
#endif
}


void ClientMob::checkActorDisplayName(int id, bool isBlick)
{
	//根据ID来检测是否有角色对头顶信息显示有特殊需求
	if (id == 3021)
	{
		int itemId = 0;
		int itemCount = 0;
		int itemMaxCount = 0;
		if (isBlick)
		{
			//头顶信息闪烁提示
			getBody()->setNameBlickStartTime(GetWorldManagerPtr()->getWorldTime());
		}
		else
		{
			//获取爱心大使当前需求的道具ICON
			if (getWorld() && !getWorld()->isRemoteMode())
			{
				char tmpbuf1[64];
				char tmpbuf2[64];
				char tmpbuf3[64];
				char tmpbuf4[64];
				sprintf(tmpbuf1, "F%d_GetCurNeedItemCount", id);
				sprintf(tmpbuf2, "F%d_GetCurNeedItemId", id);
				sprintf(tmpbuf3, "F%d_SetCurNeedItemCount", id);
				sprintf(tmpbuf4, "F%d_GetCurNeedItemIdWithOutRandom", id);
				MINIW::ScriptVM::game()->callFunction(tmpbuf1, ">ii", &itemCount, &itemMaxCount);
				if (itemCount == itemMaxCount)
				{
					//如果数量达到了总需求,随机切换需求道具
					MINIW::ScriptVM::game()->callFunction(tmpbuf2, ">i", &itemId);
					MINIW::ScriptVM::game()->callFunction(tmpbuf3, "i", 0);
					MINIW::ScriptVM::game()->callFunction(tmpbuf1, ">ii", &itemCount, &itemMaxCount);
				}
				else
				{
					//如果数量没达到总需求，继续显示当前道具
					MINIW::ScriptVM::game()->callFunction(tmpbuf4, ">i", &itemId);
				}

				if (g_pPlayerCtrl)
				{
					int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
					SharePtr<Texture2D> huires = nullptr;

					SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
						SandboxContext(nullptr)
						.SetData_Number("toolid", itemId)
						.SetData_Number("u", u)
						.SetData_Number("v", v)
						.SetData_Number("w", w)
						.SetData_Number("h", h)
						.SetData_Number("r", r)
						.SetData_Number("g", g)
						.SetData_Number("b", b));
					if (result.IsExecSuccessed())
					{
						huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
					}
					if (huires != 0)
					{
						getBody()->getNameDispObj()->setVisible(true);
						//getBody()->getNameDispObj()->setIconHuires(huires);
						getBody()->setNeedItemIcon(huires);
						char itemCountDesc[256];
						sprintf(itemCountDesc, "%d/%d", itemCount, itemMaxCount);
						getBody()->getNameDispObj()->setText(itemCountDesc);
					}
				}
				removeAiTaskTempt(m_aiTempt);
				addAiTaskTempt(3, 1.0, itemId, false);
			}
			else
			{
				//向房主请求获取当前需求的道具ICON
				syscActorDisplayNameFromHost();
			}
		}

		//同步显示数据
		if (getWorld() && !getWorld()->isRemoteMode())
		{
			syscActorDisplayNameToClient(isBlick);
		}
	}
}

void ClientMob::updateActorDisplayName(int id, int itemId, int itemCount, int animId)
{
	//根据ID来更新角色对头顶信息显示
	if (id == 3021)
	{
		if (animId != 0)
		{
			getBody()->playAnimBySeqId(animId);
		}
		if (itemId == 0)
		{
			//头顶信息闪烁提示
			getBody()->setNameBlickStartTime(GetWorldManagerPtr()->getWorldTime());
		}
		else
		{
			int count = 0;
			int maxCount = 0;
			char tmpBuf1[64];
			sprintf(tmpBuf1, "F%d_GetCurNeedItemCount", id);
			MINIW::ScriptVM::game()->callFunction(tmpBuf1, ">ii", &count, &maxCount);
			count = itemCount;
			//获取爱心大使当前需求的道具ICON
			if (g_pPlayerCtrl)
			{
				int u = 0, v = 0, w = 0, h = 0, r = 255, g = 255, b = 255;
				SharePtr<Texture2D> huires = nullptr;

				SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ItemIconManager_getItemIcon",
					SandboxContext(nullptr)
					.SetData_Number("toolid", itemId)
					.SetData_Number("u", u)
					.SetData_Number("v", v)
					.SetData_Number("w", w)
					.SetData_Number("h", h)
					.SetData_Number("r", r)
					.SetData_Number("g", g)
					.SetData_Number("b", b));
				if (result.IsExecSuccessed())
				{
					huires = result.GetData_UserObject<SharePtr<Texture2D>>("huires");
				}
				if (huires != 0)
				{
					getBody()->getNameDispObj()->setVisible(true);
					//getBody()->getNameDispObj()->setIconHuires(huires);
					getBody()->setNeedItemIcon(huires);
					char itemCountDesc[256];
					sprintf(itemCountDesc, "%d/%d", count, maxCount);
					getBody()->getNameDispObj()->setText(itemCountDesc);
				}
			}
			removeAiTaskTempt(m_aiTempt);
			addAiTaskTempt(3, 1.0, itemId, false);
		}
	}
}

void ClientMob::syscActorDisplayNameToClient(bool isBlick)
{
	if (getDef()->ID == 3021)
	{
		PB_MobDisplayData data;
		int itemId = 0;
		int itemCount = 0;
		int itemMaxCount = 0;
		int animId = 0;
		char tmpBuf1[64];
		char tmpBuf2[64];
		char tmpBuf3[64];
		sprintf(tmpBuf1, "F%d_GetCurNeedItemIdWithOutRandom", getDef()->ID);
		sprintf(tmpBuf2, "F%d_GetCurNeedItemCount", getDef()->ID);
		sprintf(tmpBuf3, "F%d_GetCurAnimId", getDef()->ID);
		MINIW::ScriptVM::game()->callFunction(tmpBuf1, ">i", &itemId);
		if (isBlick)
		{
			//闪烁的时候不需要itemId
			itemId = 0;
		}
		MINIW::ScriptVM::game()->callFunction(tmpBuf2, ">ii", &itemCount, &itemMaxCount);
		MINIW::ScriptVM::game()->callFunction(tmpBuf3, ">i", &animId);
		data.set_itemid(itemId);
		data.set_mobid(getDef()->ID);
		data.set_itemcount(itemCount);
		data.set_animid(animId);
		GetGameNetManagerPtr()->sendBroadCast(PB_SYNC_LOVEAMBASSADOR_ICONID_HC, data);
	}
}

void ClientMob::syscActorDisplayNameFromHost()
{
	if (getDef()->ID == 3021)
	{
		int itemId = 0;
		int itemCount = 0;
		PB_MobDisplayData data;
		data.set_itemid(itemId);
		data.set_mobid(getDef()->ID);
		data.set_itemcount(itemCount);
		GetGameNetManagerPtr()->sendToHost(PB_SYNC_LOVEAMBASSADOR_ICONID_CH, data);
	}
}

bool ClientMob::checkIfSavageAttracked()
{
	if (m_Def && (m_Def->ID == 3101 || m_Def->ID == 3102 || m_Def->ID == 3105))
	{
		int danceState = GetDanceState();
		return danceState == AttractStatus::AttractStatus_Dance || danceState == AttractStatus::AttractStatus_Eat;
	}

	return false;
}

void ClientMob::transToNewSavage()
{
	int newModId = 0;
	if (m_Def->ID == 3101)
	{
		newModId = 3200;
	}
	else if (m_Def->ID == 3102)
	{
		newModId = 3202;
	}
	else if (m_Def->ID == 3105)
	{
		newModId = 3201;
	}
	//变身还是同步加载效果好点，异步加载怪物会消失一段时间不好看
	ClientMob* newmob = createFromDef(newModId, 0, true, true, false);
	if (newmob && m_pWorld)
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return;
		newmob->getLocoMotion()->gotoPosition(getLocoMotion()->m_Position, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
		actorMgr->spawnActor(newmob);
		auto effectComponent = newmob->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_3200_freedom2");
		}
		auto sound = newmob->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3200.freedom2", 1.0f, 1.0f / (GenRandomFloat() * 0.4f + 0.8f));
		}
	}

	//设置野人的出生时间
	/*if (GetWorldManagerPtr())
	{
		auto *villager = dynamic_cast<ActorVillager *>(newmob);
		if(villager)
			villager->setSpawnTime(GetWorldManagerPtr()->getWorldTime());
	}*/
}

void ClientMob::setDisplayName(std::string name)
{
	m_displayName = name;
	getBody()->setDispayName(m_displayName.c_str(), getTeam());
	if (GetWorldManagerPtr() && !GetWorldManagerPtr()->IsRentServerHost())
	{
		getBody()->getNameDispObj()->setVisible(true);
		if (getTamedOwnerID() > 0)
		{
			if (g_pPlayerCtrl && getTamedOwnerID() == g_pPlayerCtrl->getUin())
			{
				getBody()->getNameDispObj()->setTextColor(0, 250, 154);
			}
			else
			{
				getBody()->getNameDispObj()->setTextColor(147, 142, 146);
			}
		}
	}
	notifyVillagerChange(1, 0, name);
}

//宠物自定义昵称
void ClientMob::setmodName(std::string mobName)
{
	if (m_modName != mobName && mobName != "")
	{
		m_modName = mobName;
		getBody()->setDispayName(m_modName.c_str(), 0);
#ifndef IWORLD_SERVER_BUILD
		getBody()->getNameDispObj()->setTextColor(239, 128, 18);
#endif
	}
}

void ClientMob::setRandWildmanName(std::string name)
{
	if (m_displayName == "" && name != "")
	{
		setDisplayName(name);
	}
}

std::string ClientMob::getDisplayName()
{
	return m_displayName;
}

bool ClientMob::isDodge()
{
	if (getAiInvulnerableProb() > 0)
	{
		if (hasAttChanged(2)) {
			if (getAiInvulnerableProb() == 100) {
				return true;
			}
			else {
				return (rand() % 100 < getAiInvulnerableProb());
			}
		}
		else if (GenRandomInt(getAiInvulnerableProb()) == 0)
		{
			return true;
		}
	}
	return false;
}

void ClientMob::LoadActorEventListen()
{
	if (!m_EventListen)
	{
		m_EventListen = SANDBOX_NEW(MobEventListen, dynamic_cast<ClientActor*>(this));
	}
}

bool ClientMob::transformation(int monsterid)
{
	if (m_Def && (monsterid == m_Def->ID) || monsterid <= 0)
	{
		return true;
	}
	auto oldPos = WCoord(0, 0, 0);
	if (m_pWorld && getLocoMotion())
	{
		oldPos = getLocoMotion()->getPosition();
	}
	bool ret = init(monsterid);

	if (m_pWorld)
	{
		if (getLocoMotion())
		{
			getLocoMotion()->onEnterWorld(m_pWorld);
			getLocoMotion()->setPosition(oldPos.x, oldPos.y, oldPos.z);
		}
		if (getBody())
		{
			getBody()->onEnterWorld(m_pWorld);
		}
	}

	return ret;
}
void ClientMob::initAi()
{
	if (!g_WorldMgr)
	{
		LOG_INFO("ClientMob::initAi g_WorldMgr is nil");
		return;
	}
	loadAI();
	//if (!m_pWorld)
	//{
	//	return;
	//}
	//const MonsterDef* def = m_Def;
	//assert(def);
	//unsigned int monsterid = def->ID;
	////可变部分用脚本实现

	//char szScriptFun[256];
	//snprintf(szScriptFun, sizeof(szScriptFun), "F%d_Init", getDef()->ID);
	//MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);

	//// AI
	//bool ai = false;
	//if (!ai && g_WorldMgr && g_WorldMgr->GetBTManager()) // 行为树AI
	//{
	//	if (m_btree) {
	//		g_WorldMgr->GetBTManager()->DestroyBTree(m_btree);
	//	}
	//	snprintf(szScriptFun, sizeof(szScriptFun) - 1, "MOB_%d", getDef()->ID);
	//	m_btree = g_WorldMgr->GetBTManager()->CreateBTreeForTarget(szScriptFun, this);

	//	if (m_bb) {
	//		g_WorldMgr->GetBTManager()->DestoryBlackboard(m_bb);
	//	}
	//	snprintf(szScriptFun, sizeof(szScriptFun) - 1, "MOB_%d", getDef()->ID);
	//	m_bb = g_WorldMgr->GetBTManager()->CreateBlackboard(szScriptFun);
	//	if (m_btree && m_bb)
	//	{
	//		g_WorldMgr->GetBTManager()->BTreeBindBlackboard(m_btree, m_bb); // 绑定行为树和黑板

	//		ai = true; // ai绑定成功
	//	}
	//	else
	//	{
	//		if (m_bb)// 行为树AI
	//			GetWorldManagerPtr()->GetBTManager()->DestoryBlackboard(m_bb);
	//		if (m_btree)
	//			GetWorldManagerPtr()->GetBTManager()->DestroyBTree(m_btree);
	//		m_bb = NULL;
	//		m_btree = NULL;
	//	}
	//}
	//if (!ai && def->gamemod)
	//{
	//	//如果在json中定义了AI
	//	jsonxx::Array jsonArray;
	//	def->gamemod->Event2().Emit<jsonxx::Array &, int>("Mod_GetAIJsonArrayByMonsterId", jsonArray, monsterid);

	//	//jsonxx::Array jsonArray = def->gamemod->GetAIJsonArrayByMonsterId(monsterid);
	//	if (jsonArray.size() > 0)
	//	{
	//		ai = true; // ai绑定成功
	//		ParseAI(jsonArray);
	//		//从配置文件中读取的三个属性。lua中已经调用的相应的函数，不需要重复调用
	//		//setSunHurt(def->SunHurt);
	//		//setCanPassClosedWoodenDoors(def->CanPassClosedWoodenDoors);
	//		//setAvoidWater(def->AvoidWater);
	//		//setPathHide(def->PathHide);
	//	}

	//	//如果在json中定义了avatar数据
	//	jsonxx::Object *pobj = nullptr;
	//	def->gamemod->Event2().Emit<jsonxx::Object *&, int>("Mod_GetAvatarJsonObjById", pobj, monsterid);
	//	//jsonxx::Object* pobj = def->gamemod->GetAvatarJsonObjById(monsterid);
	//	if (pobj)
	//	{
	//		//解析avatar数据
	//		ParseAvatarInfo(*pobj);
	//	}
	//}
	//if (!ai)//使用lua中定义的ai脚本
	//{
	//	ENG_DELETE(m_AITask);
	//	ENG_DELETE(m_AITaskTarget);
	//	if (m_Def->ModelType == MONSTER_CUSTOM_MODEL || m_Def->ModelType == MONSTER_FULLY_CUSTOM_MODEL)
	//		snprintf(szScriptFun, sizeof(szScriptFun), "FCustomActor_SetAi");
	//	else
	//	{
	//		if (getIsHomeLandPet()) //是宠物共用同一套
	//		{
	//			snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", 10001);
	//		}
	//		else
	//		{
	//			snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAi", getDef()->ID);
	//		}

	//		//code-by：dengpeng
	//		if (m_Def->Fodder[0] == 1) //草食生物AI
	//		{
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
	//		}
	//		if (m_Def->Fodder[0] == 2) //肉食生物AI
	//		{
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
	//		}

	//		if (m_Def->Fodder[0] == 3) //杂食生物AI
	//		{
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_PLANT_FEED_TROUGH, 50);
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_MEAT_FEED_TROUGH, 50);
	//			addAiTask<AIEatFeedBlock>(1, BLOCK_FEED_TROUGH, 50);
	//		}
	//	}
	//	if (MINIW::ScriptVM::game()->isFunction(szScriptFun))
	//	{
	//		MINIW::ScriptVM::game()->callFunction(szScriptFun, "u[ClientMob]", this);
	//		ai = true;
	//	}
	//	else
	//	{
	//		ai = false;
	//	}
	//}
	//if (!ai)//AILua方式管理
	//{
	//	snprintf(szScriptFun, sizeof(szScriptFun), "F%d_SetAILua", getDef()->ID);
	//	GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCreate", "u[ClientMob]s>i", this, szScriptFun, &m_AILuaKey);
	//}
}

void ClientMob::initActorAttribTrigger()
{
	if (!m_ActorAttribTrigger)
	{
		m_ActorAttribTrigger = ENG_NEW(ActorAttribut);
	}

	m_ActorAttribTrigger->MaxHP = getAttrib()->getMaxHP();
	m_ActorAttribTrigger->NowHP = getAttrib()->getHP();
	m_ActorAttribTrigger->HPRecover = getAttrib()->getHPRecover();
	m_ActorAttribTrigger->MaxHunger = getMobAttrib()->getMaxFood();
	m_ActorAttribTrigger->NowHunger = (int)getMobAttrib()->getFood();
	m_ActorAttribTrigger->MaxOxygen = getLivingAttrib()->getMaxOxygen();
	m_ActorAttribTrigger->NowOxygen = getLivingAttrib()->getOxygen();
	m_ActorAttribTrigger->MoveSpeed = getAttrib()->getSpeedAtt(Actor_Walk_Speed);
	m_ActorAttribTrigger->RunSpeed = getAttrib()->getSpeedAtt(Actor_Walk_Speed);
	m_ActorAttribTrigger->SwimSpeed = getAttrib()->getSpeedAtt(Actor_Swim_Speed);
	m_ActorAttribTrigger->JumpSpeed = getAttrib()->getSpeedAtt(Actor_Jump_Speed);
	m_ActorAttribTrigger->Weight = (float)getMass();
	m_ActorAttribTrigger->Dodge = (float)getAiInvulnerableProb();
	m_ActorAttribTrigger->NearAttack = getLivingAttrib()->getAttackBaseLua(ATTACK_PUNCH);
	m_ActorAttribTrigger->RemoteAttack = getLivingAttrib()->getAttackBaseLua(ATTACK_RANGE);
	m_ActorAttribTrigger->NearArmor = getLivingAttrib()->getArmorBaseLua(ATTACK_PUNCH);
	m_ActorAttribTrigger->RemoteArmor = getLivingAttrib()->getArmorBaseLua(ATTACK_RANGE);
	m_ActorAttribTrigger->ExtraHP = getAttrib()->getExtraHP();
}

void ClientMob::initAcotrBody(ActorBody* pAcotrBody)
{
	initMobBody(pAcotrBody, m_Def);
	if (!m_Def)
	{
		//m_facade = "";
		return;
	}
	m_RandomScale = m_Def->MinRandomScale + (1 - m_Def->MinRandomScale) * GenRandomFloat();

	setScale(m_Def->ModelScale * m_RandomScale);
	updateModelName();
}

void ClientMob::initLocomotion()
{
	newLocoMotion();
	getLocoMotion()->setBound((int)(m_Def->Height * m_Def->ModelScale), (int)(m_Def->Width * m_Def->ModelScale));

	getLocoMotion()->setAttackBound(
		(int)(m_Def->HitHeight * m_Def->ModelScale),
		(int)(m_Def->HitWidth * m_Def->ModelScale),
		(int)(m_Def->HitThickness * m_Def->ModelScale));
	if (m_Def->CollideBoxs.size() > 0)
	{
		getLocoMotion()->setBaseCollideBoxs(m_Def->CollideBoxs, m_Def->CollideBoxs);
	}

}

MobAttrib* ClientMob::CreateAttrib()
{
	return CreateComponent<MobAttrib>("MobAttrib");
}

void ClientMob::initMobAttrib()
{
	MobAttrib* attrib = NULL;
	if (IsVillager(m_Def->ID))
	{
		attrib = CreateComponent<VillagerAttrib>("VillagerAttrib");
	}
	else
	{
		attrib = CreateComponent<MobAttrib>("MobAttrib");
	}

	if (attrib) {
		attrib->init(m_Def);
		do // 绑定效果事件
		{
			auto callBackAppend = [this](int buffid, int bufflvl) {
				this->onBuffAppend(buffid, bufflvl);
			};
			attrib->setDelegateBuffAppend(callBackAppend);

			auto callBackRemove = [this](int buffid, int bufflvl) {
				this->onBuffRemove(buffid, bufflvl);
			};
			attrib->setDelegateBuffRemove(callBackRemove);
		} while (false);
	}
}

bool ClientMob::loadAIEditTree(bool islocal)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->GetBTManager()) // 行为树AI
	{
		//加载AI编辑器
		int aitype = getDef()->AIConfigType;
		if (aitype < 2 && islocal)
		{
			return false;
		}
		unsigned int curMobId = getDef()->ID;
		std::string aiconfig;
		std::string uuid;
		ScriptVM* LuaVM = MINIW::ScriptVM::game();
		if (LuaVM && islocal)
		{
			char aiid[256] = { 0 };
			LuaVM->callFunction("BTGetAiID", "i>s", curMobId, aiid);
			uuid = aiid;
			aiconfig = uuid;
		}
		else
		{
			char aidir[256] = { 0 };
			uuid = getDef()->AIConfig;
			if (uuid.length() > 0)
			{
				aiconfig = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->GetAiDir(uuid);
			}
		}
		auto btmanager = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager());
		if (m_bb)// 行为树AI
			btmanager->DestoryBlackboard(m_bb);
		if (m_btree)
			btmanager->DestroyBTree(m_btree);
		if (aiconfig.length() > 0)
		{
			m_btree = btmanager->AIEditCreateBTreeForTarget(aiconfig, curMobId, uuid.c_str(), this, islocal);
			if (m_btree)
			{
				m_btree->SetUUID(uuid.c_str());
				m_bb = btmanager->AIEditCreateBlackboard(aiconfig, curMobId, uuid.c_str(), islocal);
				if (m_bb)
				{
					btmanager->BTreeBindBlackboard(m_btree, m_bb);
				}
				//如果在地图中则设置为激活状态
				if (m_pWorld)
				{
					m_btree->SetActive(true);
				}
				//不一定有黑板文件
				return true;
			}
		}
	}
	return false;
}

void ClientMob::initDefault()
{
	init(3400);
}

bool ClientMob::ModloadAI(const jsonxx::Object& obj)
{
	if (!GetWorldManagerPtr() || !GetWorldManagerPtr()->GetBTManager())
		return false;
	ScriptVM* LuaVM = MINIW::ScriptVM::game();
	if (!LuaVM)
		return false;

	if (!obj.has<jsonxx::Object>("properties"))
		return false;

	const jsonxx::Object& propObj = obj.get<jsonxx::Object>("properties");
	if (!propObj.has<jsonxx::Object>("BTreeArray"))
		return false;

	const jsonxx::Object& BttreeArray = propObj.get<jsonxx::Object>("BTreeArray");
	if (!BttreeArray.has<jsonxx::Array>("value"))
		return false;

	//获取树相关路径
	std::vector<std::string> m_treepaths;
	std::vector<std::string> m_bbpaths;
	std::vector<std::string> m_treeids;
	std::vector<std::string> m_bbtreeids;
	std::vector<jsonxx::Object> m_bbvalues;

	bool isadd = false;
	std::string treeid = "";
	const jsonxx::Array& value = BttreeArray.get<jsonxx::Array>("value");
	for (int j = 0; j < value.size(); j++)
	{
		const jsonxx::Object& values = value.get<jsonxx::Object>(j);
		if (!values.has<jsonxx::Object>("value"))
			continue;

		const jsonxx::Object& treeinfo = values.get<jsonxx::Object>("value");
		if (UnSerializeValue<bool, jsonxx::Boolean>("isEnable", treeinfo, isadd) && isadd)
		{
			if (UnSerializeValue<std::string, jsonxx::String>("treeid", treeinfo, treeid) && !treeid.empty())
			{
				std::string dir = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->GetAiDir(treeid);
				if (dir.length() > 0)
				{
					m_treeids.push_back(treeid);
					m_treepaths.emplace_back(dir + "/btree.lua");

					m_bbtreeids.push_back(treeid);
					m_bbvalues.push_back(treeinfo);
					m_bbpaths.emplace_back(dir + "/blackboard.lua");
				}
			}
		}
	}

	if (m_treepaths.size() == 0)
		return false;

	//创建树和黑板值
	if (m_bb)// 行为树AI
		static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestoryBlackboard(m_bb);
	if (m_btree)
		static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->DestroyBTree(m_btree);

	unsigned int curMobId = getDef()->ID;
	m_btree = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->AIEditLoadBTreeByPaths(curMobId, m_treepaths, m_treeids, this);
	if (!m_btree)
		return false;

	if (m_treeids.size() == 1)
	{
		m_btree->SetUUID(m_treeids[0].c_str());
	}
	else
	{
		m_btree->SetUUIDS(m_treeids);
	}
	m_bb = static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->AIEditLoadBlackboardByPaths(curMobId, m_bbpaths, m_bbtreeids);
	if (m_bb)
	{
		static_cast<BTManager*>(GetWorldManagerPtr()->GetBTManager())->BTreeBindBlackboard(m_btree, m_bb);
	}
	//如果在地图中则设置为激活状态

	//加载配置里面值
	if (m_bb)
	{
		m_bb->SetInitValue(m_bbvalues);
	}

	if (m_pWorld)
	{
		m_btree->SetActive(true);
	}
	return true;
}

void ClientMob::LoadBTree()
{
	if (!m_bLoadBTree)
		return;

	m_bLoadBTree = false;
	const MonsterDef* def = getDef();
	if (!def)
		return;

	bool loadai = false;
	UgcAssetPrefab* pPrefab = UgcAssetMgr::GetInstancePtr()->GetPrefabAsset(ModPackMgr::GetInstancePtr()->GetResIdByCfgId(CustomModType::Mod_Monster, def->ID));
	if (pPrefab)
	{
		const jsonxx::Object& jsonObj = pPrefab->GetJsonObject();
		if (jsonObj.has<jsonxx::Object>("root"))
		{
			const jsonxx::Object& obj = jsonObj.get<jsonxx::Object>("root");
			if (!obj.empty())
			{
				const jsonxx::Array& vComponents = obj.get<jsonxx::Array>("components");

				jsonxx::Object monsterEditObj;
				jsonxx::Object fightEditObj;
				jsonxx::Object bTreeEditObj;
				for (size_t i = 0; i < vComponents.size(); i++)
				{
					const jsonxx::Object& cmpObj = vComponents.get<jsonxx::Object>(i);
					if (cmpObj.has<jsonxx::String>("name"))
					{
						const std::string& sCmpName = cmpObj.get<jsonxx::String>("name");
						if ("MonsterEdit" == sCmpName)
						{
							monsterEditObj = cmpObj;
						}
						else if ("FightEdit" == sCmpName)
						{
							fightEditObj = cmpObj;
						}
						else if ("BTree" == sCmpName)
						{
							bTreeEditObj = cmpObj;
						}
					}
				}

				if (!bTreeEditObj.empty())
				{
					loadai = ModloadAI(bTreeEditObj);
				}

				if (!loadai && !monsterEditObj.empty() && !fightEditObj.empty())
				{
					ParseAIByMonsterModConfig(def, monsterEditObj);
					ParseAIByFightModConfig(def, fightEditObj);
					loadai = true;
				}
			}
		}
	}

	if (!loadai)
	{
		loadAI();
	}
}

ClientMob* ClientMob::Instantiate(int id, const jsonxx::Object& obj, int mapid/* =-1 */)
{
	World* pWorld = nullptr;
	if (mapid == -1 || !g_WorldMgr)
	{
		pWorld = MNSandbox::GetWorldByCurrentScene();
	}
	else
	{
		pWorld = g_WorldMgr->getWorld(mapid);
	}

	if (!pWorld)
		return nullptr;

	ClientMob* pMob = ClientMob::createFromDef(id, 0, true, true, true, false);
	if (!pMob)
		return nullptr;

	if (!pMob->init(id, obj))
	{
		pMob->Release();
		return nullptr;
	}

	pWorld->getActorMgr()->spawnActor(pMob);
	pMob->LoadScriptComponent();

	return pMob;
}

ClientActor* ClientMob::clone()
{
	return  SANDBOX_NEW(ClientMob);
}

void ClientMob::setIsHideStatus(bool val)
{
	m_isHideStatus = val;
	m_scorpionHideTick = 3;
}

void ClientMob::setMonsterId(const int id)
{
	ActorLiving::setMonsterId(id);
	m_monsterId = id;
}

void ClientMob::SetDescription(const std::string& desc)
{
	m_description = desc;
}

void ClientMob::SetModelActorBody(int iModelType, const std::string& sModelPath, int iExtraData, bool saveMesh)
{
	ClientActor::SetModelActorBody(iModelType, sModelPath, iExtraData, saveMesh);

	if (m_Body)
	{
		m_Body->setControlRotation(true);
		m_Body->setInterpRotation(true);
	}
}

bool ClientMob::canEquipByPickup(int itemid)
{
	// 皮质
	if (itemid >= 12201 && itemid <= 12204)
	{
		return true;
	}
	// 锁链
	else if (itemid >= 12211 && itemid <= 12214)
	{
		return true;
	}
	// 秘银
	else if (itemid >= 12221 && itemid <= 12224)
	{
		return true;
	}
	// 黄铜
	else if (itemid >= 12216 && itemid <= 12219)
	{
		return true;
	}
	// 钛金
	else if (itemid >= 12231 && itemid <= 12234)
	{
		return true;
	}
	// 钨金
	else if (itemid >= 12241 && itemid <= 12244)
	{
		return true;
	}
	// 防寒
	else if (itemid >= 12312 && itemid <= 12315)
	{
		return true;
	}
	// 花冠
	else if (itemid == ITEM_GARLAND)
	{
		return true;
	}

	return false;
}

void ClientMob::OnUpdateBoundBoxChanged()
{
    OPTICK_EVENT();
	auto body = getBody();
	if (!body)
		return;

	auto model = body->getModel();
	auto entity = body->getEntity();
	if (!model || !entity)
		return;

	BoxSphereBound b;
	model->getLocalBounds(b);
	auto bound = b.getBox();
	Rainbow::Vector3f scale = entity->GetScale();
	auto pos = body->getPosition().toVector3();

	MNSandbox::Box<float> boundbox(b.getMin(), b.getMax());
	if (boundbox.IsZero())
		return;

	MNSandbox::Coord3<float> boxscale(scale);
	boundbox *= boxscale;
	boundbox += pos;
	GetBoundingBox()->SetSelfBox(boundbox.ToCast<int>());
}

WORLD_ID ClientMob::getMobAttackTarget()
{
	if (m_bb && m_btree)
	{
		long long id;
		m_bb->GetData_Number("bbKeyBeHurtTargetObjId", id);
		return id;
	}
	else
	{
		auto targetComponent = getToAttackTargetComponent();
		if (targetComponent != NULL)
		{
			return targetComponent->getTargetId();
		}
	}
	return 0;
}


bool ClientMob::getHasAttackTarget()
{
	if (m_bb != NULL)
	{
		long long objid = 0;
		m_bb->GetData_Object("enemy_id", objid);
		if (objid > 0)
		{
			return true;
		}
		m_bb->GetData_Object("bbKeyBeHurtTargetObjId", objid);
		if (objid > 0)
		{
			return true;
		}
	}

	auto targetComponent = getToAttackTargetComponent();
	if (targetComponent && targetComponent->hasTarget())
	{
		return true;
	}

	return false;
}

bool ClientMob::needSaveInChunk()
{
	return false;
	// auto pClientFlyComponent = getClientFlyComponent();
	// if (pClientFlyComponent)
	// {
	// 	bool ret = pClientFlyComponent->needSaveInChunk();
	// 	if (!ret)
	// 		return false;
	// }


	// return m_needSaveInChunk;
};

void ClientMob::SetNeedSaveInChunk(bool needSaveInChunk)
{
	m_needSaveInChunk = needSaveInChunk;
};

void ClientMob::InitDefaultChildren()
{
	m_bInitChildrenNode = true;
}

void ClientMob::setSpawnPoint(const WCoord& spawnPos)
{
	m_SpawnPoint = spawnPos;
}

int ClientMob::getViewDist()
{
	int retDist = 0;
	if (m_Def)
	{
		if (m_viewDistance > 0)
			retDist = m_viewDistance * BLOCK_FSIZE;
		else
			retDist = m_Def->ViewDistance * BLOCK_FSIZE;
	}
	return retDist;
}


bool ClientMob::isVacantFox(int monsterId)
{
	//虚空狐狸
	return monsterId >= 3248 && monsterId <= 3253;
}

bool ClientMob::isSyncLoadModelWhileNewActorBody()
{
	//虚空狐狸
	if (isVacantFox(m_monsterId))
		return true;

	return m_defaultSyncLoadMonsterModel;
}

void ClientMob::RegisterTLAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName)
{
	if (!m_tlMgr)
	{
		m_tlMgr = ENG_NEW(SandboxTLManager);
		TimelineDef def;
		def.name = "ComboAttackState_timeline";
		def.mod = 0;
		m_timeline = m_tlMgr->CreateTimelineForTarget(&def, this);
	}
	TLAnimationFrameEventDef eDef;
	eDef.tlType = "TLFrameEventCallFunc";
	eDef.animSeqId = seqId;
	eDef.startFrame = keyFrame;
	eDef.name = eventName;
	m_tlMgr->CreateTLNode(&eDef, m_timeline);
}

void ClientMob::ActiveTLAnimFrameEvent()
{
	if (m_timeline)
		m_timeline->Active();
}

void ClientMob::ClearTLAnimFrameEvent()
{
	if (m_timeline)
	{
		m_timeline->Over();
		m_timeline->ClearTLNode();
	}
}
IActorLocoMotion* ClientMob::GetMobLocoMotion()
{
	return getLocoMotion();
}

int ClientMob::GetMobType()
{
	if (m_Def)
		return m_Def->Type;

	return 0;
}

void ClientMob::PlayModBeHit(bool explode)
{
	if (nullptr == getBody() || !getBody()->getEntity())
	{
		return;
	}

	if (m_ModExplodeBeHitState) //还没爬起来
	{
		return;
	}

	ModBeHitConf *beHitConf = ModPackMgr::GetInstancePtr()->GetModBeHitConf();
	if (!beHitConf)
	{
		return;
	}

	if (explode)
	{
		m_ModExplodeBeHitState = 1;
		m_ModExplodeBeHitActTick = beHitConf->m_actLieTime/0.05f;
		playAnimById(beHitConf->m_explosionActLie);

		//禁AI
		if (m_btree)
		{
			m_btree->SetActive(false);
		}
		setAIActive(false);
		getNavigator()->clearPathEntity();
	}
	else
	{
		int actCnt = beHitConf->m_notExplosionActs.size();
		if (actCnt <= 0)
		{
			return;
		}
		int actID = beHitConf->m_notExplosionActs[(rand() % actCnt)];
#ifdef IWORLD_SERVER_BUILD
		playAnimById(actID);
#else
		ColourValue color(0.5f, 0, 0);
		getBody()->getEntity()->SetOverlayColor(&color);
		if (!getBehitDisable())
		{
			playAnimById(actID);
		}
		getBody()->setHurtTick(10);
#endif
	}
}
bool ClientMob::NeedTickForever()
{
	/*const MonsterDef*def =  getDef();
	if (def)
	{
		if (def->AlwaysUpdate > 0)
		{
			return true;
		}
	}*/
	return false;
}

void ClientMob::SaveMobTriggerAttr(SAVE_BUFFER_BUILDER& builder,flatbuffers::Offset<flatbuffers::Vector<float>>& vec)
{
	MobAttrib * atrr =  getMobAttrib();
	ActorBody* body = getBody();
	if (!body || !atrr) { return; }
	std::vector<float> tvec;
	if (m_viewDistance > 0)
	{
		tvec.push_back(CREATUREATTR::VIEW_DISTANCE); 
		tvec.push_back(getViewDist());
	}
	float val = body->getBodyLerpSpeed();
	if (val != 6)//非默认值
	{
		tvec.push_back(CREATUREATTR::BODY_LERP_SPEED);
		tvec.push_back(val);
	}
	val = atrr->getAttackPhysical();
	if (val != 0)//非默认值
	{
		tvec.push_back(CREATUREATTR::ATK_PHYSICAL);
		tvec.push_back(val);
	}

	val = atrr->getAttackElem();
	if (val != 0) //非默认值
	{
		tvec.push_back(CREATUREATTR::ATK_MAGIC);
		tvec.push_back(val);
	}

	val = atrr->getDefPhysical();
	if (val != 0) //非默认值
	{
		tvec.push_back(CREATUREATTR::DEF_PHYSICAL);
		tvec.push_back(val);
	}

	val = atrr->getDefElem();
	if (val != 0) //非默认值
	{
		tvec.push_back(CREATUREATTR::DEF_MAGIC);
		tvec.push_back(val);
	}

	val = atrr->getExtraHP();
	if (val != 0) //非默认值
	{
		tvec.push_back(CREATUREATTR::EXTRA_HP);
		tvec.push_back(val);
	}

	val = atrr->getToughnessBase();
	if (val != 0) //非默认值
	{
		tvec.push_back(CREATUREATTR::TOUGHNESS);
		tvec.push_back(val);
	}
	vec = builder.CreateVector(tvec);
}

void ClientMob::LoadMobTriggerAttr(const flatbuffers::Vector<float>* vec)
{
	MobAttrib* atrr = getMobAttrib();
	ActorBody* body = getBody();
	if (!body || !atrr) { return; }
	for (size_t i = 0; i < vec->size(); )
	{
		int enumid = vec->Get(i);
		float val = vec->Get(i + 1);
		i = i + 2;
		if (enumid == CREATUREATTR::VIEW_DISTANCE)
		{
			setViewDist(val);
		}
		else if (enumid == CREATUREATTR::BODY_LERP_SPEED)
		{
			body->setBodyLerpSpeed(val);
		}
		else if (enumid == CREATUREATTR::ATK_PHYSICAL)
		{
			atrr->setAttackPhysical(val);
		}
		else if (enumid == CREATUREATTR::ATK_MAGIC)
		{
			atrr->setAttackElem(val);
		}
		else if (enumid == CREATUREATTR::DEF_PHYSICAL)
		{
			atrr->setDefPhysical(val);
		}
		else if (enumid == CREATUREATTR::DEF_MAGIC)
		{
			atrr->setDefElem(val);
		}
		else if (enumid == CREATUREATTR::EXTRA_HP)
		{
			atrr->setExtraHP(val);
		}
		else if (enumid == CREATUREATTR::TOUGHNESS)
		{
			atrr->setToughnessBase(val);
		}
	}
}

void ClientMob::updateSkinnedAppearance()
{
    // 更新尸体外观为已剥皮状态
    if (getBody())
    {
        // 1. 改变模型颜色 - 使用更接近血肉的颜色
        if (getBody()->getEntity())
        {
            // 使用更鲜明的红色调，表示已被剥皮的肉体
            Rainbow::ColourValue color(0.7f, 0.2f, 0.2f);
            getBody()->getEntity()->SetOverlayColor(&color);
        }
        
        // 3. 播放一次性特效，表示剥皮完成
        // auto effectComponent = getEffectComponent();
        // if (effectComponent)
        // {
        //     // 播放血迹特效
        //     effectComponent->playBodyEffect(BODYFX_HURT); // 使用伤害特效替代
        // }
        
        // // 5. 添加蝇群粒子效果 
        // ParticlesComponent::playParticles(this, "flies.ent", true);
        
        // // 6. 添加击中效果粒子
        // ParticlesComponent::playParticles(this, "ball_hit.ent");
        
        // // 7. 添加血迹贴花在地面上
        // WCoord pos = getPosition();
        // World* pWorld = getWorld();
        // if (pWorld)
        // {
        //     pWorld->addDecal("blood_decal.material", pos, 150.0f, 150.0f, GenRandomFloat() * 360.0f);		
        // }
    }
}

float ClientMob::getSkinningPercentage() const
{
    if (m_skinningTime <= 0.0f)
        return 0;
    
    return (m_skinningElapsedTime / m_skinningTime);
}

bool ClientMob::startSkinning(ClientPlayer* player)
{
    // 已经被剥皮或者不是尸体，不能剥皮
    if (m_isSkinned || !isDead() || !getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE))
    {
        LOG_INFO("StartSkinning failed: mob [%lld, %d] is already skinned or not a valid corpse", getObjId(), getDefID());

		if (m_isSkinned)
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000101);
		}
		else if (!getFlagBit(ACTORFLAG_INTERACTIVE_CORPSE))
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000102);
		}
        return false;
    }
    
    // 已经在剥皮中
    // if (m_isSkinning)
    // {
    //     LOG_INFO("StartSkinning failed: mob [%lld, %d] is already being skinned", getObjId(), getDefID());
	// 	player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000103);
    //     return false;
    // }
    
    // 检查玩家与尸体之间的距离
    if (!isPlayerInSkinningRange(player))
    {
        LOG_INFO("StartSkinning failed: player [%lld] is too far from mob [%lld, %d], distance: %.2f", 
                player->getObjId(), getObjId(), getDefID(), getDistanceTo(player));
        
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000104);
        return false;
    }
    
    // 检查玩家当前使用的工具是否适合剥皮
    int currItemID = player->getCurToolID();
    bool hasValidTool = true;
    
    // 检查工具是否适合剥皮
	//DefDataTable<SkinningToolDef>& skinningToolTable = GetDefManagerProxy()->getSkinningToolTable();
	//for (int i = 0; i < skinningToolTable.GetRecordSize(); i++)
	//{
	//	const SkinningToolDef* toolDef = skinningToolTable.GetRecordByIndex(i);
	//	if (toolDef && toolDef->ToolID == currItemID)
	//	{
	//		hasValidTool = true;
	//		m_skinningToolID = currItemID;
	//		break;
	//	}
	//}
    
    if (!hasValidTool)
    {
        LOG_INFO("StartSkinning failed: player [%lld] does not have a valid skinning tool, current tool: %d", player->getObjId(), currItemID);
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000105);
        return false;
    }
    
    // 设置剥皮状态
    m_isSkinning = true;
    m_skinningElapsedTime = 0.0f;
    m_skinningPlayerID = player->getObjId();
    
    // 计算剥皮时间（根据动物类型和工具效率）
    m_skinningTime = m_Def->SkinningTime;
	if (m_skinningTime <= 0.0f)
	{
		m_skinningTime = 4.0f;
	}
    
    // 根据工具效率调整剥皮时间
	//const SkinningToolDef* toolDef = GetDefManagerProxy()->getSkinningToolTable().GetRecord(currItemID);
	//if (toolDef)
	//{
	//	m_skinningTime = m_skinningTime / toolDef->Efficiency;
	//	LOG_INFO("StartSkinning success: player [%lld] started skinning mob [%lld, %d], tool: %d, duration: %.2f seconds, efficiency: %.2f", 
	//			player->getObjId(), getObjId(), getDefID(), currItemID, m_skinningTime, toolDef->Efficiency);
	//}
    
    
    // 播放剥皮开始动画和音效
    // playSkinningAnimation();
    // playSkinningSound();
    
    return true;
}

bool ClientMob::startSkinningClient(ClientPlayer* player)
{
	// common op
	startSkinning(player);

	// send request to server
	PB_PlayerSkinning req;
	req.set_op(ePBSkinningOP::eStartSkinning);
	req.set_uin(player->getUin());
	req.set_mobid(getObjId());
	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SKINNING_CH, req);

	// update UI
	updateSkinningUI(player, true);

	return true;
}

bool ClientMob::startSkinningServer(ClientPlayer* player)
{
	startSkinning(player);

	return true;
}

// 更新剥皮进度
void ClientMob::updateSkinning(float dtime, ClientPlayer* player)
{
    if (!m_isSkinning || m_skinningPlayerID != player->getObjId())
    {
        LOG_INFO("UpdateSkinning failed: mob [%lld, %d] is not being skinned by player [%lld]", 
                getObjId(), getDefID(), player->getObjId());
        return;
    }
    
    // 检查玩家是否仍然在有效距离内
    if (!isPlayerInSkinningRange(player))
    {
        LOG_INFO("UpdateSkinning: player [%lld] moved too far from mob [%lld, %d], distance: %.2f, cancelling skinning", 
                player->getObjId(), getObjId(), getDefID(), getDistanceTo(player));

		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000104);
        
        // 如果玩家距离太远，取消剥皮
        cancelSkinningClient(player);
        
        return;
    }
    
    // 检查玩家工具是否变化
	bool isToolChanged = false;
    int currItemID = player->getCurToolID();
	if (currItemID != m_skinningToolID && m_skinningToolID > 0)
	{
		isToolChanged = true;
	}

    if (isToolChanged)
    {
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000107);
        LOG_INFO("UpdateSkinning: player [%lld] changed tool from skinning tool to %d, cancelling skinning", player->getObjId(), currItemID);
        cancelSkinningClient(player);
        return;
    }
    
    // 更新剥皮时间
    m_skinningElapsedTime += dtime;
    
    // 每秒记录一次进度日志
    static float logTimer = 0.0f;
    logTimer += dtime;
    if (logTimer >= 1.0f)
    {
        LOG_INFO("UpdateSkinning: player [%lld] skinning mob [%lld, %d], progress: %.1f/%.1f seconds (%.1f%%)", 
                player->getObjId(), getObjId(), getDefID(), m_skinningElapsedTime, m_skinningTime, (m_skinningElapsedTime / m_skinningTime) * 100.0f);
        logTimer = 0.0f;
    }
    
    // 检查是否完成剥皮
    if (m_skinningElapsedTime >= m_skinningTime)
    {
        LOG_INFO("UpdateSkinning: skinning time reached for mob [%lld, %d], finishing skinning", getObjId(), getDefID());
        finishSkinningClient(player);
        return;
    }
    
    // 每0.5秒播放一次剥皮音效
    static float soundTimer = 0.0f;
    soundTimer += dtime;
    if (soundTimer >= 0.5f)
    {
        // playSkinningSound();
        soundTimer = 0.0f;
    }
    
    // 更新剥皮进度UI
    updateSkinningUI(player);
}

// 取消剥皮过程
void ClientMob::cancelSkinning(ClientPlayer* player)
{
    if (!m_isSkinning)
    {
        LOG_INFO("CancelSkinning: mob [%lld, %d] is not being skinned", getObjId(), getDefID());
        return;
    }
    
    LOG_INFO("CancelSkinning: cancelling skinning for mob [%lld, %d], player [%lld], progress: %.1f/%.1f seconds (%.1f%%)", 
            getObjId(), getDefID(), m_skinningPlayerID, m_skinningElapsedTime, m_skinningTime, 
            (m_skinningElapsedTime / m_skinningTime) * 100.0f);
    
    // 重置剥皮状态
    m_isSkinning = false;
    m_skinningElapsedTime = 0.0f;

	if (player)
	{
		player->setSkinning(false);
	}
}

void ClientMob::cancelSkinningClient(ClientPlayer* player)
{
	cancelSkinning(player);

	PB_PlayerSkinning req;
	req.set_op(ePBSkinningOP::eCancelSkinning);
	req.set_uin(player->getUin());
	req.set_mobid(getObjId());
	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SKINNING_CH, req);

	if (player)
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000107);
		updateSkinningUI(player, true);
		m_skinningPlayerID = 0;
		m_skinningToolID = 0;
	}	
}

void ClientMob::cancelSkinningServer(ClientPlayer* player)
{
	cancelSkinning(player);

	if (player)
	{
		m_skinningPlayerID = 0;
		m_skinningToolID = 0;
	}
}

bool ClientMob::finishSkinning(ClientPlayer* player)
{
    if (!m_isSkinning || m_skinningPlayerID != player->getObjId())
    {
        LOG_INFO("FinishSkinning failed: mob [%lld, %d] is not being skinned by player [%lld]", getObjId(), getDefID(), player->getObjId());
        return false;
    }
    
    LOG_INFO("FinishSkinning: player [%lld] finished skinning mob [%lld, %d] after %.1f seconds", 
            player->getObjId(), getObjId(), getDefID(), m_skinningElapsedTime);
    
    m_isSkinned = true;
    m_isSkinning = false;
	m_corpseRemainTime = 0.0f;
    m_skinningElapsedTime = 0.0f;
    m_skinningPlayerID = 0;
	m_skinningToolID = 0;
    
    LOG_INFO("FinishSkinning: skinning completed successfully for mob [%lld, %d]", getObjId(), getDefID());
    
    return true;
}

bool ClientMob::finishSkinningClient(ClientPlayer* player)
{
	finishSkinning(player);

	// 更新尸体外观为已剥皮状态
	updateSkinnedAppearance();

	// 隐藏剥皮进度UI
	updateSkinningUI(player, true);

	PB_PlayerSkinning req;
	req.set_op(ePBSkinningOP::eFinishSkinning);
	req.set_uin(player->getUin());
	req.set_mobid(getObjId());
	GetGameNetManagerPtr()->sendToHost(PB_PLAYER_SKINNING_CH, req);

	player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000109);

	return true;
}

bool ClientMob::finishSkinningServer(ClientPlayer* player)
{
	finishSkinning(player);
	// 消耗工具耐久度
	// player->getAttrib()->onCurToolUsed(-1);

	MobAttrib* mobAttrib = static_cast<MobAttrib*>(getLivingAttrib());
	if (mobAttrib) {
		mobAttrib->dropSkinningsItems();
		mobAttrib->dropBagsItems();
		mobAttrib->dropEquipItems();
	}

	return true;
}

// 更新剥皮进度UI
void ClientMob::updateSkinningUI(ClientPlayer* player, bool forceUpdate)
{
    if (!player->hasUIControl() && !forceUpdate)
        return;

	float p = getSkinningPercentage();
	if (p > 0) {
		g_pPlayerCtrl->setCircleProgress(p);
	}
	else {
		g_pPlayerCtrl->setCircleProgress(-1.0f);
	}
    
    //// 创建UI上下文
    //MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
    //
    //if (m_isSkinning && m_skinningPlayerID == player->getObjId())
    //{
    //    // 正在剥皮，显示进度条
    //    uiContext.SetData_Number("type", 200); // 表示剥皮进度条
    //    uiContext.SetData_Number("progress", getSkinningPercentage()); // 进度百分比
    //    uiContext.SetData_Bool("show_skinning_progress", true);
    //}
    //else
    //{
    //    // 隐藏剥皮进度条
    //    uiContext.SetData_Number("type", 201); // 表示隐藏剥皮进度条
    //    uiContext.SetData_Bool("hide_skinning_progress", true);
    //}
    //
    //// 发送UI事件
    //if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
    //{
    //    MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_SKINNING_PROGRESS", uiContext);
    //}
}

// 计算与另一个Actor之间的距离
float ClientMob::getDistanceTo(ClientActor* actor)
{
    if (!actor)
        return 999999.0f; // 如果目标无效，返回一个非常大的距离
    
    // 获取两个Actor的位置
	WCoord myPos = this->getPosition();
    WCoord targetPos = actor->getPosition();
    
    // 计算两点之间的距离
    float dx = static_cast<float>(myPos.x - targetPos.x);
    float dy = static_cast<float>(myPos.y - targetPos.y);
    float dz = static_cast<float>(myPos.z - targetPos.z);
    
    return sqrt(dx*dx + dy*dy + dz*dz);
}

// 检查玩家是否在剥皮有效距离内
bool ClientMob::isPlayerInSkinningRange(ClientPlayer* player)
{
    if (!player)
        return false;
    
    // 获取玩家与尸体之间的距离
    float distance = getDistanceTo(player);
    
    // 定义最大剥皮距离（以游戏单位为单位）
    const float MAX_SKINNING_DISTANCE = 400.0f; // 4格距离
    
    // 检查距离是否在有效范围内
    return distance <= MAX_SKINNING_DISTANCE;
}
