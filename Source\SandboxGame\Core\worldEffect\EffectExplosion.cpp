#include "EffectExplosion.h"
#include "ClientActor.h"
#include "blocks/BlockMaterialMgr.h"
#include "ClientActorLiving.h"
#include "ActorBomb.h"
#include "blocks/special_blockid.h"
#include "WorldManager.h"
#include "LuaInterfaceProxy.h"
#include "MusicManager.h"
#include "ClientPlayer.h"
#include "EffectParticle.h"
#include "ActorLocoMotion.h"
#include "ClientMob.h"
#include "ClientActorProjectile.h"
#include "ClientItem.h"
#include "worldData/block_tickmgr.h"

using namespace MINIW;

float GetBlockDensity(World *pworld, const WCoord &center, const CollideAABB &aabb)
{
	int dx = BLOCK_SIZE*BLOCK_SIZE / (aabb.dim.x*2 + BLOCK_SIZE);
	int dy = BLOCK_SIZE*BLOCK_SIZE / (aabb.dim.y*2 + BLOCK_SIZE);
	int dz = BLOCK_SIZE*BLOCK_SIZE / (aabb.dim.z*2 + BLOCK_SIZE);
	int nempty = 0;
	int total = 0;

	WorldRay ray;
	for(int x = 0; x <= BLOCK_SIZE; x += dx)
	{
		for(int y = 0; y <= BLOCK_SIZE; y += dy)
		{
			for(int z = 0; z <= BLOCK_SIZE; z += dz)
			{
				WCoord pos = aabb.minPos() + aabb.dim*WCoord(x+dx/2,y+dy/2,z+dz/2)/BLOCK_SIZE;

				ray.m_Origin = pos.toWorldPos();
				ray.m_Dir = (center - pos).toVector3();
				ray.m_Range = ray.m_Dir.Length();
				if(ray.m_Range == 0) nempty++;
				else
				{
					ray.m_Dir /= ray.m_Range;
					if(!pworld->pickGround(ray, NULL))
					{
						nempty++;
					}
				}

				total++;
			}
		}
	}

	return (float)nempty / (float)total;
}

//穿透衰减
float GetIntoAttenuation(World* pworld, const WCoord& center, ClientActor* actor)
{
	if (!pworld || !actor)
		return 0.0f;

	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	float penetrationAcc = 0.0f;
	WCoord actorPos = actor->getPosition();
	actorPos.y = (actorPos.y / 100) * 100;
	WCoord centerPos = center;
	centerPos.y = (centerPos.y / 100) * 100;
	WorldRay ray;
	ray.m_Origin = centerPos.toWorldPos();
	ray.m_Dir = (actorPos - centerPos).toVector3();
	ray.m_Range = ray.m_Dir.Length();
	if (ray.m_Range != 0.0f)
	{
		ray.m_Dir /= ray.m_Range;
		//Block
		IntersectResult presult;
		bool pickHas = pworld->pickGround(ray, &presult, PICK_METHOD_BLOCK);
		if (pickHas && !(presult.blockVector.empty()))
		{
			for (const WCoord& blockPos : presult.blockVector)
			{
				if (blockPos == CoordDivBlock(centerPos) || blockPos == CoordDivBlock(actorPos))
					continue;

				int blockid = pworld->getBlockID(blockPos);
				BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
				if (blockDef)
				{
					//方块衰减
					if (blockDef->Type == "basic")// basic类型方块衰减
					{
						std::unordered_map<int, float>& basicMap = ugcCfg.basic;
						if (basicMap.find(blockDef->MineTool) != basicMap.end())
							penetrationAcc += Rainbow::Clamp(basicMap[blockDef->MineTool], 0.0f, 1.0f);
						else
							penetrationAcc += 1.0f;
					}
					else
					{
						float value = ugcCfg.get(blockDef->Type.c_str(), 1.0f);
						penetrationAcc += Rainbow::Clamp(value, 0.0f, 1.0f);
					}
				}
			}
		}
		//Actor
		ActorExcludes excludes;
		excludes.addActorWithRiding(actor);
		pworld->pickAttackedActors(ray, excludes, &presult);
		if (!(presult.actors.empty()))
		{
			for (const auto& it_actor : presult.actors)
			{
				if (it_actor && !(it_actor->IsKindOf<ActorLiving>()) && !(it_actor->IsKindOf<ClientActorProjectile>()) && !(it_actor->IsKindOf<ClientItem>()))
				{
					penetrationAcc += Rainbow::Clamp(ugcCfg.get("creature", 1.0f), 0.0f, 1.0f);
				}
			}
		}
	}
	return penetrationAcc;
}

// 这个不是范围，是球体内各个方向的划分力度，8表示 8*8*8=512个方向  4表示 4*4*4=64个方向
const int MAX_EXPLODE_RANGE = 16;
Explosion::Explosion(World* world, IClientActor* actor, int explodesize, const WCoord& blockpos, bool flaming, bool smoking, bool versionEx)
{
	m_World = world;
	m_Exploder = actor;
	m_ExplodePos = blockpos;
	m_ExplodeSize = (float)explodesize;
	m_Smoking = smoking;
	m_Flaming = flaming;
	m_VersionEx = versionEx;
}

void ExplosionGeneral::getExplodeBlocks_Old(bool explodeUpHalf)
{
	Rainbow::HashTable<WCoord, bool, WCoordHashCoder> affects(127);
	float dist = 2.0f / ((float)MAX_EXPLODE_RANGE - 1.0f);
	for (int x = 0; x < MAX_EXPLODE_RANGE; ++x)
	{
		for (int y = (explodeUpHalf ? MAX_EXPLODE_RANGE / 2 : 0); y < MAX_EXPLODE_RANGE; ++y)
		{
			for (int z = 0; z < MAX_EXPLODE_RANGE; ++z)
			{
				if (x == 0 || x == MAX_EXPLODE_RANGE - 1 || y == 0 || y == MAX_EXPLODE_RANGE - 1 || z == 0 || z == MAX_EXPLODE_RANGE - 1)
				{
					Rainbow::Vector3f dir(x * dist - 1.0f, y * dist - 1.0f, z * dist - 1.0f);
					dir = MINIW::Normalize(dir);

					// 爆炸力度衰减
					int exp_len = int(m_ExplodeSize * (0.7f + GenRandomFloat() * 0.6f));
					WCoord pos = m_ExplodePos;
					const float step = 30.0f;

					while (exp_len > 0)
					{
						WCoord blockpos = CoordDivBlock(pos);

						int blockid = m_World->getBlockID(blockpos);
						if (blockid > 0)
						{
							auto mtl = g_BlockMtlMgr.getMaterial(blockid);
							float resist = GetDefManagerProxy()->getBlockDef(blockid)->AntiExplode / 10.0f;
							if (mtl)
							{
								resist = mtl->getBlockAntiExplode() / 10.0f;
							}
							int data = m_World->getBlockData(blockpos);
							if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && m_World->getCurMapID() == MAPID_LIEYANSTAR && (m_World->getBlockData(blockpos) & 4)) //未解除封印
								resist = -1;

							if (resist < 0) exp_len = -1;
							else exp_len -= int((resist + 0.3f) * step);
						}

						if (exp_len > 0)
						{
							affects.insert(blockpos, true);
						}

						pos += dir * step;
						exp_len -= step * 0.75f;
					}
				}
			}
		}
	}
	Rainbow::HashTable<WCoord, bool, WCoordHashCoder>::Element *ele = affects.iterate(NULL);
	while(ele)
	{
		m_AffectedBlocks.push_back(ele->key);
		ele = affects.iterate(ele);
	}
}

void ExplosionGeneral::getExplodeBlocks(bool explodeUpHalf)
{
	// 使用m_ExplodeSize/BLOCK_SIZE/2为半径遍历所有方块
	int radius = (int)(m_ExplodeSize / BLOCK_SIZE / 2);
	
	std::map<int, std::map<int, std::map<int, int>>> explodeMap;
	
	WCoord basepos = CoordDivBlock(m_ExplodePos);
	// 遍历以爆炸点为中心的立方体区域
	for (int x = -radius; x <= radius; ++x)
	{
		for (int y = (explodeUpHalf ? 0 : -radius); y <= radius; ++y)
		{
			for (int z = -radius; z <= radius; ++z)
			{
				// 计算目标方块位置
				WCoord targetBlockPos = basepos + WCoord(x, y, z);
				
				// 检查是否需要遮挡判断
				bool needBlockCheck = true;
				if ((x == 0 || x == 1 || x == -1) && 
					(y == 0 || y == 1 || y == -1) && 
					(z == 0 || z == 1 || z == -1))
				{
					needBlockCheck = false;
				}
				
				// 检查从爆炸点到目标方块中心点之间是否有遮挡
				bool isBlocked = false;
				if (needBlockCheck)
				{
					// 计算目标方块中心点
					WCoord targetCenter = targetBlockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE/2, BLOCK_SIZE/2, BLOCK_SIZE/2);
					
					// 根据目标方块的中心点来计算dir
					Rainbow::Vector3f dir = (targetCenter - m_ExplodePos).toVector3();
					dir = MINIW::Normalize(dir);
					targetBlockPos = CoordDivBlock(m_ExplodePos + dir * (float)(radius * BLOCK_SIZE));

					float step_len = m_ExplodeSize;
					const float step = 30.0f;
					while (step_len > 0)
					{
						WCoord pos = m_ExplodePos + dir * step;
						step_len -= step;
						WCoord blockpos = CoordDivBlock(pos);
						if (blockpos.y != targetBlockPos.y)
						{
							int blockid = m_World->getBlockID(blockpos);
							if (blockid > 0)
							{
								isBlocked = true;
								break;
							}
						}
					}
				}
				
				// 如果被遮挡，忽略该方块
				if (isBlocked) continue;
				
				// 检查目标方块是否存在
				int blockid = m_World->getBlockID(targetBlockPos);
				if (blockid > 0)
				{
					// 检查是否已经在爆炸影响列表中
					if (explodeMap.find(targetBlockPos.x) != explodeMap.end() && 
						explodeMap[targetBlockPos.x].find(targetBlockPos.y) != explodeMap[targetBlockPos.x].end() && 
						explodeMap[targetBlockPos.x][targetBlockPos.y].find(targetBlockPos.z) != explodeMap[targetBlockPos.x][targetBlockPos.y].end())
					{
						continue;
					}
					
					explodeMap[targetBlockPos.x][targetBlockPos.y][targetBlockPos.z] = blockid;
				}
			}
		}
	}

	// 将爆炸影响的方块添加到列表中
	for (const auto& it_explodeMap : explodeMap)
	{
		for (const auto& it_explodeMap2 : it_explodeMap.second)
		{
			for (const auto& it_explodeMap3 : it_explodeMap2.second)
			{
				m_AffectedBlocks.push_back(WCoord(it_explodeMap.first, 
					it_explodeMap2.first, it_explodeMap3.first));
			}
		}
	}
}

void ExplosionGeneral::doExplosionA()
{
	if (m_VersionEx) return doExplosionAEx();

	bool needDamage = true, explodeUpHalf = false, isSecondExplodeType = false;
	float secondExplodeValue = 0.0f;
	ClientActorProjectile* pFromProjectile = dynamic_cast<ClientActorProjectile*>(m_Exploder);
	if (pFromProjectile)
	{
		if (pFromProjectile->GetItemId() == BLOCK_VOID_FRUIT || pFromProjectile->GetItemId() == ITEM_VOID_SUPER_FRUIT)
			needDamage = false;

		if (pFromProjectile->m_ProjectileDef)
		{
			//上半球
			explodeUpHalf = pFromProjectile->m_ProjectileDef->ExplodeRangeType == 2;
			//是否 第2种 爆炸
			isSecondExplodeType = pFromProjectile->m_ProjectileDef->AttackType == 2;

			for (int i = 0; i < 7; i++)
			{
				secondExplodeValue += pFromProjectile->m_ExplodePoints[i];
			}
		}
	}
	//新爆炸其他开启条件
	if (!m_Exploder && m_newAttackType)
	{
		isSecondExplodeType = true;
		secondExplodeValue = m_atkValue;
		explodeUpHalf = m_upHalf;
	}

	float expsize_bak = m_ExplodeSize;
	// getExplodeBlocks_Old(explodeUpHalf);
	getExplodeBlocks(explodeUpHalf);

	float explodeSizeHalf = m_ExplodeSize;
	m_ExplodeSize *= 2.0f;
	WCoord sizepos(m_ExplodeSize + BLOCK_SIZE, (explodeUpHalf ? explodeSizeHalf : m_ExplodeSize) + BLOCK_SIZE, m_ExplodeSize + BLOCK_SIZE);
	CollideAABB box;
	box.pos = m_ExplodePos - sizepos;
	box.dim = sizepos * 2;
	std::vector<IClientActor *>actors;
	m_World->getActorsInBoxExclude(actors, box, m_Exploder);

	if (needDamage)
	{
		for (size_t i = 0; i < actors.size(); ++i)
		{
			IClientActor* actor = actors[i];
			//物理载具
			//VehicleAssembleLocoMotion* vloc = dynamic_cast<VehicleAssembleLocoMotion*>(actor->getLocoMotion());
			bool isVloc = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(), LocoMotionType::VEHICLE_ASSEMBLE_LOCOMOTION);
			Rainbow::Vector3f dp = (actor->getPosition() - m_ExplodePos).toVector3();
			float dist = dp.Length();
			float t = dist / m_ExplodeSize;
			bool openNewHpdec = GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate;
			//新爆炸修正，距离占比
			if (openNewHpdec && isSecondExplodeType)
			{
				t = dist / explodeSizeHalf;
			}
			const int MAX_COLBOX_DIM = BLOCK_SIZE * 2;
			if (t <= 1.0f || isVloc)
			{
				if (dist != 0 || isVloc)
				{
					Rainbow::Vector3f dir = dp / dist;
					actor->getCollideBox(box);
					WCoord shrinksize(0, 0, 0);
					if (box.dim.x > MAX_COLBOX_DIM) shrinksize.x = (MAX_COLBOX_DIM - box.dim.x) / 2;
					if (box.dim.y > MAX_COLBOX_DIM * 2) shrinksize.y = (MAX_COLBOX_DIM * 2 - box.dim.y) / 2;
					if (box.dim.z > MAX_COLBOX_DIM) shrinksize.z = (MAX_COLBOX_DIM - box.dim.z) / 2;
					box.expand(shrinksize.x, shrinksize.y, shrinksize.z);

					float density = GetBlockDensity(m_World, m_ExplodePos, box);
					float explodestr = (1.0f - dist / m_ExplodeSize) * density;
					float explodesize = m_ExplodeSize / (BLOCK_FSIZE * 2.0f);
					int atkpoints_old = int((explodestr * explodestr + explodestr) * 8.0f * explodesize + 1.0f) * 5;
					int atkpoints = atkpoints_old;

					OneAttackData atkdata;
					atkdata.damage_armor = true;
					// 独立爆炸 code-by:liya
					if (openNewHpdec)
					{
						//新类型，第2种爆炸 code-by:李元星
						if (isSecondExplodeType)
						{
							t = 1.0f - Rainbow::Max(0.0f, (dist - BLOCK_SIZE)) / explodeSizeHalf; //修正计算因子
							static constexpr float minDamaPercent = 0.23f; //最小伤害百分比(暂定)
							float disAttenuation = (t * t + t) * 0.5f + (1.0f - t) * minDamaPercent;//距离衰减
							float intoAttenuation = GetIntoAttenuation(m_World, m_ExplodePos, actor->ToCast<ClientActor>());//穿透衰减
							atkpoints = secondExplodeValue * disAttenuation * Rainbow::Max(0.0f, 1.0f - intoAttenuation / CoordDivBlock(explodeSizeHalf));
						}
						atkdata.atktype = ATTACK_EXPLODE;
						atkdata.atkTypeNew = (1 << ATTACK_EXPLODE);
						atkdata.explodePoints[m_damageType] = atkpoints;
					}
					else
					{
						atkdata.atktype = ATTACK_EXPLODE;
						atkdata.atkpoints = atkpoints;
					}
					atkdata.fromplayer = dynamic_cast<IClientPlayer*>(getExploder());
					atkdata.explodePos_x = m_ExplodePos.x;
					atkdata.explodePos_y = m_ExplodePos.y;
					atkdata.explodePos_z = m_ExplodePos.z;
					atkdata.explodeSize = m_ExplodeSize;

					//人发射了投掷物, 投掷物产生了爆炸
					if (!atkdata.fromplayer)
					{
						if (pFromProjectile)
						{
							IClientPlayer* player = nullptr;
							bool result = m_Exploder->Event2().Emit<IClientPlayer*&>("Projectile_getshootingActor", player);
							Assert(result);
							if (player)
							{
								atkdata.fromplayer = player;
							}
						}
					}

					if (m_World && m_World->isRaining())
					{
						IClientMob* mob = dynamic_cast<IClientMob*>(m_Exploder);
						if (mob && mob->GetMobID() == 3109) //爆爆蛋下雨天伤害减少30%
						{
							atkdata.atkpoints *= 0.7f;
						}
					}

					auto component = actor->getActorComponent(ComponentType::COMPONENT_ATTACK);
					//auto component = actor->getAttackedComponent();
					if (component)
					{
						bool result = component->Event2().Emit<IClientActor*, IClientActor*, OneAttackData&>("Attack_attack",m_Exploder, getExploder(), atkdata);
						Assert(result);
					}
					//actor->attackedFromType(ATTACK_EXPLODE, atkpoints, getExploder());

					if (actor->getILocoMotion())
					{
						// 这里判断是否为物理actor
						// 标准：标准质量定1000g，
						// 注：PhysicsActorDef->Mass 单位：g 物理机械对应是getMass()
						// 载具目前没有击退效果 2019/08/10
						bool isPhysxMotion = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(), LocoMotionType::PHYSICS_LOCOMOTION);
						bool isvehicleLoco = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(), LocoMotionType::VEHICLE_ASSEMBLE_LOCOMOTION);
						bool hasPhysActor = false;
						bool result = actor->getILocoMotion()->GetEvent2()->Emit<bool&, IClientActor*, bool, Rainbow::Vector3f&, float>("PhyscsLoco_PhysActor",
							hasPhysActor, actor, isvehicleLoco, dp, explodestr);
						if (isPhysxMotion && hasPhysActor)
						{
							
						}
						else
						{
							//向上抛物线
							actor->setMotionChange(dir.x * atkpoints_old, GenRandomInt(100, 120), dir.z * atkpoints_old, true);
						}
					}
				}
			}
		}
	}

	m_ExplodeSize = expsize_bak;

	//检测爆炸范围8个方块是否有冰凌
	int maxLength = 8 * 8 * 8;
	WCoord pos = CoordDivBlock(m_ExplodePos);
	int nRange = 8;
	for (int z = pos.z - nRange; z <= pos.z + nRange; z++)
	{
		for (int x = pos.x - nRange; x <= pos.x + nRange; x++)
		{
			for (int y = pos.y; y <= pos.y + nRange; y++)
			{
				WCoord blockpos = WCoord(x, y, z);
				if ((blockpos - pos).lengthSquared() <= maxLength)
				{
					int blockid = m_World->getBlockID(blockpos);
					if (blockid == BLOCK_WEAK_ICICLE)
					{
						icicleBlocks.emplace_back(blockpos);
						break;
					}
				}
			}
		}
	}
}

void ExplosionGeneral::doExplosionB()
{
	// if (m_VersionEx) return doExplosionBEx();

	EffectManager *effectmgr = m_World->getEffectMgr();

	float pitch = (1.0f + (GenRandomFloat()-GenRandomFloat())*0.2f) * 0.7f;
	std::string soundPath = m_soundPath.empty() ? "misc.explode" : m_soundPath;
	effectmgr->playSound(m_ExplodePos, soundPath.c_str(), 4.0f, pitch);

	std::string effectPath = m_effectPath.empty() ? "particles/1005.ent" : m_effectPath;
	if(m_ExplodeSize>=BLOCK_SIZE*2 && m_Smoking)
	{
		//hugeexplode
		auto eff = effectmgr->playParticleEffectAsync(effectPath.c_str(), m_ExplodePos, 100, 0, 0, true, 0, 1);
		//让爆炸类特效总是更新
		if (eff) {
			effectmgr->SetPersistentUpdate(eff, true);
		}
	}
	else
	{
		//largeexplode
		auto eff = effectmgr->playParticleEffectAsync(effectPath.c_str(), m_ExplodePos, 100, 0, 0, true, 0, 1);
		//让爆炸类特效总是更新
		if (eff) {
			effectmgr->SetPersistentUpdate(eff, true);
		}
	}

	if(m_Smoking)
	{
		bool haveProtectedBlock = false;
		IClientActor* shooter = nullptr;
		if (m_Exploder)
		{
			bool result = m_Exploder->Event2().Emit<IClientActor*&>("Actor_getshootingActor", shooter);
			Assert(result);
			/*if (m_Exploder->isPlayer()) player = m_Exploder;
			else player = m_Exploder->getShootingActor();*/
		}
		if (shooter)
		{
			IClientPlayer* player = dynamic_cast<IClientPlayer*>(shooter);
			for (size_t i = 0; i < m_AffectedBlocks.size(); i++)
			{
				WCoord affectpos = m_AffectedBlocks[i];
				int blockid = m_World->getBlockID(affectpos);

				if (blockid > 0)
				{
					if (!m_World->IsBrokenBlockEnable(blockid, shooter->getObjId()))
					{
						continue;
					}
					if (m_World->IsBlockProtected(affectpos, blockid))
					{
						haveProtectedBlock = true;
						continue;
					}
					BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
					if (mtl)
					{
						int blockdata = m_World->getBlockData(affectpos);
						float hardness = math::min(1.0f, mtl->getDestroyHardness(blockdata, player));
						float damage = m_atkValue * (1 - hardness);
						if (mtl->DoOnBlockDamaged(m_World, affectpos, player, damage))
						{
							int recvHurt = m_World->getBlockDataEx(affectpos);  // 累计伤害
							if (mtl->GetBlockDef() && recvHurt >= mtl->GetBlockDef()->MaxHP)
							{
								m_World->destroyBlockEx(affectpos.x, affectpos.y, affectpos.z, true);
							}
						}
					}
				}
			}
		}

		if (haveProtectedBlock)
		{
			IClientPlayer* fromplayer = NULL;
			if (getExploder())
			{
				fromplayer = dynamic_cast<IClientPlayer*>(getExploder());

			}
			if (!fromplayer)
			{
				if (m_Exploder)
				{
					ClientActorProjectile* pFromProjectile = dynamic_cast<ClientActorProjectile*>(m_Exploder);
					if (pFromProjectile)
					{
						IClientPlayer* player = nullptr;
						bool result = m_Exploder->Event2().Emit<IClientPlayer*&>("Projectile_getshootingActor", fromplayer);
						Assert(result);
						if (player)
						{
							fromplayer = player;
						}
					}
				}
			}
			if (fromplayer) fromplayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000200);
		}
		//爆炸范围6格范围冰凌有几率会掉落
		for (size_t i = 0; i < icicleBlocks.size(); i++)
		{
			if (GenRandomInt(100) < 80)
			{
				int blockcount = 0;
				int upblockcount = 0;
				vector<WCoord> posArray;
				WCoord _downblockpos = icicleBlocks[i];
				while (m_World->getBlockID(_downblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_downblockpos);
					_downblockpos = _downblockpos - WCoord(0, 1, 0);
					blockcount++;
				}
				WCoord _upblockpos = icicleBlocks[i] + WCoord(0, 1, 0);
				while (m_World->getBlockID(_upblockpos) == BLOCK_WEAK_ICICLE)
				{
					posArray.emplace_back(_upblockpos);
					_upblockpos = _upblockpos + WCoord(0, 1, 0);
					blockcount++;
					upblockcount++;
				}
				//下方有至少1格空间则坠落
				if (blockcount > 0 && m_World->getBlockID(_downblockpos) == BLOCK_AIR)
				{
					for (int i = 0; i < posArray.size(); i++)
					{
						m_World->setBlockAir(posArray[i]);
					}
					WCoord shootPos = (upblockcount > 0) ? DownCoord(_upblockpos) : icicleBlocks[i];
					auto pos = BlockBottomCenter(shootPos);
					Rainbow::GetMusicManager().PlaySound("sounds/item/32/prepare.ogg", pos.toVector3(), 1.0f, 1.0f);
					GetISandboxActorSubsystem()->shootIcicleAuto(ITEM_WEAK_ICICLE, m_World, BlockBottomCenter(shootPos), blockcount);
				}
			}
		}
	}

	if(m_Flaming)
	{
		for(size_t i=0; i<m_AffectedBlocks.size(); i++)
		{
			const WCoord &blockpos = m_AffectedBlocks[i];
			int blockid = m_World->getBlockID(blockpos);

			if(blockid == 0)
			{
				int blockid2 = m_World->getBlockID(DownCoord(blockpos));
				BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid2);

				if(mtl && mtl->isOpaqueCube() && GenRandomInt(3)==0)
				{
					m_World->setBlockAll(blockpos, BLOCK_FIRE, 0);
				}
			}
		}

		//投射火球
		if (m_Exploder)
		{
			IClientActor *ptarget = nullptr;
			//auto targetComponent = m_Exploder->GetComponent<ToAttackTargetComponent>();
			auto targetComponent = m_Exploder->getActorComponent(ComponentType::COMPONENT_TO_ATTACK_TARGET);
			if (targetComponent)
			{
				bool result = targetComponent->Event2().Emit<IClientActor*&>("ToAttackTarget_getTarget", ptarget);
				Assert(result);
			}
			IClientMob *pMob = dynamic_cast<IClientMob*>(m_Exploder);
			if (pMob && ptarget)
			{
				for (int i = 0; i < GenRandomInt(12, 20); i++)
				{
					GetISandboxActorSubsystem()->throwItemByMob(m_World, m_Exploder, 0.3f, 11587, 100001, 180, 180);
				}
			}
		}
	}
}

void ExplosionGeneral::doExplosionAEx()
{
    // 先执行原有的地形破坏计算逻辑
    float expsize_bak = m_ExplodeSize;
    Rainbow::HashTable<WCoord, bool, WCoordHashCoder> affects(127);
    float dist = 2.0f / ((float)MAX_EXPLODE_RANGE - 1.0f);

    // 创建爆炸物理实例
    ExplosionPhysics* physics = new ExplosionPhysics(m_World, m_ExplodePos, 3);
    
    // 设置爆炸参数
    if (m_Exploder) {
        ClientActorProjectile* pFromProjectile = dynamic_cast<ClientActorProjectile*>(m_Exploder);
        if (pFromProjectile && pFromProjectile->m_ProjectileDef) {
            physics->setUpHalfExplosion(pFromProjectile->m_ProjectileDef->ExplodeRangeType == 2);
            physics->setSecondaryExplosion(pFromProjectile->m_ProjectileDef->AttackType == 2);
        }
    }

    // 原有的爆炸范围计算
    for (int x = 0; x < MAX_EXPLODE_RANGE; ++x) {
        for (int y = 0; y < MAX_EXPLODE_RANGE; ++y) {
            for (int z = 0; z < MAX_EXPLODE_RANGE; ++z) {
                if (x == 0 || x == MAX_EXPLODE_RANGE - 1 || 
                    y == 0 || y == MAX_EXPLODE_RANGE - 1 || 
                    z == 0 || z == MAX_EXPLODE_RANGE - 1) {
                    
                    Rainbow::Vector3f dir(x * dist - 1.0f, y * dist - 1.0f, z * dist - 1.0f);
                    dir = MINIW::Normalize(dir);

                    int exp_len = int(m_ExplodeSize * (0.7f + GenRandomFloat() * 0.6f));
                    WCoord pos = m_ExplodePos;
                    const float step = 30.0f;

                    // 射线检测破坏方块
                    while (exp_len > 0) {
                        WCoord blockpos = CoordDivBlock(pos);
                        int blockid = m_World->getBlockID(blockpos);
                        
                        if (blockid > 0) {
                            float resist = GetDefManagerProxy()->getBlockDef(blockid)->AntiExplode / 10.0f;
                            int data = m_World->getBlockData(blockpos);
                            
                            // 特殊方块处理
                            if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && 
                                m_World->getCurMapID() == MAPID_LIEYANSTAR && 
                                (data & 4)) {
                                resist = -1;
                            }

                            if (resist < 0) exp_len = -1;
                            else exp_len -= int((resist + 0.3f) * step);
                        }

                        if (exp_len > 0) {
                            affects.insert(blockpos, true);
                        }

                        pos += dir * step;
                        exp_len -= step * 0.75f;
                    }
                }
            }
        }
    }

    // 2. 收集受影响的方块并生成碎片
    m_AffectedBlocks.clear();
    std::vector<WCoord> affectedBlocks;
    
    Rainbow::HashTable<WCoord, bool, WCoordHashCoder>::Element* ele = affects.iterate(NULL);
    while (ele) {
        WCoord blockPos = ele->key;
        m_AffectedBlocks.push_back(blockPos);
        affectedBlocks.push_back(blockPos);
        ele = affects.iterate(ele);
    }

    // 3. 为受影响的方块生成碎片
    physics->generateFragments(affectedBlocks);

    // 4. 处理实体伤害
    m_ExplodeSize *= 2.0f;
    WCoord sizepos(m_ExplodeSize + BLOCK_SIZE, m_ExplodeSize + BLOCK_SIZE, m_ExplodeSize + BLOCK_SIZE);
    CollideAABB box;
    box.pos = m_ExplodePos - sizepos;
    box.dim = sizepos * 2;
    
    std::vector<IClientActor*> actors;
    m_World->getActorsInBoxExclude(actors, box, m_Exploder);

    if (m_Exploder)
    {
        ClientActorProjectile* pFromProjectile = dynamic_cast<ClientActorProjectile*>(m_Exploder);
        if (pFromProjectile && pFromProjectile->m_ProjectileDef)
        {
            float secondExplodeValue = 0.0f;
            for (int i = 0; i < 7; i++)
            {
                secondExplodeValue += pFromProjectile->m_ExplodePoints[i];
            }
            physics->setSecondaryExplosionValue(secondExplodeValue);
        }
    }

    if (!m_Exploder && m_newAttackType)
    {
        physics->setSecondaryExplosion(true);
        physics->setSecondaryExplosionValue(m_atkValue);
        physics->setUpHalfExplosion(m_upHalf);
    }

    m_ExplodeSize = expsize_bak;

    if (m_World && m_World->getBlockTickMgr()) {
        m_World->getBlockTickMgr()->RegisterExplosion(physics);
    }
}

void ExplosionGeneral::doExplosionBEx()
{

}

IClientActor *Explosion::getExploder()
{
	if (m_Exploder == NULL) return NULL;

	ActorBomb* tnt = dynamic_cast<ActorBomb*>(m_Exploder);
	if (tnt) return tnt->getPlaceByActor();
	else
	{
		ActorLiving* actor = dynamic_cast<ActorLiving*>(m_Exploder);
		if (actor) return m_Exploder;
		else return NULL;
	}
}


ExplosionAirBall::ExplosionAirBall(World* world, ClientActor* actor, const WCoord& blockpos, int explosionRadius, int atkpoint, bool isInWater, Rainbow::Vector3f rotation)
	: Explosion(world, actor, explosionRadius, blockpos, false, false)
{
	m_atkpoint = atkpoint;
	m_isInWater = isInWater;
	m_rotation = rotation;
}

void ExplosionAirBall::doExplosionA()
{
	float expsize_bak = m_ExplodeSize;
	Rainbow::HashTable<WCoord, bool, WCoordHashCoder> affects(127);

	float dist = 2.0f / ((float)MAX_EXPLODE_RANGE - 1.0f);

	for (int x = 0; x < MAX_EXPLODE_RANGE; ++x)
	{
		for (int y = 0; y < MAX_EXPLODE_RANGE; ++y)
		{
			for (int z = 0; z < MAX_EXPLODE_RANGE; ++z)
			{
				if (x == 0 || x == MAX_EXPLODE_RANGE - 1 || y == 0 || y == MAX_EXPLODE_RANGE - 1 || z == 0 || z == MAX_EXPLODE_RANGE - 1)
				{
					Rainbow::Vector3f dir(x * dist - 1.0f, y * dist - 1.0f, z * dist - 1.0f);
					dir = MINIW::Normalize(dir);

					int exp_len = int(m_ExplodeSize * (0.7f + GenRandomFloat() * 0.6f));
					WCoord pos = m_ExplodePos;
					const float step = 30.0f;

					while (exp_len > 0)
					{
						WCoord blockpos = CoordDivBlock(pos);

						int blockid = m_World->getBlockID(blockpos);
						if (blockid > 0)
						{
							float resist = GetDefManagerProxy()->getBlockDef(blockid)->AntiExplode / 10.0f;
							int data = m_World->getBlockData(blockpos);
							if (blockid == BLOCK_KEY_OF_BROKEN_SWORD && m_World->getCurMapID() == MAPID_LIEYANSTAR && (m_World->getBlockData(blockpos) & 4)) //未解除封印
								resist = -1;

							if (resist < 0) exp_len = -1;
							else exp_len -= int((resist + 0.3f) * step);
						}

						if (exp_len > 0)
						{
							affects.insert(blockpos, true);
						}

						pos += dir * step;
						exp_len -= step * 0.75f;
					}
				}
			}
		}
	}

	Rainbow::HashTable<WCoord, bool, WCoordHashCoder>::Element* ele = affects.iterate(NULL);
	while (ele)
	{
		m_AffectedBlocks.push_back(ele->key);
		ele = affects.iterate(ele);
	}

	m_ExplodeSize *= 2.0f;
	WCoord sizepos(m_ExplodeSize + BLOCK_SIZE, m_ExplodeSize + BLOCK_SIZE, m_ExplodeSize + BLOCK_SIZE);
	WCoord blockpos1 = CoordDivBlock(m_ExplodePos - sizepos);
	WCoord blockpos2 = CoordDivBlock(m_ExplodePos + sizepos);
	CollideAABB box;
	//box.pos = blockpos1;
	//box.dim = blockpos2 - blockpos1;
	box.pos = m_ExplodePos - sizepos;
	box.dim = sizepos * 2;
	std::vector<IClientActor*>actors;
	m_World->getActorsInBoxExclude(actors, box, m_Exploder);

	for (size_t i = 0; i < actors.size(); ++i)
	{
		IClientActor* actor = actors[i];
		//物理载具
		bool vloc = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(),LocoMotionType::VEHICLE_ASSEMBLE_LOCOMOTION);
		Rainbow::Vector3f dp = (actor->getPosition() - m_ExplodePos).toVector3();
		float dist = dp.Length();
		float t = dist / m_ExplodeSize;
		const int MAX_COLBOX_DIM = BLOCK_SIZE * 2;

		if (t <= 1.0f || vloc)
		{
			if (dist != 0 || vloc)
			{
				Rainbow::Vector3f dir = dp / dist;
				actor->getCollideBox(box);
				WCoord shrinksize(0, 0, 0);
				if (box.dim.x > MAX_COLBOX_DIM) shrinksize.x = (MAX_COLBOX_DIM - box.dim.x) / 2;
				if (box.dim.y > MAX_COLBOX_DIM * 2) shrinksize.y = (MAX_COLBOX_DIM * 2 - box.dim.y) / 2;
				if (box.dim.z > MAX_COLBOX_DIM) shrinksize.z = (MAX_COLBOX_DIM - box.dim.z) / 2;
				box.expand(shrinksize.x, shrinksize.y, shrinksize.z);

				float density = GetBlockDensity(m_World, m_ExplodePos, box);
				float explodestr = (1.0f - t) * density;

				//int atkpoints = int((explodestr*explodestr + explodestr) / 2.0f * 8.0f * m_ExplodeSize/BLOCK_FSIZE + 1.0f);
				float explodesize = m_ExplodeSize / (BLOCK_FSIZE * 2.0f);
				int atkpoints = m_atkpoint;
				if (atkpoints > 0)
				{
					OneAttackData atkdata;
					//memset(&atkdata, 0, sizeof(atkdata));
					atkdata.damage_armor = true;
					// 独立爆炸 code-by:liya
					if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
					{
						atkdata.atkTypeNew = (1 << ATTACK_EXPLODE);
						atkdata.explodePoints[0] = atkpoints;
					}
					else
					{
						atkdata.atktype = ATTACK_EXPLODE;
						atkdata.atkpoints = atkpoints;
					}
					atkdata.fromplayer = dynamic_cast<IClientPlayer*>(getExploder());
					atkdata.explodePos_x = m_ExplodePos.x;
					atkdata.explodePos_y = m_ExplodePos.y;
					atkdata.explodePos_z = m_ExplodePos.z;
					atkdata.explodeSize = m_ExplodeSize;

					//人发射了投掷物, 投掷物产生了爆炸
					if (!atkdata.fromplayer)
					{
						bool isProjectile = GetISandboxActorSubsystem()->IsActorType(m_Exploder, ActorType::ACTOR_PROJECTILE);
						if (isProjectile)
						{
							IClientPlayer* player = nullptr;
							bool result = m_Exploder->Event2().Emit<IClientPlayer*&>("Projectile_getshootingActor", player);
							Assert(result);
							if (player)
							{
								atkdata.fromplayer = player;
							}
						}
					}

					if (m_World && m_World->isRaining())
					{
						IClientMob* mob = dynamic_cast<IClientMob*>(m_Exploder);
						if (mob && mob->GetMobID() == 3109) //爆爆蛋下雨天伤害减少30%
						{
							atkdata.atkpoints *= 0.7f;
						}
					}
					//auto component = actor->getAttackedComponent();
					auto component = actor->getActorComponent(ComponentType::COMPONENT_ATTACK);
					if (component)
					{
						IClientActor* attacker = getExploder();
						bool result = component->Event2().Emit<IClientActor*, IClientActor*, OneAttackData&>("Attack_attack", m_Exploder, attacker, atkdata);
						Assert(result);
					}
					//actor->attackedFromType(ATTACK_EXPLODE, atkpoints, getExploder());
				}
				if (actor->getILocoMotion())
				{
					// 这里判断是否为物理actor
					// 标准：标准质量定1000g，
					// 注：PhysicsActorDef->Mass 单位：g 物理机械对应是getMass()
					// 载具目前没有击退效果 2019/08/10
					bool isPhysxMotion = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(), LocoMotionType::PHYSICS_LOCOMOTION);
					bool isvehicleLoco = GetISandboxActorSubsystem()->IsLocoMotionType(actor->getILocoMotion(), LocoMotionType::VEHICLE_ASSEMBLE_LOCOMOTION);
					bool hasPhysActor = false;
					bool result = actor->getILocoMotion()->GetEvent2()->Emit<bool&, IClientActor*, bool, Rainbow::Vector3f&, float>("PhyscsLoco_PhysActor",
						hasPhysActor, actor, isvehicleLoco, dp, explodestr);
					if (isPhysxMotion && hasPhysActor)
					{

					}
					else
					{
						//向上抛物线
						//actor->setMotionChange(-GenRandomInt(180, 240), GenRandomInt(120, 150), 0, true);
						int montionParam = atkpoints;
						int montiony = GenRandomInt(100, 120);
						if (!m_isInWater)
						{
							montiony = 0;
							montionParam = GetLuaInterfaceProxy().get_lua_const()->air_ball_land_montion_param;
						}
						actor->setMotionChange(dir.x * montionParam, montiony, dir.z * montionParam, true);
					}
				}
			}
		}
	}

	m_ExplodeSize = expsize_bak;
}

void ExplosionAirBall::doExplosionB()
{
	EffectManager* effectmgr = m_World->getEffectMgr();

	float pitch = (1.0f + (GenRandomFloat() - GenRandomFloat()) * 0.2f) * 0.7f;

	std::string soundPath = "item.11649.airball1";
	float soundVoloum = 4.0f;
	std::string path = "particles/sea_04_baozha.ent";
	if (!m_isInWater)
	{
		path = "particles/sea_05_fashe.ent";
		soundPath = "item.11649.airball2";
	}

	effectmgr->playSound(m_ExplodePos, soundPath.c_str(), soundVoloum, pitch);
	if (m_ExplodeSize >= BLOCK_SIZE * 2 && m_Smoking)
	{
		//hugeexplode
		auto eff = effectmgr->playParticleEffectAsync(path.c_str(), m_ExplodePos, 100, m_rotation.y, m_rotation.x, true, 0, 1);
		//让爆炸类特效总是更新
		if (eff) {
			effectmgr->SetPersistentUpdate(eff, true);
		}
	}
	else
	{
		//largeexplode
		auto eff = effectmgr->playParticleEffectAsync(path.c_str(), m_ExplodePos, 100, m_rotation.y, m_rotation.x, true, 0, 1);
		//让爆炸类特效总是更新
		if (eff) {
			effectmgr->SetPersistentUpdate(eff, true);
		}
	}
}

void ExplosionAirBall::doExplosionAEx()
{

}

void ExplosionAirBall::doExplosionBEx()
{

}
