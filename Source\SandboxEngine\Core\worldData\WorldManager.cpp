

#include "IClientPlayer.h"
#include "WorldManager.h"
#include "ClientActorDef.h"
#include "Particle/LegacyParticleSystem.h"
#include "ChunkGenerator.h"
#include "Environment.h"
#include "GameNetManager.h"
#include "IActorBody.h"
#include "gamemode/GameMode_CameraConfig.h"
#include "chunkio.h"
#include "IPlayerControl.h"
#include "IGameMode.h"
#include "blocks/BlockMaterialMgr.h"
#include "container_world.h"
#include "physics/OgrePhysXManager.h"

#include <chrono>

#include "Platforms/PlatformInterface.h"
#include "OgreTimer.h"
#include "GameCamera.h"
#include "CameraManager.h"
#include "MpActorManager.h"

#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "CustomMotionMgr.h"
#include "ImportCustomModelMgr.h"

#include "IRecordInterface.h"
#include "IClientGameManagerInterface.h"
#include "File/DirVisitor.h"
#include "EffectManager.h"
#include "ObjectEventManager.h"

#include "MpGameSurviveCdnResMgr.h"
#include "genCustomModel.h"


#include "ClientInfoProxy.h"
#include "SandboxGlobalNotify.h"

#ifdef IWORLD_SERVER_BUILD
#include "ICloudProxy.h"
#endif

#include "DangerNightManagerInterface.h"
#include "TemperatureManagerInterface.h"
#include "RadiationManagerInterface.h"
#include "StatisticsManagerProxy.h"
#include "WorldRender.h"
#include "ClientActorHelper.h"

#include "EcosysBigBuildBuilder.h"
#include "EcosysUnit_DesertVillage.h"

#include "CommonUtil.h"
#include "SandboxGameMapHost.h"
#include "SandboxCfg.h"

#ifdef BUILD_MINI_EDITOR_APP
#include "Core/Engine.h"
#include "EditorCbeScene.h"
#include "EditorOmodScene.h"
#include "EditorCmScene.h"
#include "EditorBluePrintScene.h"
#include "EditorBlockSystem/Extend/NewBluePrintScene.h"
#include "EditorBlockSystem/Extend/NewBoneScene.h"
#endif

#include "WaterPressureManager.h"
#include "EcosysUnit_FishingVillageBuild.h"
#include "EcosysUnit_ShipWrecks.h"
#include "EcosysUnit_IsLandBuild.h"
#include "SandboxGameStep.h"

#include "Utils/GameInfoProxy.h"
#include "AdventureGuideMgrProxy.h"
#include "EcosysUnit_IceVillage.h"
#include "EcosysUnit_IceAltar.h"
#include "SandboxThreadSaveGroup.h"
#include "IWorldConfigProxy.h"
#include "SandBoxManager.h"
#include "ChunkViewer.h"

#include "EcosysUnit_City.h"
#include "EcosysUnit_RoadBuild.h"
#include "SandboxSceneMgrService.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

static void loadBigBuildParamerFromFB(BuildSaveFillParam& data, const FBSave::BigBuildFillParam* fb)
{
	if (fb == NULL)
	{
		return;
	}
	data.choiceState = fb->choiceState();
	data.clipBlockId = fb->clipBlockId();
	data.maxClipRange = fb->maxClipRange();
	data.fillBlockId = fb->fillBlockId();
	data.maxFillDepth = fb->maxFillDepth();
	data.progress = fb->progress();
	data.maxFillDepth = fb->maxDropHeight();
}

static  flatbuffers::Offset<FBSave::BigBuildFillParam> saveBigBuildParamerToFB(BuildSaveFillParam& data, flatbuffers::FlatBufferBuilder& builder)
{
	return FBSave::CreateBigBuildFillParam(builder, data.clipBlockId, data.maxClipRange, data.fillBlockId, data.maxFillDepth, data.choiceState, data.progress, data.maxFillDepth);
}

static IClientGameInterface* getUGCGame()
{
	auto pCurGame = GetIClientGameManagerInterface()->getICurGame();
	if (!pCurGame)
		return nullptr;

	// 未获取到正确的游戏类，尝试获取
	if (strcmp(pCurGame->getName(), "MainMenuStage") == 0)
	{
		if (GetClientInfoProxy()->getMultiPlayer() > GAME_NET_MP_GAME_NOT_INIT)
		{
			pCurGame = GetIClientGameManagerInterface()->getIGame("UGCMpGame");
		}
		else
		{
			pCurGame = GetIClientGameManagerInterface()->getIGame("UGCModeGame");
		}
	}

	if (!pCurGame)
	{
		return nullptr;
	}

	return pCurGame;
}

static void loadBigBuildBasePlaceDataFromFB(BuildVmoBasePlaceData& placeData, const FBSave::BigBuildBasePlaceData* fb)
{
	if (fb == NULL)
	{
		return;
	}
	loadBigBuildParamerFromFB(placeData.fillParam, fb->fillParam());
	placeData.dir = fb->dir();
	placeData.startPos = Coord3ToWCoord(fb->startPos());
	placeData.progress = fb->progress();
	placeData.type = fb->type();
}

static  flatbuffers::Offset<FBSave::BigBuildBasePlaceData> saveBigBuildBasePlaceDataToFB(BuildVmoBasePlaceData& data, flatbuffers::FlatBufferBuilder& builder)
{
	auto startPos = WCoordToCoord3(data.startPos);

	return FBSave::CreateBigBuildBasePlaceData(builder, &startPos, data.type, data.progress, data.dir, saveBigBuildParamerToFB(data.fillParam, builder));
}


extern void GCMemory();

MINIW::GameStatic<MNSandbox::Notify<int>> s_NotifyViewRangeChange(MINIW::kInitManual);
MNSandbox::Notify<int>& GetNotifyPlayerViewRange()
{
	return *s_NotifyViewRangeChange.EnsureInitialized();
}

WorldManager *g_WorldMgr = NULL;

WorldManager::WorldManager(WorldDesc *desc)
	: m_SurviveGameConfig(NULL), m_PrefabSceneVisible(false), m_bExitingWorld(false)
{
	SDB_NETMONITOR_BEGIN

	m_TimeStampServerStart = std::chrono::steady_clock::now();

	m_skillMgr = GetISandboxActorSubsystem()->CreateSkillMgr();
	//LOG_INFO("[TAG_PRO] WorldManager::WorldManager %s", MNSandbox::ToString(Rainbow::GetTimeSinceStartupMS()).c_str());
	auto runMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (runMode == MNSandbox::RunMode::editor_run)
	{
		desc->ForceOpentype = OWTYPE_GAMEMAKER_STUDIO_RUN;
	}
	else if (runMode == MNSandbox::RunMode::editor_edit)
	{
		desc->ForceOpentype = OWTYPE_GAMEMAKER_STUDIO_EDIT;
	}
	else
	{
		if (desc->worldtype == OWTYPE_GAMEMAKER_STUDIO_EDIT || desc->worldtype == OWTYPE_GAMEMAKER_STUDIO_RUN) //此两种模式的地图只支持在MiniStudio中编辑和运行，将地图模式强制改为开发者编辑地图来打开
		{
			desc->ForceOpentype = OWTYPE_GAMEMAKER;
		}
	}
	bool isCreateFromModPackWorld = false;
	MNSandbox::GetGlobalEvent().Emit<bool&, long long>("WorldArchiveMgr_isCreateFromModPackWorldByWid", isCreateFromModPackWorld, desc->worldid);
	if (isCreateFromModPackWorld)
	{
		if (desc->worldtype == OWTYPE_GAMEMAKER)
		{
			desc->ForceOpentype = OWTYPE_GAMEMAKER_RUN;
		}
		else if(desc->worldtype == OWTYPE_GAMEMAKER_STUDIO_EDIT || desc->worldtype == OWTYPE_GAMEMAKER_STUDIO_RUN)
		{
			desc->ForceOpentype = OWTYPE_GAMEMAKER_SANDBOXNODE_RUN;
		}
	}

#ifdef IWORLD_SERVER_BUILD
   auto& info = *GetClientInfoProxy();
   if (atoi(info.getEnterParam("edit_mode")) == 1)
   {
	   desc->ForceOpentype = OWTYPE_GAMEMAKER_STUDIO_EDIT;
   }
#endif

	if (desc->fromowid < 0)  //转换别人的地图为自己的地图
	{
		int olduin = (int)-desc->fromowid;
		init(desc->worldid, desc->fromowid, desc->ForceOpentype != 0 ? desc->ForceOpentype : desc->worldtype, desc->owneruin, desc->realowneruin, desc->createdata, olduin, desc->_specialType, desc->CType, desc->editorSceneSwitch);
		desc->fromowid = 0;
		OWORLD *owdesc = NULL;
		MNSandbox::GetGlobalEvent().Emit<OWORLD*&, long long>("OWorldList_findWorldDesc", owdesc, desc->worldid);
		if (owdesc)
		{
			owdesc->FromOWID = 0;
			if (HOME_GARDEN_WORLD == owdesc->_specialType)
			{
				MNSandbox::GetGlobalEvent().Emit<long long, bool, int>("OWorldList_setHGWorldListDirty", desc->worldid, false, 2 /*UPWDESC_TYPE::UPWDESC_UPDATE*/);
			}
			else
			{
				MNSandbox::GetGlobalEvent().Emit<long long, bool, int>("OWorldList_setWorldListDirty", desc->worldid, false, 2 /*UPWDESC_TYPE::UPWDESC_UPDATE*/);
			}
		}
		if (CustomModelMgr::GetInstancePtr())
			CustomModelMgr::GetInstancePtr()->reEncryptCustomModelData(desc->worldid, olduin, desc->realowneruin, desc->_specialType);

		if (FullyCustomModelMgr::GetInstancePtr())
			FullyCustomModelMgr::GetInstancePtr()->reEncryptFullyCustomModelData(desc->worldid, olduin, desc->realowneruin, desc->realNickName, desc->_specialType);

	}
	else
		init(desc->worldid, desc->fromowid, desc->ForceOpentype != 0 ? desc->ForceOpentype : desc->worldtype, desc->owneruin, desc->realowneruin, desc->createdata, 0, desc->_specialType, desc->CType, desc->editorSceneSwitch);

	int flag = (int)desc->passportflag;
	if (flag > 0) {
		if (flag != 3) {//flag == 3 是通行证到期了
			flag = (flag == 10 ? 2 : 1);
		}
		setDeveloperFlag(flag);
	}
	m_RealOwnerUin = desc->realowneruin;
	m_sOwnerNickName = desc->ownernick;
	m_WorldName = desc->worldname;
	m_WorldOpenState = desc->open;
	m_TempType = desc->TempType;

	MNSandbox::GetGlobalEvent().Emit<long long, int&, long long&, int&>("WorldArchiveMgr_getWorldFissionInfo", desc->worldid, m_fissionType, m_fissionFrom, m_fissionVersion);
	MNSandbox::GetGlobalEvent().Emit<long long, std::string&>("WorldArchiveMgr_getWorldExtraInfoJsonStr", desc->worldid, m_extraInfo);

	
	m_pwid = desc->pwid;
	m_isEasyMode = desc->isEasyMode;
	if (GetClientInfoProxy()->getMultiPlayer() > GAME_NET_MP_GAME_NOT_INIT)
	{
		m_pMpCdnResMgr = ENG_NEW(MpGameSurviveCdnResMgr)(getWorldId(), getSpecialType());
	}
	else
	{
		m_pMpCdnResMgr = nullptr;
	}

	m_isvrTime = GetClientInfoProxy()->getSvrTime();

	m_canDropItem = true;

	m_bAutoSaveFlag = true;
	m_nAutoSaveFlagDurationTicks = 0;
	m_CheckTreePlantTickCD = CHECK_PLANT_TICK;
	m_updategraphicsvec = nullptr;
	m_LockTime = -1;
	
	createEditorScenes();
	//进出地图清理Prefab ShareCache
	//Rainbow::GetAssetManager().GetAssetShareAccessor().Clear();

	m_NewActorMoveLerpSwitch = true;
	m_bHideAllChunks = false;
	MINIW::ScriptVM::game()->callFunction("GameObjectActorTransformLerpSwitch", ">b", &m_NewActorMoveLerpSwitch);

	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("GetCityConfig", SandboxContext(nullptr));
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("GetModBiomeConfig", SandboxContext(nullptr));
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("GetUgcEcosysBuild", SandboxContext(nullptr));
}

WorldManager::WorldManager(WORLD_ID worldid, int worldtype, int owneruin, int realowner, const WorldCreateData &createdata, WORLD_ID fromowid, int specialtype, int ctype, int editorSceneSwitch)
	: m_SurviveGameConfig(NULL), m_PrefabSceneVisible(false), m_bExitingWorld(false)
{
	//LOG_INFO("[TAG_PRO] WorldManager::WorldManager %s", MNSandbox::ToString(Rainbow::GetTimeSinceStartupMS()).c_str());
	SDB_NETMONITOR_BEGIN
	
	m_skillMgr = GetISandboxActorSubsystem()->CreateSkillMgr();
	auto runMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (runMode == MNSandbox::RunMode::editor_run)
	{
		worldtype = OWTYPE_GAMEMAKER_STUDIO_RUN;
	}
	else if (runMode == MNSandbox::RunMode::editor_edit)
	{
		worldtype = OWTYPE_GAMEMAKER_STUDIO_EDIT;
	}
	else
	{
		if (worldtype == OWTYPE_GAMEMAKER_STUDIO_EDIT || worldtype == OWTYPE_GAMEMAKER_STUDIO_RUN) //此两种模式的地图只支持在MiniStudio中编辑和运行，将地图模式强制改为开发者编辑地图来打开
		{
			worldtype = OWTYPE_GAMEMAKER;
		}
	}

	init(worldid, fromowid, worldtype, owneruin, realowner, createdata, 0, specialtype, ctype, editorSceneSwitch);
	m_RealOwnerUin = 0;
	m_sOwnerNickName = "";

	m_WorldName = "";
	m_WorldOpenState = 0;
	m_TempType = 0;
	m_pwid = 0;
	m_isEasyMode = false; //简单模式
	if (GetClientInfoProxy()->getMultiPlayer() > GAME_NET_MP_GAME_NOT_INIT)
	{
		m_pMpCdnResMgr = ENG_NEW(MpGameSurviveCdnResMgr)(getWorldId(), getSpecialType());
	}
	else
	{
		m_pMpCdnResMgr = nullptr;
	}
	m_isvrTime = 0;

	m_canDropItem = true;

	m_bAutoSaveFlag = true;
	m_nAutoSaveFlagDurationTicks = 0;
	m_CheckTreePlantTickCD = CHECK_PLANT_TICK;

	m_updategraphicsvec = nullptr;
	m_LockTime = -1;
	createEditorScenes();
	//进出地图清理Prefab ShareCache
	//Rainbow::GetAssetManager().GetAssetShareAccessor().Clear();

	m_NewActorMoveLerpSwitch = true;
	m_bHideAllChunks = false;
	MINIW::ScriptVM::game()->callFunction("GameObjectActorTransformLerpSwitch", ">b", &m_NewActorMoveLerpSwitch);
	
	SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("GetCityConfig", SandboxContext(nullptr));
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("GetModBiomeConfig", SandboxContext(nullptr));
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("GetUgcEcosysBuild", SandboxContext(nullptr));
}

void WorldManager::GetBlockTemperatureAndLevel(World* world, const WCoord& blockpos, float& temp, int& level)
{
	if (m_pTemperatureMgr)
	{
		m_pTemperatureMgr->GetBlockTemperatureAndLevel(world, blockpos, temp, level);
	}
}

void WorldManager::InitSandboxMgr()
{
	if (!m_pSandbox)
	{
		m_pSandbox = SANDBOX_NEW(WorldMgrSandbox, this);
	}
}

void WorldManager::init(WORLD_ID worldid, WORLD_ID fromowid, int worldtype, int owneruin, int realowner, const WorldCreateData &createData, int reencryptuin, int specialtype, int ctype, int editorSceneSwitch)
{
	InitSandboxMgr();

	m_generalMgrs.Init();
	//SprayPaintMgr::getInstance()->initData();	//20211020 喷漆初始化数据 codeby:柯冠强

	//清除区块缓存文件
	clearHostTrunk();

	#ifndef IWORLD_SERVER_BUILD
	m_ParticleMgr = ENG_NEW(Rainbow::ParticleManager);
	#endif
	m_SaveGlobalTick = 0;
	m_NpcSpawnTime = 0;
	m_LoveAmbassadorSpawnTime = 0;
	m_isEmptyFlatCreated = false;
	m_TimeHour = 8;
	
	setHours(8.0f, true);
	m_DayTimeSpeed = 1;
	GetISandboxActorSubsystem()->ResetObjId();

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldManager_Game_Init", SandboxContext(nullptr));
	if (result.IsExecSuccessed())
	{
		m_pWaterPressMgr = result.GetData_Usertype<WaterPressureManagerInterface>("waterInterface");
		m_pTemperatureMgr = result.GetData_Usertype<TemperatureManagerInterface>("temperatureInterface");
		m_pRadiationMgr = result.GetData_Usertype<RadiationManagerInterface>("radiationInterface");
	}
	else
	{
		Assert(false);
	}

	m_worldId = worldid;
	m_fromOWID = fromowid;
	if (worldtype == OWTYPE_GAMEMAKER_STUDIO_EDIT)
	{
		m_NeedSaveFlags = NEEDSAVE_ALL & (~NEEDSAVE_PLAYERS); //Studio编辑模式不保存老的player的位置
	}
	else
	{
		m_NeedSaveFlags = (m_worldId >= SELECTROLEWORLDID && m_worldId <= NEWBIEWORLDID) ? NEEDSAVE_NULL : NEEDSAVE_ALL;
	}
	
	m_gameMode = worldtype;
	m_editorSceneSwitch = editorSceneSwitch;
	m_ctype = ctype;
	m_ownerUin = owneruin;
	m_worldCreateData = createData;
	m_iDeveloperFlag = 0;
	m_CameraIsInWater = false;
	m_SpawnPoint = WCoord(0,-1,0);
	m_HostRevivePoint = WCoord(0,-1,0);
	m_mapSpawnPoint.clear();
	m_mapHostRevivePoint.clear();
	m_ClientWorldPointMap.clear();
	m_DragonStatuePoint = WCoord(0,-1,0);
	m_nSpecialType = specialtype;

	GetIWorldConfigProxy()->checkOWAuthorUin(worldid, realowner);  //传真实作者给chunk解密之前，校验一下真实作者数据是否正常（有玩家出现realowner为0的情况）
	m_ChunkIOMgr = ENG_NEW(ChunkIOMgr)(worldid, realowner, specialtype);
	if (reencryptuin > 0)
	{
		ChunkIOMgr::reEncryptChunkData(worldid, reencryptuin, realowner, specialtype);
	}
	if (m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT || m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN)
	{

		m_RuleMgr = GetISandboxActorSubsystem()->CreateGameMode(getWorldId());// ENG_NEW(GameMode)(getWorldId());
		m_CustomCamera = ENG_NEW(CameraConfig)();
	}
	else
	{
		m_RuleMgr = NULL;
		m_CustomCamera = NULL;
	}
	//m_SectionMeshThred = new CalcSectionMeshThread();
	//m_SectionMeshThred->start();

	ChunkViewerList::clearDirtyWatchers();

	m_FirstSurviveWorld = (m_gameMode==0 && GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_NOT_INIT && GetIWorldConfigProxy()->getStatistics("createworlds")==1);
	m_RenderEyeMap = 0;
	m_RenderEyePos = WCoord(0,0,0);
	m_RenderEyeDir = Rainbow::Vector3f(0, 0, 1.0f);

	unsigned int defseed = createData.randseed1 ^ createData.randseed2;
	m_DefRandGen = ENG_NEW(ChunkRandGen)(defseed);

	m_TraderCertainID = 0;

	for (int i = 0; i < MAX_MAP; i++)
	{
		m_LandingPoints[i] = WCoord(0, -1, 0);
		m_BossPoints[i] = WCoord(0, 0, 0);
	}

	m_SurviveGameConfig = ENG_NEW(SurviveGameConfig)();
	// 初始化沙盒游戏
	if (m_pSandbox)
	{
		m_pSandbox->InitSandboxGame();
		const std::string worldIdStr = toString(m_worldId);
		if (MNSandbox::Config::GetSingleton().IsSandboxMode())
		{
			MINIW::setCrashTag("studioMap", worldIdStr.c_str());
		}
		if (ThreadSaveGroup::GetSingletonPtr()) {
			ThreadSaveGroup::GetSingletonPtr()->RegisterThread(m_ChunkIOMgr);
		}
	}
	

	WarningStringMsg("WorldManager init gamemode = %s", MNSandbox::ToString(m_gameMode).c_str());

	// 行为树管理器
	m_pBTMgr = GetISandboxActorSubsystem()->CreateBTManager();
	m_pObjEventMgr = ENG_NEW(ObjectEventManager)();

	//GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_VOLCANO_APPEAR, 0, 0, "vacant boss");

	result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("ClientMob_New", SandboxContext(nullptr));
	IClientActor* actor = result.IsExecSuccessed() ? result.GetData_Usertype<IClientActor>("clientmob_ptr") : nullptr;
	if (actor)
		m_AcotorTemplates[actor->getActorType()] = actor;
	else
		Assert(false);

	//20211111 云服伪房主 codeby:liushuxin
	m_GameLeaderUin = 0;
	SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("SandboxMgrBase");
	MNSandbox::Callback callback = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("SandboxMgrBase", nullptr, [&](SandboxContext context) -> SandboxResult {
		SandboxMgrBase* base = (SandboxMgrBase*)context.GetData_Userdata("SandboxMgrBase", "sandboxmgrBase");
		removeManager(base);
		return SandboxResult(nullptr, false);
	});
	m_eventHandlers.insert(std::make_pair("SandboxMgrBase", callback));

	if (isNewSandboxNodeGame())
	{
		MNSandbox::GameStep::GetSingleton().SetAutoSimulation(false);
	}
	else
	{
		MNSandbox::GameStep::GetSingleton().SetAutoSimulation(true);
	}

	m_travelingTraderInfo.bedPos = WCoord::zero;
	m_travelingTraderInfo.housingLevel = 0;
	m_travelingTraderInfo.biome = -1;
	m_travelingTraderInfo.inHome = false;
	m_travelingTraderInfo.lastTimePoint = 0;
	m_travelingTraderInfo.nextTimeLength = 0;
	m_travelingTraderInfo.hunger = 0;
	m_travelingTraderInfo.biomePos = WCoord::zero;
	m_travelingTraderInfo.lastInHomePos = WCoord(0, -1, 0);

	//游商销售商品数据初始化,全设置为0
	m_travelingTraderInfo.traderGoodsDatas.goodsMaxNum = 8;
	
	if (m_gameMode == 7) //20240313，修正简单模式第一版本枚举7的worldtype和埋点冲突的问题，后续枚举7被移除，需要把枚举7的类型转换回0，冒险类型
	{
		m_gameMode = 0;
		GetClientInfoProxy()->setWorldTypeAndSave(m_worldId, m_gameMode);
	}
}

void WorldManager::onInit()
{
	MINIW::ScriptVM::game()->callFunction("onWorldMgrInit", "");

	//喷漆 codeby:Logo
	//ScriptVM::game()->setUserTypePointer("SprayPaintMgr", "SprayPaintMgr", SprayPaintMgr::create());
	GetISandboxActorSubsystem()->CreateSandboxMgr("SprayPaintMgr");
	//危险夜晚 codeby:chenshaobin
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("WorldManager_DangerNight_Create", SandboxContext(nullptr));
	if (!result.IsExecSuccessed())
	{
		Assert(false);
	}
	GetISandboxActorSubsystem()->CreateSandboxMgr("BulletMgr");
	GetISandboxActorSubsystem()->CreateSandboxMgr("MeteorShowerMgr");
	m_skillMgr->init();
}

void WorldManager::onLoad()
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if(i->second)
			i->second->onLoad();
	}
}

void WorldManager::onUpdate(float dt)
{
    OPTICK_EVENT();
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if(i->second)
			i->second->onUpdate(dt);
	}
}

void WorldManager::onTick()
{
    OPTICK_EVENT();
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if(i->second)
			i->second->onTick();
	}
}

void WorldManager::onGameModeChange(int iGameMode)
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if(i->second)
			i->second->onGameModeChange(iGameMode);
	}
}

void WorldManager::onDestroy()
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		auto manager = i->second;
		i->second = NULL;
		if (manager)
			manager->onDestroy();
		//WorldInfoManager是WorldManager New的 这里释放
		if("WorldInfo" == i->first)
		{
			ENG_DELETE(manager);
		}
	}
	m_Manager.clear();
	m_generalMgrs.Release();
	m_skillMgr->release();
    METRIC_GAUGE_SET(app_game_state, 0, { "type", "GamePlay" });
}

void WorldManager::onEnterWorld(MNSandbox::Object* pWorld)
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if (i->second)
			i->second->onEnterWorld(pWorld);
	}
	GetSceneManagerPtr()->SetCurrentScene((pWorld->StaticToCast<World>())->getCurMapID());
}

void WorldManager::onLeaveWorld()
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if (i->second)
			i->second->onLeaveWorld();
	}
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		MINIW::rmCrashTag("studioMap");
	}
}

void WorldManager::setEnableDangerNight(bool flag)
{
	m_enableDangerNight = flag;
	if (getSandboxMgr("DangerNightMgr"))
	{
		DangerNightManagerInterface* pDNMgr = dynamic_cast<DangerNightManagerInterface*>(getSandboxMgr("DangerNightMgr"));
		if (pDNMgr)
		{
			pDNMgr->setIsEanble(m_enableDangerNight);
		}
	}
	if (m_RuleMgr != nullptr)
	{
		m_RuleMgr->setGameRule(GMRULE_OPEN_DANGERNIGHT, flag ? 1 : 2);
	}
}

void WorldManager::addManager(const std::string& name, SandboxMgrBase* pMgr)
{
	if (!pMgr) { return; }
	std::map<std::string, SandboxMgrBase*>::iterator it = m_Manager.find(name);
	if (it != m_Manager.end())
		return;

	m_Manager[name] = pMgr;
}

void WorldManager::removeManager(SandboxMgrBase* pMgr)
{
	for (auto i = m_Manager.begin(); i != m_Manager.end(); i++)
	{
		if (i->second == pMgr)
		{
			m_Manager.erase(i);
			return;
		}
	}
}


SandboxMgrBase*  WorldManager::getSandboxMgr(const std::string& name)
{
	std::map<std::string, SandboxMgrBase*>::iterator it = m_Manager.find(name);
	if (it == m_Manager.end())
		return NULL;

	return it->second;
}

SandboxMgrBase* WorldManager::GetSandboxMgrOrCreate(const std::string& name)
{
	if (!getSandboxMgr(name))
	{
		return GetISandboxActorSubsystem()->CreateSandboxMgr(name);
	}
	return getSandboxMgr(name);
}
WorldManager::~WorldManager()
{
	SDB_NETMONITOR_END
	m_bExitingWorld = true;
	GCMemory();
	onDestroy();

	if (ThreadSaveGroup::GetSingletonPtr()) {
		ThreadSaveGroup::GetSingletonPtr()->UnRegisterThread(m_ChunkIOMgr);
	}
	ENG_DELETE(m_ChunkIOMgr);

	
	
	if (m_RuleMgr)
		m_RuleMgr->CleanAllDatas(); //ENG_DELETE(m_RuleMgr);
	ENG_DELETE(m_CustomCamera);
	ENG_DELETE(m_pWaterPressMgr);
	ENG_DELETE(m_pTemperatureMgr);
	ENG_DELETE(m_pRadiationMgr);

	for(size_t i=0; i<m_MapData.size(); i++)
	{
		ENG_DELETE(m_MapData[i]);
	}

	if (FullyCustomModelMgr::GetInstancePtr())
		FullyCustomModelMgr::GetInstancePtr()->leaveWorld();

	if (CustomMotionMgr::GetInstancePtr())
		CustomMotionMgr::GetInstancePtr()->leaveWorld();

	if (ImportCustomModelMgr::GetInstancePtr())
	{
		ImportCustomModelMgr::GetInstancePtr()->leaveWorld();
	}

	if (ResourceCenter::GetInstancePtr())
		ResourceCenter::GetInstancePtr()->leaveWorld();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_leaveWorld", SandboxContext(nullptr));

	if (m_Worlds.size() > 0)
	{
		MINIW::ScriptVM::game()->callFunction("clearAllworld", "");
	}
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		iter->second->clear();
		ENG_DELETE(iter->second);
	}
	m_Worlds.clear();

	#ifndef IWORLD_SERVER_BUILD
	ENG_DELETE(m_ParticleMgr);
	#endif

	ENG_DELETE(m_DefRandGen);

	ENG_DELETE(m_SurviveGameConfig);

	/*std::map<std::string, BluePrint*>::iterator bluePrintIter = m_BluePrints.begin();
	for (; bluePrintIter != m_BluePrints.end(); bluePrintIter++)
	{
		ENG_DELETE(bluePrintIter->second);
	}
	m_BluePrints.clear();*/
	//m_WaitLoadBluePrintAreas.clear();

	//GetClientAccountMgr().setMultiPlayer(GAME_NET_MP_GAME_NOT_INIT);

	if (CustomModelMgr::GetInstancePtr())
		CustomModelMgr::GetInstancePtr()->leaveWorld();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_leaveWorld", SandboxContext(nullptr));

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_leaveWorld", SandboxContext(nullptr));

	GetEcosysUnitDesertVillage().leaveWorld();
	// 行为树管理器
	ENG_DELETE(m_pBTMgr);
	ENG_DELETE(m_pObjEventMgr);

	GetStarStationTransferMgrInterface()->leaveWorld();
	GetDesertTradeCaravanMgrInterface()->leaveWorld();
	GetThermalSpringMgrInterface()->leaveWorld();
	//清除区块缓存文件
	//2024-09-09 Studio启动云服调试：以小镇为例：≈330ms
	clearHostTrunk();

	// 释放沙盒游戏
	if (m_pSandbox)
	{
		//2024-09-09 Studio启动云服调试：以小镇为例：≈215ms
		m_pSandbox->ReleaseSandboxGame();
	}

	m_updategraphicsvec = nullptr;

	ENG_DELETE(m_pMpCdnResMgr);
	//GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_VOLCANO_APPEAR, 0, 0);
	auto actor = m_AcotorTemplates.begin();
	while (actor != m_AcotorTemplates.end())
	{
		actor->second->release();
		actor++;
	}

	if (GenCustomModelManager::GetInstancePtr())
	{
		GenCustomModelManager::GetInstance().gcCustomModel(0,true);
	}

	auto iter_ = m_eventHandlers.begin();
	while (iter_ != m_eventHandlers.end())
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe(iter_->first, iter_->second);
		iter_++;
	}

	// 前面已经清理了数据，这里只是释放
	ENG_DELETE(m_RuleMgr);


	if (m_pSandbox)
	{
#ifdef SANDBOX_USE_PROFILE
		m_pSandbox->ProfileSandboxGameObject();
#endif
		SANDBOX_DELETE(m_pSandbox);
	}

	// 退出新手引导
	if (GetAdventureGuideMgrProxy()->isEnterInGuide())
	{
		GetAdventureGuideMgrProxy()->exit();
	};
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("WorldManager_Destructor", SandboxContext(nullptr));
	ENG_DELETE(m_skillMgr);
}

void WorldManager::createEditorScenes()
{
	#ifdef BUILD_MINI_EDITOR_APP
	{
		Rainbow::EngineWorld *pEw = Rainbow::GetWorld();
		if (pEw)
		{
			m_pEditorCbeScene = pEw->CreateGameScene<EditorCbeScene>();
			m_pEditorOmodScene = pEw->CreateGameScene<EditorOmodScene>();
			m_pEditorCmScene = pEw->CreateGameScene<EditorCmScene>(getWorld(m_worldId));
			m_pEditorBluePrintScene = pEw->CreateGameScene<EditorBluePrintScene>(getWorld(m_worldId));
		}
	}
	#endif
}

void WorldManager::setSpawnPointRaw(const WCoord& pt)
{
	m_SpawnPoint = pt;
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Birth));
}

void WorldManager::setRevivePointRaw(const WCoord& pt)
{
	m_HostRevivePoint = pt;
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("refresh_map", MNSandbox::SandboxContext(nullptr).SetData_Number("type", MRT_Revive));
}

bool WorldManager::isRemote()
{  
	/*if(RecordPkgManager::getInstance() == NULL) return false;*/
	if (GetClientInfoProxy() && GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
	{
		return true;
	}
	else return false;
}

bool WorldManager::isGodMode()
{
	return m_gameMode==OWTYPE_CREATE || m_gameMode==OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT;
}

bool WorldManager::isCreativeMode()
{
	return m_gameMode==OWTYPE_CREATE;
}

bool WorldManager::isExtremityMode()
{
	return m_gameMode==OWTYPE_EXTREMITY;
}

bool WorldManager::isEasyMode() //冒险之自由模式
{
	return m_isEasyMode;
}

bool WorldManager::isFreeMode()
{
	return m_gameMode == OWTYPE_FREEMODE;
}

bool WorldManager::isSurviveMode()
{
	return m_gameMode==OWTYPE_SINGLE || m_gameMode==OWTYPE_FREEMODE;
}

bool WorldManager::isCreateRunMode()
{
	return m_gameMode==OWTYPE_CREATE_RUNGAME;
}

bool WorldManager::isGameMakerMode()
{
	return m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT;
}

bool WorldManager::isGameMakerRunMode()
{
	return m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN;
}

bool WorldManager::isCustomGame()
{
	return m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_RUN 
		|| m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN;
}

bool WorldManager::isRecordMode()
{
	return m_gameMode == OWTYPE_RECORD;
}

bool WorldManager::isNewSandboxNodeGame()
{
	return m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT || m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN;
}

bool WorldManager::isNewSandboxNodeStudioEditMode()  
{
	return m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT;
}

bool WorldManager::isNewSandboxNodeGameRunMode()
{
	return m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN;
}

bool WorldManager::isNewSandboxNodeStudioRunMode()
{
	return m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN;
}

void WorldManager::syncBattlePassEventToClientWrap(IClientActor* pActor, std::string stype, int val, std::string extenddata /*= ""*/)
{
	auto pPlayer = dynamic_cast<IClientPlayer*>(pActor);
	if (nullptr != pPlayer)
	{
		syncBattlePassEventToClient(pPlayer, stype, val, extenddata);
	}
}

void WorldManager::syncBattlePassEventToClient(IClientPlayer *player, std::string stype, int val, std::string extenddata /* = "" */)
{
	if(!MINIW::ScriptVM::game())
		return;

	if (player && player->hasUIControl())
	{
		MINIW::ScriptVM::game()->callFunction("NewBattlePassEventOnTrigger", "sis", stype.c_str(), val, extenddata.c_str());
	}
	else if(GetGameNetManagerPtr())
	{
		auto mType = GetGameNetManagerPtr()->getMPGameType();
		 if(mType == GAME_NET_MP_GAME_NOT_INIT)
			MINIW::ScriptVM::game()->callFunction("NewBattlePassEventOnTrigger", "sis", stype.c_str(), val, extenddata.c_str());
		else
		{
			PB_BPEventHC bpEventHC;
			bpEventHC.set_stype(stype);
			bpEventHC.set_val(val);
			bpEventHC.set_extenddata(extenddata);
		
			if (player)
				GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_BATTLEPASS_EVENT_HC, bpEventHC);
			else
				GetGameNetManagerPtr()->sendBroadCast(PB_BATTLEPASS_EVENT_HC, bpEventHC, 0, false);
		}
	}
}

bool WorldManager::GetWorldTemperatureActive()
{
	return getTemperatureMgr()->GetTemperatureActive();
}


// 20210824：增加活动事件  codeby： yaoxinqun
void WorldManager::syncFestivalActivitiesEventToClient(IClientPlayer* player, std::string stype, int toolId, int x, int y, int z, DirectionType face)
{
	if (!MINIW::ScriptVM::game()||!player||!player->hasUIControl())
		return;

	MINIW::ScriptVM::game()->callFunction("FestivalActivitiesEventOnTrigger", "u[IClientPlayer]u[World]siiiii", player, player->GetPlayerWorld(), stype.c_str(), toolId, x,y,z, face);
}


void WorldManager::clearWorldChunk(int mapid, bool clearPhysActor /* = false */)
{
	std::map<int, World *>::iterator iter = m_Worlds.find(mapid);
	if (iter != m_Worlds.end())
	{
		iter->second->clearChunk(clearPhysActor);
		//delete iter->second;;
	}
}

bool WorldManager::toggleGameMode(bool scenefallback/* =true */)
{
	if(GetClientInfoProxy()->getMultiPlayer() != GAME_NET_MP_GAME_NOT_INIT)
	{
		if (HOME_GARDEN_WORLD == m_nSpecialType && OWTYPE_GAMEMAKER_RUN == m_gameMode) //特殊处理，家园地图：联机（玩法模式）-- 单机（编辑模式）
		{
			m_gameMode = OWTYPE_GAMEMAKER;

			MNSandbox::GetGlobalEvent().Emit<long long, int, int>("OWorldList_alterWorldType", m_worldId, m_gameMode, m_nSpecialType);
		}
		return false;
	}

	// 基于整合包创建的地图 不允许转模式
	if (m_fissionType == 1)
	{
		return false;
	}

	if (GetIPlayerControl())
	{
		GetIPlayerControl()->DoCurShotGunModEntry(false);
	}
	//重置个人重生块 需在save前否则会保存至玩法模式初始化中
	if (GetIPlayerControl() && m_gameMode == OWTYPE_GAMEMAKER )
	{
		SandboxResult result;
		SandboxContext sandboxContext;
		GetIPlayerControl()->ControlCastToActor()->Event2().Emit<SandboxResult &, SandboxContext >("revive_getSpawnPoint", result, sandboxContext);

		WCoord spawnpt = result.GetData_UserObject<WCoord>("point");
		if (spawnpt.y >= 0) {
			int newdata = GetIPlayerControl()->getIWorld()->getBlockData(spawnpt) > 0 ? 0 : 1; //修改为不同的data以使得setBlockAll可用
			//auto container = dynamic_cast<WorldEffectContainer *>(GetIPlayerControl()->getIWorld()->getContainerMgr()->getContainer(spawnpt));
			GetIPlayerControl()->getIWorld()->getContainerMgr()->destroyContainer(spawnpt);
			GetIPlayerControl()->getIWorld()->getContainerMgr()->updateTick();
			GetIPlayerControl()->getIWorld()->setBlockAll(spawnpt, BLOCK_PERSONALSPAWN, newdata, 2);
			//GetIPlayerControl()->setSpawnPoint(WCoord(0, -1, 0));
			SandboxContext context;
			context.SetData_UserObject("point", WCoord(0, -1, 0));
			GetIPlayerControl()->ControlCastToActor()->Event().Emit("revive_setSpawnPoint", context);
		}
	}

	bool bRunToEditNoResetWorld = false;
	if (m_gameMode != OWTYPE_GAMEMAKER && m_gameMode != OWTYPE_GAMEMAKER_STUDIO_EDIT)
	{
		if (m_RuleMgr && m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1)
		{
			if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
			{
				GetIPlayerControl()->getIWorld()->clearBlockLightEx();
				// 玩法转编辑清理玩家身上的BUff
				GetIPlayerControl()->clearBuffAndStatus();
			}
			GetStarStationTransferMgrInterface()->clearTransferData();
			GetStarStationTransferMgrInterface()->loadMapStarStationData(m_worldId);

		}
		else if (m_gameMode == OWTYPE_GAMEMAKER_RUN)
		{
			bRunToEditNoResetWorld = true;
		}
		save();
	}

	int oldmode = m_gameMode;

	// 离开玩法模式
	if (m_pSandbox && (!bRunToEditNoResetWorld))
	{
		m_pSandbox->LeavePlayMode(oldmode);
	}

	onGameModeChange(oldmode);

	if(m_gameMode == OWTYPE_CREATE) m_gameMode = OWTYPE_CREATE_RUNGAME;
	else if (m_gameMode == OWTYPE_GAMEMAKER)
	{
		//更新附近块，比如寻路块
		if(GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			WCoord center = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
			GetIPlayerControl()->getIWorld()->markBlockForUpdate(center - WCoord(16, 16, 16), center + WCoord(16, 16, 16), false);
			GetIPlayerControl()->iSetGameScore(0);
		}
		save();
		m_gameMode = OWTYPE_GAMEMAKER_RUN;
		if(m_RuleMgr == NULL) return false;
		//确定初始出生点
		m_RuleMgr->confirmInitialPoint();
		//清理到队伍得分(策划说：切换到玩法模式分数清零)
		m_RuleMgr->clearTeamScore();
		//清理掉所有玩家存储数据
		if (m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 0 && HOME_GARDEN_WORLD != m_nSpecialType)
		{
			std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

			core::string fullpath;
			char path[256];
			sprintf(path, "%s/w%lld/roles", rootpath.c_str(), m_worldId);
			GetFileManager().ToWritePathFull(path, fullpath);
			Rainbow::DirVisitorDeleteFileByExt deleteByExt("p");
			deleteByExt.scanTree(fullpath.c_str());
			m_NeedSaveFlags = m_NeedSaveFlags & (~NEEDSAVE_PLAYERS);

			
		}
		if(m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1) m_NeedSaveFlags = NEEDSAVE_NULL;
	}
	else if(m_gameMode == OWTYPE_CREATE_RUNGAME || m_gameMode == OWTYPE_GAMEMAKER_RUN)
	{
		//更新附近块，比如寻路块
		if(GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			WCoord center = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
			GetIPlayerControl()->getIWorld()->markBlockForUpdate(center - WCoord(16, 16, 16), center + WCoord(16, 16, 16), false);
		}
		WorldDesc *desc = GetClientInfoProxy()->findWorldDesc(m_worldId);

		{
			if(desc==NULL || desc->owneruin!=desc->realowneruin) return false;
		}

		m_gameMode = m_gameMode==OWTYPE_CREATE_RUNGAME ? OWTYPE_CREATE : OWTYPE_GAMEMAKER;

		if(m_gameMode == OWTYPE_GAMEMAKER)
		{
			//if(m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1) m_NeedSaveFlags = NEEDSAVE_GLOBAL;
			//else m_NeedSaveFlags = NEEDSAVE_ALL;
			m_NeedSaveFlags = NEEDSAVE_ALL;
		}
	}
#ifdef BUILD_MINI_EDITOR_APP
	else if (m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT)
	{
		m_gameMode = OWTYPE_GAMEMAKER_STUDIO_RUN;
		if (m_RuleMgr == NULL) return false;
		//确定初始出生点
		m_RuleMgr->confirmInitialPoint();
		//清理掉所有玩家存储数据
		if (m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 0)
		{
			std::string rootpath = "data";
			core::string fullpath;
			char path[256];
			sprintf(path, "%s/w%lld/roles", rootpath.c_str(), m_worldId);
			GetFileManager().ToWritePathFull(path, fullpath);
			Rainbow::DirVisitorDeleteFileByExt deleteByExt("p");
			deleteByExt.scanTree(fullpath.c_str());
			m_NeedSaveFlags = m_NeedSaveFlags & (~NEEDSAVE_PLAYERS);
		}

		//save();

		if (m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1) m_NeedSaveFlags = NEEDSAVE_NULL;
		
	}
	else if (m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN)
	{
		//更新附近块，比如寻路块
		if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			WCoord center = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
			GetIPlayerControl()->getIWorld()->markBlockForUpdate(center - WCoord(16, 16, 16), center + WCoord(16, 16, 16), false);
		}
		WorldDesc* desc = GetClientInfoProxy()->findWorldDesc(m_worldId);
		if (desc == NULL || desc->owneruin != desc->realowneruin) return false;
		m_gameMode = OWTYPE_GAMEMAKER_STUDIO_EDIT;
		m_NeedSaveFlags = NEEDSAVE_ALL & (~NEEDSAVE_PLAYERS); //Studio编辑模式不保存老的player的位置
	}
#endif

	// 进入玩法模式
	if (m_pSandbox && (!bRunToEditNoResetWorld))
	{
		m_pSandbox->EnterPlayMode(m_gameMode);
	}
	
	if(m_gameMode != oldmode)
	{
		MNSandbox::GetGlobalEvent().Emit<long long, int, int>("OWorldList_alterWorldType", m_worldId, m_gameMode, m_nSpecialType);

		//if(GetIPlayerControl())
		//	GetIPlayerControl()->resetTickTime(); //重置设置存档

		// 开发者编辑转玩法  创造地图转冒险
		if (m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_CREATE_RUNGAME || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN || m_gameMode == OWTYPE_SINGLE || m_gameMode == OWTYPE_EXTREMITY || m_gameMode == OWTYPE_FREEMODE)
		{
			// 加载开发者脚本文件			
			if (m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN)
			{
				if (m_RuleMgr == NULL) return false;
				//需在玩家设置队伍之前调用
				//玩法规则: 切换到运行模式, 玩家属性初始化
				if (GetIPlayerControl())
				{
					IClientPlayer* player = dynamic_cast<IClientPlayer*>(GetIPlayerControl());
					m_RuleMgr->initPlayerAttr(player);
					//玩法规则: 基础权限初始化
					m_RuleMgr->initPlayerPermit(player);

					if (!player->iGetTeam())
					{
						player->SetPlayerTeam(getNewPlayerTeamID(player));
						// 观察者事件接口
						ObserverEvent_Player obevent(player->getUin());
						GetObserverEventManager().OnTriggerEvent("Player.JoinTeam", &obevent);
					}
					// 观察者事件接口:对应的功能：任意玩家进入游戏时
					ObserverEvent_Player obevent(player->getUin());
					GetObserverEventManager().OnTriggerEvent("Game.AnyPlayer.EnterGame", &obevent);
				}

				IGameMode* gmaker = this->m_RuleMgr;
				gmaker->setHadShowSelTeamFrame(false);
				if ( gmaker->getNeedShowSelTeamFrame()) // 队伍选择
				{
					gmaker->setCustomGameStage(CGAME_STAGE_SELECTTEAM);
				}
				//Studio运行模式开启倒计时，以供调试GameSeting的CountDown属性 by：Jeff
				if (m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN && gmaker)
				{

					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);

				}
				else
				{
					m_RuleMgr->onGameStart(false);
				}
				if (m_pBTMgr)
				{
					m_pBTMgr->Reload();
				}
				std::map<int, World*>::iterator iter = m_Worlds.begin();
				for (; iter != m_Worlds.end(); iter++)
				{
					World* pworld = iter->second;
					ActorManagerInterface* actorMgr = pworld->getActorMgr();
					if (actorMgr)
						actorMgr->reLoadMobAI();
				}
			}
		}
		else if (m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_CREATE || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT) // 转编辑模式和创造模式清理脚本
		{// 加载开发者脚本文件
			if (m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT)
			{
				// 切换模式清空数据
				GetThermalSpringMgrInterface()->leaveWorld();

				//玩法转编辑模式，场景还原
				if (scenefallback && m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) == 1)
				{
					resetDataByToggleGameMakerMode();

					//先加载玩家脚下的chunk，否则在台阶上转换模式时会因为找不到block高度被卡住  by：Jeff
					if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
					{
						WCoord pos = GetIPlayerControl()->GetPlayerControlPosition();
						GetIPlayerControl()->getIWorld()->syncLoadChunk(CoordDivSection(pos.x), CoordDivSection(pos.z));
					}

				}

			}
		}
		
		nodeTreePlay();
		
		//notify ui
		//ge GetGameEventQue().postSimpleEvent("GE_TOGGLE_GAMEMODE");
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_TOGGLE_GAMEMODE", MNSandbox::SandboxContext(nullptr));
#if BUILD_MINI_EDITOR_APP && SWITCHMODE_OPTIMIZE
		MNSandbox::GetGlobalEvent().Emit("GameModeChange", oldmode, m_gameMode);
#endif
		return true;
	}
	else return false;
}

void WorldManager::nodeTreePlay()
{
	// 编辑模式下需要额外自动执行
	if (m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_CREATE) {
		//进入编辑模式
		auto gameMap = GetCurrentGameMap();
		auto& workspaces = gameMap->GetWorkspaces();
		for (auto& work : workspaces)
		{
			if (work && work->GetScene()) {
				auto legacyActorRoot = gameMap->GetServiceNodeByID(work->GetScene()->GetSceneid() + MNSandbox::SdbSceneManager::ms_workspaceLegacyNodeid);
				if (legacyActorRoot)
				{
					legacyActorRoot->BeginPlay();
				}
			}
		}
	}
}

bool WorldManager::hostToggleMpGameMode()
{
	if (GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_NOT_INIT
		|| HOME_GARDEN_WORLD == m_nSpecialType
		|| (OWTYPE_CREATE != m_gameMode && OWTYPE_CREATE_RUNGAME != m_gameMode))
	{
		return false;
	}

	WorldDesc* desc = GetClientInfoProxy()->findWorldDesc(m_worldId);
	if (desc == NULL)
		return false;

	{
		if (desc->owneruin != desc->realowneruin)
			return false;
	}

	// 基于整合包创建的地图 不允许转模式

	if (4 == desc->OpenCode)
	{
		return false;
	}

	int oldmode = m_gameMode;

	if (!OwTypeCreateModeSwith())
		return false;

	if (m_gameMode != oldmode)
	{
		MNSandbox::GetGlobalEvent().Emit<long long, int, int>("OWorldList_alterWorldType", m_worldId, m_gameMode, m_nSpecialType);

		//notify ui
		//ge GetGameEventQue().postSimpleEvent("GE_TOGGLE_GAMEMODE");
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_TOGGLE_GAMEMODE", MNSandbox::SandboxContext(nullptr));
		return true;
	}
	else
		return false;
}


bool WorldManager::clientToggleMpGameMode(const int oldMode, const int newMode)
{
	if (GetClientInfoProxy()->getMultiPlayer() != GAME_NET_MP_GAME_CLIENT
		|| NORMAL_WORLD != m_nSpecialType
		|| (OWTYPE_CREATE != m_gameMode && OWTYPE_CREATE_RUNGAME != m_gameMode))
	{
		return false;
	}

	if (newMode == m_gameMode)
		return true;

	if (oldMode != m_gameMode)
		return false;


	int oldmode = m_gameMode;

	if (!OwTypeCreateModeSwith())
		return false;

	if (m_gameMode != oldmode)
	{
		//notify ui
		//GetGameEventQue().postSimpleEvent("GE_TOGGLE_GAMEMODE");
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_TOGGLE_GAMEMODE", MNSandbox::SandboxContext(nullptr));
		return true;
	}
	else
		return false;
}

bool WorldManager::OwTypeCreateModeSwith()
{
	int oldmode = m_gameMode;

	if (m_gameMode == OWTYPE_CREATE)
		m_gameMode = OWTYPE_CREATE_RUNGAME;
	else if (m_gameMode == OWTYPE_CREATE_RUNGAME)
	{
		//更新附近块，比如寻路块
		if (GetIPlayerControl())
		{
			WCoord center = CoordDivBlock(GetIPlayerControl()->GetPlayerControlPosition());
			GetIPlayerControl()->getIWorld()->markBlockForUpdate(center - WCoord(16, 16, 16), center + WCoord(16, 16, 16), false);
		}

		m_gameMode = OWTYPE_CREATE;
	}
	else
	{
		return false;
	}
	onGameModeChange(oldmode);

	return oldmode != m_gameMode;
}

void WorldManager::toggleDayNight()
{
	int n = isMainworldDaytime() ? TICKS_NIGHTTIME : TICKS_DAYTIME;

	int t = m_DayNightTime + n;
	setDayTime(t - (t%n));
}

void WorldManager::toggleDayNightEx()
{
	int n = isMainworldDaytime() ? TICKS_NIGHTTIME : TICKS_DAYTIME;

	int t = m_DayNightTime + n;
	setDayTime(t - (t % n));

	int addTime = n - (t % n);
	m_WorldTime += addTime;
}

void WorldManager::resetRandSeed()
{
	time_t t;
	time(&t);

	m_worldCreateData.randseed1 = Rainbow::Timer::getSystemTick();
	m_worldCreateData.randseed2 = (unsigned int)t;
}



void WorldManager::getSpawnPointEx(int &x, int &y, int &z, World *pWorld)
{
	std::map<World *, WCoord>::iterator iter = m_mapSpawnPoint.find(pWorld);
	if (iter != m_mapSpawnPoint.end())
	{
		x = iter->second.x;
		y = iter->second.y;
		z = iter->second.z;

		return ;
	}

	x = m_SpawnPoint.x;
	y = m_SpawnPoint.y;
	z = m_SpawnPoint.z;

	return;
}

void WorldManager::setClientAccountWorldPointMap(int uin, int mapid, WCoord spawnpoint/* = WCoord(0, -1, 0)*/, WCoord revivepoint/* = WCoord(0, -1, 0)*/)
{
	IT_CLIENTWPMAP itMap = m_ClientWorldPointMap.find(uin);
	if(itMap != m_ClientWorldPointMap.end())    
	{
		bool bExist = false;
		for (size_t i = 0; i < (itMap->second).size(); i++)
		{
			AccountWorldPointInfo& src = (itMap->second)[i];
			if (src.mapid == mapid)
			{
				bExist = true;
				if (spawnpoint != WCoord(0, -1, 0))
				{
					src.spawnpoint = spawnpoint;
				}
				if (revivepoint != WCoord(0, -1, 0))
				{
					src.revivepoint = revivepoint;
				}

				break;
			}
		}

		if (!bExist)
		{
			AccountWorldPointInfo src;
			src.mapid = mapid;
			src.spawnpoint = spawnpoint;
			src.revivepoint = revivepoint;
			
			(itMap->second).push_back(src);
		}
	}
	else
	{
		AccountWorldPointInfo src;
		src.mapid = mapid;
		src.spawnpoint = spawnpoint;
		src.revivepoint = revivepoint;
		
		std::vector<AccountWorldPointInfo>AccountWorldPoint;
		AccountWorldPoint.resize(0);
		AccountWorldPoint.push_back(src);

		m_ClientWorldPointMap.insert(make_pair(uin, AccountWorldPoint));
	}
}

void WorldManager::setSpawnPointEx(int x, int y, int z, World *pWorld)
{
	setSpawnPointEx(WCoord(x, y, z), pWorld);
}

void WorldManager::prepareTick()
{
	OPTICK_EVENT();
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		if (pworld != nullptr)
		{
			pworld->prepareTick();
		}
		//pworld->getActorMgr()->prepareTick();
	}
}

bool WorldManager::isMainworldDaytime()
{
	int t = m_DayNightTime % TICKS_ONEDAY;
	return t>=0 && t<TICKS_DAYTIME;
}

void WorldManager::tick()
{
	OPTICK_EVENT("WorldManager::tick")
    METRIC_PROFILER("WorldManager::tick");

	auto nowSteady = std::chrono::steady_clock::now(); // 或 high_resolution_clock
    m_TimeStampSinceStart = std::chrono::duration_cast<std::chrono::milliseconds>((nowSteady - m_TimeStampServerStart)).count();

	auto nowSystem = std::chrono::system_clock::now();
	m_CurrentTimeStamp = std::chrono::duration_cast<std::chrono::milliseconds>(nowSystem.time_since_epoch()).count(); // 等价于 gettimeofday()

	m_TickTime.push(Rainbow::Timer::getSystemTick());
	
#ifdef BUILD_MINI_EDITOR_APP
	if (SandBoxCfg::GetInstance().getLoadingMap() != LoadingMapStatus::loaded)
	{
		return;
	}
#endif // BUILD_MINI_EDITOR_APP
    //记录当前地图名称和地图的运行时间
    METRIC_DYNAMIC_COUNTER_INCREMENT_VALUE(game_map_info, GetFrameTimeManager().GetDeltaTime(), { "world", getCurWorldName() });

	bool bStudioGame = isNewSandboxNodeGame();
	SANDBOXPROFILING_LOG(log, "WorldManager::tick");
	if (!bStudioGame)
	{
		if (GetAdventureGuideMgrProxy()->isEnterInGuide())
		{
			GetAdventureGuideMgrProxy()->tick();
		}
	}

	m_WorldTime += m_DayTimeSpeed;
	
	MINIW::PhysXManager::GetInstance().FrameMove(0.05f);
//#ifdef IWORLD_SERVER_BUILD
//	MINIW::PhysXManager::GetInstance().UpdateVehicleManager(0.05f);
//#endif
	//SprayPaintMgr::getInstance()->tick();	//20211020 喷漆tick codeby:柯冠强

	if(m_worldId>=SELECTROLEWORLDID && m_worldId<=NEWBIEWORLDID) setHours(12.0f);
	else if(m_RuleMgr && m_RuleMgr->getRuleOptionVal(GMRULE_TIMELOCKED)>0)
	{
		if ((m_gameMode == OWTYPE_GAMEMAKER || m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT || m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_RUN) && m_LockTime > 0)
		{
			setHours(m_LockTime);
		}
		else
		{
			setHours(m_RuleMgr->getRuleOptionVal(GMRULE_CURTIME));
		}
	}
	else if (isAdventureMode() && !GetAdventureGuideMgrProxy()->canTimeGo() && m_LockTime > 0)
	{
		setHours(m_LockTime);
	}
	else
	{
		setDayTime(m_DayNightTime + m_DayTimeSpeed);
	}

	if(m_NewActorMoveLerpSwitch)
		onTick();

	bool coconutSkipNight = true;
	bool allsleep = true;
	std::map<int, World*>::iterator iter = m_Worlds.begin();	
	if (!bStudioGame)
	{
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->getSpecialType() == HOME_GARDEN_WORLD)
		{
			//studio 家园地图 不遵循以前的睡床规则
			for (; iter != m_Worlds.end(); ++iter)
			{
				World* pworld = iter->second;
				if (pworld == NULL)
				{
					continue;
				}
				pworld->tick();
			}
			allsleep = false;
			coconutSkipNight = false;
		}
		else
		{
			for (; iter != m_Worlds.end(); iter++)
			{
				World* pworld = iter->second;
				if (pworld == NULL)
				{
					continue;
				}
				pworld->tick();
				if (!pworld->getActorMgr()->areAllPlayersAsleep())
				{
					allsleep = false;
				}
				//被椰子打中会跳过夜晚
				if (!pworld->getActorMgr()->areAllPlayersSkipNight())
				{
					coconutSkipNight = false;
				}
			}
		}
	}
	else
	{
		//studio 家园地图 不遵循以前的睡床规则
		for (; iter != m_Worlds.end(); ++iter)
		{
			World* pworld = iter->second;
			if (pworld == NULL)
			{
				continue;
			}
			pworld->tick();
		}
		allsleep = false;
		coconutSkipNight = false;
	}

	//if (m_SectionMeshThred)
	//{
		//m_SectionMeshThred->checkMustRlease();
	//}

	if (m_pMpCdnResMgr)
		m_pMpCdnResMgr->tick();
	if (!bStudioGame && m_pWaterPressMgr)
		m_pWaterPressMgr->tick();
	if (m_pTemperatureMgr) 
		m_pTemperatureMgr->OnTick();
	if (m_pRadiationMgr)
		m_pRadiationMgr->OnTick();

	if (!m_NewActorMoveLerpSwitch)
		onTick();

	static const std::string amt("AchievementManager_tick");
	MNSandbox::GetGlobalEvent().Emit<>(amt);

#if USE_METRIC_STATICS
    int actors = 0;
    int players = 0;
    EffectManager::EffectStat effStat;
    std::map<int, World*>::iterator world_iter = m_Worlds.begin();
    for (; world_iter != m_Worlds.end(); world_iter++) {
        World* pworld = world_iter->second;
        if (pworld == NULL) continue;
        actors += pworld->getActorMgr()->getActorCount();
        players += pworld->getActorMgr()->getNumPlayer();
        pworld->getEffectMgr()->GetEffectStat(effStat);
    }
    METRIC_GAUGE_SET(game_mapplay_info, m_Worlds.size(), { "type", "worlds" });
    METRIC_GAUGE_SET(game_mapplay_info, actors, { "type", "actors" });
    METRIC_GAUGE_SET(game_mapplay_info, players, { "type", "players" });
    METRIC_GAUGE_SET(game_mapplay_info, effStat.particles, { "type", "effects" });
    METRIC_GAUGE_SET(game_mapplay_info, effStat.sounds, { "type", "sounds" });
    METRIC_GAUGE_SET(game_mapplay_info, effStat.musicGrids, { "type", "music_grids" });
    METRIC_GAUGE_SET(game_mapplay_info, effStat.longSound2D + effStat.longSound2DP, { "type", "2Dsounds" });
    METRIC_GAUGE_SET(game_mapplay_info, effStat.longSound3D, { "type", "3Dsounds" });
#endif

	m_lastTickTime = GetTimeSinceStartup();
	if(GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT)
	{
		if(isGameMakerRunMode()) m_RuleMgr->tickRunModeClient();
		if (m_ChunkIOMgr)	m_ChunkIOMgr->tick();
		return;
	}

	if(isGameMakerRunMode()) m_RuleMgr->tickRunMode();

	//recordPlayerGameTime();

	TickAutoSaveFlag();

	tickSave();

	if (m_ChunkIOMgr)
		m_ChunkIOMgr->tick();

	if(GetClientInfoProxy()->getMultiPlayer() != GAME_NET_MP_GAME_NOT_INIT || GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
	{
#ifdef  IWORLD_SERVER_BUILD
		//云服务器节省流量模式开关
		if ((m_WorldTime % 41) == 0) {
			if (GetClientInfoProxy()->getFlowLevel() > 0) 
			{
				if ( m_WorldTime % 123 ) syncAllPlayerInfo();
			}
			else 
			{
				syncAllPlayerInfo();
			}
		}
#else
		if (m_WorldTime % 41 == 0) syncAllPlayerInfo();
#endif
		if((m_WorldTime % 201) == 0) sendWGlobalUpdate();
	}

	if (coconutSkipNight)
	{
		if (!isMainworldDaytime())
		{
			toggleDayNight();
		}
		std::map<int, World*>::iterator iter = m_Worlds.begin();
		for (; iter != m_Worlds.end(); iter++)
		{
			World* pworld = iter->second;
			if (pworld == NULL) continue;

			pworld->getActorMgr()->wakeSkipNightPlayers();
			pworld->m_Environ->resetRainThunder();
		}
	}
	if (allsleep)
	{
		if (!isMainworldDaytime()) toggleDayNight();

		std::map<int, World *>::iterator iter = m_Worlds.begin();
		for (; iter != m_Worlds.end(); iter++)
		{
			World *pworld = iter->second;
			if (pworld == NULL) continue;

			pworld->getActorMgr()->wakeAllPlayers();
			pworld->m_Environ->resetRainThunder();
		}
	}

	for (size_t i = 0; i < m_ReadyTeleports.size(); i++)
	{
		TeleportInfo &info = m_ReadyTeleports[i];
		IClientPlayer *player = info.player;

		doActualTeleport(info.player, info.targetmap, player->isRocketTeleport() || player->isStarStationTeleporting());

		if (GetGameNetManagerPtr())
		{
			// mapid 可能发生变化，不能使用syncPlayerPos TODO(liusijia)
			PB_ActorTeleportHC actorTeleportHC;
			actorTeleportHC.set_objid(player->iGetObjId());
			actorTeleportHC.set_targetmap(player->iGetCurMapID());

			WCoord pos = player->iGetPosition();
			actorTeleportHC.mutable_targetpos()->set_x(pos.x);
			actorTeleportHC.mutable_targetpos()->set_y(pos.y);
			actorTeleportHC.mutable_targetpos()->set_z(pos.z);

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_ACTOR_TELEPORT_HC, actorTeleportHC);
			player->setTeleportPos(pos);
		}

		if (player->isRocketTeleport())
		{
			player->teleportRidingRocket(player->iGetCurMapID());
			player->setRocketTeleport(false);
		}

		if(player->isStarStationTeleporting())
		{
			player->setIsStarStationTeleporting(false);
			if(player->hasUIControl())
			{
				ScriptVM::game()->callFunction("OnStarStationTeleportSuccess", "i", info.targetmap);
			}
			else
			{
				PB_PlayerTransferByStarStationHC transferPlayerByStarStationHC;
				transferPlayerByStarStationHC.set_uin(player->getUin());
				transferPlayerByStarStationHC.set_destmapid(info.targetmap);
				GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_NOTIFY_PLAYER_TRANSFER_BY_STRSTATION_HC, transferPlayerByStarStationHC);
			}
		}

		player->CastToActor()->release();


		//传送完成之后，player对应的宠物要重新召唤
		SummonPetInfomation petInfo = player->getCurSummonPetInfo();
		player->summonPet(petInfo.monsterid, player->getCurSummonPetID(), petInfo.petid, petInfo.stage, petInfo.stage);
	}

	m_ReadyTeleports.clear();
	// 传送点
	for(size_t i = 0; i < m_ReadyTransfer.size(); i++)
	{
		TransferInfo& info = m_ReadyTransfer[i];
		IClientPlayer *player = info.player;
		doActualTransfer(info.player, info.targetmap, info.destStarStationId, info.position);

		if (GetGameNetManagerPtr())
		{
			// mapid 可能发生变化，不能使用syncPlayerPos TODO(liusijia)
			PB_ActorTeleportHC actorTeleportHC;
			actorTeleportHC.set_objid(player->iGetObjId());
			actorTeleportHC.set_targetmap(player->iGetCurMapID());

			WCoord pos = player->iGetPosition();
			actorTeleportHC.mutable_targetpos()->set_x(pos.x);
			actorTeleportHC.mutable_targetpos()->set_y(pos.y);
			actorTeleportHC.mutable_targetpos()->set_z(pos.z);

			GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_ACTOR_TELEPORT_HC, actorTeleportHC);
			player->setTeleportPos(pos);
		}
		
		if(player->isStarStationTeleporting())
		{
			player->setIsStarStationTeleporting(false);
			if(player->hasUIControl())
			{
				ScriptVM::game()->callFunction("OnStarStationTeleportSuccess", "i", info.targetmap);
			}
			else
			{
				PB_PlayerTransferByStarStationHC transferPlayerByStarStationHC;
				transferPlayerByStarStationHC.set_uin(player->getUin());
				transferPlayerByStarStationHC.set_destmapid(info.targetmap);
				GetGameNetManagerPtr()->sendToClient(player->getUin(), PB_NOTIFY_PLAYER_TRANSFER_BY_STRSTATION_HC, transferPlayerByStarStationHC);
			}
		}
		player->CastToActor()->release();
	}
	m_ReadyTransfer.clear();
	//update MpActorMgr, 同步消息
	for(iter=m_Worlds.begin(); iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		pworld->getMpActorMgr()->tick();
	}

	// 行为树
	m_pBTMgr->Tick();

	if (m_CheckTreePlantTickCD > 0)
	{
		m_CheckTreePlantTickCD--;
		if (m_CheckTreePlantTickCD <= 0)
		{
			m_CheckTreePlantTickCD = CHECK_PLANT_TICK;
			checkHolyTreePlantInfo();
		}
	}

	GetDesertTradeCaravanMgrInterface()->tick();

	if (GetIPlayerControl())
	{
		World* pworld = GetIPlayerControl()->getIWorld();
		if (pworld && !pworld->isRemoteMode() && canSpawnSuviveBoss())
		{
			GetISandboxActorSubsystem()->HandleEnemyContext(pworld,this);
		}
	}
	GetGameNetManagerPtr()->tickActorMoveSync();

	TickTravelingTraderInfo();
#ifdef IWORLD_SERVER_BUILD
	//技能update
	m_skillMgr->update(getTickDelta());
#endif
}

void WorldManager::update(float dtime)
{
	OPTICK_EVENT();
    METRIC_PROFILER("WorldManager::update");
#ifdef BUILD_MINI_EDITOR_APP
	if (SandBoxCfg::GetInstance().getLoadingMap() != LoadingMapStatus::loaded)
	{
		return;
	}
#endif // BUILD_MINI_EDITOR_APP

	if (GetAdventureGuideMgrProxy()->isEnterInGuide())
	{
		GetAdventureGuideMgrProxy()->update(dtime);
	}

	unsigned int dtick = Rainbow::TimeToTick(dtime);

	g_BlockMtlMgr.update(dtick);

	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		iter->second->update(dtime);
	}

	onUpdate(dtime);

	getSkillManager()->update(dtime);

	GetISandboxActorSubsystem()->AddCurActorFrame();
}

//static flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::GameInitItem>>> CreateGameInitItemsVec(flatbuffers::FlatBufferBuilder &builder, GameInitItem *items)
//{
//	flatbuffers::Offset<FBSave::GameInitItem> offsets[MAX_GAMEINIT_ITEMS];
//	int i;
//	for(i=0; i<MAX_GAMEINIT_ITEMS; i++)
//	{
//		if(items[i].itemid == 0) break;
//		offsets[i] = FBSave::CreateGameInitItem(builder, items[i].itemid, items[i].num, items[i].prob);
//	}
//	if(i == 0) return 0;
//	else return builder.CreateVector(offsets, i);
//}

void WorldManager::saveToFlatBuffer(flatbuffers::FlatBufferBuilder &builder)
{
	std::vector<flatbuffers::Offset<FBSave::WorldMap>> maps;
	std::vector<flatbuffers::Offset<FBSave::WorldMapBoss>> bossarray;

	for(size_t i=0; i<m_MapData.size(); i++)
	{
		WorldMapData *srcmap = m_MapData[i];
		if (srcmap == NULL) continue;

		auto portal = WCoordToCoord3(srcmap->portalpos);

		bossarray.resize(srcmap->bosses.size());
		for(size_t iboss=0; iboss<srcmap->bosses.size(); iboss++)
		{
			WorldBossData &srcboss = srcmap->bosses[iboss];

			auto spawnpt = WCoordToCoord3(srcboss.spawnpoint);
			bossarray[iboss] = FBSave::CreateWorldMapBoss(builder, srcboss.defid, srcboss.hp, srcboss.flags, &spawnpt);
		}

		std::vector<FBSave::Coord3> weatherforecastblocks;
		for (unsigned int i = 0; i < srcmap->weatherforecastblocks.size(); i++)
		{
			if (i >= WEATHER_BLOCK_MAX) break;
			weatherforecastblocks.push_back(WCoordToCoord3(srcmap->weatherforecastblocks[i]));
		}

		std::vector<FBSave::Coord3> templepos;
		for (unsigned int i = 0; i < srcmap->templepos.size(); i++)
		{
			templepos.push_back(WCoordToCoord3(srcmap->templepos[i]));
		}
		std::vector<FBSave::Coord3> templedim;
		for (unsigned int i = 0; i < srcmap->templedim.size(); i++)
		{
			templedim.push_back(WCoordToCoord3(srcmap->templedim[i]));
		}
		std::vector<FBSave::Coord3> baldachinepos;
		for (unsigned int i = 0; i < srcmap->baldachinepos.size(); i++)
		{
			baldachinepos.push_back(WCoordToCoord3(srcmap->baldachinepos[i]));
		}
		std::vector<FBSave::Coord3> baldachinedim;
		for (unsigned int i = 0; i < srcmap->baldachinedim.size(); i++)
		{
			baldachinedim.push_back(WCoordToCoord3(srcmap->baldachinedim[i]));
		}
		std::vector<int8_t> godstatues;
		for (unsigned int i = 0; i < srcmap->godstatues.size(); i++)
		{
			godstatues.push_back( (srcmap->godstatues[i])?1:0);
		}

		auto rain = FBSave::CreateRainforestArchitectures(builder,builder.CreateVectorOfStructs(templepos),
			builder.CreateVectorOfStructs(templedim),builder.CreateVectorOfStructs(baldachinepos),builder.CreateVectorOfStructs(baldachinedim),builder.CreateVector(godstatues));


		FBSave::Coord3 godtemplepos = WCoordToCoord3(srcmap->godtemplepos);
		FBSave::Coord3 godstatuepos = WCoordToCoord3(srcmap->godstatuepos);

		flatbuffers::Offset<FBSave::CreatingGodTemple> creatinggod = 0;
		if (srcmap->iscreateBigBuild) {
			creatinggod = FBSave::CreateCreatingGodTemple(builder,
				&godtemplepos,
				&godstatuepos,
				srcmap->godtempleprogress,
				srcmap->godtempleminheight, srcmap->godtempledir);
		}

		std::vector<flatbuffers::Offset<FBSave::UndergroundPalace>> uPalaces;
		if (srcmap->undergroundpalaceList.size())
		{
			for (auto iter : srcmap->undergroundpalaceList)
			{
				auto palaceStartPos = WCoordToCoord3(iter.first);
				auto mazeStartPos = WCoordToCoord3(iter.second.subStartPos);
				auto nextStartPos = WCoordToCoord3(iter.second.subNextStartPos);


				std::vector<flatbuffers::Offset<FBSave::UndergroundPalaceMaze>> maindoors;
				for (auto subiter : iter.second.mainRoomOpenDoorPoses)
				{
					auto maindoorp = WCoordToCoord3(subiter.second);
					auto data = FBSave::CreateUndergroundPalaceMaze(builder, subiter.first, &maindoorp);
					maindoors.push_back(data);
				}
				std::vector<flatbuffers::Offset<FBSave::UndergroundPalaceMaze>> subdoors;
				for (auto subiter : iter.second.subRoomOpenDoorPoses)
				{
					auto subdoorp = WCoordToCoord3(subiter.second);
					auto data = FBSave::CreateUndergroundPalaceMaze(builder, subiter.first, &subdoorp);
					subdoors.push_back(data);
				}
				std::vector<FBSave::Coord3> treasureposes;
				for (auto treasureIter : iter.second.treasurePoses)
				{
					treasureposes.push_back(WCoordToCoord3(treasureIter));
				}

				auto underPalace = FBSave::CreateUndergroundPalace(builder, &palaceStartPos, iter.second.buildStatus, iter.second.buildStepY, iter.second.buildCurArrayIdx,
					iter.second.buildDir, iter.second.roomChannelStatus, &mazeStartPos, &nextStartPos, iter.second.subbuildDir, builder.CreateVector(maindoors)
					, builder.CreateVector(subdoors), builder.CreateVectorOfStructs(treasureposes));
				uPalaces.push_back(underPalace);
			}
		}
		auto duststormInitPos = WCoordToCoord3(srcmap->duststormInitPos);
		auto duststorm = FBSave::CreateDuststormData(builder, srcmap->duststormIsCome, srcmap->duststormTime, srcmap->duststormDir, &duststormInitPos, srcmap->hadActorSandworm);
		auto tempest = FBSave::CreateTempestData(builder, srcmap->tempestIsCome, srcmap->tempestTime, srcmap->tempestDir);
		std::vector<flatbuffers::Offset<FBSave::DesertVillageBuildData>> deserVillageBuilds;
		for (auto p : srcmap->desertVillageBuildList)
		{
			std::vector<flatbuffers::Offset<FBSave::DesertVillageBuildNpcInfo>> npcInfos;
			for (auto info : p.npcInfos)
			{
				npcInfos.push_back(FBSave::CreateDesertVillageBuildNpcInfo(builder, info.x, info.z, info.defId));
			}
			auto startPos = WCoordToCoord3(p.startPos);
			auto villagePos = WCoordToCoord3(p.villagePos);
			deserVillageBuilds.push_back(FBSave::CreateDesertVillageBuildData(builder, &startPos, p.type, p.progress, builder.CreateVector(npcInfos), &villagePos));
		}

		//特殊建筑信息
		std::vector<flatbuffers::Offset<FBSave::SpecialEcosysInfo>> specials;
		auto& specialEcosysInfos = srcmap->specialChunk;
		for (auto it = specialEcosysInfos.begin(); it != specialEcosysInfos.end(); it++)
		{
			auto& infos = it->second;
			std::vector<flatbuffers::Offset<FBSave::SpecialEcosysChunkInfo>> chunkInfo;
			for (auto p : infos)
			{
				auto info = FBSave::CreateSpecialEcosysChunkInfo(builder, p.index.x, p.index.z, p.status, p.range.x, p.range.z);
				chunkInfo.push_back(info);
			}
			auto ecosysInfo = FBSave::CreateSpecialEcosysInfo(builder, it->first, builder.CreateVector(chunkInfo));
			specials.push_back(ecosysInfo);
		}

		std::vector<flatbuffers::Offset<FBSave::FishingVillageBuildData>> fishingVillageBuilds;
		for (auto p : srcmap->fishingVillageBuildList)
		{
			auto startPos = WCoordToCoord3(p.startPos);
			auto villagePos = WCoordToCoord3(p.villagePos);
			fishingVillageBuilds.push_back(FBSave::CreateFishingVillageBuildData(builder, &startPos, p.type, p.progress, &villagePos, saveBigBuildParamerToFB(p.fillParam, builder), p.dir));
		}

		std::vector<flatbuffers::Offset<FBSave::IslandBuildListData>> islandBuildList;
		for (auto p : srcmap->islandBuildList)
		{
			auto startPos = WCoordToCoord3(p.startPos);
			islandBuildList.push_back(FBSave::CreateIslandBuildListData(builder, &startPos, p.type, p.progress, saveBigBuildParamerToFB(p.fillParam, builder)));
		}

		std::vector<flatbuffers::Offset<FBSave::TreasureBoxData>> treasureBoxList;
		for (auto p : srcmap->treasureBoxGenList)
		{
			treasureBoxList.push_back(FBSave::CreateTreasureBoxData(builder, p.index.x, p.index.z));
		}

		std::vector<FBSave::Coord3> treasureBoxExistList;
		for (auto p : srcmap->treasureBoxList)
		{
			treasureBoxExistList.push_back(WCoordToCoord3(p));
		}
		// 沉船群--相关信息落地
		std::vector<flatbuffers::Offset<FBSave::ShipWreckBuildData>> shipWrecksBuild;
		for (auto& p : srcmap->shipWrecksList)
		{
			////几艘配套的小船 相关建筑信息--进度、类型、坐标、朝向
			//std::vector<flatbuffers::Offset<FBSave::ShipWreckData>> smallShipWrecksBuild;
			//for (auto smallship : p.smallShips)
			//{
			//	auto startPos = WCoordToCoord3(smallship.position);

			//	smallShipWrecksBuild.push_back(FBSave::CreateShipWreckData(builder, 
			//														&startPos, 
			//														smallship.progress, 
			//														smallship.dir, 
			//														short(smallship.type)));
			//}
			////大船 相关建筑信息--进度、类型、坐标、朝向
			//auto bigshipPos = WCoordToCoord3(p.bigShip.position);
			//auto bigshipdata = FBSave::CreateShipWreckData(builder,
			//						&bigshipPos,
			//						p.bigShip.progress,
			//						p.bigShip.dir,
			//						short(p.bigShip.type));
			auto shipPos = WCoordToCoord3(p.position);
			shipWrecksBuild.push_back(FBSave::CreateShipWreckBuildData(builder, &shipPos, p.progress, p.dir, short(p.type)));
		}
		auto birthPointPos = WCoordToCoord3(srcmap->birthPointBuild.position);
		auto birthBuild = FBSave::CreateBirthPointBigBuildRecord(builder, &birthPointPos, srcmap->birthPointBuild.progress, srcmap->birthPointBuild.dir,
			saveBigBuildParamerToFB(srcmap->birthPointBuild.fillParam, builder));
		//冰原建筑
		std::vector<flatbuffers::Offset<FBSave::IceBuildData>> iceBuildList;
		for (auto& p : srcmap->iceBuildList)
		{
			iceBuildList.push_back(FBSave::CreateIceBuildData(builder, saveBigBuildBasePlaceDataToFB(p, builder)));
		}

		//新增地形组天气
		std::vector<flatbuffers::Offset<FBSave::WeatherGroupData>> uWeathers;
		if (srcmap->biomeGroupWeatherList.size())
		{
			for (auto& iter : srcmap->biomeGroupWeatherList)
			{
				auto startPos = iter.first;
				auto duststormInitPos = WCoordToCoord3(iter.second.duststormInitPos);
				auto weatherGourpData = FBSave::CreateWeatherGroupData(builder, startPos, iter.second.strength, iter.second.curweathertime, iter.second.curweather, iter.second.nextweathertime,
					iter.second.nextweather, iter.second.isCome, iter.second.dir, &duststormInitPos, iter.second.hadActorSandworm);
				uWeathers.emplace_back(weatherGourpData);
			}
		}

		//added by dongjianan 2023.7.6
		//desc:新增chunkindex相关额外数据
		std::vector<flatbuffers::Offset<FBSave::BuildingAdditionalData>> additionalData;
		for (auto& p : srcmap->additionalDatas)
		{
			additionalData.push_back(FBSave::CreateBuildingAdditionalData(builder, p.index.x, p.index.z, p.datas));
		}

		std::vector<FBSave::Coord3> deathJarBlocks;
		for (unsigned int i = 0; i < srcmap->deathJarBlocks.size(); i++)
		{
			deathJarBlocks.push_back(WCoordToCoord3(srcmap->deathJarBlocks[i]));
		}

		auto destmap = FBSave::CreateWorldMap(builder, srcmap->mapid, &portal, srcmap->raining, srcmap->thundering,
			srcmap->raintime, srcmap->thundertime, srcmap->rainstrength, srcmap->thunderstrength, builder.CreateVector(bossarray),
			srcmap->darking, srcmap->darkstrength, srcmap->curweathertime, srcmap->curweather, builder.CreateVectorOfStructs(weatherforecastblocks),rain,creatinggod,
			srcmap->nextweathertime, srcmap->nextweather, builder.CreateVector(uPalaces), duststorm, builder.CreateVector(deserVillageBuilds), builder.CreateVector(specials), builder.CreateVector(fishingVillageBuilds), builder.CreateVector(islandBuildList), builder.CreateVector(treasureBoxList), builder.CreateVectorOfStructs(treasureBoxExistList),
			tempest, builder.CreateVector(shipWrecksBuild), birthBuild, builder.CreateVector(iceBuildList), builder.CreateVector(uWeathers), builder.CreateVector(additionalData), builder.CreateVectorOfStructs(deathJarBlocks), srcmap->isNeverShowEasyModeTips, srcmap->openGuide);

		maps.push_back(destmap);
	}

	flatbuffers::Offset<FBSave::CustomGameRule> gamerule = 0;
	flatbuffers::Offset<FBSave::CustomGameData> gamedata = 0;
	flatbuffers::Offset<FBSave::GameMakerData> gamemaker = m_RuleMgr!=NULL ? m_RuleMgr->save(builder) : 0;
	flatbuffers::Offset<FBSave::CustomCameraData> customcamera = m_CustomCamera != NULL ? m_CustomCamera->save(builder) : 0;
	flatbuffers::Offset<FBSave::BaseSetterData> setterdata = m_RuleMgr != NULL ? m_RuleMgr->saveBaseSetting(builder) : 0;
	/*
	if(m_CustomGame)
	{
		flatbuffers::Offset<FBSave::GameRuleMod> rulemods[MAX_GAMERULE_MOD];
		for(int i=0; i<m_CGameRule.rulemodnum; i++)
		{
			const GameRuleMod &src = m_CGameRule.rulemods[i];
			rulemods[i] = FBSave::CreateGameRuleMod(builder, src.modtype, builder.CreateVector(src.modvars, MAX_RULEMOD_VAR));
		}

		std::vector<FBSave::Coord3>spawnpts;
		for(int i=0; i<m_CGameRule.teamnum; i++)
		{
			spawnpts.push_back(WCoordToCoord3(m_CGameRule.spawnpoints[i]));
		}

		gamerule = FBSave::CreateCustomGameRule(builder, m_CGameRule.gametime, m_CGameRule.teamnum, m_CGameRule.teammode, m_CGameRule.locktime, m_CGameRule.beginmode, m_CGameRule.savemode, m_CGameRule.mobgen,
			CreateCGVarTypeVec(builder,m_CGameRule.teamvartypes,m_CGameRule.teamvarnum), 
			CreateCGVarTypeVec(builder,m_CGameRule.playervartypes,m_CGameRule.playervarnum),
			CreateCGVarTypeVec(builder,m_CGameRule.globalvartypes,m_CGameRule.globalvarnum),
			builder.CreateVector(rulemods, m_CGameRule.rulemodnum),
			builder.CreateVectorOfStructs(spawnpts),
			CreateGameInitItemsVec(builder,m_CGameRule.inititems),
			CreateGameInitItemsVec(builder,m_CGameRule.reviveitems), m_CGameRule.revivebuff);

		flatbuffers::Offset<FBSave::GameTeam> partyoffsets[MAX_GAME_TEAMS];
		for(int i=0; i<m_CGameRule.teamnum; i++)
		{
			flatbuffers::Offset<flatbuffers::Vector<int32_t>> varoffset = builder.CreateVector<int>(m_CGameData.teams[i].vars, m_CGameRule.teamvarnum);
			partyoffsets[i] = FBSave::CreateGameTeam(builder, varoffset);
		}
		auto globalvaroff = builder. CreateVector(m_CGameData.globalvars, m_CGameRule.globalvarnum);
		gamedata = FBSave::CreateCustomGameData(builder, globalvaroff, 0);
	}*/

	
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getBanItems",
		SandboxContext(nullptr));
	std::vector<int> banitems;
	if (result.IsExecSuccessed())
	{
		banitems = result.GetData_UserObject<std::vector<int>>();
	}

	IClientPlayer * player = dynamic_cast<IClientPlayer*> (GetIClientGameManagerInterface()->getICurGame() ? GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer() : nullptr);
	
	auto spawnpt = WCoordToCoord3((player && player->GetPlayerWorld()) ? getSpawnPointEx(player->GetPlayerWorld()) : m_SpawnPoint);
	auto revivept = WCoordToCoord3((player && player->GetPlayerWorld()) ? getRevivePointEx(player->GetPlayerWorld()) : m_HostRevivePoint);
	auto statuept = WCoordToCoord3(m_DragonStatuePoint);

	std::vector<FBSave::Coord3> totempts;
	std::vector<unsigned char>totemmaps;

	getWorldInfoManager()->fillTotemPointData(totempts, totemmaps);

	std::vector<FBSave::Coord3> landingpts;
	for (int i = 0; i < MAX_MAP; i++)
	{
		landingpts.push_back(WCoordToCoord3(m_LandingPoints[i]));
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BluePrintData>>> blueprintsoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::BluePrintData>> blueprints;
	if (GetBluePrintMgrInterface())
		GetBluePrintMgrInterface()->saveWaitLoadBluePrintAreas(builder, &blueprints);
	blueprintsoffset = builder.CreateVector(blueprints);
	/*SandboxResult result2 = SandboxEventDispatcherManager::GetGlobalInstance().Emit("BluePrintMgr_saveWaitLoadBluePrintAreas",
		SandboxContext(nullptr)
		.SetData_UserObject("builder", &builder));
	if (result2.IsExecSuccessed())
	{
		blueprintsoffset = result2.GetData_UserObject<flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BluePrintData>>>>("blueprintsoffset");
	}*/


	/*auto iter = m_WaitLoadBluePrintAreas.begin();
	for (; iter != m_WaitLoadBluePrintAreas.end(); iter++)
	{
		auto startPos = WCoordToCoord3(iter->second.start);
		auto dim = WCoordToCoord3(iter->second.dim);
		blueprints.push_back(FBSave::CreateBluePrintData(builder, &startPos, &dim, builder.CreateString(iter->first)));
	}*/
	unsigned int hashmd5 = GetStatisticsManagerProxy()->getHashMD5();

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CustomItemData>>> customitemssoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::CustomItemData>> customitems;
	customitems.clear();

	//这里废弃保存 CustomItem的保存改成在CustomModelMgr::saveCustomModelItem保存
	/*for(size_t i = 0; i < GetDefManagerProxy()->m_CustomItems.size(); i++)
	{
		if (!GetDefManagerProxy()->needDelCustomItem(GetDefManagerProxy()->m_CustomItems[i].itemid))
		{
			auto filename = builder.CreateString(GetDefManagerProxy()->m_CustomItems[i].modelfilename);
			auto classname = builder.CreateString(GetDefManagerProxy()->m_CustomItems[i].classname);

			customitems.push_back(FBSave::CreateCustomItemData(builder, GetDefManagerProxy()->m_CustomItems[i].itemid, filename, classname, GetDefManagerProxy()->m_CustomItems[i].type, GetDefManagerProxy()->m_CustomItems[i].involvedid));
		}
	}*/
	customitemssoffset = builder.CreateVector(customitems);

	std::vector<int> blocksettingatt;
	blocksettingatt.clear();
	std::vector<unsigned char> blocksettingattexid;
	blocksettingattexid.clear();
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1)
		g_BlockMtlMgr.saveBlockSettingAtt(blocksettingatt, blocksettingattexid);

	std::vector<flatbuffers::Offset<FBSave::NpcShopSkuData_w>> vNpcShop;
	auto NpcShopInfoList = getWorldInfoManager()->getNpcShopInfoList();
	vNpcShop.clear();
	for (auto it = NpcShopInfoList.begin(); it != NpcShopInfoList.end(); it++) {
		for (auto skuIt = it->second.begin(); skuIt != it->second.end(); skuIt++) {
			vNpcShop.push_back(FBSave::CreateNpcShopSkuData_w(builder, it->first, skuIt->first, skuIt->second.iLeftCount, skuIt->second.iEndTime));
		}
	}

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::NpcShopSkuData_w>>> npcshop = 0;
	if (vNpcShop.size() > 0) {
		npcshop = builder.CreateVector(vNpcShop);
	} 

	//野人/村民相关
	std::vector<flatbuffers::Offset<FBSave::VillageData>> villages;
	std::map<int, VillageInfo> out;
	getWorldInfoManager()->getAllVillageInfo(out);
	for (auto it=out.begin(); it!=out.end(); it++)
	{
		VillageInfo villageInfo = it->second;
		auto villagepos = WCoordToCoord3(villageInfo.villagePoint);
		std::vector<FBSave::Coord3> tabletotems;
		for(size_t i=0; i<villageInfo.villageTotems.size(); i++)
		{
			FBSave::Coord3 tmpTotem = WCoordToCoord3(villageInfo.villageTotems[i]);
			tabletotems.push_back(tmpTotem);
		}

		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VillagerFlagData>>> flagdataoffsets = 0;
		std::vector<flatbuffers::Offset<FBSave::VillagerFlagData>> flagdatavector;
		for(auto iter=villageInfo.villageFlags.begin(); iter!=villageInfo.villageFlags.end(); iter++)
		{
			std::vector<FBSave::Coord3> tableflags;
			for (size_t i=0; i<iter->second.size(); i++)
			{
				FBSave::Coord3 tmpflag = WCoordToCoord3(iter->second[i]);
				tableflags.push_back(tmpflag);
			}
			auto flagdata = FBSave::CreateVillagerFlagData(builder, iter->first, builder.CreateVectorOfStructs(tableflags) );
			flagdatavector.push_back(flagdata);
		}
		flagdataoffsets = builder.CreateVector(flagdatavector);

		std::vector<uint64_t> bindactors;
		for (auto iter=villageInfo.bindVillagers.begin(); iter!=villageInfo.bindVillagers.end(); iter++)
		{
			bindactors.push_back(*iter);
		}

		std::vector<flatbuffers::Offset<FBSave::VillageHelperRelationship>> helperRelationship;
		for (auto iter=villageInfo.helperRelationships.begin(); iter!=villageInfo.helperRelationships.end(); iter++)
		{
			auto relation = FBSave::CreateVillageHelperRelationship(builder, iter->first, iter->second.targetId, iter->second.needNum, iter->second.needType);
			helperRelationship.push_back(relation);
		}

		std::vector<flatbuffers::Offset<FBSave::VillageBedRelationship>> bedRelationship;
		for (auto iter=villageInfo.bedRelationships.begin(); iter!=villageInfo.bedRelationships.end(); iter++)
		{
			auto relation = FBSave::CreateVillageBedRelationship(builder, iter->first, iter->second.blockX, iter->second.blockY, iter->second.blockZ, iter->second.status);
			bedRelationship.push_back(relation);
		}

		auto village = FBSave::CreateVillageData(builder, it->first, &villagepos, builder.CreateVectorOfStructs(tabletotems), flagdataoffsets, builder.CreateVector(bindactors), builder.CreateVector(helperRelationship), builder.CreateVector(bedRelationship));
		villages.push_back(village);
	}
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VillageData>>> villageOffset = builder.CreateVector(villages);

	//设置光照位置信息
	std::vector<flatbuffers::Offset<FBSave::SpcLightValue>> spclightvec;
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1)
	{
		Rainbow::HashTable<WCoord, int, WCoordHashCoder>::Element * it = nullptr;
		//获取去当前世界设置的光照数据
		if (GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			World * pworld = GetIPlayerControl()->getIWorld();
			for (int i = 0; i < (int)pworld->m_BlockLightExHashTable.size(); i++)
			{
				it = pworld->m_BlockLightExHashTable.iterate(it);
				if (it)
				{
					FBSave::Coord3  pos = WCoordToCoord3(it->key);
					int  value = it->value;
					auto relation = FBSave::CreateSpcLightValue(builder, &pos, it->value);
					spclightvec.push_back(relation);
				}
			}
		}
	}
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::SpcLightValue>>> spclights = builder.CreateVector(spclightvec);

	//神圣树种植信息
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::PlantInfo>>> holytreeplantinfooffsets = 0;
	std::vector<flatbuffers::Offset<FBSave::PlantInfo>> holytreeplantinfos;
	for (auto it = m_HolyTreePlantTime.begin(); it != m_HolyTreePlantTime.end(); it++)
	{
		int mapid = it->first;
		for (unsigned int i = 0; i < it->second.size(); i++)
		{
			FBSave::Coord3 tmpflag = WCoordToCoord3(it->second[i].pos);
			auto plantinfo = FBSave::CreatePlantInfo(builder, it->first, &tmpflag, it->second[i].planttime);
			holytreeplantinfos.push_back(plantinfo);
		}
	}
	holytreeplantinfooffsets = builder.CreateVector(holytreeplantinfos);

	//作物的种植信息
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::PlantInfo>>> plantinfooffsets = 0;
	std::vector<flatbuffers::Offset<FBSave::PlantInfo>> plantinfos;
	//m_plantTime
	for (auto it = m_plantTime.begin(); it != m_plantTime.end(); it++)
	{
		int mapid = it->first;
		std::map<WCoord, PlantTimeInfo> infos = it->second;

		for (auto infosit = infos.begin(); infosit != infos.end(); infosit++)
		{
			FBSave::Coord3 tmpflag = WCoordToCoord3(infosit->first);
			auto plantinfo = FBSave::CreatePlantInfo(builder, mapid, &tmpflag, infosit->second.planttime, infosit->second.fertilizeduptime);
			plantinfos.push_back(plantinfo);
		}
	}
	plantinfooffsets = builder.CreateVector(plantinfos);
	char luaData[512] = "";
	MINIW::ScriptVM::game()->callFunction("WorldMgr_OnSaveData", ">s", luaData);
	//游商信息
	flatbuffers::Offset<FBSave::TravelingTraderInfo> travelingTraderInfo = 0;

	//游商售卖的商品信息START
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::TraderGoodsData>>> traderGoodsInfo = 0;	
	std::vector<flatbuffers::Offset<FBSave::TraderGoodsData>> vGoodsInfo;

	for (int i = 0; i < m_travelingTraderInfo.traderGoodsDatas.goodsData.size(); i++) {
		flatbuffers::Offset<FBSave::TraderGoodsData> goodsInfo = 0;
		goodsInfo = FBSave::CreateTraderGoodsData(builder, m_travelingTraderInfo.traderGoodsDatas.goodsData[i].index,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].itemId,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].payItemId,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].price,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].num,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].leftcount,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].enchants);
		vGoodsInfo.push_back(goodsInfo);
	}  
	traderGoodsInfo = builder.CreateVector(vGoodsInfo);
	//游商售卖的商品信息END

	FBSave::Coord3 tmpBedPos = WCoordToCoord3(m_travelingTraderInfo.bedPos);
	FBSave::Coord3 tmpbiomePos = WCoordToCoord3(m_travelingTraderInfo.biomePos);
	FBSave::Coord3 tmpLastInHomePos = WCoordToCoord3(m_travelingTraderInfo.lastInHomePos);
	travelingTraderInfo = FBSave::CreateTravelingTraderInfo(builder, &tmpBedPos, m_travelingTraderInfo.housingLevel, m_travelingTraderInfo.biome, m_travelingTraderInfo.inHome, m_travelingTraderInfo.lastTimePoint, m_travelingTraderInfo.nextTimeLength, m_travelingTraderInfo.hunger, &tmpbiomePos, &tmpLastInHomePos,
		traderGoodsInfo);
	std::vector<flatbuffers::Offset<FBSave::CookBooks>> cookbooksystem;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CookBooks>>> cookbooksystemoffsets = 0;
	for (auto it = m_mapPlayerCookBooks.begin(); it != m_mapPlayerCookBooks.end(); it++)
	{
		unsigned int objid = it->first;
		const std::vector<CookBookInfo>& infos = it->second;
		std::vector<flatbuffers::Offset<FBSave::CookBookInfo>> cookbookinfos;
		for (auto infosit = infos.begin(); infosit != infos.end(); infosit++)
		{
			auto cookbookinfo = FBSave::CreateCookBookInfo(builder, infosit->id, infosit->state);
			cookbookinfos.push_back(cookbookinfo);
		}
		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CookBookInfo>>> cookbookinfosoffs = builder.CreateVector(cookbookinfos);
		auto cookbooksys = FBSave::CreateCookBooks(builder, cookbookinfosoffs, objid);
		cookbooksystem.push_back(cookbooksys);
	}
	cookbooksystemoffsets = builder.CreateVector(cookbooksystem);

	auto wg = FBSave::CreateWorldGlobal(builder, builder.CreateVector(maps)
	, GetISandboxActorSubsystem()->GetCurObjId(), m_NpcSpawnTime, gamerule, gamedata, m_WorldTime
	, &spawnpt, &revivept, 0, &statuept, builder.CreateVector(m_UnlockItems)
	, gamemaker, m_DayNightTime, CommonUtil::GetInstance().GetGameVersionInt(), m_DefRandGen->getSeed()
	, customcamera, builder.CreateVector(banitems), builder.CreateVectorOfStructs(totempts)
	, m_TraderCertainID, builder.CreateVectorOfStructs(landingpts), builder.CreateVector(totemmaps)
	, blueprintsoffset, hashmd5, customitemssoffset,m_LoveAmbassadorSpawnTime,getIsEmptyFlatCreated()
	, blocksettingatt.size() > 0 ? builder.CreateVector(blocksettingatt) : 0
	, npcshop
	, setterdata
	, villageOffset
	,spclights
	, holytreeplantinfooffsets, builder.CreateString(luaData)
	, blocksettingattexid.size() > 0 ? builder.CreateVector(blocksettingattexid) : 0
	, plantinfooffsets, travelingTraderInfo, cookbooksystemoffsets);
	builder.Finish(wg);
}

void WorldManager::saveToFlatBufferFromNet(flatbuffers::FlatBufferBuilder &builder)
{
	std::vector<flatbuffers::Offset<FBSave::WorldMap>> maps;
	std::vector<flatbuffers::Offset<FBSave::WorldMapBoss>> bossarray;

	for(size_t i=0; i<m_MapData.size(); i++)
	{
		WorldMapData *srcmap = m_MapData[i];

		auto portal = WCoordToCoord3(srcmap->portalpos);

		bossarray.resize(srcmap->bosses.size());
		for(size_t iboss=0; iboss<srcmap->bosses.size(); iboss++)
		{
			WorldBossData &srcboss = srcmap->bosses[iboss];

			auto spawnpt = WCoordToCoord3(srcboss.spawnpoint);
			bossarray[iboss] = FBSave::CreateWorldMapBoss(builder, srcboss.defid, srcboss.hp, srcboss.flags, &spawnpt);
		}
		std::vector<FBSave::Coord3> weatherforecastblocks;
		for (unsigned int i = 0; i < srcmap->weatherforecastblocks.size(); i++)
		{
			weatherforecastblocks.push_back(WCoordToCoord3(srcmap->weatherforecastblocks[i]));
		}

		auto destmap = FBSave::CreateWorldMap(builder, srcmap->mapid, &portal, srcmap->raining, srcmap->thundering,
			srcmap->raintime, srcmap->thundertime, srcmap->rainstrength, srcmap->thunderstrength, builder.CreateVector(bossarray),
			srcmap->darking, srcmap->darkstrength, srcmap->curweathertime, srcmap->curweather, builder.CreateVectorOfStructs(weatherforecastblocks),
			0, 0, srcmap->nextweathertime, srcmap->nextweather);

		maps.push_back(destmap);
	}

	flatbuffers::Offset<FBSave::CustomGameRule> gamerule = 0;
	flatbuffers::Offset<FBSave::CustomGameData> gamedata = 0;
	flatbuffers::Offset<FBSave::GameMakerData> gamemaker = m_RuleMgr!=NULL ? m_RuleMgr->save(builder) : 0;
	flatbuffers::Offset<FBSave::CustomCameraData> customcamera = m_CustomCamera != NULL ? m_CustomCamera->save(builder) : 0;
	flatbuffers::Offset<FBSave::BaseSetterData> setterdata = m_RuleMgr != NULL ? m_RuleMgr->saveBaseSetting(builder) : 0;

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getBanItems",
		SandboxContext(nullptr));
	std::vector<int> banitems;
	if (result.IsExecSuccessed())
	{
		banitems = result.GetData_UserObject<std::vector<int>>();
	}

	IPlayerControl * player = dynamic_cast<IPlayerControl*> (GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());
	
	auto spawnpt = WCoordToCoord3((player && player->getIWorld()) ? getSpawnPointEx(player->getIWorld()) : m_SpawnPoint);
	auto revivept = WCoordToCoord3((player && player->getIWorld()) ? getRevivePointEx(player->getIWorld()) : m_HostRevivePoint);
	auto statuept = WCoordToCoord3(m_DragonStatuePoint);

	std::vector<FBSave::Coord3> totempts;
	std::vector<unsigned char>totemmaps;
	getWorldInfoManager()->fillTotemPointData(totempts, totemmaps);

	std::vector<FBSave::Coord3> landingpts;
	for (int i = 0; i < MAX_MAP; i++)
	{
		landingpts.push_back(WCoordToCoord3(m_LandingPoints[i]));
	}

	//flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::BluePrintData>>> blueprintsoffset = 0;
	//std::vector<flatbuffers::Offset<FBSave::BluePrintData>> blueprints;
	//if (GetBluePrintMgrPtr())
		//GetBluePrintMgr().saveWaitLoadBluePrintAreas(builder, &blueprints);

	//auto iter = m_WaitLoadBluePrintAreas.begin();
	//for (; iter != m_WaitLoadBluePrintAreas.end(); iter++)
	//{
	//	auto startPos = WCoordToCoord3(iter->second.start);
	//	auto dim = WCoordToCoord3(iter->second.dim);
	//	blueprints.push_back(FBSave::CreateBluePrintData(builder, &startPos, &dim, builder.CreateString(iter->first)));
	//}
	//blueprintsoffset = builder.CreateVector(blueprints);

	unsigned int hashmd5 = GetStatisticsManagerProxy()->getHashMD5();

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CustomItemData>>> customitemssoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::CustomItemData>> customitems;
	customitems.clear();
	//这里废弃保存 CustomItem的保存改成在CustomModelMgr::saveCustomModelItem保存
	/*for(size_t i = 0; i < GetDefManagerProxy()->m_CustomItems.size(); i++)
	{
		if (!GetDefManagerProxy()->needDelCustomItem(GetDefManagerProxy()->m_CustomItems[i].itemid))
		{
			auto filename = builder.CreateString(GetDefManagerProxy()->m_CustomItems[i].modelfilename);
			auto classname = builder.CreateString(GetDefManagerProxy()->m_CustomItems[i].classname);

			customitems.push_back(FBSave::CreateCustomItemData(builder, GetDefManagerProxy()->m_CustomItems[i].itemid, filename, classname, GetDefManagerProxy()->m_CustomItems[i].type, GetDefManagerProxy()->m_CustomItems[i].involvedid));
		}
	}*/
	customitemssoffset = builder.CreateVector(customitems);

	//野人/村民相关
	std::vector<flatbuffers::Offset<FBSave::VillageData>> villages;
	std::map<int, VillageInfo> out;
	getWorldInfoManager()->getAllVillageInfo(out);
	for (auto it=out.begin(); it!=out.end(); it++)
	{
		VillageInfo villageInfo = it->second;
		auto villagepos = WCoordToCoord3(villageInfo.villagePoint);
		std::vector<FBSave::Coord3> tabletotems;
		for(size_t i=0; i<villageInfo.villageTotems.size(); i++)
		{
			FBSave::Coord3 tmpTotem = WCoordToCoord3(villageInfo.villageTotems[i]);
			tabletotems.push_back(tmpTotem);
		}

		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VillagerFlagData>>> flagdataoffsets = 0;
		std::vector<flatbuffers::Offset<FBSave::VillagerFlagData>> flagdatavector;
		for(auto iter=villageInfo.villageFlags.begin(); iter!=villageInfo.villageFlags.end(); iter++)
		{
			std::vector<FBSave::Coord3> tableflags;
			for (size_t i=0; i<iter->second.size(); i++)
			{
				FBSave::Coord3 tmpflag = WCoordToCoord3(iter->second[i]);
				tableflags.push_back(tmpflag);
			}
			auto flagdata = FBSave::CreateVillagerFlagData(builder, iter->first, builder.CreateVectorOfStructs(tableflags) );
			flagdatavector.push_back(flagdata);
		}
		flagdataoffsets = builder.CreateVector(flagdatavector);

		std::vector<uint64_t> bindactors;
		for (auto iter=villageInfo.bindVillagers.begin(); iter!=villageInfo.bindVillagers.end(); iter++)
		{
			bindactors.push_back(*iter);
		}

		std::vector<flatbuffers::Offset<FBSave::VillageHelperRelationship>> helperRelationship;
		for (auto iter=villageInfo.helperRelationships.begin(); iter!=villageInfo.helperRelationships.end(); iter++)
		{
			auto relation = FBSave::CreateVillageHelperRelationship(builder, iter->first, iter->second.targetId, iter->second.needNum, iter->second.needType);
			helperRelationship.push_back(relation);
		}

		std::vector<flatbuffers::Offset<FBSave::VillageBedRelationship>> bedRelationship;
		for (auto iter=villageInfo.bedRelationships.begin(); iter!=villageInfo.bedRelationships.end(); iter++)
		{
			auto relation = FBSave::CreateVillageBedRelationship(builder, iter->first, iter->second.blockX, iter->second.blockY, iter->second.blockZ, iter->second.status);
			bedRelationship.push_back(relation);
		}

		auto village = FBSave::CreateVillageData(builder, it->first, &villagepos, builder.CreateVectorOfStructs(tabletotems), flagdataoffsets, builder.CreateVector(bindactors), builder.CreateVector(helperRelationship), builder.CreateVector(bedRelationship));
		villages.push_back(village);
	}
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VillageData>>> villageOffset = builder.CreateVector(villages);

	std::vector<int> blocksettingatt;
	blocksettingatt.clear();
	std::vector<unsigned char> blocksettingattexid;
	blocksettingattexid.clear();
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1)
		g_BlockMtlMgr.saveBlockSettingAtt(blocksettingatt, blocksettingattexid);

	//作物的种植信息
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::PlantInfo>>> plantinfooffsets = 0;
	std::vector<flatbuffers::Offset<FBSave::PlantInfo>> plantinfos;
	//m_plantTime
	for (auto it = m_plantTime.begin(); it != m_plantTime.end(); it++)
	{
		int mapid = it->first;
		std::map<WCoord, PlantTimeInfo> infos = it->second;

		// 地图太大了 这里信息太多超长了，先关闭
		// for (auto infosit = infos.begin(); infosit != infos.end(); infosit++)
		// {
		// 	FBSave::Coord3 tmpflag = WCoordToCoord3(infosit->first);
		// 	auto plantinfo = FBSave::CreatePlantInfo(builder, mapid, &tmpflag, infosit->second.planttime, infosit->second.fertilizeduptime);
		// 	plantinfos.push_back(plantinfo);
		// }
	}
	//游商信息
	flatbuffers::Offset<FBSave::TravelingTraderInfo> travelingTraderInfo = 0;

	//游商售卖的商品信息START
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::TraderGoodsData>>> traderGoodsInfo = 0;
	std::vector<flatbuffers::Offset<FBSave::TraderGoodsData>> vGoodsInfo;

	for (int i = 0; i < m_travelingTraderInfo.traderGoodsDatas.goodsData.size(); i++) {
		flatbuffers::Offset<FBSave::TraderGoodsData> goodsInfo = 0;
		goodsInfo = FBSave::CreateTraderGoodsData(builder, m_travelingTraderInfo.traderGoodsDatas.goodsData[i].index,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].itemId,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].payItemId,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].price,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].num,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].leftcount,
			m_travelingTraderInfo.traderGoodsDatas.goodsData[i].enchants);
		vGoodsInfo.push_back(goodsInfo);
	}
	traderGoodsInfo = builder.CreateVector(vGoodsInfo);
	//游商售卖的商品信息END

	FBSave::Coord3 tmpBedPos = WCoordToCoord3(m_travelingTraderInfo.bedPos);
	FBSave::Coord3 tmpbiomePos = WCoordToCoord3(m_travelingTraderInfo.biomePos);
	FBSave::Coord3 tmpLastInHomePos = WCoordToCoord3(m_travelingTraderInfo.lastInHomePos);
	travelingTraderInfo = FBSave::CreateTravelingTraderInfo(builder, &tmpBedPos, m_travelingTraderInfo.housingLevel, m_travelingTraderInfo.biome, m_travelingTraderInfo.inHome, m_travelingTraderInfo.lastTimePoint, m_travelingTraderInfo.nextTimeLength, m_travelingTraderInfo.hunger, &tmpbiomePos, &tmpLastInHomePos,
		traderGoodsInfo);

	//食谱信息
	std::vector<flatbuffers::Offset<FBSave::CookBooks>> cookbooksystem;
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CookBooks>>> cookbooksystemoffsets = 0;
	for (auto it = m_mapPlayerCookBooks.begin(); it != m_mapPlayerCookBooks.end(); it++)
	{
		unsigned int objid = it->first;
		const std::vector<CookBookInfo> & infos = it->second;
		std::vector<flatbuffers::Offset<FBSave::CookBookInfo>> cookbookinfos;
		for (auto infosit = infos.begin(); infosit != infos.end(); infosit++)
		{
			auto cookbookinfo = FBSave::CreateCookBookInfo(builder, infosit->id, infosit->state);
			cookbookinfos.push_back(cookbookinfo);
		}
		flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::CookBookInfo>>> cookbookinfosoffs = builder.CreateVector(cookbookinfos);
		auto cookbooksys = FBSave::CreateCookBooks(builder, cookbookinfosoffs, objid);
		cookbooksystem.push_back(cookbooksys);
	}
	cookbooksystemoffsets = builder.CreateVector(cookbooksystem);




	auto wg = FBSave::CreateWorldGlobal(builder, builder.CreateVector(maps)
	, GetISandboxActorSubsystem()->GetCurObjId(), m_NpcSpawnTime, gamerule, gamedata, m_WorldTime
	, &spawnpt, &revivept, 0, &statuept, builder.CreateVector(m_UnlockItems)
	, gamemaker, m_DayNightTime, CommonUtil::GetInstance().GetGameVersionInt(), m_DefRandGen->getSeed()
	, customcamera, builder.CreateVector(banitems), builder.CreateVectorOfStructs(totempts)
	, m_TraderCertainID, builder.CreateVectorOfStructs(landingpts), builder.CreateVector(totemmaps)
	, 0, hashmd5, customitemssoffset,m_LoveAmbassadorSpawnTime,getIsEmptyFlatCreated()
	, blocksettingatt.size() > 0 ? builder.CreateVector(blocksettingatt) : 0
	, 0
	, setterdata
	, villageOffset, 0, 0, 0
	, blocksettingattexid.size() > 0 ? builder.CreateVector(blocksettingattexid) : 0
	, plantinfooffsets, travelingTraderInfo, cookbooksystemoffsets);
	builder.Finish(wg);
}

void WorldManager::loadFromFlatBuffer(const void *data, size_t datalen)
{
	flatbuffers::Verifier verifier((const uint8_t *)data, datalen);
	if(!FBSave::VerifyWorldGlobalBuffer(verifier))
	{
		GetISandboxActorSubsystem()->ResetObjId(GetISandboxActorSubsystem()->GetCurObjId() + 50000LL);
		return;
	}

	const FBSave::WorldGlobal *worldsave = FBSave::GetWorldGlobal(data);
	if(worldsave == NULL) return;

	int version = worldsave->version();
	if(worldsave->nextobjid() > 0)
	{
		GetISandboxActorSubsystem()->ResetObjId(worldsave->nextobjid() + 100000LL);
	}

	if(worldsave->spawnpoint()) setSpawnPoint(Coord3ToWCoord(worldsave->spawnpoint()));
	if (worldsave->worldtime() > 0)
	{
		int itime = worldsave->worldtime();
		if (getWoldPwid() > 0)
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveTempMgr_NeedResetTime", SandboxContext(nullptr));
			if (result.IsExecSuccessed())
			{
				bool reset = result.GetData_Bool();
				if (reset)
				{
					itime = 0;
					SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveTempMgr_SetReset", SandboxContext(nullptr).SetData_Bool(false));
				}
			}
		}
		setWorldTime(itime);
	}
	if(worldsave->daynighttime() >= 0)
		setDayTime(worldsave->daynighttime());
	else
		setDayTime(m_WorldTime);

	if (worldsave->revivepoint()) setRevivePointRaw(Coord3ToWCoord(worldsave->revivepoint()));
	if(worldsave->statuepoint()) m_DragonStatuePoint = Coord3ToWCoord(worldsave->statuepoint());
	m_NpcSpawnTime = worldsave->npcspawntime();
	m_LoveAmbassadorSpawnTime = worldsave->loveambassadorspawntime();
	setIsEmptyFlatCreated(worldsave->isEmptyFlatCreated());

	if (worldsave->tradercertainid()) m_TraderCertainID = worldsave->tradercertainid();

	auto mapsrc = worldsave->maps();
	if(mapsrc)
	{
		for(size_t i=0; i<mapsrc->size(); i++)
		{
			auto worldmap = mapsrc->Get(i);

			WorldMapData *destmap = getMapData(worldmap->mapid(), true);
			if (worldmap->portal())
			destmap->portalpos = Coord3ToWCoord(worldmap->portal());
			destmap->raining = worldmap->raining()!=0;
			destmap->thundering = worldmap->thundering()!=0;
			destmap->darking = worldmap->darking()!=0;
			destmap->raintime = worldmap->raintime();
			destmap->thundertime = worldmap->thundertime();
			destmap->rainstrength = worldmap->rainstrength();
			destmap->thunderstrength = worldmap->thunderstrength();
			destmap->darkstrength = worldmap->darkstrength();

			destmap->curweathertime = worldmap->curweathertime();
			destmap->curweather = worldmap->curweather();

			destmap->nextweathertime = worldmap->nextweathertime();
			destmap->nextweather = worldmap->nextweather();

			destmap->bosses.clear();
			if(worldmap->bosses())
			{
				for(size_t iboss=0; iboss<worldmap->bosses()->size(); iboss++)
				{
					auto boss = worldmap->bosses()->Get(iboss);

					WorldBossData bossdata;
					bossdata.defid = boss->defid();
					bossdata.hp = boss->hp();
					if(version == 0) bossdata.hp *= 5.0f;

					bossdata.flags = boss->flags();
					bossdata.spawnpoint = Coord3ToWCoord(boss->spawnpoint());

					destmap->bosses.push_back(bossdata);

					if(bossdata.hp <= 0 && bossdata.defid == 3502)
					{
						addUnlockItem(1);
					}
				}
			}

			destmap->weatherforecastblocks.clear();
			if (worldmap->weaterforecastblocks())
			{
				for (size_t i = 0; i < worldmap->weaterforecastblocks()->size(); i++)
				{
					//destmap->weatherforecastblocks.push_back(Coord3ToWCoord(worldmap->weaterforecastblocks()->Get(i)));
					const WCoord& wcoord = Coord3ToWCoord(worldmap->weaterforecastblocks()->Get(i));
					auto iter = std::find(destmap->weatherforecastblocks.begin(), destmap->weatherforecastblocks.end(), wcoord);
					if (iter == destmap->weatherforecastblocks.end())
					{
						if (destmap->weatherforecastblocks.size() < WEATHER_BLOCK_MAX) 
						{
							destmap->weatherforecastblocks.push_back(wcoord);
						}
						else 
						{
							WarningStringMsg("m_WeaterForecastBlocks is large than max : %d", worldmap->weaterforecastblocks()->size());
							break;
						}
					}
				}
			}

			destmap->deathJarBlocks.clear();
			if (worldmap->deathJarBlocks())
			{
				for (size_t i = 0; i < worldmap->deathJarBlocks()->size(); i++)
				{
					const WCoord& wcoord = Coord3ToWCoord(worldmap->deathJarBlocks()->Get(i));
					auto iter = std::find(destmap->deathJarBlocks.begin(), destmap->deathJarBlocks.end(), wcoord);
					if (iter == destmap->deathJarBlocks.end())
					{
						destmap->deathJarBlocks.push_back(wcoord);
					}
				}
			}

			destmap->baldachinedim.clear();
			destmap->baldachinepos.clear();
			destmap->godstatues.clear();
			destmap->templedim.clear();
			destmap->templepos.clear();

			auto rainforest = worldmap->rainforestArchitectures();
			if(rainforest)
			{
				auto baldachinedim = worldmap->rainforestArchitectures()->baldachinedim();
				if(baldachinedim)
				{
					for (size_t  i = 0; i < baldachinedim->size();i++)
					{
						destmap->baldachinedim.push_back(Coord3ToWCoord(baldachinedim->Get(i)));
					}
				}
				auto baldachinepos = worldmap->rainforestArchitectures()->baldachinepos();
				if(baldachinepos)
				{
					for (size_t  i = 0; i < baldachinepos->size();i++)
					{
						destmap->baldachinepos.push_back(Coord3ToWCoord(baldachinepos->Get(i)));
					}
				}
				auto templedim = worldmap->rainforestArchitectures()->templedim();
				if(templedim)
				{
					for (size_t  i = 0; i < templedim->size();i++)
					{
						destmap->templedim.push_back(Coord3ToWCoord(templedim->Get(i)));
					}
				}
				auto templepos = worldmap->rainforestArchitectures()->templepos();
				if(templepos)
				{
					for (size_t  i = 0; i < templepos->size();i++)
					{
						destmap->templepos.push_back(Coord3ToWCoord(templepos->Get(i)));
					}
				}
				auto godstatues = worldmap->rainforestArchitectures()->godstatues();
				if(godstatues)
				{
					for (size_t  i = 0; i < godstatues->size();i++)
					{
						destmap->godstatues.push_back( godstatues->Get(i) > 0);
					}
				}

				auto creatinggodinfo = worldmap->creatinggodtemple();
				if(creatinggodinfo){
					destmap->iscreateBigBuild = true;//creatinggodinfo->iscreategodtemple == 1;
					destmap->godstatuepos = Coord3ToWCoord(creatinggodinfo->godstatuepos());
					destmap->godtemplepos = Coord3ToWCoord(creatinggodinfo->godtemplepos());
					destmap->godtempledir = creatinggodinfo->godtempledir();
					destmap->godtempleminheight = creatinggodinfo->godtempleminheight();
					destmap->godtempleprogress = creatinggodinfo->godtempleprogress();
				}else{
					destmap->iscreateBigBuild = false;
				}
				if (worldmap->undergroundpalaces())
				{
					for (int i = 0; i < worldmap->undergroundpalaces()->size(); i++)
					{
						auto data = worldmap->undergroundpalaces()->Get(i);
						UnderGroundPalaceData udata;
						udata.buildStatus = data->PalaceBuildStatus();
						udata.buildStepY = data->PalaceYStep();
						udata.buildCurArrayIdx = data->PalaceLayerStep();
						udata.buildDir = data->PalaceDir();
						udata.roomChannelStatus = data->MazeBuildStatus();
						udata.subStartPos = Coord3ToWCoord(data->MazeStartPos());
						udata.subNextStartPos = Coord3ToWCoord(data->MazeNextStartPos());
						udata.subbuildDir = data->MazeDir();
						udata.mazeRoomId = data->MazeRoomId();
						if (data->MainDoors())
						{
							for (int j = 0; j < data->MainDoors()->size(); j++)
							{
								auto maindoor = data->MainDoors()->Get(j);
								udata.mainRoomOpenDoorPoses.insert(make_pair(maindoor->DoorsDir(), Coord3ToWCoord(maindoor->DoorPos())));
							}
						}
						if (data->SubDoors())
						{
							for (int j = 0; j < data->SubDoors()->size(); j++)
							{
								auto subdoor = data->SubDoors()->Get(j);
								udata.subRoomOpenDoorPoses.insert(make_pair(subdoor->DoorsDir(), Coord3ToWCoord(subdoor->DoorPos())));
							}
						}
						if (data->TreasurePoses())
						{
							for (int j = 0; j < data->TreasurePoses()->size(); j++)
							{
								auto treasurePos = data->TreasurePoses()->Get(j);
								udata.treasurePoses.push_back(Coord3ToWCoord(treasurePos));
							}
						}
						destmap->undergroundpalaceList.insert(make_pair(Coord3ToWCoord(data->PalaceStartPos()), udata));
					}
				}
			}
			//新增地形组天气
			auto weathergroup = worldmap->weathergroupdata();
			if (weathergroup)
			{
				for (int i = 0; i < weathergroup->size(); i++)
				{
					auto data = weathergroup->Get(i);

					BiomeGroupWeatherData udata;
					udata.strength = data->strength();
					udata.curweathertime = data->curweathertime();
					udata.curweather = data->curweather();
					udata.nextweathertime = data->nextweathertime();
					udata.nextweather = data->nextweather();
					udata.isCome = data->isCome();
					udata.dir = data->dir();
					//可能存在崩溃问题data->duststormInitPos()为空
					if (data->duststormInitPos())
					{
						udata.duststormInitPos = Coord3ToWCoord(data->duststormInitPos());
					}
					else
						udata.duststormInitPos = WCoord();

					udata.hadActorSandworm = data->hadActorSandworm();
					destmap->biomeGroupWeatherList.insert(make_pair(data->startPos(), udata));
				}
			}

			auto duststorm = worldmap->duststorm();
			if (duststorm)
			{
				destmap->duststormDir = duststorm->duststormDir();
				destmap->duststormInitPos = Coord3ToWCoord(duststorm->duststormInitPos());
				destmap->duststormIsCome = duststorm->duststormIsCome();
				destmap->duststormTime = duststorm->duststormTime();
				destmap->hadActorSandworm = duststorm->hadActorSandworm();
			}
			auto tempest = worldmap->tempest();
			if (tempest)
			{
				destmap->tempestDir = tempest->dir();
				destmap->tempestIsCome = tempest->isCome();
				destmap->tempestTime = tempest->time();
			}

			if (worldmap->desertVillageBuild())
			{
				int size = worldmap->desertVillageBuild()->size();
				destmap->desertVillageBuildList.clear();
				destmap->desertVillageBuildList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto& listData = destmap->desertVillageBuildList[i];
					auto data = worldmap->desertVillageBuild()->Get(i);
					listData.progress = data->progress();
					listData.type = (DesrtVillageBuildIngType)(data->type());
					listData.startPos = Coord3ToWCoord(data->startPos());
					if (data->villagePos())
						listData.villagePos = Coord3ToWCoord(data->villagePos());
					int npcSize = data->npcInfo()->size();
					auto npcData = data->npcInfo();
					listData.npcInfos.resize(npcSize);
					for (int j = 0; j < npcSize; j++)
					{
						auto& npcInfo = listData.npcInfos[j];
						auto infoData = npcData->Get(j);
						npcInfo.x = infoData->x();
						npcInfo.z = infoData->z();
						npcInfo.defId = infoData->defid();
					}
				}
			}
			//特殊建筑信息
			auto& specialEcosysInfos = destmap->specialChunk;
			specialEcosysInfos.clear();
			auto specialEcosysOffsetInfos = worldmap->specialEcosys();
			if (specialEcosysOffsetInfos)
			{
				for (int i = 0; i < (int)specialEcosysOffsetInfos->size(); i++)
				{
					auto info = specialEcosysOffsetInfos->Get(i);
					auto chunks = info->chunks();
					int size = chunks->size();
					if (size > 0)
					{
						auto& chunkInfos = specialEcosysInfos[(SpecialEcosysType)info->type()];
						for (int k = 0; k < size; k++)
						{
							auto chunkInfoOffset = chunks->Get(k);
							if (chunkInfoOffset)
							{
								int rangeX = chunkInfoOffset->rangeX() <= 0 ? 10 : chunkInfoOffset->rangeX();
								int rangeZ = chunkInfoOffset->rangeZ() <= 0 ? 10 : chunkInfoOffset->rangeZ();
								chunkInfos.push_back({ chunkInfoOffset->x(), chunkInfoOffset->z(), rangeX, rangeZ, chunkInfoOffset->status()});
							}
						}
					}
				}
			}

			//added by dongjianan 2023.7.6
			//desc:新增chunkindex相关额外数据
			auto& additionalDatas = destmap->additionalDatas;
			additionalDatas.clear();
			auto additionalDatas_src = worldmap->additionalDatas();
			if (additionalDatas_src)
			{
				for (int i = 0; i < (int)additionalDatas_src->size(); i++)
				{
					auto info = additionalDatas_src->Get(i);
					int x = info->x();
					int z = info->z();
					int datas = info->datas();

					additionalDatas.push_back(BuildingAdditionalData(x, z, datas));
				}
			}

			if (worldmap->fishingVillageBuild())
			{
				int size = worldmap->fishingVillageBuild()->size();
				destmap->fishingVillageBuildList.clear();
				destmap->fishingVillageBuildList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto& listData = destmap->fishingVillageBuildList[i];
					auto data = worldmap->fishingVillageBuild()->Get(i);
					listData.progress = data->progress();
					listData.type = (FishingVillageBuildIngType)(data->type());
					listData.startPos = Coord3ToWCoord(data->startPos());
					listData.villagePos = Coord3ToWCoord(data->villagePos());
					listData.dir = data->dir();
					loadBigBuildParamerFromFB(listData.fillParam, data->fillParam());
				}
			}

			if (worldmap->islandBuildList())
			{
				int size = worldmap->islandBuildList()->size();
				destmap->islandBuildList.clear();
				destmap->islandBuildList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto& listData = destmap->islandBuildList[i];
					auto data = worldmap->islandBuildList()->Get(i);
					listData.progress = data->progress();
					listData.type = (IslandBuildType)(data->type());
					listData.startPos = Coord3ToWCoord(data->startPos());
					loadBigBuildParamerFromFB(listData.fillParam, data->fillParam());
				}
			}

			if (worldmap->treasureBoxList())
			{
				int size = worldmap->treasureBoxList()->size();
				destmap->treasureBoxGenList.clear();
				destmap->treasureBoxGenList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto& listData = destmap->treasureBoxGenList[i];
					auto data = worldmap->treasureBoxList()->Get(i);
					listData.index.x = data->x();
					listData.index.z = data->z();
				}
			}

			if (worldmap->treasureBoxExistList())
			{
				int size = worldmap->treasureBoxExistList()->size();
				destmap->treasureBoxList.clear();
				destmap->treasureBoxList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto data = worldmap->treasureBoxExistList()->Get(i);
					destmap->treasureBoxList[i] = Coord3ToWCoord(data);
				}
			}
			// 沉船群--相关信息读取
			if (worldmap->shipWrecksList())
			{
				int size = worldmap->shipWrecksList()->size();
				destmap->shipWrecksList.clear();
				destmap->shipWrecksList.resize(size);
				if (size >= 300)
				{
					//LogInfo("ErrorWarning : too many deep ocean ship wrecks!");
				}
				for (int i = 0; i < size; i++)
				{
					WorldShipWreckBuildData& listData = destmap->shipWrecksList[i];
					auto data = worldmap->shipWrecksList()->Get(i);
					listData.dir = data->dir();
					listData.position = Coord3ToWCoord(data->startPos());
					listData.progress = data->progress();
					listData.type = MapDataShipWreckType(data->type());
					//listData.bigShip.progress = data->BigShipWreck()->progress();
					//listData.bigShip.position = Coord3ToWCoord(data->BigShipWreck()->startPos());
					//listData.bigShip.dir = data->BigShipWreck()->dir();
					//listData.bigShip.type = MapDataShipWreckType(data->BigShipWreck()->type());

					//int smallshipSize = data->SmallShipWreck()->size();

					//listData.smallShips.clear();
					//for (int k = 0; k < smallshipSize; k++)
					//{
					//	// 落地fbs格式
					//	auto smallshipfbs = data->SmallShipWreck()->Get(k);
					//	// 地图数据格式
					//	ShipWreckData smallship;
					//	smallship.dir = smallshipfbs->dir();
					//	smallship.progress = smallshipfbs->progress();
					//	smallship.position = Coord3ToWCoord(smallshipfbs->startPos());
					//	smallship.type = MapDataShipWreckType(smallshipfbs->type());
					//	listData.smallShips.push_back(smallship);
					//}
				}
			}

			//出生点建筑物
			if (worldmap->birthPointBuild())
			{
				destmap->birthPointBuild.progress = worldmap->birthPointBuild()->progress();
				destmap->birthPointBuild.dir = worldmap->birthPointBuild()->dir();
				destmap->birthPointBuild.position = Coord3ToWCoord(worldmap->birthPointBuild()->position());
				loadBigBuildParamerFromFB(destmap->birthPointBuild.fillParam, worldmap->birthPointBuild()->fillParam());
			}
			//冰原建筑物
			if (worldmap->iceBuildList())
			{
				int size = worldmap->iceBuildList()->size();
				destmap->iceBuildList.clear();
				destmap->iceBuildList.resize(size);
				for (int i = 0; i < size; i++)
				{
					auto& listData = destmap->iceBuildList[i];
					auto data = worldmap->iceBuildList()->Get(i);
					loadBigBuildBasePlaceDataFromFB(listData, data->base());
				}
			}

			destmap->openGuide = worldmap->openGuide();
		}
	}
	else
	{
		assert(0);
	}

	auto gamerule = worldsave->gamerule();
	if(gamerule && worldsave->customgame()>0)
	{
		if(m_RuleMgr == NULL) m_RuleMgr = GetISandboxActorSubsystem()->CreateGameMode(getWorldId()); //ENG_NEW(GameMode)(getWorldId());
		if (m_CustomCamera == nullptr) m_CustomCamera = ENG_NEW(CameraConfig)();
		//if(m_SetterMgr == NULL) m_SetterMgr = ENG_NEW(BaseSettingManager)(this);
		if (m_gameMode == OWTYPE_CREATE)
		{
			m_gameMode = OWTYPE_GAMEMAKER;
		}
		else if(!isNewSandboxNodeGame())
		{
			m_gameMode = OWTYPE_GAMEMAKER_RUN;
		}
		
		MNSandbox::GetGlobalEvent().Emit<long long, int, int>("OWorldList_alterWorldType", m_worldId, m_gameMode, m_nSpecialType);
		if(gamerule->mobgen() > 0) m_worldCreateData.flags |= 3;

		m_RuleMgr->loadGameRule(gamerule);
		m_RuleMgr->setNearestGameRule(GMRULE_STARTPLAYERS, 2);
	}

	m_UnlockItems.clear();
	auto unlockitems = worldsave->unlockitems();
	if(unlockitems)
	{
		for(size_t i=0; i<unlockitems->size(); i++)
		{
			m_UnlockItems.push_back(unlockitems->Get(i));
		}
	}
#ifndef IWORLD_SERVER_BUILD
	auto banitems = worldsave->banitems();
	if (banitems)
	{
		for (size_t i = 0; i < banitems->size(); i++)
		{
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_banItem",
				SandboxContext(nullptr).SetData_Number("itemid", banitems->Get(i)));
		}
	}
#endif

	auto gmmaker = worldsave->makerdata();
	if(gmmaker && m_RuleMgr)
	{
		m_RuleMgr->load(gmmaker);
		if (m_RuleMgr) m_RuleMgr->IFitWithOldRules(m_RuleMgr);
		if((isGameMakerRunMode() && m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE)==1)) m_NeedSaveFlags = NEEDSAVE_NULL;
	}

	//如果客机并且timelock了那么应该去使用当前的时间去设置(因为前面代码已经设置过时间了),否则客机进入的时候locktime为-1会使用ruleoption里面的值
	if (GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT && m_RuleMgr && m_RuleMgr->getRuleOptionVal(GMRULE_TIMELOCKED) > 0)
	{
		setLockTime(getTimeHour());
	}

	auto setterdata = worldsave->basesetdata();
	if (setterdata && m_RuleMgr)
	{
		m_RuleMgr->loadBaseSetting(setterdata, version);
	}

	if (m_CustomCamera &&
		worldsave->customcamera())
	{
		m_CustomCamera->load(worldsave->customcamera());
	}

	if(isGameMakerRunMode() && m_RuleMgr && GetClientInfoProxy()->getMultiPlayer()!=GAME_NET_MP_GAME_CLIENT)
	{
		m_RuleMgr->setCustomGameStage(CGAME_STAGE_PREPARE);
		
		int saveMode = (int)m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE);
		if (saveMode == 1) { // 重置模式下才需重新设置时间
			setHours(m_RuleMgr->getRuleOptionVal(GMRULE_CURTIME), true);
		}
	}

	m_DefRandGen->setSeed64(worldsave->randseed());

	auto totempoint = worldsave->totempoint();
	if (totempoint)
	{
		auto totemmaps = worldsave->totempointmap();

		for (size_t i = 0; i < totempoint->size(); i++)
		{
			getWorldInfoManager()->addTotemPoint(Coord3ToWCoord(totempoint->Get(i)), totemmaps ? totemmaps->Get(i) : 0, false);
		}
	}

	auto landingpoints = worldsave->landingpoints();
	if (landingpoints)
	{
		for (size_t i = 0; i < landingpoints->size(); i++)
		{
			m_LandingPoints[i] = Coord3ToWCoord(landingpoints->Get(i));
		}
	}

	//m_BluePrints.clear();
	//m_WaitLoadBluePrintAreas.clear();
	auto blueprints = worldsave->blueprint();
	if (blueprints)
	{
		for (int i = 0; i < (int)blueprints->size(); i++)
		{
			auto src = blueprints->Get(i);

			/*BluePrintArea area;
			area.start = Coord3ToWCoord(src->startpos());
			area.dim = Coord3ToWCoord(src->dim());
			m_WaitLoadBluePrintAreas[src->filename()->c_str()] = area;*/


			if (GetBluePrintMgrInterface())
				GetBluePrintMgrInterface()->addBluePrintArea(src->filename()->c_str(), Coord3ToWCoord(src->startpos()), Coord3ToWCoord(src->dim()));

			/*BluePrint *blueprint = ENG_NEW(BluePrint)(Coord3ToWCoord(src->startpos()), Coord3ToWCoord(src->dim()));
			if (blueprint->load(m_worldId, src->filename()->c_str()))
				m_BluePrints[src->filename()->c_str()] = blueprint;*/
		}
	}

	//GetDefManagerProxy()->m_CustomItems.clear();
	//if (!isRemote())
	//{
	//	//旧的地图数据还是要从这里load进来
	//	GetDefManagerProxy()->loadCustomModelItem();
	//}
	
	auto customitems = worldsave->customitem();
	if (customitems && CustomModelMgr::GetInstancePtr())
	{
		for (int i = 0; i < (int)customitems->size(); i++)
		{
			auto src = customitems->Get(i);
			std::string classname = "default";
			if (src->classname())
				classname = src->classname()->c_str();

			int type = BLOCK_MODEL;
			if (src->type())
				type = src->type();
			short involvedId = 0;
			if (src->involvedid())
				involvedId = src->involvedid();

			CustomModelMgr::GetInstancePtr()->addCustomItemData(src->id(), src->modelname()->c_str(), classname, type, involvedId);
		}

		
		CustomModelMgr::GetInstancePtr()->checkDefByCustomItem();
	}

	//方块类型设置属性 att = settingatt << 18 + blockid
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		auto blocksettingatt = worldsave->blocksettingatt();
		auto blocksettingattexid = worldsave->blocksettingattexid();
		if (blocksettingatt) {
			int blockinfo = 0;
			BlockMaterial* mtl;
			for (unsigned int i = 0; i < blocksettingatt->size(); i++) {
				blockinfo = blocksettingatt->Get(i);
				int blockinfoex = 0;
				if (blocksettingattexid && i < blocksettingattexid->size())
				{
					blockinfoex = blocksettingattexid->Get(i);
				}
				mtl = g_BlockMtlMgr.getMaterial(Block::toMakeBlockIDWithEx(blockinfo, blockinfoex));
				if (!mtl) { continue; }

				mtl->setBlockSettingAtt(blockinfo >> 18);
			}
		}
	}

	auto NpcShopInfoList = getWorldInfoManager()->getNpcShopInfoList();
	NpcShopInfoList.clear();
	if (worldsave->npcshopinfo()) {
		std::string rootpath = GetWorldRootBySpecialType(m_nSpecialType);

		char filepath[256];
		sprintf(filepath, "%s/w%lld/mods/", rootpath.c_str(), m_worldId);
		std::map<int, std::vector<int> > mNpcShopChangeInfo;
		GetDefManagerProxy()->getNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		for (size_t i = 0; i < worldsave->npcshopinfo()->size(); i++)
		{
			auto data = worldsave->npcshopinfo()->Get(i);
			auto itShop = mNpcShopChangeInfo.find(data->shopid());
			if (itShop != mNpcShopChangeInfo.end()) {//reset this shop's sku
				if (itShop->second.size() == 0)
					continue;

				int j = 0;
				for (; j < (int)itShop->second.size(); j++) {
					if (itShop->second[j] == data->skuid()) {
						break;
					}
				}

				if (j != itShop->second.size()) { continue; }
			}
			std::map<int, NpcShopInfo> mNpcShopInfo;
			mNpcShopInfo.clear();

			if (NpcShopInfoList.find(data->shopid()) != NpcShopInfoList.end()) {
				mNpcShopInfo = NpcShopInfoList[data->shopid()];
			}

			NpcShopInfo skuinfo;
			skuinfo.iLeftCount = data->leftcount();
			skuinfo.iEndTime = data->endtime();

			mNpcShopInfo[data->skuid()] = skuinfo;
			NpcShopInfoList[data->shopid()] = mNpcShopInfo;
		}

		if (mNpcShopChangeInfo.size() > 0) {
			mNpcShopChangeInfo.clear();
			GetDefManagerProxy()->setNpcShopChangeConfigStatus(filepath, mNpcShopChangeInfo);
		}
	}

	//村庄
	getWorldInfoManager()->clearVillageInfo();
	auto pvillages = worldsave->villages();
	if (pvillages)
	{
		for (int i=0; i<(int)pvillages->size(); i++)
		{
			auto pData = pvillages->Get(i);
			int ownerid = (int)pData->owneruin();
			VillageInfo villageData;
			villageData.villagePoint = Coord3ToWCoord(pData->villagepos());
			//图腾
			if (pData->villagetotems())
			{
				auto ptotems = pData->villagetotems();
				if (ptotems->size()<2)//这里做个特殊处理，因为当前只会记录一个有效的图腾，如果因为之前的bug产生了非法的数据，这里做下过滤
				{
					for (int j=0; j<(int)ptotems->size(); j++)
					{
						villageData.villageTotems.push_back(Coord3ToWCoord(ptotems->Get(j)));
					}
				}
			}
			//旗帜
			if (pData->villagerflags())
			{
				auto flaglist = pData->villagerflags();
				for(int j=0; j<(int)flaglist->size(); j++)
				{
					auto flagdata = flaglist->Get(j);
					auto poslist = flagdata->flagposes();
					int id = flagdata->blockid();
					std::vector<WCoord> poses;
					for (int k=0; k<(int)poslist->size(); k++)
					{
						poses.push_back(Coord3ToWCoord(poslist->Get(k)));
					}
					villageData.villageFlags.insert(make_pair(id,poses));
				}
			}
			//村民
			if (pData->bindvillagers())
			{
				auto villagerlist = pData->bindvillagers();
				for (int j=0;j<(int)villagerlist->size();j++)
				{
					WORLD_ID id = villagerlist->Get(j);
					villageData.bindVillagers.insert(id);
				}
			}
			//助手工作绑定关系
			if (pData->helperrelationship())
			{
				auto relationshipList = pData->helperrelationship();
				for (int j=0; j<(int)relationshipList->size(); j++)
				{
					auto relation = relationshipList->Get(j);
					VillageHelperTarget target;
					target.targetId = relation->target();
					target.needType = relation->needtype();
					target.needNum = relation->neednum();
					WORLD_ID helperId = relation->helper();
					villageData.helperRelationships.insert(make_pair(helperId, target));
				}
			}
			//床绑定关系
			if (pData->bedrelationship())
			{
				auto relationshipList = pData->bedrelationship();
				for (int j=0; j<(int)relationshipList->size(); j++)
				{
					auto relation = relationshipList->Get(j);
					VillagerBedData data;
					WORLD_ID villager = relation->villager();
					data.blockX = relation->blockx();
					data.blockY = relation->blocky();
					data.blockZ = relation->blockz();
					data.status = relation->status();
					villageData.bedRelationships.insert(make_pair(villager, data));
				}
			}
			getWorldInfoManager()->insertVillageInfo(ownerid, villageData);
		}
	}

	m_SaveBlockLightEx.clear();
	// 设置的光照
	auto spclights = worldsave->spclight();
	if (spclights)
	{
		for (int i = 0; i < (int)(spclights->size()); i++)
		{
			auto light = spclights->Get(i);
			WCoord pos = Coord3ToWCoord (light->SpclightPos());
			m_SaveBlockLightEx[pos] = light->value();
		}
	}

	m_HolyTreePlantTime.clear();
	auto holyTreeplantinfo = worldsave->holytreeplantinfo();
	if (holyTreeplantinfo)
	{
		for (size_t i = 0; i < holyTreeplantinfo->size(); i++)
		{
			auto fInfo = holyTreeplantinfo->Get(i);
			WCoord pos = Coord3ToWCoord(fInfo->pos());
			int mapid = fInfo->mapid();

			PlantInfo info;
			info.pos = pos;
			info.planttime = fInfo->planttime();

			auto iter = m_HolyTreePlantTime.find(mapid);
			if (iter == m_HolyTreePlantTime.end())
			{
				std::vector<PlantInfo> plantInfos;
				plantInfos.push_back(info);

				m_HolyTreePlantTime[mapid] = plantInfos;
			}
			else
			{
				iter->second.push_back(info);
			}
		}
	}
	m_plantTime.clear();
	auto plantinfos = worldsave->plantinfos();
	if (plantinfos)
	{
		for (size_t i = 0; i < plantinfos->size(); i++)
		{
			auto fInfo = plantinfos->Get(i);
			WCoord pos = Coord3ToWCoord(fInfo->pos());
			int mapid = fInfo->mapid();
			auto iter = m_plantTime.find(mapid);
			if (iter == m_plantTime.end())
			{
				std::map<WCoord, PlantTimeInfo> plantInfos;
				PlantTimeInfo timeinfo;
				timeinfo.planttime = fInfo->planttime();
				timeinfo.fertilizeduptime = fInfo->fertilizeduptime();
				plantInfos[pos] = timeinfo;
				m_plantTime[mapid] = plantInfos;
			}
			else
			{
				PlantTimeInfo timeinfo;
				timeinfo.planttime = fInfo->planttime();
				timeinfo.fertilizeduptime = fInfo->fertilizeduptime();
				iter->second[pos]= timeinfo;
			}
		}
	}

	if (worldsave->luadata())
		MINIW::ScriptVM::game()->callFunction("WorldMgr_OnLoadData", "s", worldsave->luadata()->c_str());
	else
		MINIW::ScriptVM::game()->callFunction("WorldMgr_OnLoadData", "s", "");

	//游商信息
	auto* travelingTraderInfo = worldsave->travelingtraderinfo();
	if (travelingTraderInfo)
	{
		m_travelingTraderInfo.bedPos = Coord3ToWCoord(travelingTraderInfo->bedPos());
		m_travelingTraderInfo.housingLevel = travelingTraderInfo->housingLevel();
		m_travelingTraderInfo.biome = travelingTraderInfo->biome();
		m_travelingTraderInfo.inHome = travelingTraderInfo->inHome();
		m_travelingTraderInfo.lastTimePoint = travelingTraderInfo->lasttimePoint();
		m_travelingTraderInfo.nextTimeLength = travelingTraderInfo->nexttimeLength();
		m_travelingTraderInfo.hunger = travelingTraderInfo->hunger();
		m_travelingTraderInfo.biomePos = Coord3ToWCoord(travelingTraderInfo->biomePos());
		m_travelingTraderInfo.lastInHomePos = Coord3ToWCoord(travelingTraderInfo->lastInHomePos());

		//加载游商的售卖商品信息
		auto* traderGoodsData = travelingTraderInfo->traderGoodsData();
		if (traderGoodsData && traderGoodsData->size() > 0) {
			/*如果有存档数据，则先清空下, init的时候预置了数据*/
			m_travelingTraderInfo.traderGoodsDatas.goodsData.clear();
			for (int i = 0; i < traderGoodsData->size(); i++) {
				TraderGoodsInfo goods;
				goods.index     = (*traderGoodsData)[i]->index();
				goods.itemId    = (*traderGoodsData)[i]->itemid();
				goods.payItemId = (*traderGoodsData)[i]->payItemId();
				goods.price     = (*traderGoodsData)[i]->price();
				goods.num       = (*traderGoodsData)[i]->num();
				goods.leftcount = (*traderGoodsData)[i]->leftcount();
				goods.enchants = (*traderGoodsData)[i]->enchants();
				m_travelingTraderInfo.traderGoodsDatas.goodsData.push_back(goods);
			}
		}
		//如果没有存储数据则初始化默认数据
		else {
			loadTravelingTraderGoodsInfo(1);
		}
	}
	
	//食谱信息
	if (worldsave->cookbookinfos())
	{
		auto* list = worldsave->cookbookinfos();
		for (auto listIter = list->begin(); listIter != list->end(); listIter++)
		{
			auto objid = listIter->objid();
			auto objcooks = listIter->info();
			std::vector<CookBookInfo> infos;
			for (auto cooksIter = objcooks->begin(); cooksIter != objcooks->end(); cooksIter++)
			{
				CookBookInfo cookbook;
				cookbook.id = cooksIter->id();
				cookbook.state = cooksIter->state();
				infos.push_back(cookbook);
			}
			m_mapPlayerCookBooks[objid] = infos;
		}
	}
}

void WorldManager::loadGlobal(const PB_OWGlobal &owglobal, bool bin_comppressed)
{
	WCoord spt;
	//restorePos(owglobal.misc().initpos(), &spt);
	GetISandboxActorSubsystem()->RestorePos(owglobal.misc().initpos(), &spt);
	setSpawnPoint(spt);
	setWorldTime(owglobal.svrstart());

	if (bin_comppressed)
	{
		Rainbow::CompressTool ctool(Rainbow::CompressTool::COMPRESS_LZMA);
		int originsize = owglobal.misc().globalflag() & 0xffffff;
		if (originsize == 0) return;

		void *data = malloc(originsize);
		if (ctool.decompress(data, originsize, owglobal.misc().globalbin().bincontent().c_str(), owglobal.misc().globalbin().binlen()))
		{
			loadFromFlatBuffer(data, originsize);
		}
		free(data);
	}
	else loadFromFlatBuffer(owglobal.misc().globalbin().bincontent().c_str(), owglobal.misc().globalbin().binlen());
}

int WorldManager::saveGlobal(PB_OWGlobal *pOWGlobal)
{
	collectGlobalData();

	//memset(&owglobal, 0, sizeof(owglobal));
	pOWGlobal->Clear();

	PB_OWGlobalMisc* pGlobalMisc = pOWGlobal->mutable_misc();
	PB_Pos* pInitPos = pGlobalMisc->mutable_initpos();
	PB_Pos* pRevicePos = pGlobalMisc->mutable_revicepos();
	PB_GlobalBin* pGlobalBin = pGlobalMisc->mutable_globalbin();

	IPlayerControl * player = dynamic_cast<IPlayerControl*>(GetIClientGameManagerInterface()->getICurGame()->getIMainPlayer());
	WCoord tempSpawnPoint = (player && player->getIWorld()) ? getSpawnPointEx(player->getIWorld()) : m_SpawnPoint;

	//storePos(pInitPos, &tempSpawnPoint);
	GetISandboxActorSubsystem()->StorePos(pInitPos, &tempSpawnPoint);
	pInitPos->set_map(0);

	WCoord pos(0, -1, 0);
	//storePos(pRevicePos, &pos);
	GetISandboxActorSubsystem()->StorePos(pRevicePos, &pos);
	pRevicePos->set_map(0);
	pOWGlobal->set_svrstart(getWorldTime());

	flatbuffers::FlatBufferBuilder builder;
	saveToFlatBufferFromNet(builder);

	Rainbow::CompressTool ctool(Rainbow::CompressTool::COMPRESS_LZMA);
	size_t destlen = MAX_GLOBAL_BINLEN - 1;
	char binContent[MAX_GLOBAL_BINLEN];
	if (ctool.compress(binContent, destlen, builder.GetBufferPointer(), builder.GetSize()))
	{
		pGlobalBin->set_bincontent(binContent, destlen);
		pGlobalBin->set_binlen(destlen);
		pGlobalMisc->set_globalflag(builder.GetSize());
		return 0;
	}
	else
	{
		pGlobalBin->set_binlen(0);
		return 1;
	}
}

bool WorldManager::isNewMap(long long owid, int specialType)
{
	char path[256];
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	sprintf(path, "%s/w%lld/wglobal.fb", rootpath.c_str(), owid);
	return !(Rainbow::GetFileManager().IsFileExistWritePath(path));
}

void WorldManager::end()
{
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		if (iter->second)
			iter->second->end();
	}
	GetEcosysUnitRoadBuild().leaveWorld();
	GetEcosysUnitCityBuild().leaveWorld();
}

void WorldManager::save(bool sync)
{
	SANDBOXPROFILING_FUNC("Save World Manager");

	//退出游戏时不占用星站传送舱 code by hansom
	for(auto iter=m_Worlds.begin(); iter!=m_Worlds.end(); iter++)
	{  
		if(iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if(actormgr == NULL) continue;
		for(size_t i = 0; i < (size_t)actormgr->getNumPlayer(); i++)
		{
			IClientPlayer *player = actormgr->iGetIthPlayer(i);
			if(player == NULL) continue;
			if (player)
			{
				player->tryStandup();
			}
		}
		//城市数据保存
		iter->second->saveCityData();
		//建筑数据保存
		iter->second->saveBuildData();
	}
	GetDesertTradeCaravanMgrInterface()->saveFile();
	if(!saveGlobalAndPlayers()) return;

	bool isDirty = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("AchievementManager_getDirty", isDirty);
	if(needSave(NEEDSAVE_GLOBAL) && isDirty && m_ChunkIOMgr)
	{
		MNSandbox::GetGlobalEvent().Emit<long long, ChunkIOMgr *, int>("AchievementManager_saveWorldAchievements", m_worldId, m_ChunkIOMgr, m_nSpecialType);
		MNSandbox::GetGlobalEvent().Emit<bool>("AchievementManager_setDirty", false);
	}
	/*if (TaskSubSystem::GetTaskSubSystem() && needSave(NEEDSAVE_GLOBAL))
	{
		TaskSubSystem::GetTaskSubSystem()->TrySaveAllWorldTask(m_worldId, m_ChunkIOMgr); 
	}*/
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr() && needSave(NEEDSAVE_GLOBAL)) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("owid", m_worldId).
			SetData_Userdata("ChunkIOMgr", "iomgr", m_ChunkIOMgr);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_TrySaveAllWorldTask", sContext);
	}

	if(needSave(NEEDSAVE_CHUNKS))
	{
		std::map<int, World *>::iterator iter = m_Worlds.begin();
		for(; iter!=m_Worlds.end(); iter++)
		{
			World *pworld = iter->second;
			pworld->saveChunks(true);
		}
	}

	// 存档沙盒
	if (m_pSandbox && Config::GetSingleton().IsNeedSave()) // 工具的玩法模式不需要存档
	{
		SANDBOXPROFILING_FUNC("Save Sandbox");
		if (MNSandbox::Config::GetSingleton().IsHost())
		{
			std::map<int, World*>::iterator iter = m_Worlds.begin();
			for (; iter != m_Worlds.end(); iter++)
			{
				iter->second->SaveWorldScene(sync);//场景树存档
			}

			auto gamemaphost = m_pSandbox->GetGameMap().ToCast<GameMapHost>();
			assert(gamemaphost);
			if (gamemaphost)
			{
				gamemaphost->SaveAllMap(sync); // 存档事件
			}
		}
	}
}

bool WorldManager::loadFromFile(long long owid, int specialType/* = NORMAL_WORLD*/, bool delaySubMgr /* = false*/)
{
	// 先加载沙盒
	if (m_pSandbox && MNSandbox::Config::GetSingleton().IsHost())
	{
		auto gamemaphost = m_pSandbox->GetGameMap().ToCast<GameMapHost>();
		assert(gamemaphost);
		if (gamemaphost)
			gamemaphost->LoadMap(GameMapHost::MAPLOAD::NORMARL);
	}

	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/wglobal.fb", rootpath.c_str(), owid);
	int buflen = 0;
	bool ret = false;
	void *buf = ReadWholeFile(path, buflen);

	if(buf == NULL)
	{
		ret = false; // 失败
		if (HOME_GARDEN_WORLD == specialType)  //家园地图
		{
			std::string fullpath = GetFileManager().ToWriteableFullPath(path);
			if (Rainbow::DirVisitor::isDir(fullpath.c_str()))	//如果这个文件变成了文件夹，删掉文件夹（不知名原因，发现有的家园地图这个文件变成了文件夹）
			{
				GetFileManager().DeleteWritePathFileOrDir(path);
			}
		}
		// lua要初始化
		MINIW::ScriptVM::game()->callFunction("WorldMgr_OnLoadData", "s", "");
	}
	else
	{
		loadFromFlatBuffer(buf, buflen);
		free(buf);

		MINIW::ScriptVM::game()->callFunction("OnLoadWGlobalEx", "w", owid);

		if(m_RuleMgr)
			m_RuleMgr->onGameLoad();

		//customModelDataLoad(owid);

		ret = true; // 成功
	}
	if (!delaySubMgr)
		onLoad();

	if (HOME_GARDEN_WORLD == specialType)
	{
		return ret; //家园地图不加载开发者脚本
	}

	return ret;
}

bool WorldManager::loadFileByToggleGameMakerMode(long long owid, int specialType/* = NORMAL_WORLD*/)
{
	std::string rootpath = GetWorldRootBySpecialType(specialType);

	char path[256];
	sprintf(path, "%s/w%lld/wglobal.fb", rootpath.c_str(), owid);
	int buflen = 0;
	void *buf = ReadWholeFile(path, buflen);
	if (buf == NULL) return false;

	flatbuffers::Verifier verifier((const uint8_t *)buf, buflen);

	if (!FBSave::VerifyWorldGlobalBuffer(verifier))
	{
		GetISandboxActorSubsystem()->ResetObjId(GetISandboxActorSubsystem()->GetCurObjId() + 50000LL);

		free(buf);
		return false;
	}

	const FBSave::WorldGlobal *worldsave = FBSave::GetWorldGlobal(buf);

	if (worldsave == NULL) 
	{
		free(buf);
		return false;
	}

	if (worldsave->worldtime() > 0) setWorldTime(worldsave->worldtime());
	if (worldsave->daynighttime() >= 0)
		setDayTime(worldsave->daynighttime());
	else
		setDayTime(m_WorldTime);

	auto gmmaker = worldsave->makerdata();
	if (gmmaker && m_RuleMgr)
	{
		m_RuleMgr->loadFileByToggleGameMakerMode(gmmaker);
	}

	//村庄
	getWorldInfoManager()->clearVillageInfo();
	auto pvillages = worldsave->villages();
	if (pvillages)
	{
		for (int i = 0; i < (int)(pvillages->size()); i++)
		{
			auto pData = pvillages->Get(i);
			int ownerid = (int)pData->owneruin();
			VillageInfo villageData;
			villageData.villagePoint = Coord3ToWCoord(pData->villagepos());
			//图腾
			if (pData->villagetotems())
			{
				auto ptotems = pData->villagetotems();
				if (ptotems->size() < 2)//这里做个特殊处理，因为当前只会记录一个有效的图腾，如果因为之前的bug产生了非法的数据，这里做下过滤
				{
					for (int j = 0; j < (int)(ptotems->size()); j++)
					{
						villageData.villageTotems.push_back(Coord3ToWCoord(ptotems->Get(j)));
					}
				}
			}
			//旗帜
			if (pData->villagerflags())
			{
				auto flaglist = pData->villagerflags();
				for (int j = 0; j < (int)(flaglist->size()); j++)
				{
					auto flagdata = flaglist->Get(j);
					auto poslist = flagdata->flagposes();
					int id = flagdata->blockid();
					std::vector<WCoord> poses;
					for (int k = 0; k < (int)(poslist->size()); k++)
					{
						poses.push_back(Coord3ToWCoord(poslist->Get(k)));
					}
					villageData.villageFlags.insert(make_pair(id, poses));
				}
			}
			//村民
			if (pData->bindvillagers())
			{
				auto villagerlist = pData->bindvillagers();
				for (int j = 0; j < (int)(villagerlist->size()); j++)
				{
					WORLD_ID id = villagerlist->Get(j);
					villageData.bindVillagers.insert(id);
				}
			}
			//助手工作绑定关系
			if (pData->helperrelationship())
			{
				auto relationshipList = pData->helperrelationship();
				for (int j = 0; j < (int)(relationshipList->size()); j++)
				{
					auto relation = relationshipList->Get(j);
					VillageHelperTarget target;
					target.targetId = relation->target();
					target.needType = relation->needtype();
					target.needNum = relation->neednum();
					WORLD_ID helperId = relation->helper();
					villageData.helperRelationships.insert(make_pair(helperId, target));
				}
			}
			//床绑定关系
			if (pData->bedrelationship())
			{
				auto relationshipList = pData->bedrelationship();
				for (int j=0; j < (int)(relationshipList->size()); j++)
				{
					auto relation = relationshipList->Get(j);
					VillagerBedData data;
					WORLD_ID villager = relation->villager();
					data.blockX = relation->blockx();
					data.blockY = relation->blocky();
					data.blockZ = relation->blockz();
					data.status = relation->status();
					villageData.bedRelationships.insert(make_pair(villager, data));
				}
			}
			getWorldInfoManager()->insertVillageInfo(ownerid, villageData);
		}
	}

	free(buf);
	return true;
}

bool WorldManager::saveToFile(long long owid)
{
	collectGlobalData();

	flatbuffers::FlatBufferBuilder builder;
	saveToFlatBuffer(builder);
	MINIW::ScriptVM::game()->callFunction("OnSaveWGlobalEx", "w", owid);

	flatbuffers::Verifier verifier(builder.GetBufferPointer(), builder.GetSize());
	if(!FBSave::VerifyWorldGlobalBuffer(verifier)) return false;
	if(IsFlatBufferCompleteZero(builder.GetBufferPointer(), builder.GetSize())) return false;

	if(m_ChunkIOMgr != NULL )  m_ChunkIOMgr->pushCmd(CIOCMD_SAVEWGLOBAL, builder.GetBufferPointer(), builder.GetSize());
	return true;

	/*
	char path[256];
	sprintf(path, "data/w%lld/wglobal.fb", owid);
	return GetFileManager().SaveToWritePath(path, builder.GetBufferPointer(), builder.GetSize());
	*/
}

void WorldManager::collectGlobalData()
{
	if(GetIPlayerControl())
	{
		SandboxResult result;
		SandboxContext sandboxContext;
		GetIPlayerControl()->ControlCastToActor()->Event2().Emit<SandboxResult &, SandboxContext >("revive_getRevivePointEx", result, sandboxContext);
		m_HostRevivePoint = result.GetData_UserObject<WCoord>("point");
		World* pWorld = GetIPlayerControl()->getIWorld();
		if (pWorld)
		{
			m_mapHostRevivePoint[pWorld] = m_HostRevivePoint;
		}
	}

	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		if(pworld == NULL) continue;
		//城市数据保存
		pworld->saveCityData();
		//保存一下建筑数据
		pworld->saveBuildData();
		WorldMapData *destdata = getMapData(pworld->getCurMapID(), true);
		if(destdata == NULL) continue; 
		destdata->portalpos = pworld->getPortalPoint();
		if(pworld->m_Environ)  pworld->m_Environ->save(destdata);
		if(pworld->getActorMgr() == NULL) continue;

		pworld->getActorMgr()->AddBossToMapData(destdata);

		destdata->deathJarBlocks.clear();
		for (size_t i = 0; i < pworld->getDeathJarNum(); i++)
		{
			const WCoord &pos = pworld->getDeathJarPosByIdx(i);
			destdata->deathJarBlocks.push_back(pos);
		}

		destdata->isNeverShowEasyModeTips = pworld->m_isNeverShowEasyModeTips;

	}
	bool isneedSave = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("StatisticRainforest_needSave", isneedSave);
	if(isneedSave)
	{
		if(GetIPlayerControl() && GetIPlayerControl()->getIWorld())
		{
			MNSandbox::GetGlobalEvent().Emit<WorldMapData*>("StatisticRainforest_save", getMapData(GetIPlayerControl()->getIWorld()->getCurMapID()));
		} 
	}
}

bool WorldManager::saveGlobalAndPlayers()
{
    OPTICK_EVENT();
	//if(DefManager::GetInstancePtr() == NULL) return false;
	//if(!GetDefManagerProxy()->checkCrcCode(CRCCODE_ITEMS)) return false;   20230313 消耗性能 这里反外挂暂时屏蔽处理

	if (needSave(NEEDSAVE_GLOBAL))
	{
		if (!saveToFile(m_worldId)) return false;
	}

	//云服修改, 一键云服使用ArchiveManager的getNeedSyncArchive来判断是否对玩家数据存档 2021.12.23 by huanglin
	bool needSavePlayers = false;
#ifdef IWORLD_SERVER_BUILD
	{
		// 一键云服模式下的[玩家actor]使用 ArchiveManager::getNeedSyncArchive 来判断是否保存
		SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
		if (sandboxResult.IsExecSuccessed())
		{
			needSavePlayers = sandboxResult.GetData_Bool();
		}
	}
#else
		needSavePlayers = needSave(NEEDSAVE_PLAYERS);
#endif

	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		if (iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if (actormgr == NULL) continue;
		for (size_t i = 0; i < (size_t)actormgr->getNumPlayer(); i++)
		{
			IClientPlayer* player = actormgr->iGetIthPlayer(i);
			if (player == NULL) continue;
			if (needSavePlayers && m_ChunkIOMgr)
			{
				player->saveToFile(0, m_ChunkIOMgr);
			}
			player->saveUserData(m_worldId);
			player->saveTechTree(m_worldId);

		}
	}

	return true;
}

WorldMapData *WorldManager::getMapData(int mapid, bool create)
{
	for(size_t i=0; i<m_MapData.size(); i++)
	{
		if (m_MapData[i] && m_MapData[i]->mapid == mapid) return m_MapData[i];
	}

	if(create)
	{
		WorldMapData *mapdata = ENG_NEW(WorldMapData)(mapid);
		m_MapData.push_back(mapdata);

		return mapdata;
	}
	else return NULL;
}

void WorldManager::sendWGlobalUpdate()
{
	PB_WGlobalUpdateHC wGlobalUpdateHC;
	wGlobalUpdateHC.set_worldtime(m_WorldTime);
	wGlobalUpdateHC.set_daynighttime(m_DayNightTime);
	wGlobalUpdateHC.set_viewrange(GetISandboxActorSubsystem()->GetCurViewRange(nullptr)/*IClientPlayer::m_ViewRangeSetting*/);

	std::vector<int>uins;
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		wGlobalUpdateHC.set_mapid(pworld->getCurMapID());
		wGlobalUpdateHC.set_raining(pworld->m_Environ->isRaining() ? 1 : 0);
		wGlobalUpdateHC.set_darking(pworld->m_Environ->isDarking() ? 1 : 0);
		wGlobalUpdateHC.set_curweather(pworld->m_Environ->getCurWeather());

		uins.resize(0);
		for (size_t i = 0; i < (size_t)pworld->getActorMgr()->getNumPlayer(); i++)
		{
			IClientPlayer *player = pworld->getActorMgr()->iGetIthPlayer(i);
			uins.push_back(player->getUin());
		}

		if (!uins.empty()) GetGameNetManagerPtr()->sendToClientMulti(&uins[0], uins.size(), PB_WGLOBAL_UPDATE_HC, wGlobalUpdateHC);
	}
}
World* WorldManager::createEditorWorld(int mapid)
{
	World* pworld = ENG_NEW(World)(this);
	pworld->m_OWID = m_worldId;
	pworld->createEditor(m_worldCreateData, mapid, m_ownerUin);
	m_Worlds[mapid] = pworld;
	return pworld;
}

std::vector<int> WorldManager::getAllMapid()const
{
	std::vector<int> ret(m_Worlds.size());
	for (auto it = m_Worlds.begin(); it != m_Worlds.end(); it++) {
		ret.push_back(it->first);
	}
	return ret;
}

bool WorldManager::removeWorld(int mapid, int curmapid/* = -1*/)
{
	//if (isRemote()) {
	//	return false;
	//}

	//remove mapdata//m_MapData//WorldMapData*
	auto mapdataIt = m_MapData.begin();
	for (; mapdataIt != m_MapData.end(); ) {
		WorldMapData* target = *mapdataIt;
		if (target && target->mapid == mapid) {
			mapdataIt = m_MapData.erase(mapdataIt);
		}else {
			mapdataIt++;
		}
	}

	//remove world
	auto it = m_Worlds.find(mapid);
	if (it == m_Worlds.end()) {
		//todo:remove world save file.
		//removeWorldSaveFiles();
		return true;
	}
	World* target = it->second;
	m_Worlds.erase(it);

	target->clear();
	ENG_DELETE(target);

	//todo:remove world save file.
	//removeWorldSaveFiles();

	//switch to current world
	auto curpit = m_Worlds.find(curmapid);
	if (curpit != m_Worlds.end()) {
		World* cur = curpit->second;
		if (cur) {
			cur->SetEngineCurrentScene();
			CameraManager::GetInstance().m_GameCamera->InitEngineCamera(cur);
		}
	}
	return true;
}

World *WorldManager::createWorld(int mapid, bool is_own)
{
	ENTRYMAPCOST_STEP("Begin WorldManager::createWorld");
	
	bool isLoadAll = false;
	MINIW::ScriptVM::game()->callFunction("CreateMapLoadAll", ">b", &isLoadAll);
	World *pworld = ENG_NEW(World)(this);
	pworld->setSOCCreateMap(isLoadAll);
	setCanDropItem(!isLoadAll);
	if(GetClientInfoProxy()->getMultiPlayer() == GAME_NET_MP_GAME_CLIENT)
	{
		pworld->setRemoteMode(true);
	}

	pworld->m_OWID = m_worldId;
	
	int rentServerHost = (int)IsRentServerHost();
	ROOM_HOST_TYPE hostType = ROOM_BY_PLAYER;
	if(GetGameInfoProxy()) hostType = GetGameInfoProxy()->GetRoomHostType();
	int isHostUin = 1;
	if (GetGameNetManagerPtr()) isHostUin = (int)(GetGameNetManagerPtr()->getHostUin() == m_ownerUin);
	WarningStringMsg("------ createWorld, RentServerHost:%d , HostType:%d , IsHost:%d -------", rentServerHost,(int)hostType,isHostUin);

	pworld->create(m_worldCreateData, mapid, m_ownerUin, m_nSpecialType, is_own);
	m_Worlds[mapid] = pworld;

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("OnWorldCreate", SandboxContext(nullptr).SetData_Usertype(pworld));

	if(!pworld->isRemoteMode())
	{
		WorldMapData *mapdata = getMapData(mapid);
		if(mapdata)
		{
			pworld->resetPortalPoint(mapdata->portalpos);
			if(pworld->m_Environ) pworld->m_Environ->load(mapdata);
			MNSandbox::GetGlobalEvent().Emit<WorldMapData *>("StatisticRainforest_load", mapdata);

			//遗落的盒子
			for (size_t i = 0; i < mapdata->deathJarBlocks.size(); i++)
			{
				pworld->addDeathJarPoint(mapdata->deathJarBlocks[i]);
			}

			pworld->m_isNeverShowEasyModeTips = mapdata->isNeverShowEasyModeTips;
		}


		pworld->getChunkProvider()->createBoss(0);
		//建筑数据读取
		pworld->loadBuildData();
		if (mapdata)
		{
			WorldMapData* mapData = g_WorldMgr->getMapData(pworld->getCurMapID());
			if (mapData && mapData->iscreateBigBuild) {// && mapData->godstatuepos == blockpos
				//神庙
				BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::GodTempleID);
			}
			if (mapData && mapData->undergroundpalaceList.size())
			{
				BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::UndergroundPalaceID);
			}
			if (mapData && mapData->desertVillageBuildList.size() > 0)
			{
				BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::DesertBuild1);
			}
			if (mapData && mapData->fishingVillageBuildList.size() > 0)
			{
				BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::BigFishingBuild);
			}
			if (mapData && mapData->shipWrecksList.size() > 0)
			{
				BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::BigBuildShipWrecksBig);
			}
			if (mapData)
			{
				if (mapData->islandBuildList.size() > 0)
				{
					BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::IsLandBuildDesertTreasureBox1);
				}
				if (mapData->iceBuildList.size() > 0)
				{
					BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::IcePlantBuildCenter);
				}
				if (isSurviveMode())
				{
					if (mapData->birthPointBuild.progress >= 0)
					{
						BigBuildCreater::getInstance()->resume(pworld, mapData, BigBuildCreater::BigBuildsId::BirthPointBigBuild);
					}
				}
			}


			if (mapData)
			{
				//沙漠建筑
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_DesertVillage];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitDesertVillage().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				//渔村
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_FishingVillage];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitFishingVillageBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				// 沉船群
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_ShipWrecks];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitShipWrecks().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				//荒岛建筑
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_DesertIslandTreasureBox];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIsLandBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				//珊瑚岛建筑
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_CoralIsLandBuild];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIsLandBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				//红土岛建筑
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_ShoreRedSoilTreasureBox];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIsLandBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				//普通宝箱
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_NormalTreasureBox];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIsLandBuild().genTreasureBox(pworld, p.index);
						}
					}
				}
				//冰原村庄和雪山祭坛
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_IceVillageBuild];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIceVillageBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
				{
					auto& chunks = mapData->specialChunk[SpecialEcosysType_IceAltar];
					for (auto& p : chunks)
					{
						if (p.status == SpecialEcosysBuildStatus_UNFINISHED)
						{
							pworld->syncLoadChunk(p.index);
							GetEcosysUnitIceAltarBuild().addToWorld(pworld->getWorldProxy(), p.index, true);
						}
					}
				}
			}

		}

		pworld->LoadWorldScene(); // 加载场景
	}

	MNSandbox::Scene* mnscene = pworld->GetWorldScene() ? pworld->GetWorldScene()->StaticToCast<MNSandbox::Scene>() : nullptr;
	mnscene->LoadDefaultNode();

	GlobalNotify::GetInstance().LoadWorldFinish.Emit(mapid, mnscene); // 加载完成

	nodeTreePlay();

	if (m_gameMode == OWTYPE_SINGLE || m_gameMode == OWTYPE_CREATE)
	{
		GetAdventureGuideMgrProxy()->InitializeWithWorld(pworld);
	}
	else
	{
#ifdef Test_ActorBotNpc
		ActorBotNpc::Get(pworld);
#endif
		GetAdventureGuideMgrProxy()->setIsEnterInGuide(false);
	}

	ENTRYMAPCOST_STEP("End WorldManager::createWorld");
	return pworld;
}

void WorldManager::tickSave()
{
    OPTICK_EVENT();
	if(needSave(NEEDSAVE_CHUNKS))
	{
		int nchunks = 0;
		for(auto iter=m_Worlds.begin(); iter!=m_Worlds.end(); iter++)
		{
			World *pworld = iter->second;
			if(pworld == NULL) continue;
			nchunks += pworld->saveChunks(false);

#ifdef SANDBOX_CHUNK_STREAM_LOAD
			if (pworld->GetWorldScene()) {
				pworld->GetWorldScene()->OnTickSave();
			}
#endif
		}

		GetStarStationTransferMgrInterface()->writeToFile(false);
	}

	m_SaveGlobalTick++;
#ifdef IWORLD_SERVER_BUILD
	if (m_SaveGlobalTick > 10 * 20 + 3)
#else
	if (m_SaveGlobalTick > 3 * 20 + 3)
#endif
	{
		m_SaveGlobalTick = 0;
		saveGlobalAndPlayers();
	}

	bool isDirty = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("AchievementManager_getDirty", isDirty);
	if (needSave(NEEDSAVE_GLOBAL) && isDirty && m_ChunkIOMgr)
	{
		MNSandbox::GetGlobalEvent().Emit<long long, ChunkIOMgr *, int>("AchievementManager_saveWorldAchievements", m_worldId, m_ChunkIOMgr, m_nSpecialType);
		MNSandbox::GetGlobalEvent().Emit<bool>("AchievementManager_setDirty", false);
	}
	/*if (TaskSubSystem::GetTaskSubSystem() && needSave(NEEDSAVE_GLOBAL))
	{
		TaskSubSystem::GetTaskSubSystem()->TrySaveAllWorldTask(m_worldId, m_ChunkIOMgr);
	}*/
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr() && needSave(NEEDSAVE_GLOBAL)) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("owid", m_worldId).
			SetData_Userdata("ChunkIOMgr", "iomgr", m_ChunkIOMgr);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_TrySaveAllWorldTask", sContext);
	}
}

void WorldManager::getAllPlayers(std::vector<IClientPlayer *>&players)
{
	players.resize(0);
	for(auto iter=m_Worlds.begin(); iter!=m_Worlds.end(); iter++)
	{
		if(iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if (actormgr)
		{
			for (size_t i = 0; i < (size_t)actormgr->getNumPlayer(); i++)
			{
				players.push_back(actormgr->iGetIthPlayer(i));
			}
		}
	}
}

void WorldManager::getAllPlayersObjid(std::vector<long long>& players)
{
	players.resize(0);
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		if (iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if (actormgr)
		{
			for (size_t i = 0; i < (size_t)actormgr->getNumPlayer(); i++)
			{
				players.push_back(actormgr->iGetIthPlayer(i)->iGetObjId());
			}
		}
	}
}

int WorldManager::getAllPlayersNum()
{
	int num = 0;
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		if (iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if (actormgr)
			num += actormgr->getNumPlayer();
	}
	return num;
}

int WorldManager::getAllActorsNum()
{
	int num = 0;
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		if (iter->second == NULL) continue;
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		if (actormgr)
			num += actormgr->getActorCount();
	}
	return num;
}


void WorldManager::updatePlayerSkin(int uin, int plarIndex, char *custom, char* custommodel)
{
	auto player = getPlayerByUin(uin);
	if (player)
	{
		player->changePlayerModel(plarIndex, 0, custom, custommodel);
	}
}


IClientPlayer *WorldManager::getPlayerByUin(int uin)
{
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		if(pworld && pworld->getActorMgr()){
			IClientPlayer *player = pworld->getActorMgr()->iFindPlayerByUin(uin);
			if(player) return player;
		}
	}
	return NULL;
}

void WorldManager::addWaitDownloadRes(std::string resname, int restype)
{
	if (!m_pMpCdnResMgr)
		return;

	m_pMpCdnResMgr->addWaitDownloadRes(resname, restype);
}

void WorldManager::setFissionInfo(int fissionType, long long fissionFrom, int fissionVersion)
{
	m_fissionType = fissionType;
	m_fissionFrom = fissionFrom;
	m_fissionVersion = fissionVersion;
}

bool WorldManager::isAdventureMode()
{
	return m_gameMode == OWTYPE_SINGLE || m_gameMode == OWTYPE_EXTREMITY || m_gameMode == OWTYPE_CREATE_RUNGAME || m_gameMode == OWTYPE_FREEMODE;
}

bool WorldManager::isOriginAdventureMode()
{
	return m_gameMode == OWTYPE_SINGLE || m_gameMode == OWTYPE_EXTREMITY || m_gameMode == OWTYPE_FREEMODE;
}

bool WorldManager::isShowTempMode()//显示温度计
{
	return isAdventureMode() || m_gameMode == OWTYPE_GAMEMAKER_RUN;//by renjie 比isAdventureMode多了OWTYPE_GAMEMAKER_RUN
}

bool WorldManager::isAdvNewStarConsumMode()//冒险模式星星优化，星星消耗数量增加模式
{
	return m_gameMode == OWTYPE_SINGLE || m_gameMode == OWTYPE_EXTREMITY || m_gameMode == OWTYPE_FREEMODE || IsUGCGameNewStarConsumMode();
}

bool WorldManager::IsUGCGameNewStarConsumMode()//高级创作模式星星优化，星星消耗数量增加模式
{
	if (isUGCMode())
	{
		bool isMode = false;
		MINIW::ScriptVM::game()->callFunction("checkUGCGameNewStarConsumMode",">b" ,&isMode);
		return isMode;
	}
	return false;
}

bool WorldManager::isActorExist(IClientActor* actor)
{
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		if (pworld->getActorMgr()->iIsActorExist(actor))
		{
			return true;
		}
	}
	return false;
}

IClientActor *WorldManager::findActorByWID(long long wid)
{
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		IClientActor *actor = pworld->getActorMgr()->iFindActorByWID(wid);
		if(actor) return actor;
	}
	return NULL;
}

IClientMob* WorldManager::findMobByWID(long long wid)
{
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for(; iter!=m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		IClientMob*mob = pworld->getActorMgr()->iFindMobByWID(wid);
		if(mob) return mob;
	}
	return NULL;
}

IClientMob* WorldManager::findMobByServerID(const std::string& serverid)
{
	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for (; iter != m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		IClientMob*mob = pworld->getActorMgr()->iFindMobByServerID(serverid);
		if (mob) return mob;
	}
	return NULL;
}

int WorldManager::getNewPlayerTeamID(IClientPlayer *player)
{
	if(m_RuleMgr == NULL) return 0;

	int teamid = -1;
	if(isGameMakerRunMode()) teamid = m_RuleMgr->CallEventScript(WES_ENTER_TEAMID, player);
	if(teamid >= 0) return teamid;

	int teamnum = (int)m_RuleMgr->getRuleOptionVal(GMRULE_TEAMNUM);
	if(teamnum >= 1)
	{
		int assignModel = TEAM_ASSIGN_BYORDER;
		if (m_RuleMgr) assignModel = m_RuleMgr->getTeamAssignMode();
		
		if(assignModel == TEAM_ASSIGN_BYORDER || assignModel == TEAM_ASSIGN_RANDOM) 
		{
			
			int teammembers[MAX_GAME_TEAMS];
			memset(teammembers, 0, sizeof(teammembers));
			getAllPlayers(m_AllPlayers);
			for(size_t i=0; i<m_AllPlayers.size(); i++)
			{
				int id = m_AllPlayers[i]->iGetTeam();
				//if(id > teamnum) id = 0;
				if (id >= 0 && id < MAX_GAME_TEAMS)
					teammembers[id]++;
			}

			// 顺序分配-旧的默认分配机制
			if (assignModel == TEAM_ASSIGN_BYORDER)
			{
				int minslot = 0;
				int minmembers = 100000;
				for(int i=1; i < MAX_GAME_TEAMS; i++)
				{
					bool teamEnable = true;
					if (m_RuleMgr != NULL)
					{
						teamEnable = m_RuleMgr->getTeamEnable(i);
						int playerNum = m_RuleMgr->getTeamPlayerNum(i);
						teamEnable = teamEnable && teammembers[i]<playerNum;
					}

					if(teamEnable && teammembers[i] < minmembers)
					{
						minslot = i;
						minmembers = teammembers[i];
					}
				}

				return minslot;
			}
			else // 新分配机制：随机分配
			{
				if (m_RuleMgr == NULL) return 0;
				vector<int> teamIds = m_RuleMgr->getCurEnableTeams();
				int teamsize = teamIds.size();

				if (teamsize == 1) {
					return teamIds[0];
				} else if (teamsize > 1) {
					int count = 0;
					do {//遍历获取
						int random = GenRandomInt(teamsize);
						int teamId = teamIds[random];
						int playerNum = m_RuleMgr->getTeamPlayerNum(teamId);
						if (teammembers[teamId] < playerNum)
							return teamId;
					} while (++count < 10);
				} else {
					return 0;
				}
			}
		}
		else
		{
			return 0; // 不分配队伍或自己选
		}
	}

	return 0;
}

void WorldManager::syncAllPlayerInfo(int uin, bool sync_name)
{
	//LOG_INFO("WorldManager::syncAllPlayerInfo(): %d", uin);
	PB_PlayersUpdateInfoHC playersUpdateInfoHC;

	getAllPlayers(m_AllPlayers);
	for (size_t i = 0; i < m_AllPlayers.size(); i++)
	{
		IClientPlayer *player = m_AllPlayers[i];
		if (playersUpdateInfoHC.players_size() >= MAX_WORLD_PLAYERS) 
		{
			break;
		}

		PB_PlayerBriefInfo* briefInfo = playersUpdateInfoHC.add_players();
		briefInfo->set_uin(player->getUin());
		briefInfo->set_mapid(player->iGetCurMapID());

		auto attr = player->CastToActor()->getActorComponent(ComponentType::COMPONENT_PLAYER_ATTRIB);
		if (attr)
		{
			bool result = attr->Event2().Emit<PB_PlayerBriefInfo*>("PlayerAttrib_setbriefInfo", briefInfo);
			Assert(result);
		}
		briefInfo->set_teamid(player->iGetTeam());
		briefInfo->mutable_pos()->set_x(player->iGetPosition().x);
		briefInfo->mutable_pos()->set_y(player->iGetPosition().y);
		briefInfo->mutable_pos()->set_z(player->iGetPosition().z);
		briefInfo->mutable_vipinfo()->set_viptype(player->getVipInfo().vipType);
		briefInfo->mutable_vipinfo()->set_viplevel(player->getVipInfo().vipLevel);
		briefInfo->mutable_vipinfo()->set_vipexp(player->getVipInfo().vipExp);
		briefInfo->add_cgvars(player->getGameScore() | (player->getGameRanking() << 24));
		briefInfo->add_cgvars(player->getGameResults());
		briefInfo->set_inspectator((int)player->isInSpectatorMode());
		briefInfo->set_acctountskinid(player->GetAccountSkinID());
		if (sync_name)
		{
			briefInfo->set_nickname(player->getNickname());
			briefInfo->set_playerindex(player->iGetBody()->getPlayerIndex());
			if (briefInfo->uin()== GetClientInfoProxy()->getUin())
			{
				briefInfo->set_frameid(GetClientInfoProxy()->getHeadFrameId());
			}
			else {
				briefInfo->set_frameid(player->iGetBody()->getPlayerFrameId());
			}

		}
		else
		{
			briefInfo->set_nickname("");
			briefInfo->set_playerindex(0);
		}

		const char *customskins = player->getCustomjson();
		if(customskins && customskins[0]) 
			briefInfo->set_customjson(customskins);
	}

	if (isGameMakerRunMode())
	{
		int maxlifenum = int(m_RuleMgr->getRuleOptionVal(GMRULE_LIFE_NUM));
// 		int nteam = m_RuleMgr->getNumTeam() + 1;
		for (int i = 0; i < MAX_GAME_TEAMS; i++)
		{
			int flags = m_RuleMgr->getTeamResults(i);
			if (maxlifenum > 0)
			{
				flags |= m_RuleMgr->getTeamDieTimes(i) << 2;
			}
			playersUpdateInfoHC.add_teamflags(flags);
			playersUpdateInfoHC.add_teamscores(m_RuleMgr->getTeamScore(i));
		}
	}

	if (uin == 0) 
		GameNetManager::getInstance()->sendBroadCast(PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
	else 
		GameNetManager::getInstance()->sendToClient(uin, PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
}

void WorldManager::packPlayerBriefInfo(IClientPlayer* player, PB_PlayerBriefInfo* briefInfo, BRIEF_INFO_SYNC_TYPE type){
	if (!player || !briefInfo)
		return;
	briefInfo->set_uin(player->getUin());
	if (type & BIS_MAP){
		briefInfo->set_mapid(player->iGetCurMapID());
	}

	GetISandboxActorSubsystem()->PackPlayerAttrib(briefInfo, type, player);
	
	if (type & BIS_TEAM_ID){
		briefInfo->set_teamid(player->iGetTeam());
	}
	if (type & BIS_POSITION){
		briefInfo->mutable_pos()->set_x(player->iGetPosition().x);
		briefInfo->mutable_pos()->set_y(player->iGetPosition().y);
		briefInfo->mutable_pos()->set_z(player->iGetPosition().z);
	}
	if (type & BIS_VIP){
		briefInfo->mutable_vipinfo()->set_viptype(player->getVipInfo().vipType);
		briefInfo->mutable_vipinfo()->set_viplevel(player->getVipInfo().vipLevel);
		briefInfo->mutable_vipinfo()->set_vipexp(player->getVipInfo().vipExp);
	}
	if (type & BIS_CGVARS){
		briefInfo->add_cgvars(player->getGameScore() | (player->getGameRanking() << 24));
		briefInfo->add_cgvars(player->getGameResults());
	}
	if (type & BIS_INSPECTATOR)
		briefInfo->set_inspectator((int)player->isInSpectatorMode());
	if (type & BIS_SKIN_ID)
		briefInfo->set_acctountskinid(player->GetAccountSkinID());
	if (type & BIS_NICK_NAME)
		briefInfo->set_nickname(player->getNickname());
	if (type & BIS_PLAYER_INDEX)
		briefInfo->set_playerindex(player->iGetBody()->getPlayerIndex());
	if (type & BIS_EXPOSE_POS)
		briefInfo->set_exposepostoother(player->CanExposePosToOther());
	if (type & BIS_FRAME_ID){
		if (player->iGetObjId()== GetClientInfoProxy()->getUin())
			briefInfo->set_frameid(GetClientInfoProxy()->getHeadFrameId());
		else
			briefInfo->set_frameid(player->iGetBody()->getPlayerFrameId());
	}

	if (type & BIS_CUSTOM_JSON){
		const char *customskins = player->getCustomjson();
		if(customskins && customskins[0]) 
			briefInfo->set_customjson(customskins);
	}
}
void WorldManager::syncBriefInfo(BRIEF_INFO_SYNC_TYPE type, int to_uin/*=0*/, int from_uin /*=0*/)
{
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		return;
	}
	PB_PlayersUpdateInfoHC playersUpdateInfoHC;

	std::vector<IClientPlayer *> players;
	std::vector<IClientPlayer *> *ref_players;

	if (from_uin){
		players.push_back(getPlayerByUin(from_uin));
		ref_players = &players;
	} else {
		ref_players = &m_AllPlayers;
	}
	for (size_t i = 0; i < ref_players->size(); i++)
	{
		IClientPlayer *player = ref_players->at(i);
		if (!player)
			continue;
		if (playersUpdateInfoHC.players_size() >= MAX_WORLD_PLAYERS) 
		{
			break;
		}

		PB_PlayerBriefInfo* briefInfo = playersUpdateInfoHC.add_players();
		packPlayerBriefInfo(player, briefInfo, type);
	}

	if (to_uin == 0) 
		GameNetManager::getInstance()->sendBroadCast(PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
	else 
		GameNetManager::getInstance()->sendToClient(to_uin, PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
}
void WorldManager::signChangedToSync(int uin, BRIEF_INFO_SYNC_TYPE type){
	if (isRemote())
		return;
	m_Uin2SyncBriefInfo[uin] |= type;
}
void WorldManager::syncAllPlayerInfo(){
	if (m_Uin2SyncBriefInfo.empty())
		return;
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		return;
	}
	int packed_count = 0;
	PB_PlayersUpdateInfoHC playersUpdateInfoHC;
	for (auto iter=m_Uin2SyncBriefInfo.begin(); iter!=m_Uin2SyncBriefInfo.end(); ++iter){
		IClientPlayer *player = getPlayerByUin(iter->first);
		if (!player)
			continue;
		PB_PlayerBriefInfo* briefInfo = playersUpdateInfoHC.add_players();
		packPlayerBriefInfo(player, briefInfo, (BRIEF_INFO_SYNC_TYPE)iter->second);
		packed_count += 1;
	}
	m_Uin2SyncBriefInfo.clear();
	if (packed_count)
		GameNetManager::getInstance()->sendBroadCast(PB_PLAYERS_UPDATEINFO_HC, playersUpdateInfoHC, 0, false);
}

void WorldManager::createBirthBuild(World* pworld, IPlayerControl* player)
{
	//必须是地球
	if (!pworld || pworld->getCurMapID() != 0 || m_MapData.size() == 0)
	{
		return;
	}

	WorldMapData* mapData = m_MapData[0];
	if (!mapData)
	{
		return;
	}

	GetISandboxActorSubsystem()->CreateBirthBuild(this, pworld, player, mapData);
}

/*
 * 主机同步玩家退出游戏时的briefinfo, 目前只在多人游戏中调用
 * @param uin 退出玩家的uin
 */
void WorldManager::broadcastPlayerLeave(int uin){
	getAllPlayers(m_AllPlayers);
	m_AllPlayerUins.erase(uin);
	if (m_RuleMgr && m_RuleMgr->getGameStage() == CGAME_STAGE_END)
		return;
	PB_PlayerLeaveHC playersLeaveHC;
	playersLeaveHC.add_uins(uin);

	// 云服可能会广播给player未初始化好的对象，这里改成使用玩家遍历广播 TODO(alaska) 下个版本改成正式的，提供一个新版sendBroadcast
	GameNetManager::getInstance()->sendBroadCast(PB_PLAYER_LEAVE_HC, playersLeaveHC, 0);
}

/*
 * 同步team_id分数信息至 to_uin
 * @param team_id 要同步的队伍ID, 默认-1, 默认值时同步所有队伍
 * @param to_uid 接收者uin, 默认0, 默认值时同步给所有玩家
 */
void WorldManager::syncTeamScore(int team_id, int to_uin){
	if (!isGameMakerRunMode()) // 参考syncAllPlayerInfo, 判断此模式 2022.03.09 by huanglin
		return;
	if (!m_RuleMgr)
		return;
	PB_TeamScoreHC TeamScoreHC;
	std::vector<int> team_ids;
	if (team_id != -1)
		team_ids.push_back(team_id);
	else {
		for (int i = 0; i < MAX_GAME_TEAMS; i++)
			team_ids.push_back(i);
	}
	int maxlifenum = int(m_RuleMgr->getRuleOptionVal(GMRULE_LIFE_NUM));
	for (auto iter=team_ids.begin(); iter!=team_ids.end(); ++iter){
		auto team = TeamScoreHC.add_teams();
		int team_id = *iter;
		int flags = m_RuleMgr->getTeamResults(team_id);
		if (maxlifenum > 0)
		{
			flags |= m_RuleMgr->getTeamDieTimes(team_id) << 2;
		}
		team->set_flags(flags);
		team->set_teamid(team_id);
		team->set_score(m_RuleMgr->getTeamScore(team_id));
	}
	if (to_uin == 0) 
		GameNetManager::getInstance()->sendBroadCast(PB_TEAM_SCORE_HC, TeamScoreHC);
	else 
		GameNetManager::getInstance()->sendToClient(to_uin, PB_TEAM_SCORE_HC, TeamScoreHC);
}

/*
 * 同步玩家进入游戏时的briefinfo, 目前只在多人游戏中调用
 * @param uin 进入玩家的uin
 */
void WorldManager::syncPlayerEnter(int uin){
	m_AllPlayerUins.insert(uin);
	getAllPlayers(m_AllPlayers);
	syncBriefInfo(BIS_ALL, uin);  // 将所有玩家briefinfo同步给uin
	syncBriefInfo(BIS_ALL, 0, uin);  // 将uin玩家信息同步给所有玩家
	syncTeamScore(-1, uin);  // 将所有队伍的分数发给uin
}

void WorldManager::doActualTeleport(IClientPlayer *player, int targetmap, bool isrocketteleport/* =false */)
{
	if (!player) return;
	auto iactor = player->CastToActor();
	iactor->addRef(); //防止refcount==0导致释放
	
	World* oldWorld = player->GetPlayerWorld();
	if (oldWorld == nullptr) return;
	bool changeworld = false;
	if (oldWorld->getCurMapID() != targetmap)
	{
		changeworld = true;
	}

	if (changeworld && iactor->getWorld())
	{
		iactor->getWorld()->getActorMgr()->DespawnClientActor(iactor, true);
	}

	World* pworld = nullptr; //getOrCreateWorld(targetmap, player);

	if (changeworld)
	{
		pworld = GoToWorld(oldWorld, targetmap, player);
	
		//set cur scene
		GetSceneManagerPtr()->SetCurrentScene(targetmap);

		if (pworld == nullptr) return;
		pworld->getActorMgr()->SpawnPlayerAddRef(player,true);
	}
	else
	{
		pworld = getOrCreateWorld(targetmap, player);
	}

	AssertMsg(pworld, "New world is null!");
	if (pworld == nullptr) return;
	WCoord targetpos = pworld->getPortalPoint();
	if (isrocketteleport)
	{
		targetpos = player->getRealLandingPoint(targetmap, pworld);
	}

	if (changeworld)
	{
		IPlayerControl* playerCtrl = dynamic_cast<IPlayerControl*>(player);
		if (playerCtrl)
		{
			playerCtrl->resetHandModel();
		}
	}

	if(targetpos.y<0 && targetmap!=0 && pworld->getChunkProvider() != nullptr)
	{
		WCoord center(0,pworld->getChunkProvider()->getSpawnMinY(),0);
		pworld->syncLoadChunk(center, 16);

		pworld->createPortal(center);
		saveGlobalAndPlayers();
		targetpos = pworld->getPortalPoint();
	}

	WCoord realteleportpos = WCoord(0, -1, 0);
	if (targetpos.y < 0)
	{
		pworld = getWorld(0);
		if (pworld)
			player->gotoSpawnPoint(pworld);
	}
	else
	{
		player->gotoTeleportPos(pworld, targetpos, realteleportpos);
	}
	
	InitNewWorldPoint(player, pworld);

	//CameraManager::GetInstance().m_GameCamera->InitEngineCamera();
	
	//设置星站传送落脚点为默认复活点
	SetDefaultRevivePoint(player, pworld, realteleportpos);

	player->CastToActor()->release();
}

void WorldManager::teleportPlayer(IClientPlayer *player, int targetmap)
{
	TeleportInfo info;

	player->CastToActor()->addRef();
	info.player = player;
	info.targetmap = targetmap;
	m_ReadyTeleports.push_back(info);
}

void WorldManager::doActualTransfer(IClientPlayer *player, int targetmap, int destStarStationId, WCoord& pos)
{
	if (!player) return;

	player->CastToActor()->addRef(); //防止refcount==0导致释放teleportPlayer

	World* oldWorld = player->GetPlayerWorld();

	bool changeworld = false;
	if(oldWorld->getCurMapID() != targetmap)
	{
		changeworld = true;
	}	

	if(changeworld && player->GetPlayerWorld()) player->GetPlayerWorld()->getActorMgr()->DespawnClientActor(player->CastToActor());

	World* pworld = nullptr;//  getOrCreateWorld(targetmap, player);

	if (changeworld)
	{
		pworld = GoToWorld(oldWorld, targetmap, player);
	}
	else
	{
		pworld = getOrCreateWorld(targetmap, player);
	}

	AssertMsg(pworld, "New world is null!");

	if (targetmap != 0)
	{
		WCoord center(pos.x / BLOCK_SIZE, pos.y / BLOCK_SIZE, pos.z / BLOCK_SIZE);
		pworld->syncLoadChunk(center, 16);
		saveGlobalAndPlayers();
	}

	if (changeworld)
	{
		pworld->getActorMgr()->spawnPlayer(player);

		if (GetIPlayerControl() && player->getUin() == GetIPlayerControl()->GetIUin()){
			player->addAchievement(1, ACHIEVEMENT_ENTER_WORLD, pworld->getCurMapID()); //进入世界，触发冒险成就任务 enterWorld找不到玩家完成不了任务   code_by:wuweiwei
		}

		InitNewWorldPoint(player, pworld);
	}

	//CameraManager::GetInstance().m_GameCamera->InitEngineCamera();	

	WCoord blockpos(pos.x / BLOCK_SIZE, pos.y / BLOCK_SIZE, pos.z / BLOCK_SIZE);
	player->gotoTransferPos(pworld, blockpos); 	

	player->CastToActor()->release();
}

// jump from old_world to new_world
World* WorldManager::GoToWorld(World* old_world, int targetmap, IClientPlayer* player)
{
	Assert(old_world);
	//Assert(new_world);

	if(!GetIPlayerControl() || player->CastToActor() != GetIPlayerControl()->ControlCastToActor())
		return getOrCreateWorld(targetmap, player);

	old_world->FreeOldWorld();

	World* new_world = getOrCreateWorld(targetmap, player);
	new_world->SetEngineCurrentScene();
	
	CameraManager::GetInstance().m_GameCamera->InitEngineCamera(new_world);

	//CameraManager::GetInstance().m_GameCamera->AttachCameralModeToScene(new_world->getScene());
	if(GetIPlayerControl())	
		GetIPlayerControl()->switchCurrentItem();

	new_world->GetWorldScene()->handleTeleporWorldDone(targetmap, player->CastToNode());
	return new_world;
}

void WorldManager::transferPlayer(IClientPlayer *player, int targetmap, int destStarStationId, WCoord pos)
{
	TransferInfo info;
	player->CastToActor()->addRef();
	info.player = player;
	info.targetmap = targetmap;
	info.position = pos;
	info.destStarStationId = destStarStationId;
	m_ReadyTransfer.push_back(info);
}
World *WorldManager::getWorld(int mapid)
{
	std::map<int, World *>::iterator iter = m_Worlds.find(mapid);
	if(iter == m_Worlds.end())
	{
		return NULL;
	}
	else return iter->second;
}

World* WorldManager::getWorldByOwid(long long owid)
{
	std::map<int, World*>::iterator iter = m_Worlds.begin();
	for (; iter != m_Worlds.end(); ++iter)
	{
		if (iter->second->getOWID() == owid)
		{
			return iter->second;
		}
	}
	return NULL;
}

World *WorldManager::getOrCreateWorld(int mapid, IClientPlayer *player/* = NULL*/)
{
	World *pworld = getWorld(mapid);
	if(pworld == NULL) 
	{
		pworld = createWorld(mapid, GetIPlayerControl() && GetIPlayerControl()->GetIUin() == player->getUin());
	}

	if (GetIPlayerControl() && !pworld->isRemoteMode() && mapid > 0)
	{
		char sMapid[6];
		sprintf(sMapid, "%d", mapid);
		//GetIPlayerControl()->statisticToWorld(GetIPlayerControl()->GetIUin(), 30001, "", GetIPlayerControl()->getCurWorldType(), sMapid);
	}

	SandboxContext context(nullptr);
	context.SetData_Number("mapid", (double)mapid);
	context.SetData_RefEx("pworld", pworld);

	SandboxResult result = player->CastToActor()->Event().Emit("revive_getValidAccountWorldPoint", context);
	if (result.IsExecSuccessed()) {
		if (GetIPlayerControl() && GetIPlayerControl()->GetIUin() == player->getUin())
		{
			setSpawnPointEx(result.GetData_UserObject<WCoord>("spawn"), pworld);
			setRevivePointEx(result.GetData_UserObject<WCoord>("revive"), pworld);
		}
	}

	// WorldRenderer of old world will be destroy after jump to new world
	if (pworld->GetWorldRenderer() == nullptr)
	{
		WorldRenderer* wr = ENG_NEW(WorldRenderer)(pworld);
		pworld->SetWorldRenderer(wr);
	}

	return pworld;
}

int WorldManager::getCustomCameraOption(unsigned char option)
{
	if (m_CustomCamera && option < CAMERA_OPTION_INDEX_MAX)
		return m_CustomCamera->getOption(option);

	return -1;
}

void WorldManager::setSpawnPoint(const WCoord &pt)
{
	setSpawnPointRaw(pt);
	m_SpawnChunk = ChunkIndex(BlockDivSection(pt.x), BlockDivSection(pt.z));
}

void WorldManager::setSpawnPointEx(const WCoord &pt, World *pWorld)
{
	if (pWorld)
	{
		setSpawnPointRaw(pt);
		m_mapSpawnPoint[pWorld] = pt;		
	}
}

WCoord WorldManager::getSpawnPointEx(World *pWorld)
{
	std::map<World *, WCoord>::iterator iter = m_mapSpawnPoint.find(pWorld);
	if (iter != m_mapSpawnPoint.end())
	{
		return iter->second;
	}

	return m_SpawnPoint;
}

static int s_TeamSpawnOffset[8][2] = {{-1,0}, {1,0}, {0,-1}, {0,1}, {-1,-1}, {-1,1}, {1,-1},{1,1}};
static bool CanPlayerStandOnBlock(World *pworld, const WCoord &blockpos)
{
	return pworld->doesBlockHaveSolidTopSurface(blockpos) && pworld->isAirBlock(blockpos+WCoord(0,1,0)) && pworld->isAirBlock(blockpos+WCoord(0,2,0));
}

WCoord WorldManager::getRevivePointEx(World *pWorld)
{
	return getRevivePointEx(pWorld, m_HostRevivePoint);
}

WCoord WorldManager::getRevivePointEx(World* pWorld, const WCoord defaultPos)
{
	std::map<World*, WCoord>::iterator iter = m_mapHostRevivePoint.find(pWorld);
	if (iter != m_mapHostRevivePoint.end())
	{
		return iter->second;
	}

	return defaultPos;
}


WCoord WorldManager::getRandomRespawnPoint(World* pWorld)
{
	if (pWorld)
	{
	  return pWorld->getRandomRespawnPoint();
	}
	return WCoord(0, -1, 0);
}

void WorldManager::setRevivePointEx(const WCoord &pt, World *pWorld)
{
	if (pWorld)
	{
		setRevivePointRaw(pt);
		m_mapHostRevivePoint[pWorld] = pt;		
	}
}

void WorldManager::InitNewWorldPoint(IClientPlayer *player, World *pworld, bool bRemoteMode/* = false*/)
{
	if (!player || !pworld)
	{
		return;
	}

	bool bSetWorldMgr = GetIPlayerControl() && GetIPlayerControl()->GetIUin() == player->getUin();
	if (bSetWorldMgr && GetWorldManagerPtr())
	{
		GetWorldManagerPtr()->m_RenderEyeMap = pworld->getCurMapID();
	}

	/*
	SandboxContext context;
	context.SetData_Bool("bSetWorldMgr", bSetWorldMgr);
	context.SetData_RefEx("world", pworld);
	SandboxResult result = player->CastToActor()->Event().Emit("initNewWorldPoint", context);

	WCoord spawnpoint  = result.GetData_UserObject<WCoord>("spawn");
	WCoord revivepoint = result.GetData_UserObject<WCoord>("revive");
	if (result.IsExecSuccessed())
	{
		if (bSetWorldMgr)
		{
			setSpawnPointRaw(spawnpoint);
			setRevivePointRaw(WCoord(0, -1, 0));
		}

		WCoord tmpHostRevivePoint = WCoord(0, -1, 0);
		m_mapHostRevivePoint[pworld] = tmpHostRevivePoint;
		m_mapSpawnPoint[pworld] = spawnpoint;	
	}
	else
	{
		m_mapSpawnPoint[pworld] = spawnpoint;
		m_mapHostRevivePoint[pworld] = revivepoint;

		if (GetIPlayerControl() && GetIPlayerControl() == player)
		{
			setSpawnPointRaw(spawnpoint);
			setRevivePointRaw(revivepoint);
		}
	}
	*/
	if (player->GetPlayerWorld() && !player->GetPlayerWorld()->isRemoteMode() && !player->hasUIControl())
	{
		//player->checkChangeSpawnPoint(getSpawnPointEx(player->getWorld()), player->getWorld()->getCurMapID());
		SandboxContext context;
		context.SetData_UserObject("point", getSpawnPointEx(player->GetPlayerWorld()));
		context.SetData_Number("mapid", player->GetPlayerWorld()->getCurMapID());
		player->CastToActor()->Event().Emit("revive_checkChangeSpawnPoint", context);
	}
}

void WorldManager::SetDefaultRevivePoint(IClientPlayer* player, World* pworld, WCoord pos)
{
	if (player == NULL || pworld == NULL)
		return;

	WCoord defaultPos = WCoord(0, -1, 0);
	if (getRevivePointEx(pworld, defaultPos) != defaultPos) //已经通过道具设置了新复活点
		pos = getRevivePointEx(pworld, defaultPos);

	SandboxContext contextRevive;
	contextRevive.SetData_Bool("bSetWorldMgr", true);
	contextRevive.SetData_Bool("forced", true);
	contextRevive.SetData_UserObject("point", pos);
	player->CastToActor()->Event().Emit("revive_setRevivePointEx", contextRevive);
}

WCoord WorldManager::getTeamSpawnPoint(IClientPlayer *player, int teamId)
{
	if(m_RuleMgr)
	{
		int teamid = teamId > 0 ? teamId : player->iGetTeam();
		WCoord pt = WCoord(0, -1, 0);

		SandboxContext context;
		context.SetData_Number("mapid", player->GetPlayerWorld() ? player->GetPlayerWorld()->getCurMapID() : -1);

		SandboxResult result = player->CastToActor()->Event().Emit("revive_getLastInteractSpBlockList", context);
		if (result.IsExecSuccessed())
			pt = result.GetData_UserObject<WCoord>("point");
		else
			pt = m_RuleMgr->getSpawnPoint(teamid, player);
		
		if(pt.y < 0 && teamid > 0) pt = m_RuleMgr->getSpawnPoint(0, player);
		if(pt.y >= 0)
		{
			World *pworld = getWorld(0);
			if(pworld)
			{
				pworld->syncLoadChunk(pt, 1);
				int startindex = GenRandomInt(8);
				for(int i=0; i<8; i++)
				{
					int index = (startindex + i) % 8;
					WCoord pt2 = pt + WCoord(s_TeamSpawnOffset[index][0], 0, s_TeamSpawnOffset[index][1]);
					if(CanPlayerStandOnBlock(pworld,pt2)) return TopCoord(pt2);
					else if(CanPlayerStandOnBlock(pworld, DownCoord(pt2))) return pt2;
				}
			}
			return pt;
		}
	}

	WCoord pt = WCoord(0, -1, 0);
	if(player->GetPlayerWorld())
	{
		WCoord spawnpoint = WCoord(0, -1, 0);
		WCoord revivepoint = WCoord(0, -1, 0);

		SandboxContext context;
		context.SetData_UserObject("spawn", spawnpoint);
		context.SetData_UserObject("revive", revivepoint);
		context.SetData_Number("mapid", player->GetPlayerWorld()->getCurMapID());
		SandboxResult result = player->CastToActor()->Event().Emit("revive_getAccountWorldPoint", context);

		if (result.IsExecSuccessed())//player->getAccountWorldPoint(player->getWorld()->getCurMapID(), spawnpoint, revivepoint)
		{
			pt = result.GetData_UserObject<WCoord>("revive");//revivepoint;
			if(pt.y < 0) pt = result.GetData_UserObject<WCoord>("spawn"); //spawnpoint;
		}
		else
		{
			pt = getRevivePointEx(player->GetPlayerWorld());
			if(pt.y < 0) pt = getSpawnPointEx(player->GetPlayerWorld());
		}
	}
	else if (!player->GetPlayerWorld() && pt.y < 0 && getWorld(0) && (m_mapSpawnPoint.find(getWorld(0)) != m_mapSpawnPoint.end()))
	{
		std::map<World *, WCoord>::iterator iter = m_mapSpawnPoint.find(getWorld(0));
		pt = iter->second;
	} 
#ifdef IWORLD_SERVER_BUILD
	else{
		pt = getSpawnPoint();
	}
#endif

	return pt;
}

WCoord WorldManager::getTeamPrePoint(IClientPlayer *player, int teamId)
{
	if (m_RuleMgr)
	{ 
		if(player == NULL) return  WCoord(0, -1, 0);
		int teamid = teamId > 0 ? teamId : player->iGetTeam();
		WCoord pt = m_RuleMgr->getPrePoint(teamid, player);
		if (pt.y < 0 && teamid > 0) pt = m_RuleMgr->getPrePoint(0, player);
		if (pt.y >= 0)
		{
			World *pworld = getWorld(0);
			if(pworld == NULL) return  WCoord(0, -1, 0);
			pworld->syncLoadChunk(pt, 1);
			int startindex = GenRandomInt(8);
			for (int i = 0; i < 8; i++)
			{
				int index = (startindex + i) % 8;
				WCoord pt2 = pt + WCoord(s_TeamSpawnOffset[index][0], 0, s_TeamSpawnOffset[index][1]);
				if (CanPlayerStandOnBlock(pworld, pt2)) return TopCoord(pt2);
				else if (CanPlayerStandOnBlock(pworld, DownCoord(pt2))) return pt2;
			}
			return pt;
		}
	}

	return WCoord(0, -1, 0);
}
bool WorldManager::needSave(int flags)
{
#ifdef DEDICATED_SERVER
	return true;
#else
	{
		if (isRemote()) return false;

		if (!GetAutoSaveFlag())
			return false;

		return (m_NeedSaveFlags&flags) != 0;
	}
#endif
}



//判定某个方块位置是否在地图内,一个区块等于16方块，区块=0表示无限长
// 20210811：codeby： fuqihang
// 20210917: codeby: lulei. 前一版"fuqihang"的判断方块是否在世界外有问题, 导致区域工具在位置x<0||z<0的地方没法使用
bool WorldManager::isBlockInMap(const WCoord &blockpos)
{
	bool xInMap = false, zInMap = false;
	int xStart = 0, xEnd = 0, zStart = 0, zEnd = 0;

	NumTiles2StartEnd(xStart, xEnd, m_worldCreateData.xtiles);
	NumTiles2StartEnd(zStart, zEnd, m_worldCreateData.ztiles);
	xStart *= CHUNK_SECTION_DIM;
	zStart *= CHUNK_SECTION_DIM;
	xEnd = xEnd * CHUNK_SECTION_DIM + CHUNK_SECTION_DIM - 1;
	zEnd = zEnd * CHUNK_SECTION_DIM + CHUNK_SECTION_DIM - 1;


	if (m_worldCreateData.xtiles == 0) {
		xInMap = true;
	}
	else if (blockpos.x >= xStart && blockpos.x <= xEnd) {
		xInMap = true;
	}
	if (m_worldCreateData.ztiles == 0) {
		zInMap = true;
	}
	else if (blockpos.z >= zStart && blockpos.z <= zEnd) {
		zInMap = true;
	}
	return xInMap && zInMap;
}

void WorldManager::addUnlockItem(int condUnlcokType)
{
	if(!isUnlockItem(condUnlcokType))
		m_UnlockItems.push_back(condUnlcokType);
}

bool WorldManager::isUnlockItem(int condUnlcokType)
{
	for(size_t i=0; i<m_UnlockItems.size(); i++)
	{
		if(m_UnlockItems[i] == condUnlcokType) return true;
	}

	return false;
}

void WorldManager::setTraderCertainID(int itemid)
{
	m_TraderCertainID = itemid;
}

void WorldManager::addLandingPoint(int mapid, const WCoord &pt, bool reset/* =false */)
{
	if (mapid < MAX_MAP)
	{
		if (getLandingPoint(mapid).y < 0 || reset)
		{
			m_LandingPoints[mapid] = pt;
		}
	}
}

const WCoord& WorldManager::getLandingPoint(int mapid)
{
	static WCoord defpt(0, -1, 0);
	if(mapid < MAX_MAP)
		return m_LandingPoints[mapid];

	return defpt;
}

static int landingoffset[9][2] = {
	{ -1, -1 },{ 0, -1 },{ 1,-1 },{ -1, 0 },{ 0, 0 },{ 1, 0 },{ -1, 1 },{ 0, 1 },{ 1, 1 }
};


void WorldManager::addBossPoint(const WCoord &pt, int mapid)
{
	if (mapid < MAX_MAP)
	{
		m_BossPoints[mapid] = pt;
	}
}

void WorldManager::removeBossPoint(int mapid)
{
	if (mapid < MAX_MAP)
	{
		m_BossPoints[mapid] = WCoord(0,0,0);
	}
}

WCoord& WorldManager::getBossPoint(int mapid)
{
	static WCoord defpt(0, 0, 0);
	if(mapid < MAX_MAP)
		return m_BossPoints[mapid];

	return defpt;
}

const WCoord WorldManager::getRocketRealLandingPt(WCoord landingPt, World *pworld)
{
	WCoord realPt = landingPt;
	for (int i = 0; i < 30; i++)
	{
		WCoord checkPt = realPt;
		for (int j = landingPt.y; j > 0; j--)
		{
			bool canMove = true;
			for (int k = 0; k < 9; k++)
			{
				int offsetX = landingoffset[k][0];
				int offsetZ = landingoffset[k][1];
				int blockid = pworld->getBlockID(WCoord(checkPt.x + offsetX, j, checkPt.z + offsetZ));
				int colliderType = pworld->getBlockMaterial(WCoord(checkPt.x, j, checkPt.z))->getMoveCollider();
				if (colliderType != 0 && colliderType != 2)
				{
					canMove = false;
					break;
				}
			}

			if (!canMove)
			{
				checkPt.y = (j + 1);
				break;
			}
		}

		if (realPt.y <= checkPt.y)
		{
			realPt = landingPt + WCoord(GenRandomInt(-16, 16), 0, GenRandomInt(-16, 16));
			continue;
		}

		IClientActor *actor = pworld->findNearestActor(checkPt*BLOCK_SIZE, 1, OBJ_TYPE_ROCKET);
		if (!actor || actor->getSquareDistToPos(checkPt.x*BLOCK_SIZE, checkPt.y*BLOCK_SIZE, checkPt.z*BLOCK_SIZE) > 300 * 300) //附加3格没有火箭
		{
			return realPt;
		}
		else
		{
			realPt = landingPt + WCoord(GenRandomInt(-16, 16), 0, GenRandomInt(-16, 16));
		}

	}

	return landingPt + WCoord(GenRandomInt(-16, 16), 0, GenRandomInt(-16, 16));
}

void WorldManager::getGameRuleValue(int ruleId, float &val)
{
	if(m_RuleMgr)
	{
		val = m_RuleMgr->getRuleOptionVal((GAMEMAKER_RULE)ruleId);
	}
}

void WorldManager::setGameRuleValue(int ruleId, float val)
{
	if(m_RuleMgr)
	{
		m_RuleMgr->setNearestGameRule((GAMEMAKER_RULE)ruleId, val);
	}
}
void WorldManager::getRuleOptionID(int ruleId, int &opId, float &val)
{
	if (m_RuleMgr)
	{
		m_RuleMgr->getRuleOptionID(ruleId, opId, val);
	}
}

void WorldManager::syncAllPlayersCustomModel()
{
	getAllPlayers(m_AllPlayers);
	for (size_t i = 0; i < m_AllPlayers.size(); i++)
	{
		m_AllPlayers[i]->setSyncCustomModelTick(0);
	}
}

void WorldManager::syncPlayerTransferData(int uin)
{
	IClientPlayer* player = getPlayerByUin(uin);
	if (player)
	{
		player->setSyncTransferTick(0);
	}
}

void WorldManager::resetAllPlayerHandModel()
{
	getAllPlayers(m_AllPlayers);
	for (size_t i = 0; i < m_AllPlayers.size(); i++)
	{
		m_AllPlayers[i]->applyEquips(EQUIP_WEAPON);

		//装备
		for (int slot = 0; slot < EQUIP_WEAPON; ++slot)
			m_AllPlayers[i]->applyEquips((EQUIP_SLOT_TYPE)slot);
	}

}

bool WorldManager::getIsEmptyFlatCreated()
{
	return m_isEmptyFlatCreated;
}

void WorldManager::setIsEmptyFlatCreated(bool isEmptyFlatCreated)
{
	m_isEmptyFlatCreated = isEmptyFlatCreated;
}
void WorldManager::setTimeHour(int hour)
{
	if (m_TimeHour != hour)
	{
		m_TimeHour = hour;

		// 观察者事件接口
		ObserverEvent obevent;
		obevent.SetData_Time(-1, hour);
		GetObserverEventManager().OnTriggerEvent("Game.Hour", &obevent);
	}
}
void WorldManager::resetTimeHour()
{
	m_TimeHour = -1;
}

void WorldManager::resetDataByToggleGameMakerMode()
{
	if (!GetIPlayerControl())
		return;

	World *pworld = GetIPlayerControl()->getIWorld();
	if (!pworld)
		return;

	//auto RidComp = GetIPlayerControl()->getRiddenComponent();
	IClientActor* clientactor = dynamic_cast<IClientActor*>(GetIPlayerControl());
	auto RidComp = clientactor->getActorComponent(ComponentType::COMPONENT_RIDDEN);
	if (RidComp)
	{
		//取消骑乘
		bool result = RidComp->Event2().Emit<IClientActor*, IClientActor*>("Ridden_mountActor", clientactor, nullptr);
		Assert(result);
	}
	
	//取消音效/音乐
	auto sound = GetIPlayerControl()->ControlCastToActor()->getActorComponent(ComponentType::COMPONENT_SOUND);
	if (sound)
	{
		sound->Event2().Emit<>("stopSoundByTrigger");
	}
	GetIPlayerControl()->CastToPlayer()->stopMusicByTrigger(NULL);

	std::map<int, World *>::iterator iter = m_Worlds.begin();
	for (; iter != m_Worlds.end(); iter++)
	{
		World *pworld = iter->second;
		//清除容器
		WorldContainerMgr* containerMgr =  dynamic_cast<WorldContainerMgr*>(pworld->getContainerMgr());	
		if(containerMgr){ 		
			if (containerMgr) containerMgr->clearContainers(false);
		}

		//清除生物
		ActorManagerInterface* actorMgr = pworld->getActorMgr();
		if (actorMgr)
			actorMgr->resetByToggleGameMakerMode();

		//清理特效
		pworld->getEffectMgr()->clearParticleEffect();

		//清除chunk
		clearWorldChunk(pworld->getCurMapID(), true);

		//取消音效播放
		pworld->getEffectMgr()->stopAllSoundEffects();
		//取消所有长音效播放
		pworld->getEffectMgr()->stopAllLongSounds();

		// 重新加载场景树
		pworld->ReloadWorldScene();
	}

	//重新load时间
	loadFileByToggleGameMakerMode(pworld->getOWID(), pworld->getMapSpecialType());

	//重新load天气
	WorldMapData *mapdata = getMapData(pworld->getCurMapID());
	if (mapdata && pworld->m_Environ)
	{
		pworld->m_Environ->load(mapdata);
	}

	MNSandbox::GetGlobalEvent().Emit<WorldMapData *>("StatisticRainforest_load", mapdata);

	//
	{
		auto mgr = MNSandbox::GetCurrentSceneMgrService();
		if (mgr) {
			mgr->ReqSwitchScene(mgr->GetDefaultSceneId());
		}
	}

	//重置角色相关
	GetIPlayerControl()->onToggleGameMode();

	//重置方块设置
	g_BlockMtlMgr.resetBlockSettingAtt();

	//清除区块缓存文件
	clearHostTrunk();

	m_LockTime = -1; // 玩法转编辑非存档清理锁定时间设置
}

void WorldManager::clearHostTrunk()
{
	core::string fullpath;
	GetFileManager().ToWritePathFull("data/cache_host_trunk", fullpath);
	#if STUDIO_SERVER || BUILD_MINI_EDITOR_APP
	if (!GetFileManager().IsFileExist(fullpath.c_str()))
	{
		return;
	}
	#endif
	DirVisitorDelete delDir;
	delDir.scanTree(fullpath.c_str());
}

//区域工具相关********************************************************************************
bool WorldManager::isGameMakerToolMode()
{
	if(m_gameMode == OWTYPE_GAMEMAKER)
	{
		if(m_bIsToolMode == TOOL_MODE_NONE)
			return false;
		else
			return true;
	}

	return false;
}

bool WorldManager::isAreaToolMode()
{
	return m_bIsToolMode == TOOL_MODE_AREA;
}

bool WorldManager::isPositionToolMode()
{
	return m_bIsToolMode == TOOL_MODE_POINT;
}

bool WorldManager::isActorToolMode()
{
	return m_bIsToolMode == TOOL_MODE_ACTOR;
}

//20211025:对象库工具模式添加显示板模式 codeby caiqizhen
bool WorldManager::isDisplayBoardToolMode() 
{
	return m_bIsToolMode == TOOL_MODE_DISPLAYBOARD;
}

bool WorldManager::gotoToolMode(World* pworld, int nToolModeState)
{
	if(m_gameMode == OWTYPE_GAMEMAKER)
	{
		m_bIsToolMode = nToolModeState;
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_onGotoToolMode", SandboxContext(nullptr).
			SetData_Number("nToolModeState", nToolModeState).
			SetData_Bool("bIsEnter", true));

		
		return true;
	}

	return false;
}

void WorldManager::quitToolMode()
{
	m_bIsToolMode = TOOL_MODE_NONE;
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_onGotoToolMode", SandboxContext(nullptr).
		SetData_Bool("bIsEnter", false));
}

int WorldManager::getToolModeCurType()
{
	return m_bIsToolMode;
}

void WorldManager::setDeveloperFlag(int flag)
{
	if (flag <= 0) { return; }
	m_iDeveloperFlag = flag;
}

void WorldManager::setRealOwnerUin(int uin)
{
	m_RealOwnerUin = uin;
}

void WorldManager::setCurWorldName(const char* worldName)
{
	if (worldName != NULL) m_WorldName = worldName;
}

bool WorldManager::getPlayerPermit(int pType, int teamId)
{
	if (m_RuleMgr && isGameMakerRunMode())
	{
		return m_RuleMgr->getPlayerPermit(pType, teamId);
	}

	return true;
}

bool WorldManager::getNeedShowIntroFrame()
{
	if (isGameMakerRunMode() && m_RuleMgr)
	{
		return m_RuleMgr->getNeedShowIntroFrame();
	}

	return false;
}

bool WorldManager::IsRentServerHost()
{
//云服主机的宏定义
#ifdef IWORLD_SERVER_BUILD
	return true;
#else
	return false;
#endif
}

void WorldManager::checkNoPlayerToExit()
{
#ifdef DEDICATED_SERVER
	bool noPlayer = false;
	for (auto iter = m_Worlds.begin(); iter != m_Worlds.end(); iter++)
	{
		ActorManagerInterface* actormgr = iter->second->getActorMgr();
		int numPlayer = actormgr->getNumPlayer();
		if (0 == numPlayer) {
			noPlayer = true;
		}
	}

	if (noPlayer && GetICloudProxyPtr()->ExitOnNoPlayer()) {
		GetClientInfoProxy()->hostAutoExit();
	}
#endif
}


void WorldManager::notifySFActivity2Tracking(int type, int id, int value)
{

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("SpringFestival_sfActivity2Tracking", SandboxContext(nullptr).SetData_Number("type", type).SetData_Number("id", id).SetData_Number("value", value));
}

void WorldManager::recordPlayerGameTime()
{
	/*bool ret = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("SpringFestival_interface_exec", ret);
	if (ret)
	{
		int num = m_WorldTime % 150;
		if (num == 1)
		{
			getAllPlayers(m_AllPlayers);
			if (m_isvrTime == 0) m_isvrTime = GetClientInfoProxy()->getSvrTime() - 30;
			int time = GetClientInfoProxy()->getSvrTime() - m_isvrTime;
			m_isvrTime = GetClientInfoProxy()->getSvrTime();

			for (size_t i = 0; i < m_AllPlayers.size(); i++)
			{
				IClientPlayer *player = m_AllPlayers[i];
				WCoord pos = player->getPosition();
				if (player->getWorld())
				{
					int biometype = player->getWorld()->getBiomeType(CoordDivBlock(pos.x), CoordDivBlock(pos.z));
					if (BIOME_INVALID == biometype) biometype = -1;
					player->addSFActivity(SFACTIVITY_GAMETIME, 1, time, !player->hasUIControl());
					player->addSFActivity(SFACTIVITY_EXPLORE, biometype, time, !player->hasUIControl());
				}
			}
		}
	}*/
}

void WorldManager::SetAutoSaveFlag(bool bAutoSave, int nDurationTicks/*= 200*/)
{
	//bAutoSave: true:开启 false:关闭自动保存
	//nDurationTicks: 关闭自动保存的时间, 默认200tick, 即10S
	m_bAutoSaveFlag = bAutoSave;
	m_nAutoSaveFlagDurationTicks = nDurationTicks;
}

bool WorldManager::GetAutoSaveFlag()
{
	return m_bAutoSaveFlag;
}

void WorldManager::TickAutoSaveFlag()
{
	if (!m_bAutoSaveFlag)
	{
		if (m_nAutoSaveFlagDurationTicks <= 0)
		{
			m_bAutoSaveFlag = true;
		}

		m_nAutoSaveFlagDurationTicks--;
	}
}

void WorldManager::registerGraphicsVec3Callback(const UPDATEGRAPHICSVEC3_FUNC& func, const REMOVEGRAPHICS_FUNC& removeFunc)
{
	m_updategraphicsvec = func;
	m_removeGraphics = removeFunc;
}

bool WorldManager::IsUinInHistoryList(long long owid, int uin)
{
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveTempMgr_IsUinInHistoryList",SandboxContext(nullptr).SetData_Number("mapid",owid).SetData_Number("uin",uin));
	if (result.IsExecSuccessed())
	{
		return result.GetData_Bool();
	}
	return false;
}

void WorldManager::addHolyTreePlantInfo(const WCoord &pt, int mapid)
{
	PlantInfo info;
	info.pos = pt;
	info.planttime = m_WorldTime;

	auto iter = m_HolyTreePlantTime.find(mapid);
	if (iter == m_HolyTreePlantTime.end())
	{
		std::vector<PlantInfo> plantInfos;
		plantInfos.push_back(info);

		m_HolyTreePlantTime[mapid] = plantInfos;
	}
	else
		iter->second.push_back(info);
}

void WorldManager::removeHolyTreePlantInfo(const WCoord &pt, int mapid)
{
	auto iter = m_HolyTreePlantTime.find(mapid);
	if (iter != m_HolyTreePlantTime.end())
	{
		auto iterInfo = iter->second.begin();
		for (; iterInfo != iter->second.end(); iterInfo++)
		{
			if (iterInfo->pos == pt)
			{
				iter->second.erase(iterInfo);
				return;
			}
		}
	}
}

void WorldManager::checkHolyTreePlantInfo()
{
	auto iter = m_HolyTreePlantTime.begin();
	for (; iter != m_HolyTreePlantTime.end(); iter++)
	{
		auto *pWorld = getWorld(iter->first);
		if(!pWorld)
			continue;

		auto iterInfo = iter->second.begin();
		for (; iterInfo != iter->second.end(); )
		{
			int curid = pWorld->getBlockID(iterInfo->pos);
			if (curid == BLOCK_SAPLING_HOLY)
			{
				BlockMaterial *ptml = g_BlockMtlMgr.getMaterial(curid);
				int realGrowTime = m_WorldTime - iterInfo->planttime;
				int data = pWorld->getBlockData(iterInfo->pos);
				int &needGrowTime = GetWorldManagerPtr()->m_SurviveGameConfig->holytreecfg.growth_tick;
				int growStageTime = needGrowTime / 2;
				if (ptml)
				{
					if (((data == 0 || data == 1) && realGrowTime > growStageTime))
					{
						ptml->DoBlockTick(pWorld, iterInfo->pos);
						data = pWorld->getBlockData(iterInfo->pos);
					}
						
					if(data == 2 && realGrowTime >= needGrowTime)
					{
						ptml->DoBlockTick(pWorld, iterInfo->pos);
					}
				}
				iterInfo++;
			}
			else
				iterInfo = iter->second.erase(iterInfo);
		}
	}
}

void WorldManager::setPlantInfo(const WCoord& pt, int mapid, int time)
{
	int planttime = (time == -1) ? getDayNightTime() : time;
	auto iter = m_plantTime.find(mapid);
	if (iter == m_plantTime.end())
	{
		std::map<WCoord, PlantTimeInfo> plantInfos;
		PlantTimeInfo timeinfo;
		timeinfo.planttime = planttime;
		timeinfo.fertilizeduptime = 0;
		plantInfos[pt] = timeinfo;
		m_plantTime[mapid] = plantInfos;
	}
	else
	{
		if (iter->second.find(pt) == iter->second.end())
		{
			PlantTimeInfo timeinfo;
			timeinfo.planttime = planttime;
			timeinfo.fertilizeduptime = 0;
			iter->second[pt] = timeinfo;
		}
		else
		{
			PlantTimeInfo timeinfo = iter->second[pt];
			timeinfo.planttime = planttime;
			iter->second[pt] = timeinfo;
		}
		
	}		
}

void WorldManager::removePlantInfo(const WCoord& pt, int mapid)
{
	auto iter = m_plantTime.find(mapid);
	if (iter != m_plantTime.end())
	{
		auto plantiter1 = iter->second.find(pt);
		if (plantiter1 != iter->second.end())
		{
			iter->second.erase(plantiter1);
		}
	}
}

int WorldManager::getPlantTime(const WCoord& pt, int mapid)
{
	auto iter = m_plantTime.find(mapid);
	if (iter != m_plantTime.end())
	{
		auto plantiter1 = iter->second.find(pt);
		if (plantiter1 != iter->second.end())
		{
			return plantiter1->second.planttime;
		}
	}
	return -1;

}

bool WorldManager::CheckIsUnlock(long long objid, int id)
{
	auto iter = m_mapPlayerCookBooks.find(objid);

	if (iter != m_mapPlayerCookBooks.end())
	{
		CookBookInfo cookbook;
		cookbook.id = id;
		auto cbiter = std::find(iter->second.begin(), iter->second.end(), cookbook);
		if (cbiter != iter->second.end())
		{
			return true;
		}
	}
	return false;
}

void WorldManager::AddCookBook(long long objid, int cookbookid, int state)
{
	CookBookInfo cookbook;
	cookbook.id = cookbookid;
	cookbook.state = state;
	if (m_mapPlayerCookBooks.find(objid) == m_mapPlayerCookBooks.end())
	{
		std::vector<CookBookInfo> cooks;
		cooks.push_back(cookbook);
		m_mapPlayerCookBooks[objid] = cooks;
	}
	else
	{
		auto iter = m_mapPlayerCookBooks.find(objid);
		auto citer = std::find(iter->second.begin(), iter->second.end(), cookbook);
		if (citer != iter->second.end())
		{
			citer->state = cookbook.state;
		}
		else
		{
			iter->second.push_back(cookbook);
		}
	}
}

int WorldManager::getFertilizedUpTime(const WCoord& pt, int mapid)
{
	auto iter = m_plantTime.find(mapid);
	if (iter != m_plantTime.end())
	{
		auto plantiter1 = iter->second.find(pt);
		if (plantiter1 != iter->second.end())
		{
			return plantiter1->second.fertilizeduptime;
		}
	}
	return 0;
}

void  WorldManager::addFertilizedUpTime(const WCoord& pt, int mapid, int time)
{
	auto iter = m_plantTime.find(mapid);
	if (iter != m_plantTime.end())
	{
		auto infoiter = iter->second.find(pt);
		if (infoiter != iter->second.end())
		{
			infoiter->second.fertilizeduptime += time;
		}
	}
}



//void WorldManager::OnExecute(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen)
//{
//	if (wEventID == SandBoxMgrEventID::EVENT_VOLCANO_APPEAR )
//	{
//		/*BossPos *bosspos = (BossPos *)pszContext; 
//		if (bosspos->pWorld)
//		{
//			WorldMapData *destdata = getMapData(bosspos->pWorld->getCurMapID());
//			if (destdata)
//			{
//				for (int i = 0; i<destdata->bosses.size(); i++)
//				{
//					if (destdata->bosses[i].defid == 3515 && destdata->bosses[i].flags == 2)
//					{
//						ClientVacantBoss *actor = ENG_NEW(ClientVacantBoss)();
//						actor->init(3516);//召唤虚空boss
//						actor->setSpawnPoint(BlockBottomCenter(bosspos->pos));
//						if (bosspos->pWorld)
//						{
//							bosspos->pWorld->syncLoadChunk(CoordDivSection(actor->getPosition().x), CoordDivSection(actor->getPosition().z));
//							bosspos->pWorld->getActorMgr()->spawnBoss(actor);
//							actor->startAppeal();
//							ChunkGenNormal* gen = dynamic_cast<ChunkGenNormal*>(bosspos->pWorld->getChunkProvider());
//							if (gen)
//							{
//								WCoord blockpos = CoordDivBlock(actor->getPosition());
//								gen->setBossPos(blockpos);
//							}
//						}
//					}
//				}
//			}
//		}*/
//	}
//}

void WorldManager::setLockTime(int time)
{
	// 开发者地图玩法模式
	// 新增冒险强制引导锁定地图时间
	if ((m_RuleMgr && m_RuleMgr->getRuleOptionVal(GMRULE_TIMELOCKED) > 0 && (m_gameMode == OWTYPE_GAMEMAKER_RUN || m_gameMode == OWTYPE_GAMEMAKER_STUDIO_EDIT || m_gameMode == OWTYPE_GAMEMAKER_SANDBOXNODE_RUN))
		|| (!GetAdventureGuideMgrProxy()->canTimeGo() && isAdventureMode()))
	{
		m_LockTime = time; 
	}
	else
	{
		m_LockTime = -1;
	}

}

IClientActor* WorldManager::cloneActor(std::string type_name)
{
	auto temp = m_AcotorTemplates.find(type_name);
	if (temp != m_AcotorTemplates.end())
	{
		return temp->second->IClone();
	}
	else
	{
		return NULL;
	}
}

int WorldManager::getCameraEditState()
{
	return CameraManager::GetInstance().getCameraEditState();
}

void WorldManager::setCameraEditState(int state)
{
	CameraManager::GetInstance().setCameraEditState(state);
}
//#endif

WorldInfoManager* WorldManager::getWorldInfoManager()
{
	if(!getSandboxMgr("WorldInfo"))
	{
		addManager("WorldInfo", ENG_NEW(WorldInfoManager)(this));
	}
	return static_cast<WorldInfoManager*>(getSandboxMgr("WorldInfo"));
}

DangerNightManagerInterface* WorldManager::getDangerNightManager()
{
	if (!m_enableDangerNight)
	{
		return nullptr;
	}

	if (!canOpenDangerNight())
	{
		setEnableDangerNight(false);
		return nullptr;
	}

	if (!getSandboxMgr("DangerNightMgr"))
	{
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("WorldManager_DangerNight_Create", SandboxContext(nullptr));
		if (!result.IsExecSuccessed())
		{
			Assert(false);
		}
	}
	return dynamic_cast<DangerNightManagerInterface*>(getSandboxMgr("DangerNightMgr"));
}

bool WorldManager::isVoidNight()
{
	DangerNightManagerInterface*pDNMgr = getDangerNightManager();
	if (pDNMgr)
	{
		return pDNMgr->isVoidNight();
	}

	return false;
}

bool WorldManager::isZombieWave()
{
	return getModWeatherType() == ZOMBIE_WAVE;
}

void WorldManager::setGameLeaderUin(int uin)
{
	//租赁服下维持为服主
#ifdef  DEDICATED_SERVER
	if (GetClientInfoProxy()->isRentServerMode())
	{
		uin =  GameNetManager::getInstance()->getHostUin();
	}
#endif // DEDICATED_SERVER

	int oldUin = m_GameLeaderUin;
	m_GameLeaderUin = uin;
	MINIW::ScriptVM::game()->callFunction("RentGameLeaderSwitched", "ii", oldUin, m_GameLeaderUin);
}

int WorldManager::getGameLeaderUin()
{
	//租赁服下返回服主
#ifdef  DEDICATED_SERVER
	if (GetClientInfoProxy()->isRentServerMode())
	{
		return GameNetManager::getInstance()->getHostUin();
	}
	if (GetClientInfoProxy()->isRentType(1))
	{
		auto cliInfo = GetClientInfoProxy();
		if (cliInfo)
		{
			const char* s_uin = cliInfo->getEnterParam("account");
			if (s_uin)
			{
				int owner_uin = atoi(s_uin);
				return owner_uin;
			}
		}
	}
#endif  // DEDICATED_SERVER

	return m_GameLeaderUin;
}

bool WorldManager::IsAttackedProtect()
{
	//默认是开启受击保护的
	if (m_RuleMgr)
	{
		float optionVal = m_RuleMgr->getRuleOptionVal(GMRULE_BEHURT);
		return optionVal > 0;
	}

	return true;
}

bool WorldManager::isStudioGameMode()
{
	return MNSandbox::Config::GetSingleton().IsSandboxMode();
}

bool WorldManager::isUGCMode()
{
	auto pCurGame = getUGCGame();
	if (!pCurGame)
		return false;
	return pCurGame->isUGCMode(0);
}

bool WorldManager::isUGCEditMode()
{
	auto pCurGame = getUGCGame();
	if (!pCurGame)
		return false;
	return pCurGame->isUGCMode(1);
}

bool WorldManager::isUGCEditBuildMode()
{
	auto pCurGame = getUGCGame();
	if (!pCurGame)
		return false;
	return pCurGame->isUGCMode(2);
}

void WorldManager::setPrefabSceneVisible(bool visible)
{
	m_PrefabSceneVisible = visible;
}

bool WorldManager::getPrefabSceneVisible()
{
	return m_PrefabSceneVisible;
}
int WorldManager::getComboAttack()
{
	if (GetIModPackMgr()->IsCustomRule())
	{
		return GetIModPackMgr()->GetCanDoRuleByType(CanOpenComboAttack);
	}
	if (m_RuleMgr)
	{
		return m_RuleMgr->getComboAttack();
	}
	return 1;
}

bool WorldManager::canOpenDangerNight()
{ 
	return GetIModPackMgr()->GetCanDoRuleByType(CanOpenDangerNight);
}
bool WorldManager::canOpenLevelMode()
{
	if (m_RuleMgr != nullptr)
	{
		return !m_RuleMgr->isNoExpMode();
	}
	return GetIModPackMgr()->GetCanDoRuleByType(CanOpenLevelExpMode);
}
bool WorldManager::canSpawnSuviveBoss()
{
	return GetIModPackMgr()->GetCanDoRuleByType(CanSpawnSuviveBoss);
}
bool WorldManager::canSleepToSkipNight() 
{ 
	return GetIModPackMgr()->GetCanDoRuleByType(CanSleepToSkipNight);
}
bool WorldManager::canSpwanTravelTrader()
{ 
	return GetIModPackMgr()->GetCanDoRuleByType(CanSpwanTravelTrader);
}
bool WorldManager::canOpenAttrPropSet()
{
	return GetIModPackMgr()->IsCustomRule() && GetIModPackMgr()->GetCanDoRuleByType(CanOpenAttrPropSet);
}

bool WorldManager::canWaterPressAffectPlayer()
{
	return GetIModPackMgr()->GetCanDoRuleByType(CanWaterPressAffectPlayer);
}

bool WorldManager::canOpenMachineRepair()
{
	return GetIModPackMgr()->GetCanDoRuleByType(CanOpenMachineRepair);
}

int WorldManager::getModWeatherType()
{
	if (GetIModPackMgr()->IsCustomRule())
	{
		return GetIModPackMgr()->getWeatherType();
	}
	return 0;
}

int WorldManager::getModAttackType()
{
	if (GetIModPackMgr()->IsCustomRule())
	{
		return GetIModPackMgr()->getAttackType();
	}
	return -1;
}

//GameStatic<WorldManager>  s_WorldManager(kInitManual);
//WorldManager& GetWorldManager()
//{
//	return *g_WorldMgr;//*s_WorldManager.EnsureInitialized();
//}

WorldManager * GetWorldManagerPtr()
{
	return g_WorldMgr;
}

#ifdef BUILD_MINI_EDITOR_APP
void WorldManager::addExtendScene(const int sceneId, Rainbow::GameScene* gsScene)
{
	auto oldBpScene = getExtendScene(sceneId);
	if (oldBpScene != nullptr)
	{
		//修改
		m_gsScenes[sceneId] = gsScene;
	}
	else
	{
		//新增
		m_gsScenes.insert(make_pair(sceneId, gsScene));
	}
}

void WorldManager::deleteExtendScene(const int sceneId)
{
	for (auto iter = m_gsScenes.begin(); iter != m_gsScenes.end();)
	{
		if (sceneId == iter->first)
		{
			Rainbow::GetEngine().GetWorld()->RemoveGameScene(iter->second);
			iter = m_gsScenes.erase(iter);
		}
		else
		{
			++iter;
		}
	}
}

Rainbow::GameScene * WorldManager::getExtendScene(const int sceneId)
{
	auto it = m_gsScenes.find(sceneId);
	if (it != m_gsScenes.end())
	{
		return it->second;
	}
	return nullptr;
}

Rainbow::GameScene* WorldManager::getExtendFocusScene()
{
	for (auto iter = m_gsScenes.begin(); iter != m_gsScenes.end(); iter++)
	{
		ExtendScene* scene = dynamic_cast<ExtendScene*>(iter->second);
		if (scene && scene->isShow())
		{
			return iter->second;
		}
	}
	return nullptr;
}

void WorldManager::setBoneScene(NewBoneScene* boneScene)
{
	m_boneScene = boneScene;
}

void WorldManager::deleteBoneScene()
{
	if (m_boneScene)
	{
		Rainbow::GetEngine().GetWorld()->RemoveGameScene(m_boneScene);
		m_boneScene = nullptr;
	}
}

NewBoneScene* WorldManager::getBoneScene()
{
	return m_boneScene;
}
#endif


void SetWorldManagerPtr(WorldManager* wg)
{
	if (wg != nullptr && g_WorldMgr != nullptr && wg != g_WorldMgr)
	{
		//OGRE_DELETE(g_WorldMgr);
		ErrorStringMsg("SetWorldManagerPtr Error!");
	}
	g_WorldMgr = wg;
}

ChunkIOMgr* GetChunkIOMgr()
{
	if (GetWorldManagerPtr() != nullptr)
		return GetWorldManagerPtr()->m_ChunkIOMgr;
	if (g_TestChunkIOMgr != nullptr)
		return g_TestChunkIOMgr;
	return nullptr;
}

bool WorldManager::surviveGameToggleDiffMode()
{
	if (NORMAL_WORLD != m_nSpecialType)
	{
		return false;
	}

	WorldDesc* desc = GetClientInfoProxy()->findWorldDesc(m_worldId);
	if (desc == NULL)
		return false;
	
	if (!OwTypeAdvantureModeSwith())
		return false;

	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_TOGGLE_GAMEMODE", MNSandbox::SandboxContext(nullptr));
	return true;
}

//冒险经典和简单模式切换
bool WorldManager::OwTypeAdvantureModeSwith()
{
	if (!GetIPlayerControl())
		return false;

	World* pworld = GetIPlayerControl()->getIWorld();
	if (!pworld)
		return false;
	
	m_isEasyMode = !m_isEasyMode;
	
	GetClientInfoProxy()->saveWorldModeDiff(m_worldId, m_isEasyMode);

	pworld->getActorMgr()->resetLiveMobAttr();

	return true;
}

void WorldManager::TickTravelingTraderInfo()
{
	if (!GetIPlayerControl())
		return;

	World* world_groud = getWorld(MAPID_GROUND);
	if (!world_groud)
		return;

	if (world_groud->isRemoteMode())
		return;

	if (!isAdventureMode())
		return;

	//完成某成就: 好好休息一下
	auto result_task = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_GetTaskState", MNSandbox::SandboxContext(nullptr).SetData_Number("objId", GetIPlayerControl()->CastToPlayer()->iGetObjId()).SetData_Number("taskId", 1002014));
	if (result_task.IsFailed() || result_task.GetData_Number("taskState") != TASK_COMPLETE)
		return;

	//游戏天数检查
	int curDayNum = getWorldTimeDay();
	if (curDayNum < 10)
		return;

	//检查冷却时间
	if (curDayNum - m_travelingTraderInfo.lastTimePoint < m_travelingTraderInfo.nextTimeLength)
		return;

	//是否绑定床位
	if (m_travelingTraderInfo.housingLevel == 0)
	{
		float curTimeHour = getHours();
		if (curTimeHour >= 7.0f && curTimeHour <= 13.0f)
		{
			if (!m_travelingTraderInfo.inHome) //1 出现
			{
				m_travelingTraderInfo.inHome = true;
				m_travelingTraderInfo.lastInHomePos = WCoord(0, -1, 0);
				//刷新默认商品
				loadTravelingTraderGoodsInfo(1);
			}
		}
		else
		{
			if (m_travelingTraderInfo.inHome) //0 消失
			{
				m_travelingTraderInfo.inHome = false;
				m_travelingTraderInfo.lastTimePoint = curDayNum;
				m_travelingTraderInfo.nextTimeLength = GenRandomInt(6, 10);
			}
		}
	}
	else if (m_travelingTraderInfo.housingLevel > 0)
	{
		if (m_travelingTraderInfo.inHome) //0 旅行
		{
			m_travelingTraderInfo.inHome = false;
			m_travelingTraderInfo.lastTimePoint = curDayNum;
			m_travelingTraderInfo.nextTimeLength = m_travelingTraderInfo.housingLevel == 1 ? GenRandomInt(3, 5) : GenRandomInt(2, 4);
			//搜索绑定点周围的生态
			m_travelingTraderInfo.biome = -1;
			m_travelingTraderInfo.biomePos = WCoord::zero;
			WCoord centerPos = getRevivePointEx(world_groud);
			if (centerPos.y < 0) centerPos = getSpawnPointEx(world_groud);
			//[游商旅行配置] 生态数组
			static const int biomeArr[]{ 1039, 1002, 1013, 1019, 1008, 1040, 1043 };
			static const int biomeTypeIdx[]{ 0, 1, 3, 4, 5, 6 }; //雨林 沙漠 沙滩 雪山 空岛 火山
			//搜索
			size_t biomeTypeLen = sizeof(biomeTypeIdx) / sizeof(biomeTypeIdx[0]);
			size_t startIdx = GenRandomInt(0, biomeTypeLen - 1);
			startIdx = biomeTypeIdx[startIdx];
			size_t biomeArrLen = sizeof(biomeArr) / sizeof(biomeArr[0]);
			for (size_t i = 0; i < biomeArrLen; i++)
			{
				WCoord blockPos_out;
				if (world_groud->FindEcosystem(blockPos_out, centerPos, biomeArr[startIdx] - 1000, 20))
				{
					m_travelingTraderInfo.biome = biomeArr[startIdx];
					m_travelingTraderInfo.biomePos = blockPos_out;
					break;
				}
				startIdx = ++startIdx % biomeArrLen;
			}
		}
		else //1 回家
		{
			m_travelingTraderInfo.inHome = true;
			m_travelingTraderInfo.lastTimePoint = curDayNum;
			m_travelingTraderInfo.nextTimeLength = GenRandomInt(2, 3);
			m_travelingTraderInfo.hunger = 0;
			m_travelingTraderInfo.lastInHomePos = WCoord(0, -1, 0);
			//刷新商品
			int groupId;
			MINIW::ScriptVM::game()->callFunction("G_TerrainID2GroupId", "i>i", m_travelingTraderInfo.biome, &groupId);
			loadTravelingTraderGoodsInfo(groupId);
		}
	}
	else
		assert(false);
}

TravelingTraderInfo& WorldManager::GetTravelingTraderInfo()
{
	return m_travelingTraderInfo;
}

int WorldManager::getTravelingTraderGoodsNum()
{
	return m_travelingTraderInfo.traderGoodsDatas.goodsData.size();
}

const TraderGoodsInfo * WorldManager::getTravelingTraderGoodsInfo(int index)
{
	if (index < 0 || index >= m_travelingTraderInfo.traderGoodsDatas.goodsData.size()) {
		return NULL;
	}
	return &m_travelingTraderInfo.traderGoodsDatas.goodsData[index];
}

void WorldManager::loadTravelingTraderGoodsInfo(int groupID)
{
#define PAY_ITEM_ID 14001
	std::vector<const NpcTradeDef*> availables;
	int totalWeight = 0;

	if (!GetDefManagerProxy())
		return;

	for (int i = 0, _num = GetDefManagerProxy()->getNpcTradeNum(); i < _num; ++i)
	{
		const NpcTradeDef* def = GetDefManagerProxy()->getNpcTradeDef(i + 1);
		if (!def)
			continue;
		const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(def->ItemID);
		if (itemdef && itemdef->CondUnlcokType != 0)
		{
			if (itemdef->CondUnlcokType > 0 && isUnlockItem(itemdef->CondUnlcokType)) continue;
			if (itemdef->CondUnlcokType == -1 && GetIPlayerControl() && !GetIPlayerControl()->CastToPlayer()->isUnlockItem(itemdef->CondUnlcokType)) continue;
		}

		if (3022 == def->NpcID && groupID == def->GroupID)
		{
			//需要确定下这个组ID是什么意思
			//if ((certainid == 0 && !HasGroup2Grids(def->GroupID)) || (certainid > 0 && certainid == def->ItemID))
			{
				availables.push_back(def);
				totalWeight += def->Weight;
			}
		}
	}
	if (totalWeight == 0) return;

	int traderGoodsNum = availables.size();
	int goodsNum = GenRandomInt(5, 8);

	typedef struct {
		int ID;
		int Weight;
	}DefWeight;

	std::vector<DefWeight> defWeightVec;
	std::map<int, const NpcTradeDef*> defMap;
	for (size_t i = 0; i < traderGoodsNum; i++){
		defMap.insert(std::make_pair(availables[i]->ID, availables[i]));
		defWeightVec.push_back({ availables[i]->ID, availables[i]->Weight + GenRandomInt(1, totalWeight) });
	}
	std::sort(defWeightVec.begin(), defWeightVec.end(), [](const DefWeight a, const DefWeight b){return a.Weight > b.Weight;});
		
	goodsNum = (availables.size() < 5) ? availables.size() : goodsNum;
	goodsNum = (defWeightVec.size() < goodsNum) ? defWeightVec.size() : goodsNum;

	//先清空下一次
	m_travelingTraderInfo.traderGoodsDatas.goodsData.clear();
	for (size_t i = 0; i < goodsNum; i++)
	{
		const NpcTradeDef* defInfo = defMap[defWeightVec[i].ID];
		if (NULL == defInfo) {
			continue;
		}

		TraderGoodsInfo traderGoods;
		traderGoods.index     = i + 1;
		traderGoods.itemId    = defInfo->ItemID;
		traderGoods.price     = GenRandomInt(defInfo->PriceFloor, defInfo->PriceCeil);
		traderGoods.num       = GenRandomInt(defInfo->NumFloor, defInfo->NumCeil);
		traderGoods.payItemId = defInfo->PayItemID;
		if (0 == traderGoods.payItemId) {
			traderGoods.payItemId = PAY_ITEM_ID;
		}

		//如果是兑换物品, 需要换过来，因为表格ItemID表示需要兑换成经验的物品，表格设计和使用问题导致
		if (0 == defInfo->TradeType) {
			traderGoods.itemId = traderGoods.payItemId;
			traderGoods.payItemId = defInfo->ItemID;
			traderGoods.price = GenRandomInt(defInfo->NumFloor, defInfo->NumCeil);
			traderGoods.num = GenRandomInt(defInfo->PriceFloor, defInfo->PriceCeil);
		}

		//如果以物品换物品, 表格设计和使用问题导致
		if (2 == defInfo->TradeType) {
			traderGoods.price = GenRandomInt(defInfo->PayItemNumFloor, defInfo->PayItemNumCeil);
			traderGoods.num = GenRandomInt(defInfo->NumFloor, defInfo->NumCeil);
		}

		traderGoods.leftcount = defInfo->LockNum;

		if (isRuneStoneAuthed(defInfo->ItemID)) {//已鉴定的符文石 code by:tanzhenyu

			MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateAuthedRuneStoneUserdataStr",
				MNSandbox::SandboxContext(nullptr).SetData_Number("authedRuneStoneItemID", defInfo->ItemID));
			std::string userdatastr = "";
			if (result.IsExecSuccessed())
			{
				userdatastr = result.GetData_String();
			}
			//m_Grids[index].obtain.setUserdataStr(userdatastr.c_str());
		}
		m_travelingTraderInfo.traderGoodsDatas.goodsData.push_back(traderGoods);
	}
}

bool WorldManager::setTraderGoodsLeftCount(int index, int valua)
{
	if (index < 0 || index >= m_travelingTraderInfo.traderGoodsDatas.goodsData.size()) {
		return false;
	}
	m_travelingTraderInfo.traderGoodsDatas.goodsData[index].leftcount = valua;

	if (needSave(NEEDSAVE_GLOBAL))
	{
		if (!saveToFile(m_worldId)) return false;
	}
	return true;
}

double WorldManager::getTickDelta() 
{ 
	return GetTimeSinceStartup() - m_lastTickTime; 
}

/** 
 * @brief 获取当前运行的逻辑帧率(每秒tick数)
 * @return 帧率(每秒tick数)
 */
unsigned WorldManager::getTickInSecond()
{
	unsigned last = m_TickTime.getLast();
	for (unsigned i=1; i < m_TickTime.size(); ++i)
	{
		if (last - m_TickTime.getReverse(i) >= 1000)
			return i;
	}
	return 1;
}

int WorldManager::GetStatics(int &actors, int &players, EffectManager::EffectStat &effStat)
{
    std::map<int, World*>::iterator world_iter = m_Worlds.begin();
    for (; world_iter != m_Worlds.end(); world_iter++) {
        World* pworld = world_iter->second;
        if (pworld == NULL) continue;
        actors += pworld->getActorMgr()->getActorCount();
        players += pworld->getActorMgr()->getNumPlayer();
        pworld->getEffectMgr()->GetEffectStat(effStat);
    }
	return 0;
}
