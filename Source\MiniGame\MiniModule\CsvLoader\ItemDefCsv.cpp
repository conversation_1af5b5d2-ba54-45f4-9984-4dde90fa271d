#include "ItemDefCsv.h" 
#include "OgreUtils.h"
#include "StringDefCsv.h"
#include "defmanager.h"
#include "WorldManager.h"
#include "ModManager.h"
#include "Common/OgreShared.h"
#include "MonsterCsv.h"
#include "BlockDefCsv.h"
#include "PhysicsActorCsv.h"
#include "ProjectileDefCsv.h"
#include "ToolDefCsv.h"
#include "ClientInfoProxy.h"
#include "OgreStringUtil.h"
#include "Modules/SandboxGame/Mods/ModDefine.h"

using namespace MINIW;
using MINIW::CSVParser; 
IMPLEMENT_LAZY_SINGLETON(ItemDefCsv)

extern bool gFunc_IsDomesticVer();

ItemDefCsv::ItemDefCsv() 
{ 
	m_CustomItem.clear();
	m_CustomItemEgg.clear();
	m_ValueCustomItemTable.clear();

	m_ItemTable.reserve(20000);
	m_ValueItemTable.clear();
	m_ValueItemTable.reserve(15000);
	Rainbow::DeletePointerArrayLabel(m_ItemTable, kMemConfigDef);
	m_ItemGroupTable.clear();
}

ItemDefCsv::~ItemDefCsv() 
{ 
	Rainbow::DeletePointerArrayLabel(m_ItemTable, kMemConfigDef);
	m_ItemGroupTable.clear();
	onClear();
} 

void ItemDefCsv::onParse(CSVParser& parser) 
{ 
	parser.SetTitleLine(1);
	bool isDomestic = gFunc_IsDomesticVer();

	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		auto& line = parser[i];
		int id = line["ID"].UInt();
		if(id == 0) continue;

		int regionType = line["RegionType"].Int();
		if ((isDomestic && regionType == REGION_TYPE_UNIVERSE) || (!isDomestic && regionType == REGION_TYPE_DOMESTIC)) 
			continue;

		ItemDef *def = ENG_NEW_LABEL(ItemDef,kMemConfigDef);
		def->ID = id;
		def->Type = line["Type"].Int();
		def->ResType = line["EditType"].Int();
		def->OriginEditType = line["EditType"].Int();
		def->EditType = line["EditTypeEX"].Int();
		def->IsTemplate = line["IsTemplate"].Int() > 0;
		def->DropType = line["DropType"].Int();
		def->Model = line["Model"].Str();
		std::string tmpModel = line["Model"].Str();
		int subIndex = tmpModel.find('#');
		if (subIndex == 0)
			def->Model = tmpModel.substr(1, tmpModel.size()).c_str();

		def->Texture = line["Texture"].Str();
		def->TextureID = line["TextureID"].Int();
		//def->Texture[0] = 0;

		def->FilterType = line["FilterType"].Int();
		def->Quality = line["Quality"].Int();
		def->Chip = line["Chip"].Int();
		def->MeshType = line["IsMeshModel"].Int();
		def->InvolvedID = line["InvolvedID"].Int();
		def->UnlockType = line["UnlockType"].Int();
		def->UnlockFlag = line["UnlockFlag"].Int();
		def->ReplaceID = line["ReplaceID"].Int();
		def->ChipPrice = line["ChipPrice"].Int();
		def->ChipNum = line["ChipNum"].Int();
		def->CondUnlcokType = line["CondUnlcokType"].Int();
		def->CreateType = line["CreateType"].Int();
		def->SortId	= line["SortId"].Int();
		def->IconEffect = line["IconEffect"].Int();
		def->CoolDown = line["Cooldown"].Int();
		def->TriggerType = line["TriggerType"].Int();

		def->Name = ColumnLang(line, "Name");
		def->EnglishName = line["Key"].Str(); // Item ENName -> Key
		//MyStringCpy(def->GainWay, sizeof(def->GainWay), ColumnLang(line, "GainWay", m_CurLanguage));
		def->Desc = ColumnLang(line, "Desc");
		def->Icon = line["Icon"].Str();
		def->UseScript = line["UseScript"].Str();
		def->UseScript2 = line["UseScript2"].Str();
		def->EmitScript = line["EmitScript"].Str();
		def->GetWay = ColumnLang(line, "GetWay");
		def->UnlockWay = StringDefCsv::getInstance()->get(line["UnlockWay"].Int());

		def->UseTarget = line["UseTarget"].Int();
		def->WieldScale = line["WieldScale"].Float();
		def->ThirdPersonScale = line["ThirdPersonScale"].Float();
		def->DropScale = line["DropScale"].Float();
		def->WieldPeriod = line["WieldPeriod"].Int();
		def->StackMax = line["StackMax"].Int();
		def->Usable = line["Usable"].Int();
		def->Range = line["Range"].Int();
		def->ItemGroup = line["ItemGroup"].Int();
		def->EnchantTag = line["EnchantTag"].Int();
		def->StuffType = line["StuffType"].Int();
		def->EnchantAfterID = line["EnchantAfterID"].Int();
		def->CanExtract = line["CanExtract"].Int();
		def->Score = line["Score"].Float();
		def->CostFoodLevel = line["CostFoodLevel"].Int();
		MyStringCpy(def->HandEffect, sizeof(def->HandEffect), line["HandEffect"].Str());
		def->HandEffectScale = line["HandEffectScale"].Float();
		def->TypeDesc = StringDefCsv::getInstance()->get(line["TypeDesc"].Int());
		def->Version = line["Version"].Str();
		def->ScriptMasking = line["ScriptMasking"].Int();
		def->ShowType = line["ShowType"].Int();
		def->ShowId = line["ShowId"].Int();
		def->ShowQuality = line["ShowQuality"].Int();
		//2023/04/13 codeby:fym item表新增道具品质
		def->QualityLevel = line["QualityLevel"].Int();
		def->EXSortId = line["EXSortId"].Int();
		def->PriorityBag = line["PriorityBag"].Int();

		const BlockDef *blockdef = NULL;
		if (def->ID >= 0 && def->ID < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			blockdef = BlockDefCsv::getInstance()->get(def->ID);
			//方块的名称需要从item表中获取 刷新下block的名称
			BlockDefCsv::getInstance()->updateName(def->ID, def->Name.c_str());
		}

		def->Crc = DefManager::CalItemCrc(def, blockdef);

		def->para = line["para"].String().c_str();

		def->MaintenanceProps = line["MaintenanceProps"].String().c_str();
		jsonxx::Value jsonSkills;
		if (jsonSkills.parse((char*)line["Skills"].Str()))
		{
			jsonxx::Array skillsArray = jsonSkills.get<jsonxx::Array>();
			for(int j = 0; j<(int)skillsArray.size(); j++)
			{
				def->SkillID.push_back((int)skillsArray.get<jsonxx::Number>(j));
			}
		}

		if (def->ID >= int(m_ItemTable.size())) m_ItemTable.resize(def->ID + 1, NULL);
		ENG_DELETE_LABEL(m_ItemTable[def->ID], kMemConfigDef);
		m_ItemTable[def->ID] = def;
		m_ValueItemTable.push_back(def->ID);

		jsonxx::Value jsonClassificationType;
		if (jsonClassificationType.parse((char*)line["ClassificationType"].Str()))
		{
			jsonxx::Array ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();
			for (int j = 0; j<(int)ClassificationTypeArray.size(); j++)
			{
				def->ClassificationType.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
			}
		}

		// 为【组】建立索引 ItemGroup
		if (def->ItemGroup > 0)
		{
			auto iterGroup = m_ItemGroupTable.find(def->ItemGroup);
			if (iterGroup == m_ItemGroupTable.end())
			{
				RAINBOW_VECTOR(kMemConfigDef, int) itemlist;
				itemlist.push_back(def->ID);
				m_ItemGroupTable.insert(make_pair(def->ItemGroup, itemlist));
			}
			else
			{
				iterGroup->second.push_back(def->ID);
			}
		}
		//解析Addskills
		{
			std::vector<std::string> vDefault;
			vDefault.clear();
			std::string sDefault = line["AddSkills"].Str();
			Rainbow::StringUtil::split(vDefault, sDefault, "|");
			int size = vDefault.size();
			def->Addskills.reserve(size);
			def->Addskills.resize(size);
			for (int i = 0; i < size; i++)
			{
				def->Addskills[i] = vDefault[i].c_str();
			}
		}
	}
	//m_ValueItemTable.reserve(m_ValueItemTable.size());
	g_DefMgr.resetCrcCode(CRCCODE_ITEMS);
} 
void ItemDefCsv::onClear() 
{ 
	m_ValueItemTable.clear();
	m_ValueCustomItemTable.clear();
} 
const char* ItemDefCsv::getName() 
{ 
    return "itemdef"; 
} 
const char* ItemDefCsv::getClassName() 
{ 
    return "ItemDefCsv"; 
} 

bool ItemDefCsv::checkItemCrc(int itemid)
{	
    //根据讨论结果不用crc方式做外挂检查
    //(void)itemid;
    return true;
}

void ItemDefCsv::removeCustom(int id, int type, bool needresetcrc/* =false */, int involvedid/* =0 */)
{
	if (type == BLOCK_MODEL || type == FULLY_BLOCK_MODEL || type == IMPORT_BLOCK_MODEL)
	{
		//if (id >= 0 && id<int(BlockDefCsv::getInstance()->getNum()))
		if (id >= 0 && id < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			BlockDefCsv::getInstance()->removeDef(id);
		}
	}
	
	if (type == WEAPON_MODEL || type == PROJECTILE_MODEL || type == BOW_MODEL || type == FULLY_PACKING_CUSTOM_MODEL || type == IMPORT_ITEM_MODEL)
	{
		ToolDefCsv::getInstance()->m_ToolTable.RemoveRecord(id);
	}

	if (type == GUN_MODEL)
	{
		g_DefMgr.m_GunTable.RemoveRecord(id);
	}

	if (type == PROJECTILE_MODEL)
	{
		auto projectilesIter = ProjectileDefCsv::getInstance()->m_Projectiles.begin();
		for (; projectilesIter != ProjectileDefCsv::getInstance()->m_Projectiles.end(); projectilesIter++)
		{
			if ((*projectilesIter)->ID == id)
			{
				 ProjectileDefCsv::getInstance()->m_Projectiles.erase(projectilesIter);
				break;
			}
		}
		ProjectileDefCsv::getInstance()->m_ProjectileTable.RemoveRecord(id);

		std::vector<PhysicsActorDef>::iterator iter = PhysicsActorCsv::getInstance()->m_PhysicsActor.begin();
		for (; iter !=  PhysicsActorCsv::getInstance()->m_PhysicsActor.end(); iter++)
		{
			if (iter->ActorID == id)
			{
				PhysicsActorCsv::getInstance()->m_PhysicsActor.erase(iter);
				break;
			}
		}
	}

	if (type == ACTOR_MODEL || type == FULLY_ACTOR_MODEL || type == IMPORT_ACTOR_MODEL)
	{
		auto monsterIter = MonsterCsv::getInstance()->m_MonsterTable.begin();
		for (; monsterIter != MonsterCsv::getInstance()->m_MonsterTable.end(); monsterIter++)
		{
			if ((*monsterIter)->ID == id)
			{
				MonsterCsv::getInstance()->m_MonsterTable.erase(monsterIter);
				break;
			}
		}
		MonsterCsv::getInstance()->m_Monsters.RemoveRecord(id);

		if (involvedid >= 0 && involvedid<int(m_ItemTable.size()))
		{
			ENG_DELETE_LABEL(m_ItemTable[involvedid], kMemConfigDef);
			m_ItemTable[involvedid] = NULL;
			removeValueId(involvedid);
		}
		else if (involvedid > ItemIdStart && involvedid <= ItemIdEnd)
		{
			int iCustomId = involvedid - ItemEggIdStart;
			if (iCustomId > 0 && iCustomId < m_CustomItemEgg.size())
			{
				ENG_DELETE_LABEL(m_CustomItemEgg[iCustomId], kMemConfigDef);
				m_CustomItemEgg[iCustomId] = nullptr;
				removeValueId(involvedid);
			}
			else
			{
				iCustomId = involvedid - ItemIdStart;
				if (iCustomId < m_CustomItem.size())
				{
					ENG_DELETE_LABEL(m_CustomItem[iCustomId], kMemConfigDef);
					m_CustomItem[iCustomId] = nullptr;
					removeValueId(involvedid);
				}
			}
		}
	}
	
	if (id > ItemIdStart && id <= ItemIdEnd)
	{
		int iCustomId = id - ItemEggIdStart;
		if (iCustomId > 0 && iCustomId < m_CustomItemEgg.size())
		{
			ENG_DELETE_LABEL(m_CustomItemEgg[iCustomId], kMemConfigDef);
			m_CustomItemEgg[iCustomId] = nullptr;
			removeValueId(id);
		}
		else
		{
			iCustomId = id - ItemIdStart;
			if (iCustomId < m_CustomItem.size())
			{
				ENG_DELETE_LABEL(m_CustomItem[iCustomId], kMemConfigDef);
				m_CustomItem[iCustomId] = nullptr;
				removeValueId(id);
			}
		}
	}
	else if (id >= 0 && id<int(m_ItemTable.size()))
	{
		// 物理机械的id 再加上一个类型判断 防止删错了
		if (VEHICLE_MODEL == type && m_ItemTable[id] && m_ItemTable[id]->Type != VEHICLE_MODEL)
		{
			LOG_INFO("vehicle id %d type is wrong!",id);
			return;
		}
		ENG_DELETE_LABEL(m_ItemTable[id], kMemConfigDef);
		m_ItemTable[id] = NULL;
		removeValueId(id);
	}

	if(needresetcrc)
		g_DefMgr.resetCrcCode(CRCCODE_ITEMS);
}

int ItemDefCsv::getValueIdNum()
{
	return m_ValueItemTable.size() + m_ValueCustomItemTable.size();
}

int ItemDefCsv::getIdByIndex(int index)
{
	if (index < 0 || index >= getValueIdNum())
	{
		return -1;
	}

	if (index < m_ValueItemTable.size())
	{
		return m_ValueItemTable[index];
	}

	index = index - m_ValueItemTable.size();
	return m_ValueCustomItemTable[index];
}

ItemDef* ItemDefCsv::getDefByValueIndex(int id, bool takeplace)
{
	int trueId = getIdByIndex(id);
	return get(trueId, takeplace);
}

ItemDef* ItemDefCsv::getDefByValueIndexRange(int id, int min, int max, bool takeplace)
{
	int trueId = getIdByIndex(id);
	if (min > 0 && trueId < min)
	{
		return NULL;
	}
	if (max > 0 && trueId > max)
	{
		return NULL;
	}
	return get(trueId, takeplace);
}


ItemDef* ItemDefCsv::get(int id, bool takeplace/* =false */)
{
	load();
#if 0
	//LOG_INFO("get(): id = %d", id);
	assert(id >= 0);

	ItemDef *itemDef = g_ModMgr.tryGetItemDef(id);
	//LOG_INFO("get(): itemDef = %d", itemDef);
	if (itemDef != nullptr)
	{
		return itemDef;
	}
    
    if(m_ItemTable.size() <=0) return  NULL;
	if(takeplace && id >= getNum())
	{
		return m_ItemTable[ITEM_DEFAULT];
	}


	if ( id >= getNum() || id < 0 )
	{
//#ifdef IWORLD_DEV_BUILD
//#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
//		TCHAR msg_[64] = { 0 };
//		_stprintf(msg_, __TEXT("发现不存在的物品id=%d"), id);
//		MessageBox(NULL, msg_, __TEXT("Error"), MB_OK);
//#endif
//#endif
		return m_ItemTable[ITEM_DEFAULT];
	}

	assert(id < getNum());
	return m_ItemTable[id];
#else
	assert(id >= 0);

	ItemDef* itemDef = g_ModMgr.tryGetItemDef(id);
	if (itemDef)
		return itemDef;

	int iSize = getNum();
	if (iSize <= 0)
		return takeplace ? m_ItemTable[ITEM_DEFAULT] : nullptr;

	int iCustomId = id - ItemEggIdStart;//如果id传的索引，此处一定小于ItemIdStart或者ItemEggIdStart
	if (iCustomId >= 0 && iCustomId < m_CustomItemEgg.size())
	{
		return m_CustomItemEgg[iCustomId];//自定义ID段
	}
	else
	{
		iCustomId = id - ItemIdStart;
		if (iCustomId >= 0 && iCustomId < m_CustomItem.size())
		{
			return m_CustomItem[iCustomId];//自定义ID段
		}
	}

	if (id >= iSize || id < 0)
	{
		return takeplace ? m_ItemTable[ITEM_DEFAULT] : nullptr;
	}

	//需要处理takeplace
	int iOriginNum = m_ItemTable.size();
	if (id < iOriginNum)
	{
		itemDef = m_ItemTable[id];
		if (itemDef == nullptr && takeplace)
			itemDef = m_ItemTable[ITEM_DEFAULT];

		return itemDef;
	}
		
	
	id -= iOriginNum;
	return m_CustomItem[id];
#endif
}

bool ItemDefCsv::getItemsByGroup(int id, std::vector<int>& out)
{
	load();

	auto iter = m_ItemGroupTable.find(id);
	if (iter != m_ItemGroupTable.end())
	{
		out.insert(out.end(), iter->second.begin(), iter->second.end());
		return true;
	}
	return false;
}

ItemDef *ItemDefCsv::getOrigin(int id)
{	
	load();

	assert(id >= 0);
	if(id >= 0 && id < m_ItemTable.size())
		return m_ItemTable[id];

	//id = id - ItemIdStart;
	//if (id >= 0 && id < m_CustomItem.size())
	//	return m_CustomItem[id];

	return nullptr;
}

int ItemDefCsv::getNum()
{	
	load();

	return m_ItemTable.size() + m_CustomItem.size() + m_CustomItemEgg.size();
}

ItemDef* ItemDefCsv::addByCopy(int id, int type, int copyId)
{
	auto* templateItemDef = get(copyId);
	auto* pDef = add(id, type, templateItemDef);
	if (!pDef)
		return NULL;
	pDef->CopyID = copyId;
	pDef->ScriptMasking = 0;
	return pDef;
}
ItemDef* ItemDefCsv::add(int id, int type, ItemDef* templateItemDef, std::string model /* = "" */, std::string name /* = "" */, std::string desc /* = "" */, short involvedid /* = 0 */)
{
	if (templateItemDef == nullptr) return nullptr;
	ItemDef* tmpItemDef = ENG_NEW_LABEL(ItemDef, kMemConfigDef)();
	tmpItemDef->ID = id;
	tmpItemDef->Type = templateItemDef->Type;
	tmpItemDef->EditType = templateItemDef->EditType;
	tmpItemDef->IsTemplate = templateItemDef->IsTemplate;
	tmpItemDef->DropType = templateItemDef->DropType;
	if (MODEL_TYPE_MAX != type)
	{
		if (type > BLOCK_MODEL && type <= VEHICLE_MODEL)
			tmpItemDef->Model = model.c_str();
		else if (type == ACTOR_MODEL || (type >= FULLY_BLOCK_MODEL && type <= FULLY_PACKING_CUSTOM_MODEL) || (type >= IMPORT_BLOCK_MODEL && type <= IMPORT_MODEL_MAX))
			tmpItemDef->Model = model.c_str();
		else
			tmpItemDef->Model = templateItemDef->Model;
	}
	else
	{
		tmpItemDef->Model = model.c_str();
	}
	tmpItemDef->Texture = templateItemDef->Texture;
	tmpItemDef->TextureID = templateItemDef->TextureID;
	//def->Texture[0] = 0;

	tmpItemDef->FilterType = templateItemDef->FilterType;
	tmpItemDef->Quality = templateItemDef->Quality;
	tmpItemDef->Chip = templateItemDef->Chip;
	if (type > BLOCK_MODEL && type <= BOW_MODEL || type == ACTOR_MODEL)
		tmpItemDef->MeshType = CUSTOM_GEN_MESH;  //使用微雕模型
	else if (type >= FULLY_BLOCK_MODEL && type <= FULLY_PACKING_CUSTOM_MODEL)
		tmpItemDef->MeshType = FULLY_CUSTOM_GEN_MESH;
	else if (type >= IMPORT_BLOCK_MODEL && type < IMPORT_MODEL_MAX)
		tmpItemDef->MeshType = IMPORT_MODEL_GEN_MESH;
	else if (type == VEHICLE_MODEL)
	{
		tmpItemDef->MeshType = VEHICLE_GEN_MESH;  //使用微雕模型
	}
	else
		tmpItemDef->MeshType = templateItemDef->MeshType;
	if (involvedid > 0)
		tmpItemDef->InvolvedID = involvedid;
	else
		tmpItemDef->InvolvedID = templateItemDef->InvolvedID;
	tmpItemDef->UnlockType = templateItemDef->UnlockType;
	tmpItemDef->UnlockFlag = templateItemDef->UnlockFlag;
	tmpItemDef->ReplaceID = templateItemDef->ReplaceID;
	tmpItemDef->ChipPrice = templateItemDef->ChipPrice;
	tmpItemDef->ChipNum = templateItemDef->ChipNum;
	tmpItemDef->CondUnlcokType = templateItemDef->CondUnlcokType;
	tmpItemDef->CreateType = templateItemDef->CreateType;
	tmpItemDef->SortId = templateItemDef->SortId;
	tmpItemDef->IconEffect = templateItemDef->IconEffect;
	tmpItemDef->CoolDown = templateItemDef->CoolDown;
	tmpItemDef->ShowType = templateItemDef->ShowType;
	tmpItemDef->ShowId = templateItemDef->ShowId;
	tmpItemDef->EXSortId = templateItemDef->EXSortId;

	if (name != "")
		tmpItemDef->Name = name.c_str();
	else
		tmpItemDef->Name = templateItemDef->Name;
	tmpItemDef->EnglishName = templateItemDef->EnglishName;
	//MyStringCpy(tmpItemDef->GainWay, sizeof(tmpItemDef->GainWay), templateItemDef->GainWay);
	tmpItemDef->Desc = templateItemDef->Desc;
	if (type > BLOCK_MODEL && type <= BOW_MODEL)
		tmpItemDef->Icon = "customitem";
	else if (type == VEHICLE_MODEL)
	{
		tmpItemDef->Icon = "vehicleitem";
	}
	else if (type == ACTOR_MODEL)
		tmpItemDef->Icon = "customegg";
	else if (type == FULLY_ITEM_MODEL)
		tmpItemDef->Icon = "fullycustomitem";
	else if (type == FULLY_ACTOR_MODEL)
		tmpItemDef->Icon = "fullycustomegg";
	else if (type == FULLY_PACKING_CUSTOM_MODEL)
		tmpItemDef->Icon = "fullycustompacking";
	else if (type >= IMPORT_BLOCK_MODEL && type <= IMPORT_MODEL_MAX)
		tmpItemDef->Icon = "importcustommodel";
	else
		tmpItemDef->Icon = templateItemDef->Icon;

	tmpItemDef->UseScript = templateItemDef->UseScript;
	tmpItemDef->EmitScript = templateItemDef->EmitScript;
	tmpItemDef->Version = templateItemDef->Version;
	tmpItemDef->GetWay = templateItemDef->GetWay;
	tmpItemDef->UnlockWay = templateItemDef->UnlockWay;

	tmpItemDef->UseTarget = templateItemDef->UseTarget;
	tmpItemDef->WieldScale = templateItemDef->WieldScale;
	tmpItemDef->ThirdPersonScale = templateItemDef->ThirdPersonScale;
	tmpItemDef->DropScale = templateItemDef->DropScale;
	tmpItemDef->WieldPeriod = templateItemDef->WieldPeriod;
	tmpItemDef->StackMax = templateItemDef->StackMax;
	tmpItemDef->Usable = templateItemDef->Usable;
	tmpItemDef->Range = templateItemDef->Range;
	tmpItemDef->ItemGroup = templateItemDef->ItemGroup;
	tmpItemDef->EnchantTag = templateItemDef->EnchantTag;
	tmpItemDef->StuffType = templateItemDef->StuffType;
	tmpItemDef->EnchantAfterID = templateItemDef->EnchantAfterID;
	tmpItemDef->CanExtract = templateItemDef->CanExtract;
	tmpItemDef->Score = templateItemDef->Score;
	tmpItemDef->CostFoodLevel = templateItemDef->CostFoodLevel;
	tmpItemDef->TypeDesc = templateItemDef->TypeDesc;
	MyStringCpy(tmpItemDef->HandEffect, sizeof(tmpItemDef->HandEffect), templateItemDef->HandEffect);
	tmpItemDef->HandEffectScale = templateItemDef->HandEffectScale;

	const BlockDef* blockdef = NULL;
	//SOC 方块ID 范围 0-4095 不支持扩展id
	if (tmpItemDef->ID < SOC_BLOCKID_MAX) blockdef = BlockDefCsv::getInstance()->get(id);

	tmpItemDef->Crc = DefManager::CalItemCrc(tmpItemDef, blockdef);

	for (int j = 0; j < (int)templateItemDef->SkillID.size(); j++)
	{
		tmpItemDef->SkillID.push_back(templateItemDef->SkillID[j]);
	}

	for (int k = 0; k < (int)templateItemDef->ClassificationType.size(); k++)
	{
		tmpItemDef->ClassificationType.push_back(templateItemDef->ClassificationType[k]);
	}

	if (id > ItemIdStart && id <= ItemIdEnd)
	{
		int iCustomId = id - ItemEggIdStart;
		if (iCustomId > 0)
		{
			if (iCustomId >= m_CustomItemEgg.size())
				m_CustomItemEgg.resize(iCustomId + 1, nullptr);

			m_CustomItemEgg[iCustomId] = tmpItemDef;
		}
		else
		{
			iCustomId = id - ItemIdStart;
			if (iCustomId >= m_CustomItem.size())
				m_CustomItem.resize(iCustomId + 1, nullptr);

			m_CustomItem[iCustomId] = tmpItemDef;
		}

		m_ValueCustomItemTable.push_back(tmpItemDef->ID);
	}
	else
	{
		if (tmpItemDef->ID >= int(m_ItemTable.size())) m_ItemTable.resize(id + 1, NULL);
		if (!m_ItemTable[tmpItemDef->ID])
		{
			m_ValueItemTable.push_back(tmpItemDef->ID);
		}
		else
			ENG_DELETE_LABEL(m_ItemTable[tmpItemDef->ID], kMemConfigDef);

		m_ItemTable[tmpItemDef->ID] = tmpItemDef;
	}

	if (!name.empty())
		tmpItemDef->Name = name.c_str();

	if (!desc.empty())
		tmpItemDef->Desc = desc.c_str();

	g_DefMgr.resetCrcCode(CRCCODE_ITEMS);
	return tmpItemDef;
}

ItemDef* ItemDefCsv::add(int id, int type, std::string model /* = "" */, std::string name /* = "" */, std::string desc /* = "" */, short involvedid /* = 0 */)
{	
	load();

	int defaultId = 2000;
	if (MODEL_TYPE_MAX != type)
	{
		if (type > BLOCK_MODEL && type <= BOW_MODEL)
			defaultId = 10000 - type;
		else if (type == FULLY_ITEM_MODEL || type == IMPORT_ITEM_MODEL)
			defaultId = 9999;
		else if (type == FULLY_PACKING_CUSTOM_MODEL)
			defaultId = 9995;
		else if (type == ACTOR_MODEL || type == FULLY_ACTOR_MODEL || type == IMPORT_ACTOR_MODEL)
			defaultId = 13100;
		else if (type == VEHICLE_MODEL)
			defaultId = 10112;
		else if (type == FULLY_BLOCK_MODEL || type == IMPORT_BLOCK_MODEL)
			defaultId = 1999;
	}
	else
	{
		defaultId = 13100;
	}
	auto* templateItemDef = get(defaultId);
	return add(id, type, templateItemDef, model, name, desc, involvedid);
}

ItemDef* ItemDefCsv::getAutoUseForeignID(int id)
{	
	load();

	assert(id >= 0);
	id = g_ModMgr.getItemInnerId(id);
	ItemDef *itemDef = g_ModMgr.tryGetItemDef(id);
	if (itemDef != nullptr)
	{
		return itemDef;
	}

	if (m_ItemTable.size() <= 0) return  NULL;

	if (id >= getNum() || id < 0)
	{
		return m_ItemTable[ITEM_DEFAULT];
	}

	assert(id < getNum());
	return m_ItemTable[id];
}

void ItemDefCsv::removeValueId(int id)
{
	auto iter = std::find(m_ValueItemTable.rbegin(), m_ValueItemTable.rend(), id);
	if (iter != m_ValueItemTable.rend())
	{
		m_ValueItemTable.erase(std::next(iter).base());
	}
}

bool ItemDefCsv::addModItemDef(int id, ItemDef* def)
{
	if (id < 0 || id >= m_ItemTable.size())
	{
		return false;
	}
	//if (!m_ItemTable[id])
	//{
		//没有值才添加
		auto iter = std::find(m_ValueItemTable.rbegin(), m_ValueItemTable.rend(), id);
		if (m_ValueItemTable.rend() == iter)
		{
			m_ValueItemTable.push_back(id);
		}
	//}
	//else
	//{
		//ENG_DELETE(m_ItemTable[id]);
	//}
	//ItemDef* newDef = ENG_NEW_LABEL(ItemDef, kMemConfigDef)(*def);
	//m_ItemTable[id] = newDef;
	return true;
}

bool ItemDefCsv::removeModItemDef(int id)
{
	if (id < 0 || id >= m_ItemTable.size())
	{
		return false;
	}
	//if (!m_ItemTable[id])
	//{
	//	return false;
	//}
	//ENG_DELETE(m_ItemTable[id]);
	//m_ItemTable[id] = nullptr;
	removeValueId(id);
	return true;
}

void ItemDefCsv::clearAllCustomDef()
{
	for (size_t i = 0; i < m_CustomItem.size(); i++)
	{
		ENG_DELETE_LABEL(m_CustomItem[i], kMemConfigDef);
	}
	m_CustomItem.clear();

	for (size_t i = 0; i < m_CustomItemEgg.size(); i++)
	{
		ENG_DELETE_LABEL(m_CustomItemEgg[i], kMemConfigDef);
	}
	m_CustomItemEgg.clear();
	
	m_ValueCustomItemTable.clear();
}