#include "DisassembleCsv.h"
#include "OgreUtils.h"
#include "OgreStringUtil.h"
#include "defmanager.h"
#include "ModManager.h"
#include "OgreStringUtil.h"
#include "ModManager.h"
using MINIW::CSVParser;
using namespace MINIW;
IMPLEMENT_LAZY_SINGLETON(DisassembleCsv)

DisassembleCsv::DisassembleCsv()
{
}

DisassembleCsv::~DisassembleCsv()
{
	onClear();
}

void DisassembleCsv::onParse(CSVParser& parser)
{
    m_Table.clear();
    parser.SetTitleLine(1);
    int numLines = (int)parser.GetNumLines();
    for (int i = 2; i < numLines; ++i)
    {
        int id = parser[i]["ID"].Int();
        if(id == 0) continue;

        //int regionType = parser[i]["RegionType"].Int();
        //if ((isDomestic && regionType == REGION_TYPE_UNIVERSE) || (!isDomestic && regionType == REGION_TYPE_DOMESTIC)) 
        //	continue;

        CraftingDef def;
        def.CopyID = 0;
        def.gamemod = nullptr;

        def.ID = id;
        jsonxx::Value jsonClassificationType;
        auto typeChar = (char*)parser[i]["Type"].Str();
        if (strlen(typeChar) > 0 && typeChar[0] != '[')
        {
            def.Type.push_back(atoi(typeChar));//旧版本数据，不包含"[]",单个数字
        }
        else if (jsonClassificationType.parse((char*)parser[i]["Type"].Str()))
        {
            const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
            for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
            {
                def.Type.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
            }
        }
        auto editTypeChar = (char *)parser[i]["EditType"].Str();
        if (strlen(editTypeChar) > 0 && editTypeChar[0] != '[')
        {
            def.EditType.push_back(atoi(editTypeChar));//旧版本数据，不包含"[]",单个数字
        }
        else if (jsonClassificationType.parse((char*)parser[i]["EditType"].Str()))
        {
            const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
            for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
            {
                def.EditType.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
            }
        }
        def.ResultID = parser[i]["ResultID"].Int();
        //LOG_WARNING("dCrafting ID ... %d", def.ID);

        def.TechId = parser[i]["TechId"].Int();
        def.ResultCount = parser[i]["ResultCount"].Int();
        def.UseExp = parser[i]["UseExp"].Int();
        def.MoneyID = parser[i]["MoneyID"].Int();
        def.MoneyCount = parser[i]["MoneyCount"].Int();
        def.GridX = parser[i]["GridX"].Int();
        def.GridY = parser[i]["GridY"].Int();
        def.IsGroup = parser[i]["IsGroup"].Int() > 0;
        //def.IsFollowMe = parser[i]["IsFollowMe"].Int() == 11000;这个值的对应功能用CraftingItemID代替了  code-by:DemonYan
        def.IsTemplate = parser[i]["IsTemplate"].Int() > 0;
        def.EnglishName = parser[i]["Key"].Str(); // Crafting ENName -> Key
        def.UnlockLevel = parser[i]["UnlockLevel"].Int();
        def.MaterialNum = 0;
        for(int y=0; y<def.GridY; y++)
        {
            for(int x=0; x<def.GridX; x++)
            {
                int index = y*def.GridX+x;
                char tmpname[64];
                sprintf(tmpname, "MaterialID%d", index+1);
                def.MaterialID[index] = parser[i][tmpname].Int();
                sprintf(tmpname, "MaterialCount%d", index+1);
                def.MaterialCount[index] = parser[i][tmpname].Int();
                sprintf(tmpname, "ContainerID%d", index+1);
                def.ContainerID[index] = parser[i][tmpname].Int();
                if (def.MaterialID[index] > 0 && def.MaterialCount[index] > 0)
                {
                    def.MaterialNum += 1;
                }
            }
        }
        def.Score = parser[i]["Score"].Float();
        def.HelperCheck = parser[i]["HelperCheck"].Int();
        def.CookingTick = parser[i]["Cooktime"].Int();
        def.CraftingItemID = parser[i]["IsFollowMe"].Int();
        def.DisplayOrder = parser[i]["DispOrder"].Int();
        auto SubTypeChar = (char*)parser[i]["SubType"].Str();
        if (strlen(SubTypeChar) > 0 && SubTypeChar[0] != '[')
        {
            def.SubType.push_back(atoi(SubTypeChar));//旧版本数据，不包含"[]",单个数字
        }
        else if (jsonClassificationType.parse((char*)parser[i]["SubType"].Str()))
        {
            const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
            for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
            {
                def.SubType.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
            }
        }

        auto SubTypeOrderChar = (char*)parser[i]["SubTypeOrder"].Str();
        if (strlen(SubTypeOrderChar) > 0 && SubTypeOrderChar[0] != '[')
        {
            def.SubTypeOrder.push_back(atoi(SubTypeOrderChar));//旧版本数据，不包含"[]",单个数字
        }
        else if (jsonClassificationType.parse((char*)parser[i]["SubTypeOrder"].Str()))
        {
            const jsonxx::Array& ClassificationTypeArray = jsonClassificationType.get<jsonxx::Array>();//新版本数据，包含"[]",可同时存在多个数字[1,3,5]
            for (int j = 0; j < (int)ClassificationTypeArray.size(); j++)
            {
                def.SubTypeOrder.push_back((int)ClassificationTypeArray.get<jsonxx::Number>(j));
            }
        }

        m_Table.AddRecord(def.ID, def);
    }
}

void DisassembleCsv::onClear()
{
    m_Table.clear();
}

const char* DisassembleCsv::getName()
{
	return "disassemble";
}

const char* DisassembleCsv::getClassName()
{
	return "DisassembleCsv";
}

int DisassembleCsv::getNum()
{
	load();
	return m_Table.GetRecordSize();
}

const CraftingDef* DisassembleCsv::get(int id)
{
	load();
	return m_Table.GetRecord(id);
}

const CraftingDef* DisassembleCsv::getByItemId(int itemId)
{
    load();

    auto iter = m_Table.m_Records.begin();
    for (; iter != m_Table.m_Records.end(); iter++)
    {
        if (itemId == iter->second.ResultID) return &iter->second;
    }

    return nullptr;
}