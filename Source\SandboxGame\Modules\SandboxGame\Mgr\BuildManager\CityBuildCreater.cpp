#include "CityBuildCreater.h"
#include "voxelmodel.h"
#include "BuildType.h"
#include "BuildUGCBluePrint.h"
#include "EcosysBuildHelp.h"
#include "chunk.h"
#include "world.h"
#include "UgcAssetMgr.h"
#include "UgcAssetPrefab.h"
#include "ClientMob.h"
#include "ActorLocoMotion.h"
#include "UgcAssetMgr.h"
#include "ModPackMgr.h"
#include "container_monstersummoner.h"
#include "WorldManager.h"
#include "ClientActorHelper.h"
#include "EcosysUnit_City.h"
#include "gameplay/ChestMgr.h"
#include "container_polaroid.h" 
#include "Vehicle/ActorVehicleAssemble.h"


void CityBuildProcess::forwardOneStep(World* pworld, Chunk* chunk, BuildCreaterBase* creater)
{
	//CityBuildProcess 和 CityBuildCreater是强绑定的
	CityBuildCreater* cityCreater = static_cast<CityBuildCreater*>(creater);
	if (!cityCreater || !pworld || !chunk)
	{
		return;
	}
	auto paramer = cityCreater->getBuildData().paramter;
	int y = m_buildStage;
	if (y < 0)
	{
		WCoord pos = cityCreater->getOffsetPos(m_startPos) - EcosysBuildHelp::getChunkStartPos(m_chunkIndex);
		int oz = pos.z;
		//此时的y是负值
		pos.y = cityCreater->getOffsetPos(WCoord(0, 0, 0)).y + y;
		if (pos.y >= 0 && pos.y < CHUNK_BLOCK_Y)
		{
			for (int x = m_startPos.x; x <= m_endPos.x; x++, pos.x++)
			{
				pos.z = oz;
				for (int z = m_startPos.z; z <= m_endPos.z; z++, pos.z++)
				{
					if (!(pos.x >= 0 && pos.x < CHUNK_BLOCK_X)) continue;
					if (!(pos.z >= 0 && pos.z < CHUNK_BLOCK_Z)) continue;
					int blockid = chunk->getBlockID(pos.x, pos.y, pos.z);
					if (IsAirBlockID(blockid))
					{
						int fillBlockId = paramer.fillBlockId;
						if (paramer.useBuildFoundationBiomeBlock)
						{
							auto curBiome = chunk->getBiome(pos.x, pos.z);
							if (curBiome) fillBlockId = curBiome->FillBlock;
						}
						cityCreater->handleBlock(pworld, x, y, z, fillBlockId, 0);
					}
				}
			}
		}
		m_buildStage++;
	}
	else
	{
		WCoord chunkStartPos = chunk->m_Origin;
		for (int x = m_startPos.x; x <= m_endPos.x; x++)
		{
			for (int z = m_startPos.z; z <= m_endPos.z; z++)
			{
				Block c = cityCreater->getBlock(x, y, z);
				int blockid1 = c.getResID();
				//优化,是空气就别赋值了
				if (IsAirBlockID(blockid1) && chunk->getBlockID(cityCreater->getOffsetPos(WCoord(x, y, z) - chunk->m_Origin)) == blockid1)
				{
					continue;
				}
				if (GetDefManagerProxy()->getBuildReplaceDef(blockid1))
				{
					m_specialPos.push_back(specialPos(blockid1, WCoord(x, y, z)));
					//不要在这里设置,在其他地方设置
					//cityCreater->handleBlock(pworld, x, y, z, BLOCK_AIR, 0);
				}
				else
				{
					cityCreater->handleBlock(pworld, x, y, z, blockid1, c.getData());
				}
			}
		}
		m_buildStage++;
	}
	if (isEnd(*cityCreater))
	{
		m_active = false;
		endCallback(*cityCreater, chunk, pworld);
	}
}

//void CityBuildProcess::save(jsonxx::Object& object)
//{
//	BuildNormalProcess::save(object);
//	//保存特殊点信息
//	if (!m_specialPos.empty())
//	{
//		jsonxx::Array posArr;
//		for (const auto& p : m_specialPos)
//		{
//			jsonxx::Object posObj;
//			posObj << "x" << p.vmoPos.x;
//			posObj << "y" << p.vmoPos.y;
//			posObj << "z" << p.vmoPos.z;
//			posObj << "blockId" << p.blockId;
//			posArr << posObj;
//		}
//		object << "specialPos" << posArr;
//	}
//}

//void CityBuildProcess::load(const jsonxx::Object& object)
//{
//	BuildNormalProcess::load(object);
//	m_specialPos.clear();
//	if (object.has<jsonxx::Array>("specialPos"))
//	{
//		const auto& arrJson = object.get<jsonxx::Array>("specialPos");
//		m_specialPos.reserve(arrJson.size());
//		m_specialPos.resize(arrJson.size());
//		for (int posIndex = 0; posIndex < arrJson.size(); posIndex++)
//		{
//			if (arrJson.has<jsonxx::Object>(posIndex))
//			{
//				const auto& posJson = arrJson.get<jsonxx::Object>(posIndex);
//				auto& posData = m_specialPos[posIndex];
//				if (posJson.has<jsonxx::Number>("x"))
//				{
//					posData.vmoPos.x = posJson.get<jsonxx::Number>("x");
//				}
//				if (posJson.has<jsonxx::Number>("y"))
//				{
//					posData.vmoPos.y = posJson.get<jsonxx::Number>("y");
//				}
//				if (posJson.has<jsonxx::Number>("z"))
//				{
//					posData.vmoPos.z = posJson.get<jsonxx::Number>("z");
//				}
//				if (posJson.has<jsonxx::Number>("blockId"))
//				{
//					posData.blockId = posJson.get<jsonxx::Number>("blockId");
//				}
//			}
//		}
//	}
//}

flatbuffers::Offset<FBSave::BuildProcessSaveData> CityBuildProcess::save(flatbuffers::FlatBufferBuilder& builder)
{
	std::vector<flatbuffers::Offset<FBSave::CitySpecialPos>> specials;
	specials.reserve(m_specialPos.size());
	for (const auto& p : m_specialPos)
	{
		auto pos = WCoordToCoord3(p.vmoPos);
		specials.push_back(FBSave::CreateCitySpecialPos(builder, &pos, p.blockId));
	}
	auto data = FBSave::CreateCityBuildProcessFB(builder, saveNormal(builder), builder.CreateVector(specials));
	return FBSave::CreateBuildProcessSaveData(builder, FBSave::BuildProcessUnion_CityBuildProcessFB, data.Union(), m_type);
}

bool CityBuildProcess::load(const void* srcData)
{
	const FBSave::CityBuildProcessFB* base = static_cast<const FBSave::CityBuildProcessFB*>(srcData);
	if (!base) return false;
	if (!BuildNormalProcess::load(base->baseData())) return false;
	m_specialPos.clear();
	if (base->specialData())
	{
		int size = base->specialData()->size();
		m_specialPos.reserve(size);
		m_specialPos.resize(size);
		for (int i = 0; i < size; i++)
		{
			auto posFB = base->specialData()->Get(i);
			if (posFB)
			{
				m_specialPos[i].vmoPos = Coord3ToWCoord(posFB->vmoPos());
				m_specialPos[i].blockId = posFB->blockid();
			}
		}
	}
	return true;
}

CityBuildCreater::CityBuildCreater():m_configIndex(-1), m_cityIndex(-1), m_reportPos(0, -1, 0)
{

}

bool CityBuildCreater::resumeVmoDataInit()
{
	if (!m_pBigBuildVM)
	{
		return false;
	}
	if (m_data.fileName.empty())
	{
		LOG_WARNING("maybe err!fileName empty");
		return false;
	}
	std::string realPath;
	//if (CityConfig::getSingletonPtr() && CityConfig::getSingletonPtr()->m_debug)
	//{
	//	char filePath[100];
	//	sprintf(filePath, "vbp/%s.vbp", m_data.fileName.c_str());
	//	realPath = filePath;
	//}
	//else
	{
		realPath = UgcAssetMgr::GetInstance().GetAssetFilePath(m_data.fileName.c_str());
	}
	if (!m_pBigBuildVM->LoadAssert(realPath.c_str(), m_data.dir, true))
	{
		//assert(0);
		LOG_WARNING("maybe err!load %s failed!", m_data.fileName.c_str());
		return false;
	}
	return true;
}

void CityBuildCreater::setBuildIndex(int index, int cityIndex)
{
	m_configIndex = index;
	m_cityIndex = cityIndex;
}

const EcosysBuildHelp::BuildGenInfo* CityBuildCreater::getGenInfoById(int blockid)
{
	if (CityConfig::getSingletonPtr())
	{
		if (m_configIndex < CityBuildSingleBase)
		{
			const auto* pCity = CityConfig::getSingletonPtr()->getCityDataByIndex(m_cityIndex);
			if (pCity)
			{
				const auto* info = pCity->getBuildInfoByIndex(m_configIndex);
				if (info)
				{
					auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
						return blockid == info.m_blockId;
					});
					if (p != info->m_genData.end())
					{
						return &(*p);
					}
				}
			}
		}
		else if (m_configIndex < CityBuildPrecious)
		{
			int index = m_configIndex - CityBuildSingleBase;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getBuildInfoByIndex(index);
			if (info)
			{
				auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
				});
				if (p != info->m_genData.end())
				{
					return &(*p);
				}
			}
		}
		else if (m_configIndex < CityBuildBase)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().preciousData;
			{
				auto p = std::find_if(buildData.m_genData.begin(), buildData.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != buildData.m_genData.end())
				{
					return &(*p);
				}
			}
		}
		else if (m_configIndex < CityWaterBuild)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().baseData;
			{
				auto p = std::find_if(buildData.m_genData.begin(), buildData.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != buildData.m_genData.end())
				{
					return &(*p);
				}
			}
		}
		else if (m_configIndex < CityBuildOtherBase)
		{
			int index = m_configIndex - CityWaterBuild;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getWaterBuildInfoByIndex(index);
			if (info)
			{
				auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != info->m_genData.end())
				{
					return &(*p);
				}
			}
		}
		else
		{
			const auto& data = CityConfig::getSingletonPtr()->getOtherConfig().m_baseSingleData;
			{
				auto p = std::find_if(data.m_genData.begin(), data.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != data.m_genData.end())
				{
					return &(*p);
				}
			}
		}
	}
	return nullptr;
}

int CityBuildCreater::getBuildLevel(ChunkRandGen& randgen)
{
	Rainbow::Vector2i level(0, 0);
	if (CityConfig::getSingletonPtr())
	{
		if (m_configIndex < CityBuildSingleBase)
		{
			const auto* pCity = CityConfig::getSingletonPtr()->getCityDataByIndex(m_cityIndex);
			if (pCity)
			{
				const auto* info = pCity->getBuildInfoByIndex(m_configIndex);
				if (info)
				{
					level = info->m_level;
				}
			}
		}
		else if (m_configIndex < CityBuildPrecious)
		{
			int index = m_configIndex - CityBuildSingleBase;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getBuildInfoByIndex(index);
			if (info)
			{
				level = info->m_level;
			}
		}
		else if (m_configIndex < CityBuildBase)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().preciousData;
			{
				level = buildData.m_level;
			}
		}
		else if (m_configIndex < CityWaterBuild)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().baseData;
			{
				level = buildData.m_level;
			}
		}
		else if (m_configIndex < CityBuildOtherBase)
		{
			int index = m_configIndex - CityWaterBuild;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getWaterBuildInfoByIndex(index);
			if (info)
			{
				level = info->m_level;
			}
		}
		else
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().m_baseSingleData;
			{
				level = buildData.m_level;
			}
		}
	}
	if (level.y() >= level.x())
	{
		return randgen.get(level.x(), level.y());
	}
	return 0;
}

bool CityBuildCreater::isSpecialBiome(int blockid)
{
	if (CityConfig::getSingletonPtr())
	{
		if (m_configIndex < CityBuildSingleBase)
		{
			const auto* pCity = CityConfig::getSingletonPtr()->getCityDataByIndex(m_cityIndex);
			if (pCity)
			{
				const auto* info = pCity->getBuildInfoByIndex(m_configIndex);
				if (info)
				{
					auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
						return blockid == info.m_blockId;
					});
					if (p != info->m_genData.end())
					{
						return true;
					}
				}
			}
		}
		else if (m_configIndex < CityBuildPrecious)
		{
			int index = m_configIndex - CityBuildSingleBase;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getBuildInfoByIndex(index);
			if (info)
			{
				auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
				});
				if (p != info->m_genData.end())
				{
					return true;
				}
			}
		}
		else if (m_configIndex < CityBuildBase)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().preciousData;
			{
				auto p = std::find_if(buildData.m_genData.begin(), buildData.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != buildData.m_genData.end())
				{
					return true;
				}
			}
		}
		else if (m_configIndex < CityWaterBuild)
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().baseData;
			{
				auto p = std::find_if(buildData.m_genData.begin(), buildData.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != buildData.m_genData.end())
				{
					return true;
				}
			}
		}
		else if (m_configIndex < CityBuildOtherBase)
		{
			int index = m_configIndex - CityWaterBuild;
			const auto* info = CityConfig::getSingletonPtr()->getSingleBuildConfig().getWaterBuildInfoByIndex(index);
			if (info)
			{
				auto p = std::find_if(info->m_genData.begin(), info->m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != info->m_genData.end())
				{
					return true;
				}
			}
		}
		else
		{
			const auto& buildData = CityConfig::getSingletonPtr()->getOtherConfig().m_baseSingleData;
			{
				auto p = std::find_if(buildData.m_genData.begin(), buildData.m_genData.end(), [blockid](const EcosysBuildHelp::BuildGenInfo& info) {
					return blockid == info.m_blockId;
					});
				if (p != buildData.m_genData.end())
				{
					return true;
				}
			}
		}
	}
	return false;
}

//void CityBuildCreater::save(jsonxx::Object& object)
//{
//	BuildCreaterBase::save(object);
//	/*if (!m_genData.empty())
//	{
//		jsonxx::Array dataArray;
//		for (const auto& p : m_genData)
//		{
//			jsonxx::Object data;
//			data << "type" << p.m_type;
//			data << "genNum" << p.m_prob;
//			data << "prob" << p.m_prob;
//			data << "monsterid" << p.m_result.monsterId;
//			data << "containerId" << p.m_result.containerId;
//			data << "blockid" << p.m_blockId;
//			dataArray << data;
//		}
//		object << "genDatas" << dataArray;
//	}*/
//	object << "buildIndex" << m_configIndex;
//	object << "cityIndex" << m_cityIndex;
//}
//
//bool CityBuildCreater::resume(const BuildData& data, const jsonxx::Object& object, World* pworld)
//{
//	if (!BuildCreaterBase::resume(data, object, pworld))
//	{
//		return false;
//	}
//	if (object.has<jsonxx::Number>("buildIndex"))
//	{
//		m_configIndex = object.get<jsonxx::Number>("buildIndex");
//	}
//	if (object.has<jsonxx::Number>("cityIndex"))
//	{
//		m_cityIndex = object.get<jsonxx::Number>("cityIndex");
//	}
//	return true;
//}

flatbuffers::Offset<FBSave::BuildCreaterSaveData> CityBuildCreater::save(flatbuffers::FlatBufferBuilder& builder)
{
	std::vector<flatbuffers::Offset<FBSave::CityEndSpecialPos>> specials;
	specials.reserve(m_endPlace.size());
	for (const auto& p : m_endPlace)
	{
		auto pos = WCoordToCoord3(p.pos);
		specials.push_back(FBSave::CreateCityEndSpecialPos(builder, &pos, p.block.getResID(), p.block.getData()));
	}
	auto reportPos = WCoordToCoord3(m_reportPos);
	return FBSave::CreateBuildCreaterSaveData(builder, FBSave::BuildCreaterUnion_CityBuildCreaterData, FBSave::CreateCityBuildCreaterData(builder, saveBase(builder), m_configIndex
	, m_cityIndex, builder.CreateVector(specials), &reportPos).Union(), getBuildData().id_2);
}

bool CityBuildCreater::load(const void* srcdata)
{
	const FBSave::CityBuildCreaterData* fBData = static_cast<const FBSave::CityBuildCreaterData*>(srcdata);
	if (!fBData) return false;
	if (!BuildCreaterBase::load(fBData->baseData())) return false;
	m_configIndex = fBData->buildIndex();
	m_cityIndex = fBData->cityIndex();
	if (fBData->endBuild())
	{
		int size = fBData->endBuild()->size();
		m_endPlace.reserve(size);
		m_endPlace.resize(size);
		for (int i = 0; i < size; i++)
		{
			auto posFB = fBData->endBuild()->Get(i);
			if (posFB)
			{
				m_endPlace[i].pos = Coord3ToWCoord(posFB->vmoPos());
				m_endPlace[i].block.setAll(posFB->blockid(), posFB->blockdata());
			}
		}
	}
	if (fBData->reportPos())
	{
		m_reportPos = Coord3ToWCoord(fBData->reportPos());
	}
	return true;
}

void CityBuildCreater::getMosterJsonArray(jsonxx::Array& jsonArr, const EcosysBuildHelp::BuildGenInfo* genInfo, ChunkRandGen& randgen, const WCoord& vmoPos, World* pworld)
{
	if (!pworld) return;
	if (!genInfo) return;
	const auto* monsterData = EcosysBuildHelp::randBuildData(*genInfo, randgen);
	if (!monsterData) return;
	if (randgen.nextInt(100) <= genInfo->m_prob)
	{
		if (monsterData->m_datas.size() > 0)
		{
			for (const auto& data : monsterData->m_datas)
			{
				jsonxx::Object posObj;
				int num = data.num;
				if (num > 0)
				{
					auto wpos = BlockCenterCoord(getOffsetPos(vmoPos));
					posObj << "worldPosX" << wpos.x;
					posObj << "worldPosY" << wpos.y;
					posObj << "worldPosZ" << wpos.z;
					posObj << "monsterNum" << num;
					posObj << "worldMapId" << pworld->getCurMapID();
					if (data.m_id > 0)
					{
						posObj << "monsterId" << data.m_id;
					}
					else if (!data.m_prefabId.empty())
					{
						posObj << "prefabId" << data.m_prefabId.c_str();
					}
					else
					{
						continue;
					}
					jsonArr << posObj;
				}
			}
		}
		else if(genInfo->m_genNum >= 1)
		{
			jsonxx::Object posObj;
			int num = randgen.get(1, genInfo->m_genNum);
			if (num > 0)
			{
				auto wpos = BlockCenterCoord(getOffsetPos(vmoPos));
				posObj << "worldPosX" << wpos.x;
				posObj << "worldPosY" << wpos.y;
				posObj << "worldPosZ" << wpos.z;
				posObj << "monsterNum" << num;
				posObj << "worldMapId" << pworld->getCurMapID();
				if (monsterData->m_id > 0)
				{
					posObj << "monsterId" << monsterData->m_id;
				}
				else if (!monsterData->m_prefabId.empty())
				{
					posObj << "prefabId" << monsterData->m_prefabId.c_str();
				}
				else
				{
					return;
				}
				jsonArr << posObj;
			}
		}
	}
}

void CityBuildCreater::endCallBack(World* pworld)
{
	if (pworld)
	{
		for (const auto& p : m_endPlace)
		{
			pworld->setBlockAll(p.pos, p.block.getResID(), p.block.getData());
		}
	}
	BuildCreaterBase::endCallBack(pworld);
	if (m_configIndex == CityBuildBase && m_reportPos.y >= 0)
	{
		GetEcosysUnitCityBuild().addSpecialBuildMark(pworld, m_reportPos.x, m_reportPos.z);
	}
}

void CityBuildCreater::chunkBuildPlaceCallBack(World* world, BuildProcessBase* process)
{
	if (!world || !process) return;
	if (BuildProcessType_Normal == process->type() || BuildProcessType_City == process->type())
	{
		CityBuildProcess* pCityProcess = static_cast<CityBuildProcess*>(process);
		if (!pCityProcess) return;
		ChunkRandGen randGen;
		randGen.setSeed64((world->getChunkSeed(pCityProcess->chunindex().x, pCityProcess->chunindex().z) << 4) + pCityProcess->m_specialPos.size());
		//基地就一个, 没有新手引导时, 直接就变为空气
		bool genNormal = true;
		if (m_configIndex == CityBuildBase)
		{
			if (g_WorldMgr)
			{
				const auto* pMapData = g_WorldMgr->getMapData(world->getCurMapID());
				if (pMapData) 
				{
					genNormal = pMapData->openGuide;
				}
			}
		}
		//正常处理
		{
			//这个是专门给幸存者用的
			jsonxx::Array survivalPosArray;
			//这个是专门给僵尸用的
			jsonxx::Array zombilePosArray;

			for (const auto& posInfo : pCityProcess->m_specialPos)
			{
				auto replaceInfo = GetDefManagerProxy()->getBuildReplaceDef(posInfo.blockId);
				if (!replaceInfo) continue;
				switch (replaceInfo->ReplaceType)
				{
				case BuildReplaceType::genMonster:
					// handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// if (genNormal && (randGen.nextInt(100) <= genInfo->m_prob))
					// {
					// 	const auto* monsterData = EcosysBuildHelp::randBuildData(*genInfo, randGen);
					// 	if (monsterData && genInfo->m_genNum >= 1)
					// 	{
					// 		int num = randGen.get(1, genInfo->m_genNum);
					// 		if (monsterData->m_id > 0)
					// 		{
					// 			for (int i = 1; i <= num; i++)
					// 			{
					// 				world->getActorMgr()->ToCastMgr()->spawnMob(BlockCenterCoord(getOffsetPos(posInfo.vmoPos)), monsterData->m_id, false, false);
					// 			}
					// 		}
					// 		else if (!monsterData->m_prefabId.empty() && UgcAssetMgr::GetInstancePtr())
					// 		{
					// 			for (int i = 1; i <= num; i++)
					// 			{
					// 				auto* prefab = UgcAssetMgr::GetInstancePtr()->GetPrefabAsset(monsterData->m_prefabId.c_str());
					// 				if (prefab)
					// 				{
					// 					ClientMob* mob = dynamic_cast<ClientMob*>(prefab->Instantiate(world->getCurMapID()));
					// 					if (mob && mob->getLocoMotion())
					// 					{
					// 						mob->getLocoMotion()->gotoPosition(BlockCenterCoord(getOffsetPos(posInfo.vmoPos)), float(GenRandomInt(360)), 0);
					// 					}
					// 				}
					// 			}
					// 		}
					// 	}
					// }
					break;
				case BuildReplaceType::genContainer:
					{
						int id = EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
						if (id > 0)
						{
							auto realpos = getOffsetPos(posInfo.vmoPos);
							world->setBlockAll(realpos, id, 0, (3 | kBlockUpdateFlagBlockEventImmediate));
							world->getWorldProxy()->addDungeonChest(realpos, id, 0, randGen);

							world->GetChestMgr()->addBuildingChest(posInfo.blockId, getOffsetPos(posInfo.vmoPos));

							LOG_INFO_BUILD("#####Create Map record randblockid = %d, replacetype = %d, changed Chest id = %d, pos x = %d, y = %d, z = %d", posInfo.blockId, replaceInfo->ReplaceType, id, realpos.x, realpos.y, realpos.z);
						}
						else
						{
							handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
						}
					}
					break;
				case BuildReplaceType::genHandleByScript:
					//脚本信息
				{
					// handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// if (genNormal) getMosterJsonArray(survivalPosArray, genInfo, randGen, posInfo.vmoPos, world);
					break;
				}
				case BuildReplaceType::genZombile:
					//特殊的刷僵尸
				{
					// handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// if (genNormal) getMosterJsonArray(zombilePosArray, genInfo, randGen, posInfo.vmoPos, world);
					break;
				}
				case BuildReplaceType::genItem: //生成物品
				{
					// if (m_configIndex == CityBuildBase)
					// {
					// 	handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// 	if (randGen.nextInt(100) <= genInfo->m_prob)
					// 	{
					// 		const auto* itemData = EcosysBuildHelp::randBuildData(*genInfo, randGen);
					// 		int id = 0;
					// 		if (itemData->m_id > 0)
					// 		{
					// 			id = itemData->m_id;
					// 		}
					// 		else if (ModPackMgr::GetInstancePtr() && !itemData->m_prefabId.empty())
					// 		{
					// 			id = ModPackMgr::GetInstancePtr()->GetAllocatedId(itemData->m_prefabId.c_str());
					// 		}
					// 		if (id > 0)
					// 		{
					// 			m_endPlace.push_back(SaveBuildEndPlaceData(Block::makeBlock(id, itemData->m_blockData), getOffsetPos(posInfo.vmoPos)));
					// 		}
					// 	}
					// }
					// else if (genNormal && (randGen.nextInt(100) <= genInfo->m_prob))
					// {
					// 	//int num = randGen.get(0, genInfo->m_genNum);
					// 	//for (int i = 1; i <= num; i++)
					// 	//{
					// 	const auto* itemData = EcosysBuildHelp::randBuildData(*genInfo, randGen);
					// 	if (itemData)
					// 	{
					// 		int id = -1;
					// 		if (itemData->m_id > 0)
					// 		{
					// 			id = itemData->m_id;
					// 		}
					// 		else if (ModPackMgr::GetInstancePtr() && !itemData->m_prefabId.empty())
					// 		{
					// 			id = ModPackMgr::GetInstancePtr()->GetAllocatedId(itemData->m_prefabId.c_str());
					// 		}
					// 		if (id > 0)
					// 		{
					// 			const WCoord& pos = getOffsetPos(posInfo.vmoPos);
					// 			world->setBlockAll(pos, id, 0);//核心方块
					// 			//副本方块需要放置三个, 要用container类型判断.
					// 			//电脑方块肯定不需要,就不放了,提升效率
					// 			ContainerMonsterSummoner* MonsterSummoner = nullptr;
					// 			if (id != 200440)
					// 			{
					// 				WorldContainer* container = world->getContainerMgr()->getContainer(getOffsetPos(posInfo.vmoPos));
					// 				if (nullptr != container)
					// 				{
					// 					MonsterSummoner = dynamic_cast<ContainerMonsterSummoner*>(container);
					// 				}
					// 			}
					// 			if (MonsterSummoner)
					// 			{
					// 				world->setBlockAll(NeighborCoord(NeighborCoord(pos, DIR_POS_Y), DIR_POS_Y), id, 8); //占位方块
					// 				world->setBlockAll(NeighborCoord(pos, DIR_POS_Y), id, 4); //占位方块
					// 			}
					// 			if (genInfo->m_levelType > 0)
					// 			{
					// 				if (MonsterSummoner)
					// 				{
					// 					MonsterSummoner->setSummonerLevelType(genInfo->m_levelType);
					// 					if (genInfo->m_level > 0)
					// 					{
					// 						MonsterSummoner->setSummonerLevel(genInfo->m_level);
					// 					}
					// 				}
					// 			}
					// 		}
					// 		else
					// 		{
					// 			handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// 		}
					// 	}
					// 	else
					// 	{
					// 		handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// 	}
					// }
					// else
					// {
					// 	handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);
					// }
				}
				break;
				
				case BuildReplaceType::replaceBlock:
				{
					WCoord offset = getOffsetPos(posInfo.vmoPos);
					auto curBiome = world->getBiome(offset.x, offset.z);
					int topBlockId = 0;
					if (curBiome) topBlockId = curBiome->TopBlock;
					handleBlock(world, posInfo.vmoPos, topBlockId, 0);
					LOG_INFO_BUILD("#####Create Map record randblockid = %d, replacetype = %d, changed biomeid = %d , topblockid = %d"
						, posInfo.blockId, replaceInfo->ReplaceType, curBiome->ID, topBlockId);
					break;
				}
				case BuildReplaceType::genRandomNpc:
				{
					handleBlock(world, posInfo.vmoPos, BLOCK_AIR, 0);

					int id = EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
					if (id > 0)
					{
						auto realBlockpos = getOffsetPos(posInfo.vmoPos);
						auto realNpcPos = BlockCenterCoord(realBlockpos);	 
						world->getActorMgr()->ToCastMgr()->addMobPreset(posInfo.blockId, realNpcPos.x, realNpcPos.y, realNpcPos.z);
						LOG_INFO_BUILD("#####Create Map record randblockid = %d, replacetype = %d, changed npc id = %d, pos x = %d, y = %d, z = %d"
							, posInfo.blockId, replaceInfo->ReplaceType, id, realNpcPos.x, realNpcPos.y, realNpcPos.z);
					}
					break;
				}

				case BuildReplaceType::genRadiationSource:
				{
					handleBlock(world, posInfo.vmoPos, BLOCK_RADIATION, 0);
					LOG_INFO_BUILD("#####Create Map record randblockid = %d, replacetype = %d, changed to radiation"
						, posInfo.blockId, replaceInfo->ReplaceType);
				}
				break;
				case BuildReplaceType::genVehicle:
				{
					int id = EcosysBuildHelp::randReplaceId(replaceInfo, randGen);
					if (id > 0) {
						int dir = DIR_NEG_X;
						auto realpos = getOffsetPos(posInfo.vmoPos);
						ActorVehicleAssemble::createWithUserdataFile(world, id, realpos, dir);
						LOG_INFO_BUILD("#####Create Map record randblockid = %d, replacetype = %d, changed to vehicle id = %d, vechiclepos x = %d, y = %d, z = %d"
							, posInfo.blockId, replaceInfo->ReplaceType, id, realpos.x, realpos.y, realpos.z);
					}
				}
				break;
				
				default:
					break;
				}
			}

			//由脚本处理的在这里
			if (!survivalPosArray.empty())
			{
				jsonxx::Object customObj;
				ObserverEvent obevent;
				customObj << "monsterArray" << survivalPosArray; //[{worldPosX:1, worldPosY:1, worldPosZ:1, monsterId: 111, monsterNum:1}, {worldPosX:1, worldPosY:1, worldPosZ:1, prefabId:"121112"}]
				obevent.SetData_CustomStr(customObj.json_nospace());
				if (m_configIndex == CityBuildBase)
				{
					GetObserverEventManager().OnTriggerEvent("Build.BaseActorSpawn", &obevent);
				}
				else
				{
					GetObserverEventManager().OnTriggerEvent("Build.ActorSpawn", &obevent);
				}
			}
			//特殊的僵尸事件
			if (!zombilePosArray.empty())
			{
				int level = getBuildLevel(randGen);
				//获取怪物等级
			/*	const auto* cityInfo = CityConfig::getSingletonPtr()->getCityDataByIndex(m_cityIndex);
				if (cityInfo) level = cityInfo->m_monsterLevel;*/
				jsonxx::Object customObj;
				ObserverEvent obevent;
				customObj << "monsterArray" << zombilePosArray; //[{worldPosX:1, worldPosY:1, worldPosZ:1, monsterId: 111, monsterNum:1}, {worldPosX:1, worldPosY:1, worldPosZ:1, prefabId:"121112"}]
				customObj << "monsterLevel" << level;
				obevent.SetData_CustomStr(customObj.json_nospace());
				if (m_configIndex == CityBuildBase)
				{
					GetObserverEventManager().OnTriggerEvent("Build.BaseZombieSpawn", &obevent);
				}
				else
				{
					GetObserverEventManager().OnTriggerEvent("Build.ZombieSpawn", &obevent);
				}
			}
		}
	}
}

BuildProcessBase* CityBuildCreater::createNormalProcess(const WCoord& cstart, const WCoord& cend, const ChunkIndex& index)
{
	return ENG_NEW(CityBuildProcess)(cstart, cend, index);
}

bool CityBuildCreater::getRealFilePath(Rainbow::FixedString& simplePath, Rainbow::FixedString& realPath)
{
	if (m_data.fileName.empty())
	{
		LOG_WARNING("CityBuildCreater getRealFilePath error, file name error");
		return false;
	}
	realPath = UgcAssetMgr::GetInstance().GetAssetFilePath(m_data.fileName.c_str()).c_str();
	simplePath = m_data.fileName;
	return true;
}

bool CityBuildCreater::isLoadPath(const Rainbow::FixedString& simplePath, const Rainbow::FixedString& realPath)
{
	if (m_data.fileName.empty())
	{
		LOG_WARNING("CityBuildCreater isLoadPath error, file name error");
		return false;
	}
	return m_data.fileName == simplePath;
}

//bool CityRoadCreater::resumeVmoDataInit()
//{
//	if (!m_pBigBuildVM)
//	{
//		return false;
//	}
//	std::string realPath;
//	if (CityConfig::getSingletonPtr() && CityConfig::getSingletonPtr()->m_debug)
//	{
//		char filePath[100];
//		sprintf(filePath, "vbp/%s.vbp", m_data.fileName.c_str());
//		realPath = filePath;
//	}
//	else
//	{
//		realPath = UgcAssetMgr::GetInstance().GetAssetFilePath(m_data.fileName.c_str());
//	}
//	if (!m_pBigBuildVM->LoadAssert(realPath.c_str(), m_data.dir, true))
//	{
//		assert(0);
//		LOG_WARNING("maybe err!load %s failed!", m_data.fileName.c_str());
//		return false;
//	}
//	return true;
//}

void CityBuildCreater::fillContainersChunk(World* world, BuildProcessBase* process)
{
	if (!process) return;
	if (process->type() != BuildProcessType_City) return;
	CityBuildProcess* pCityProcess = static_cast<CityBuildProcess*>(process);
	if (!pCityProcess) return;
	auto index = pCityProcess->chunindex();
	if (!m_pBigBuildVM)
		return;
	auto containerData = m_pBigBuildVM->getContainerFBData();
	if (!containerData)
		return;
	WCoord leftBottom(index.x * SECTION_BLOCK_DIM, 0, index.z * SECTION_BLOCK_DIM);
	WCoord rightTop = leftBottom + WCoord(SECTION_BLOCK_DIM - 1, CHUNK_BLOCK_Y - 1, SECTION_BLOCK_DIM - 1);
	const auto& specialPos = pCityProcess->m_specialPos;

	for (size_t i = 0; i < containerData->size(); i++)
	{
		auto con = containerData->Get(i);
		if (!con) continue;
		WCoord vmoPos = Coord3ToWCoord(con->relativepos());
		WCoord relativePos = getOffsetPos(vmoPos);
		//如果位置在这个chunk的话,再去赋值
		if (!EcosysBuildHelp::isInRange(relativePos, leftBottom, rightTop))
		{
			continue;
		}
		//有说明是该被删除的，不要添加
		if (std::find_if(specialPos.begin(), specialPos.end(), [&vmoPos](const CityBuildProcess::specialPos& posA) {
			return posA.vmoPos == vmoPos;
			}) != specialPos.end())
		{
			continue;
		}

		WorldContainer* container = CreateWorldContainerFromChunkContainer(con->container());
		if (container)
		{
			if (container->load(con->container()->container()))
			{
				container->setBlockPos(relativePos);
				ContainerPolaroidFrame* pContainerFrame = dynamic_cast<ContainerPolaroidFrame*>(container);
				if (pContainerFrame)
				{
					pContainerFrame->setRecreate(true);
				}
				m_container.push_back(container);
				//world->getContainerMgr()->spawnContainer(container);
			}
			else
			{
				SANDBOX_DELETE(container);
			}
		}
	}
}