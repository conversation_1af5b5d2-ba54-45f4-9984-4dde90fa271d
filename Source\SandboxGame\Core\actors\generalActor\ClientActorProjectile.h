
#ifndef __CLIENTACTORPROJECTILE_H__
#define __CLIENTACTORPROJECTILE_H__

#include "ClientActorProjectileDef.h"

#include "actors/clientActor/ClientActor.h"
#include "blocks/container.h"
#include "BaseClass/PPtr.h"
#include "GameModeDef.h"

struct ProjectileDef;
class BaseItemMesh;
class BackPackGrid;
EXPORT_SANDBOXGAME extern int  g_all_projectile;


enum PROJECTILE_SKILL_TRIGGER_TIME
{
	PROJECTILE_SKILL_INSTANITATE = 0,
	PROJECTILE_SKILL_IMPACT_WITH_ACTOR_OR_BLOCK,
};

struct DelayMotionData 
{
	std::string name = "";
	bool reset_play = false;
	int motion_class = 0;
	float looptime = -1;
};

struct DelayMotionCompare
{
	bool operator()(int a, int b) const
	{
		return a < b;
	}
};

class BaseItemMesh;
namespace game {
    namespace hc {
        class PB_GeneralEnterAOIHC;
    }

	namespace common {
		class PB_ActorProjectile;
	}
}

class EXPORT_SANDBOXGAME ClientActorProjectile;
class ClientActorProjectile : public ClientActor //tolua_exports
{ //tolua_exports
	DECLARE_SCENEOBJECTCLASS(ClientActorProjectile)
public:
	void CreateEvent2();
	void DestroyEvent2();
	//tolua_begin
	ClientActorProjectile();
	virtual void init(int itemid, ClientActor *shooter = nullptr);
	virtual void onMotionStop();
	virtual int getDelayClearTicks(); //获取子弹延迟清理的时间，这里和 配置表的不一样这里不会影响triger 的触发 
	virtual void onImpactWithActor(ClientActor *actor, const std::string& partname);
	virtual void onImpactWithBlock(const WCoord *blockpos, int face);
	virtual void onCollideWithPlayer(ClientActor *player);
	virtual bool canBeCollidedWith();

	virtual void doTrigger();
	//virtual void onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum);
	virtual void update(float dtime);
	virtual void RotateForWeapon(int itemId, Rainbow::Entity* entity);
	virtual void tick();
	virtual int  getObjType() const override;
	virtual int getMass();
	virtual std::string getFullyCustomModelKey();

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual bool supportSaveToPB();
	virtual int saveToPB(game::hc::PB_GeneralEnterAOIHC* pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC& pb);
	int saveToProjectilePB(game::common::PB_ActorProjectile* actorProj);
	int LoadFromProjectilePB(const game::common::PB_ActorProjectile& actorProj);

	virtual void moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks);
	virtual void moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks);

	void setShootingActor(ClientActor *actor);
	virtual ClientActor *getShootingActor();
	virtual void setNeedClearEx(int delay_ticks=0);
	virtual void onClear();
	bool NeedTickForever() override{
		return true; 
	}
	void clearBlock();
	bool isBullets(int id);
	bool needRoteBullets(int id);
	static ClientActorProjectile *shootProjectileAuto(int itemid, World *pworld, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, float deviation, long long shooterObjId, int durable = -1);

	bool isInFire()
	{
		return (m_AttachedEffect & 1) != 0;
	}
	int getFireLv()
	{
		return m_FireLv > 0 ? m_FireLv : 1;
	}
	bool hasExplodeEffect()
	{
		return (m_AttachedEffect & 2) != 0;
	}
	bool canPickup()
	{
		return (m_AttachedEffect & 4) == 0;
	}

	int GetItemId()
	{
		return m_ItemID;
	}

	void setBuff(int buffid)
	{
		m_BuffId = buffid;
	}

	void applyRuneData(ClientActor* targetActor, bool damaged);

	void playMotion(const char *name, bool reset_play=true, int motion_class=0, int delayTicks = 0,float looptime = -1.0f);
	void stopMotion();
	void stopMotion(const char* name);	//停止指定特效
	void setMotionScale(const char* name, float fScale);	//设置特效大小
	float getMotionScale(const char* name);				//获取特效大小
	int getMotionCount();				//获取特效数
	void playCustomMotion(char *zipPath, const char* resPath, bool reset_play = true, int motion_class = 211111); // 211111 :是zipload完之后播放的默认class_type
	const char* getMotionNameByIndex(int index, int class_type);//获取特效名

	void setProperty_byGrid(const BackPackGrid* grid);//设置附魔/符文属性
	
	unsigned int getColor() { return m_Color; }
	virtual bool IsAutoClear();
	
	std::vector<ClientActor*> doPickActorByItemSkill(ClientActor*target, int skillid, WCoord &centerPos);
	std::vector<WCoord> doPickBlockByItemSkill(int skillid, const WCoord *block, int face);
	void useItemSkill(ClientActor *actor,const WCoord *blockpos = NULL, int face = DIR_NEG_X);
	void checkUseItemSkillBlock(const WCoord* blockpos = NULL, int face = DIR_NEG_X);
	void checkUseItemSkillActor(ClientActor* actor);
	void checkCallMob();
	void checkCreateActor(ClientActor* actor, const WCoord* block, int face);//创建各种玩法特殊的actor需求
	bool isProjectileSkill();
	virtual void playEffect();
	virtual void enterWorld(World *pworld) override;
	virtual void leaveWorld(bool keep_inchunk) override;

	virtual bool interact(ClientActor*pPlayer, bool onshift = false, bool isMobile = false)override;
	virtual bool leftClickInteract(ClientActor* player) override;

	void createPhysActor(int id);
	void resetModel();

	void playSoundByPhysCollision();

	virtual void updateBodyByFullyCustomModel();
	virtual void updateBodyByImportModel();

	void setBeShootedActor(ClientActor *actor);
	virtual ClientActor *getBeShootedActor();
	void playEntityModelAnim(int seq,int inputloopmode = -1, bool include_me = false);
	void stopEntityModelAnim(int seq = -1);
	/*
	投掷物引用带动作的微缩模型播放动画
	act 动画ID  triggeract.csv
	playmode 动画播放模式
*/
	bool playAct(int act, int playmode);
	//tolua_end
	void changeModel(Rainbow::Model* model, Rainbow::Entity* entity); //改变投掷物外观接口
	bool isInfluence(int skillId, ClientActor* actor);
	int GetAnimID() { return m_TriggerAnimID; }
	int GetAnimMode() { return m_AnimMode; }
	void UpdateAttackPoints();
	void UpdateAttackPointsByAttri(LivingAttrib* attri);

	void addSkillCfg(const Rainbow::FixedString& id, int triggerType);
	void setDropable(bool able)
	{
		m_dropable = able;
	}

	virtual bool canPassActor();
	virtual bool checkCanPenetrate(int blockid);
	virtual bool skillPlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class = 0);
	virtual bool skillStopBodyEffect(const char* path);
protected:
	void setProperty(int durable, int enchantNum, const int* enchants);
	void processSkillOnInit(ClientActor* shooter = nullptr);

private:
	void DoUseItemSkillBlock(const WCoord* block, int face);
	
public:
	//tolua_begin
	float m_BaseAtk;
	unsigned char m_AttachedEffect; //bit0: fire,   bit1: explode,   bit2: 不能拾取
	unsigned char m_FireLv;
	WCoord m_StartPos;

	float m_AttackPoints;
	float m_BuffAttackAdd;
	float m_KnockbackStrength;
	float m_ImpactTimeMark;
	const ProjectileDef* m_ProjectileDef;

	bool m_HasImpackActor;
	//这个buff会覆盖def中的buff
	int m_BuffId;
	//被攻击的生物是否被道具技能击飞过
	bool m_bAttackFly;
	int m_BoomerangState; //回旋镖所处状态，0：手持，嵌入方块，1：飞出去，2：飞回来，3：未接住后落地过程
	std::string m_UserDataStr;
	//tolua_end

	// 新增属性
	int m_atkType;					// 攻击类型
	float m_bowDamageIns;			// 弓伤害百分比加成
	int m_touReduce;				// 实际削韧（炸弹随距离衰减、弓箭随蓄力衰减）
	float m_strength;				// 蓄力
	float m_AttackPointsNew[10];	// 普通攻击力（0近战、1远程、2爆炸、3物理、4火、5毒、6混乱、7电、8冰、9魔法）
	float m_ExplodePoints[7];		// 爆炸攻击力（0物理、1火、2毒、3混乱、4电、5冰、6空元素）

	bool m_dropable;	//?·???protected:
	virtual ~ClientActorProjectile();

	Rainbow::PPtr<Rainbow::Model> m_Model;
	Rainbow::PPtr<Rainbow::Entity> m_EntityModel;
	Rainbow::PPtr<Rainbow::Model> m_Model_Left;
	Rainbow::PPtr<Rainbow::Entity> m_EntityModel_Left;
	WORLD_ID m_ShootingActorID;
	WORLD_ID m_BeShootedActorID;  //被射击的actor
	int m_ItemID;

	int m_Durable;
	int m_MaxDurable;
	int m_EnchantNum;
	int m_Enchants[MAX_ITEM_ENCHANTS];

	int m_Color;

	int m_nBlockID;
	WCoord m_BlockPos;
	float m_starBulletDuration; //光照方块持续时间

	GridRuneData m_Runedata;

	std::string m_FullySKey;
	bool m_isCheckFinally;
	bool m_isUseCustomAtkDuration; // 是否用技能编辑器模式自定义的耐久消耗
private:
	std::map<std::string, const char *>  m_motionNames; //特效索引
	int m_TriggerAnimID;
	int m_AnimMode;
	std::string m_cureffectName;//客机微缩模型不显示特效

	std::map<int, DelayMotionData, DelayMotionCompare> m_delayMotionMap; // 延迟播放特效列表
	int m_curTicksNum;
	
	int m_canPassActorNum;		// 可穿过actor的数量
	int m_passActorNum;			// 穿过actor的数量

	Rainbow::FixedString m_skillName;	//技能编辑器技能名字
	int m_skillTriggerType;		//技能编辑器的技能触发时机：0=投掷物生成时  1 = 命中目标时（命中一次触发一次） 2 = 投掷物结束时（落地、击碎时落地等）
	float m_puncture; //击碎值
	SkillComponent* m_pSkillComponent;              //技能组件
	// 通知监听
	MNSandbox::AutoRef<MNSandbox::Listener<IClientPlayer*&>> m_listenerProj1;
	MNSandbox::AutoRef<MNSandbox::Listener<int&, int&>> m_listenerProj2;
}; //tolua_exports

#endif