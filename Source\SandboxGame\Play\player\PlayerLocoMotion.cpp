
#include "ClientActor.h"
#include "Components/Camera.h"
#include "PlayerLocoMotion.h"
#include "ActorAttrib.h"
#include "PlayerControl.h"
//#include "GameEvent.h"
#include "BlockMaterial.h"
#include "BlockMaterialMgr.h"
#include "ActorBody.h"
#include "DefManagerProxy.h"
#include "GameMode.h"
//#include "OgreRoot.h"
#include "special_blockid.h"
#include "GameCamera.h"
#include "LuaInterfaceProxy.h"
//#include "OgreMath.h"
#include "GunUseComponent.h"
#include "ActorBall.h"
#include "OgrePhysXManager.h"
#include "RecordPkgManager.h"
#include "InputInfo.h"
#include "OgrePrerequisites.h"
#include "DebugDataMgr.h"
#include "ActorBasketBall.h"
#include "ActorVehicleAssemble.h"
#include "PhysicsComponent.h"
#include "Physics/MessageParameters.h"
#include "BlockScene.h"
#include "PlayerAttrib.h"
#include "IRecordInterface.h"
#include "vector_nofree.h"
#include "ClientActorFuncWrapper.h"
#include "RiddenComponent.h"
#include "EffectComponent.h"
#include "CarryComponent.h"
#include "AttackedComponent.h"
#include "OgreUtils.h"
#include "PlayerCheat.h"
//#include "VMProtect.h"
#include "GameNetManager.h"
#include "OgreEntity.h"
#include "TransferMgr.h"
#include "section.h"
#include "SandboxCfg.h"
#include "ActorPushSnowBall.h"
#include "FallComponent.h"

#include "Optick/optick.h"
#include "ChargeJumpComponent.h"
#include "ScriptComponent.h"
#include "Misc/TimeManager.h"
#include "ActionIdleStateGunAdvance.h"
#include "Graphics/LegacyGlobalShaderParam.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
IMPLEMENT_COMPONENTCLASS(PlayerLocoMotion)

void initCheckClipInterval();

const int kLadderNone = 0;
const int kLadderTick = 1;
const int kOnLadder = 2;
const int kNotOnLadder = 3;

//#define USE_RIGIDBODY 1

PlayerLocoMotion::PlayerLocoMotion() : add_motion(0, 0, 0),
	m_OldBlockPos(0, -10000, 0)/*, m_OldTickBlockPos(0, -10000, 0)*/, m_CheckPosCD(0), m_tickLadder(0)
{
	//m_WalkSpeed = 4;
	//m_SprintSpeed = 6;
	//m_SneakSpeed = 2;
	m_PhysActor = NULL;
	m_EnchantClimbCount = 0;
	memset(m_AirJumpCount, 0x00, sizeof(m_AirJumpCount));

	initCheckClipInterval();
	m_RoleController = NULL;
	m_BoundSizeProtect = m_BoundHeightProtect = 0;
	m_InWaterProtect = 0;
	m_MoveUp = 0;
	m_CurPhysType = RolePhysType::PHYS_ROLECONTROLLER;
	m_skillChargeMove = -1.0f;

	m_OldTransPos = Rainbow::Vector3f(0, -1, 0);
	m_TickOffsetTime = 0;
}
PlayerLocoMotion::~PlayerLocoMotion()
{
}
static bool CanJumpOnPos2(ClientActor *actor, int dx, int dz)
{
	World *pworld = actor->getWorld();
	WCoord pos = actor->getPosition();

	WCoord blockpos = CoordDivBlock(pos);
	/*
	for(int h=1; h<=2; h++)
	{
		int blockid = pworld->getBlockID(blockpos + WCoord(0,h,0));
		if(blockid>0 && GetDefManagerProxy()->getBlockDef(blockid)->MoveCollide==1) return false;
	}*/

	WCoord targetpos = CoordDivBlock(pos + WCoord(dx, BLOCK_SIZE/2, dz));
	if(targetpos == blockpos) return false;

	int blockid = pworld->getBlockID(targetpos);
	if(blockid == 0) return false;

	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if(!pmtl) return false;
	if(!pmtl->defBlockMove()) return false;
	if(!pmtl->isOpaqueCube()) return false;

	for(int i=1; i<=2; i++)
	{
		auto block = pworld->getBlock(targetpos + WCoord(0, i, 0));
		if (!block.isEmpty() && (block.moveCollide() == 1))
		{
			return false;
		}
		//int upid = pworld->getBlockID(targetpos+WCoord(0,i,0));
		//if(upid>0 && GetDefManagerProxy()->getBlockDef(upid)->MoveCollide==1) 
		//	return false;
	}

	return true;
}

static SInt64 CalMoveDist(World *pworld, const CollideAABB &box, const WCoord &mvec)
{
	WCoord realmove = pworld->moveBox(box, mvec);
	return realmove.lengthSquared();
}

static bool CanJumpOnPos(ClientActor* actor, int dx, int dz)
{
	XMLNode node = GetIWorldConfigProxy()->getRootNode().getChild("GameData");
	if (!node.isNull())
	{
		XMLNode child = node.getChild("Settinig");
		if (!child.isNull())
		{
			if (child.hasAttrib("autojump"))
			{
				if (child.attribToInt("autojump") != 1 || (GetWorldManagerPtr()->isCustomGame() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal((GAMEMAKER_RULE)GMRULE_GRAVITYFACTOR) > 1))
					return false;
			}
		}
	}

	World* pworld = actor->getWorld();

	CollideAABB boundbox;
	actor->getCollideBox(boundbox);

	bool hasEnoughRoomByNonPhys = false;
	bool hasEnoughRoomByPhys = false;

	WCoord mvec(dx, 0, dz);
	SInt64 md = mvec.lengthSquared();

	boundbox.pos.y += BLOCK_SIZE + BLOCK_SIZE / 8;
	SInt64 d = CalMoveDist(pworld, boundbox, mvec);
	boundbox.pos.y -= BLOCK_SIZE - BLOCK_SIZE / 8;
	SInt64 d1 = CalMoveDist(pworld, boundbox, mvec);

	if (d > d1 + 1 || (d == d1 && d >= 5000))
	{
		hasEnoughRoomByNonPhys = true;
	}

	Rainbow::Vector3f direction(mvec.x, mvec.y, mvec.z);
	direction.NormalizeSafe();
	Rainbow::RaycastHit hitInfo;
	float radius = 20.0f;
	PlayerLocoMotion* loc = nullptr;
	auto physScene = pworld->m_PhysScene;
	Rainbow::Vector3f position(0, -1, 0);
	
	static unsigned int iLayerMask = 0xffffffff; /*~((1 << kLayerIndexCustom_Player) | (1 << kLayerIndexCustom_Trigger) | (1 << kLayerIndexCustom_Dynamic));*/
	if (physScene && actor->isPlayer())
	{
		loc = static_cast<PlayerLocoMotion*>(actor->getLocoMotion());
		if (loc->getPhysType() == RolePhysType::PHYS_RIGIDBODY && loc->m_PhysActor)
		{
			Quaternionf quat;
			loc->m_PhysActor->GetPos(position, quat);
		}
		else if (loc->getPhysType() == RolePhysType::PHYS_ROLECONTROLLER && loc->getRoleController())
		{
			position = loc->getRoleController()->GetPosition();
		}

		if (position.y < 0)
		{
			hasEnoughRoomByPhys = true;
		}
		else
		{
			float boundheight = loc->m_BoundHeight / 2.0f;
			bool bRet = physScene->SweepCapsule(radius, Rainbow::Vector3f(position.x, position.y + BLOCK_SIZE + 1 + radius - boundheight, position.z)
				, Rainbow::Vector3f(position.x, position.y + BLOCK_SIZE + 1 + boundheight - radius, position.z), direction, 80.0f, hitInfo, iLayerMask, true);

			if (!bRet)		//没有物理障碍
				hasEnoughRoomByPhys = true;
			/*else if (hitInfo.collider)
			{
				ClientActor* pActor = dynamic_cast<ClientActor*>((ClientActor*)hitInfo.collider->GetUserData());
				if (!pActor || !pActor->getPhysicsComponent())
					hasEnoughRoomByPhys = true;
			}*/
		}
	}
	else
		hasEnoughRoomByPhys = true;  //没有物理场景就不需要考虑物理的障碍物了

	boundbox.pos.y += BLOCK_SIZE / 2 + BLOCK_SIZE / 8;
	SInt64 d2 = CalMoveDist(pworld, boundbox, mvec);

	if (d1 < md && d1 == d2 && d > d1 + 1)
	{
		if (hasEnoughRoomByNonPhys && hasEnoughRoomByPhys)
			return true;
	}

	if (!physScene || !actor->isPlayer() || position.y < 0)	//不是角色或者没有物理场景就不需要考虑物理的障碍物了
	{
		return false;
	}

	if (hasEnoughRoomByNonPhys && hasEnoughRoomByPhys && loc)
	{
		float boundheight = loc->m_BoundHeight / 2.0f;

		bool bRet = pworld->m_PhysScene->SweepCapsule(radius, Rainbow::Vector3f(position.x, position.y - boundheight + radius, position.z)
			, Rainbow::Vector3f(position.x, position.y + boundheight - radius, position.z), direction, 40.0f, hitInfo, iLayerMask, true);

		if (bRet)
		{

			bRet = pworld->m_PhysScene->SweepCapsule(radius, Rainbow::Vector3f(position.x, position.y - boundheight + radius + 55, position.z)
				, Rainbow::Vector3f(position.x, position.y + boundheight - radius + 55, position.z), direction, 40.0f, hitInfo, iLayerMask, true);

			return bRet;
		}
	}

	return false;
}


int PlayerLocoMotion::sweepMovePos(const WCoord& mvec, Rainbow::Vector3f& normal, float &walkUpLadderHeight, bool &onRebound)
{
	OPTICK_EVENT();

	auto oldState = m_CurMovementState;
	m_CurMovementState = MovementState::Other;  //先重置状态，下面再计算真实的状态
	if (!m_pWorld)
		return 0;

	auto pPhysScene = m_pWorld->m_PhysScene;
	if (!pPhysScene)
		return 0;

	int iRet = -1;
	auto player = static_cast<ClientPlayer*>(GetOwner());
	if (!player || player->isFlying())
	{
		if (player)
		{
			player->SetOnPlatform(nullptr);
		}
		return iRet;
	}

	float boundheight = (m_BoundHeight - m_BoundSize) / 2.0f;
	float boundsize = m_BoundSize / 2.0f;
	if (player && player->getSneaking())
	{
		boundheight = (m_BoundHeight / 2.0 - 2 * boundsize) / 2.0f;
	}
	Rainbow::Vector3f direction(0, -1.0f, 0);

	Rainbow::Vector3f position;
	Rainbow::RaycastHit hitInfo;
	ClientActor* pActor = nullptr;
	static unsigned int iSweepLayerMask = ~(1 << kLayerIndexCustom_Player);//~(1 << kLayerIndexCustom_Player | 1 << kLayerIndexCustom_Trigger);

	do
	{
		if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
		{
			if (!m_PhysActor)
			{
				iRet = 0;
				break;
			}

			Quaternionf quat;
			m_PhysActor->GetPos(position, quat);

			bool bRet = pPhysScene->SweepCapsule(boundsize, Rainbow::Vector3f(position.x, position.y - boundheight + 33, position.z),
				Rainbow::Vector3f(position.x, position.y + boundheight + 33, position.z), direction, 43.0f, hitInfo, iSweepLayerMask, true);
			if (bRet && hitInfo.collider)
			{
				if (mvec.x == 0 && mvec.z == 0 && mvec.y < 0 && (hitInfo.point.y - position.y + m_BoundHeight / 2) < -1)  //地面不水平动的时候 调整落地高度偏差
				{
					m_CurMovementState = MovementState::AdjustLandingOffset;
				}

				bool bOnPlatform = player->IsOnPlatform();

				pActor = dynamic_cast<ClientActor*>((ClientActor*)hitInfo.collider->GetUserData());
				if (pActor)
				{
					ScriptComponent* scriptcom = pActor->getScriptComponent();
					if (scriptcom && scriptcom->GetIsRebound())  //落地的时候落在了带有反弹组件的实体上
					{
						onRebound = true;
					}
					auto pPhysicsComponent = pActor->getPhysicsComponent();
					if (pPhysicsComponent)
					{
						if (pPhysicsComponent->IsMoveType(LPT_Platform))
						{
							if (!bOnPlatform || player->GetPlatform() != pActor)
							{
								player->SetOnPlatform(pActor);
								pPhysicsComponent->OnCollisionEvent(PhxEvent_BindPlatform, player);
							}
						}
						else
						{
							pActor = nullptr;
						}
					}
					else
					{
						pActor = nullptr;
					}
				}

				iRet = 0;
			}
		}
		else
		{
			if (!m_RoleController || !m_RoleController->GetGameObject())
			{
				iRet = 0;
				break;
			}

			float checkDist = 13; //物理底部位置比loc位置高2+ActorLocoMotion::doMoveStep逻辑里有往下移动10像素~但是却不能完全下移10像素判断为在地上的逻辑+浮点数精度问题所以这里是13
			boundsize = boundsize - 2;
			position = m_RoleController->GetPosition();
			if (player && player->getSneaking())
			{
				position.y -= m_BoundHeight / 4.0;
			}

			bool bRet = pPhysScene->SweepCapsule(boundsize, Rainbow::Vector3f(position.x, position.y - boundheight + 1, position.z)
				, Rainbow::Vector3f(position.x, position.y + boundheight + 1, position.z), direction, checkDist, hitInfo, iSweepLayerMask, true);

			if(bRet && hitInfo.collider)
			{
				bool bOnPlatform = player->IsOnPlatform();

				pActor = dynamic_cast<ClientActor*>((ClientActor*)hitInfo.collider->GetUserData());
				if (pActor)
				{
					ScriptComponent* scriptcom = pActor->getScriptComponent();
					if (scriptcom && scriptcom->GetIsRebound())  //落地的时候落在了带有反弹组件的实体上
					{
						onRebound = true;
					}

					auto pPhysicsComponent = pActor->getPhysicsComponent();
					if (pPhysicsComponent)
					{
						if (pPhysicsComponent->IsMoveType(LPT_Platform))
						{
							if (!bOnPlatform || player->GetPlatform() != pActor)
							{
								player->SetOnPlatform(pActor);
								pPhysicsComponent->OnCollisionEvent(PhxEvent_BindPlatform, player);
							}
						}
						else
						{
							pActor = nullptr;
						}
					}
					else
					{
						pActor = nullptr;
					}
				}

				iRet = 0;
			}
		}

		if (iRet == 0 && (Abs(hitInfo.normal.x) > kEpsilon || Abs(hitInfo.normal.z) > kEpsilon)) //完全由这里判断是否在坡道上不太准确,潜行时站在方块边缘也会被误测出来
		{

			bool checkBlockEdgeOK = true;
			float rayCastHitAngle = -999.0f;
			Vector3f hitInfo2Point = Vector3f(0, -1, 0);
			if (mvec.x != 0 || mvec.z != 0)  //当有移动时，判断碰撞点往行走方向5单位得位置发射向下射线检测，看是否有坡道，避免潜行时在方块边缘检测成坡道掉落下去了
			{
				Rainbow::RaycastHit hitInfo2;
				Rainbow::Vector3f dir(mvec.x, 0, mvec.z);
				dir.NormalizeSafe();
				Rainbow::Vector3f beginpt = hitInfo.point + dir * 5.0f + Vector3f(0, 200, 0);

				bool ret = m_pWorld->m_PhysScene->RayCast(beginpt, Rainbow::Vector3f(0, -1, 0), 280, hitInfo2, iSweepLayerMask, true);  //站在坡的边缘会检测不到，不过这种情况应该影响不大
				if (ret)
				{
					rayCastHitAngle = Rainbow::Rad2Deg(Rainbow::Angle(hitInfo2.normal, Vector3f::yAxis));
					hitInfo2Point = hitInfo2.point;
					//if (angle < 5) //不是坡道或者坡道很小，这种情况视为不站在坡上
					//	checkBlockEdgeOK = false;
				}
				checkBlockEdgeOK = ret;
			}

			if (checkBlockEdgeOK)
			{
				float angle = Rainbow::Rad2Deg(Rainbow::Angle(hitInfo.normal, Vector3f::yAxis));
				if (angle > 0)
				{
					normal = hitInfo.normal;
					if (angle > 60 && (rayCastHitAngle == -999.0f || rayCastHitAngle > 60))
					{
						m_CurMovementState = MovementState::OnSlopeFall;
					}
					else if(angle <= 60)
					{
						bool isSlope = true;
						if (hitInfo2Point.y >= 0 && hitInfo2Point.y < hitInfo.point.y)
						{
							Vector3f normal2 = hitInfo.point - hitInfo2Point;
							float angle2 = Rainbow::Rad2Deg(Rainbow::Angle(normal2, Vector3f::yAxis));
							if (angle2 < 30 || angle2 > 150)  //当前坡道小于60°，但连续两个碰撞点直接形成的坡道角度大于60°，判断为不是连续坡道
								isSlope = false;
						}

						if(isSlope)
							m_CurMovementState = MovementState::OnSlope;
					}
				}
			}
		}

		if (((iRet == 0 && m_CurMovementState == MovementState::Other) || oldState == MovementState::WalkUpHalfBlockHeight) && (mvec.x != 0 || mvec.z != 0))  //在地上，不是半坡上时，如果有水平方向的移动
		{
			Rainbow::Vector3f dir(mvec.x, 0, mvec.z);	//检测半格远的行进方向有低于半格高的障碍物，可以直接行走上去
			dir.NormalizeSafe();
			Rainbow::Vector3f pt = position + dir * 50.0f;
			float radius = 5.0f;
			float capsuleHeight = 10.0f;
			/*bool bRet = pPhysScene->SweepCapsule(radius, Rainbow::Vector3f(pt.x, pt.y + 140 + radius + capsuleHeight, pt.z)
				, Rainbow::Vector3f(pt.x, pt.y + 140 + radius, pt.z), direction, 228, hitInfo, iSweepLayerMask, true);*/

			//用SweepCapsule检测时胶囊体碰到方块边缘有可能碰撞点法线方向被判断成前方是坡道，所以改成用RayCast
			bool bRet = m_pWorld->m_PhysScene->RayCast(Rainbow::Vector3f(pt.x, pt.y + 220, pt.z), Rainbow::Vector3f(0, -1, 0), 305, hitInfo, iSweepLayerMask, true); 

			if (bRet)
			{
				float dist = hitInfo.point.y - (position.y - boundheight - boundsize);
				if (dist > 0 && dist <= 110)
				{
					float angle = Rainbow::Rad2Deg(Rainbow::Angle(hitInfo.normal, Vector3f::yAxis));
					if (angle >= 0 && angle <= 10)
					{
						if (dist <= 55)
						{
							m_CurMovementState = MovementState::WalkUpHalfBlockHeight;
							walkUpLadderHeight = dist;
							if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER)  //角色控制器，物理位置要比游戏位置要高2像素
								walkUpLadderHeight += 2;    
						}
						else
						{
							bool canAutoJump = true;
							XMLNode node = GetIWorldConfigProxy()->getRootNode().getChild("GameData");
							if (!node.isNull())
							{
								XMLNode child = node.getChild("Settinig");
								if (!child.isNull())
								{
									if (child.hasAttrib("autojump"))
									{
										if (child.attribToInt("autojump") != 1 || (GetWorldManagerPtr()->isCustomGame() && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal((GAMEMAKER_RULE)GMRULE_GRAVITYFACTOR) > 1))
											canAutoJump = false;
									}
								}
							}

							// 自动跳跃关闭 on socgame note by cloud.
							// if(canAutoJump)
							// 	m_CurMovementState = MovementState::JumpOnBlockHeight;
						}
					}
					else if (angle <= 60)
					{
						Rainbow::RaycastHit hitInfo2;
						Rainbow::Vector3f beginpt = position - Vector3f(0, boundheight+boundsize, 0);

						bool ret = m_pWorld->m_PhysScene->RayCast(beginpt, dir, 50, hitInfo2, iSweepLayerMask, true);  //增加一个脚底向移动方向的射线来辅助检查坡度情况，避免像弧形方块这样的 底部位置的坡度大于60，顶部位置的坡度小于60,坡度判断不准导致偶尔能爬上去偶尔爬不上去
						if (ret)
						{
							float angle2 = Rainbow::Rad2Deg(Rainbow::Angle(hitInfo2.normal, Vector3f::yAxis));
							if(angle2 > 60)
								m_CurMovementState = MovementState::StopUphill;
	
						}
						
						if(m_CurMovementState != MovementState::StopUphill)
						{
							m_CurMovementState = MovementState::OnSlope;
							normal = hitInfo.normal;
						}
					}
					else
					{
						m_CurMovementState = MovementState::StopUphill;
					}
				}
			}
		}
	} while (false);

	//LogStringMsg("m_CurMovementState +++++++++++++++++++++++++++++++++++++ %d", (int)m_CurMovementState);
	player->SetOnPlatform(pActor);
	return iRet;
}

Rainbow::Transform* PlayerLocoMotion::GetTransform()
{
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		return m_PhysActor->GetTransform();
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController && m_RoleController->GetGameObject())
	{
		return m_RoleController->GetTransform();
	}

	return nullptr;
}

Rainbow::GameObject* PlayerLocoMotion::GetGameObject()
{
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		return m_PhysActor->GetGameObject();
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController)
	{
		return m_RoleController->GetGameObject();
	}

	return nullptr;
}

void PlayerLocoMotion::SetLinearVelocity(const Rainbow::Vector3f& v)
{
	if (m_PhysActor)
	{
		m_PhysActor->SetLinearVelocity(v);
	}
}

void PlayerLocoMotion::addMotion(float dx, float dy, float dz)
{
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	if (player && player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT && player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		return;
	}
	ActorLocoMotion::addMotion(dx, dy, dz);
}

float PlayerLocoMotion::getWaterJumpingSpeed()
{
	float waterJumpBaseSpeed = LivingLocoMotion::getWaterJumpingSpeed();
	float waterJumpSpeed = waterJumpBaseSpeed;

	PlayerAttrib* attrib = dynamic_cast<PlayerAttrib*>(getOwnerActor()->getAttrib());

	if (attrib)
	{
		waterJumpSpeed = attrib->calculateMoveSpeed(waterJumpBaseSpeed, Actor_Swim_Speed);
	}

	return waterJumpSpeed;
}


void PlayerLocoMotion::doTwoJump(float motiony /* = -1 */)
{
	if (motiony < 0)
	{
		float height = getJumpHeight() * 2;
		m_Motion.y = height;
	}
	else
		m_Motion.y = motiony;
	
	//SetLinearVelocity(m_Motion * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.phys_Angular_damping * MOTION2VELOCITY);
	SetLinearVelocity(m_Motion * 2.6f * MOTION2VELOCITY);

	ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
	if (player)
	{
		player->addAchievement(3, ACHIEVEMENT_JUMPCOUNT);
		player->getPlayerAttrib()->useStamina(STAMINA_JUMP);
	}
}

void PlayerLocoMotion::checkFallByRigidType()
{
	if (m_PhysActor)
	{
		Vector3f newPos;
		Rainbow::Quaternionf quat;
		m_PhysActor->GetPos(newPos, quat);
		
		if (!m_RigidBodyFallData.onGround || !m_OnGround)  //两次都不在地面上时才记录掉落距离
		{
			float fallDist = newPos.y - m_RigidBodyFallData.oldPos.y;
			FallComponent::calFallMotion(getOwnerActor(), fallDist, m_OnGround);
		}

		m_RigidBodyFallData.oldPos = newPos;
		m_RigidBodyFallData.onGround = m_OnGround;
	}
}

bool PlayerLocoMotion::specialMove(WCoord &realmov, float &fSlopeFallSpeed, float fWalkUpLadderHeight, Vector3f vSlopeNormal)
{
	if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER)
	{
		if (realmov.y == 0)
		{
			switch (m_CurMovementState)
			{
			case MovementState::WalkUpHalfBlockHeight:
			{
				if (fWalkUpLadderHeight > 0)
				{
					realmov.y = ceil(fWalkUpLadderHeight);
				}
			}
			break;
			case MovementState::OnSlopeFall:
			{
				Vector3f velocity = m_RoleController->GetVelocity();
				LogStringMsg("OnSlopeFall velocity.y %f", velocity.y);

				float stepY = -30;
				Vector3f move = ProjectOnPlane(Vector3f(0, stepY, 0), vSlopeNormal);

				realmov.x = move.x;
				realmov.z = move.z;
				realmov.y = move.y;
			}
			break;
			case MovementState::OnSlope:
			{
				/*float baseStep = Vector3f(realmov.x, 0, realmov.z).Length();
				float stepRate = baseStep / 21;

				Vector3f move = ProjectOnPlane(Vector3f(realmov.x, 0, realmov.z), vSlopeNormal).GetNormalizedSafe() * baseStep;

				if (move.y != 0 && baseStep > 8)
				{
					Vector3f gravityEffectMove = ProjectOnPlane(Vector3f(0, -1, 0), vSlopeNormal).GetNormalizedSafe() * 8.0f;
					move += gravityEffectMove;
				}
				
				realmov.x = move.x;
				realmov.z = move.z;
				realmov.y = move.y;
				if (move.y - realmov.y > 0)
					realmov.y++;
				else if (move.y - realmov.y < 0)
					realmov.y--;*/


				if (DotProduct(realmov.toVector3(), vSlopeNormal) < 0)//上坡
				{
					realmov.y = 1; //默认值
					if (m_pWorld->m_PhysScene) //小于60°
					{
						Rainbow::Vector3f position = m_RoleController->GetPosition();
						Rainbow::Vector3f beginpt = position + Rainbow::Vector3f(realmov.x, 50, realmov.z);
						Rainbow::RaycastHit hitInfo;
						bool ret = m_pWorld->m_PhysScene->RayCast(beginpt, Rainbow::Vector3f(0, -1, 0), 70+m_BoundHeight/2-2, hitInfo, 0xffffffff, true);
						if (ret)
						{
							float diffHeight = hitInfo.point.y - (position.y-m_BoundHeight/2) + 2;
							realmov.y = diffHeight;
						}
					}
				}
				else
				{
					float anngle = Rainbow::Angle(vSlopeNormal, Vector3f::yAxis);
					realmov.y = -50.0f * anngle;
				}
			}
			break;
			default:
				break;
			}
		}
	}
	else if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
	{
		if (realmov.y == 0)
		{
			switch (m_CurMovementState)
			{
				case MovementState::WalkUpHalfBlockHeight:
				{
					//LogStringMsg("WalkUpHalfBlockHeight %f", fWalkUpLadderHeight);
					if (!m_PhysActor || !m_pWorld->m_PhysScene)
						return true;

					Rainbow::Vector3f direction(realmov.x, 0, realmov.z);
					direction.NormalizeSafe();
					Rainbow::RaycastHit hitInfo;
					Rainbow::Vector3f pos;
					Rainbow::Quaternionf rot;
					m_PhysActor->GetPos(pos, rot);

					bool bRet = m_pWorld->m_PhysScene->SweepCapsule(10, Rainbow::Vector3f(pos.x, pos.y - m_BoundHeight/2+ fWalkUpLadderHeight, pos.z)
						, Rainbow::Vector3f(pos.x, pos.y - m_BoundHeight / 2 + fWalkUpLadderHeight+10, pos.z), direction, 100, hitInfo, ~(1 << kLayerIndexCustom_Player), true);

					if (bRet)
					{
						m_PhysActor->SetLinearVelocity(Vector3f::zero);
						m_PhysActor->SetAngularVelocity(Vector3f::zero);

						Vector3f vHorizontal = Vector3f(realmov.x, 0, realmov.z);
						float stepRate = vHorizontal.Length() / 21;

						auto newpos = Vector3f(hitInfo.point.x, pos.y + fWalkUpLadderHeight, hitInfo.point.z);
						Vector3f force = Vector3f(hitInfo.point.x - pos.x, fWalkUpLadderHeight, hitInfo.point.z - pos.z).GetNormalized() * 1500000.0f * stepRate;
						return AddRealRigidSpecialMove(force);
					}
				}
				break;
				case MovementState::OnSlopeFall:
				{
					Vector3f moveGravityForce = ProjectOnPlane(Vector3f(0, -5000000, 0), vSlopeNormal);    //模拟重力影响
					return AddRealRigidSpecialMove(moveGravityForce);
				}
				break;
				case MovementState::OnSlope:
				{
					Vector3f vHorizontal = Vector3f(realmov.x, 0, realmov.z);
					float stepRate = vHorizontal.Length() / 21;

					Vector3f moveForce = ProjectOnPlane(Vector3f(realmov.x, 0, realmov.z), vSlopeNormal).GetNormalizedSafe() * 950000.0 * stepRate;  //移动步长受到的力

					bool ret = AddRealRigidSpecialMove(moveForce);
					if (m_PhysActor && m_PhysActor->GetLinearVelocity().y != 0)	//模拟重力影响，防止沿着曲面切线移动时，角色浮在空中；
					{
						AddRealRigidSpecialMove(Vector3f(0, -300000.0f * stepRate, 0));
					}

					return ret;
				}
				break;
				case MovementState::StopUphill:
				{
					if (m_PhysActor)
					{
						m_PhysActor->SetLinearVelocity(Vector3f::zero);
						m_PhysActor->SetAngularVelocity(Vector3f::zero);
					}
					return true;
				}
				break;
				default:
					break;
			}
		}
	}

	return false;
}

void PlayerLocoMotion::reviseCheckAutoJumpBox(CollideAABB& boundbox)
{
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		Vector3f position;
		Quaternionf quat;
		m_PhysActor->GetPos(position, quat);

		boundbox.pos = WCoord(position.x- m_BoundSize/2+2, position.y - m_BoundHeight/2, position.z- m_BoundSize/2+2);
		//boundbox.pos = WCoord(position.x, boundbox.pos.y, position.z);
	}
}

void PlayerLocoMotion::updatePhysCollisionBetweenPlayers()
{
	if (!m_pWorld || !m_pWorld->m_PhysScene)
		return;

	if (m_RoleController && m_RoleController->GetGameObject())  //目前只需要处理角色控制器
	{
		auto  layer = m_RoleController->GetGameObject()->GetLayer();
		m_pWorld->m_PhysScene->IgnoreCollision(layer, layer, !hasCollisionBetweenPlayers());
	}
}

void PlayerLocoMotion::getCollideBoxMatchPhysBox(CollideAABB& box)
{
	auto height = m_BoundHeight;
	if (getOwnerActor()->getSneaking())
		height *= 0.5;
	box.dim = WCoord(m_BoundSize-4, height, m_BoundSize-4);  //角色物理胶囊体的半径比角色游戏碰撞盒子半径小2个单位
	box.pos = getPosition() - WCoord(box.dim.x / 2, m_yOffset, box.dim.z / 2);
}

void PlayerLocoMotion::moveUpByUnderFootPlaceBlock(int blockid)
{
	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	float addHeight = 0.0f;
	if (blockid == BLOCK_WOODPILE || IsFenceBlock(blockid) || IsFenceGate(blockid))  //放置新木桩，一次是两根，需要跳跃更高,栅栏和栅栏门也需要更高
	{
		if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
			addHeight = 2 * BLOCK_SIZE;
		else
		{
			doTwoJump(200.0f);

			//放置的方块物理盒子太高了，需要先把向上移动的逻辑执行了，而不是等tick里的doMoveStep的执行移动逻辑（放置方块的物理盒子会把角色胶囊体包住，导致角色胶囊体移不上去）
			doMoveStep(m_Motion);
			m_Motion.y = 0;
		}
			
	}
	else
	{
		if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
			addHeight = BLOCK_SIZE;
		else
			doJump();
	}
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
	{
		if (m_PhysActor)
		{
			Vector3f pos;
			Quaternionf quat;
			m_PhysActor->GetPos(pos, quat);
			pos.y += addHeight;
			m_PhysActor->SetPos(pos, quat);
		}
	}
	else
		player->setMotionChange(m_Motion);
}

//static float jump_scale = 2.0f;
static float jump_scale = 1.5f;
void PlayerLocoMotion::tick()
{
	if ((m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController) || (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor))
	{
		checkPhysWorld();
	}
	m_tickLadder = kLadderTick;
	ActorLocoMotion::tick();

	//updateInWaterState();

	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	if (player)
	{
		if (m_OldBlockPos.y == -10000)
			m_OldBlockPos = m_Position / BLOCK_SIZE;

		m_CheckPosCD++;
		if (m_CheckPosCD >= 10)
		{
			m_CheckPosCD = 0;

			WCoord posBlock = m_Position / BLOCK_SIZE;
			int moveBlockNum = Rainbow::Abs(posBlock.x - m_OldBlockPos.x);
			moveBlockNum += Rainbow::Abs(posBlock.y - m_OldBlockPos.y);
			moveBlockNum += Rainbow::Abs(posBlock.z - m_OldBlockPos.z);
			if (moveBlockNum > 16)  //超过16格算1格,防止类似使用传送卷轴这种瞬移类的移动触发多次
				moveBlockNum = 1;

			for (int i = 0; i < moveBlockNum; i++)
			{
				player->moveOneBlockSizeOnTrigger();
			}

			if (moveBlockNum > 0)
			{
				m_OldBlockPos = m_Position / BLOCK_SIZE;
			}
		}

		//Rainbow::WorldPos actorWorldPos = m_Position.toWorldPos();
		//LOG_INFO("player m_OnGround: %d   actorWorldPos: %d %d %d", m_OnGround, actorWorldPos.x, actorWorldPos.y, actorWorldPos.z);
	}

	//------------------------------LivingLocoMotion::tick() content;------------------------------
	m_Motion *= 0.98f;
	if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
	//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
	if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;
	if(player == NULL) return;
	
	PlayerAttrib* pattrib = player->getPlayerAttrib();
	if (pattrib && !pattrib->isPlayerDowned())
		updateJumping(m_CurPhysType == RolePhysType::PHYS_RIGIDBODY);
	
	if(g_pPlayerCtrl == player && player != NULL && player->isInSpectatorMode() && player->getSpectatorType() == SPECTATOR_TYPE_FOLLW )
	{
		ClientPlayer* tospectatorplayer = g_pPlayerCtrl->getToSpectatorPlayer();
		if(tospectatorplayer)
		{
			m_OldPosition = m_Position;
			m_Position = tospectatorplayer->getLocoMotion()->getPosition();
			
			if (g_WorldMgr)
				g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
		}
		return;
	}
	else
	{
		m_MoveStrafing *= 0.98f;
		m_MoveForward *= 0.98f;	
		moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
	}

	//playEnterLeaveWaterSound();
	//updatePrevInWaterState();
	
	if (getOwnerActor() != NULL)
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			if (functionWrapper->getCanFly())
			{
				if (m_Motion.y < 0) m_Motion.y *= 0.6f;
			}
		}
	}

	if(player != NULL && !player->isInSpectatorMode())
		collideWithNearbyActors();
	//------------------------------LivingLocoMotion::tick() end;------------------------------

	int iSpeedType = Actor_Walk_Speed;
	ActorAttrib *attrib = getOwnerActor()->getAttrib();
	if(attrib){
		m_JumpMovementFactor = attrib->getSpeedInAir();
	}
	
	if (m_InWater || m_InLava || m_InHoney)
	{
		iSpeedType = Actor_Swim_Speed;
	}
	else if (player->getRun())
	{
		m_JumpMovementFactor *= GetLuaInterfaceProxy().get_lua_const()->chongci_yidong_beilv;
		iSpeedType = Actor_Run_Speed;
	}
	else if (player->getSneaking())
	{
		iSpeedType = Actor_Sneak_Speed;
	}
	else
	{
		iSpeedType = Actor_Walk_Speed;
	}

	float currentMoveSpeed = (attrib != NULL) ? attrib->getMoveSpeed(iSpeedType) : 0;

	//新枪械对移动速度的影响
	ActionIdleStateGunAdvance* idleStateGunAdvance = player->GetIdleStateGunAdvance();
	if (idleStateGunAdvance)
	{
		currentMoveSpeed *= idleStateGunAdvance->GetSpeedBonus();
	}

	//枪对移动速度的影响	
	if (GetDefManagerProxy()->getGunDef(player->getCurToolID()))
	{
		currentMoveSpeed = currentMoveSpeed * player->getGunLogical()->getGunSpeedRatio();
	}

	//道具技能对速度的影响
	const ItemSkillDef* skilldef = player->getCurItemSkillDef();
	if (skilldef && skilldef->ChargeMove > 0.001f)
		currentMoveSpeed = currentMoveSpeed * skilldef->ChargeMove;
	if (m_skillChargeMove > 0)
	{
		currentMoveSpeed = currentMoveSpeed * m_skillChargeMove;
	}

	//附魔的影响	
	if (m_IsEnchClimb && isOnAirWall())
	{
		doJump();
	}
	m_IsEnchClimb = false;
	if (getOwnerActor() && player &&(!(m_tickLadder == kOnLadder) && (m_tickLadder == kNotOnLadder || !isOnLadder())) && !isOnAirWall())
	{

		float value2 = 0;
		float value1 = player->getLivingAttrib()->getEquipEnchantValue(EQUIP_SHOE, ENCHANT_CLIMB,ATTACK_ALL,ATTACK_TARGET_ALL,&value2);
		if(value2 > 0)
		{
			//getOwnerActor()->m_FallDistance = 0;
			//if(m_Motion.y < -15.0f) m_Motion.y = -15.0f;

			if(m_CollidedHorizontally)
			{
				m_Motion.x *= (1 + value1);
				m_Motion.z *= (1 + value1);
				m_Motion.y = 5.0f*(1 + value1);

				//每2秒消耗耐久
				if(m_EnchantClimbCount>0 && m_EnchantClimbCount%40 == 0)
				{
					player->getLivingAttrib()->damageEquipItemWithType(EQUIP_SHOE, (int)value2);
				}

				m_IsEnchClimb = true;
				m_EnchantClimbCount++;
			}
			else
			{
				m_EnchantClimbCount = 0;
			}
		}
	}	
	m_tickLadder = kLadderNone;
	if (player->getCurOperate() == PLAYEROP_BALL_CHARGE_BEGIN)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.charge_move_speed_ratio;
	}
	else if (player->getCurOperate() == PLAYEROP_BASKETBALL_CHARGE_BEGIN)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.charge_move_ratio;
	}
	else if (player->getCatchBall())
	{
		if (player->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_move_ratio;
		else if (player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_move_ratio;
		else
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.dribbing_move_ratio;
	}
	else if (player->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.football_way__move_ratio;
	}
	else if (player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		if (player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.obstruct_move_ratio;
		}
		else
		{
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.basketball_way_move_ratio;
		}
	}
	else if (player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.football_way__move_ratio;
	}

	if (player->isChargeWalkSlow())
	{
		currentMoveSpeed *= GetLuaInterfaceProxy().get_lua_const()->xuli_yidong_beilv;
	}
	else if (player->getRun())
	{
		currentMoveSpeed *= GetLuaInterfaceProxy().get_lua_const()->chongci_yidong_beilv;
	}

	if (player->IsInDefanceState())
	{
		const ItemDef* def = GetDefManagerProxy()->getItemDef(player->getCurToolID());
		if (def && def->SkillID.size() > 0)
		{
			const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(def->SkillID[0]);
			if (skilldef)
			{
				currentMoveSpeed = currentMoveSpeed * skilldef->ChargeMove;
			}
		}
		if (m_skillChargeMove > 0)
		{
			currentMoveSpeed = currentMoveSpeed * m_skillChargeMove;
		}

	}

	auto funcWrapper = player->getFuncWrapper();
	if (funcWrapper)
	{
		funcWrapper->setAIMoveSpeed(currentMoveSpeed * player->getSpeedUpTimes());
	}

	if(m_JumpingTicks==0 && !getOwnerActor()->getSneaking() && m_OnGround && !getOwnerActor()->getFlying() && (fabs(m_MoveForward) > EPSILON ||fabs(m_MoveStrafing) > EPSILON))
	{
		Rainbow::Vector3f fdir = Yaw2FowardDir(m_RotateYaw);
		Rainbow::Vector3f sdir = Yaw2StrafingDir(m_RotateYaw);
		float r = (m_BoundSize*jump_scale) / Sqrt(m_MoveForward*m_MoveForward + m_MoveStrafing*m_MoveStrafing);
		int dx = int((m_MoveForward*fdir.x + m_MoveStrafing*sdir.x) * r);
		int dz = int((m_MoveForward*fdir.z + m_MoveStrafing*sdir.z) * r);
	
		if(m_CurMovementState == MovementState::JumpOnBlockHeight)
		{
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
			if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
			}
			else
			{
				int cooldown;
				if (prepareJump(cooldown))
				{
					if (m_CurPhysType == PHYS_RIGIDBODY)
					{
						m_Motion.SetZero();
					}

					autoStep();
					m_JumpingTicks = cooldown;
				}
			}
		}
	}

	//land from fly state
	if(m_OnGround && getOwnerActor() && m_Motion.y<=0 && getOwnerActor()->getFlying())
	{
		//******** 非玩法创造模式 落地后不设置取消飞行 codeby qinpeng
		// 审查模式下，落地不取消飞行 code-by:liya
		if (g_WorldMgr && !g_WorldMgr->isGameMakerRunMode() && !player->IsInPierceMode() && player->hasUIControl())
		{
			if (player->isNewMoveSyncSwitchOn())
				player->changeMoveFlag(IFC_Fly, false);
			else
				player->setFlying(false);
		}
	}

	//滑板自定义动作判断是否在方块边缘
	ClientPlayer* pPlayer = static_cast<ClientPlayer*>(GetOwnerActor());
	if (pPlayer->getSkinID() == 440 && m_OnGround && !getOwnerActor()->getFlying()) {

		Rainbow::Vector3f rotEuler = player->GetWorldRotationEuler();
		Rainbow::Vector3f  directions[] = {
			{1, 0, 0},
			{0, 0, 1},
			{-1, 0, 0},
			{0, 0, -1}
		};


		auto BlockIsSoft = [](int blockid) {
			auto blockdef = GetDefManagerProxy()->getBlockDef(blockid);
			auto type = blockdef->Type;
			bool res1 = false;
			if (blockdef)
			{
				if (type == "colorflower" || type == "grayleaf")
				{
					res1 = true;
				}
			}
			bool res2 = IsAirBlockID(blockid) || IsLeavesBlockID(blockid) || IsSnowID(blockid) || IsNormalGrassBlockID(blockid);
			return res1 || res2;
		};


		float minAngle = -1;
		int closestDirection = -1;

		Vector3f fdir = Yaw2FowardDir(m_RotateYaw);

		for (int i = 0; i < 4; ++i) {
			float angle = DotProduct(fdir, directions[i]);
			if (angle > minAngle) {
				minAngle = angle;
				closestDirection = i;
			}
		}
		//WCoord curPos = CoordDivBlock(m_Position); //玩家坐标
		WCoord curPos = m_Position; //玩家坐标
		Vector3f curPos_v3f = curPos.toVector3();
		curPos_v3f *= directions[closestDirection] * directions[closestDirection];//只保留沿当前垂直移动方向上，即z轴坐标值，其他两个轴的坐标值 乘以 0 
		float curMoveAxisValue = curPos_v3f.x + curPos_v3f.y + curPos_v3f.z; //当前移动轴的坐标值
		//float curDirIsPositive = directions[closestDirection].x + directions[closestDirection].y + directions[closestDirection].z; //z轴方向是正值还是负值

		int tempY = (curPos.y % 100 + 30) > 100 ? 1 : 0;          //修复玩家处于悬空状态下，y坐标下降了10左右的问题
		curPos = CoordDivBlock(curPos) + WCoord(0, tempY, 0);   //修复玩家处于悬空状态下，y坐标下降了10左右的问题

		//玩家当前位置再往前一个单位的方块的id
		WCoord frontOffset = directions[closestDirection];
		int frontBlockId = m_pWorld->getBlockID(curPos + frontOffset);

		//向+x方向移动100后的坐标
		Vector3f nextZV3 = curPos_v3f + (frontOffset.toVector3() * Vector3f(100, 100, 100));
		Vector3f nextZV3toV1X = nextZV3 * frontOffset.toVector3() * frontOffset.toVector3();
		float nextZValue = nextZV3toV1X.x + nextZV3toV1X.y + nextZV3toV1X.z; //当前移动轴的坐标值

		//如果移动后坐标值大于0，是往正方向移动，反之往负方向移动
		float curZAxisSign = (nextZValue - curMoveAxisValue) > 0 ? 1 : -1;

		//玩家当前位置再往前一个单位,再往左一个单位的方块的id
		//左一个单位，就是要获取当前坐标系的-x方向，刚好是 directions[closestDirection+1]的方向
		Vector3f leftOffset = directions[int((closestDirection + 1) % 4)];
		int leftFrontBlockId = m_pWorld->getBlockID(curPos + frontOffset + leftOffset);

		//当前位置再往前一个单位,再往下一个单位的方块的id
		WCoord frontDownOffset = curPos + frontOffset + WCoord(0, -1, 0);
		int frontDownBlockId = m_pWorld->getBlockID(frontDownOffset);
		int value1 = int(curMoveAxisValue) % 100;

		bool condition11 = value1 > 0 && value1 < 20 && curZAxisSign < 0;
		bool condition12 = value1 > 80 && curZAxisSign > 0;
		bool condition13 = value1 < 0 && value1 > -20 && curZAxisSign > 0;
		bool condition14 = value1 < -80 && curZAxisSign < 0;

		//往前一个单位和往前一个单位再往下一个单位都没有方块

		int downBlockId = m_pWorld->getBlockID(curPos + WCoord(0, -1, 0));//当前位置往下一个单位

		//bool spaceCondition1 = frontDownBlockId == 0 && frontBlockId == 0;
		bool spaceCondition1 = BlockIsSoft(frontDownBlockId) && BlockIsSoft(frontBlockId);

		int backDownBlockId = m_pWorld->getBlockID(curPos + WCoord(0, -1, 0) + frontOffset * -1);//当前位置往下，再向后一个单位

		//如果看起来已经在空中，但是自身碰撞体和下方的方块还有一点接触没有掉下去
		//bool spaceCondition2 = downBlockId == 0 && spaceCondition1;
		bool spaceCondition2 = BlockIsSoft(downBlockId) && spaceCondition1;

		bool condition21 = value1 > -100 && curZAxisSign < 0;
		bool condition22 = value1 < 100 && curZAxisSign > 0;

		int leftBlockId = m_pWorld->getBlockID(curPos + leftOffset);
		int leftDownBlockId = m_pWorld->getBlockID(curPos + leftOffset + WCoord(0, -1, 0));
		WCoord rightOffset = -leftOffset;
		int rightBlockId = m_pWorld->getBlockID(curPos + rightOffset);
		int rightDownBlockId = m_pWorld->getBlockID(curPos + rightOffset + WCoord(0, -1, 0));
		int rightFrontBlockId = m_pWorld->getBlockID(curPos + frontOffset + rightOffset);
		//如果往左一个单位没有bloack ,往左
		// 一个单位再往下一个单位也没有bloack，处于方块靠左的边缘
		//可以使用特殊滑行动作

		Vector3f curPosV3toV1X = m_Position.toVector3() * leftOffset * leftOffset;//只保留沿当前水平方向,即x方向的坐标值，其他两个轴的坐标值 乘以 0 
		float curXAxisValue = curPosV3toV1X.x + curPosV3toV1X.y + curPosV3toV1X.z; //当前移动轴的坐标值
		//float curXAxisSign = leftOffset.x + leftOffset.y + leftOffset.z; //x轴方向是正值还是负值

		//向+x方向移动100后的坐标
		Vector3f nextXV3 = curPosV3toV1X + (leftOffset * Vector3f(100, 100, 100));
		Vector3f nextXV3toV1X = nextXV3 * leftOffset * leftOffset;
		float nextXValue = nextXV3toV1X.x + nextXV3toV1X.y + nextXV3toV1X.z; //当前移动轴的坐标值

		//如果移动后坐标值大于0，是往正方向移动，反之往负方向移动
		float curXAxisSign = (curXAxisValue - nextXValue) > 0 ? 1 : -1;

		int curXAxisValueLimit = int(curXAxisValue) % 100;

		bool conditionx1 = curXAxisValueLimit >= 0 && curXAxisValueLimit < 30 && curXAxisSign > 0;
		bool conditionx2 = curXAxisValueLimit > 70 && curXAxisSign < 0;
		bool conditionx3 = curXAxisValueLimit <= 0 && curXAxisValueLimit > -30 && curXAxisSign < 0;
		bool conditionx4 = curXAxisValueLimit < -70 && curXAxisSign > 0;
		bool conditionx5 = curXAxisValueLimit >= 0 && curXAxisValueLimit < 30 && curXAxisSign < 0;
		bool conditionx6 = curXAxisValueLimit > 70 && curXAxisSign > 0;
		bool conditionx7 = curXAxisValueLimit <= 0 && curXAxisValueLimit > -30 && curXAxisSign > 0;
		bool conditionx8 = curXAxisValueLimit < -70 && curXAxisSign < 0;

		bool spaceConditionX1 = BlockIsSoft(leftBlockId) && BlockIsSoft(leftDownBlockId) && !BlockIsSoft(downBlockId) && (conditionx1 || conditionx2 || conditionx3 || conditionx4);
		bool spaceConditionX2 = !BlockIsSoft(rightDownBlockId) && BlockIsSoft(downBlockId) && (conditionx5 || conditionx6 || conditionx7 || conditionx8);
		bool spaceConditionX3 = BlockIsSoft(rightBlockId) && BlockIsSoft(rightDownBlockId) && !BlockIsSoft(downBlockId) && (conditionx5 || conditionx6 || conditionx7 || conditionx8);
		bool spaceConditionX4 = !BlockIsSoft(leftDownBlockId) && BlockIsSoft(downBlockId) && (conditionx1 || conditionx2 || conditionx3 || conditionx4);

		if (spaceConditionX1 || spaceConditionX2)
		{
			m_OnBlockLeftEdge = true;
		}
		else
		{
			m_OnBlockLeftEdge = false;
			//m_CanDriftWithSkateboard = false;
		}

		if (spaceConditionX3 || spaceConditionX4)
		{
			m_OnBlockRightEdge = true;
		}
		else
		{
			m_OnBlockRightEdge = false;
			//m_CanDriftWithSkateboard = false;
		}

		//前方是否有一个一个高的方块，并且该方块的左边没有方块
		bool spaceCondition3L = !BlockIsSoft(frontBlockId) && BlockIsSoft(leftFrontBlockId) && (conditionx1 || conditionx2 || conditionx3 || conditionx4);
		bool spaceCondition3R = !BlockIsSoft(frontBlockId) && BlockIsSoft(rightFrontBlockId) && (conditionx5 || conditionx6 || conditionx7 || conditionx8);
		if (spaceCondition3L)
		{
			m_FrontBlockLeftIsEmpty = true;
			m_CanDriftWithSkateboard = true;
		}
		else m_FrontBlockLeftIsEmpty = false;

		if (spaceCondition3R)
		{
			m_FrontBlockRightIsEmpty = true;
			m_CanDriftWithSkateboard = true;
		}
		else m_FrontBlockRightIsEmpty = false;

		//判断是否在方块边缘靠前的位置
		bool isOnFront = (spaceCondition1 && (condition11 || condition12 || condition13 || condition14)) || (spaceCondition2 && (condition21 || condition22));
		if (m_OnBlockLeftEdge == false && m_OnBlockRightEdge == false && isOnFront)
		{
			m_OnBlockFrontEdge = true;
		}
		else m_OnBlockFrontEdge = false;

	}


	//clear fall distance in fly status
	if (getOwnerActor() != NULL && (getOwnerActor()->getFlying() || player->isInSpectatorMode()))
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0.0f);
		}
	}

	OnFixedUpdate();

	// 状态(开发者用)
	player->setFallGround(m_OnGround);
	bool horizontalMove = fabs(m_Motion.x) > EPSILON ||fabs(m_Motion.z) > EPSILON;
	player->setCurrentMoveState(horizontalMove);
	player->setStopState(m_OnGround && !horizontalMove);
	player->updateRunState();
	checkOtherEffectTriggerMotion();
}

bool PlayerLocoMotion::isOnLadder()
{
	WCoord pos(m_Position.x, m_Position.y + m_yOffset, m_Position.z);
	{
		int blockid = m_pWorld->getBlockID(CoordDivBlock(pos));
		if (IsClimbBlockID(blockid))//(blockid==BLOCK_LADDER || blockid==BLOCK_VINE);
			return true;
		//还要考虑从背面爬--所以要检查面向的是不是树藤
		Rainbow::Vector3f dir = getLookDir();
		dir  = MINIW::Normalize(dir);
		dir *= (BLOCK_SIZE * 0.5);
		WCoord blockpos = CoordDivBlock(pos + dir);
		blockid = m_pWorld->getBlockID(blockpos);
		if (IsClimbBlockID(blockid))
			return true;
	}

	MINIW::WorldRay ray;
	pos.y += BLOCK_SIZE;
	ray.m_Origin = pos.toWorldPos();
	ray.m_Dir = Rainbow::Vector3f(0, -1, 0);
	ray.m_Range = 3*BLOCK_SIZE;//优化射线检测
	static ActorExcludes excludes;
	std::vector<IClientActor*> actors(m_pWorld->pickAllVehicleActors(ray, excludes,0, false)); 
	pos.y -= BLOCK_SIZE;
	int blockID = 0, _x, _y, _z; 
	for (int i = 0; i < (int)actors.size(); i++)
	{
		ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(actors[i]);
		if (vehicle == NULL)
		{
			continue;
		}
		if (vehicle->inBlock(pos, blockID, _x, _y, _z))
		{
			if (IsClimbBlockID(blockID))//(blockID==BLOCK_LADDER || blockid==BLOCK_VINE)
				return true;
		}
		pos.y += BLOCK_SIZE/2;
		if (vehicle->inBlock(pos, blockID, _x, _y, _z))
		{
			if (IsClimbBlockID(blockID))//(blockID==BLOCK_LADDER || blockid==BLOCK_VINE)
				return true;
		}
	}

	return false;
}
namespace {
	unsigned cheatcCheckClipCount = 0;  // 计数次数
	unsigned cheatCheckBoundinterval = 1;  // 发送次数间隔, 可变
	const unsigned cheatCheckBoundintervalMax = 1000;  // 发送次数间隔上限
	const unsigned cheatCheckRandom = (Timer::getTimeUS() & 0xffff);
}
void initCheckClipInterval(){
	cheatcCheckClipCount = 0;
	cheatCheckBoundinterval = 1;
}
/**
 * @brief 返回本次是否被发送间隔限制, 为避免发送频率过高, 发送间隔会逐渐增大
 * 
 * @return true 被限制, 不应发送
 * @return false 未被限制, 可发送
 */
bool checkIntervalLimited(){
	if (++cheatcCheckClipCount >= cheatCheckBoundinterval){
		cheatcCheckClipCount = 0;
		if (cheatCheckBoundinterval <= cheatCheckBoundintervalMax)
			cheatCheckBoundinterval += 10;
		return false;
	}
	return true;
}
/**
 * @brief 客机检测碰撞异常, 将参数发送到主机校验
 */
void PlayerLocoMotion::checkCheckClipHost()
{
	PlayerControl* player = dynamic_cast<PlayerControl*>(getOwnerActor());
	if (!player || !player->hasUIControl())
		return;
	// 检测bound数值是否正常
	if (!checkBound() || m_BoundHeight < 2 || m_BoundSize < 2)
	{
		if (checkIntervalLimited())
			return;
		PB_HostCheckCheat pbHCC;
		pbHCC.set_checktype(CCT_Clip);
		auto* pbSub = pbHCC.mutable_clip();
		pbSub->set_bound_height(m_BoundHeight);
		pbSub->set_bound_size(m_BoundSize);
		GameNetManager::getInstance()->sendToHost(PB_CHEAT_CHECK_CH, pbHCC);
		return;
	}
	
#ifdef USE_ACTOR_CONTROL
	if (m_CurPhysType == RolePhysType::NOT_PHYS)
	{
		return;
	}
	else
	{
		float h = (float)(m_BoundHeight - m_BoundSize);
		float r = (float)(m_BoundSize / 2 - 2);
		if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER  && m_RoleController)
		{
			float r = m_RoleController->GetRadius();
			float h = m_RoleController->GetHalfHeight();
		}
		
		if (r < 2 || h < 2)
		{
			if (checkIntervalLimited())
				return;
			PB_HostCheckCheat pbHCC;
			pbHCC.set_checktype(CCT_Clip);
			auto* pbSub = pbHCC.mutable_clip();
			pbSub->set_radius(r);
			pbSub->set_half_height(h);
			GameNetManager::getInstance()->sendToHost(PB_CHEAT_CHECK_CH, pbHCC);
			return;
		}
	}
#endif
}

void sendNoClip(int ret, PlayerControl *player){
	static int s_count = 101;
	// 减少上报次数
	if (++s_count > 100)
	{
		s_count = 0;
		if (player)
		{
			jsonxx::Object log;
			log << "client_clip_type" << ret;
			player->SendActionLog2Host(false, "cheat_client_clip", log.json_nospace());
		}
	}
}
// 返回true，说明客户端作弊，尝试穿墙
bool PlayerLocoMotion::checkCheatClip()
{
	PlayerControl *player = dynamic_cast<PlayerControl*>(getOwnerActor());
	if (!player)
		return false;
	// 1. 检测clip属性
	if (UseNoClipMove())
	{
		sendNoClip(1, player);
		return true;
	}

	return false;
}
//#define DEBUG_SECTION
int setMeshInvalid(const WCoord& min, const WCoord& max, WCoord* unionMin, WCoord* unionMax, World* pWorld) {
	int counter = 0;
	for (int i = min.x; i <= max.x; i++)
	{
		for (int j = min.y; j <= max.y; j++)
		{
			for (int k = min.z; k <= max.z; k++)
			{
				counter++;
				auto ptmpsection = pWorld->getSectionBySCoord(i, j, k);
				if (ptmpsection) {
					if (unionMin && unionMax)
					{
						if (i >= unionMin->x && i <= unionMax->x
							&& j >= unionMin->y && i <= unionMax->y
							&& k >= unionMin->z && k <= unionMax->z)
						{
							continue;
						}
					}
					ptmpsection->setMeshInvalid(true);
#ifdef DEBUG_SECTION
					if (ptmpsection->GetRenderSection()&& ptmpsection->GetRenderSection()->NeedUpdate())
					{
						LogStringMsg("setMeshInvalid:  %d,%d,%d", i, j, k);
					}
#endif
				}
			}
		}
	}
	return counter;
}



// David 2023/6/25 
//强绑定section边界刷新会有一种情况，如果玩家正好在边界进出，会导致大量的section update
//比如在section边界上跳跃
//优化策略是做一个section的update 计数器，当计数器大于阈值，才实际更新
//这个策略只给checkMeshInvalid使用，避免引起其他逻辑问题。
//本策略最糟糕情况下，当玩家移动非常快，可能会延时一些设置section 的 update标志
//目前了解的情况是checkMeshInvalid用于刷新玩家周围的地形的LOD（圆角或者直角），所以没有功能层面问题。
//有了地形LOD后，该方法应该移除
void PlayerLocoMotion::checkMeshInvalid(WCoord& positionPre, WCoord& positionnow)
{

	if ((g_pPlayerCtrl != getOwnerActor()) || (!m_pWorld))
	{
		return;
	}
	OPTICK_EVENT();
	WCoord preSectionPos = CoordDivSection(positionPre);
	WCoord nowSectionPos = CoordDivSection(positionnow);

	//记录最后一次section的位置
	static WCoord lastSectionPos = nowSectionPos;
	static int sFlag = 0;
	if (preSectionPos == nowSectionPos && sFlag == 0)
	{
		return;
	}

	auto deltaPos = nowSectionPos - lastSectionPos;
	//如果section位置变化是1以内的, 延迟10个tick处理,500ms
	if ((abs(deltaPos.x) <= 1 && abs(deltaPos.y) <= 1 && abs(deltaPos.z) <= 1))
	{
		sFlag++;
		if (sFlag < 10)
		{
 #ifdef DEBUG_SECTION
			LogStringMsg("===============TICK %d==================", sFlag);
#endif
			return;
		}
	}

	sFlag = 0;

// 	static VectorNoFree<WCoord> checkpre;
// 	checkpre.clear();
// 	static VectorNoFree<WCoord> checknew;
// 	checknew.clear();
// 
// 	if (preSectionPos != nowSectionPos)
// 	{
// #ifdef DEBUG_SECTION
// 		LogString("===============END==================");
// #endif
// 		for (int i = -2; i <= 2; i++)
// 		{
// 			for (int j = -2; j <= 2; j++)
// 			{
// 				for (int k = -2; k <= 2; k++)
// 				{
// 					checkpre.push_back(WCoord(i + preSectionPos.x, j + preSectionPos.y, k + preSectionPos.z));
// 					checknew.push_back(WCoord(i + nowSectionPos.x, j + nowSectionPos.y, k + nowSectionPos.z));
// 				}
// 			}
// 		}
// 
// 		int loopcounter = 0;
// 		for (int i = 0; i < (int)checkpre.size(); i++)
// 		{
// 			int t = 0;
// 			for (; t < (int)checknew.size(); t++)
// 			{
// 				loopcounter++;
// 				if (checkpre[i] == checknew[t])
// 				{
// 					break;
// 				}
// 			}
// 			if (t == checknew.size())
// 			{
// 				auto ptmpsection = m_pWorld->getSectionBySCoord(checkpre[i].x, checkpre[i].y, checkpre[i].z);
// 				if (ptmpsection)
// 				{
// 					ptmpsection->setMeshInvalid(true);z
// #ifdef DEBUG_SECTION
// 					LogStringMsg("setpre mesh invalid:%d,%d,%d", checkpre[i].x, checkpre[i].y, checkpre[i].z);
// #endif
// 				}
// 			}
// 		}
// #ifdef DEBUG_SECTION
// 		LogStringMsg("setpre loop %d", loopcounter);
// #endif
// 		loopcounter = 0;
// 		for (int i = 0; i < checknew.size(); i++)
// 		{
// 			int t = 0;
// 			for (; t < (int)checkpre.size(); t++)
// 			{
// 				loopcounter++;
// 				if (checknew[i] == checkpre[t])
// 				{
// 					break;
// 				}
// 			}
// 			if (t == checkpre.size())
// 			{
// 				auto ptmpsection = m_pWorld->getSectionBySCoord(checknew[i].x, checknew[i].y, checknew[i].z);
// 				if (ptmpsection)
// 				{
// 					ptmpsection->setMeshInvalid(true);
// #ifdef DEBUG_SECTION
// 					LogStringMsg("setnew mesh invalid:%d,%d,%d", checknew[i].x, checknew[i].y, checknew[i].z);
// #endif
// 				}
// 			}
// 		}
// #ifdef DEBUG_SECTION
// 		LogStringMsg("setnew loop %d", loopcounter);
// 		LogString("===============END==================");
// #endif
// 	}
// 
// 
// 优化：
// 原有算法计算一个5x5x5 = 125 的section范围，然后对新旧范围的重叠区域做计算
// 算法是简单的数组去重，单个处理最坏需要循环125*125 > 10000次，2次去重需要2万次循环。
// 实际手机测试情况下，角色简单前进，每个tick需要需要处理 16000次左右的循环，耗时0.15ms
// 
// 优化后，用section的边界做相交计算。然后对2个section边界索引直接做循环，过滤掉中间相交部分
// 最后循环次数为每个section 125次 * 2， 共250次循环。实际测试耗时0.03ms。
// 减少循环次数后，效率提升500%

#ifdef DEBUG_SECTION
		LogString("-----------start---------");
#endif
		int loopcounter = 0;
		auto minPreSection = preSectionPos - 2;
		auto maxPreSection = preSectionPos + 2;
		auto minNewSection = nowSectionPos - 2;
		auto maxNewSection = nowSectionPos + 2;

		auto minOverlapped = Max(minPreSection, minNewSection);
		auto maxOverlapped = Min(maxPreSection, maxNewSection);
#ifdef DEBUG_SECTION
 		LogStringMsg("pre section range %s - %s", minPreSection.__tostring().c_str(), maxPreSection.__tostring().c_str());
 		LogStringMsg("new section range %s - %s", minNewSection.__tostring().c_str(), maxNewSection.__tostring().c_str());
 		LogStringMsg("overlapped section range %s - %s", minOverlapped.__tostring().c_str(),
			maxOverlapped.__tostring().c_str());
#endif
		//将差异找出并设置
		if (minOverlapped.x >= maxOverlapped.x || minOverlapped.y >= maxOverlapped.y || minOverlapped.z >= maxOverlapped.z) {
			// 两个立方体没有交集，全部要设置
			loopcounter += setMeshInvalid(minPreSection, maxPreSection, nullptr, nullptr, m_pWorld);
			loopcounter += setMeshInvalid(minNewSection, maxNewSection, nullptr, nullptr, m_pWorld);
		}
		else {
			loopcounter += setMeshInvalid(minPreSection, maxPreSection, &minOverlapped, &maxOverlapped, m_pWorld);
			loopcounter += setMeshInvalid(minNewSection, maxNewSection, &minOverlapped, &maxOverlapped, m_pWorld);
		}
		OPTICK_TAG("loop", loopcounter);
#ifdef DEBUG_SECTION
		LogStringMsg("setMeshInvalid loop %d", loopcounter);
		LogString("--------------END---------");
#endif
	
}

bool PlayerLocoMotion::AddRealRigidSpecialMove(Vector3f mov)
{
#ifdef USE_ACTOR_CONTROL
	if (m_PhysActor && (mov.x != 0 || mov.y != 0 || mov.z != 0) && !(IsNAN(mov.x) || IsNAN(mov.y) || IsNAN(mov.z)))
	{
		ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
		m_PhysActor->AddForce(mov);

		if (g_WorldMgr)
			g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
	}
	return true;
#else
	return false;
#endif
}

void PlayerLocoMotion::addRealMove(const CollideAABB &box, WCoord &realmov)
{
#ifdef USE_ACTOR_CONTROL
	if (((m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor) || (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER) && m_RoleController) /*&& hasCollisionBetweenPlayers()*/) //先把这个hasCollisionBetweenPlayers控制去掉，玩家家的碰撞开关不应该影响玩家移动逻辑；否则走到下面的逻辑不会更新玩家的物理位置
	{
		ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
		if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
		{
			if (player && (player->hasUIControl() || player->isMoveControlActive()))
			{
				if (!realmov.isZero())
				{
					if (m_OnGround && player && player->IsOnPlatform() && realmov.y < 0)
						realmov.y = 0;

					Rainbow::Vector3f motion((float)realmov.x, (float)realmov.y, (float)realmov.z);
					//m_PhysActor->SetLinearVelocity(motion * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.phys_static_friciton * MOTION2VELOCITY);
					m_PhysActor->SetLinearVelocity(motion * 3.2f * MOTION2VELOCITY);

					if (g_WorldMgr)
						g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
				}
			}
		}
		else
		{
			if (m_OnGround && player && player->IsOnPlatform() && realmov.y < 0)
				realmov.y = 0;

			bool xchange = false;
			bool ychange = false;
			bool zchange = false;

#ifdef DEDICATED_SERVER
			if (!m_RoleController || m_RoleController->GetGameObjectSafe() == nullptr)
				return;
#endif			
			
			Rainbow::Vector3f prePhyPos = m_RoleController->GetPosition();
			Rainbow::Vector3f motion((float)realmov.x, (float)realmov.y, (float)realmov.z);
			m_RoleController->Move(motion, 0, false);
			Rainbow::Vector3f nowPhyPos = m_RoleController->GetPosition();

			if (abs(nowPhyPos.x - prePhyPos.x - float(realmov.x)) >= 1)
			{
				xchange = true;
			}
			if (abs(nowPhyPos.y - prePhyPos.y - float(realmov.y)) >= 1)
			{
				ychange = true;
			}
			if (abs(nowPhyPos.z - prePhyPos.z - float(realmov.z)) >= 1)
			{
				zchange = true;
			}

			m_OldPosition = m_Position;
			WCoord positionPre = m_Position;
			//初始化物理胶囊体是y为m_BoundHeight中间，所以每次计算时不需要偏移值
			m_Position = nowPhyPos - Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2)/* - 2.0f*/, 0.0f);
			WCoord realmovPre = realmov;
			if (!xchange)
			{
				m_Position.x = positionPre.x + realmov.x;
			}
			if (!ychange)
			{
				m_Position.y = positionPre.y + realmov.y;
			}
			if (!zchange)
			{
				m_Position.z = positionPre.z + realmov.z;
			}

			if (m_OnGround && realmov.y < 0 && ychange)	//保持角色控制器比m_Position高2个单位的距离
			{
				m_Position.y = ceil(nowPhyPos.y - (float)(m_BoundHeight / 2) + 2);
			}

			if (m_pWorld && m_pWorld->isRemoteMode())
			{
				realmov = m_Position - positionPre;
				if (player)
				{
					add_motion = realmovPre - realmov;
					if (player->getCurOperate() == PLAYEROP_NULL)
					{
						if ((add_motion.lengthSquared() * 9) < (45 * 45))
						{
							add_motion.x *= 3;
							add_motion.y *= 3;
							add_motion.z *= 3;
						}
					}
				}
			}
			else
			{
				realmov = m_Position - positionPre;
			}
			if (g_WorldMgr)
				g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
		}
	}
	else 
	{
		ActorLocoMotion::addRealMove(box, realmov);
	}
	//这里放到lodtick统一处理了
	//if (0 == BlockMaterialMgr::m_BlockShape)
	//{
	//	checkMeshInvalid(m_OldPosition, m_Position);
	//}
#else
	 ActorLocoMotion::addRealMove(box, realmov);
#endif
	if (GetOwnerPlayer() && GetOwnerPlayer()->hasUIControl())
		AntiSetting::logFlagChange(33, m_Position.x + m_Position.y + m_Position.z, "pl");
}

void PlayerLocoMotion::gotoPosition(const WCoord &pos, float yaw, float pitch)
{
	LivingLocoMotion::gotoPosition(pos, yaw, pitch);
#ifdef USE_ACTOR_CONTROL
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		Rainbow::Quaternionf rot = AngleEulerToQuaternionf(Vector3f(0, m_PrevRotateYaw, 0));
		m_PhysActor->SetPos(m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_BoundHeight / 2, 0.0f), rot);
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController)
	{
		m_RoleController->SetPosition(m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_BoundHeight / 2 - 2.0f, 0.0f));
	}

#endif
	//这里放到lodtick统一处理了
	//if (0 == BlockMaterialMgr::m_BlockShape)
	//{
	//	checkMeshInvalid(m_OldPosition, m_Position);
	//}
// 
	//如果ServerInterpolTick 正在插值移动，则取消；  否则会把pos跟上一次的serverpos插值，可能出现卡虚空
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		MNSandbox::SandboxContext context(this);
		context.SetData_Bool("cancel", true);
		getOwnerActor()->Event().Emit("moveToPosition", context);
	}
}

void PlayerLocoMotion::setPosition(int x, int y, int z)
{
	m_OldPosition = m_Position;
	m_Position.x = x;
	m_Position.y = y;
	m_Position.z = z;
#ifdef USE_ACTOR_CONTROL
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		Rainbow::Quaternionf rot = AngleEulerToQuaternionf(Vector3f(0, m_PrevRotateYaw, 0));
		m_PhysActor->SetPos(m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_BoundHeight / 2, 0.0f), rot);
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController)
	{
		Rainbow::Vector3f offset = Vector3f(0,0,0);
		Rainbow::Vector3f prePhyPos = m_RoleController->GetPosition();
		if (abs(prePhyPos.x - x) < 1)
			offset.x = prePhyPos.x - x;
		if (abs(prePhyPos.y - y) < 1)
			offset.y = prePhyPos.y - y;
		if (abs(prePhyPos.z - z) < 1)
			offset.z = prePhyPos.z - z;
		m_RoleController->SetPosition(m_Position.toVector3() + Rainbow::Vector3f(0.0f, m_BoundHeight / 2 - 2.0f, 0.0f) + offset);
	}
#endif
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	if (g_WorldMgr)
		g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
	//这里放到lodtick统一处理了
	//if (0 == BlockMaterialMgr::m_BlockShape)
	//{
	//	checkMeshInvalid(m_OldPosition, m_Position);
	//}
	if (GetOwnerPlayer() && GetOwnerPlayer()->hasUIControl())
		AntiSetting::logFlagChange(33, m_Position.x + m_Position.y + m_Position.z, "pl");
}

bool PlayerLocoMotion::findBottomBlock(int blockID, WCoord& blockpos, WORLD_ID &vehicleID)
{
	 if (ActorLocoMotion::findBottomBlock(blockID, blockpos, vehicleID))
		return true;
	WCoord pos(m_Position.x, m_Position.y+m_yOffset, m_Position.z);
	MINIW::WorldRay ray;
	pos.y += BLOCK_SIZE;
	ray.m_Origin = pos.toWorldPos();
	ray.m_Dir = Rainbow::Vector3f(0, -1, 0);
	ray.m_Range = 3*BLOCK_SIZE;
	ActorExcludes excludes;
	std::vector<IClientActor*> actors;
	actors = m_pWorld->pickAllVehicleActors(ray, excludes,0, false); 
	pos.y -= BLOCK_SIZE;
	int _x = 0, _y = 0, _z = 0; 
	bool on_ladder = false;
	ActorVehicleAssemble *vehicle_ = NULL;
	for (int i = 0; i < (int)actors.size(); i++)
	{
		ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(actors[i]);
		if (vehicle == NULL)
		{
			continue;
		}
		if (vehicle->inBlock(pos, blockID, _x, _y, _z))
		{
			blockpos = WCoord(_x, _y, _z);
			vehicleID = vehicle->getObjId();
			return true;
		}
		pos.y -= BLOCK_SIZE;
		if (vehicle->inBlock(pos, blockID, _x, _y, _z))
		{
			blockpos = WCoord(_x, _y, _z);
			vehicleID = vehicle->getObjId();
			return true;
		}
	}

	return false;
}

void PlayerLocoMotion::doPickThrough()
{
	if (m_pWorld->isRemoteMode()) return;

	WCoord mvec = getIntegerMotion(m_Motion);
	if (m_OnGround && mvec.y < 0)
		mvec.y = 0;
	if (mvec.length() < 100)
	{
		return;
	}
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	MINIW::WorldRay ray;
	ray.m_Origin = m_Position.toWorldPos();
	ray.m_Dir = mvec.toVector3();
	ray.m_Dir.y += 10;
	ray.m_Range = ray.m_Dir.Length();
	ray.m_Dir /= ray.m_Range;

	ActorExcludes excludes;
	excludes.addActorWithRiding(GetOwnerT<ClientPlayer>());

	IntersectResult presult;
	WorldPickResult intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	if (intertype == WorldPickResult::NOTHING && (player->getCurOperate() == PLAYEROP_TACKLE || player->getCurOperate() == PLAYEROP_BASKETBALL_GRAB))
	{
		ray.m_Dir.y += BLOCK_SIZE;
		intertype = m_pWorld->pickAll(ray, &presult, excludes, PICK_METHOD_CLICK);
	}
		
	if (intertype == WorldPickResult::ACTOR) //actor
	{
		ActorBall *ball = dynamic_cast<ActorBall *>(presult.actor);
		if (ball)
		{
			WCoord pos = (m_Position + mvec) / BLOCK_SIZE;
			if (ball->getPosition() != pos)
			{
				ball->onCollideWithPlayer(player);
			}
		}
	}
}

int PlayerLocoMotion::doMoveStep(const Rainbow::Vector3f &motion)
{
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	if (player->IsRunSandboxPlayer()) return 0;  //走新player 则老player 不在执行行走逻辑
	ActorBall *ball = dynamic_cast<ActorBall *>(player->getCatchBall());
	if (ball)
		ball->dribblingRotate(motion, player->getLocoMotion()->m_RotateYaw);

	ActorPushSnowBall* snowball = dynamic_cast<ActorPushSnowBall*>(player->getCatchBall());
	if (snowball)
	{
		snowball->dribblingRotate(motion, player->getLocoMotion()->m_RotateYaw);
	}

	if (!player->hasUIControl())
	{
		m_Motion = motion;
	}
	checkVortex();
	return ActorLocoMotion::doMoveStep(m_Motion);
}

void PlayerLocoMotion::attachPhysActor()
{
	if (!SandBoxCfg::GetInstance().IsPhysicalEffectEnable())
	{
		return;
	}
	if (!GetWorldManagerPtr() || GetWorldManagerPtr()->isNewSandboxNodeGame() == true)
	{
		//暂时先屏蔽掉老游戏的角色控制器 版本号:4961 codeby:chengjie
		return;
	}
#ifndef USE_ACTOR_CONTROL
	if (m_pWorld->isRemoteMode()) return;
#endif

	if (g_WorldMgr && g_WorldMgr->m_RuleMgr && m_pWorld)
	{
		//if (!m_pWorld->isRemoteMode())	// 这里客机需要取主机同步下来的规则， 不需要挡住客机
		{
			// 检查地图是否开启 玩家物理动态刚体
			float val = 0.f;
			int optid = 0;
			g_WorldMgr->m_RuleMgr->getRuleOptionID(GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE, optid, val);
			if (optid == 2)
			{
				// 2是动态刚体，其他的使用默认值
				m_CurPhysType = RolePhysType::PHYS_RIGIDBODY;
			}
			else {
				m_CurPhysType = RolePhysType::PHYS_ROLECONTROLLER;
			}
		}
	}

	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
	{
		if (!m_PhysActor)
		{
			if (!m_pWorld->m_PhysScene)
				return;

			Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2), 0.0f);
			Rainbow::Quaternionf rot = AngleEulerToQuaternionf(Vector3f(0, m_PrevRotateYaw, 0));

			float boundheight = (float)(m_BoundHeight - m_BoundSize) / 2/* + 3.0f*/;
			float boundsize = (float)(m_BoundSize / 2/* - 2*/);

			MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");
			m_PhysActor = m_pWorld->m_PhysScene->AddRigidDynamicActor(pos, rot, boundheight, boundsize, 0.0f, 0.0f, 0.0f, static_cast<ClientPlayer*>(GetOwnerPlayer())->getMass()/1000, false, nullptr, 0, 1, false);
			m_PhysActor->SetLinearDamping(10.0f);

			m_PhysActor->GetGameObject()->SetLayer(kLayerIndexCustom_Player);
			if (m_pWorld->getScene())
				m_pWorld->getScene()->AddGameObject(m_PhysActor->GetGameObject());

			auto pRigidbody = static_cast<Rainbow::Rigidbody*>(m_PhysActor->GetRigidbodyComponent());
			if (pRigidbody)
			{
				pRigidbody->SetSleepThreshold(0.0f);
				pRigidbody->SetMaxAngularVelocity(0.0f);
				pRigidbody->SetConstraints(kFreezeRotationX | kFreezeRotationZ);
				pRigidbody->ApplyConstraints();
			}

			auto collider = m_PhysActor->GetComponent<Collider>();
			if (collider)
			{
				collider->SetUserData(GetOwnerActor());
				auto mat = collider->GetMaterial();
				if(mat)
					mat->SetBounceCombine(1);
			}

			m_PhysActor->GetPos(m_RigidBodyFallData.oldPos, rot);
			m_RigidBodyFallData.onGround = false;
			//GlobalCallbacks::Get().fixedTimeTick.Register<PlayerLocoMotion>(&PlayerLocoMotion::OnFixedUpdate, this);

			GlobalCallbacks::Get().fixedTimeTick.Register<PlayerLocoMotion>(&PlayerLocoMotion::PhysicUpdateModel, this);
		}
	}
	else if(m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER)
	{
		if (m_RoleController == NULL)
		{
			// 优化 2个格山洞：修正值25
			Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2 - 2), 0.0f);
			if (m_pWorld->m_PhysScene == NULL)
			{
				return;
			}
			ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
			// 物理胶囊根据系数同步调整
			float boundheight = (float)(m_BoundHeight - m_BoundSize);
			float boundsize = (float)(m_BoundSize / 2 - 2);
			//把胶囊体高度设在m_BoundHeight中间位置
			pos.y = m_Position.toVector3().y + m_BoundHeight / 2/*  - 3*/;

			//LOG_INFO( "m_BoundHeight=[%d], m_BoundSize=[%d], boundheight=[%f], boundsize=[%f], [%d][%d][%d]", m_BoundHeight, m_BoundSize, boundheight, boundsize, pos.x, pos.y, pos.z );

			if (boundheight <= 0.0f || boundsize <= 0.0f)
				return;

			m_RoleController = m_pWorld->m_PhysScene->CreateRoleCapsuleController(boundheight, boundsize, pos, GetOwnerActor());
			if (m_pWorld->getScene())
				m_pWorld->getScene()->AddGameObject(m_RoleController->GetGameObject());

			updatePhysCollisionBetweenPlayers();

			MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");
			if (m_RoleController && GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig)
			{
				if (!m_pWorld->isRemoteMode())
				{
					m_RoleController->SetMassLimit(GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.mass_limit);
					if (GetOwnerActor())
					{
						//ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
						PlayerControl* player_control = GetOwnerT<PlayerControl>();
						// 客机
						if (player && ((player_control != g_pPlayerCtrl) || (player_control == NULL)))
						{
							m_RoleController->setIsClient(true);
						}
					}
				}
				else
				{
					m_RoleController->SetMassLimit(0);
				}
				m_RoleController->SetStepOffset(20);

				//m_RoleController->GetGameObject()->AddEvent(Rainbow::Evt_ControllerColliderHit, &PlayerLocoMotion::OnRoleControllerCollision, this);
			}
		}
	}
}


void PlayerLocoMotion::detachPhysActor()
{
#ifndef USE_ACTOR_CONTROL
	if (m_pWorld->isRemoteMode()) return;
#endif

	if(m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor)
	{
		//GlobalCallbacks::Get().fixedTimeTick.Unregister<PlayerLocoMotion>(&PlayerLocoMotion::OnFixedUpdate, this);
		m_pWorld->m_PhysScene->DeleteRigidActor(m_PhysActor);
		m_PhysActor = nullptr;
		GlobalCallbacks::Get().fixedTimeTick.Unregister<PlayerLocoMotion>(&PlayerLocoMotion::PhysicUpdateModel, this);
		//是动态刚体的话就设置成无物理的, 防止坐上载具, 模型没有同步
		m_CurPhysType = RolePhysType::NOT_PHYS;
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController)
	{
		//if (m_RoleController->GetGameObject())
		//{
		//	m_RoleController->GetGameObject()->RemoveEvent(Rainbow::Evt_ControllerColliderHit, &PlayerLocoMotion::OnRoleControllerCollision, this);
		//}
		m_pWorld->m_PhysScene->DeleteRoleController(m_RoleController);
		m_RoleController = NULL;
	}

	for (int i = 0; i < (int)m_preCheckPhy.size(); i++)
	{
		if (m_pWorld)
			m_pWorld->updateLeaveSectionPhysics(m_preCheckPhy[i].x, m_preCheckPhy[i].y, m_preCheckPhy[i].z);
	}
	m_preCheckPhy.clear();
}

void PlayerLocoMotion::OnRoleControllerCollision(const Rainbow::EventContent* collision)
{
	Rainbow::ControllerColliderHit* pCollision = reinterpret_cast<Rainbow::ControllerColliderHit*>(collision->userData);
	if (!pCollision || !pCollision->collider)
		return;

	ClientActor* pActor = dynamic_cast<ClientActor*>((ClientActor*)pCollision->collider->GetUserData());
	if (pActor && pActor->getPhysicsComponent())
	{
		pActor->getPhysicsComponent()->OnCollisionEvent(PhxEvent_CollidePlayer, getOwnerActor());
	}
}

void PlayerLocoMotion::OnFixedUpdate()
{
	bool isUgcEdit = m_pWorld->GetWorldMgr()->isUGCEditMode();
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY && m_PhysActor && !isUgcEdit)
	{
		Rainbow::Vector3f pos;
		Rainbow::Quaternionf rot;
		m_PhysActor->GetPos(pos, rot);
		if (IsNAN(pos.y))//偶现物理引擎返回nan
			return;

		m_OldPosition = m_Position;
		m_Position = pos - Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2), 0.0f);

		rot = AngleEulerToQuaternionf(Vector3f(0.0f, m_PrevRotateYaw, 0.0f));
		m_PhysActor->SetPos(pos, rot);

		if (std::abs(m_Motion.x) < kEpsilon && std::abs(m_Motion.z) < kEpsilon)
		{
			auto v = m_PhysActor->GetLinearVelocity();
			if (IsZeroVector(v))
				return;

			v.x = 0.0f;
			v.z = 0.0f;
			if (Abs(v.y) < 0.01f)
				v.y = 0;

			m_PhysActor->SetLinearVelocity(v);
			m_PhysActor->SetAngularVelocity(Vector3f::zero);
		}
	}
	else if (m_CurPhysType == RolePhysType::PHYS_ROLECONTROLLER && m_RoleController)
	{
#ifdef DEDICATED_SERVER
		if (m_RoleController->GetGameObjectSafe() == nullptr)
			return;
#endif
		ClientPlayer* player = static_cast<ClientPlayer*>(GetOwner());
		if (player->IsOnPlatform())
		{
			m_OldPosition = m_Position;
			m_Position = m_RoleController->GetPosition() - Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2 - 2), 0.0f);
		}
	}
}

void PlayerLocoMotion::PhysicUpdateModel()
{
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
	{
		ClientPlayer* pPlayer = static_cast<ClientPlayer*>(GetOwnerActor());
		auto pPlayerTransform = pPlayer->GetTransform();
		PlayerLocoMotion* pLoco = static_cast<PlayerLocoMotion*>(pPlayer->getLocoMotion());
		if (pPlayerTransform && pLoco)
		{
			pPlayer->getEntity()->SetPosition(pPlayerTransform->GetWorldPosition() - Rainbow::Vector3f(0.0f, pLoco->m_BoundHeight / 2.0f - (pLoco->getRigidDynamicActor() ? 2.0f : 0.0f), 0.0f));
		}
	}
}

void PlayerLocoMotion::attachPhysActorForRect(int forward, int side)
{
#ifndef USE_ACTOR_CONTROL
	if (m_pWorld->isRemoteMode()) return;
#endif
	if (m_RoleController == NULL)
	{
		// 优化 2个格山洞：修正值25
		Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2 - 30), 0.0f);
		if (m_pWorld->m_PhysScene == NULL)
		{
			return;
		}
		m_RoleController = m_pWorld->m_PhysScene->CreateRoleController( (float)(m_BoundHeight / 2), (float)forward, (float)side, pos, GetOwnerActor());
		if (m_pWorld->getScene())
			m_pWorld->getScene()->AddGameObject(m_RoleController->GetGameObject());
// 		m_RoleController = m_pWorld->m_PhysScene->CreateRoleCapsuleController(m_BoundHeight - 50, forward, pos, m_OwnerActor);
		MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");
		if (m_RoleController && GetWorldManagerPtr() && GetWorldManagerPtr()->m_SurviveGameConfig)
		{
			if (!m_pWorld->isRemoteMode())
			{
				m_RoleController->SetMassLimit(GetWorldManagerPtr()->m_SurviveGameConfig->physxconfig.mass_limit);
			}
			else
			{
				m_RoleController->SetMassLimit(0);
			}
		}
		//if (m_pWorld->isRemoteMode())
		//m_RoleController->getPxController()->getActor()->setRigidBodyFlag(PxRigidBodyFlag::eKINEMATIC, false);
	}
}

void PlayerLocoMotion::checkPhysWorld()
{
	if (m_CurPhysType == RolePhysType::PHYS_RIGIDBODY)
	{
		if (!m_pWorld || !m_PhysActor || !SandBoxCfg::GetInstance().IsPhysicalEffectEnable()) {
			if (m_pWorld && m_PhysActor && g_WorldMgr && g_WorldMgr->m_RuleMgr && m_pWorld)
			{
				// 检查地图是否开启 玩家物理动态刚体
				float val = 0.f;
				int optid = 0;
				g_WorldMgr->m_RuleMgr->getRuleOptionID(GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE, optid, val);
				if (optid == 2)
				{
					// 2是动态刚体，其他的使用默认值 
					// GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE 配置 和 SandBoxCfg::GetInstance().IsPhysicalEffectEnable 冲突了？ 日志记录下
					if (((int)Rainbow::GetTimeSec()) % 10 == 0) {
						LOG_WARNING("PlayerLocoMotion::checkPhysWorld SandBoxCfg::IsPhysicalEffectEnable AND GAMEMAKER_RULE::GMRULE_PLAYERPHYSTYPE Conflicted");
					}
				}
				else
				{
					return;
				}
			}
			else
			{
				return;
			}
		}
	}
	else
	{
		if (!m_pWorld || !m_RoleController || !SandBoxCfg::GetInstance().IsPhysicalEffectEnable())
			return;
	}
	
    OPTICK_CATEGORY(OPTICK_FUNC, Optick::Category::Physics);
	WCoord curpos = m_Position;
	WCoord range(SECTION_SIZE / 2, SECTION_SIZE / 2, SECTION_SIZE / 2);
	WCoord minpos = CoordDivSection(curpos - range);
	WCoord maxpos = CoordDivSection(curpos + range);

	static VectorNoFree<WCoord> checkphy;
	checkphy.clear();
	for (int y = minpos.y; y <= maxpos.y; y++)
	{
		for (int z = minpos.z; z <= maxpos.z; z++)
		{
			for (int x = minpos.x; x <= maxpos.x; x++)
			{
				//m_pWorld->updateSectionPhysics(x, y, z);
				checkphy.push_back(WCoord(x, y, z));
			}
		}
	}

	for (int i = 0; i < (int)m_preCheckPhy.size(); i++)
	{
		int j = 0;
		const WCoord& pos = m_preCheckPhy[i];
		for (; j < (int)checkphy.size(); j++)
		{
			if (pos == checkphy[j])
			{
				break;
			}
		}
		if (j == checkphy.size())
		{
			m_pWorld->updateLeaveSectionPhysics(pos.x, pos.y, pos.z);
		}
	}

	for (int i = 0; i < (int)checkphy.size(); i++)
	{
		int j = 0;
		const WCoord& pos = checkphy[i];
		for (; j < (int)m_preCheckPhy.size(); j++)
		{
			if (pos == m_preCheckPhy[j])
			{
				break;
			}
		}
		if (j == m_preCheckPhy.size())
		{
			m_pWorld->updateEnterSectionPhysics(pos.x, pos.y, pos.z);
		}
		m_pWorld->updateSectionPhysics(pos.x, pos.y, pos.z);
	}

	m_preCheckPhy.resize(checkphy.size());
	for (int i = 0; i < (int)checkphy.size(); i++)
	{
		m_preCheckPhy[i] = checkphy[i];
	}
}

void PlayerLocoMotion::OnSneakChange(bool sneak)
{
	if (m_RoleController)
	{
		if (sneak)
		{
			m_RoleController->SetCenterPos({ 0, -45, 0 });
			m_RoleController->SetHalfHeight(m_BoundHeight / 2 - m_BoundSize - 4);
		}
		else
		{
			m_RoleController->SetCenterPos({ 0, 0, 0 });
			m_RoleController->SetHalfHeight((m_BoundHeight - m_BoundSize) / 2 - 2);
		}
	}
}


void PlayerLocoMotion::moveEntityWithHeading(float strafing, float forward)
{
	GetIWorldConfigProxy()->VmpBeginUltra("player_loco_move");
	WCoord oldpos = m_Position;
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	auto RidComp = getOwnerActor()->getRiddenComponent();

	// 主机上的客机对象且开启了新版位移同步需要调用 LivingLocoMotion::moveEntityWithHeading 2023.11.15 by huanglin
	if(!player->isMoveControlActive() && (!player->hasUIControl() || GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false)))
	{
		if (!player->isMotionChange())
		{
			m_Motion *= 0.6f;
		}
		if (isOnLadder()) 
		{
			m_tickLadder = (m_tickLadder == kLadderTick) ? kOnLadder : kLadderNone;
			auto functionWrapper = player->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0.0f);
			}
		} else {
			m_tickLadder = (m_tickLadder == kLadderTick) ? kNotOnLadder : kLadderNone;
		}
	}
	else if(getOwnerActor()->getFlying() && !(RidComp && RidComp->isRiding()))
	{
		float my = m_Motion.y;
		float oldfactor = m_JumpMovementFactor;

		m_JumpMovementFactor = getOwnerActor()->getAttrib()->getFlySpeed();
		LivingLocoMotion::moveEntityWithHeading(strafing, forward);

		if (g_pPlayerCtrl && g_pPlayerCtrl->m_InputInfo->flyUp == -1) {
			float fallSpeedVal2 = 0;
			float fallSpeedVal1 = player->getLivingAttrib()->getEquipEnchantValue(EQUIP_LEGGING, ENCHANT_FALL_SPEED, ATTACK_ALL, ATTACK_TARGET_ALL, &fallSpeedVal2);
			if (fallSpeedVal1 <= 0.0f) {
				m_Motion.y = my * 0.6f;
				m_JumpMovementFactor = oldfactor;
			}
		}
		else {
			m_Motion.y = my * 0.6f;
			m_JumpMovementFactor = oldfactor;
		}
	}
	else if(!(RidComp && RidComp->isRiding()))
	{
		LivingLocoMotion::moveEntityWithHeading(strafing, forward);
	}
	WCoord dp = m_Position - oldpos;
	player->addMoveStats(dp);

	if (g_pPlayerCtrl == player && dp != WCoord::zero)
	{
		WCoord posForTransfer;
		WORLD_ID vehicleID = 0;
		auto pTransferMgr = TransferMgr::GetInstancePtr();
		if (findBottomBlock(BLOCK_TRANSFERCORE, posForTransfer, vehicleID))
		{
			ActorVehicleAssemble* vehicle = NULL;
			if (vehicleID)
			{
				ClientActor* actor_ = static_cast<ActorManager*>(player->getWorld()->getActorMgr())->findActorByWID(vehicleID);
				if (actor_)
				{
					vehicle = dynamic_cast<ActorVehicleAssemble*>(actor_);
				}
			}

			//SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_openTransferUI",
			//	SandboxContext(nullptr)
			//	.SetData_Bool("b", true)
			//	.SetData_Number("curMapID", m_pWorld->getCurMapID())
			//	.SetData_UserObject("posForTransfer", posForTransfer)
			//	.SetData_UserObject("vehicle", vehicle));

			if (pTransferMgr)
			{
				pTransferMgr->openTransferUI(true, m_pWorld->getCurMapID(), posForTransfer, vehicle);
			}
		}
		else
		{
			//SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_openTransferUI",
			//	SandboxContext(nullptr)
			//	.SetData_Bool("b", false)
			//	.SetData_Number("curMapID", m_pWorld->getCurMapID())
			//	.SetData_UserObject("posForTransfer", posForTransfer)
			//	.SetData_UserObject("vehicle", NULL));

			if (pTransferMgr)
			{
				pTransferMgr->openTransferUI(false, m_pWorld->getCurMapID(), posForTransfer, nullptr);
			}
		}
	}

	GetIWorldConfigProxy()->VmpEnd();
}

void PlayerLocoMotion::moveFlying(float strafing, float forward, float speed)
{
	ClientPlayer *player = getOwnerActor()->ToCast<ClientPlayer>();

	PlayerControl* control = dynamic_cast<PlayerControl*>(player);
	if (player && (player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL) && player->getCatchBall())
	{
		if (forward < 0)
			forward = 0;

		float r = strafing * strafing + forward * forward;
		if (r <= 0.0001f)
		{
			return;
		}

		r = Sqrt(r);
		if (r < 1.0f) r = 1.0f;
		r = speed / r;
		// strafing *= r;
		forward *= r;

		Vector3f fdir = Yaw2FowardDir(m_RotateYaw);
		//Vector3f sdir = Yaw2StrafingDir(m_RotateYaw);


		//if (m_NavigationRotateYaw >= 0)
		//	fdir = Yaw2FowardDir(m_NavigationRotateYaw);

		//if (m_NavigationRotationPitch >= 0)
		//	sdir = Yaw2StrafingDir(m_NavigationRotationPitch);

		m_Motion.x += forward * fdir.x;
		m_Motion.z += forward * fdir.z;
	}
	else if (control && control->getViewMode() == CAMERA_TPS_OVERLOOK)
	{
		float r = strafing*strafing + forward*forward;
		if(r <= 0.0001f)
		{
			return;
		}

		r = Sqrt(r);
		if(r < 1.0f) r = 1.0f;
		r = speed/r;
		strafing *= r;
		forward *= r;

		Rainbow::Vector3f fdir(0.0f, 0.0f, 1.0f);
		Rainbow::Vector3f sdir(1.0f, 0.0f, 0.0f);

		m_Motion.x += forward*fdir.x + strafing*sdir.x;
		m_Motion.z += forward*fdir.z + strafing*sdir.z;
	}
	else if (control && control->getViewMode() == CAMERA_CUSTOM_VIEW
		&& control->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_TOPVIEW)
	{
		Rainbow::Vector3f fdir, sdir;
		Quaternionf quat = control->m_pCamera->getEngineCamera()->GetWorldRotation();
		quat.Inverse();
		
		PitchYaw2Direction(fdir, Rainbow::QuaternionToEulerAngle(quat).y + 180.0f, 0.0f);
		PitchYaw2Direction(sdir, Rainbow::QuaternionToEulerAngle(quat).y + 270.0f, 0.0f);

		float r = strafing*strafing + forward*forward;
		if (r <= 0.0001f)
		{
			return;
		}

		r = Sqrt(r);
		if (r < 1.0f) r = 1.0f;
		r = speed / r;
		strafing *= r;
		forward *= r;

		m_Motion.x += forward*fdir.x + strafing*sdir.x;
		m_Motion.z += forward*fdir.z + strafing*sdir.z;
	}
	else LivingLocoMotion::moveFlying(strafing, forward, speed);
}

bool PlayerLocoMotion::prepareJump(int &cooldown)
{
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	if (m_OnGround)
	{
		cooldown = 10; //5
		clearAirJump();

		if (player->getSkinID() == 440) 
		{
			if (m_FrontBlockLeftIsEmpty) 
			{ 
				player->playAnim(SEQ_SKATEBOARD_JUMP2BLOCK2); 
			}
			else if (m_FrontBlockRightIsEmpty) 
			{ 
				player->playAnim(SEQ_SKATEBOARD_JUMP2BLOCK); 
			}
			else
			{
				int randInt = GenRandomInt(6);

				if (randInt == 3)
				{
					player->playAnim(SEQ_SKATEBOARD_SHOW1);
				}
				else if (randInt == 4)
				{
					player->playAnim(SEQ_SKATEBOARD_SHOW2);
				}
				else if (randInt == 5)
				{
					player->playAnim(SEQ_SKATEBOARD_SHOW3);
				}
				else 
					player->playAnim(SEQ_JUMP);
			}
		}
		else 
			player->playAnim(SEQ_JUMP);

	}
	else
	{
		float extvalues[4];
		player->getGeniusValue(GENIUS_TWOJUMP, extvalues);
		if (int(extvalues[0]) != 1)
		{
			return false;
		}
		if (isAirJump())
		{
			return false;
		}

		auto CarryComp = player->getCarryComponent();
		if (CarryComp && CarryComp->isCarrying())
		{
			//扛起生物时，不可二段跳
			return false;
		}
		if (GetWorldManagerPtr() != NULL)
		{
			if (!GetWorldManagerPtr()->getPlayerPermit(ENABLE_JUMPTWICE, player->getTeam()))
			{
				//玩法设置不可二段跳
				return false; 
			}
		}
		PlayerAttrib* playerAttrib = player->getPlayerAttrib();
		if (!playerAttrib->isStrengthEnough(GetLuaInterfaceProxy().get_lua_const()->strength_consumption_of_double_jump))
		{
			GetLuaInterfaceProxy().showGameTips(1571);
			return false;
		}
		cooldown = 10;
		increaseAirJump();

		player->playAnim(SEQ_JUMPTWO);
		
		auto effectComponent = getOwnerActor()->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect(BODYFX_ROLEJUMP);
		}
	}
	
	// 状态
	player->setJumpState(getAriJumpCount());
	checkEffectJumpTriggerMotion(getAriJumpCount());

	return true;
}

void PlayerLocoMotion::clearAirJump()
{
	memset(m_AirJumpCount, 0x00, sizeof(m_AirJumpCount));
}

/**
 * @brief 获取跳跃高度
 * 
 * @return float 跳跃高度
 */
float PlayerLocoMotion::getJumpHeight(){
	const float default_base = 40.0;
	float base = default_base;
	auto owner = getOwnerActor();
	if (owner && owner->getAttrib()) {
		base = owner->getAttrib()->getSpeedAtt(Actor_Jump_Speed);
		if (base < 0.0f) { base = default_base; }
	}

	ClientPlayer *player = dynamic_cast<ClientPlayer*>(owner);
	if (player) {
		base *= (1.0f + player->getGeniusValue(GENIUS_TWOJUMP));
#if 0
		if (player->getPlayerAttrib()->isNewStatus()) {
			base += player->getPlayerAttrib()->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_JUMP_SPEED, base);
		}
		else {
			base *= (1.0f + player->getPlayerAttrib()->getModAttrib(MODATTR_JUMP_SPEED));
		}
#else
		base += player->getPlayerAttrib()->getActorAttValueWithStatus(BuffAttrType::BUFFATTRT_JUMP_SPEED, base);
#endif
		base+=player->getLivingAttrib()->getEnchantJumpHeightInc();//renjie 增加跳跃能力附魔
	}
	return base;
}
void PlayerLocoMotion::doJump()
{
	float height = getJumpHeight();
	m_Motion.y = height;
	//m_Motion.y = base * (1.0f + player->getGeniusValue(GENIUS_TWOJUMP)) * (1.0f + player->getPlayerAttrib()->getModAttrib(MODATTR_JUMP_SPEED));
	//SetLinearVelocity(m_Motion * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.phys_Angular_damping * MOTION2VELOCITY);
	SetLinearVelocity(m_Motion * 2.6f * MOTION2VELOCITY);
	
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(getOwnerActor());
	if (player && GetWorldManagerPtr() && GetWorldManagerPtr()->isRemote()){
		PB_HostCheckCheat pbHCC;
		pbHCC.set_checktype(CCT_JumpHeight);
		PB_CheatCheatSubJump* pbSubJump = pbHCC.mutable_jump();
		pbSubJump->set_jumpheight(height);
		pbSubJump->set_onground((bool)m_OnGround);
		pbSubJump->set_airjump(isAirJump());
		GameNetManager::getInstance()->sendToHost(PB_CHEAT_CHECK_CH, pbHCC);
	}

	if (player)
	{
		player->addAchievement(3, ACHIEVEMENT_JUMPCOUNT);
		player->getPlayerAttrib()->useStamina(STAMINA_JUMP);
		player->setNeedSyncPosition();
	}
}

void PlayerLocoMotion::checkEffectTriggerMotion(BuffAttrType type)
{
	std::vector<StatusAttInfo> vValue;
	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	player->getPlayerAttrib()->getStatusAddAttInfo(type, vValue);
	for (auto p = vValue.begin(); p != vValue.end(); p++)
	{
		if (p->vValue.size() < 2)
		{
			continue;
		}
		int param2UiType = p->vValue[0].iType;
		int param3UiType = p->vValue[1].iType;
		if (param2UiType != 2)
		{
			continue;
		}
		int attackEnumID = p->vValue[1].value;
		const BuffEffectEnumDef* def = GetDefManagerProxy()->getBuffEffectEnumDef(attackEnumID);
		if (nullptr == def)
		{
			continue;
		}
		int attackType = def->AttType - BuffAttrType::BUFFATTRT_MELEE_HURT;
		if (def->AttType == BuffAttrType::BUFFATTRT_FIXED_HURT)
		{
			attackType = ATTACK_TYPE::ATTACK_FIXED;
		}
		if (def->AttType == BuffAttrType::BUFFATTRT_TRUE_DAMAGE)
		{
			attackType = ATTACK_TYPE::TRUE_DAMAGE;

		}
		if (def->AttType == BuffAttrType::BUFFATTRT_ICE_PROPERTY) {
			attackType = ATTACK_TYPE::ATTACK_ICE;
		}
		int damage = p->vValue[0].value;
		//m_Player->attackedFromType((ATTACK_TYPE)attackType, damage);
		auto component = player->getAttackedComponent();
		if (component)
		{
			component->attackedFromType_Base((ATTACK_TYPE)attackType, damage);
		}
	}
}

void PlayerLocoMotion::checkEffectJumpTriggerMotion(int jumpCount)
{
	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	//应该只有0,1值
	if (jumpCount == 0)
	{
		//普通起跳
		if (player->getRun())
		{
			triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN_JUMP);
		}
	}
	else if (jumpCount == 1)
	{
		//二段跳
		triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN_SECOND_JUMP);
	}
	else
	{
		//普通跳
	}
}

void PlayerLocoMotion::checkOtherEffectTriggerMotion()
{
	if (m_isJumping)
	{
		return;
	}
	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	if (player->getRun())
	{
		//triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_RUN);
	}
	else if (isInLiquid())
	{
		/*if (m_Motion.x > EPSILON || fabs(m_Motion.z) > EPSILON || fabs(m_Motion.y) > EPSILON)
		{
			triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_SWIM);
		}*/
	}
	else
	{
		const int op = player->getCurOperate();
		//LOG_INFO("isChargingItem(): op = %d", op);
		switch (op)
		{
		case PLAYEROP_DIG:
		{
			triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_DIG);
		}
		return;
		case PLAYEROP_ATTACK_BOW:
		case PLAYEROP_USE_ITEM_SKILL:
		case PLAYEROP_GRAVITY_CHARGE_BEGIN:
			break;
		default:
			return;
		}
		const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(player->getCurToolID());
		if (!tooldef)
		{
			return;
		}
		if (tooldef->AccumulatorType == 0 || tooldef->AccumulatorType == 1)
		{
			triggerEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_CHARING);
		}
	}
}

void PlayerLocoMotion::triggerEffectAttackMotion()
{
	checkEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_ATTACK);
	checkEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_ALL);
}

void PlayerLocoMotion::triggerEffectTriggerMotion(BuffAttrType type)
{
	checkEffectTriggerMotion(type);
	checkEffectTriggerMotion(BUFFATTRT_PLAYER_STRENUOUS_EXERCISE_ALL);
}

float PlayerLocoMotion::getGravityFactor(bool up)
{
	m_IsEnchFall = false;
	ClientPlayer *player = static_cast<ClientPlayer *>(getOwnerActor());
	if(player->getLivingAttrib()->hasStatusEffect(STATUS_EFFECT_BUBBLE))
	{
		if(up) return 0.05f;
		else return -0.5f;
	}

	if(player->getLivingAttrib()->hasBuff(FREEZING_BUFF))
	{
		if(up) return 0.05f;
		else return -0.5f;
	}

	float factor = 1.0f;
	if(!up)
	{
		if(player->getGeniusType() == GENIUS_TWOJUMP) factor = 0.5f;

		float fallProtectVal2 = 0;
		float fallProtectVal1 = player->getLivingAttrib()->getEquipEnchantValue(EQUIP_PIFENG, ENCHANT_FALL_PROTECT,ATTACK_ALL,ATTACK_TARGET_ALL,&fallProtectVal2);

		if(fallProtectVal1 > 0)
		{
			factor *= fallProtectVal2;
		}

		float fallSpeedVal2 = 0;
		float fallSpeedVal1 = player->getLivingAttrib()->getEquipEnchantValue(EQUIP_LEGGING, ENCHANT_FALL_SPEED, ATTACK_ALL,ATTACK_TARGET_ALL,&fallSpeedVal2);
		if(fallSpeedVal1 > 0 && g_pPlayerCtrl && g_pPlayerCtrl->m_InputInfo->flyUp == -1)
		{
			factor *= (1 + fallSpeedVal1);
			m_IsEnchFall = true;
		}
	}

	if (player->getCurDorsumID() == ITEM_FIRE_ROCKET)
	{
		factor *= 0.025f;
	}
	if (player->getCurDorsumID() == ITEM_SNAKEGOD_WING)
	{
		ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
		if (player)
		{
			if (player->isNewMoveSyncSwitchOn()  // 客机运行, 客机玩家开启新版移动同步
			|| player->isMoveControlActive()  // 主机运行, 客机玩家开启新版移动同步
			)
			{
				if (player->m_SnakeGodWingState.wingState > 0)
					factor *= 0.1f;
				else
					factor *= 0.5f;
			}
			else
			{
				if (g_pPlayerCtrl && g_pPlayerCtrl->m_InputInfo->wingState > 0)
					factor *= 0.1f;
				else
					factor *= 0.5f;
			}
		}
	}
	return factor;
}
MINIW::RoleController* PlayerLocoMotion::getRoleController()
{
	if(m_RoleController == NULL) return NULL;
	return m_RoleController;
}

Rainbow::RigidDynamicActor* PlayerLocoMotion::getRigidDynamicActor()
{
	if (m_PhysActor == NULL) return NULL;
	return m_PhysActor;
}

RolePhysType PlayerLocoMotion::getPhysType()
{
	return m_CurPhysType;
}

void PlayerLocoMotion::switchPhysType(RolePhysType type)
{
	detachPhysActor();
	m_CurPhysType = type;
	attachPhysActor();
}

namespace AntiSetting {
	bool IsKickOn(unsigned value);
	bool IsSwitchOff(unsigned value);
}

void PlayerLocoMotion::setBound(int height, int width)
{
	LivingLocoMotion::setBound(height, width);
	// 对bound size 和 boud height 做一定运算, 后续使用checkBoud校验, 防止size和height被篡改内存
	m_BoundSizeProtect = (m_BoundSize + 8431) ^ cheatCheckRandom;
	m_BoundHeightProtect = (m_BoundHeight + 8431) ^ cheatCheckRandom;
}

void PlayerLocoMotion::prepareTick()
{
	ActorLocoMotion::prepareTick();
	ClientPlayer* pPlayer = static_cast<ClientPlayer*>(GetOwnerActor());
	auto pPlayerTransform = pPlayer->GetTransform();

	if (pPlayerTransform && !pPlayer->IsOnPlatform())
		m_OldTransPos.y = -1;
}

void PlayerLocoMotion::RecordOldTransPosOnPlatform()
{
	ClientPlayer* pPlayer = static_cast<ClientPlayer*>(GetOwnerActor());
	auto pPlayerTransform = pPlayer->GetTransform();
	m_OldTransPos = pPlayerTransform->GetWorldPosition();
	m_TickOffsetTime = 0;
}

void PlayerLocoMotion::LerpModelOnPlatform(float dtime)
{
	ClientPlayer* pPlayer = static_cast<ClientPlayer*>(GetOwnerActor());
	if (pPlayer && m_OldTransPos.y >= 0 && pPlayer->getEntity())
	{
		auto curTranform = pPlayer->GetTransform();
		if (curTranform)
		{
			Vector3f pos = Vector3f::zero;
			if (dtime == -1 || m_TickOffsetTime + dtime > GAME_TICK_TIME)
			{
				pos = curTranform->GetWorldPosition();
				m_TickOffsetTime = GAME_TICK_TIME;
			}
			else
			{
				m_TickOffsetTime += dtime;
				pos = Lerp(m_OldTransPos, curTranform->GetWorldPosition(), m_TickOffsetTime / GAME_TICK_TIME);
			}

			pos.y -= pPlayer->getLocoMotion()->m_BoundHeight / 2;
			pPlayer->getEntity()->SetPosition(pos);

			if (pPlayer->getBody())
			{
				unsigned int dtick = TimeToTick(dtime);
				pPlayer->getBody()->updateForOverHead(dtick, pos);
			}
				

			if (dtime == -1)
			{
				m_TickOffsetTime = 0;
			}
		}
	}
}

bool PlayerLocoMotion::checkBound()
{
	if (m_BoundSizeProtect == 0)
		return true;
	return ((m_BoundSizeProtect ^ cheatCheckRandom) - 8431 == m_BoundSize)
		&& ((m_BoundHeightProtect ^ cheatCheckRandom) - 8431 == m_BoundHeight);
}

void PlayerLocoMotion::setInWater(bool inwater)
{
	LivingLocoMotion::setInWater(inwater);

	if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchInWater))
	{
		ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
		if (player && player->hasUIControl())
		{
			m_InWaterProtect = Timer::getTimeUS() & 0xffff;
			if (m_InWater)
				m_InWaterProtect |= 0x0002;
			else
				m_InWaterProtect &= ~0x0002;
		}
	}
}
bool PlayerLocoMotion::handleWaterMovement()
{
	if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchInWater))
	{
		ClientPlayer* player = getOwnerActor()->ToCast<ClientPlayer>();
		if (player && player->hasUIControl())
		{
			if (m_InWaterProtect) {
				bool protected_in_water = m_InWaterProtect & 0x0002;
				if (protected_in_water != m_InWater) {
					jsonxx::Object log;
					log << "client_in_water" << m_InWater;
					log << "client_tmp_in_water" << protected_in_water;
					g_pPlayerCtrl->SendActionLog2Host(AntiSetting::IsKickOn(AntiSetting::gCheatConfig.SwitchInWater), "cheat_client_inwater", log.json_nospace());
				}

			}
		}
	}
	return LivingLocoMotion::handleWaterMovement();
}

bool PlayerLocoMotion::isOnVehicle()
{
	if (getVehicle())
	{
		return true;
	}
	return false;
}

ActorVehicleAssemble* PlayerLocoMotion::getVehicle()
{
	//if (m_RoleController && m_RoleController->getPxController())
	//{
	//	PxControllerState state;
	//	m_RoleController->getPxController()->getState(state);
	//	if (state.touchedShape && state.touchedShape->userData)
	//	{
	//		ClientActor* actor = static_cast<ClientActor*>(state.touchedShape->userData);
	//		auto assemble = dynamic_cast<ActorVehicleAssemble*>(actor);
	//		return assemble;
	//	}
	//}
	return NULL;
}

WCoord PlayerLocoMotion::getGlabalPos()
{
	//if (m_RoleController && m_RoleController->getPxController())
	//{
	//	return WCoord(m_RoleController->GetPosition().x, m_RoleController->GetPosition().y - m_BoundHeight / 2, m_RoleController->GetPosition().z);
	//}
	return getPosition();
}

WCoord PlayerLocoMotion::getRelativeVehiclePos()
{
	//if (m_RoleController && m_RoleController->getPxController())
	//{
	//	PxControllerState state;
	//	m_RoleController->getPxController()->getState(state);
	//	if (state.touchedShape)
	//	{
	//		auto actor = state.touchedShape->getActor();
	//		if (actor)
	//		{
	//			auto transform = actor->getGlobalPose();
	//			PxVec3 pos(m_RoleController->GetPosition().x, m_RoleController->GetPosition().y - m_BoundHeight / 2, m_RoleController->GetPosition().z);
	//			PxVec3 post = transform.transformInv(pos);
	//			return WCoord(post.x, post.y, post.z);
	//		}
	//	}
	//}
	return WCoord();
}

void PlayerLocoMotion::refreshPos()
{
	/*MINIW::Vector3 motion((float)0, (float)-1, (float)0);
	m_RoleController->Move(motion, 0, false);*/
}

WCoord PlayerLocoMotion::getGlobalVehiclePos(WCoord& pos)
{
	//if (m_RoleController && m_RoleController->getPxController())
	//{
	//	PxControllerState state;
	//	m_RoleController->getPxController()->getState(state);
	//	if (state.touchedShape)
	//	{
	//		auto actor = state.touchedShape->getActor();
	//		if (actor)
	//		{
	//			auto transform = actor->getGlobalPose();
	//			PxVec3 pos_(pos.x, pos.y, pos.z);
	//			PxVec3 post = transform.transform(pos_);
	//			return WCoord(post.x, post.y, post.z);
	//		}
	//	}
	//}
	return WCoord();
}

void PlayerLocoMotion::updateTick()
{
	if (m_RoleController)
	{
		checkPhysWorld();
	}
	m_tickLadder = kLadderTick;
	ActorLocoMotion::updateTick();

	//updateInWaterState();

	ClientPlayer* player = static_cast<ClientPlayer*>(getOwnerActor());
	if (player)
	{
		if (m_OldBlockPos.y == -10000)
			m_OldBlockPos = m_Position / BLOCK_SIZE;

		m_CheckPosCD++;
		if (m_CheckPosCD >= 10)
		{
			m_CheckPosCD = 0;

			WCoord posBlock = m_Position / BLOCK_SIZE;
			int moveBlockNum = Rainbow::Abs(posBlock.x - m_OldBlockPos.x);
			moveBlockNum += Rainbow::Abs(posBlock.y - m_OldBlockPos.y);
			moveBlockNum += Rainbow::Abs(posBlock.z - m_OldBlockPos.z);
			if (moveBlockNum > 16)  //超过16格算1格,防止类似使用传送卷轴这种瞬移类的移动触发多次
				moveBlockNum = 1;

			for (int i = 0; i < moveBlockNum; i++)
			{
				player->moveOneBlockSizeOnTrigger();
			}

			if (moveBlockNum > 0)
			{
				m_OldBlockPos = m_Position / BLOCK_SIZE;
			}
		}

		//Rainbow::WorldPos actorWorldPos = m_Position.toWorldPos();
		//LOG_INFO("player m_OnGround: %d   actorWorldPos: %d %d %d", m_OnGround, actorWorldPos.x, actorWorldPos.y, actorWorldPos.z);
	}

	//------------------------------LivingLocoMotion::tick() content;------------------------------
	m_Motion *= 0.98f;
	if (Abs(m_Motion.x) < 0.5f) m_Motion.x = 0;
	//if(Abs(m_Motion.y) < 0.5f) m_Motion.y = 0;
	if (Abs(m_Motion.z) < 0.5f) m_Motion.z = 0;

	if(player == NULL) return;

	updateJumping();

	if(g_pPlayerCtrl == player && player != NULL && player->isInSpectatorMode() && player->getSpectatorType() == SPECTATOR_TYPE_FOLLW )
	{
		ClientPlayer* tospectatorplayer = g_pPlayerCtrl->getToSpectatorPlayer();
		if(tospectatorplayer)
		{
			m_OldPosition = m_Position;
			m_Position = tospectatorplayer->getLocoMotion()->getPosition();
			
			if (g_WorldMgr)
				g_WorldMgr->signChangedToSync(player->getObjId(), BIS_POSITION);
		}
		return;
	}
	else
	{
		m_MoveStrafing *= 0.98f;
		m_MoveForward *= 0.98f;	
		moveEntityWithHeading(m_MoveStrafing, m_MoveForward);
	}

	//playEnterLeaveWaterSound();
	//updatePrevInWaterState();
	
	if (getOwnerActor() != NULL)
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			if (functionWrapper->getCanFly())
			{
				if (m_Motion.y < 0) m_Motion.y *= 0.6f;
			}
		}
	}

	if(player != NULL && !player->isInSpectatorMode())
		collideWithNearbyActors();
	//------------------------------LivingLocoMotion::tick() end;------------------------------

	int iSpeedType = Actor_Walk_Speed;
	ActorAttrib *attrib = getOwnerActor()->getAttrib();
	if(attrib){
		m_JumpMovementFactor = attrib->getSpeedInAir();
	}
	
	if (m_InWater || m_InLava || m_InHoney)
	{
		iSpeedType = Actor_Swim_Speed;
	}
	else if (player->getRun())
	{
		m_JumpMovementFactor *= GetLuaInterfaceProxy().get_lua_const()->chongci_yidong_beilv;
		iSpeedType = Actor_Run_Speed;
	}
	else if (player->getSneaking())
	{
		iSpeedType = Actor_Sneak_Speed;
	}
	else
	{
		iSpeedType = Actor_Walk_Speed;
	}

	float currentMoveSpeed = (attrib != NULL) ? attrib->getMoveSpeed(iSpeedType) : 0;

	//枪对移动速度的影响	
	if (GetDefManagerProxy()->getGunDef(player->getCurToolID()))
	{
		currentMoveSpeed = currentMoveSpeed * player->getGunLogical()->getGunSpeedRatio();
	}

	//道具技能对速度的影响
	const ItemSkillDef* skilldef = player->getCurItemSkillDef();
	if (skilldef && skilldef->ChargeMove > 0.001f)
		currentMoveSpeed = currentMoveSpeed * skilldef->ChargeMove;

	if (m_skillChargeMove > 0)
	{
		currentMoveSpeed = currentMoveSpeed * m_skillChargeMove;
	}

	//附魔的影响
	if (m_IsEnchClimb && isOnAirWall())
	{
		doJump();
	}
	m_IsEnchClimb = false;
	if (getOwnerActor() && player &&(!(m_tickLadder == kOnLadder) && (m_tickLadder == kNotOnLadder || !isOnLadder())) && !isOnAirWall())
	{

		float value2 = 0;
		float value1 = player->getLivingAttrib()->getEquipEnchantValue(EQUIP_SHOE, ENCHANT_CLIMB,ATTACK_ALL,ATTACK_TARGET_ALL,&value2);
		if(value2 > 0)
		{
			//getOwnerActor()->m_FallDistance = 0;
			//if(m_Motion.y < -15.0f) m_Motion.y = -15.0f;

			if(m_CollidedHorizontally)
			{
				m_Motion.x *= (1 + value1);
				m_Motion.z *= (1 + value1);
				m_Motion.y = 5.0f*(1 + value1);

				//每2秒消耗耐久
				if(m_EnchantClimbCount>0 && m_EnchantClimbCount%40 == 0)
				{
					player->getLivingAttrib()->damageEquipItemWithType(EQUIP_SHOE, (int)value2);
				}

				m_IsEnchClimb = true;
				m_EnchantClimbCount++;
			}
			else
			{
				m_EnchantClimbCount = 0;
			}
		}
	}	
	m_tickLadder = kLadderNone;
	if (player->getCurOperate() == PLAYEROP_BALL_CHARGE_BEGIN)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.charge_move_speed_ratio;
	}
	else if (player->getCurOperate() == PLAYEROP_BASKETBALL_CHARGE_BEGIN)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.charge_move_ratio;
	}
	else if (player->getCatchBall())
	{
		if (player->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_move_ratio;
		else if (player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_move_ratio;
		else
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.dribbing_move_ratio;
	}
	else if (player->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.football_way__move_ratio;
	}
	else if (player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		if (player->getCurOperate() == PLAYEROP_BASKETBALL_OBSTRUCT)
		{
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.obstruct_move_ratio;
		}
		else
		{
			currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.basketball_way_move_ratio;
		}
	}
	else if (player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		currentMoveSpeed = currentMoveSpeed * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.football_way__move_ratio;
	}

	if (player->isChargeWalkSlow())
	{
		currentMoveSpeed *= GetLuaInterfaceProxy().get_lua_const()->xuli_yidong_beilv;
	}
	else if (player->getRun())
	{
		currentMoveSpeed *= GetLuaInterfaceProxy().get_lua_const()->chongci_yidong_beilv;
	}
	auto funcWrapper = player->getFuncWrapper();
	if (funcWrapper)
	{
		funcWrapper->setAIMoveSpeed(currentMoveSpeed * player->getSpeedUpTimes());
	}

	if(m_JumpingTicks==0 && !getOwnerActor()->getSneaking() && m_OnGround && !getOwnerActor()->getFlying() && (fabs(m_MoveForward) > EPSILON ||fabs(m_MoveStrafing) > EPSILON))
	{
		Rainbow::Vector3f fdir = Yaw2FowardDir(m_RotateYaw);
		Rainbow::Vector3f sdir = Yaw2StrafingDir(m_RotateYaw);
		float r = (m_BoundSize*jump_scale) / Sqrt(m_MoveForward*m_MoveForward + m_MoveStrafing*m_MoveStrafing);
		int dx = int((m_MoveForward*fdir.x + m_MoveStrafing*sdir.x) * r);
		int dz = int((m_MoveForward*fdir.z + m_MoveStrafing*sdir.z) * r);
	
		if(CanJumpOnPos(getOwnerActor(), dx, dz))
		{
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getOwnerActor()->getAttrib());
			if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
			{
			}
			else
			{
				int cooldown;
				if (prepareJump(cooldown))
				{
					autoStep();
					m_JumpingTicks = cooldown;
				}
			}
		}
	}

	//land from fly state
	if(m_OnGround && getOwnerActor() && m_Motion.y<=0 && getOwnerActor()->getFlying())
	{
		//******** 非玩法创造模式 落地后不设置取消飞行 codeby qinpeng
		// 审查模式下，落地不取消飞行 code-by:liya
		if (!g_WorldMgr->isGameMakerRunMode() && !player->IsInPierceMode() && player->hasUIControl())
		{
			if (player->isNewMoveSyncSwitchOn())
				player->changeMoveFlag(IFC_Fly, false);
			else
				player->setFlying(false);
		}
	}

	//clear fall distance in fly status
	if (getOwnerActor() != NULL && (getOwnerActor()->getFlying() || player->isInSpectatorMode()))
	{
		auto functionWrapper = getOwnerActor()->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0.0f);
		}
	}

	//if (m_PhysActor)
	//{
	//	Rainbow::Vector3f pos = m_Position.toVector3() + Rainbow::Vector3f(0.0f, (float)(m_BoundHeight / 2), 0.0f);
	//	Rainbow::Quaternionf rot;
	//	//rot.setEulerAngle(m_PrevRotateYaw, 0.0f, 0.0f);
	//	rot=AngleEulerToQuaternionf(Vector3f(0.0f, m_PrevRotateYaw, 0.0f));
	//	m_PhysActor->SetKinematicPos(pos, rot);
	//}

	// 状态(开发者用)
	player->setFallGround(m_OnGround);
	bool horizontalMove = fabs(m_Motion.x) > EPSILON ||fabs(m_Motion.z) > EPSILON;
	player->setCurrentMoveState(horizontalMove);
	player->setStopState(m_OnGround && !horizontalMove);
	player->updateRunState();
	checkOtherEffectTriggerMotion();
}

void PlayerLocoMotion::setMoveType(int val)
{
	LivingLocoMotion::setMoveType(val);
	if (GetOwnerPlayer() && GetOwnerPlayer()->hasUIControl())
		AntiSetting::logFlagChange(13, val, "pl");
}

void PlayerLocoMotion::setOnGround(bool on_ground)
{
	LivingLocoMotion::setOnGround(on_ground);
}
void PlayerLocoMotion::preMove()
{
	ClientPlayer *player = dynamic_cast<ClientPlayer*>(GetOwnerPlayer());

	if (player && (isMovementBlocked() && !player->isInSpectatorMode()) || player->getUsingEmitter())
	{
		clearTarget();
	}
	else
	{
		if (m_MoveTargetSpeed >= 0)
			updateMoveTarget();
	}
}
