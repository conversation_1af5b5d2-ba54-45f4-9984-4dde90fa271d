#include "AiVacant.h"
#include "ActorVehicleAssemble.h"
#include "ClientVacantBoss.h"
#include "LivingLocoMotion.h"
#include "PlayerAttrib.h"
#include "EffectManager.h"
#include "AttackedComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "ActorBody.h"
#include "ClientPlayer.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialBase.h"


EXPORT_SANDBOXENGINE extern bool g_EnableReLighting;

AIVacant::AIVacant(ActorLiving *pActor, char* parm) :m_pActor(pActor), m_fireNum(3), m_bBorn(false)
{
	m_pLivingLocomotion = dynamic_cast<LivingLocoMotion*>(m_pActor->getLocoMotion());
	m_pLivingLocomotion->m_MovementAbility.SetFlag(ActorLocoMotion::Movement_Water, false);
	m_pLivingLocomotion->SetSmoothRotation(false);
	//�����Լ���Ѫ
	m_DechpAdd = 0;
	m_Uin = 0;
	m_bFly_RushCD = false;
	m_EmptyControlCount = 0;
	m_bRushDown = false;
	auto effectComponent = m_pActor->getEffectComponent();
	if (effectComponent)
	{
		effectComponent->playBodyEffect("mob_100068_hq1", true);
		effectComponent->playBodyEffect("mob_100068_hq2", true);
		effectComponent->playBodyEffect("mob_100068_hq3", true);
		effectComponent->playBodyEffect("boss_10068_zs", true);
	}
	m_standOutTime = 0;
	m_standPos = WCoord(0, 0, 0);
	m_rushObj.clear();
	m_tpHpObj.clear();
	m_curLoopSnd = NULL;
	//��ʼ������
	m_paramObj.parse(parm);
}

AIVacant::~AIVacant()
{
	if (m_curLoopSnd)
		OGRE_RELEASE(m_curLoopSnd);
	GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_pActor->getObjId() & 0xffffffff);
	GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_COLLIDE, 0, m_pActor->getObjId() & 0xffffffff);
	if (m_Uin)
		GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
	GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_APPEAL, 0, m_pActor->getObjId() & 0xffffffff);
	GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_APPEAL_, 0, m_pActor->getObjId() & 0xffffffff);
}

bool AIVacant::nearTarget()
{
	WCoord pos = m_pActor->getPosition();
	WCoord pos1 = m_pLivingLocomotion->m_MoveTarget;
	double vecx = pos.x - pos1.x;
	double vecy = pos.y - pos1.y;
	double vecz = pos.z - pos1.z;
	if ((vecx*vecx + vecy * vecy + vecz * vecz) < (25.0f*25.0f))
	{
		return true;
	}
	return false;
}

void AIVacant::OnTimer(unsigned long dwTimerID)
{
	if (m_pActor->getWorld() == NULL)
		return;
	if (dwTimerID == Fly_Up)
	{
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		WCoord pos = m_pActor->getPosition();
		int bh = m_pActor->getLocoMotion()->m_BoundHeight;
		WCoord top(pos.x, pos.y + bh + 50, pos.z);
		for (; pos < top; pos.y += 50)
		{
			//��Ҫ���Ƿ���ƻ����ж�
			m_pActor->getWorld()->destroyBlock(CoordDivBlock(pos), BLOCK_MINE_NONE, 0);
		}
		if (nearTarget())
		{
			GetSandBoxManager().KillTimer(Fly_Up, this);
			//���뾯��״̬
			if (m_paramObj.has<jsonxx::Number>("appeal_up_end_time"))
				GetSandBoxManager().SetTimer(Fly_Guard, m_paramObj.get<jsonxx::Number>("appeal_up_end_time"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Guard, 2000, this);
			//todo 
			resetMove();
		}
	}
	else if (dwTimerID == Fly_Dir_Target)
	{
		if (GetWorldManagerPtr())
		{
			ClientPlayer *player = NULL;
			player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(m_Uin));
			if (player)
				m_pActor->faceActor(player, 100.0f, 100.0f);
		}
	}
	else if (dwTimerID == Fly_Down)
	{
		if (m_fireNum > 0)
		{
			if (isBeBlockMove() || nearTarget())
			{
				//��ͣ����
				GetSandBoxManager().KillTimer(Fly_Down, this); 

				//���Ŷ�����ͣ����
				playLoopSound("ent.3515.charge", 1.0f, 1.0f);
				m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM3);
				auto effectComponent = m_pActor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("mob_100068_cc", true);
				}
				m_pActor->setPosition(m_pLivingLocomotion->m_MoveTarget);
				GetSandBoxManager().SetTimer(Fly_Dir_Target, 50, this);

				int time = 1000;
				if (m_paramObj.has<jsonxx::Number>("rush_pre_time_1") && m_paramObj.has<jsonxx::Number>("rush_select_target_time_1"))
					time = m_paramObj.get<jsonxx::Number>("rush_pre_time_1") - m_paramObj.get<jsonxx::Number>("rush_select_target_time_1");
				GetSandBoxManager().SetTimer(FLy_Down_Select_Target, time, this);

				resetMove();
			}
		}
		else
		{
			assert(0);
		}
	}
	else if (dwTimerID == Fly_Down_)
	{
		m_pActor->playAnim(SEQ_VACANT1_ANNIM15);
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			float v = 3.0f;
			sound->playSoundFollowActor("ent.3515.dash", v, 1.0f,false);
		}
		if (m_pActor->getPosition().y - m_TargetWCoord.y < 0)// ����
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM5);
		else//ˮƽ
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM4);

		if (m_paramObj.has<jsonxx::Number>("rush_speed_1"))
		{
			if (m_fireNum == 1 && m_paramObj.has<jsonxx::Number>("rush_speed_1_1"))
				m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("rush_speed_1_1");
			else
				m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("rush_speed_1");
		}
		else
			m_pLivingLocomotion->m_SpeedMultiple = 10.0;
		m_pLivingLocomotion->m_HasTarget = true;
		m_pLivingLocomotion->m_MoveTarget = m_TargetWCoord;
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
		m_rushObj.clear();

		if (m_curLoopSnd)
			OGRE_RELEASE(m_curLoopSnd);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_cc", true);
		}
		//��ʼ���
		GetSandBoxManager().KillTimer(Fly_Down_, this);
		GetSandBoxManager().SetTimer(Fly_Rush, 50, this);
	}
	else if (dwTimerID == FLy_Down_Select_Target)
	{
		GetSandBoxManager().KillTimer(Fly_Dir_Target, this);
		GetSandBoxManager().KillTimer(FLy_Down_Select_Target, this);
		ClientPlayer *player = NULL;
		if (GetWorldManagerPtr())
		{
			player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(m_Uin));
		}
		if (player)
		{
			Rainbow::Vector3f dir = player->getPosition().toVector3() - m_pActor->getPosition().toVector3();
			dir  = MINIW::Normalize(dir);

			if (m_paramObj.has<jsonxx::Number>("rush_distance_1"))
				dir *= m_paramObj.get<jsonxx::Number>("rush_distance_1");
			else
				dir *= 3000.0f;

			dir += m_pActor->getPosition().toVector3();
			m_TargetWCoord = WCoord(dir.x, dir.y, dir.z);
			m_pActor->faceActor(player, 100.0f, 100.0f);

			if (m_paramObj.has<jsonxx::Number>("rush_select_target_time_1"))
				GetSandBoxManager().SetTimer(Fly_Down_, m_paramObj.get<jsonxx::Number>("rush_select_target_time_1"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Down_, 1000, this);
		}
		else
		{
			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM1);
			GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
		}
	}
	else if (dwTimerID == Fly_Rush)
	{
		/*  - �ƻ�;���ķ��飨������㸽�����飩����ǰ������ײ��������
			- boss;���·��ķ����ϣ����ӻ��淽�飨�Ż�*/
		Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();
		dir  = MINIW::Normalize(dir);
		//dir *= 100.0f;//(m_pActor->getLocoMotion()->m_BoundSize/2 + 10);
		/*�ݻٷ���*/
		WCoord pos = m_pActor->getPosition();
		int destroyRangeX = 1;
		int destroyRangeY = 1;
		if ( m_paramObj.has<jsonxx::Number>("rush_destory_block_range_x_1"))
			destroyRangeX = m_paramObj.get<jsonxx::Number>("rush_destory_block_range_x_1") / 100;
		if (m_paramObj.has<jsonxx::Number>("rush_destory_block_range_y_1"))
			destroyRangeY = m_paramObj.get<jsonxx::Number>("rush_destory_block_range_y_1") / 100;
		for (float len = 0.0f; len <= 200.0f; len = len + 50.0f)
		{
			Rainbow::Vector3f dir_ = dir;
			dir_ *= len;
			WCoord target(pos.x + dir_.x, pos.y + dir_.y, pos.z + dir_.z);
			WCoord wcoord = CoordDivBlock(target);
			int range_x = m_pActor->getLocoMotion()->m_BoundSize / 200 + destroyRangeX;
			int range_y = m_pActor->getLocoMotion()->m_BoundHeight / 100 + destroyRangeY;
			int range_z = m_pActor->getLocoMotion()->m_BoundSize / 200 + destroyRangeX;
			for (int x_ = -range_x; x_ <= range_x; x_++)
			{
				for (int y_ = 0; y_ < range_y; y_++)
				{
					for (int z_ = -range_z; z_ <= range_z; z_++)
					{
						WCoord tmpcood = WCoord(wcoord.x + x_, wcoord.y + y_, wcoord.z + z_);
						if (isBlockCanDestroy(tmpcood)) 
						{
							m_pActor->getWorld()->setBlockAll(tmpcood, 0, 0 , 3, false, false);
						}
							
					}
				}
			}
		}

		//boss���µĵط�ˢ����
		WCoord wcoord = CoordDivBlock(m_pActor->getPosition());
		int range_x = m_pActor->getLocoMotion()->m_BoundSize / 200;
		//int range_y = m_pActor->getLocoMotion()->m_BoundHeight/100;
		int range_z = m_pActor->getLocoMotion()->m_BoundSize / 200;

		if (m_paramObj.has<jsonxx::Number>("fire_size_z"))
			 range_x = m_paramObj.get<jsonxx::Number>("fire_size_z") / 100;
		
		if (m_paramObj.has<jsonxx::Number>("fire_size_x"))
			range_z = m_paramObj.get<jsonxx::Number>("fire_size_x") / 100;

		for (int x_ = -range_x; x_ <= range_x; x_++)
		{
			//for (int y_ = 0; y_< range_y; y_++)
			{
				for (int z_ = -range_z; z_ <= range_z; z_++)
				{
					WCoord tmpcood = WCoord(wcoord.x + x_, wcoord.y, wcoord.z + z_);
					if (isBlockCanDestroy(tmpcood))
					{
						m_pActor->getWorld()->setBlockAll(tmpcood, BLOCK_FIRE, 0 , 3, false, false);
					}
						
				}
			}
		}
		std::vector<ClientActor *>actors;
		Rainbow::Vector3f dir1 = Yaw2FowardDir(m_pActor->getLocoMotion()->m_RotateYaw);
		dir1.y = 0.02f;
		int range = 400;
		int width = 400;
		if (m_paramObj.has<jsonxx::Number>("rush_attack_range_1"))
			range = m_paramObj.get<jsonxx::Number>("rush_attack_range_1");
		if (m_paramObj.has<jsonxx::Number>("rush_attack_width_1"))
			width = m_paramObj.get<jsonxx::Number>("rush_attack_width_1");
		
		m_pActor->getLocoMotion()->getFacedActors(actors, dir1, range, width);

		int damage = 20;
		float knockup = 0;
		float knockback = 1.0f;
		OneAttackData atkdata;
		//memset(&atkdata, 0, sizeof(atkdata));
		if (m_paramObj.has<jsonxx::Number>("rush_damage_1"))
			damage = m_paramObj.get<jsonxx::Number>("rush_damage_1");
		if (m_paramObj.has<jsonxx::Number>("rush_knockup_1"))
			knockup = m_paramObj.get<jsonxx::Number>("rush_knockup_1") / 100;
		if (m_paramObj.has<jsonxx::Number>("rush_knockback_1"))
			knockback = m_paramObj.get<jsonxx::Number>("rush_knockback_1") / 100;
		// ���˺�����ϵͳ code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
			atkdata.atkPointsNew[ATTACK_PUNCH] = damage;
		}
		else
		{
			atkdata.atkpoints = damage;
			atkdata.atktype = ATTACK_PUNCH;
		}
		atkdata.knockup = knockup;
		//atkdata.knockback = knockback;
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor *target = actors[i];
			if (target == NULL || target == m_pActor) continue;
			bool isBeRush = false;
			for (size_t j = 0; j < m_rushObj.size(); j++)
			{
				if (m_rushObj[j] == target->getObjId())
				{
					isBeRush = true;
					break;
				}
			}
			if (isBeRush) continue;
			m_rushObj.push_back(target->getObjId());
			auto component = target->getAttackedComponent();
			if (component)
			{
				component->attackedFrom(atkdata, m_pActor);
			}
			Rainbow::Vector3f dir_ = target->getPosition().toVector3() - m_pActor->getPosition().toVector3();
			dir_  = MINIW::Normalize(dir_);
			dir_ *= knockback * 100;
			Rainbow::Vector3f motion = dir_;
			target->setMotionChange(motion);
			ClientPlayer *pl = dynamic_cast<ClientPlayer*>(target);
			if (pl)
				pl->getLivingAttrib()->addBuff(1009, 5);
		}

		if (nearTarget() || isBeBlockMove())
		{
			//ԭ����Ϣ2��
			GetSandBoxManager().KillTimer(Fly_Rush, this);
			if (m_paramObj.has<jsonxx::Number>("rush_end_time_1"))
				GetSandBoxManager().SetTimer(Fly_Rush_, m_paramObj.get<jsonxx::Number>("rush_end_time_1"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_, 2000, this);

			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM1);

			//�ﵽĿ�����������ֻʣһ�������������
			if (m_fireNum == 1)
			{
				m_pActor->playAnim(SEQ_VACANT1_ANNIM7);
				auto effectComponent = m_pActor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->playBodyEffect("mob_100068_bosscj", true);
				}
				//m_pActor->playSound("ent.3515.blast", 1.0f, 1.0f);
				GetSandBoxManager().SetTimer(Fly_Rush_Down_Sound, 400, this);
				//��ײ�к�boss�ȸ߶�y����Χshoke_wave_range
				std::vector<ClientActor *> shokeActors;
				Rainbow::Vector3f dir = Yaw2FowardDir(m_pActor->getLocoMotion()->m_RotateYaw);
				dir.y = 0.02f;
				int range = 300;
				int height = 10;
				int damage = 20;
				float knockup = 1.0f;
				float knockback = 1.0f;
				int buffId = CATCH_FIRE_BUFF;
				int buffLv = 4;
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_range"))
					range = m_paramObj.get<jsonxx::Number>("shoke_wave_range");
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_height"))
					height = m_paramObj.get<jsonxx::Number>("shoke_wave_height");
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_damage"))
					damage = m_paramObj.get<jsonxx::Number>("shoke_wave_damage");
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_knockup"))
					knockup = m_paramObj.get<jsonxx::Number>("shoke_wave_knockup") / 100;
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_knockback"))
					knockback = m_paramObj.get<jsonxx::Number>("shoke_wave_knockback") / 100;
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_buffId"))
					buffId = m_paramObj.get<jsonxx::Number>("shoke_wave_buffId");
				if (m_paramObj.has<jsonxx::Number>("shoke_wave_buffLv"))
					buffLv = m_paramObj.get<jsonxx::Number>("shoke_wave_buffLv");
				
				m_pActor->getLocoMotion()->getFanShapedAreaFacedActors(shokeActors, dir, range, height, 360);

				OneAttackData atkdata;
				//memset(&atkdata, 0, sizeof(atkdata));
				// ���˺�����ϵͳ code-by:liya
				if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
				{
					atkdata.atkTypeNew = (1 << (ATTACK_FIRE + 1));
					atkdata.atkPointsNew[ATTACK_FIRE + 1] = damage;
				}
				else
				{
					atkdata.atkpoints = damage;
					atkdata.atktype = ATTACK_FIRE;
				}
				atkdata.knockup = knockup;
				atkdata.knockback = knockback;
				for (size_t i = 0; i < shokeActors.size(); i++)
				{
					ClientActor *target = shokeActors[i];
					if (target == m_pActor || target->getObjId() == m_pActor->getObjId()) continue;
					ClientPlayer *pl = dynamic_cast<ClientPlayer*>(target);
					if (pl)
						pl->getLivingAttrib()->addBuff(buffId, buffLv);
					auto component = target->getAttackedComponent();
					if (component)
					{
						component->attackedFrom(atkdata, m_pActor);
					}
				}
			}
			else
				m_pActor->playAnim(SEQ_VACANT1_ANNIM16);


			if (isLowHp())
			{
				if (m_paramObj.has<jsonxx::Number>("rush_cd_2"))
					GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, m_paramObj.get<jsonxx::Number>("rush_cd_2"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, 15000, this);
			}
			else
			{
				if (m_paramObj.has<jsonxx::Number>("rush_cd_1"))
					GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, m_paramObj.get<jsonxx::Number>("rush_cd_1"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, 20000, this);
			}
			//todo
			resetMove();
			m_bFly_RushCD = true;
		}
	}
	else if (dwTimerID == Fly_Rush_)
	{
		GetSandBoxManager().KillTimer(Fly_Rush_, this);
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM1);
		//�ص�ԭλ��
		m_fireNum--;
		if (m_fireNum > 0)
		{
			char name[32];
			sprintf(name, "mob_100068_hq%d", m_fireNum + 1);
			auto effectComponent = m_pActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect(name, true);
			}
			//�ػؾ���״̬
			GetSandBoxManager().SetTimer(Fly_Guard, 0, this);
		}
		else
		{
			auto effectComponent = m_pActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("mob_100068_hq1", true);
			}
			//�������
			if (m_paramObj.has<jsonxx::Number>("add_fire_pre_time"))
				GetSandBoxManager().SetTimer(Fly_Add_Fire, m_paramObj.get<jsonxx::Number>("add_fire_pre_time"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Add_Fire, 3000, this);
		}
	}
	else if (dwTimerID == Fly_Rush_CD_Finish)
	{
		m_bFly_RushCD = false;
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_bosscj", true);
			effectComponent->stopBodyEffect("boss_10069_zd", true);
		}
		GetSandBoxManager().KillTimer(Fly_Rush_CD_Finish, this);
	}
	else if (dwTimerID == Fly_Rush_UP)
	{
		if (nearTarget())
		{
			GetSandBoxManager().KillTimer(Fly_Rush_UP, this);

			if (m_paramObj.has<jsonxx::Number>("rush_pre_time_2"))
				GetSandBoxManager().SetTimer(Fly_Rush_Down, m_paramObj.get<jsonxx::Number>("rush_pre_time_2"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_Down, 2000, this);

			m_rushObj.clear();

			//������Ч��ͣ����
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM12);
			playLoopSound("ent.3515.charge", 1.0f, 1.0f);
			//Ŀ������ ��Ч
			int range = 300;
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_range"))
				range = m_paramObj.get<jsonxx::Number>("shoke_wave_range");
			int height = 1000;
			if (m_paramObj.has<jsonxx::Number>("rush_pre_height_2"))
				height = m_paramObj.get<jsonxx::Number>("rush_pre_height_2");
			m_TargetWCoord.y -= height;
			m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/item_boss_yujing2.ent", m_TargetWCoord, 60, 0, 0, true, 30);
			
			resetMove();
		}
		else if (isBeBlockMove())//�����ϰ�ֹͣ
		{
			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM1);
			GetSandBoxManager().KillTimer(Fly_Rush_UP, this);
			GetSandBoxManager().SetTimer(Fly_Guard, 0, this);
		}
	}
	else if (dwTimerID == Fly_Rush_Down)
	{
		m_pActor->playAnim(SEQ_VACANT1_ANNIM17);
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM13);
		GetSandBoxManager().KillTimer(Fly_Rush_Down, this);
		GetSandBoxManager().SetTimer(Fly_Rush_Down_, 10, this);

		if (m_paramObj.has<jsonxx::Number>("rush_speed_2"))
			m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("rush_speed_2");
		else
			m_pLivingLocomotion->m_SpeedMultiple = 10.0f;

		m_pLivingLocomotion->m_HasTarget = true;
		m_pLivingLocomotion->m_MoveTarget = m_TargetWCoord;
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
		m_bFly_RushCD = true;

		if (m_curLoopSnd)
			OGRE_RELEASE(m_curLoopSnd);
		
		if (isLowHp())
		{
			if (m_paramObj.has<jsonxx::Number>("rush_cd_2"))
				GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, m_paramObj.get<jsonxx::Number>("rush_cd_2"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, 15000, this);
		}
		else
		{
			if (m_paramObj.has<jsonxx::Number>("rush_cd_1"))
				GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, m_paramObj.get<jsonxx::Number>("rush_cd_1"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_CD_Finish, 20000, this);
		}
	}
	else if (dwTimerID == Fly_Rush_Down_)
	{
		Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();
		dir  = MINIW::Normalize(dir);
		/*�ݻٷ���*/
		WCoord pos = m_pActor->getPosition();
		int destroyRangeX = 1;
		int destroyRangeY = 1;
		if (m_paramObj.has<jsonxx::Number>("rush_destory_block_range_x_2"))
			destroyRangeX = m_paramObj.get<jsonxx::Number>("rush_destory_block_range_x_2") / 100;
		if (m_paramObj.has<jsonxx::Number>("rush_destory_block_range_y_2"))
			destroyRangeY = m_paramObj.get<jsonxx::Number>("rush_destory_block_range_y_2") / 100;
		for (float len = 0.0f; len <= 200.0f; len = len + 50.0f)
		{
			Rainbow::Vector3f dir_ = dir;
			dir_ *= len;
			WCoord target(pos.x + dir_.x, pos.y + dir_.y, pos.z + dir_.z);
			WCoord wcoord = CoordDivBlock(target);
			int range_x = m_pActor->getLocoMotion()->m_BoundSize / 200 + destroyRangeX;
			int range_y = m_pActor->getLocoMotion()->m_BoundHeight / 100 + destroyRangeY;
			int range_z = m_pActor->getLocoMotion()->m_BoundSize / 200 + destroyRangeX;
			for (int x_ = -range_x; x_ <= range_x; x_++)
			{
				for (int y_ = 0; y_ < range_y; y_++)
				{
					for (int z_ = -range_z; z_ <= range_z; z_++)
					{
						WCoord tmpcood = WCoord(wcoord.x + x_, wcoord.y + y_, wcoord.z + z_);
						if (isBlockCanDestroy(tmpcood))
							m_pActor->getWorld()->setBlockAll(tmpcood, 0, 0);
					}
				}
			}
		}

		std::vector<ClientActor *>actors;
		Rainbow::Vector3f dir1 = Yaw2FowardDir(m_pActor->getLocoMotion()->m_RotateYaw);
		dir1.y = 0.02f;
		int range = 400;
		int width = 400;
		if (m_paramObj.has<jsonxx::Number>("rush_attack_range_1"))
			range = m_paramObj.get<jsonxx::Number>("rush_attack_range_1");
		if (m_paramObj.has<jsonxx::Number>("rush_attack_width_1"))
			width = m_paramObj.get<jsonxx::Number>("rush_attack_width_1");
		
		m_pActor->getLocoMotion()->getFacedActors(actors, dir1, range, width);

		int damage = 20;
		float knockup = 0;
		float knockback = 1.0f;
		OneAttackData atkdata;
		//memset(&atkdata, 0, sizeof(atkdata));
		if (m_paramObj.has<jsonxx::Number>("rush_damage_1"))
			damage = m_paramObj.get<jsonxx::Number>("rush_damage_1");
		if (m_paramObj.has<jsonxx::Number>("rush_knockup_1"))
			knockup = m_paramObj.get<jsonxx::Number>("rush_knockup_1") / 100;
		if (m_paramObj.has<jsonxx::Number>("rush_knockback_1"))
			knockback = m_paramObj.get<jsonxx::Number>("rush_knockback_1") / 100;
		// ���˺�����ϵͳ code-by:liya
		if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
		{
			atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
			atkdata.atkPointsNew[ATTACK_PUNCH] = damage;
		}
		else
		{
			atkdata.atkpoints = damage;
			atkdata.atktype = ATTACK_PUNCH;
		}
		atkdata.knockup = knockup;
		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientActor *target = actors[i];
			if (target == NULL || target == m_pActor) continue;
			bool isBeRush = false;
			for (size_t j = 0; j < m_rushObj.size(); j++)
			{
				if (m_rushObj[j] == target->getObjId())
				{
					isBeRush = true;
					break;
				}
			}
			if (isBeRush) continue;
			m_rushObj.push_back(target->getObjId());
			auto component = target->getAttackedComponent();
			if (component)
			{
				component->attackedFrom(atkdata, m_pActor);
			}
			Rainbow::Vector3f dir_ = target->getPosition().toVector3() - m_pActor->getPosition().toVector3();
			dir_  = MINIW::Normalize(dir_);
			dir_ *= knockback * 100;
			Rainbow::Vector3f motion = dir_;
			target->setMotionChange(motion);
		}
		//�Ƿ������ǰ�ƶ�
		/*if (isBeBlockMove())
		{
			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
			GetSandBoxManager().KillTimer(Fly_Rush_Down_, this);
			if (m_paramObj.has<jsonxx::Number>("rush_end_time_2"))
				GetSandBoxManager().SetTimer(Fly_Rush_, m_paramObj.get<jsonxx::Number>("rush_end_time_2"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_, 2000, this);
		}*/
		if (nearTarget() || isBeBlockMove())
		{
			auto effectComponent = m_pActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("boss_10069_zd", true);
			}
			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
			//�ﵽĿ���󣬴��������

			m_pActor->playAnim(SEQ_VACANT1_ANNIM7);
			if (effectComponent)
			{
				effectComponent->playBodyEffect("mob_100068_bosscj", true);
			}
			GetSandBoxManager().SetTimer(Fly_Rush_Down_Sound, 400, this);
			//��ײ�к�boss�ȸ߶�y����Χshoke_wave_range
			std::vector<ClientActor *> shokeActors;
			Rainbow::Vector3f dir = Yaw2FowardDir(m_pActor->getLocoMotion()->m_RotateYaw);
			dir.y = 0.02f;
			int range = 300;
			int height = 10;
			int damage = 20;
			float knockup = 1.0f;
			float knockback = 1.0f;
			int buffId = CATCH_FIRE_BUFF;
			int buffLv = 4;
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_range"))
				range = m_paramObj.get<jsonxx::Number>("shoke_wave_range");
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_height"))
				height = m_paramObj.get<jsonxx::Number>("shoke_wave_height");
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_damage"))
				damage = m_paramObj.get<jsonxx::Number>("shoke_wave_damage");
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_knockup"))
				knockup = m_paramObj.get<jsonxx::Number>("shoke_wave_knockup") / 100;
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_knockback"))
				knockback = m_paramObj.get<jsonxx::Number>("shoke_wave_knockback") / 100;
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_buffId"))
				buffId = m_paramObj.get<jsonxx::Number>("shoke_wave_buffId");
			if (m_paramObj.has<jsonxx::Number>("shoke_wave_buffLv"))
				buffLv = m_paramObj.get<jsonxx::Number>("shoke_wave_buffLv");
			
			m_pActor->getLocoMotion()->getFanShapedAreaFacedActors(shokeActors, dir, range, height, 360);

			OneAttackData atkdata;
			//memset(&atkdata, 0, sizeof(atkdata));
			// ���˺�����ϵͳ code-by:liya
			if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate)
			{
				atkdata.atkTypeNew = (1 << ATTACK_PUNCH);
				atkdata.atkPointsNew[ATTACK_PUNCH] = damage;
			}
			else
			{
				atkdata.atkpoints = damage;
				atkdata.atktype = ATTACK_PUNCH;
			}
			atkdata.knockup = knockup;
			atkdata.knockback = knockback;
			for (size_t i = 0; i < shokeActors.size(); i++)
			{
				ClientActor *target = shokeActors[i];
				if (target == m_pActor || target->getObjId() == m_pActor->getObjId()) continue;
				ClientPlayer *pl = dynamic_cast<ClientPlayer*>(target);
				if (pl)
					pl->getLivingAttrib()->addBuff(buffId, buffLv);
				auto component = target->getAttackedComponent();
				if (component)
				{
					component->attackedFrom(atkdata, m_pActor);
				}
			}
			GetSandBoxManager().KillTimer(Fly_Rush_Down_, this);
			if (m_paramObj.has<jsonxx::Number>("rush_end_time_2"))
				GetSandBoxManager().SetTimer(Fly_Rush_, m_paramObj.get<jsonxx::Number>("rush_end_time_2"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Rush_, 2000, this);
		}
		
	}
	else if (dwTimerID == Fly_Rush_Down_Sound)
	{
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.blast", 1.0f, 1.0f);
		}
		GetSandBoxManager().KillTimer(Fly_Rush_Down_Sound, this);
	}
	else if (dwTimerID == Fly_Add_Fire)
	{
		GetSandBoxManager().KillTimer(Fly_Add_Fire, this);
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_100068_xr", true);
		}
		/*��ʼ�ص���̳����*/
		WCoord wcoord = m_InitWCoord;
		wcoord += WCoord(0, 1, 0);
		wcoord = BlockBottomCenter(wcoord);

		if (m_paramObj.has<jsonxx::Number>("add_fire_speed"))
			m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("add_fire_speed");
		else
			m_pLivingLocomotion->m_SpeedMultiple = 1;

		m_CostInfos.clear();
		m_WayCalcCount = 20;
		m_pLivingLocomotion->m_HasTarget = true;
		m_pLivingLocomotion->m_MoveTarget = wcoord;
		m_TargetWCoord = wcoord;
		m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
		GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part1, 50, this);
	}
	else if (dwTimerID == Fly_MoveToFire1_Part1)
	{
		GetSandBoxManager().KillTimer(Fly_MoveToFire1_Part1, this);

		// �ִ�
		m_pLivingLocomotion->m_HasTarget = true;
		m_pLivingLocomotion->m_MoveTarget = m_TargetWCoord;
		if (nearTarget())
		{
			GetSandBoxManager().SetTimer(Fly_Add_Fire1, 50, this);
			return;
		}

		if (!isBeBlockMove())
		{
			// ������
			GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part1, 50, this);
			return;
		}
		
		// Ѱ·��ǰ������
		WCoord curPos = m_pActor->getPosition();
		WCoord tgtPos = m_pLivingLocomotion->m_MoveTarget;
		//std::map<WCoord, float> costDatas;
		const int step = 300; // һ���������
		
		const float cost_horizontal = 5.0f;
		const float cost_vertical = 4.0f;
		const float cost_h_corner = cost_horizontal * 1.414f;
		auto funcCalcMoveEnable = [&](const WCoord& begpos, float dirLength, Rainbow::Vector3f dir) -> bool {
			dir = MINIW::Normalize(dir);
			dir *= dirLength;
			
			CollideAABB box;
			m_pLivingLocomotion->getCollideBox(box);
			box.pos += begpos - m_pLivingLocomotion->getPosition();

			WCoord mvec = m_pLivingLocomotion->getIntegerMotion(dir);
			WCoord ret = m_pActor->getWorld()->moveBox(box, mvec);
			if (abs(ret.x) < abs(mvec.x) || abs(ret.y) < abs(mvec.y) || abs(ret.z) < abs(mvec.z))
			{
				return false;
			}
			return true;
		};
		// ʣ�࿪��
		auto funcCalcBackCost = [&](WCoord curPos, WCoord tgtPos) -> float {
			int abs_x = abs(curPos.x - tgtPos.x);
			int abs_y = abs(curPos.y - tgtPos.y);
			int abs_z = abs(curPos.z - tgtPos.z);
			return abs_y * cost_vertical + Rainbow::Min(abs_x, abs_z) * cost_h_corner + abs(abs_x - abs_z) * cost_horizontal;
		};

		// ������С���ѵĽ��
		auto funcGetLeftMinCost = [](const CostData& data) -> float {
			return data._backCost;
		};
		auto funcGetTotalMinCost = [](const CostData& data) -> float {
			return data._frontCost + data._backCost;
		};
		auto funcCalcCostMin = [&](WCoord& outPos, CostData& outPath, std::function<float(const CostData&)> funcGet, bool checkActive) -> bool {
			float costMin = FLT_MAX;
			float costCalc = 0.0f;
			auto iterResult = m_CostInfos.end();
			for (auto iterInfo = m_CostInfos.begin(); iterInfo != m_CostInfos.end(); iterInfo++)
			{
				if (checkActive && !iterInfo->second._active)
					continue;

				costCalc = funcGet(iterInfo->second);
				if (costMin <= costCalc)
					continue;

				costMin = costCalc;
				iterResult = iterInfo;
			}
			if (iterResult == m_CostInfos.end())
				return false;

			outPos = iterResult->first;
			outPath = iterResult->second;
			return true;
		};
		auto funcCalcLeftMin = [&](WCoord& outPos, CostData& outPath) -> bool {
			return funcCalcCostMin(outPos, outPath, funcGetLeftMinCost, false);
		};
		auto funcCalcTotalMin = [&](WCoord& outPos, CostData& outPath) -> bool {
			return funcCalcCostMin(outPos, outPath, funcGetTotalMinCost, true);
		};

		// ��������·��
		auto funcCalcFinalPath = [&](WCoord pos, std::vector<WCoord>& path) -> void {
			auto iter = m_CostInfos.find(pos);
			while(iter != m_CostInfos.end())
			{
				path.push_back(pos);
				pos = iter->second._prevPos;
				if (pos.y < 0)
					return;

				iter = m_CostInfos.find(pos);
			}
		};

		const FindWayNeighborInfo neighborData[] = {
			FindWayNeighborInfo(0, -1, 0, cost_vertical),
			FindWayNeighborInfo(0, 1, 0, cost_vertical),
			FindWayNeighborInfo(1, 0, 0, cost_horizontal),
			FindWayNeighborInfo(-1, 0, 0, cost_horizontal),
			FindWayNeighborInfo(0, 0, 1, cost_horizontal),
			FindWayNeighborInfo(0, 0, -1, cost_horizontal),
			FindWayNeighborInfo(1, 0, 1, cost_h_corner),
			FindWayNeighborInfo(-1, 0, 1, cost_h_corner),
			FindWayNeighborInfo(1, 0, -1, cost_h_corner),
			FindWayNeighborInfo(-1, 0, -1, cost_h_corner),
		};
		const int neighborSize = sizeof(neighborData) / sizeof(neighborData[0]);
		int count = 40;
		float limitCost = funcCalcBackCost(curPos, curPos + WCoord(step, step, step)) * 2.0f;

		std::function<bool(void)> funcFindWay;
		funcFindWay = [&](void) -> bool {
			float frontCost = 0.0f, backCost = 0.0f, minBackCost = FLT_MAX;
			WCoord nextPos;
			WCoord curPos;
			CostData curCostData;
			if (!funcCalcTotalMin(curPos, curCostData))
				return false;

			for (int i = 0; i < neighborSize; i++)
			{
				if (!funcCalcMoveEnable(curPos, float(step), neighborData[i]._offset.toVector3()))
					continue;

				nextPos = curPos + neighborData[i]._offset * step;
				frontCost = curCostData._frontCost + neighborData[i]._cost * step;
				backCost = funcCalcBackCost(nextPos, tgtPos);
				minBackCost = Rainbow::Min(backCost, minBackCost);

				auto iterCost = m_CostInfos.find(nextPos);
				if (iterCost != m_CostInfos.end())
				{
					auto& originCost = iterCost->second;
					if (frontCost < originCost._frontCost)
					{
						// ���Ѹ�С������ѡ�񣬸���
						originCost._frontCost = frontCost;
						originCost._backCost = backCost;
						originCost._prevPos = curPos;
					}
				}
				else
				{
					// ����
					CostData data;
					data._active = true;
					data._frontCost = frontCost;
					data._backCost = backCost;
					data._prevPos = curPos;
					m_CostInfos.insert(std::make_pair(nextPos, data));
				}
			}
			if (minBackCost <= limitCost)
				return true;

			if (count-- <= 0)
				return false;
			
			// �Ƴ�
			auto iterCost = m_CostInfos.find(curPos);
			if (iterCost == m_CostInfos.end())
				return false;
			else
				iterCost->second._active = false;

			// ����
			return funcFindWay();
		};

		// ���
		if (m_CostInfos.empty())
		{
			CostData data;
			data._active = true;
			data._prevPos = WCoord(0, -1, 0);
			data._frontCost = 0.0f;
			data._backCost = funcCalcBackCost(curPos, tgtPos);
			m_CostInfos.insert(std::make_pair(curPos, data));
		}

		if (!funcFindWay() && m_WayCalcCount-- > 0)
		{
			GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part1, 0, this);
			return;
		}

		if (m_CostInfos.empty())
		{
			GetSandBoxManager().SetTimer(Fly_Add_Fire1, 50, this);
			return;
		}

		auto funcCheckLastPositon = [&](const WCoord& posLast) -> bool {
			int last = (abs(posLast.x - tgtPos.x) + abs(posLast.y - tgtPos.y) + abs(posLast.z - tgtPos.z));
			int origin = (abs(curPos.x - tgtPos.x) + abs(curPos.y - tgtPos.y) + abs(curPos.z - tgtPos.z));
			return last < origin;
		};

		// �һ�����С�ķ����ƶ�
		WCoord retPos;
		CostData retCostData;
		if (funcCalcLeftMin(retPos, retCostData))
		{
			std::vector<WCoord> path;
			funcCalcFinalPath(retPos, path);

			m_WayPath = path;
			m_pLivingLocomotion->m_HasTarget = true;
			m_pLivingLocomotion->m_MoveTarget = m_WayPath.back();
			m_WayPath.pop_back();
			m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
			GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part2, 50, this);
			return;
		}

		// �޷�Ѱ·��ֱ����һ���׶�
		GetSandBoxManager().SetTimer(Fly_Add_Fire1, 50, this);
		return;
	}
	else if (dwTimerID == Fly_MoveToFire1_Part2)
	{
		GetSandBoxManager().KillTimer(Fly_MoveToFire1_Part2, this);

		WCoord curPos = m_pActor->getPosition();
		WCoord tgtPos = m_pLivingLocomotion->m_MoveTarget;
		int distance = (curPos.x - tgtPos.x) * (curPos.x - tgtPos.x)
			+ (curPos.y - tgtPos.y) * (curPos.y - tgtPos.y)
			+ (curPos.z - tgtPos.z) * (curPos.z - tgtPos.z);
		if (distance < 25.0f)
		{
			if (m_WayPath.empty())
			{
				int distanceDst = (curPos.x - m_TargetWCoord.x) * (curPos.x - m_TargetWCoord.x)
					+ (curPos.y - m_TargetWCoord.y) * (curPos.y - m_TargetWCoord.y)
					+ (curPos.z - m_TargetWCoord.z) * (curPos.z - m_TargetWCoord.z);
				if (distanceDst > 7*7*7 && !isBeBlockMove())
				{
					m_CostInfos.clear();
					m_WayCalcCount = 20;
					GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part1, 50, this);
				}
				else
				{
					GetSandBoxManager().SetTimer(Fly_Add_Fire1, 50, this);
				}
			}
			else
			{
				m_pLivingLocomotion->m_HasTarget = true;
				m_pLivingLocomotion->m_MoveTarget = m_WayPath.back();
				m_WayPath.pop_back();
				m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
				GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part2, 50, this);
			}
			return;
		}

		if (isBeBlockMove())
		{
			GetSandBoxManager().SetTimer(Fly_Add_Fire1, 50, this);
			return;
		}

		GetSandBoxManager().SetTimer(Fly_MoveToFire1_Part2, 50, this);
	}
	else if (dwTimerID == Fly_Add_Fire1)
	{
		//�������λ��  - ������Ĺ��̳���10s������վ����������
		//if (nearTarget())
		//{
			GetSandBoxManager().KillTimer(Fly_Add_Fire1, this);
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM8);
			auto effectComponent = m_pActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect("mob_100068_boss_xs", true);
			}
			playLoopSound("ent.3515.charge", 1.0f, 1.0f);
			//- ��������󣬲���3�����򣬽�������״̬
			if (isLowHp())
			{
				if (m_paramObj.has<jsonxx::Number>("add_fire_duration_2"))
					GetSandBoxManager().SetTimer(Fly_Add_Fire2, m_paramObj.get<jsonxx::Number>("add_fire_duration_2"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Add_Fire2, 15000, this);
			}
			else
			{
				if (m_paramObj.has<jsonxx::Number>("add_fire_duration_1"))
					GetSandBoxManager().SetTimer(Fly_Add_Fire2, m_paramObj.get<jsonxx::Number>("add_fire_duration_1"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Add_Fire2, 10000, this);
			}
		//}
		//else if (isBeBlockMove())
		//{
		//	WCoord wcoord = m_InitWCoord;
		//	wcoord += WCoord(0, 10, 0);
		//	wcoord = BlockBottomCenter(wcoord);
		//	//����Ҹ�λ��Ѳ��
		//	if (m_pActor->getBody())
		//		m_pActor->getBody()->stopAllAnim();
		//	m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		//	WCoord m_ValidPos;
		//	int count = 60;
		//	while (count > 0)
		//	{
		//		if (m_pActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 100, 100, NULL))
		//		{
		//			double vecx = wcoord.x - m_ValidPos.x;
		//			double vecy = wcoord.y - m_ValidPos.y;
		//			double vecz = wcoord.z - m_ValidPos.z;
		//			if ((vecx*vecx + vecy * vecy + vecz * vecz) < (2000.0f*2000.0f))
		//			{
		//				m_pLivingLocomotion->m_SpeedMultiple = 1.0;
		//				m_pLivingLocomotion->m_HasTarget = true;
		//				m_pLivingLocomotion->m_MoveTarget = m_ValidPos;
		//				m_pLivingLocomotion->setBehaviorOn(FlyLocomotion::BehaviorType::LashTagert);
		//				GetSandBoxManager().KillTimer(Fly_Add_Fire1, this);
		//				GetSandBoxManager().SetTimer(Fly_Add_Fire, GenRandomInt(2000, 5000), this);
		//				break;
		//			}
		//		}
		//		count--;
		//	}
		//}
	}
	else if (dwTimerID == Fly_Add_Fire2)
	{
		GetSandBoxManager().KillTimer(Fly_Add_Fire2, this);
		if (m_curLoopSnd)
			OGRE_RELEASE(m_curLoopSnd);
		if (m_paramObj.has<jsonxx::Number>("add_fire_end_time"))
			GetSandBoxManager().SetTimer(Fly_Guard, m_paramObj.get<jsonxx::Number>("add_fire_end_time"), this);
		else
			GetSandBoxManager().SetTimer(Fly_Guard, 3000, this);
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM2);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_100068_bossRoar", true);
			effectComponent->playBodyEffect("mob_100068_hq1", true);
			effectComponent->playBodyEffect("mob_100068_hq2", true);
			effectComponent->playBodyEffect("mob_100068_hq3", true);
		}
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.roar", 3.0f, 1.0f);
		}
		//���������Ч
		if (isLowHp())
		{
			m_fireNum = 4;
			if (effectComponent)
			{
				effectComponent->playBodyEffect("mob_100068_hq4", true);
			}
		}
		else
		{
			m_fireNum = 3;
		}
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_boss_xs", true);
			effectComponent->stopBodyEffect("mob_100068_xr", true);
		}
		m_bRushDown = false;
	}
	else if (dwTimerID == Fly_Sheer_Transmit)
	{
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		m_pActor->playAnim(SEQ_VACANT1_ANNIM9);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_csxl", true);
		}
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.portal", 1.0f, 1.0f);
		}
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit, this);
		GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_, 1500, this);
	}
	else if (dwTimerID == Fly_Sheer_Transmit1)
	{
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		m_pActor->playAnim(SEQ_VACANT1_ANNIM9);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_csxl", true);
		}
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.portal", 1.0f, 1.0f);
		}
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit1, this);
		GetSandBoxManager().SetTimer(Fly_Sheer_Transmit1_, 1500, this);
	}
	else if (dwTimerID == Fly_Sheer_Transmit1_)
	{
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_pActor->getPosition(), 20, 0, 0, true, 100);
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit1_, this);
		//����
		m_pActor->setPosition(m_TargetWCoord);
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_TargetWCoord, 20, 0, 0, true, 100);
	}
	else if (dwTimerID == Fly_Sheer_Transmit_)
	{
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_pActor->getPosition(), 20, 0, 0, true, 100);
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_, this);
		//����
		m_pActor->setPosition(m_TargetWCoord);
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_TargetWCoord, 20, 0, 0, true, 100);

		if (m_paramObj.has<jsonxx::Number>("tp_elude_guard_time"))
			GetSandBoxManager().SetTimer(Fly_Guard, m_paramObj.get<jsonxx::Number>("tp_elude_guard_time"), this);
		else
			GetSandBoxManager().SetTimer(Fly_Guard, 1000, this);
	}
	else if (dwTimerID == Fly_Guard)
	{
		ClientPlayer* player = findOnePlayer();
		//�ҵ�һ����Χ����Ҿ��˳�����
		if (player && !m_bFly_RushCD && m_fireNum > 0)
		{
			GetSandBoxManager().KillTimer(Fly_Guard, this);
			if ((!isLowHp()) || (0 != GenRandomInt(m_fireNum) || m_bRushDown))
			{
				if (m_pActor->getBody())
					m_pActor->getBody()->stopAllAnim();
				m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM1);
				//����Ŀ��λ��
				Rainbow::Vector3f dir = player->getPosition().toVector3() - m_pActor->getPosition().toVector3();
				dir.y = 0;
				dir  = MINIW::Normalize(dir);
				//boss��Ҫ���䵽����ҵȸ߷�Χ rush_pre_range_1 ��λ����
				if (m_paramObj.has<jsonxx::Number>("rush_pre_range_1"))
					dir *= m_paramObj.get<jsonxx::Number>("rush_pre_range_1");
				else
					dir *= 2000.0f;

				if (m_paramObj.has<jsonxx::Number>("rush_pre_speed_1"))
					m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("rush_pre_speed_1");
				else
					m_pLivingLocomotion->m_SpeedMultiple = 10.0;

				m_pLivingLocomotion->m_HasTarget = true;
				m_pLivingLocomotion->m_MoveTarget = WCoord(player->getPosition().x - dir.x, player->getPosition().y, player->getPosition().z - dir.z);
				m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
				GetSandBoxManager().SetTimer(Fly_Down, 50, this);
				GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
				m_Uin = player->getUin();
			}
			else//�������
			{
				m_bRushDown = true;
				m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM12);
				//����Ŀ��λ��
				Rainbow::Vector3f dir = player->getPosition().toVector3() - m_pActor->getPosition().toVector3();
				dir  = MINIW::Normalize(dir);
				dir *= 10.0f;//������һ��ƫ�� ��Ϊ����boss���ҵ�ʱ���泯Ҫ�������ˡ�

				if (m_paramObj.has<jsonxx::Number>("rush_pre_speed_2"))
					m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("rush_pre_speed_2");
				else
					m_pLivingLocomotion->m_SpeedMultiple = 10.0;

				int height = 1000;
				if (m_paramObj.has<jsonxx::Number>("rush_pre_height_2"))
					height = m_paramObj.get<jsonxx::Number>("rush_pre_height_2");

				m_pLivingLocomotion->m_HasTarget = true;
				m_TargetWCoord = WCoord(player->getPosition().x + dir.x, player->getPosition().y + height, player->getPosition().z + dir.z);
				m_pLivingLocomotion->m_MoveTarget = m_TargetWCoord;
				m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
				GetSandBoxManager().SetTimer(Fly_Rush_UP, 50, this);
				GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
				m_Uin = player->getUin();
			}
			return;
		}

		if (m_pLivingLocomotion->m_HasTarget == false)
		{
			if (m_pActor->getBody())
				m_pActor->getBody()->stopAllAnim();
			m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
			WCoord wcoord = m_InitWCoord;
			wcoord += WCoord(0, 10, 0);
			wcoord = BlockBottomCenter(wcoord);
			//����Ҹ�λ��Ѳ��
			WCoord m_ValidPos;
			int count = 100;
			while (count > 0)
			{
				if (m_pActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 100, 100, NULL))
				{
					double vecx = wcoord.x - m_ValidPos.x;
					double vecy = wcoord.y - m_ValidPos.y;
					double vecz = wcoord.z - m_ValidPos.z;
					if ((vecx*vecx + vecy * vecy + vecz * vecz) < (2000.0f*2000.0f))
					{
						m_pLivingLocomotion->m_SpeedMultiple = 1.0;
						m_pLivingLocomotion->m_HasTarget = true;
						m_pLivingLocomotion->m_MoveTarget = m_ValidPos;
						m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
						break;
					}
				}
				count--;
			}
		}
	}
	else if (dwTimerID == Fly_Sheer_Transmit_Absorb_Pre)
	{
		if (m_pActor->getBody())
			m_pActor->getBody()->stopAllAnim();
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
		m_pActor->playAnim(SEQ_VACANT1_ANNIM9);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect("mob_100068_csxl", true);
		}
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.portal", 1.0f, 1.0f);
		}
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb_Pre, this);
		GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb, 1500, this);

		if (m_paramObj.has<jsonxx::Number>("tp_hp_offset"))
			GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb_Select, m_paramObj.get<jsonxx::Number>("tp_hp_offset"), this);
		else
			GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb_Select, 500, this);
	}
	else if (dwTimerID == Fly_Sheer_Transmit_Absorb_Select)
	{
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb_Select, this);

		ClientPlayer *player = m_pActor->getActorMgr()->findPlayerByUin(m_Uin);
		if (player)
		{
			int range = 200;
			if (m_paramObj.has<jsonxx::Number>("tp_hp_show_range"))
				range = m_paramObj.get<jsonxx::Number>("tp_hp_show_range");
			Rainbow::Vector3f dir_ = player->getPosition().toVector3() - m_pActor->getPosition().toVector3();
			dir_.y = 0;
			dir_  = MINIW::Normalize(dir_);
			dir_ *= range;
			m_TargetWCoord = WCoord(player->getPosition().x + dir_.x, player->getPosition().y, player->getPosition().z + dir_.z);

			Rainbow::Vector3f dir = m_TargetWCoord.toVector3() - m_pActor->getPosition().toVector3();
			dir  = MINIW::Normalize(dir);
			float yaw, pitch;
			Direction2PitchYaw(&yaw, &pitch, dir);
			//����������Ѱ��һ���ɴ��͵�Ŀ��㡢������һ����������Ч
			WCoord wcd = WCoord(m_TargetWCoord.x, m_TargetWCoord.y + 250, m_TargetWCoord.z);
			m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_delivery.ent", wcd, 40, yaw + 90.0f, 0, true, 100);
		}
	}
	else if (dwTimerID == Fly_Sheer_Transmit_Absorb)
	{
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_pActor->getPosition(), 20, 0, 0, true, 100);
		m_pActor->playAnim(SEQ_VACANT1_ANNIM10);
		//����
		m_pActor->setPosition(m_TargetWCoord);
		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_chlz.ent", m_TargetWCoord, 20, 0, 0, true, 100);

		ClientPlayer *player = m_pActor->getActorMgr()->findPlayerByUin(m_Uin);
		if (player)
		{
			WCoord pos = m_pActor->getPosition();
			Rainbow::Vector3f dir = player->getPosition().toVector3() - m_pActor->getPosition().toVector3();
			dir  = MINIW::Normalize(dir);
			if (m_paramObj.has<jsonxx::Number>("tp_hp_offset"))
				dir *= m_paramObj.get<jsonxx::Number>("tp_hp_offset");
			else
				dir *= 100;

			if (m_paramObj.has<jsonxx::Number>("tp_hp_rush_speed"))
				m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("tp_hp_rush_speed");
			else
				m_pLivingLocomotion->m_SpeedMultiple = 10.0;

			m_pLivingLocomotion->m_HasTarget = true;
			m_pLivingLocomotion->m_MoveTarget = WCoord(m_pActor->getPosition().x + dir.x, m_pActor->getPosition().y + dir.y, m_pActor->getPosition().z + dir.z);
			m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);
			GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb, this);
			GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb_, 1000, this);//�ȴ�ǰ�˽���
		}
		else
		{
			GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb, this);
			GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
		}
	}
	else if (dwTimerID == Fly_Sheer_Transmit_Absorb_)
	{
		GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb_, this);
		GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
	}
	else if (dwTimerID == Fly_Sheer_Absorbing)
	{
		//ץȡ��������
		m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM11);
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("boss_10068_zq", true);
		}
		m_EmptyControlCount--;
		if (m_tpHpObj.size() == 0)
		{
			m_EmptyControlCount = 0;
			int buffId = 210001; //���boss��ȡ
			if (m_paramObj.has<jsonxx::Number>("tp_hp_defense_buffId"))
				buffId = m_paramObj.get<jsonxx::Number>("tp_hp_defense_buffId");
			m_pActor->getLivingAttrib()->removeBuff(buffId);
			auto effectComponent = m_pActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->stopBodyEffect("boss_10068_zq", true);
			}
			GetSandBoxManager().KillTimer(Fly_Sheer_Absorbing, this);
			GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
			GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
			m_Uin = 0;
			return;
		}
		//�Է�Χ�������ȡ
		for (size_t i = 0; i < m_tpHpObj.size(); i++)
		{
			ClientActor *target = m_pActor->getActorMgr()->findActorByWID(m_tpHpObj[i]);
			if (target == NULL || target == m_pActor || target->getObjId() == m_pActor->getObjId()) continue;
			ClientPlayer *pl = dynamic_cast<ClientPlayer*>(target);
			if (pl == NULL) continue;
			PlayerAttrib *attr = pl->getPlayerAttrib();
			if (attr)
			{
				int subhp = 0;
				int suckHp = 10;
				int transformPec = 2;
				if (m_paramObj.has<jsonxx::Number>("tp_hp_suck_hp"))
					suckHp = m_paramObj.get<jsonxx::Number>("tp_hp_suck_hp");
				if (m_paramObj.has<jsonxx::Number>("tp_hp_suck_transform"))
					transformPec = m_paramObj.get<jsonxx::Number>("tp_hp_suck_transform");
				if (!(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
				{
					//����ȡ����ֵ��ֻ��ȡhp
					subhp = suckHp;
					if (attr->getHP() < subhp)
					{
						subhp = attr->getHP();
					}
					attr->addHP(-subhp);
				}
				m_pActor->getAttrib()->addHP(transformPec * subhp);
			}
			//�����Һ�boss�����������ͽ��
			float levelRange = 200.0f;
			if (m_paramObj.has<jsonxx::Number>("tp_hp_level_range"))
			levelRange = m_paramObj.get<jsonxx::Number>("tp_hp_level_range");
			float boss_width = m_pActor->getLocoMotion()->m_BoundSize / 2;
			float player_width = pl->getLocoMotion()->m_BoundSize / 2;
			WCoord pos = m_pActor->getPosition();
			WCoord pos1 = pl->getPosition();
			double vecx = pos.x - pos1.x;
			double vecy = pos.y - pos1.y;
			double vecz = pos.z - pos1.z;
			if ((vecx*vecx + vecy * vecy + vecz * vecz) > ((boss_width / 2 + player_width / 2 + levelRange)
				*(boss_width / 2 + player_width / 2 + levelRange)) || m_EmptyControlCount <= 0)
			{
				int buffId = 210001; //���boss��ȡ
				int buffid2 = EMPTY_CONTROL_BUFF;
				if (m_paramObj.has<jsonxx::Number>("tp_hp_defense_buffId"))
					buffId = m_paramObj.get<jsonxx::Number>("tp_hp_defense_buffId");
				if (m_paramObj.has<jsonxx::Number>("tp_hp_buffId"))
					buffid2 = m_paramObj.get<jsonxx::Number>("tp_hp_buffId");
				pl->getLivingAttrib()->removeBuff(buffid2);
				m_pActor->getLivingAttrib()->removeBuff(buffId);
				auto effectComponent = m_pActor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("boss_10068_zq", true);
				}
				if (m_pActor->getBody())
					m_pActor->getBody()->stopAllAnim();
				m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM14);
				GetSandBoxManager().KillTimer(Fly_Sheer_Absorbing, this);
				GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
				GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
				m_Uin = 0;
				m_tpHpObj.clear();
				break;
			}
		}
			
	}
	else if (dwTimerID == Fly_End_Start)
	{
		GetSandBoxManager().KillTimer(Fly_End_Start, this);
		appeal(true);
	}
	else if (dwTimerID == Fly_Boss_Die)
	{
		if (GetWorldManagerPtr() && m_pActor->getWorld())
			GetWorldManagerPtr()->removeBossPoint(m_pActor->getWorld()->getCurMapID());

		ClientVacantBoss* boss = dynamic_cast<ClientVacantBoss *>(m_pActor);
		if (boss)
			boss->BossStageOneEnd();
		GetSandBoxManager().KillTimer(Fly_Boss_Die, this);
		m_pActor->setNeedClear(5);
	}
	else if (dwTimerID == Fly_Boss_Show)
	{
		WCoord wcoord = BlockBottomCenter(m_InitWCoord);
		m_pActor->setPosition(wcoord);
		WCoord pos = wcoord;
		pos.z += 10;//��̳��������
		Rainbow::Vector3f dir = pos.toVector3() - wcoord.toVector3();
		float yaw, pitch;
		Direction2PitchYaw(&yaw, &pitch, dir);
		m_pLivingLocomotion->m_RotationPitch = pitch;
		m_pLivingLocomotion->m_RotateYaw = yaw;

		//���ź�ж�������Ч
		auto effectComponent = m_pActor->getEffectComponent();
		if (effectComponent)
		{
			effectComponent->playBodyEffect("mob_100068_bossRoar", true);
		}
		m_pActor->playAnim(SEQ_VACANT1_ANNIM2);
		auto sound = m_pActor->getSoundComponent();
		if (sound)
		{
			sound->playSound("ent.3515.roar", 3.0f, 1.0f);
		}
		GetSandBoxManager().SetTimer(Fly_End_Start, 2500, this);
		GetSandBoxManager().KillTimer(Fly_Boss_Show, this);
	}
	else if (dwTimerID == Fly_Stand_Out)
	{
		Rainbow::Vector3f dir = m_standPos.toVector3() - m_pActor->getPosition().toVector3();
		if (isBeBlockMove())
		{
			if (!GetSandBoxManager().hasTimer(Fly_Add_Fire2, this))
				m_standOutTime += 1;
			else
				m_standOutTime = 0;
		}
		else if (dir.Length() < 100)
		{
			//һ���ڶ�û�ƶ�һ��
			if (!GetSandBoxManager().hasTimer(Fly_Add_Fire2, this))
				m_standOutTime += 1;
			else
				m_standOutTime = 0;
			if (GetSandBoxManager().hasTimer(Fly_Guard, this) && m_standOutTime > 3)
				m_pLivingLocomotion->m_HasTarget = false;
		}
		else
		{
			m_standOutTime = 0;
			m_standPos = m_pActor->getPosition().toVector3();
		}
		//����֮��
		int timerList[6] = {
			Fly_Sheer_Absorbing,Fly_Boss_Die,Fly_Down,Fly_Rush_Down,Fly_Rush_UP,Fly_Sheer_Transmit
		};
		for (int i = 0; i < 6; i++)
		{
			if (GetSandBoxManager().hasTimer(timerList[i], this))
				m_standOutTime = 0;
		}

		int time = 10;
		if (m_paramObj.has<jsonxx::Number>("stand_time_out"))
			time = m_paramObj.get<jsonxx::Number>("stand_time_out") / 1000;
		if (m_standOutTime >= time)
		{
			m_standOutTime = 0;
			Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();

			if (dir.Length() > 5000)
			{
				WCoord wcoord = m_InitWCoord;
				wcoord += WCoord(0, 10, 0);
				wcoord = BlockBottomCenter(wcoord);
				//����Ҹ�λ��
				WCoord m_ValidPos;
				int count = 100;
				while (count > 0)
				{
					if (m_pActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 100, 100, NULL))
					{
						double vecx = wcoord.x - m_ValidPos.x;
						double vecy = wcoord.y - m_ValidPos.y;
						double vecz = wcoord.z - m_ValidPos.z;
						if ((vecx*vecx + vecy * vecy + vecz * vecz) < (2000.0f*2000.0f))
						{
							m_pLivingLocomotion->m_SpeedMultiple = 1.0;
							m_pLivingLocomotion->m_HasTarget = true;
							m_pLivingLocomotion->m_MoveTarget = m_ValidPos;
							m_pLivingLocomotion->setBehaviorOn(BehaviorType::Pursuit);
							break;
						}
					}
					count--;
				}
			}

			sheerTransmit(m_pLivingLocomotion->m_MoveTarget);
			if (m_paramObj.has<jsonxx::Number>("tp_elude_duration"))
				GetSandBoxManager().SetTimer(Fly_Sheer_Transmit1, m_paramObj.get<jsonxx::Number>("tp_elude_duration"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Sheer_Transmit1, 1000, this);
		}
	}
}

bool AIVacant::isBlockCanDestroy(const WCoord& blockpos)
{
	int blockdata = m_pActor->getWorld()->getBlockData(blockpos);
	BlockMaterial *material = m_pActor->getWorld()->getBlockMaterial(blockpos);
	if (material &&
		(material->getDestroyHardness(blockdata, NULL) >= 0 || material->getBlockResID() == 0))
	{
		return true;
	}
	else
	{
		return false;
	}
}

void AIVacant::OnExecute(unsigned short wEventID, unsigned char bSrcType, unsigned long dwSrcID, char* pszContext, int nLen)
{
	if (!m_pActor->getWorld())
	{
		return;
	}
	if (wEventID == EVENT_ACTOR_ATTRIB && bSrcType == ACTOR_CHANGE_HP)
	{
		float hp = *(float*)pszContext;
		if (hp < 0)
		{
			float percent = 0.3f;
			if (m_paramObj.has<jsonxx::Number>("flee_hp"))
				percent = m_paramObj.get<jsonxx::Number>("flee_hp") / 100;

			float curHp = m_pActor->getLivingAttrib()->getHP();
			float maxHp = m_pActor->getLivingAttrib()->getMaxHP();
			if ((curHp + hp) < (maxHp * percent))
			{
				//- һ�׶�bossʣ��30%����ֵʱ����������ڶ��׶�--�����˳��������������
				//- ���ܶ����޷�����ϣ������Ź������Ի��ܵ��˺���Ѫ���̳�����һ�׶�
				//todo�����˳�����
				
				clearTimer();
				m_pLivingLocomotion->m_SpeedMultiple = 0.0f;
				if (m_pActor->getBody())
					m_pActor->getBody()->stopAllAnim();
				auto effectComponent = m_pActor->getEffectComponent();
				if (effectComponent)
				{
					effectComponent->stopBodyEffect("mob_100068_boss_xs", true);
					effectComponent->stopBodyEffect("mob_100068_xr", true);
				}
				m_pActor->playAnim(SEQ_VACANT1_ANNIM18);
				auto sound = m_pActor->getSoundComponent();
				if (sound)
				{
					sound->playSound("ent.3515.roar", 3.0f, 1.0f);
				}
				m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/mob_100068_bosssw.ent", m_pActor->getPosition(), 60,0, 0, true, 100);
				if (m_paramObj.has<jsonxx::Number>("flee_time"))
					GetSandBoxManager().SetTimer(Fly_Boss_Die, m_paramObj.get<jsonxx::Number>("flee_time"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Boss_Die, 5000, this);

				GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_pActor->getObjId() & 0xffffffff);
			}

			DecHp dechp;
			dechp.hp = -hp;
			dechp.time = GetSandBoxManager().getTickTime();
			m_DecHps.push_back(dechp);

			int hpcount = 0;
			int tpEludeTime = 3000;//�������ܵ��˺��ۼ�ʱ��
			if (m_paramObj.has<jsonxx::Number>("tp_elude_time"))
				tpEludeTime = m_paramObj.get<jsonxx::Number>("tp_elude_time");

			auto iter = m_DecHps.begin();
			while (iter != m_DecHps.end())
			{
				if (GetSandBoxManager().getTickTime() - (*iter).time > tpEludeTime)
				{
					iter = m_DecHps.erase(iter);
				}
				else
				{
					hpcount += (*iter).hp;
					iter++;
				}
			}

			int tpEludeDamage = 100;//�������ܵ��ۼ��˺���
			if (m_paramObj.has<jsonxx::Number>("tp_elude_damage"))
				tpEludeDamage = m_paramObj.get<jsonxx::Number>("tp_elude_damage");
			if (hpcount > tpEludeDamage && m_bFly_RushCD && (GetSandBoxManager().hasTimer(Fly_Guard, this)))
			{
				//�������Ͷ��
				WCoord wcoord = m_InitWCoord;
				wcoord += WCoord(0, 10, 0);
				wcoord = BlockBottomCenter(wcoord);
				//����Ҹ�λ��Ѳ��
				WCoord m_ValidPos;
				WCoord m_ValidPos_ = m_pActor->getPosition();
				m_ValidPos_.y += 500;
				int count = 200;
				float minRange = 1000;
				float maxRange = 2000;
				if (m_paramObj.has<jsonxx::Number>("tp_elude_min_range"))
					minRange = m_paramObj.get<jsonxx::Number>("tp_elude_min_range");
				if (m_paramObj.has<jsonxx::Number>("tp_elude_max_range"))
					maxRange = m_paramObj.get<jsonxx::Number>("tp_elude_max_range");
				while (count > 0)
				{
					if (m_pActor->getLocoMotion()->findRandTargetBlock(m_ValidPos, 100, 100, NULL))
					{
						double vecx = wcoord.x - m_ValidPos.x;
						double vecy = wcoord.y - m_ValidPos.y;
						double vecz = wcoord.z - m_ValidPos.z;
						double vecx2 = m_ValidPos_.x - m_ValidPos.x;
						double vecy2 = m_ValidPos_.y - m_ValidPos.y;
						double vecz2 = m_ValidPos_.z - m_ValidPos.z;
						if ((vecx * vecx + vecy * vecy + vecz * vecz) < (maxRange * maxRange) && (vecx2 * vecx2 + vecy2 * vecy2 + vecz2 * vecz2) > (minRange * minRange) && wcoord.y <= m_ValidPos.y)
						{
							m_ValidPos_ = m_ValidPos;
							break;
						}
					}
					count--;
				}
				sheerTransmit(m_ValidPos_);

				if (m_paramObj.has<jsonxx::Number>("tp_elude_duration"))
					GetSandBoxManager().SetTimer(Fly_Sheer_Transmit, m_paramObj.get<jsonxx::Number>("tp_elude_duration"), this);
				else
					GetSandBoxManager().SetTimer(Fly_Sheer_Transmit, 1000, this);
				m_DecHps.clear();
				GetSandBoxManager().KillTimer(Fly_Guard, this);
			}

			m_DechpAdd += -hp;
			int tpHpDamagePercent = 10;
			if (m_paramObj.has<jsonxx::Number>("tp_hp_damage"))
				tpHpDamagePercent = m_paramObj.get<jsonxx::Number>("tp_hp_damage");
			if (m_DechpAdd > (m_pActor->getLivingAttrib()->getMaxHP() * tpHpDamagePercent / 100) && m_bFly_RushCD
				&& (GetSandBoxManager().hasTimer(Fly_Guard, this)))
			{
				ClientPlayer* player = findOnePlayer();
				if (player)
				{
					int tpEffTime = 3000;
					int tpEffDistance = 200;
					int tpEffShowRange = 100;
					if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_time"))
						tpEffTime = m_paramObj.get<jsonxx::Number>("tp_elude_effect_time") / 50;
					if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_distance"))
						tpEffDistance = m_paramObj.get<jsonxx::Number>("tp_elude_effect_distance");
					if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_show_range"))
						tpEffShowRange = m_paramObj.get<jsonxx::Number>("tp_elude_effect_show_range");

					Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();
					dir  = MINIW::Normalize(dir);
					float yaw, pitch;
					Direction2PitchYaw(&yaw, &pitch, dir);
					dir.y = 0;
					dir *= tpEffDistance;
					WCoord wcd = WCoord(m_pActor->getPosition().x + dir.x, m_pActor->getPosition().y + 250 + dir.y, m_pActor->getPosition().z + dir.z);

					//ԭ������һ��������
					m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_delivery.ent", wcd, tpEffTime, yaw + 90.0f, 0, true, tpEffShowRange);
					m_pLivingLocomotion->m_MoveTarget = m_pActor->getPosition();// �������Ͳ���Ҫ�ƶ���
					resetMove();
					auto effectComponent = m_pActor->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect("mob_100068_csxl", true);
					}
					m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM8);

					//ge GetGameEventQue().postGameDialogue(85616);
					const char* info = GetDefManagerProxy()->getStringDef(85616);
					MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
						SetData_String("info", info);
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
						MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_GAME_DIALOGUE", sandboxContext);
					//����������ȡ
					m_DechpAdd = 0;
					if (m_paramObj.has<jsonxx::Number>("tp_hp_duration"))
						GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb_Pre, m_paramObj.get<jsonxx::Number>("tp_hp_duration"), this);
					else
						GetSandBoxManager().SetTimer(Fly_Sheer_Transmit_Absorb_Pre, 1000, this);
					GetSandBoxManager().KillTimer(Fly_Guard, this);
					GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
					m_Uin = player->getUin();
				}
			}
		}
	}
	else if (wEventID == EVENT_ACTOR_COLLIDE && GetSandBoxManager().hasTimer(Fly_Sheer_Transmit_Absorb_, this))
	{
		unsigned int uin = *(unsigned int*)pszContext;
		ClientPlayer *player = m_pActor->getActorMgr()->findPlayerByUin(uin);
		if (player)
		{
			//���Ӹ��ٻ�buff
			int buffId = EMPTY_CONTROL_BUFF;
			int buffLv = 1;
			int buffTime = 100;// 20��λһ
			if (m_paramObj.has<jsonxx::Number>("tp_hp_buffId"))
				buffId = m_paramObj.get<jsonxx::Number>("tp_hp_buffId");
			if (m_paramObj.has<jsonxx::Number>("tp_hp_buffLv"))
				buffLv = m_paramObj.get<jsonxx::Number>("tp_hp_buffLv");
			if (m_paramObj.has<jsonxx::Number>("tp_hp_buffTime"))
				buffTime = m_paramObj.get<jsonxx::Number>("tp_hp_buffTime") / 50;
			if (m_paramObj.has<jsonxx::Number>("tp_hp_suck_time"))
				m_EmptyControlCount = m_paramObj.get<jsonxx::Number>("tp_hp_suck_time");
			else
				m_EmptyControlCount = 5;

			//�Է�Χ�������ȡ
			Rainbow::Vector3f dir = Yaw2FowardDir(m_pActor->getLocoMotion()->m_RotateYaw);
			dir.y = 0.02f;
			int range = 200;
			int heigt = 100;
			if (m_paramObj.has<jsonxx::Number>("tp_hp_player_range"))
				range = m_paramObj.get<jsonxx::Number>("tp_hp_player_range");
			if (m_paramObj.has<jsonxx::Number>("tp_hp_player_height"))
				heigt = m_paramObj.get<jsonxx::Number>("tp_hp_player_height");
			std::vector<ClientActor *> tpHpActors;
			m_pActor->getLocoMotion()->getFanShapedAreaFacedActors(tpHpActors, dir, range, heigt, 360);
			m_tpHpObj.clear();
			for (size_t i = 0; i < tpHpActors.size(); i++)
			{
				ClientActor *target = tpHpActors[i];
				if (target == NULL || target == m_pActor || target->getObjId() == m_pActor->getObjId()) continue;
				m_tpHpObj.push_back(tpHpActors[i]->getObjId());
			}
			for (size_t i = 0; i < m_tpHpObj.size(); i++)
			{
				ClientActor *target = m_pActor->getActorMgr()->findActorByWID(m_tpHpObj[i]);
				if (target == NULL || target == m_pActor || target->getObjId() == m_pActor->getObjId()) continue;
				ClientPlayer *pl = dynamic_cast<ClientPlayer*>(target);
				if (pl)
					pl->getLivingAttrib()->addBuff(buffId, buffLv, buffTime);
			}

			//��Ѫ�ڼ䣬boss��������
			int buffId2 = 210001; //���boss��ȡ
			int buffLv2 = 1;
			int buffTime2 = 100;//20��λһ��
			if (m_paramObj.has<jsonxx::Number>("tp_hp_defense_buffId"))
				buffId2 = m_paramObj.get<jsonxx::Number>("tp_hp_defense_buffId");
			if (m_paramObj.has<jsonxx::Number>("tp_hp_defense_buffLv"))
				buffLv2 = m_paramObj.get<jsonxx::Number>("tp_hp_defense_buffLv");
			if (m_paramObj.has<jsonxx::Number>("tp_hp_defense_buffTime"))
				buffTime2 = m_paramObj.get<jsonxx::Number>("tp_hp_defense_buffTime") / 50;
			m_pActor->getLivingAttrib()->addBuff(buffId2, buffLv2, buffTime2);

			auto sound = m_pActor->getSoundComponent();
			if (sound)
			{
				sound->playSound("ent.3515.control", 1.0f, 1.0f);
			}
			GetSandBoxManager().KillTimer(Fly_Sheer_Transmit_Absorb_, this);
			if (m_paramObj.has<jsonxx::Number>("tp_hp_suck_hz"))
				GetSandBoxManager().SetTimer(Fly_Sheer_Absorbing, m_paramObj.get<jsonxx::Number>("tp_hp_suck_hz"), this);
			else
				GetSandBoxManager().SetTimer(Fly_Sheer_Absorbing, 1000, this);

			GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
			m_Uin = player->getUin();
			GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin, "speed change");
		}
	}
	else if (wEventID == EVENT_MOD_ATTRIB && bSrcType == MODATTR_MOVE_SPEED)
	{
		/*float v = *(float*)pszContext;
		ClientPlayer *player = m_pActor->getActorMgr()->findPlayerByUin(dwSrcID);
		if (player && v > 0)
		{
			player->getLivingAttrib()->removeBuff(EMPTY_CONTROL_BUFF);
			GetSandBoxManager().KillTimer(Fly_Sheer_Absorbing, this);
			GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
			GetSandBoxManager().unSubscibe_(this, SandBoxMgrEventID::EVENT_MOD_ATTRIB, MODATTR_MOVE_SPEED, m_Uin);
			m_Uin = 0;
		}*/
	}
	else if (wEventID == EVENT_ACTOR_APPEAL)
	{
		//todo���ų�������������
		int time = 1000;
		if (m_paramObj.has<jsonxx::Number>("appeal_time"))
			time = m_paramObj.get<jsonxx::Number>("appeal_time");

		m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/bossblock1_room.ent", m_pActor->getPosition(), 60, 0, 0, true, 100);
		//����boss
		WCoord pos = m_InitWCoord;
		pos = BlockBottomCenter(m_InitWCoord);
		pos.y -= 10000;
		m_pActor->setPosition(pos);

		m_tpHpObj.clear();
		m_rushObj.clear();


		//���ö�������ʱ�䣬������ʼ��ʽ�ǳ�		
	
		GetSandBoxManager().SetTimer(Fly_Boss_Show, time, this);
	}
	else if (wEventID == EVENT_ACTOR_APPEAL_)
	{
		bool first = *(bool*)pszContext;
		appeal(true);
	}
}

void AIVacant::appeal(bool first)
{
	if (!m_bBorn)
	{
		m_bBorn = true;
		ClientVacantBoss* boss = dynamic_cast<ClientVacantBoss *>(m_pActor);
		if (boss)
			m_InitWCoord = CoordDivBlock(boss->getSpawnPoint());
		if (first)
		{
			
			//ǿ�����BOSS������������з���
			WCoord wcoord = m_InitWCoord;
			
			g_EnableReLighting = false;
			int range = 20;
			int height = 10;
			int downHeight = 5;
			if (m_paramObj.has<jsonxx::Number>("appeal_clear_range"))
				range = m_paramObj.get<jsonxx::Number>("appeal_clear_range") / 100;
			if (m_paramObj.has<jsonxx::Number>("appeal_clear_height"))
				height = m_paramObj.get<jsonxx::Number>("appeal_clear_height") / 100;
			if (m_paramObj.has<jsonxx::Number>("appeal_clear_floor"))
				downHeight = m_paramObj.get<jsonxx::Number>("appeal_clear_floor") / 100;

			wcoord += WCoord(0, downHeight, 0);
			for (int range_x = -range; range_x <= range; range_x++)
			{
				for (int range_y = 0; range_y <= height; range_y++)
				{
					for (int range_z = -range; range_z <= range; range_z++)
					{
						if ((range_x*range_x + range_y * range_y + range_z * range_z) <= (range * range))
							m_pActor->getWorld()->setBlockAll(WCoord(wcoord.x + range_x, wcoord.y + range_y, wcoord.z + range_z), 0, 0);
					}
				}
			}
			g_EnableReLighting = true;
			//5���ٶ����Ϸ�
			if (m_paramObj.has<jsonxx::Number>("appeal_height_speed"))
				m_pLivingLocomotion->m_SpeedMultiple = m_paramObj.get<jsonxx::Number>("appeal_height_speed");
			else
				m_pLivingLocomotion->m_SpeedMultiple = 10.0f;
			m_pLivingLocomotion->m_HasTarget = true;
			int distance = 1000;
			if (m_paramObj.has<jsonxx::Number>("appeal_height_distance"))
				distance = m_paramObj.get<jsonxx::Number>("appeal_height_distance");
			m_pLivingLocomotion->m_MoveTarget = WCoord(m_pActor->getPosition().x, m_pActor->getPosition().y + distance, m_pActor->getPosition().z);
			m_pLivingLocomotion->setBehaviorOn(BehaviorType::LashTagert);

			GetSandBoxManager().SetTimer(Fly_Up, 50, this);
			GetSandBoxManager().SetTimer(Fly_Stand_Out, 1000, this);
		}
		else
		{
			GetSandBoxManager().SetTimer(Fly_Guard, 50, this);
		}
	}
}

bool AIVacant::isBeBlockMove()
{
	//�Ƿ������ǰ�ƶ�
	CollideAABB box;
	m_pActor->getLocoMotion()->getCollideBox(box);
	Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();

	if (dir.x > -0.000001 && dir.x < 0.000001
		&& dir.y > -0.000001 && dir.y < 0.000001
		&& dir.z > -0.000001 && dir.z < 0.000001)
	{

	}
	else {
		dir  = MINIW::Normalize(dir);
		
	}
	dir *= 10.0f;
	WCoord mvec = m_pLivingLocomotion->getIntegerMotion(dir);
	WCoord wcoord = m_pActor->getWorld()->moveBox(box, mvec);
	if (wcoord.x == 0 && wcoord.y == 0 && wcoord.z == 0)
	{
		return true;
	}
	if (m_pLivingLocomotion->m_HasTarget == false)
	{
		return true;
	}
	return false;
}

bool AIVacant::isLowHp()
{
	float percent = 0.5f;
	if (m_paramObj.has<jsonxx::Number>("low_hp"))
		percent = m_paramObj.get<jsonxx::Number>("low_hp") / 100;
	if (m_pActor->getLivingAttrib()->getHP() / m_pActor->getLivingAttrib()->getMaxHP() <= percent)
	{
		return true;
	}
	else
	{
		return false;
	}
}

void AIVacant::start()
{
	GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_ATTRIB, SandBoxMgrTypeID::ACTOR_CHANGE_HP, m_pActor->getObjId() & 0xffffffff, "hp change!");
	GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_COLLIDE, 0, m_pActor->getObjId() & 0xffffffff, "collideWithActor!");

	GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_APPEAL, 0, m_pActor->getObjId() & 0xffffffff, "Vacantstart!");
	GetSandBoxManager().subscibe_(this, SandBoxMgrEventID::EVENT_ACTOR_APPEAL_, 0, m_pActor->getObjId() & 0xffffffff, "Vacant start 2!");
	m_InitWCoord = CoordDivBlock(m_pActor->getPosition());
}

static bool selectActorsFunc(ClientActor *actor, void *userdata)
{
	if (!actor->isDead())
	{
		return true;
	}
	else
	{
		return false;
	}
}

ClientPlayer* AIVacant::findOnePlayer()
{
	ClientVacantBoss* mob = dynamic_cast<ClientVacantBoss*>(m_pActor);
	std::vector<ClientPlayer *> players;
	if (mob && m_pActor->getActorMgr())
		m_pActor->getActorMgr()->selectNearAllPlayers(players, m_pActor->getPosition(), mob->getViewDist() * BLOCK_FSIZE/*��޾���*/, selectActorsFunc);
	if (players.size())
	{
		int i = GenRandomInt(players.size());
		return players[i];
	}
	else
	{
		return NULL;
	}
}

void AIVacant::resetMove()
{
	m_pLivingLocomotion->m_SpeedMultiple = 1.0f;
	m_pLivingLocomotion->m_HasTarget = false;
	m_pLivingLocomotion->setBehaviorOff(BehaviorType::Pursuit);
	m_pLivingLocomotion->m_Velocity = Rainbow::Vector3f(0.0f, 0.0f, 0.0f);
}

void AIVacant::Release()
{
	delete this;
}

void AIVacant::playLoopSound(const char *name, float volume, float pitch)
{
	if (m_curLoopSnd)
		OGRE_RELEASE(m_curLoopSnd);
	if (name != NULL && name[0] && m_pActor->getWorld() && m_pActor->getLocoMotion())
	{
		WCoord pos = m_pActor->getLocoMotion()->getPosition();
		pos.y -= m_pActor->getLocoMotion()->m_yOffset;
		m_curLoopSnd = m_pActor->getWorld()->getEffectMgr()->playLoopSound(pos, name, volume, pitch);
	}
}

void AIVacant::sheerTransmit(WCoord pos)
{
	m_TargetWCoord = pos;
	int tpEffTime = 3000;
	int tpEffDistance = 200;
	int tpEffShowRange = 100;
	if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_time"))
		tpEffTime = m_paramObj.get<jsonxx::Number>("tp_elude_effect_time") / 50;
	if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_distance"))
		tpEffDistance = m_paramObj.get<jsonxx::Number>("tp_elude_effect_distance");
	if (m_paramObj.has<jsonxx::Number>("tp_elude_effect_show_range"))
		tpEffShowRange = m_paramObj.get<jsonxx::Number>("tp_elude_effect_show_range");

	Rainbow::Vector3f dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_pActor->getPosition().toVector3();
	dir  = MINIW::Normalize(dir);
	float yaw, pitch;
	Direction2PitchYaw(&yaw, &pitch, dir);
	dir.y = 0;
	dir *= tpEffDistance;
	WCoord wcd = WCoord(m_pActor->getPosition().x + dir.x, m_pActor->getPosition().y + 250 + dir.y, m_pActor->getPosition().z + dir.z);

	//ԭ������һ��������
	if (m_pActor->getWorld()->getEffectMgr())
	m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_delivery.ent", wcd, tpEffTime, yaw + 90.0f, 0, true, tpEffShowRange);

	//����������Ѱ��һ���ɴ��͵�Ŀ��㡢������һ����������Ч
	wcd = WCoord(m_TargetWCoord.x, m_TargetWCoord.y + 250, m_TargetWCoord.z);
	dir = m_pLivingLocomotion->m_MoveTarget.toVector3() - m_TargetWCoord.toVector3();
	Direction2PitchYaw(&yaw, &pitch, dir);
	if (m_pActor->getWorld()->getEffectMgr())
	m_pActor->getWorld()->getEffectMgr()->playParticleEffectAsync("particles/boss_10068_delivery.ent", wcd, tpEffTime, yaw + 90.0f, 0, true, tpEffShowRange);

	m_pLivingLocomotion->m_MoveTarget = m_pActor->getPosition();// �������Ͳ���Ҫ�ƶ���
	resetMove();
	auto effectComponent = m_pActor->getEffectComponent();
	if (effectComponent)
	{
		effectComponent->playBodyEffect("mob_100068_csxl", true);
	}
	m_pActor->setAnimBodyId(SEQ_VACANT1_ANNIM8);
}