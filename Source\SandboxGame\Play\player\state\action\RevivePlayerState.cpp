﻿#include "RevivePlayerState.h"
#include "PlayerControl.h"
#include "PCControl.h"
#include "InputInfo.h"
#include "Misc/InputEvent.h"
#include "ClientInfoProxy.h"
#include "Core/actors/actorAttrib/PlayerDownedStateAttrib.h"
#include "Core/actors/actorAttrib/PlayerAttrib.h"
#include "ClientPlayer.h"
#include "Core/World.h"
#include "WorldManager.h"
#include "GameNetManager.h"
#include "PlayerInputHelper.h"
#include "Math/Vector3f.h"

// 常量定义 (与PlayerDownedStateAttrib中保持一致)
const int REVIVE_TICKS = 100; // 5秒 * 20 ticks/秒

RevivePlayerState::RevivePlayerState(PlayerControl* host): PlayerState(host)
    , m_canRevivePlayer(false)
    , m_isReviving(false)
    , m_reviveTicksProgress(0)
    , m_revivingPlayerId(0)
    , m_maxReviveDistance(300.0f) // 默认3米救援距离
    , m_uiUpdateTick(0)
{
    m_StateID = "RevivePlayer";
}

RevivePlayerState::~RevivePlayerState()
{
}

void RevivePlayerState::doBeforeEntering()
{
}

std::string RevivePlayerState::update(float dtime)
{
    if (GetClientInfoProxy()->isPC())
    {
//#ifndef IWORLD_SERVER_BUILD
        // 客户端逻辑：检查附近是否有倒地玩家
        checkForDownedPlayers(m_Host);

        // 如果E键刚被按下，且有可救援玩家，开始救援
        long long targetId = 0;
        targetId = findNearestDownedPlayer(m_Host);

        if (targetId == 0 && m_isReviving)
        {
            stopRevivePlayer(m_Host);
            LOG_WARNING("RevivePlayerState ToActionIdle--");
            return "ToActionIdle";
        }

        bool eKeyHeld = m_Host->getPlayerInputHelper()->getEKeyDetector()->isKeyHeld();
        //bool eKeyUp = m_Host->getPlayerInputHelper()->getEKeyDetector()->isKeyUp();

        // 如果正在救援，停止救援
        if (!eKeyHeld && m_isReviving)
        {
            stopRevivePlayer(m_Host);
            LOG_WARNING("RevivePlayerState ToActionIdle--");
            return "ToActionIdle";
        }

        if (eKeyHeld && m_canRevivePlayer && !m_isReviving)
        {
            if (targetId > 0)
            {
                startRevivePlayer(m_Host, targetId);
            }
        }

        m_Host->m_ReviveProgress = getReviveProgressNormalized(targetId);
        //目标已经救活
        ClientActor* actor = dynamic_cast<ClientActor*>(m_Host->m_PickResult.actor);
        if (actor && actor->getPlayerAttrib() && actor->getPlayerAttrib()->getHP() > 0)
        {
            stopRevivePlayer(m_Host);
            LOG_WARNING("RevivePlayerState ToActionIdle--");
            return "ToActionIdle";
        }

//#endif

        // 更新InputInfo
        updateInputInfo(m_Host->m_InputInfo);
    }

	return "";
}

void RevivePlayerState::doBeforeLeaving()
{
    reset();
}

void RevivePlayerState::OnTick(float elapse)
{
    if (!m_Host)
        return;

#ifndef IWORLD_SERVER_BUILD
    // 定期更新UI
    m_uiUpdateTick++;
    if (m_uiUpdateTick >= 5) // 每5帧更新一次UI
    {
        updateReviveUI(m_Host);
        m_uiUpdateTick = 0;
    }

    // 如果不在救援状态，检查是否有可救援玩家并更新UI提示
    if (!m_isReviving)
    {
        checkForDownedPlayers(m_Host);
    }

    // 简化：每tick直接更新一次救援进度，无需计数器
    if (m_isReviving)
    {
        updateReviveProgress(1, m_Host); // 每次传递1表示1个tick
    }
#endif
}

void RevivePlayerState::updateInputInfo(InputInfo* inputInfo)
{
    if (inputInfo)
    {
        // 更新E键状态到InputInfo
        //inputInfo->keyEClick = m_isKeyHeld;            // 是否正在按住
        //inputInfo->keyEClickUp = m_isKeyUp;            // 是否刚释放
        //inputInfo->keyEShortPress = m_isShortPress;    // 是否短按
        //inputInfo->keyELongPressTrigger = m_isLongPress; // 是否长按
        //inputInfo->keyEPressTime = m_pressTime;        // 已按下时间
        //
        //// 更新救援相关状态
        //inputInfo->canRevivePlayer = m_canRevivePlayer;
        //inputInfo->isReviving = m_isReviving;
        //inputInfo->reviveProgress = getRevivePercentage(); // 转换为百分比
    }
}

void RevivePlayerState::reset()
{
    // 重置救援状态
    m_canRevivePlayer = false;
    m_isReviving = false;
    m_reviveTicksProgress = 0;
    m_revivingPlayerId = 0;
}

void RevivePlayerState::checkForDownedPlayers(PlayerControl* playerCtrl)
{
#ifndef IWORLD_SERVER_BUILD
    // 只在客户端执行此逻辑
    if (!playerCtrl)
    {
        m_canRevivePlayer = false;
        return;
    }

    // 如果已经在救援，不再检查其他玩家
    if (m_isReviving)
    {
        m_canRevivePlayer = true;
        return;
    }

    // 查找附近倒地的玩家
    long long nearestPlayerId = findNearestDownedPlayer(playerCtrl);
    m_canRevivePlayer = (nearestPlayerId > 0);

    // 如果状态有变化，强制更新UI
    static bool lastCanRevive = false;
    if (lastCanRevive != m_canRevivePlayer)
    {
        updateReviveUI(playerCtrl, true);
        lastCanRevive = m_canRevivePlayer;
    }
#endif
}

long long RevivePlayerState::findNearestDownedPlayer(PlayerControl* playerCtrl) const
{
#ifndef IWORLD_SERVER_BUILD
    if (!playerCtrl)
        return 0;

    ClientPlayer* currentPlayer = playerCtrl;
    float nearestDistance = m_maxReviveDistance;
    long long nearestPlayerId = 0;

    auto picktype = playerCtrl->doPick(false, false, false);
    if (picktype == 2)
    {
        auto pickactor = dynamic_cast<ClientPlayer*>(playerCtrl->m_PickResult.actor);
        if (pickactor && isPlayerDowned(pickactor))
        {
            nearestPlayerId = pickactor->getObjId();
        }
    }
    return  nearestPlayerId;
#else
    return 0;
#endif
}

float RevivePlayerState::getDistanceBetweenPlayers(ClientPlayer* currentPlayer, ClientPlayer* targetPlayer) const
{
    if (!currentPlayer || !targetPlayer)
        return 999.0f;

    // 计算两个玩家之间的距离
    Rainbow::Vector3f pos1 = currentPlayer->getPosition().toVector3();
    Rainbow::Vector3f pos2 = targetPlayer->getPosition().toVector3();

    // 计算距离
    float dx = pos1.x - pos2.x;
    float dy = pos1.y - pos2.y;
    float dz = pos1.z - pos2.z;

    return sqrt(dx * dx + dy * dy + dz * dz);
}

bool RevivePlayerState::isPlayerDowned(ClientPlayer* player) const
{
    if (!player)
        return false;

    // 获取玩家的属性组件
    PlayerAttrib* playerAttrib = player->getPlayerAttrib();
    if (!playerAttrib)
        return false;

    // 检查玩家是否处于倒地状态
    return playerAttrib->isPlayerDowned();
}

PlayerDownedStateAttrib* RevivePlayerState::getPlayerDownedStateAttrib(ClientPlayer* player) const
{
    if (!player)
        return nullptr;

    // 获取玩家的属性组件
    PlayerAttrib* playerAttrib = player->getPlayerAttrib();
    if (!playerAttrib)
        return nullptr;

    // 返回倒地状态组件
    return playerAttrib->getDownedStateAttrib();
}

bool RevivePlayerState::startRevivePlayer(PlayerControl* playerCtrl, long long playerId)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端逻辑
    if (!playerCtrl)
        return false;

    // 获取目标玩家
    ClientPlayer* targetPlayer = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(playerId));
    if (!targetPlayer || !isPlayerDowned(targetPlayer))
        return false;

    // 检查距离
    ClientPlayer* currentPlayer = playerCtrl;
    if (getDistanceBetweenPlayers(currentPlayer, targetPlayer) > m_maxReviveDistance)
        return false;

    // 发送开始救援请求到服务器
    sendReviveRequestToServer(playerCtrl, playerId);

    // 设置救援状态
    m_isReviving = true;
    m_revivingPlayerId = playerId;
    m_reviveTicksProgress = 0;

    // 更新UI
    updateReviveUI(playerCtrl, true);

    return true;
#else
    // 服务器逻辑 - 由网络消息触发，不在这里实现
    return false;
#endif
}

void RevivePlayerState::stopRevivePlayer(PlayerControl* playerCtrl)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端逻辑
    //if (!m_isReviving)
    //    return;

    // 向服务器发送取消救援请求
    sendCancelReviveToServer(playerCtrl);

    // 重置状态
    m_isReviving = false;
    m_reviveTicksProgress = 0;
    m_revivingPlayerId = 0;
    m_Host->m_ReviveProgress = -1;

    // 更新UI
    updateReviveUI(playerCtrl, true);
#else
    // 服务器逻辑 - 由网络消息触发，不在这里实现
#endif
}

void RevivePlayerState::updateReviveProgress(int ticksElapsed, PlayerControl* playerCtrl)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端逻辑
    if (!m_isReviving || m_revivingPlayerId == 0)
        return;

    // 检查救援条件是否仍然满足
    ClientPlayer* currentPlayer = playerCtrl;
    ClientPlayer* targetPlayer = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(m_revivingPlayerId));

    if (!targetPlayer || !isPlayerDowned(targetPlayer) ||
        getDistanceBetweenPlayers(currentPlayer, targetPlayer) > m_maxReviveDistance)
    {
        stopRevivePlayer(playerCtrl);
        return;
    }

    // 更新救援进度（以tick为单位）
    m_reviveTicksProgress += ticksElapsed;

    // 每10个tick（约0.5秒）向服务器发送一次进度更新
    if (m_reviveTicksProgress % 10 == 0 || m_reviveTicksProgress == REVIVE_TICKS)
    {
        sendReviveProgressToServer(playerCtrl, m_reviveTicksProgress);
    }

    // 更新UI (每5帧更新一次，避免频繁更新)
    if (m_uiUpdateTick == 0)
    {
        updateReviveUI(playerCtrl);
    }

    // 进度达到100个tick（5秒），完成救援
    if (m_reviveTicksProgress >= REVIVE_TICKS)
    {
        // 发送完成救援请求到服务器
        // 注意：实际的救援完成逻辑由服务器处理并通知所有客户端
        PB_PlayerReviveRequestCH msg;
        msg.set_player_id(currentPlayer->getObjId());
        msg.set_target_id(m_revivingPlayerId);
        msg.set_action(3); // 3表示完成救援
        GetGameNetManagerPtr()->sendToHost(PB_PLAYER_REVIVE_REQUEST_CH, msg);

        // 重置状态
        m_isReviving = false;
        m_reviveTicksProgress = 0;
        m_revivingPlayerId = 0;

        // 更新UI
        updateReviveUI(playerCtrl, true);
    }
#else
    // 服务器逻辑 - 具体的进度更新由客户端发送消息触发
#endif
}

void RevivePlayerState::updateReviveUI(PlayerControl* playerCtrl, bool forceUpdate)
{
#ifndef IWORLD_SERVER_BUILD
    // 只在客户端更新UI
    if (!playerCtrl)
        return;

    ClientPlayer* player = playerCtrl;
    if (!player->hasUIControl() && !forceUpdate)
        return;

    // 创建UI上下文
    MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);

    if (m_isReviving)
    {
        // 正在救援
        uiContext.SetData_Number("type", 100); // 表示救援UI
        uiContext.SetData_Number("progress", getRevivePercentage()); // 转换为百分比
        uiContext.SetData_Number("player_id", m_revivingPlayerId);
        uiContext.SetData_Bool("is_reviving", true);
    }
    else if (m_canRevivePlayer)
    {
        // 有可救援的玩家，但未开始救援
        uiContext.SetData_Number("type", 101); // 表示可救援提示
        uiContext.SetData_Bool("can_revive", true);
    }
    else
    {
        // 无救援状态
        uiContext.SetData_Number("type", 102); // 表示隐藏救援UI
        uiContext.SetData_Bool("hide_revive_ui", true);
    }

    // 发送UI事件
    if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
    {
        MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_REVIVE", uiContext);
    }
#endif
}

void RevivePlayerState::sendReviveRequestToServer(PlayerControl* playerCtrl, long long targetId)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端向服务器发送救援请求
    if (!playerCtrl)
        return;

    // 创建救援请求消息
    PB_PlayerReviveRequestCH msg;
    msg.set_player_id(playerCtrl->getObjId());
    msg.set_target_id(targetId);
    msg.set_action(1); // 1表示开始救援

    // 发送消息
    GetGameNetManagerPtr()->sendToHost(PB_PLAYER_REVIVE_REQUEST_CH, msg);
#endif
}

void RevivePlayerState::sendCancelReviveToServer(PlayerControl* playerCtrl)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端向服务器发送取消救援请求
    if (!playerCtrl || m_revivingPlayerId == 0)
        return;

    // 创建取消救援请求消息
    PB_PlayerReviveRequestCH msg;
    msg.set_player_id(playerCtrl->getObjId());
    msg.set_target_id(m_revivingPlayerId);
    msg.set_action(2); // 2表示取消救援

    // 发送消息
    GetGameNetManagerPtr()->sendToHost(PB_PLAYER_REVIVE_REQUEST_CH, msg);
#endif
}

void RevivePlayerState::sendReviveProgressToServer(PlayerControl* playerCtrl, int progress)
{
#ifndef IWORLD_SERVER_BUILD
    // 客户端向服务器发送救援进度更新
    if (!playerCtrl || m_revivingPlayerId == 0)
        return;

    // 创建救援进度更新消息
    PB_PlayerReviveProgressCH msg;
    msg.set_player_id(playerCtrl->getObjId());
    msg.set_target_id(m_revivingPlayerId);
    msg.set_progress_ticks(progress);

    // 发送消息
    GetGameNetManagerPtr()->sendToHost(PB_PLAYER_REVIVE_PROGRESS_CH, msg);
#endif
}

int RevivePlayerState::getRevivePercentage() const
{
    if (REVIVE_TICKS == 0)
        return 0;
    return (m_reviveTicksProgress * 100) / REVIVE_TICKS;
}

float RevivePlayerState::getReviveProgressNormalized(long long targetId) const
{
    if (targetId > 0)
    {
        return  static_cast<float>(m_reviveTicksProgress) / REVIVE_TICKS;
    }
    if (!m_isReviving)
        return -1.0f;

    if (REVIVE_TICKS == 0)
        return 0.0f;
    return -1.0f;
}