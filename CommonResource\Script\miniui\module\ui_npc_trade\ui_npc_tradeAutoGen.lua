--文件来源：assets/socTrade/ui_npc_trade.xml
local ui_npc_tradeAutoGen = Class("ui_npc_tradeAutoGen",ClassList["MiniUICommonNodeClass"])

local ui_npc_tradeCtrl,ui_npc_tradeModel,ui_npc_tradeView = GetInst("MiniUIManager"):GetMVC("ui_npc_tradeAutoGen")

local t_NpcInfo = {
	[3010]={descid=118, iconid=21},
	[3011]={descid=119, iconid=20},
	[3012]={descid=120, iconid=22},
	[3013]={descid=3511, iconid=23},
	[3014]={descid=3512, iconid=24},
	[3015]={descid=3582, iconid=25},
	[3016]={descid=3829, iconid=26},
	[3017]={descid=960, iconid=27},
	[3018]={descid=264, iconid=28},
	[3019]={descid=86004, iconid=28},
	[3210]={descid=86022, iconid=28},
	[3211]={descid=86023, iconid=28},
	[3222]={descid=86030, iconid=28},
	[3223]={descid=86029, iconid=28},
	[3229]={descid=86028, iconid=28},
}

--初始化
function ui_npc_tradeAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("ui_npc_tradeAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	
	-- 初始化NPC交易数据
	self.ctrl.tradeNum = 1 -- 购买数量
	self.ctrl.npcId = OpenedContainerMob:getDef().ID
	
	-- 刷新界面
	self.ctrl:FreshAllGoods()
	
	-- 设置默认选中第一个有效商品
	if self.ctrl.goodsIndexMap and self.ctrl.goodsIndexMap[1] then
		self.ctrl.curSelect = self.ctrl.goodsIndexMap[1]
	else
		self.ctrl.curSelect = 1
	end
	
	-- 初始化货币图标（设置一次，后续不再变化）
	self.ctrl:InitCostIcon()
	
	self.ctrl:UpdateSelectedItem()
	self.ctrl:UpdateTradeInfo()
	
	self.firstInit = 0
end

--显示
function ui_npc_tradeAutoGen:onShow()
	self.ctrl:Refresh()
	--设置刷新循环 0.5 秒一次
	if not self.m_timer then
		self.m_timer = GetInst("MiniUIScheduler"):reg(function()
			self.ctrl:FreshAllGoods()
			self.ctrl:UpdateSelectedItem()
			self.ctrl:UpdateTradeInfo()
		end, 0.5, nil, 0.5, false)
	end
end

--隐藏
function ui_npc_tradeAutoGen:onHide()
	self:_Unreg()
	self.ctrl:Reset()
end

--移除
function ui_npc_tradeAutoGen:onRemove()
	self:_Unreg()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)
end

function ui_npc_tradeAutoGen:_Unreg()
	if self.m_timer then
		GetInst("MiniUIScheduler"):unreg(self.m_timer)
		self.m_timer = nil
	end
end

--Ctrl:注册UI事件
function ui_npc_tradeCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.ui_trade_item_list, UIEventType_ClickItem, function(obj, context)
 		if self.Ui_trade_item_listClickItem then
			self:Ui_trade_item_listClickItem(obj, context)
		end
	end)
	GetInst("MiniUIComponents"):setCallback(self.view.widgets.ui_trade_item_list, "GList.itemRenderer", function(comp, idx, obj)
 		if self.Ui_trade_item_listItemRenderer then
			self:Ui_trade_item_listItemRenderer(comp, idx, obj)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_dec, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_decClick then
			self:Btn_trade_decClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade_inc, UIEventType_Click, function(obj, context)
 		if self.Btn_trade_incClick then
			self:Btn_trade_incClick(obj, context)
		end
	end)
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.btn_trade, UIEventType_Click, function(obj, context)
 		if self.Btn_tradeClick then
			self:Btn_tradeClick(obj, context)
		end
	end)
end

-- UI事件处理函数
function ui_npc_tradeCtrl:Ui_trade_item_listClickItem(obj, context)
	local item = context:getData()
	local itemIndex = obj:getChildIndex(item)
	local listIndex = itemIndex + 1 -- 列表索引从0开始，转换为1开始
	self.curSelect = self.goodsIndexMap and self.goodsIndexMap[listIndex] or listIndex
	
	-- 切换商品时重置购买数量为1
	self.tradeNum = 1
	
	self:UpdateSelectedItem()
	self:UpdateTradeInfo()
end

function ui_npc_tradeCtrl:Ui_trade_item_listItemRenderer(comp, idx, obj)
	local listIndex = idx + 1 -- 列表索引从0开始，转换为1开始
	local actualIndex = self.goodsIndexMap and self.goodsIndexMap[listIndex] or listIndex
	self:FreshGoodsItem(obj, actualIndex) -- 渲染商品项
end

function ui_npc_tradeCtrl:Btn_trade_decClick(obj, context)
	if self.tradeNum > 1 then
		self.tradeNum = self.tradeNum - 1
		self:UpdateTradeInfo()
	end
end

function ui_npc_tradeCtrl:Btn_trade_incClick(obj, context)
	self.tradeNum = self.tradeNum + 1
	self:UpdateTradeInfo()
end

function ui_npc_tradeCtrl:Btn_tradeClick(obj, context)
	self:NpcTradeFrame_Exchange()
end

--View:获取需要操作的节点
function ui_npc_tradeView:GetHandleNodes()
	self.widgets={}
	--list
	self.widgets.ui_trade_item_list = self.root:getChild("ui_trade_item_list")
	--Button
	self.widgets.btn_trade_dec = self.root:getChild("btn_trade_dec")
	--Button
	self.widgets.btn_trade_inc = self.root:getChild("btn_trade_inc")
	--Button
	self.widgets.btn_trade = self.root:getChild("btn_trade")
	--UI elements for selected item display
	self.widgets.ui_trade_img = self.root:getChild("ui_trade_img")
	self.widgets.ui_trade_name = self.root:getChild("ui_trade_name")
	self.widgets.ui_trade_desc = self.root:getChild("ui_trade_desc")
	self.widgets.txt_trade_num = self.root:getChild("txt_trade_num")
	self.widgets.txt_trade_cost = self.root:getChild("txt_trade_cost")
	self.widgets.cost_icon = self.root:getChild("n16") -- 货币图标
end

-- 刷新所有商品
function ui_npc_tradeCtrl:FreshAllGoods()
	if ClientBackpack == nil then
		return
	end
	
	-- 建立有效商品索引映射
	self.goodsIndexMap = {}
	local goodsCount = 0
	
	for i = 1, 6 do
		local obtain_index = NPCTRADE_START_INDEX + 2*i - 1
		local itemId = ClientBackpack:getGridItem(obtain_index)
		if itemId > 0 then
			goodsCount = goodsCount + 1
			self.goodsIndexMap[goodsCount] = i -- 将列表索引映射到实际商品索引
		end
	end
	
	-- 更新商品列表数量
	self.view.widgets.ui_trade_item_list:setNumItems(goodsCount)
end

-- 刷新单个商品项
function ui_npc_tradeCtrl:FreshGoodsItem(obj, index)
	if ClientBackpack == nil then
		return
	end
	
	local obtain_index = NPCTRADE_START_INDEX + 2*index - 1 -- 获得的物品索引
	local payout_index = NPCTRADE_START_INDEX + 2*(index-1) -- 付出的物品索引
	
	local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(obtain_index))
	if tradeDef == nil then 
		return 
	end

	local type = tradeDef.TradeType
	local dur = ClientBackpack:getGridDuration(obtain_index) -- 标记锁定
	
	-- 根据交易类型决定显示哪个物品的图标（关键逻辑！）
	local display_grid_index = obtain_index
	if type == 0 then
		-- 出售类型：显示付出的物品
		display_grid_index = payout_index
	end
	-- 购买/兑换类型：显示获得的物品（默认）
	
	local displayItemId = ClientBackpack:getGridItem(display_grid_index)
	if displayItemId <= 0 then
		return
	end

	-- 根据新的UI模板结构设置组件
	local tradeIcon = obj:getChild("ui_trade_icon")
	local tradePriceText = obj:getChild("txt_trade_price")
	local tradeNumText = obj:getChild("txt_trade_num")
	
	-- 设置商品图标
	if tradeIcon then
		self:SetIconObjIcon(tradeIcon, displayItemId)
		
		-- 处理锁定状态（商品已售完时图标变灰）
		if dur == 0 then
			tradeIcon:setGrayed(true)
		else
			tradeIcon:setGrayed(false)
		end
	end
	
	-- 设置售价信息（购买该商品需要付出的货币数量）
	if tradeNumText then
		local payItemId = ClientBackpack:getGridItem(payout_index)
		if payItemId > 0 then
			-- 获取单价（与tradePriceText的获取方式保持一致）
			local price = ClientBackpack:getGridNum(payout_index)
			
			-- 格式化价格显示
			local priceText = "x" .. price
			if dur == 0 then
				-- 已售完时显示为红色
				tradeNumText:setColor({r = 254, g = 85, b = 26})
				priceText = "已售完"
			else
				-- 正常显示为默认颜色
				tradeNumText:setColor({r = 153, g = 51, b = 0}) -- #993300
			end
			tradeNumText:setText(priceText)
		else
			tradeNumText:setText("--")
		end
	end
	
	-- 设置商品数量（获得的物品数量）
	if tradePriceText then
		local num = ClientBackpack:getGridNum(obtain_index)
		tradePriceText:setText("x" .. num)
	end
end

-- 更新选中商品的详细信息
function ui_npc_tradeCtrl:UpdateSelectedItem()
	if ClientBackpack == nil then
		return
	end
	
	local i = self.curSelect
	local obtain_index = NPCTRADE_START_INDEX + 2*i - 1
	local payout_index = NPCTRADE_START_INDEX + 2*(i-1)
	
	local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(obtain_index))
	if tradeDef == nil then
		return
	end
	
	local type = tradeDef.TradeType
	
	-- 根据交易类型决定显示哪个物品的详细信息
	local display_grid_index = obtain_index
	if type == 0 then
		-- 出售类型：显示付出的物品
		display_grid_index = payout_index
	end
	-- 购买/兑换类型：显示获得的物品（默认）
	
	local displayItemId = ClientBackpack:getGridItem(display_grid_index)
	if displayItemId > 0 then
		local def = ItemDefCsv:get(displayItemId)
		if def then
			-- 更新商品图片
			self:SetIconObjIcon(self.view.widgets.ui_trade_img, displayItemId)
			
			-- 更新商品名称
			self.view.widgets.ui_trade_name:setText(def.Name)
			
			-- 更新商品描述
			self.view.widgets.ui_trade_desc:setText(def.Desc or "")
			
			-- 根据交易类型更新按钮文本
			local btnText = ""
			if type == 0 then
				btnText = GetS(538) -- 出售
			elseif type == 1 then
				btnText = GetS(3059) -- 购买
			elseif type == 2 then
				btnText = GetS(539) -- 兑换
			else
				btnText = "Trade" -- 默认文本
			end
			
			-- 查找并更新按钮文本（如果按钮有子文本组件）
			local tradeBtn = self.view.widgets.btn_trade
			if tradeBtn then
				local titleChild = tradeBtn:getChild("title")
				if titleChild then
					titleChild:setText(btnText)
				end
			end
		end
	end
end

-- 初始化货币图标（只设置一次）
function ui_npc_tradeCtrl:InitCostIcon()
	if ClientBackpack == nil then
		return
	end
	
	-- 获取第一个有效商品的付出道具作为货币图标
	local firstValidIndex = self.goodsIndexMap and self.goodsIndexMap[1] or 1
	local payout_index = NPCTRADE_START_INDEX + 2*(firstValidIndex-1)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	
	if payItemId > 0 and self.view.widgets.cost_icon then
		self:SetIconObjIcon(self.view.widgets.cost_icon, payItemId)
	end
end

-- 更新交易信息（数量和总价）
function ui_npc_tradeCtrl:UpdateTradeInfo()
	if ClientBackpack == nil then
		return
	end
	
	-- 更新购买数量显示
	self.view.widgets.txt_trade_num:setText(tostring(self.tradeNum))
	
	-- 计算总价
	local i = self.curSelect
	local payout_index = NPCTRADE_START_INDEX + 2*(i-1)
	local payItemId = ClientBackpack:getGridItem(payout_index)
	
	if payItemId > 0 then
		-- 获取单价（使用高位+低位的完整价格）
		local hightNum = ClientBackpack:getGridUserdata(payout_index) * 256
		local unitPrice = ClientBackpack:getGridNum(payout_index)
		local totalCost = unitPrice * self.tradeNum
		
		-- 货币图标不再更新，在初始化时已设置
		
		-- 更新总价显示（购买数量 × 单价）
		local costText = "x" .. totalCost
		self.view.widgets.txt_trade_cost:setText(costText)
	end
end

-- 执行交易
function ui_npc_tradeCtrl:NpcTradeFrame_Exchange()
	if CurWorld == nil or ClientBackpack == nil or OpenedContainerMob == nil or CurMainPlayer == nil then
		return
	end
	
	local CurChooseIndex = self.curSelect
	
	-- 检查创造模式下的快捷栏空间
	if not self:NpcTradeIsGodModeShortcutHasSpace() then
		if CurWorld:isCreativeMode() then
			ShowGameTips(GetS(8045), 3)
		elseif CurWorld:isGameMakerMode() then
			ShowGameTips(GetS(6995), 3)
		end
		return
	end

	local grid_index = NPCTRADE_START_INDEX + 2*CurChooseIndex - 1
	local payItemId = ClientBackpack:getGridItem(grid_index-1)
	local obtainItemId = ClientBackpack:getGridItem(grid_index)
	
	if obtainItemId > 0 and payItemId > 0 then
		local hightNum = ClientBackpack:getGridUserdata(grid_index-1) * 256
		local unitNeedNum = ClientBackpack:getGridNum(grid_index-1) + hightNum
		local totalNeedNum = unitNeedNum * self.tradeNum
		local hasNum = GetItemNum2Id(payItemId)
		
		local tradeDef = DefMgr:getNpcTradeDef(ClientBackpack:getGridUserdata(grid_index))
		if tradeDef == nil then 
			return 
		end

		local type = tradeDef.TradeType
		local dur = ClientBackpack:getGridDuration(grid_index)
		
		if dur == 0 then -- 锁定
			if type == 0 then
				ShowGameTips(GetS(540), 3)
			else
				ShowGameTips(GetS(541), 3)
			end
		else
					if hasNum >= totalNeedNum then
			-- 执行多次交易（与老实现保持一致）
			for i = 1, self.tradeNum do
				CurMainPlayer:npcTrade(1, grid_index-1)
			end
			
			-- 显示交易成功提示（直接使用交易数量）
			local def = ItemDefCsv:get(obtainItemId)
			local text = ""
			if type == 1 then
				text = GetS(3595, def.Name, self.tradeNum)
			elseif type == 2 then
				text = GetS(3596, def.Name, self.tradeNum)
			end
			ShowGameTips(text, 1)
			
			-- 重置购买数量并刷新界面
			self.tradeNum = 1
			self:UpdateTradeInfo()
		else
				if type == 1 then -- 购买
					local lackNum = math.ceil((totalNeedNum - hasNum) / MiniCoin_Star_Ratio)
					local text = GetS(466, totalNeedNum - hasNum, lackNum)
					StoreMsgBox(5, text, GetS(469), -2, lackNum, totalNeedNum)
					GetInst('StoreMsgBoxFrame'):SetClientString("购买货物星星不足")
				else
					ShowGameTips(GetS(544), 3)
				end
			end
		end
	end
end

-- 检查创造模式下快捷栏是否有空位
function ui_npc_tradeCtrl:NpcTradeIsGodModeShortcutHasSpace()
	if CurWorld == nil or ClientBackpack == nil then
		return true
	end
	
	if CurWorld and CurWorld:isGodMode() then
		if ClientBackpack then
			local emptyNum = ClientBackpack:getShorCutEmptyGridNum()
			if emptyNum > 0 then
				return true
			end
		end
		return false
	end
	
	return true
end

-- 设置图标（适配新UI结构）
function ui_npc_tradeCtrl:SetIcon(obj, id)
	local icon = obj:getChild("ui_trade_icon") or obj:getChild("icon")
	self:SetIconObjIcon(icon, id)
end

-- 设置图标对象的图标
function ui_npc_tradeCtrl:SetIconObjIcon(icon, id)
	if not icon then
		return
	end
	
	icon:setVisible(true)
	
	-- 判断是否为loader组件，使用不同的API
	local loaderIcon = tolua.cast(icon, "miniui.GLoader")
	if loaderIcon then
		-- 使用专门的loader图标设置函数
		MiniuiSetItemIcon(id, loaderIcon)
	else
		-- 普通图标组件使用texture设置
		local tex = MiniuiGetModelIconParams(id)
		if tex then
			icon:setTexture(tex)
		else
			tex = MiniuiGetItemIconTexture(id)
			if tex then
				icon:setTexture(tex)
			end
		end
	end
end

