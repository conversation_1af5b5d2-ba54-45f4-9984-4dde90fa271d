#ifndef CRAFTING_QUEUE_H
#define CRAFTING_QUEUE_H

#include <vector>
#include <functional>
#include "SandboxGame.h"
// 任务结构体
namespace craft {
    struct Task {
        int craftingId;
        int count;
        int ticksPerItem;
        int remainingTicks;
		Task() : craftingId(0), count(0), ticksPerItem(0), remainingTicks(0) {
		}
        Task(int id, int c, int ticks)
            : craftingId(id), count(c), ticksPerItem(ticks), remainingTicks(ticks) {
        }
		Task(int id, int c,int ticksPer, int ticks)
			: craftingId(id), count(c), ticksPerItem(ticksPer), remainingTicks(ticks) {
		}
    };
}
class ClientPlayer;
// 定义回调函数类型
using QueueUpdateCallback = std::function<void(const std::vector<craft::Task>&, ClientPlayer* player)>;
using ProgressUpdateCallback = std::function<void(const craft::Task&)>;
class EXPORT_SANDBOXGAME CraftingQueue;
class CraftingQueue { //tolua_exports
public:
    //tolua_begin
    CraftingQueue(ClientPlayer * player,QueueUpdateCallback qCallback, ProgressUpdateCallback pCallback);
    //tolua_end
    void addTask(int craftingId, int count, int ticksPerItem);
    void loadAddTask(int craftingId, int count, int ticksPer, int ticksPerItem);
    void removeTask(int index);
    void swapTasks(int index1, int index2);
    void tick();
    //tolua_begin
    int getCountDown();
    float getProgress();
    int getQueueSize();
    int getCraftId(int index);
    int getCraftCount(int index);
	int getCraftTicks(int index);
    //tolua_end
	void ClearQueue();
    craft::Task getTask(int index);
    void Net2Client(int craftingId, int count, int ticksPer,int remainingTick);
private:
    ClientPlayer* mPlayer;
    std::vector<craft::Task> queue;
    QueueUpdateCallback queueCallback;
    ProgressUpdateCallback progressCallback;
}; //tolua_exports

#endif // CRAFTING_QUEUE_H
