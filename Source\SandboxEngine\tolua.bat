@echo off

echo SandboxEngine start

set mToluaType=%~1

setlocal EnableDelayedExpansion


setlocal DisableDelayedExpansion

set PATH_PYTHON_EXE=%cd%\..\..\Tools\buildtools\Python\Win64\python.exe
set SRC_PYTHON_TARGET=%cd%\..\..\Tools\tolua_sandbox\CppToLua.py


if "%mToluaType%" == "winserver" (
	%PATH_PYTHON_EXE% %SRC_PYTHON_TARGET% -i toluaConf_linux.ini
	if errorlevel 1 (
		echo "tolua failed: %cd%"
		exit /b 1
	)
) else (
	%PATH_PYTHON_EXE% %SRC_PYTHON_TARGET% -i toluaConf.ini
)


echo SandboxEngine end

REM pause