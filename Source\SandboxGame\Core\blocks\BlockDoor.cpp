﻿#include "BlockDoor.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "WorldProxy.h"
#include "chunk.h"
#include "block_tickmgr.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
//#include "OgreMaterial.h"
#include "proto_common.pb.h"
#include "IClientPlayer.h"
#include "SandBoxManager.h"
#include "ClientInfoProxy.h"
#include "GameNetManager.h"
#include "ClientPlayer.h"
#include "LockCtrlComponent.h"
#include "container_keydoor.h"
#include "container_socdoor.h"
#include "container_socautodoor.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

IMPLEMENT_BLOCKMATERIAL(DoorMaterial)
IMPLEMENT_BLOCKMATERIAL(BlockSimpleDoor)
IMPLEMENT_BLOCKMATERIAL(BlockKeyDoor)
IMPLEMENT_BLOCKMATERIAL(BlockCenterDoor)
IMPLEMENT_BLOCKMATERIAL(BlockHighDoor)
IMPLEMENT_BLOCKMATERIAL(BlockSocDoor)
IMPLEMENT_SCENEOBJECTCLASS(BlockSocAutoDoor)
IMPLEMENT_BLOCKMATERIAL(BlockSocDoubleDoor)
DoorMaterial::DoorMaterial() : /*m_UpperMtl(NULL),*/ m_ignoreCheckUpBlock(false)
{

}

DoorMaterial::~DoorMaterial()
{
	//ENG_RELEASE(m_UpperMtl);
}

void DoorMaterial::init(int resid)
{
	ModelBlockMaterial::init(resid);

	if(m_LoadOnlyLogic) return;

	char texname[256];
	sprintf(texname, "%s_upper", GetBlockDef()->Texture1.c_str());
	//m_UpperMtl 
	auto mtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
	m_upperMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);

}


void DoorMaterial::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}

void DoorMaterial::initGeomName()
{
	m_geomName = "door";
}

//const char *DoorMaterial::getGeomName()
//{
//	return "door";
//}

const char *DoorMaterial::getBaseTexName(char *texname, const BlockDef *def, int &gettextype)
{
	gettextype = GETTEX_WITHDEFAULT;
	sprintf(texname, "%s_lower", def->Texture1.c_str());
	return texname;
}

//upper: bit0: mirror,  updown(bit2: 1),  open(bit3)
//down:  dir(bit0-bit1),  updown(bit2: 0),  open(bit3)
int DoorMaterial::ParseDoorData(const BaseSection* sectionData, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	isupper = (blockdata&4)!=0;
	isopen = (blockdata&8)!=0;

	int placedir;
	if(isupper)
	{
		mirror = (blockdata&1)!=0;
		int downdata = sectionData->getNeighborBlockLogic(blockpos,DIR_NEG_Y).getData();
		placedir = downdata&3;
	}
	else
	{
		int updata = sectionData->getNeighborBlockLogic(blockpos,DIR_POS_Y).getData();
		mirror = (updata&1)!=0;
		placedir = blockdata&3;
	}

	//LOG_INFO("blockpos: %d %d %d blockdata: %d %d %d %d", blockpos.x, blockpos.y, blockpos.z, blockdata, isupper, isopen, mirror);

	if(isopen)
	{
		static int opendirs_mirro[] = {3, 2, 0, 1}; //旋转90度
		static int opendirs[] = {2, 3, 1, 0}; //旋转270度

		if(mirror) return opendirs_mirro[placedir];
		else return opendirs[placedir];
	}
	else return placedir;
}

int DoorMaterial::ParseDoorData(const SectionDataHandler* sectionData, const WCoord& blockpos, bool& isupper, bool& isopen, bool& mirror)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	isupper = (blockdata & 4) != 0;
	isopen = (blockdata & 8) != 0;

	int placedir;
	if (isupper)
	{
		mirror = (blockdata & 1) != 0;
		int downdata = sectionData->getNeighborBlock(blockpos, DIR_NEG_Y).getData();
		placedir = downdata & 3;
	}
	else
	{
		int updata = sectionData->getNeighborBlock(blockpos, DIR_POS_Y).getData();
		mirror = (updata & 1) != 0;
		placedir = blockdata & 3;
	}

	//LOG_INFO("blockpos: %d %d %d blockdata: %d %d %d %d", blockpos.x, blockpos.y, blockpos.z, blockdata, isupper, isopen, mirror);

	if (isopen)
	{
		static int opendirs_mirro[] = { 3, 2, 0, 1 }; //旋转90度
		static int opendirs[] = { 2, 3, 1, 0 }; //旋转270度

		if (mirror) return opendirs_mirro[placedir];
		else return opendirs[placedir];
	}
	else return placedir;
}

int DoorMaterial::ParseDoorDataInVehicle(VehicleWorld* pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	int blockdata = pworld->getBlockData(blockpos);
	isupper = (blockdata&4)!=0;
	WCoord upPos = blockpos;
	if (isupper)
	{
		for (int i = 0; i < 4; i++)//最多只找4格
		{
			int downdata = pworld->getNeighborBlock(upPos, DIR_NEG_Y).getData();
			if (( blockdata & 4 )== 0)
			{
				upPos = upPos + WCoord(0, 1, 0);
				break;
			}
			upPos = upPos + WCoord(0, -1, 0);
		}
	}
	isopen = (blockdata&8)!=0;

	int placedir;
	if(isupper)
	{
		mirror = (blockdata&1)!=0;
		int downdata = pworld->getNeighborBlock(blockpos,DIR_NEG_Y).getData();
		placedir = downdata&3;
	}
	else
	{
		int updata = pworld->getNeighborBlock(blockpos,DIR_POS_Y).getData();
		mirror = (updata&1)!=0;
		placedir = blockdata&3;
	}

	if(isopen)
	{
		static int opendirs_mirro[] = {3, 2, 0, 1}; //旋转90度
		static int opendirs[] = {2, 3, 1, 0}; //旋转270度

		if(mirror) return opendirs_mirro[placedir];
		else return opendirs[placedir];
	}
	else return placedir;
}

int DoorMaterial::ParseDoorData(World *pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	VehicleWorld *pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
	if (pVehicleWorld)
	{
		return ParseDoorDataInVehicle(pVehicleWorld, blockpos, isupper, isopen, mirror);
	}
	else
	{
		Section *psection = pworld->getSection(blockpos);
		if (psection == NULL) return 0; //空指针保护
		return ParseDoorData(psection, blockpos-psection->m_Origin, isupper, isopen, mirror);
	}
}

void DoorMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial *mtl;
	BlockGeomMeshInfo meshinfo;

	if(isopen) mirror = !mirror;

	if(isupper)
	{
		mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);//m_UpperMtl;
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}
SectionMesh *DoorMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	getGeom(0)->getFaceVerts(meshinfo, 0);
	WCoord pos(0, -BLOCK_SIZE / 4, 75);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);

	psubmesh = pmesh->getSubMesh(getRenderMtlMgr().getMtl(m_upperMtlIndex), true);
	getGeom(0)->getFaceVerts(meshinfo, 1);
	WCoord pos1(0, BLOCK_SIZE * 3 / 4, 75);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos1);

	return pmesh;
}

char* DoorMaterial::getPhisicMeshBitWithWorld(World * pWorld, const WCoord &blockpos, int &precision, int &size)
{
	BlockGeomTemplate* geom = getGeom(0);
	precision = 4;
	size = 1;

	bool isupper, isopen, mirror;
	int dir = ParseDoorData(pWorld, blockpos, isupper, isopen, mirror);
	WCoord downDoorPos = blockpos;
	if (isupper)
	{
		downDoorPos = getDownDoorPos(pWorld, blockpos);
		dir = ParseDoorData(pWorld, downDoorPos + WCoord(0, 1, 0), isupper, isopen, mirror);
	}
	BlockGeomMeshInfo meshinfo;
	if (isopen) mirror = !mirror;

	LOG_INFO("getPhisicMeshBit %d %d", dir, mirror);

	char key[16];
	sprintf(key, "%d_%d", dir, mirror);
	char* cache = geom->getPhisicMeshBitBaseCache(key);
	if (cache)
		return cache;
	if (isupper)
	{
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}
	std::vector<BlockGeomMeshInfo*> infos;
	infos.push_back(&meshinfo);
	return geom->getPhisicMeshBitBaseWithType(key, infos, precision, size);
}

char* DoorMaterial::getPhisicMeshBit(BaseSection *psection, const WCoord &blockpos)
{
	BlockGeomTemplate* geom = getGeom(0);
	bool isupper, isopen, mirror;
	auto pWorld = psection->getWorld();
	if (!pWorld) return NULL;
	WCoord OrgPos = blockpos + psection->getOrigin();
	int dir = ParseDoorData(pWorld, OrgPos, isupper, isopen, mirror);
	WCoord downDoorPos = OrgPos;
	if (isupper && psection && pWorld)
	{
		downDoorPos = getDownDoorPos(pWorld, OrgPos);
		dir = ParseDoorData(pWorld, downDoorPos + WCoord(0, 1, 0), isupper, isopen, mirror);
	}
	BlockGeomMeshInfo meshinfo;
	if(isopen) mirror = !mirror;

	//LOG_INFO("getPhisicMeshBit %d %d", dir, mirror);

	char key[16];
	sprintf(key, "%d_%d", dir, mirror);
	char* cache = geom->getPhisicMeshBitBaseCache(key);
	if (cache)
		return cache;
	if(isupper)
	{
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}
	std::vector<BlockGeomMeshInfo*> infos;
	infos.push_back(&meshinfo);
	return geom->getPhisicMeshBitBase(key, infos);
}

void DoorMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
	WCoord downDoorPos = blockpos;
	if (isupper)
	{
		downDoorPos = getDownDoorPos(pworld, blockpos);
		dir = ParseDoorData(pworld, downDoorPos + WCoord(0, 1, 0), isupper, isopen, mirror);
	}

	WCoord origin = blockpos * BLOCK_SIZE;
	int thick = BLOCK_SIZE/8;

	if(dir == DIR_NEG_X) coldetect->addObstacle(origin, origin+WCoord(thick, BLOCK_SIZE, BLOCK_SIZE));
	else if(dir == DIR_POS_X) coldetect->addObstacle(origin+WCoord(BLOCK_SIZE-thick,0,0), origin+WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	else if(dir == DIR_NEG_Z) coldetect->addObstacle(origin, origin+WCoord(BLOCK_SIZE, BLOCK_SIZE, thick));
	else if(dir == DIR_POS_Z) coldetect->addObstacle(origin+WCoord(0,0,BLOCK_SIZE-thick), origin+WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

bool DoorMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(!pworld->doesBlockHaveSolidTopSurface(blockpos+WCoord(0,-1,0))) return false;
	int blockHeight = 1;
	if (GetBlockDef() && GetBlockDef()->Height)
	{
		blockHeight = GetBlockDef()->Height;
	}
	for (int i = 0; i < blockHeight; i++)
	{
		if (!BlockMaterial::canPutOntoPos(pworld, blockpos + WCoord(0, i, 0))) return false;
	}

	return true;
}

	
bool DoorMaterial::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	//只有木门可以
	if(IsWoodDoorBlock(getBlockResID()))
	{
		WCoord vehicleBlockPos;
		bool isupper, isopen, mirror;
		int placedir;

		ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
		int blockdata = pworld->getBlockData(blockpos);
		if (isupper)
		{
			int downdata = pworld->getBlockData(NeighborCoord(blockpos, DIR_NEG_Y));
			placedir = downdata & 3;
		}
		else
		{
			placedir = blockdata & 3;
		}
		WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
		if (pworld->getBlockID(ng) != getBlockResID())
		{
			return false;
		}

		pworld->setBlockData(blockpos, pworld->getBlockData(blockpos)^8, kBlockUpdateFlagNeedUpdate);
		pworld->setBlockData(ng, pworld->getBlockData(ng)^8, kBlockUpdateFlagNeedUpdate);
		if (getBlockResID() == 150016) // 同时开同时关
		{
			WCoord next = mirror ? RightOnPlaceDir(blockpos, placedir) : LeftOnPlaceDir(blockpos, placedir);
			int nextBlockId = pworld->getBlockID(next);
			bool isuppernext, isopennext, mirrornext;
			ParseDoorData(pworld, next, isuppernext, isopennext, mirrornext);
			if (nextBlockId == getBlockResID() && mirrornext == !mirror)
			{
				if (isopennext == isopen)
				{
					pworld->setBlockData(next, pworld->getBlockData(next) ^ 8, kBlockUpdateFlagNeedUpdate);
					WCoord ng = next + WCoord(0, isupper ? -1 : 1, 0);
					pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8, kBlockUpdateFlagNeedUpdate);
				}
			}
		}
		//载具上的声音位置，需要重新计算
		if (pworld->isVehicleWorld())
		{
			VehicleWorld *pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
			ActorVehicleAssemble *pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
// 			if (isupper)
			{
				pActorVehicleAssemble->changeDistortionmesh(ng);
			}
// 			else
			{
				pActorVehicleAssemble->changeDistortionmesh(blockpos);
			}
// 			VehicleBlock *pVehicleBlock = pActorVehicleAssemble->getVehicleBlock(blockpos);
// 			if (pVehicleBlock)
			{
				vehicleBlockPos = pActorVehicleAssemble->convertWcoord(blockpos);
			}
			pworld->getEffectMgr()->playSound(vehicleBlockPos, isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat()*0.2f + 0.8f);
		}
		else
			pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), isopen?"misc.door_close":"misc.door_open", 1.0f, GenRandomFloat()*0.2f+0.8f);
	}

	return true;
}

void DoorMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata&4) != 0;

	if(isupper)
	{
		if(pworld->getBlockID(blockpos+WCoord(0,-1,0)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
		if(blockid>0 && blockid!=getBlockResID())
		{
			onNotify(pworld, blockpos+WCoord(0,-1,0), blockid);
		}
	}
	else
	{
		bool toair = false;
		WCoord upcoord = blockpos+WCoord(0,1,0);
		if(pworld->getBlockID(upcoord) != getBlockResID() && !m_ignoreCheckUpBlock)
		{
			pworld->setBlockAir(blockpos);
			toair = true;
		}

		if(!pworld->doesBlockHaveSolidTopSurface(blockpos+WCoord(0,-1,0)))
		{
			int bid = pworld->getBlockID(blockpos);
			int blockdata = pworld->getBlockData(blockpos);

			pworld->setBlockAir(blockpos);
			toair = true;

			pworld->notifyToRecycleBlock(SandBoxMgrEventID::EVENT_HOMELAND_DESTROY_NOTIFY_RECYCLE, 0, 0, bid, blockdata);

			if(pworld->getBlockID(upcoord) == getBlockResID())
			{
				pworld->setBlockAir(upcoord);
			}
		}

		if(toair)
		{
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
		else
		{
			if (blockid != m_BlockResID)
			{
				bool powered = pworld->isBlockIndirectlyGettingPowered(blockpos) || pworld->isBlockIndirectlyGettingPowered(upcoord);
				if (powered || (g_BlockMtlMgr.getMaterial(blockid) && g_BlockMtlMgr.getMaterial(blockid)->canProvidePower()) || IsLaserBlock(blockid) || IsCanEmitBlockLaser(blockid))
				{
					if (!IsKeyDoorBlock(m_BlockResID))
					{
						onPoweredBlockChange(pworld, blockpos, powered);
					}
				}
			}
		}
	}

	//同步门的刚体
	sendBroadCast(pworld, blockpos);
}

void DoorMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */)
{
	bool isupper = (blockdata&4) != 0;
	if(!isupper)
	{
		ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
	}
}

bool DoorMaterial::canBlocksMovement(World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	return (blockdata&8)==0;
}

bool DoorMaterial::isOpen(int blockdata)
{
	return (blockdata & 8) != 0;
}

int DoorMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	if ((blockdata & 4) != 0)
		return blockdata;

	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}


void DoorMaterial::onPoweredBlockChange(World *pworld, const WCoord &blockpos, bool open)
{
	bool isupper, isopen, mirror;
	int placedir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);

	if (isopen != open)
	{
		pworld->setBlockData(blockpos, pworld->getBlockData(blockpos) ^ 8, kBlockUpdateFlagNeedUpdate);

		//上面或者下面的门联动
		WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
		pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8, kBlockUpdateFlagNeedUpdate);
		if (pworld->isVehicleWorld())
		{
			VehicleWorld *pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
			ActorVehicleAssemble *pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
// 			if (isupper)
			{
				pActorVehicleAssemble->changeDistortionmesh(ng);
			}
// 			else
			{
				pActorVehicleAssemble->changeDistortionmesh(blockpos);
			}
		}
	}
}


void DoorMaterial::sendBroadCast(World *pworld, const WCoord &blockpos)
{
	if (!pworld || pworld->isRemoteMode() || GetClientInfoProxy()->getMultiPlayer() == 0) return;

	PB_DoorDataHC doorDataHC;
	PB_Vector3* tblockpos = doorDataHC.mutable_blockpos();
	tblockpos->set_x(blockpos.x);
	tblockpos->set_y(blockpos.y);
	tblockpos->set_z(blockpos.z);

	GetGameNetManagerPtr()->sendBroadCast(PB_DOOR_DATA_HC, doorDataHC);
}


void BlockSimpleDoor::init(int resid)
{
	ModelBlockMaterial::init(resid);

	if (m_LoadOnlyLogic) return;

	const BlockDef* pDef = GetBlockDef();
	if (!pDef)
		return;

	//m_UpperMtl 
	if (pDef->ModelType == ModelType_UGCObj)
	{
		m_upperMtlIndex = m_defaultMtlIndex;
	}
	else
	{
		RenderBlockMaterial* mtl = g_BlockMtlMgr.createRenderMaterial(pDef->Texture1.c_str(), pDef, GETTEX_WITHDEFAULT, getDrawType());
		m_upperMtlIndex = getRenderMtlMgr().addMtl(mtl);
		ENG_RELEASE(mtl);
	}
}


void BlockSimpleDoor::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}


BlockKeyDoor::BlockKeyDoor()
{

}

BlockKeyDoor::~BlockKeyDoor()
{

}

void BlockKeyDoor::init(int resid)
{
	ModelBlockMaterial::init(resid);

	if (m_LoadOnlyLogic) return;

	char texname[256];
	sprintf(texname, "%s", m_Def->Texture1.c_str());
	auto mtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
	m_upperMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);
}

//这个门是老的，游戏中不会用到
WorldContainer* BlockKeyDoor::createContainer(World* pworld, const WCoord& blockpos)
{
	KeyDoorContainer* container = SANDBOX_NEW(KeyDoorContainer, blockpos);
	return container;
}


void BlockKeyDoor::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();

	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;

	if (isopen) mirror = !mirror;

	if (isupper)
	{
		mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());

	World* pworld = data.m_World;
	if (pworld == NULL)
	{
		return;
	}
	if (pworld->getContainerMgr() == NULL)
	{
		return;
	}
	KeyDoorContainer* container = NULL;
	WCoord downPos = blockpos;
	if (isupper)
	{
		downPos = blockpos + WCoord(0, -1, 0);
	}
	if (psection->getSectionType() == SECTION_NORMAL)
	{
		container = dynamic_cast<KeyDoorContainer*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + downPos));
	}
	if (container && container->getHasKey())
	{
		geom->getFaceVerts(meshinfo, 2, 1.0f, 0, dir, mirror);
		psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
		geom->getFaceVerts(meshinfo, 3, 1.0f, 0, dir, mirror);
		psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
	}
}


bool BlockKeyDoor::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	WCoord vehicleBlockPos;
	bool isupper, isopen, mirror;
	int placedir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);

	WCoord downPos = blockpos;
	if (isupper)
	{
		downPos = blockpos + WCoord(0, -1, 0);
	}
	KeyDoorContainer* container = dynamic_cast<KeyDoorContainer*>(pworld->getContainerMgr()->getContainer(downPos));
	if (!container)
	{
		return false;
	}

	if (container->getHasKey())
	{
		pworld->setBlockData(blockpos, pworld->getBlockData(blockpos) ^ 8, kBlockUpdateFlagNeedUpdate);
		WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
		pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8, kBlockUpdateFlagNeedUpdate);
		//载具上的声音位置，需要重新计算
		if (pworld->isVehicleWorld())
		{
			VehicleWorld* pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
			ActorVehicleAssemble* pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
			// 			if (isupper)
			{
				pActorVehicleAssemble->changeDistortionmesh(ng);
			}
			// 			else
			{
				pActorVehicleAssemble->changeDistortionmesh(blockpos);
			}
			// 			VehicleBlock *pVehicleBlock = pActorVehicleAssemble->getVehicleBlock(blockpos);
			// 			if (pVehicleBlock)
			{
				vehicleBlockPos = pActorVehicleAssemble->convertWcoord(blockpos);
			}
			pworld->getEffectMgr()->playSound(vehicleBlockPos, isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
		}
		else
			pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
	}
	else
	{
		if (player && player->getCurToolID() == ITEM_KEY)
		{
			container->setHasKey(true);
			pworld->setBlockAll(blockpos, m_BlockResID, pworld->getBlockData(blockpos), 3, true);
			WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
			pworld->setBlockAll(ng, m_BlockResID, pworld->getBlockData(ng), 3, true);
			player->shortcutItemUsed();
		}
	}

	return true;
}

void BlockCenterDoor::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;

	if (isopen) mirror = !mirror;

	if (isupper)
	{
		mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);//m_UpperMtl;
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	int doorWidth = floor(BLOCK_SIZE / 8);
	int halfDoorWidth = floor(doorWidth / 2);
	if (isopen)
	{
		int verticeOffsetX = 0;
		int verticeOffsetZ = 0;
		if (mirror)
		{
			if (dir == DIR_NEG_X)
			{
				verticeOffsetX = -(BLOCK_SIZE / 2) + halfDoorWidth;
				verticeOffsetZ = -(BLOCK_SIZE / 2);
			}
			else if (dir == DIR_POS_X)
			{
				verticeOffsetX = BLOCK_SIZE / 2 - halfDoorWidth;
				verticeOffsetZ = BLOCK_SIZE / 2;
			}
			else if (dir == DIR_NEG_Z)
			{
				verticeOffsetX = BLOCK_SIZE / 2;
				verticeOffsetZ = -(BLOCK_SIZE / 2) + halfDoorWidth;
			}
			else if (dir == DIR_POS_Z)
			{
				verticeOffsetX = -(BLOCK_SIZE / 2);
				verticeOffsetZ = BLOCK_SIZE / 2 - halfDoorWidth;
			}
		}
		else
		{
			if (dir == DIR_NEG_X)
			{
				verticeOffsetX = -(BLOCK_SIZE / 2) + halfDoorWidth;
				verticeOffsetZ = BLOCK_SIZE / 2;
			}
			else if (dir == DIR_POS_X)
			{
				verticeOffsetX = BLOCK_SIZE / 2 - halfDoorWidth;
				verticeOffsetZ = -(BLOCK_SIZE / 2);
			}
			else if (dir == DIR_NEG_Z)
			{
				verticeOffsetX = -(BLOCK_SIZE / 2);
				verticeOffsetZ = -(BLOCK_SIZE / 2) + halfDoorWidth;
			}
			else if (dir == DIR_POS_Z)
			{
				verticeOffsetX = BLOCK_SIZE / 2;
				verticeOffsetZ = BLOCK_SIZE / 2 - halfDoorWidth;
			}
		}
		int verticeCount = meshinfo.vertices.size();
		for (int index = 0; index < verticeCount; ++index)
		{
			BlockGeomVert& blockGeomVert = meshinfo.vertices[index];
			blockGeomVert.pos.x = blockGeomVert.pos.x + verticeOffsetX;
			blockGeomVert.pos.z = blockGeomVert.pos.z + verticeOffsetZ;
		}
	}

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}

void BlockCenterDoor::GetCollideData(World* pworld, const WCoord& blockpos, WCoord& originPos, WCoord& minPos, WCoord& maxPos)
{
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);

	//原点根据方向取一般
	WCoord origin = blockpos * BLOCK_SIZE;
	int doorWidth = BLOCK_SIZE / 8;
	int halfBlock = BLOCK_SIZE / 2;

	WCoord offsetMin(0, 0, 0);
	WCoord offsetMax(0, 0, 0);
	if (isopen)
	{
		if (mirror)
		{
			if (dir == DIR_NEG_X)
			{
				origin = origin + WCoord(0, 0, halfBlock);
				offsetMin = WCoord(0, 0, 0);
				offsetMax = WCoord(doorWidth, BLOCK_SIZE, BLOCK_SIZE);
			}
			else if (dir == DIR_POS_X)
			{
				origin = origin + WCoord(0, 0, halfBlock);
				offsetMin = WCoord(BLOCK_SIZE - doorWidth, 0, -BLOCK_SIZE);
				offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, 0);
			}
			else if (dir == DIR_NEG_Z)
			{
				origin = origin + WCoord(halfBlock, 0, 0);
				offsetMin = WCoord(-BLOCK_SIZE, 0, 0);
				offsetMax = WCoord(0, BLOCK_SIZE, doorWidth);
			}
			else if (dir == DIR_POS_Z)
			{
				origin = origin + WCoord(halfBlock, 0, 0);
				offsetMin = WCoord(0, 0, BLOCK_SIZE - doorWidth);
				offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
			}
		}
		else
		{
			if (dir == DIR_NEG_X)
			{
				origin = origin + WCoord(0, 0, halfBlock);
				offsetMin = WCoord(0, 0, -BLOCK_SIZE);
				offsetMax = WCoord(doorWidth, BLOCK_SIZE, 0);
			}
			else if (dir == DIR_POS_X)
			{
				origin = origin + WCoord(0, 0, halfBlock);
				offsetMin = WCoord(BLOCK_SIZE - doorWidth, 0, 0);
				offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
			}
			else if (dir == DIR_NEG_Z)
			{
				origin = origin + WCoord(halfBlock, 0, 0);
				offsetMin = WCoord(0, 0, 0);
				offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, doorWidth);
			}
			else if (dir == DIR_POS_Z)
			{
				origin = origin + WCoord(halfBlock, 0, 0);
				offsetMin = WCoord(-BLOCK_SIZE, 0, BLOCK_SIZE - doorWidth);
				offsetMax = WCoord(0, BLOCK_SIZE, BLOCK_SIZE);
			}
		}
	}
	else
	{
		if (dir == DIR_NEG_X)
		{
			origin = origin + WCoord(halfBlock, 0, 0);
			offsetMin = WCoord(0, 0, 0);
			offsetMax = WCoord(doorWidth, BLOCK_SIZE, BLOCK_SIZE);
		}
		else if (dir == DIR_POS_X)
		{
			origin = origin + WCoord(halfBlock, 0, 0);
			offsetMin = WCoord(0, 0, 0);
			offsetMax = WCoord(doorWidth, BLOCK_SIZE, BLOCK_SIZE);
		}
		else if (dir == DIR_NEG_Z)
		{
			origin = origin + WCoord(0, 0, halfBlock);
			offsetMin = WCoord(0, 0, 0);
			offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, doorWidth);
		}
		else if (dir == DIR_POS_Z)
		{
			origin = origin + WCoord(0, 0, halfBlock);
			offsetMin = WCoord(0, 0, 0);
			offsetMax = WCoord(BLOCK_SIZE, BLOCK_SIZE, doorWidth);
		}
	}
	originPos.x = origin.x;
	originPos.y = origin.y;
	originPos.z = origin.z;

	minPos.x = offsetMin.x;
	minPos.y = offsetMin.y;
	minPos.z = offsetMin.z;

	maxPos.x = offsetMax.x;
	maxPos.y = offsetMax.y;
	maxPos.z = offsetMax.z;
}

void BlockCenterDoor::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord originPos(0, 0, 0);
	WCoord minPos(0, 0, 0);
	WCoord maxPos(0, 0, 0);
	GetCollideData(pworld, blockpos, originPos, minPos, maxPos);

	coldetect->addObstacle(originPos + minPos, originPos + maxPos);
}

void BlockCenterDoor::createPickData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord originPos(0, 0, 0);
	WCoord minPos(0, 0, 0);
	WCoord maxPos(0, 0, 0);
	GetCollideData(pworld, blockpos, originPos, minPos, maxPos);

	coldetect->addObstacle(originPos + minPos, originPos + maxPos);
}

void BlockHighDoor::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	if (isupper)
	{
		bool isuppertmp, isopentmp, mirrortmp;
		int dir = ParseDoorData(psection, blockpos + WCoord( 0,-1, 0), isuppertmp, isopentmp, mirrortmp);
		if (isuppertmp)
		{
			return;
		}
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;

	if (isopen) mirror = !mirror;

	if (isupper)
	{
		mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);//m_UpperMtl;
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}

bool BlockHighDoor::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	//只有木门可以
	if (IsWoodDoorBlock(getBlockResID()))
	{
		WCoord vehicleBlockPos;
		bool isupper, isopen, mirror;
		WCoord downDoorPos = getDownDoorPos(pworld, blockpos);
		ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
		int blockdata = pworld->getBlockData(blockpos);
		int blockHeight = 1;
		if (GetBlockDef() && GetBlockDef()->Height)
		{
			blockHeight = GetBlockDef()->Height;
		}

		for (int i = 0; i < blockHeight; i++)
		{
			pworld->setBlockData(downDoorPos+WCoord(0,i,0), pworld->getBlockData(downDoorPos + WCoord(0, i, 0)) ^ 8, kBlockUpdateFlagNeedUpdate);
		}

		//载具上的声音位置，需要重新计算
		if (pworld->isVehicleWorld())
		{
			VehicleWorld* pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
			ActorVehicleAssemble* pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
			for (int i = 0; i < blockHeight; i++)
			{
				pActorVehicleAssemble->changeDistortionmesh(downDoorPos + WCoord(0, i, 0));
			}
			pworld->getEffectMgr()->playSound(vehicleBlockPos, isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
		}
		else
			pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
	}

	return true;
}

void BlockHighDoor::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata & 4) != 0;
	int blockHeight = 1;
	WCoord downDoorPos = blockpos;
	if (GetBlockDef() && GetBlockDef()->Height)
	{
		blockHeight = GetBlockDef()->Height;
	}
	if (isupper)
	{
		downDoorPos = getDownDoorPos(pworld,blockpos);
		if (pworld->getBlockID(blockpos + WCoord(0, -1, 0)) != getBlockResID() )
		{
			pworld->setBlockAir(blockpos);
		}
		if (blockid > 0 && blockid != getBlockResID())
		{
			onNotify(pworld, blockpos + WCoord(0, -1, 0), blockid);
			onNotify(pworld, blockpos + WCoord(0, 1, 0), blockid);
		}

	}
	else
	{
		bool toair = false;
		for (int i = 1; i < blockHeight; i++)
		{
			if (pworld->getBlockID(downDoorPos + WCoord(0, i, 0)) != getBlockResID() && !m_ignoreCheckUpBlock)
			{
				pworld->setBlockAir(blockpos);
				toair = true;
				break;
			}
		}
		if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0)))
		{
			int bid = pworld->getBlockID(blockpos);
			int blockdata = pworld->getBlockData(blockpos);

			pworld->setBlockAir(blockpos);
			//toair = true;

			pworld->notifyToRecycleBlock(SandBoxMgrEventID::EVENT_HOMELAND_DESTROY_NOTIFY_RECYCLE, 0, 0, bid, blockdata);

			//for (int i = 1; i < blockHeight; i++)
			//{
			//	if (pworld->getBlockID(downDoorPos + WCoord(0, i, 0)) == getBlockResID() )
			//	{
			//		pworld->setBlockAir(downDoorPos + WCoord(0, i, 0));
			//	}
			//}

		}

	}

	//同步门的刚体
	sendBroadCast(pworld, blockpos);
}

void BlockHighDoor::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	bool isupper = (blockdata & 4) != 0;
	int blockHeight = 1;
	if (GetBlockDef() && GetBlockDef()->Height)
	{
		blockHeight = GetBlockDef()->Height;
	}
	WCoord downDoorPos = blockpos;
	if (isupper)
	{
		for (int i = 0; i < blockHeight; i++)
		{
			if (blockid == pworld->getBlockID(blockpos + WCoord(0, -i, 0)) && (pworld->getBlockData(blockpos) & 4) == 0)
			{
				downDoorPos = blockpos + WCoord(0, -i, 0);
				break;
			}
		}
	}
	for (int i = 0; i < blockHeight; i++)
	{
		pworld->setBlockAir(downDoorPos + WCoord(0, i, 0));
	}
	if (!isupper)
	{
		int downblockdata = pworld->getBlockData(downDoorPos);
		dropBlockAsItem(pworld, downDoorPos, downblockdata);
	}
}

WCoord DoorMaterial::getDownDoorPos(World* pworld, const WCoord& blockpos)
{
	if (blockpos.y <= 0) return blockpos;//加个保护
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
	WCoord downDoorPos = blockpos;
	int blockHeight = 1;
	if (GetBlockDef() && GetBlockDef()->Height)
	{
		blockHeight = GetBlockDef()->Height;
	}
	if (isupper)
	{
		bool checkUpper = true;
		int checkHeight = blockHeight;
		while (checkUpper && checkHeight)
		{
			bool isuppertmp, isopentmp, mirrortmp;
			downDoorPos = downDoorPos + WCoord(0, -1, 0);
			ParseDoorData(pworld, downDoorPos, isuppertmp, isopentmp, mirrortmp);
			if (!isuppertmp)
			{
				checkUpper = false;
				break;
			}
			checkHeight--;
		}
	}
	return downDoorPos;
}
WCoord DoorMaterial::getDownDoorPos(const BaseSection* sectionData, const WCoord& blockpos)
{
	if (blockpos.y <= 0) return blockpos;
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(sectionData, blockpos, isupper, isopen, mirror);
	WCoord downDoorPos = blockpos;
	int blockHeight = 1;
	if (GetBlockDef() && GetBlockDef()->Height)
	{
		blockHeight = GetBlockDef()->Height;
	}
	if (isupper)
	{
		bool checkUpper = true;
		int checkHeight = blockHeight;
		while (checkUpper && checkHeight)
		{
			bool isuppertmp, isopentmp, mirrortmp;
			downDoorPos = downDoorPos + WCoord(0, -1, 0);
			ParseDoorData(sectionData, downDoorPos, isuppertmp, isopentmp, mirrortmp);
			if (!isuppertmp)
			{
				checkUpper = false;
				break;
			}
			checkHeight--;
		}
	}
	return downDoorPos;
}

BlockSocDoor::BlockSocDoor()
{
}

BlockSocDoor::~BlockSocDoor()
{
}

void BlockSocDoor::init(int resid)
{
	ModelBlockMaterial::init(resid);

	if (m_LoadOnlyLogic) return;
	
	char texname[256];
	sprintf(texname, "%s", m_Def->Texture1.c_str());
	auto mtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
	m_upperMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);
}

WorldContainer* BlockSocDoor::createContainer(World* pworld, const WCoord& blockpos)
{
	//2*2双开门
	BlockSocDoubleDoor* socdoubledoor = dynamic_cast<BlockSocDoubleDoor*>(pworld->getBlockMaterial(blockpos));
	if (socdoubledoor)
	{
		//统一获取门左下角的
		auto pos = socdoubledoor->getContainerPos(pworld, blockpos);
		if (pos == blockpos)
		{
			int blockId = pworld->getBlockID(blockpos);
			SocDoorContainer* container = SANDBOX_NEW(SocDoorContainer, blockpos, blockId);
			return container;
		}
		return nullptr;
	}

	BlockSocDoor* socdoor = dynamic_cast<BlockSocDoor*>(pworld->getBlockMaterial(blockpos));
	if (socdoor)//1x2
	{
		bool isupper, isopen, mirror;
		int placedir = this->ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
		WCoord downPos = blockpos;
		if (isupper)
		{
			downPos = blockpos + WCoord(0, -1, 0);
		}
		if (downPos == blockpos)
		{
			int blockId = pworld->getBlockID(blockpos);
			SocDoorContainer* container = SANDBOX_NEW(SocDoorContainer, blockpos, blockId);
			return container;
		}
		return nullptr;
	}

	return nullptr;
}

bool BlockSocDoor::openDoor(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	WCoord vehicleBlockPos;
	bool isupper, isopen, mirror;
	int placedir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
	pworld->setBlockData(blockpos, pworld->getBlockData(blockpos) ^ 8, kBlockUpdateFlagNeedUpdate);
	WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
	pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8, kBlockUpdateFlagNeedUpdate);
	//载具上的声音位置，需要重新计算
	if (pworld->isVehicleWorld())
	{
		VehicleWorld* pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
		ActorVehicleAssemble* pActorVehicleAssemble = pVehicleWorld->getActorVehicleAssemble();
		// 			if (isupper)
		{
			pActorVehicleAssemble->changeDistortionmesh(ng);
		}
		// 			else
		{
			pActorVehicleAssemble->changeDistortionmesh(blockpos);
		}
		// 			VehicleBlock *pVehicleBlock = pActorVehicleAssemble->getVehicleBlock(blockpos);
		// 			if (pVehicleBlock)
		{
			vehicleBlockPos = pActorVehicleAssemble->convertWcoord(blockpos);
		}
		pworld->getEffectMgr()->playSound(vehicleBlockPos, isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat() * 0.2f + 0.8f);
	}

	return true;
}

void BlockSocDoor::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	//Super::createBlockMesh(data, blockpos, poutmesh);
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();

	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;

	if (isopen) mirror = !mirror;

	if (isupper)
	{
		//mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());

	World* pworld = data.m_World;
	if (pworld == NULL)
	{
		return;
	}
	if (pworld->getContainerMgr() == NULL)
	{
		return;
	}
	SocDoorContainer* container = nullptr;
	WCoord downPos = blockpos;
	if (isupper)
	{
		downPos = blockpos + WCoord(0, -1, 0);
	}
	if (psection->getSectionType() == SECTION_NORMAL)
	{
		container = dynamic_cast<SocDoorContainer*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + downPos));
	}
	if (container && container->getLockData()->type != 0)
	{
		geom->getFaceVerts(meshinfo, 2, 1.0f, 0, dir, mirror);
		psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
		geom->getFaceVerts(meshinfo, 3, 1.0f, 0, dir, mirror);
		psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
	}
}

void BlockSocDoor::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	//Super::createBlockMesh(data, blockpos, poutmesh);
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();

	bool isupper, isopen, mirror;
	int dir = blockdata;//ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	mirror = true;
	isupper = false;
	//if (isopen) mirror = !mirror;

	//if (isupper)
	//{
	//	//mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);
	//	mtl = getDefaultMtl();
	//	geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	//}
	//else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 4, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());

	//World* pworld = data.m_World;
	//if (pworld == NULL)
	//{
	//	return;
	//}
	//if (pworld->getContainerMgr() == NULL)
	//{
	//	return;
	//}
	//SocDoorContainer* container = nullptr;
	//WCoord downPos = blockpos;
	//if (isupper)
	//{
	//	downPos = blockpos + WCoord(0, -1, 0);
	//}
	//if (psection->getSectionType() == SECTION_NORMAL)
	//{
	//	container = dynamic_cast<SocDoorContainer*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + downPos));
	//}
	//if (container && container->getLockData()->type != 0)
	//{
	//	geom->getFaceVerts(meshinfo, 2, 1.0f, 0, dir, mirror);
	//	psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
	//	geom->getFaceVerts(meshinfo, 3, 1.0f, 0, dir, mirror);
	//	psubmesh->addGeomBlockLight(meshinfo, &downPos, verts_light, NULL, mtl->getUVTile());
	//}
}

bool BlockSocDoor::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(player);
	if (!client_player)
		return false;

	LockCtrlComponent *lockctrl = client_player->GetComponent<LockCtrlComponent>();
	if (!lockctrl)
		return false;

	//门是开的都给关
	if (DoorMaterial::isOpen(pworld->getBlockData(blockpos)))
	{
		openDoor(pworld, blockpos, face, player, colpoint);
		return true;
	}

	if (lockctrl->IsOpen(blockpos))
	{
		openDoor(pworld,blockpos,face,player, colpoint);
		return true;
	}

	return false;
}

//函数专门给预览用的
int BlockSocDoor::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
#ifndef IWORLD_SERVER_BUILD 
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		int dir = playerTmp->getCurPlaceDir();
		return dir;
		//return ReverseDirection(dir);
	}
#endif
	return 0;
}

bool BlockSocDoor::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	ErosionContainer* container = getSocDoorContainer(pworld, blockpos);
	if (container)
	{
		//todo 维修恢复的hp 材料的扣除   
		container->addHp(-damage);//负数扣血
		//pworld->setBlockAll()
		return true;
	}
	return false;

}

int BlockSocDoor::getBlockHP(World* pworld, const WCoord& blockpos)
{
	ErosionContainer* container = getSocDoorContainer(pworld,blockpos);
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

SocDoorContainer* BlockSocDoor::getSocDoorContainer(World* pworld, const WCoord& blockpos)
{
	//2*2双开门
	BlockSocDoubleDoor* socdoubledoor = dynamic_cast<BlockSocDoubleDoor*>(pworld->getBlockMaterial(blockpos));
	if (socdoubledoor)
	{
		auto container = dynamic_cast<SocDoorContainer*>(socdoubledoor->getWorldContainer(pworld, blockpos));
		if (!container)
			return nullptr;

		return container;
	}
	//1*2
	BlockSocDoor* socdoor = dynamic_cast<BlockSocDoor*>(pworld->getBlockMaterial(blockpos));
	if (socdoor)
	{
		bool isupper, isopen, mirror;
		int placedir = socdoor->ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
		WCoord downPos = blockpos;
		if (isupper)
		{
			downPos = blockpos + WCoord(0, -1, 0);
		}
		SocDoorContainer* container = nullptr;
		container = dynamic_cast<SocDoorContainer*>(pworld->getContainerMgr()->getContainer(downPos));
		if (!container)
			return nullptr;
		return container;
	}
}

BlockSocAutoDoor::BlockSocAutoDoor()
{
}

BlockSocAutoDoor::~BlockSocAutoDoor()
{
}

void BlockSocAutoDoor::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_HasContainer, true);
}

WorldContainer* BlockSocAutoDoor::createContainer(World* pworld, const WCoord& blockpos)
{
	int blockId = pworld->getBlockID(blockpos);
	SocAutoDoorContainer* container = SANDBOX_NEW(SocAutoDoorContainer, blockpos,blockId);
	return container;
}

bool BlockSocAutoDoor::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	SocAutoDoorContainer* container = dynamic_cast<SocAutoDoorContainer*>(pworld->getContainerMgr()->getContainer(blockpos));

	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(player);
	if (!client_player)
		return false;

	LockCtrlComponent* lockctrl = client_player->GetComponent<LockCtrlComponent>();
	if (!lockctrl)
		return false;

	//门是开的都给关
	if (container->IsOpen())
	{
		CloseDoor(pworld, blockpos);
		return true;
	}

	if (lockctrl->IsOpen(blockpos))
	{
		OpenDoor(pworld, blockpos);
		return true;
	}

	return true;
}

#define OPENIDBLOCK 152
void BlockSocAutoDoor::OpenDoor(World* pworld, const WCoord& blockpos)
{
	SocAutoDoorContainer* container = dynamic_cast<SocAutoDoorContainer*>(pworld->getContainerMgr()->getContainer(blockpos));

	if (container->IsOpen())
	{
		return;
	}

	if (pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z) ||
		pworld->getBlockID(blockpos.x, blockpos.y - 2, blockpos.z))
	{
		return;
	}

	int blockid = pworld->getBlockID(blockpos);
	int blockdata = pworld->getBlockData(blockpos.x, blockpos.y, blockpos.z);
	//11
	blockdata = blockdata & 0x03;
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y, blockpos.z), blockid, blockdata, 19, true);
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y - 1, blockpos.z), OPENIDBLOCK, blockdata, 19, true);
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y - 2, blockpos.z), OPENIDBLOCK, blockdata, 19, true);

	container->setOpen(true);
}

void BlockSocAutoDoor::CloseDoor(World* pworld, const WCoord& blockpos)
{
	SocAutoDoorContainer* container = dynamic_cast<SocAutoDoorContainer*>(pworld->getContainerMgr()->getContainer(blockpos));

	if (!container->IsOpen())
	{
		return;
	}

	int blockid = pworld->getBlockID(blockpos);
	int blockdata = pworld->getBlockData(blockpos.x, blockpos.y, blockpos.z);
	//11
	blockdata = blockdata & 0x03;
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y, blockpos.z), blockid, blockdata + 12, 19, true);
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y - 1, blockpos.z), 0, blockdata, 19, true);
	pworld->setBlockAll(WCoord(blockpos.x, blockpos.y - 2, blockpos.z), 0, blockdata, 19, true);

	container->setOpen(false);
}

bool BlockSocAutoDoor::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, float damage)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	ErosionContainer* container = dynamic_cast<SocAutoDoorContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		//todo 维修恢复的hp 材料的扣除   
		container->addHp(-damage);//负数扣血
		//pworld->setBlockAll()
		return true;
	}
	return false;

}

int BlockSocAutoDoor::getBlockHP(World* pworld, const WCoord& blockpos)
{
	ErosionContainer* container = dynamic_cast<SocAutoDoorContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

BlockSocDoubleDoor::BlockSocDoubleDoor()
{
}

BlockSocDoubleDoor::~BlockSocDoubleDoor()
{
}

//统一获取门左下角的Container
WorldContainer* BlockSocDoubleDoor::getWorldContainer(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return nullptr;

	return pworld->getContainerMgr()->getContainer(getContainerPos(pworld, blockpos));
}

WCoord BlockSocDoubleDoor::getContainerPos(World* pworld, const WCoord& blockpos)
{
	bool isupper;
	bool isopen;
	bool mirror;
	int placedir;

	ParseDoubleDoorData(pworld, blockpos, isupper, isopen, mirror, placedir);

	WCoord container_pos = blockpos;
	//是镜像
	if (!mirror)
	{
		container_pos = LeftOnPlaceDir(blockpos, placedir);
	}

	if (isupper) container_pos.y -= 1;

	return container_pos;
}

void BlockSocDoubleDoor::ParseDoubleDoorData(World* pworld, const WCoord& blockpos, bool& isupper, bool& isopen, bool& mirror, int& placedir)
{
	if (!pworld)
		return;

	int blockdata = pworld->getBlockData(blockpos);

	isupper = (blockdata & 4) != 0;
	isopen = (blockdata & 8) != 0;

	if (isupper)
	{
		int downdata = pworld->getBlockData(WCoord(blockpos.x, blockpos.y - 1, blockpos.z));
		mirror = (blockdata & 1) != 0;
		placedir = downdata & 3;
	}
	else
	{
		int updata = pworld->getBlockData(WCoord(blockpos.x, blockpos.y + 1, blockpos.z));
		mirror = (updata & 1) != 0;
		placedir = blockdata & 3;
	}
}

void BlockSocDoubleDoor::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	LOG_WARNING("BlockSocDoubleDoor::onNotify blockpos %d %d %d",blockpos.x, blockpos.y, blockpos.z);

	//Super::onNotify(pworld, blockpos, blockid);
	/*
	if (blockid != getBlockResID())
		return;

	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata & 4) != 0;
	if (isupper)
	{
		bool mirror = (blockdata & 1) != 0;
		WCoord downpos(blockpos.x, blockpos.y - 1, blockpos.z);
		if (pworld->getBlockID(downpos) != getBlockResID())
			return;

		int downdata = pworld->getBlockData(downpos);
		int placedir = downdata & 3;

		onNotify(pworld, downpos, blockid);
		if (!mirror)
		{
			WCoord left_pos = LeftOnPlaceDir(blockpos, placedir);
			onNotify(pworld, left_pos, blockid);
			onNotify(pworld, left_pos + WCoord(0, -1 , 0), blockid);
		}
		else
		{
			WCoord right_pos = RightOnPlaceDir(blockpos, placedir);
			onNotify(pworld, right_pos, blockid);
			onNotify(pworld, right_pos + WCoord(0, -1, 0), blockid);
		}
	}
	else
	{

		int placedir = blockdata & 3;
		WCoord uppos(blockpos.x, blockpos.y + 1, blockpos.z);
		if (pworld->getBlockID(uppos) != getBlockResID())
			return;

		int updata = pworld->getBlockData(uppos);
		bool mirror = (blockdata & 1) != 0;

		onNotify(pworld, uppos, blockid);

		if (!mirror)
		{
			WCoord left_pos = LeftOnPlaceDir(blockpos, placedir);
			onNotify(pworld, left_pos, blockid);
			onNotify(pworld, left_pos + WCoord(0, 1, 0), blockid);
		}
		else
		{
			WCoord right_pos = RightOnPlaceDir(blockpos, placedir);
			onNotify(pworld, right_pos, blockid);
			onNotify(pworld, right_pos + WCoord(0, 1, 0), blockid);
		}
	}
	*/
}

void BlockSocDoubleDoor::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	//if (pworld->getBlockID(blockpos) != getBlockResID())
	//	return;

	//必须使用 blockdata pworld->getBlockData(blockpos) 无效

	bool isupper = (blockdata & 4) != 0;
	bool isopen = (blockdata & 8) != 0;
	bool mirror;
	int placedir;
	
	if (isupper)
	{
		int downdata = pworld->getBlockData(WCoord(blockpos.x, blockpos.y - 1, blockpos.z));
		//判断是否是本次删除触发的,如果对应的上边或者下边被删除那么就是本次
		if (pworld->getBlockID(WCoord(blockpos.x, blockpos.y - 1, blockpos.z)) != getBlockResID())
			return;

		mirror = (blockdata & 1) != 0;
		placedir = downdata & 3;
		//isupper 会有两次,掉了一次就行
		if (mirror)
			dropBlockAsItem(pworld, blockpos + WCoord(0, -1, 0), downdata);
	}
	else
	{
		int updata = pworld->getBlockData(WCoord(blockpos.x, blockpos.y + 1, blockpos.z));
		if (pworld->getBlockID(WCoord(blockpos.x, blockpos.y + 1, blockpos.z)) != getBlockResID())
			return;

		mirror = (updata & 1) != 0;
		placedir = blockdata & 3;
	}
	LOG_WARNING("isupper %d isopen %d mirror %d placedir %d", (int)isupper, (int)isopen, (int)mirror, placedir);
	LOG_WARNING("BlockSocDoubleDoor::onBlockRemoved blockpos %d %d %d", blockpos.x, blockpos.y, blockpos.z);
	//Super::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	if (!mirror)
	{
		WCoord left_pos = LeftOnPlaceDir(blockpos, placedir);
		
		LOG_WARNING("del pos del_pos %d %d %d", left_pos.x, left_pos.y, left_pos.z);
		pworld->setBlockAir(left_pos);
		if (isupper)
		{
			WCoord del_pos = left_pos + WCoord(0, -1, 0);
			LOG_WARNING("del pos del_pos %d %d %d", del_pos.x, del_pos.y, del_pos.z);
			pworld->setBlockAir(del_pos);
		}
		else
		{
			WCoord del_pos = left_pos + WCoord(0, 1, 0);
			LOG_WARNING("del pos del_pos %d %d %d", del_pos.x, del_pos.y, del_pos.z);
			pworld->setBlockAir(del_pos);
		}
	}
	else
	{
		WCoord right_pos = RightOnPlaceDir(blockpos, placedir);
		LOG_WARNING("del pos del_pos %d %d %d", right_pos.x, right_pos.y, right_pos.z);
		pworld->setBlockAir(right_pos);
		if (isupper)
		{
			WCoord del_pos = right_pos + WCoord(0, -1, 0);
			LOG_WARNING("del pos del_pos %d %d %d", del_pos.x, del_pos.y, del_pos.z);
			pworld->setBlockAir(del_pos);
		}
		else
		{
			WCoord del_pos = right_pos + WCoord(0, 1, 0);
			LOG_WARNING("del pos del_pos %d %d %d", del_pos.x, del_pos.y, del_pos.z);
			pworld->setBlockAir(right_pos + WCoord(0, 1, 0));
		}
	}
}

bool BlockSocDoubleDoor::openDoor(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	bool isupper;
	bool isopen;
	bool mirror;
	int placedir;

	ParseDoubleDoorData(pworld, blockpos, isupper, isopen, mirror, placedir);

	LOG_WARNING("blockpos = %d %d %d", blockpos.x, blockpos.y, blockpos.z);
	Super::openDoor(pworld, blockpos, face, player, colpoint);
	if (!mirror)
	{
		WCoord left_pos = LeftOnPlaceDir(blockpos, placedir);
		LOG_WARNING("left_pos = %d %d %d", left_pos.x, left_pos.y, left_pos.z);
		Super::openDoor(pworld, left_pos, face, player, colpoint);
	}
	else
	{
		WCoord right_pos = RightOnPlaceDir(blockpos, placedir);
		LOG_WARNING("right_pos = %d %d %d", right_pos.x, right_pos.y, right_pos.z);
		Super::openDoor(pworld, right_pos, face, player, colpoint);
	}

	return true;
}

void BlockSocDoubleDoor::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	Super::createBlockMesh(data, blockpos, poutmesh);
}

void BlockSocDoubleDoor::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	//Super::createBlockMesh(data, blockpos, poutmesh);
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	


	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();

	bool isupper, isopen, mirror;
	int dir = 3&blockdata;//ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	auto worldPos = blockpos + psection->getOrigin();
	WCoord left_pos = LeftOnPlaceDir(blockpos, dir);
	WCoord right_pos = RightOnPlaceDir(blockpos, dir);

	//右边 是空气
	bool right_air = data.m_World->getBlockID(right_pos.x, right_pos.y, right_pos.z) == 0 &&
		data.m_World->getBlockID(right_pos.x, right_pos.y + 1, right_pos.z) == 0;
	
	auto tempBlockPos = blockpos;
	//左边 是空气
	bool left_air = data.m_World->getBlockID(left_pos.x, left_pos.y, left_pos.z) == 0 &&
		data.m_World->getBlockID(left_pos.x, left_pos.y + 1, left_pos.z) == 0;
	if (!right_air && left_air) // 预览需要往左移
	{
		tempBlockPos = left_pos;
	}

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	mirror = false;
	isupper = false;
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 5, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(tempBlockPos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &tempBlockPos, verts_light, NULL, mtl->getUVTile());
}


bool BlockSocDoubleDoor::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	Super::onTrigger(pworld, blockpos, face, player, colpoint);

	return true;
}
