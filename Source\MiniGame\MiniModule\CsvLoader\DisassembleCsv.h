#pragma once

#include "AbsCsv.h" 
#include "LazySingleton.h" 
#include "DefDataTable.h" 
#include "defdata.h" 
#include <string>

class DisassembleCsv : public AbsCsv { //tolua_exports 
public:
    DisassembleCsv();
    ~DisassembleCsv();

    void onParse(MINIW::CSVParser& parser) override;
    void onClear() override;
    const char* getName() override;
    const char* getClassName() override;

    //tolua_begin
    int getNum();
    const CraftingDef* get(int id);
    const CraftingDef* getByItemId(int itemId);
    //tolua_end
private:
	DefDataTable<CraftingDef> m_Table;
	DECLARE_LAZY_SINGLETON(DisassembleCsv)
};//tolua_exports 