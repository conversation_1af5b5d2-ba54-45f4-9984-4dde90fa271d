#include "PlayerDownedStateAttrib.h"
#include "PlayerAttrib.h"

#include "SoundComponent.h"
#include "WorldManager.h"
#include "ActorBody.h"
#include "DieComponent.h"
#include "AttackedComponent.h"
#include "GameNetManager.h"
#include <ctime>  // 用于随机数种子

// Add this near the top of the file, after the includes but before any function definitions
#define DOWNED_STATE_DEBUG_LOG 1
#if DOWNED_STATE_DEBUG_LOG
#define DOWNED_STATE_LOG(...) \
    do { \
        WarningStringMsg("[PlayerDownedState] " __VA_ARGS__); \
    } while(0)
#else
#define DOWNED_STATE_LOG(...)
#endif

/*
所有事件均使用 "GE_PLAYER_DOWNED" 名称，通过 type 字段区分不同类型：
type = 0：玩家进入倒地状态
type = 1：玩家正在被救援
type = 2：倒地状态血量更新
type = 3：救援进度更新
type = 4：玩家恢复正常状态（被救援成功）
type = 5：玩家在倒地状态下死亡
type = 6：救援被中断
type = 7：自救尝试开始
type = 8：自救尝试被取消
type = 9：自救成功
type = 10：自救失败，导致死亡
type = 11：自救被伤害打断
type = 12：玩家死亡后重生进入正常状态
type = 13：进入自救免疫期
type = 14：更新自救免疫期时间
type = 15：免疫期结束
type = 16：被救援免疫期结束
UI系统可以根据这个统一的事件和类型字段处理所有倒地相关的界面显示和隐藏。
*/

//------------------------------------------------------------------------------
// PlayerDownedStateAttrib 实现
//------------------------------------------------------------------------------

PlayerDownedStateAttrib::PlayerDownedStateAttrib(PlayerAttrib* owner, ClientPlayer* player)
	: m_pOwner(owner)
	, m_pPlayer(player)
	, m_fDownedHealth(0.0f)
	, m_fMaxDownedHealth(100.0f)
	, m_fPreDownedHealth(0.0f)
	, m_nReviveProgress(0)
	, m_bReviveInterrupted(false)
	, m_reviverActorId(0)
	, m_UIUpdateTick(0)
	, m_lastDamageAmount(0.0f)
	, m_bSelfReviveAttempted(false)
	, m_bIsSelfReviving(false)
	, m_selfReviveImmunityTicks(0)
	, m_randomEngine(static_cast<unsigned>(time(nullptr)))
{
	// 创建状态对象
	m_normalState = new DownedStateNormal();
	m_downedState = new DownedStateBleedOut();
	m_beingRevivedState = new DownedStateBeingRevived();

	// 设置初始状态
	m_currentState = m_normalState;
	m_previousState = nullptr;  // 初始化上一个状态为nullptr

	// 初始化配置参数，设置默认值
	m_cfg.downed_health_max = 100.0f;
	m_cfg.max_revive_health_percent = 0.5f;
	
	// 直接设置总tick数，例如5*20=100个tick内血量耗尽
	//m_cfg.total_downed_ticks = 60 * TICKS_PER_SECOND; // 5秒内耗尽
	m_cfg.total_downed_ticks = 20 * TICKS_PER_SECOND; // 5秒内耗尽
	
	// 计算每tick的血量衰减率
	m_cfg.downed_health_decay_per_tick = m_cfg.downed_health_max / m_cfg.total_downed_ticks;
	
	m_cfg.revive_speed = 20.0f;
	m_cfg.revive_speed_per_tick = m_cfg.revive_speed / TICKS_PER_SECOND; // 每tick的救援进度
	m_cfg.revive_max_distance = 300.0f; //3个格子
	m_cfg.fatal_damage_threshold = 50.0f;
	m_cfg.revive_immunity_duration = 3.0f;
	m_cfg.downed_damage_multiplier = 2.0f;

	// 自救相关配置
	m_cfg.self_revive_success_chance = 0.5f;   // 50%成功率
	m_cfg.self_revive_health_amount = 1.0f;   // 恢复10点血量
	m_cfg.self_revive_immunity_ticks = 30 * TICKS_PER_SECOND; // 进入濒死的免疫期

	// 初始进入正常状态
	m_currentState->enter(this);
}

PlayerDownedStateAttrib::~PlayerDownedStateAttrib()
{
	// 释放状态对象
	if (m_normalState) delete m_normalState;
	if (m_downedState) delete m_downedState;
	if (m_beingRevivedState) delete m_beingRevivedState;

	m_normalState = nullptr;
	m_downedState = nullptr;
	m_beingRevivedState = nullptr;
}

void PlayerDownedStateAttrib::initialize()
{
	// 初始化状态数据
	m_fMaxDownedHealth = m_cfg.downed_health_max;
	m_fDownedHealth = m_fMaxDownedHealth;
	LOG_WARNING("PlayerDownedStateAttrib m_fDownedHealth = %f", m_fDownedHealth);
	// 重置其他数据
	reset();

	// 重置自救和被救援后免疫期状态
	m_selfReviveImmunityTicks = 0;
}

void PlayerDownedStateAttrib::reset()
{
	// 重置所有状态数据
	m_fDownedHealth = m_fMaxDownedHealth;
	LOG_WARNING("PlayerDownedStateAttrib m_fDownedHealth = %f", m_fDownedHealth);
	m_nReviveProgress = 0;
	m_reviverActorId = 0;
	m_bReviveInterrupted = false;
	m_UIUpdateTick = 0;
	// 重置上次伤害值，防止影响后续判断
	m_lastDamageAmount = 0.0f;

	m_selfReviveImmunityTicks = 0;
	// 重置自救状态
	m_bSelfReviveAttempted = false;
	m_bIsSelfReviving = false;

	LOG_WARNING("Downed reset---");
}

void PlayerDownedStateAttrib::tick()
{
	// 更新当前状态
	if (m_currentState)//&& !m_pPlayer->GetWorld()->isRemoteMode())
		m_currentState->tick(this, 1);  // 假设每个tick为1单位

	// 添加免疫期计数器更新逻辑
	if (m_selfReviveImmunityTicks > 0) {
		m_selfReviveImmunityTicks--;
		
		// 如果免疫期结束，可以更新UI
		if (m_selfReviveImmunityTicks == 0 && m_pPlayer && m_pPlayer->hasUIControl()) {
			MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
			uiContext.SetData_Number("type", 15); // 15 表示免疫期结束
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
		}
	}

#ifndef IWORLD_SERVER_BUILD
	// 更新UI，限制更新频率
	m_UIUpdateTick++;
	if (m_UIUpdateTick >= 5)  // 每5个tick更新一次UI
	{
		updateUI();
		m_UIUpdateTick = 0;
	}
#else
	// 服务器端定期同步倒地血量变化
	m_UIUpdateTick++;
	if (m_UIUpdateTick >= 20 && m_currentState != m_normalState)  // 每20个tick（1秒）同步一次血量
	{
		notifyDownedHealthChange();
		m_UIUpdateTick = 0;
	}
#endif
}

void PlayerDownedStateAttrib::enterDownedState()
{
	// 如果已经在倒地状态，不做处理
	if (isInDownedState())
		return;

	// 检查是否在自救或被救援后的免疫期内
	if (m_selfReviveImmunityTicks > 0)
	{
		// 在免疫期内，直接死亡而不是进入倒地状态
		handlePlayerDeath();
		return;
	}

	// 保存当前血量，以便在恢复时使用
	m_fPreDownedHealth = m_pOwner->getHP();

	// 设置倒地状态的血量为最大值
	m_fDownedHealth = m_fMaxDownedHealth;
	LOG_WARNING("PlayerDownedStateAttrib m_fDownedHealth = %f", m_fDownedHealth);

	// 如果有其他死亡条件（如伤害过大），也直接死亡
	if (checkDirectDeathConditions())
	{
		handlePlayerDeath();
		return;
	}

	m_pOwner->dropCurrentHandItem();//丢弃当前手持物

	// 切换到倒地状态
	changeToDownedState();

	// ... 其他进入倒地状态的操作（发送网络消息、播放音效等）...
}

void PlayerDownedStateAttrib::exitDownedState()
{
	if (m_currentState == m_normalState)
		return;

	// 切换到正常状态
	changeState(m_normalState);
}

bool PlayerDownedStateAttrib::startRevive(long long reviverId)
{
	// 必须在倒地状态下才能被救援
	if (m_currentState != m_downedState)
		return false;

	// 设置救援者ID
	m_reviverActorId = reviverId;

	// 切换到被救援状态
	changeState(m_beingRevivedState);

	return true;
}

bool PlayerDownedStateAttrib::cancelRevive()
{
	// 必须在被救援状态下才能取消救援
	if (m_currentState != m_beingRevivedState)
		return false;

	// 标记救援被中断
	m_bReviveInterrupted = true;

	// 清除救援者ID
	m_reviverActorId = 0;

	// 切换回倒地状态
	changeState(m_downedState);

	return true;
}

bool PlayerDownedStateAttrib::completeRevive(float healthPercentage)
{
	// 确保玩家存在
	if (!m_pOwner)
		return false;

	// 恢复生命值
	float healthToRestore = 1;//1m_fPreDownedHealth * healthPercentage;

	// 切换到正常状态
	changeState(m_normalState);

	// 设置生命值
	m_pOwner->setHP(healthToRestore);

	return true;
}

void PlayerDownedStateAttrib::addDownedHealth(float amount)
{
	// 如果不在倒地状态，直接返回
	if (m_currentState == m_normalState)
		return;

	float oldHealth = m_fDownedHealth;
	m_fDownedHealth += amount;

	// 限制最大值
	if (m_fDownedHealth > m_fMaxDownedHealth)
		m_fDownedHealth = m_fMaxDownedHealth;

	LOG_WARNING("PlayerDownedStateAttrib m_fDownedHealth = %f", m_fDownedHealth);
	
	// 如果血量发生了变化，通知客户端（但限制频率）
	if (oldHealth != m_fDownedHealth) {
		// 只有在血量变化较大时才立即通知，否则通过定时更新机制处理
		float changeAmount = abs(m_fDownedHealth - oldHealth);
		if (changeAmount >= 5.0f) {  // 血量变化超过5点时立即通知
			notifyDownedHealthChange();
		}
	}
	
	// 更新UI
	updateUI();
}

bool PlayerDownedStateAttrib::canMove() const
{
	// 只有在正常状态下才允许移动
	return isInNormalState();
}

void PlayerDownedStateAttrib::onPlayerDamaged(float damageAmount)
{
	m_lastDamageAmount = damageAmount;

	// 将伤害处理委托给当前状态
	if (m_currentState)
		m_currentState->handleDamage(this, damageAmount);
}

bool PlayerDownedStateAttrib::attemptSelfRevive()
{
	// 只有在倒地状态且未尝试过自救时才能尝试自救
	if (m_currentState != m_downedState || m_bSelfReviveAttempted || m_bIsSelfReviving)
		return false;

	// 标记正在尝试自救
	m_bIsSelfReviving = true;

	// 通知UI显示自救尝试
	if (m_pPlayer && m_pPlayer->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 7); // 7 表示开始自救尝试
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	// 判定自救结果
	bool success = rollForSelfReviveSuccess();

	// 设置已尝试自救
	m_bSelfReviveAttempted = true;

	// 处理自救结果
	if (success)
	{
		// 自救成功，恢复一定生命值
		handleSelfReviveSuccess();
	}
	else
	{
		// 自救失败，玩家死亡
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 10); // 10 表示自救失败
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);

		handlePlayerDeath();
	}

	// 添加濒死免疫期设置
	m_selfReviveImmunityTicks = m_cfg.self_revive_immunity_ticks;

	// 重置正在自救标记
	m_bIsSelfReviving = false;

	return success;
}

bool PlayerDownedStateAttrib::cancelSelfRevive()
{
	// 如果不在自救状态，无需取消
	if (!m_bIsSelfReviving)
		return false;

	// 重置自救状态
	m_bIsSelfReviving = false;

	// 通知UI取消自救
	if (m_pPlayer && m_pPlayer->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 8); // 8 表示取消自救尝试
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	return true;
}

bool PlayerDownedStateAttrib::rollForSelfReviveSuccess()
{
	// 使用随机数生成器进行概率判定
	std::uniform_real_distribution<float> distribution(0.0f, 1.0f);
	float randomValue = distribution(m_randomEngine);

	// 如果随机值小于配置的成功概率，则自救成功
	return randomValue < m_cfg.self_revive_success_chance;
}

void PlayerDownedStateAttrib::handleSelfReviveSuccess()
{
	// 确保玩家存在
	if (!m_pOwner)
		return;

	// 通知UI自救成功
	if (m_pPlayer && m_pPlayer->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 9); // 9 表示自救成功
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	// 回复固定血量
	float healthToRestore = m_cfg.self_revive_health_amount;

	// 切换到正常状态
	changeState(m_normalState);

	// 设置生命值
	m_pOwner->setHP(healthToRestore);
	
	// 重置所有倒地状态数据
	reset();
}

void PlayerDownedStateAttrib::handlePlayerDeath()
{

	// 重置上次伤害值，防止影响后续判断
	m_lastDamageAmount = 0.0f;

	m_selfReviveImmunityTicks = 0;
	// 确保玩家存在
	if (!m_pPlayer)
		return;

	// 通知UI玩家死亡
	if (m_pPlayer->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 5); // 5 表示玩家死亡
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	// 切换到正常状态，以便死亡处理
	changeState(m_normalState);

	m_selfReviveImmunityTicks = m_cfg.self_revive_immunity_ticks;

	// 调用死亡组件

	auto DieComp = m_pPlayer->SureDieComponent();
	if (DieComp) DieComp->onDie();
}

void PlayerDownedStateAttrib::changeState(PlayerDownedState* newState)
{
	if (m_currentState == newState)
		return;

	PlayerDownedState* oldState = m_currentState;

	// 获取状态名称用于日志
	const char* oldStateName = "Unknown";
	if (oldState == m_normalState)
		oldStateName = "NormalState";
	else if (oldState == m_downedState)
		oldStateName = "DownedState";
	else if (oldState == m_beingRevivedState)
		oldStateName = "BeingRevivedState";

	const char* newStateName = "Unknown";
	if (newState == m_normalState)
		newStateName = "NormalState";
	else if (newState == m_downedState)
		newStateName = "DownedState";
	else if (newState == m_beingRevivedState)
		newStateName = "BeingRevivedState";

	// 使用新的日志宏记录状态转换
	DOWNED_STATE_LOG("Player %lld state changing from [%s] to [%s]", 
		m_pPlayer ? m_pPlayer->getObjId() : 0, oldStateName, newStateName);

	// 退出当前状态
	if (m_currentState)
		m_currentState->exit(this);

	// 更新状态
	m_previousState = m_currentState;  // 保存上一个状态
	m_currentState = newState;

	// 进入新状态
	if (m_currentState)
		m_currentState->enter(this);

	// 播放状态转换特效
	playStateEffects(oldState, newState);
	
	// 发送网络消息通知状态变化
	sendStateChangeNetwork(oldState, newState);
}

// 仅在服务模式下发送网络消息
void PlayerDownedStateAttrib::sendStateChangeNetwork(PlayerDownedState* oldState, PlayerDownedState* newState)
{
#ifndef IWORLD_SERVER_BUILD
	return;
#endif
	// 确保玩家存在
	if (!m_pPlayer)
		return;
	
	// 检查是否在有效世界中
	World* pworld = m_pPlayer->getWorld();
	if (!pworld || pworld->isRemoteMode())
		return;
		
	// 创建protobuf消息
	PB_PlayerDownedStateChangeHC stateChangeMsg;
	
	// 设置玩家ID
	stateChangeMsg.set_playerid(m_pPlayer->getObjId());
	
	// 设置状态类型
	int oldStateType = 0;
	if (oldState == m_normalState)
		oldStateType = 0;  // 正常状态
	else if (oldState == m_downedState)
		oldStateType = 1;  // 倒地状态
	else if (oldState == m_beingRevivedState)
		oldStateType = 2;  // 被救援状态
	
	int newStateType = 0;
	if (newState == m_normalState)
		newStateType = 0;  // 正常状态
	else if (newState == m_downedState)
		newStateType = 1;  // 倒地状态
	else if (newState == m_beingRevivedState)
		newStateType = 2;  // 被救援状态
	
	stateChangeMsg.set_oldstatetype(oldStateType);
	stateChangeMsg.set_newstatetype(newStateType);
	
	// 设置当前倒地血量信息
	stateChangeMsg.set_downedhealth(m_fDownedHealth);
	stateChangeMsg.set_maxdownedhealth(m_fMaxDownedHealth);
	
	// 如果是被救援状态，添加救援者ID
	if (newState == m_beingRevivedState)
		stateChangeMsg.set_reviveractorid(m_reviverActorId);
	
	// 添加血量信息
	if (m_pOwner)
		stateChangeMsg.set_currenthp(m_pOwner->getHP());
	
	stateChangeMsg.set_revive_success_chance(m_cfg.self_revive_success_chance);
	stateChangeMsg.set_total_downed_ticks(m_cfg.total_downed_ticks);
	stateChangeMsg.set_downed_health_decay_per_tick(m_cfg.downed_health_decay_per_tick);
	// 通过GameNetManager发送给所有玩家
	//GetGameNetManagerPtr()->broadcastToClients(PB_PLAYER_DOWNED_STATE_CHANGE_HC, stateChangeMsg);
	
	//GetGameNetManagerPtr()->broadcastToClients(PB_PLAYER_DOWNED_STATE_CHANGE_HC, stateChangeMsg);

	GetGameNetManagerPtr()->sendToClient(m_pPlayer->getUin(), PB_PLAYER_DOWNED_STATE_CHANGE_HC, stateChangeMsg);

	// 也可以选择只发送给特定区域内的玩家
	// pworld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_DOWNED_STATE_CHANGE_HC, stateChangeMsg, m_pPlayer, true);
}

// 通知客户端倒地血量变化
void PlayerDownedStateAttrib::notifyDownedHealthChange()
{
#ifndef IWORLD_SERVER_BUILD
	return;
#endif
	// 确保玩家存在
	if (!m_pPlayer)
		return;
	
	// 检查是否在有效世界中
	World* pworld = m_pPlayer->getWorld();
	if (!pworld || pworld->isRemoteMode())
		return;
	
	// 只有在非正常状态下才需要同步血量
	if (m_currentState == m_normalState)
		return;
		
	// 创建专门的血量更新消息
	PB_PlayerDownedHealthUpdateHC healthUpdateMsg;
	
	// 设置玩家ID
	healthUpdateMsg.set_playerid(m_pPlayer->getObjId());
	
	// 设置当前状态类型
	int currentStateType = 0;
	if (m_currentState == m_downedState)
		currentStateType = 1;
	else if (m_currentState == m_beingRevivedState)
		currentStateType = 2;
	
	healthUpdateMsg.set_currentstate(currentStateType);
	
	// 设置当前倒地血量信息 - 这是主要要同步的数据
	healthUpdateMsg.set_downedhealth(m_fDownedHealth);
	healthUpdateMsg.set_maxdownedhealth(m_fMaxDownedHealth);
	
	// 添加当前生命值信息
	if (m_pOwner)
		healthUpdateMsg.set_currenthp(m_pOwner->getHP());
	
	// 如果是被救援状态，也同步救援进度和救援者信息
	if (m_currentState == m_beingRevivedState)
	{
		healthUpdateMsg.set_reviveprogress(m_nReviveProgress);
		healthUpdateMsg.set_reviveractorid(m_reviverActorId);
	}
	
	// 发送给客户端 (需要添加新的消息ID)
	GetGameNetManagerPtr()->sendToClient(m_pPlayer->getUin(), PB_PLAYER_DOWNED_HEALTH_UPDATE_HC, healthUpdateMsg);
	
	DOWNED_STATE_LOG("Player %lld downed health changed to %f, notified client", 
		m_pPlayer->getObjId(), m_fDownedHealth);
}

bool PlayerDownedStateAttrib::checkDirectDeathConditions()
{
	// 检查是否在濒死免疫期内
	if (m_selfReviveImmunityTicks > 0)
		return true;

	if (m_pPlayer)
	{
		const SocAttackInfo& attackinfo = m_pPlayer->getSocAttackInfo();
 		const DieInfoCsvDef* dieinfodef = GetDefManagerProxy()->getDieinfoCsvDef((int)(attackinfo.atktype));
		//不能进入濒死的攻击类型
		if (dieinfodef)
		{
			m_cfg.self_revive_success_chance = (float)dieinfodef->probability / 10000;

			LOG_WARNING("Check PlayerDownedState atktype = %d probability = %d ifDying = %d time = %d move = %d", 
				attackinfo.atktype, dieinfodef->probability, dieinfodef->ifDying, dieinfodef->time, dieinfodef->move);
			if (dieinfodef->ifDying == 0) return true;
			//可以进入濒死接入策划配置的死亡时间

			m_cfg.total_downed_ticks = dieinfodef->time * TICKS_PER_SECOND;

			// 计算每tick的血量衰减率
			m_cfg.downed_health_decay_per_tick = m_cfg.downed_health_max / m_cfg.total_downed_ticks;

			return false;
		}
	}


	return false;
}

void PlayerDownedStateAttrib::updateUI()
{
	// 只有在非正常状态下才更新UI
	if (m_currentState == m_normalState)
		return;

	// 确保玩家存在且有UI控制权
	if (!m_pPlayer || !m_pPlayer->hasUIControl())
		return;

	// 计算剩余时间（秒），基于当前血量和每tick的衰减率
	int remainingTicks = static_cast<int>(m_fDownedHealth / m_cfg.downed_health_decay_per_tick);
	int remainingTimeInSeconds = remainingTicks / TICKS_PER_SECOND;
	
	// 发送倒地血量更新事件
	MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
	uiContext.SetData_Number("type", 2); // 2 表示倒地血量更新
	uiContext.SetData_Number("revive_success", m_cfg.self_revive_success_chance);
	uiContext.SetData_Number("max_time", m_fMaxDownedHealth / m_cfg.downed_health_decay_per_tick / TICKS_PER_SECOND);
	uiContext.SetData_Number("remaining_time", remainingTimeInSeconds); // 添加倒计时秒数(整数)

	// 如果是被救援状态，同时更新救援进度
	if (m_currentState == m_beingRevivedState)
	{
		uiContext.SetData_Number("revive_progress", m_nReviveProgress);
	}

	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
}

void PlayerDownedStateAttrib::playStateEffects(PlayerDownedState* oldState, PlayerDownedState* newState)
{
	// 确保玩家存在
	if (!m_pPlayer)
		return;

	// 获取声音组件
	auto soundComp = m_pPlayer->GetComponent<SoundComponent>();
	if (!soundComp)
		return;

	//// 播放对应状态的音效
	//if (oldState == m_normalState && newState == m_downedState)
	//{
	//	// 进入倒地状态音效
	//	soundComp->playSound("player_downed");
	//}
	//else if (oldState == m_downedState && newState == m_beingRevivedState)
	//{
	//	// 开始被救援音效
	//	soundComp->playSound("revive_start");
	//}
	//else if (oldState == m_beingRevivedState && newState == m_normalState)
	//{
	//	// 救援成功音效
	//	soundComp->playSound("revive_complete");
	//}
	//else if ((oldState == m_downedState || oldState == m_beingRevivedState) && newState == m_normalState)
	//{
	//	// 从倒地恢复音效
	//	soundComp->playSound("player_recover");
	//}
}

bool PlayerDownedStateAttrib::isInNormalState() const
{
	return m_currentState == m_normalState;
}

bool PlayerDownedStateAttrib::isInDownedState() const
{
	return m_currentState == m_downedState;
}

bool PlayerDownedStateAttrib::isBeingRevived() const
{
	return m_currentState == m_beingRevivedState;
}

void PlayerDownedStateAttrib::changeToNormalState() 
{ 
	changeState(m_normalState); 
}

void PlayerDownedStateAttrib::changeToDownedState() 
{ 
	changeState(m_downedState); 
}

void PlayerDownedStateAttrib::changeToBeingRevivedState() 
{ 
	changeState(m_beingRevivedState); 
}

//------------------------------------------------------------------------------
// DownedStateNormal 实现
//------------------------------------------------------------------------------

void DownedStateNormal::enter(PlayerDownedStateAttrib* context)
{
	// 正常状态不需要特殊初始化
	// 重置自救相关状态
	context->setSelfReviveAttempted(false);
	context->setSelfReviving(false);
	
	// 通知UI玩家恢复正常状态
	ClientPlayer* player = context->getPlayer();
	if (player && player->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);

		uiContext.SetData_Number("type", 4); // 4 表示玩家恢复正常状态

		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	// 如果是从自救状态进入正常状态，通知UI显示自救免疫期状态
	if (context->isRecentlySelfRevived() && context->getPlayer() && context->getPlayer()->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 13); // 13 表示进入自救免疫期
		uiContext.SetData_Number("immunity_ticks", context->getConfig().self_revive_immunity_ticks);
		uiContext.SetData_Number("remaining_ticks", context->getSelfReviveImmunityTicks());
		uiContext.SetData_Number("remaining_seconds", context->getSelfReviveImmunityTicks() / TICKS_PER_SECOND); // 为UI提供秒数
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}
}

void DownedStateNormal::exit(PlayerDownedStateAttrib* context)
{
	// 正常状态不需要特殊清理
}

void DownedStateNormal::tick(PlayerDownedStateAttrib* context, int ticks)
{
	// 如果在自救免疫期内，定期更新UI显示剩余时间
	if (context->isRecentlySelfRevived() && (context->getUIUpdateTick() % 20 == 0) && 
	    context->getPlayer() && context->getPlayer()->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 14); // 14 表示更新自救免疫期时间
		uiContext.SetData_Number("remaining_ticks", context->getSelfReviveImmunityTicks());
		uiContext.SetData_Number("remaining_seconds", context->getSelfReviveImmunityTicks() / TICKS_PER_SECOND); // 为UI提供秒数
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}
}

void DownedStateNormal::handleDamage(PlayerDownedStateAttrib* context, float damage)
{
	// 在正常状态下，伤害由PlayerAttrib处理
	// 如果伤害导致生命值降至0，检查是否应该进入倒地状态
	PlayerAttrib* owner = context->getOwner();
	if (owner && owner->getHP() <= 0)
	{
		// 检查是否满足直接死亡条件
		if (context->checkDirectDeathConditions())
		{
			// 直接死亡
			context->handlePlayerDeath();
		}
		else
		{
			// 进入倒地状态
			context->enterDownedState();
		}
	}
}

//------------------------------------------------------------------------------
// DownedStateBleedOut 实现
//------------------------------------------------------------------------------

void DownedStateBleedOut::enter(PlayerDownedStateAttrib* context)
{
	// 只有从正常状态进入倒地状态时才重新初始化血量为最大值
	// 如果是从被救援状态返回倒地状态，保持之前的血量
	PlayerDownedState* previousState = context->getPreviousState();
	DownedStateNormal* normalState = context->getNormalState();
	if (previousState == nullptr || previousState == normalState)
	{
		// 从正常状态或初始状态进入倒地，初始化倒地血量为最大值
		context->setDownedHealth(context->getMaxDownedHealth());
	}
	// 如果是从被救援状态进入，保持当前血量不变

	// 重置救援相关数据
	context->setReviveProgress(0);
	context->setReviverActorId(0);
	context->setReviveInterrupted(false);

	// 通知UI显示倒地状态
	ClientPlayer* player = context->getPlayer();
	if (player && player->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 0); // 0 表示进入倒地状态
		uiContext.SetData_Number("downed_health", context->getDownedHealth());
		uiContext.SetData_Number("max_downed_health", context->getMaxDownedHealth());
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}
}

void DownedStateBleedOut::exit(PlayerDownedStateAttrib* context)
{
	// 倒地状态退出时不需要特殊清理
}

void DownedStateBleedOut::tick(PlayerDownedStateAttrib* context, int ticks)
{
	ClientPlayer* player = context->getPlayer();
	// 如果玩家不存在或在远程模式下，不处理倒地状态逻辑
	if (player->GetWorld()->isRemoteMode())
		return;
	updateDownedHealth(context, ticks);
//#ifdef IWORLD_SERVER_BUILD
//	// 服务器端逻辑
//	// 更新倒地血量
//	updateDownedHealth(context, ticks);
//#else
//	// 客户端逻辑
//	// 客户端不需要处理血量衰减，UI更新由PlayerDownedStateAttrib::tick中的updateUI统一处理
//#endif
}

void DownedStateBleedOut::updateDownedHealth(PlayerDownedStateAttrib* context, int ticks)
{
	// 如果正在尝试自救，暂停血量衰减
	if (context->isSelfReviving())
		return;

	// 使用每tick的衰减率
	float decay = context->getConfig().downed_health_decay_per_tick * ticks;

	// 应用环境影响（可以根据需要实现）
	//ClientPlayer* player = context->getPlayer();
	//if (player)
	//{
	//	if (player->isInWater()) // 假设有这样的方法
	//		decay *= 1.5f;
	//
	//	// 可以添加其他环境因素的影响
	//}

	// 应用衰减
	float newHealth = context->getDownedHealth() - decay;
	LOG_WARNING("Downed newHealth = %f", newHealth);
	//newHealth = 100.0f;
	context->setDownedHealth(newHealth > 0 ? newHealth : 0);

	// 如果倒地血量为0，玩家死亡
	if (context->getDownedHealth() <= 0 && !context->getPlayer()->GetWorld()->isRemoteMode())
	{
		// 如果玩家还没有尝试过自救，自动触发自救尝试
		if (!context->hasSelfReviveAttempted() && !context->isSelfReviving())
		{
			context->attemptSelfRevive();
		}
		// 如果已经尝试过自救或当前不在自救中，则处理死亡
		else if (!context->isSelfReviving())
		{
			context->handlePlayerDeath();
		}
	}
}

void DownedStateBleedOut::handleDamage(PlayerDownedStateAttrib* context, float damage)
{
	// 倒地状态下受到伤害 直接死
	if (damage > 0)
	{
		damage = 99999.f;
		// 使用配置的伤害倍率
		float downedDamage = damage * context->getConfig().downed_damage_multiplier;
		float newHealth = context->getDownedHealth() - downedDamage;
		context->setDownedHealth(newHealth > 0 ? newHealth : 0);

		// 如果伤害较大且正在尝试自救，取消自救
		//if (damage > 5.0f && context->isSelfReviving())
		//{
		//	context->cancelSelfRevive();
		//
		//	// 通知玩家自救被打断
		//	ClientPlayer* player = context->getPlayer();
		//	if (player && player->hasUIControl())
		//	{
		//		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		//		uiContext.SetData_Number("type", 11); // 11 表示自救被打断
		//		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		//			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
		//	}
		//}

		// 如果倒地血量为0，玩家死亡
		if (context->getDownedHealth() <= 0)
		{
			context->handlePlayerDeath();
		}
	}
}

//------------------------------------------------------------------------------
// DownedStateBeingRevived 实现
//------------------------------------------------------------------------------

void DownedStateBeingRevived::enter(PlayerDownedStateAttrib* context)
{
	LOG_WARNING("DownedStateBeingRevived::enter");
	// 初始化救援进度
	context->setReviveProgress(0);

	// 通知UI显示救援状态
	ClientPlayer* player = context->getPlayer();
	if (player && player->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("revive_progress", 0);
		uiContext.SetData_Number("type", 1); // 1 表示进入救援状态
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}
}

void DownedStateBeingRevived::exit(PlayerDownedStateAttrib* context)
{
	LOG_WARNING("DownedStateBeingRevived::exit");
	// 如果救援被中断，发送通知
	if (context->getReviveProgress() < 100 && context->getReviverActorId() != 0)
	{
		ClientPlayer* player = context->getPlayer();
		if (player && player->hasUIControl())
		{
			MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
			uiContext.SetData_Number("type", 6); // 6 表示救援被中断
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
		}
	}
}

void DownedStateBeingRevived::tick(PlayerDownedStateAttrib* context, int ticks)
{
	// 检查救援者是否在范围内
	ClientPlayer* player = context->getPlayer();
	if (!player)
	{
		context->cancelRevive();
		return;
	}

	// 获取救援者
	ClientPlayer* reviver = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(context->getReviverActorId()));
	if (!reviver)
	{
		context->cancelRevive();
		return;
	}

	// 检查救援者距离
	float distance = player->getPosition().distanceTo(reviver->getPosition());
	if (distance > context->getConfig().revive_max_distance)
	{
		context->cancelRevive();
		return;
	}

	// 更新救援进度，使用每tick的进度值
	int progress = context->getReviveProgress() + (int)(context->getConfig().revive_speed_per_tick * ticks);
	context->setReviveProgress(progress > 100 ? 100 : progress);
	LOG_WARNING("DownedStateBeingRevived progress = %d", progress);

	// 更新UI
	if (player && player->hasUIControl())
	{
		MNSandbox::SandboxContext uiContext = MNSandbox::SandboxContext(nullptr);
		uiContext.SetData_Number("type", 3); // 3 表示救援进度更新
		uiContext.SetData_Number("revive_progress", context->getReviveProgress());
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYER_DOWNED", uiContext);
	}

	// 处理救援进度达到100%
	if (context->getReviveProgress() >= 100)
	{
		float reviveHealthPercent = context->getConfig().max_revive_health_percent;
		context->completeRevive(reviveHealthPercent);
		return;
	}

	// 被救援状态下，倒地血量暂停衰减（不更新倒地血量）
	// 注释掉原来的血量衰减逻辑
	/*
	float decay = context->getConfig().downed_health_decay_per_tick * 0.5f * ticks;
	float newHealth = context->getDownedHealth() - decay;
	context->setDownedHealth(newHealth > 0 ? newHealth : 0);

	// 如果倒地血量为0，玩家死亡
	if (context->getDownedHealth() <= 0)
	{
		context->handlePlayerDeath();
	}
	*/
}

void DownedStateBeingRevived::handleDamage(PlayerDownedStateAttrib* context, float damage)
{
	// 被救援状态下受到伤害
	if (damage > 0)
	{
		// 更新倒地血量
		//float downedDamage = damage * context->getConfig().downed_damage_multiplier;
		float newHealth = 99999.0f;
		context->setDownedHealth(newHealth > 0 ? newHealth : 0);

		// 如果倒地血量为0，玩家死亡
		if (context->getDownedHealth() <= 0)
		{
			context->handlePlayerDeath();
		}
	}
}