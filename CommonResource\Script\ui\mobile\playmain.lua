
MAX_SHORTCUT = 8
ShortCut_SelectedIndex = -1
CurPlayerExpVal = 0;
ShowBallOperateTipsTime = 0;
local math = _G.math
local PlayMain = {
};
_G.PlayMain = PlayMain;

--[==[
	切换渲染
	Created on 2021-05-10 at 17:17:58
]==]
function PlayMain:onToggleRenderInfo(enable)
	-- if not self.HP then
	-- 	return
	-- end
	-- if not self.HP.ui then
	-- 	return
	-- end
	-- if enable then
	-- 	self.HP.ui:SetPoint("topleft", "PlayMainFrame", "topleft", 20, 150);
	-- else
	-- 	self.HP.ui:SetPoint("topleft", "PlayMainFrame", "topleft", 20, 4);
	-- end
end

local BattleCoundBlinkingTime = 0;
local BossHpShown = 0;
local GVoiceSpeechUsageBeginTime = 0;--计算GVoice语音使用时长，53001打点上报使用
local GetMuteFlag = 0;    --获取禁言数据标识
-- 组合变形cd
local lastTimeCombineTransform = 0
local checkingSleepingPlayers = true  --是否需要检查正在睡觉的玩家

local GVoiceJoinRoomBtnShown = false --是否已经显示了加入语音房按钮

local MobileTipsShowed = false	--当次地图是否已经检查过流量包状态

local coloredGunAndEgeSelectedColor = -1
local coloredBrushSelectedColor = 0

local AvatarSummonIndex = 0	--装扮召唤召唤物序号

local addOutAccessSuccess = false	-- 上报房间是否成功
local addOutAccessInfo = nil	-- 上报房间信息
local addOutAccessPermit = false -- 是否允许上报房间

----------------------------------------------------UIHideFrame---------------------------------------------------
t_UIName = { "PlayMainFrame", "GongNengFrame"}

function Playmain_ShowFlyBtn()
	print("Playmain_ShowFlyBtn:");
	local flybtn = getglobal("PlayMainFrameFly");
	-- local upbtn = getglobal("PlayMainFrameFlyUp");

	flybtn:Show();
	flybtn:SetPoint("top", "PlayMainFrameFlyUp", "bottom", 0, 0);
end

function UIHideFrame_OnLoad()
	-- this:setUpdateTime(0.05);
end

function UIHideFrame_OnShow()
	-- getglobal("UIHideFrameTex"):SetBlendAlpha(1);
	if SdkManager:isShareEnabled() then  														-- and not IsOverseasVer()
		getglobal("UIHideFrameScreenshotBtn"):Show();
	else
		getglobal("UIHideFrameScreenshotBtn"):Hide();
	end
	-- if GetClientInfo():isPC() then
	local state = GetIWorldConfig():getGameData("hideui");
	if state == 0 then
		getglobal("UIHideFrameScreenshotBtn"):Hide();
		getglobal("UIHideFrameExitBtn"):Hide();
	elseif state == 1 then
		getglobal("UIHideFrameExitBtn"):Show()
	end
	-- end
end

function UIHideFrame_OnClick()
	-- if getglobal("UIHideFrameTex"):GetBlendAlpha() <= 0.1 then
	-- 	getglobal("UIHideFrameTex"):SetBlendAlpha(1);
	-- else
		-- for i=1, #(t_UIName) do
		-- 	local frame = getglobal(t_UIName[i]);
		-- 	frame:Show();
		-- end
		-- getglobal("UIHideFrame"):Hide();
		-- CurMainPlayer:setUIHide(false);
		-- if CUR_WORLD_MAPID > 0 then
		-- 	if not getglobal("InstanceTaskFrame"):IsShown() then
		-- 		getglobal("InstanceTaskFrame"):Show();
		-- 	end
		-- end
		-- GetIWorldConfig():setGameData("hideui", 0);
		-- GetClientInfo():appalyGameSetData();
	-- end
end

function UIHideFrame_OnUpdate()
	-- local UIHideFrameTex = getglobal("UIHideFrameTex")
	-- if UIHideFrameTex:GetBlendAlpha() > 0.1 then
	-- 	local alpha = UIHideFrameTex:GetBlendAlpha() - 0.05;
	-- 	if alpha < 0.1 then
	-- 		alpha = 0.1;
	-- 	end
	-- --	UIHideFrameTex:SetBlendAlpha(alpha);
	-- 	local cVal = alpha*255;
	-- 	UIHideFrameTex:SetColor(cVal, cVal, cVal, cVal);
	-- end
end

function UnhideAllUI()
	GetIWorldConfig():setGameData("hideui", 0);
	ClientMgrAppalyGameSetData()


	for i=1, #(t_UIName) do
		local frame = getglobal(t_UIName[i]);
		frame:Show();
		
	end
	getglobal("UIHideFrame"):Hide();
	if CurMainPlayer then 
		CurMainPlayer:setUIHide(false) 
	end

	-- if CUR_WORLD_MAPID == 1 then
	-- 	if not getglobal("InstanceTaskFrame"):IsShown() then
	-- 		getglobal("InstanceTaskFrame"):Show();
	-- 	end
	-- end

	if CUR_WORLD_MAPID > 0 then
		if BossHpShown == 1 then
			if not GetInst("MiniUIManager"):IsShown("BossLifeInfoAutoGen") then
				GetInst("MiniUIManager"):ShowUI("BossLifeInfoAutoGen")
			end
			BossHpShown = 0;
		end
	end

	if IsInHomeLandMap and IsInHomeLandMap() then
		ShowHomeMainUI()
		getglobal("GongNengFrame"):Hide()
		HomeLandGuideTaskCall("ShowUi", true)
	end

	GetInst("MiniUIManager"):ShowMiniUI()

	if WorldMgr and WorldMgr:getGameMode() == g_WorldType.OWTYPE_SINGLE then
		GetInst("MiniUIManager"):ShowUI("TaskTrackCtrl")
	end

	if CurWorld then
		CurWorld:SetShowName(true)
		CurWorld:SetShowHpBar(true)
	end
end

function UIHideFrameExitBtn_OnClick()
	if IsUGCEditingHighMode() then
		--截图中，先不响应
		if GetInst("MiniUIManager"):IsShown("SceneShotFrameAutoGen") then
			return;
		end
		AccelKey_HideUIToggle()
		return
	end
	
	UnhideAllUI();
	
	if UIEditorDef then
		UIEditorDef:UIHideModelQuite()
	end
end

local function checkUpdateWaterCannonGunModelTexture(currentCount)
	local maxNum = LuaConstants:get().oxygen_pack_full_bullet_num
	local percent = math.ceil(currentCount/maxNum*100)
	local texureid = 1
	if percent <= 0 then
		texureid = 1
	elseif percent <= 25 then
		texureid = 2
	elseif percent <= 60 then
		texureid = 3
	else
		texureid = 4
	end
	CurMainPlayer:updateToolModelTexture(texureid)
end

function SetGunMagazine(currentCount, maxCount, isColorGunInhale)
	MiniLog("SetGunMagazine ", currentCount, maxCount, isColorGunInhale, debug.traceback())
	--使用发射器时不能让这些显示出来，因为这个调用地方太多了，只能加在这底层的函数了
	if CurMainPlayer and CurMainPlayer:getUsingEmitter() then
		getglobal("GunMagazine"):Hide()
        getglobal("ColoreSelectedFrame"):Hide()
		getglobal("SetSightingTelescopeBtn"):Hide()
		getglobal("SetSightingTelescopeBtnRight"):Hide()
		GetInst("UgcMsgHandler"):GetToolsBar():dispatcher(SceneEditorUIDef.common.hide_autoaiming)
		return;
	end
	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:UpdateGunMagazine(currentCount, maxCount)
	end

	if currentCount == -1 then
		getglobal("GunMagazine"):Hide()
        getglobal("ColoreSelectedFrame"):Hide()
		getglobal("SetSightingTelescopeBtn"):Hide()
		getglobal("SetSightingTelescopeBtnRight"):Hide()
		GetInst("UgcMsgHandler"):GetToolsBar():dispatcher(SceneEditorUIDef.common.hide_autoaiming)
	else
        local tips = getglobal("GunMagazineTips")
        local tips2 = getglobal("GunMagazineTips2")
        local background = getglobal("GunMagazineBackground")

		local needshow = true
		local toolId = CurMainPlayer:getCurToolID()
		local coloredGunOrEgg = (toolId == ITEM_COLORED_GUN or toolId == ITEM_COLORED_EGG or toolId == ITEM_COLORED_EGG_SMALL)
		--MKTODO:
		local brush = toolId == ITEM_COLOR_BRUSH
        if coloredGunOrEgg or brush or IsDyeableBlockLua(toolId) then
			local selection = getglobal("ColoreSelected")

            if CurMainPlayer:getSelectedColor() == -1 then
				if brush then
					CurMainPlayer:setSelectedColor(coloredBrushSelectedColor)
					curSelectedColor = coloredBrushSelectedColor

					if coloredBrushSelectedColor == -1 then
						tips:SetTextColor(255,255,255)
						tips2:SetTextColor(255,255,255)
						selection:SetColor(255,255,255)
						selection:SetTexUV("icon_colour_c.png")
					else
						tips:SetTextColor(0, 0, 0)
						tips2:SetTextColor(0, 0, 0)
						selection:SetColor(0, 0, 0)
						selection:SetTexUV("icon_colour.png")
					end
				else
					tips:SetTextColor(255,255,255)
					tips2:SetTextColor(255,255,255)
					selection:SetColor(255,255,255)
					selection:SetTexUV("icon_colour_c.png")
				end
            else
				local curSelectedColor = CurMainPlayer:getSelectedColor()
				-- 编辑、创造模式下，散弹枪独立颜色设置逻辑才生效
				local isValidMode = CurWorld and (CurWorld:isGameMakerMode() or CurWorld:isCreativeMode())

				-- 吸色功能，需要更新彩弹枪，彩蛋缓存颜色
				if isColorGunInhale then
					coloredGunAndEgeSelectedColor = curSelectedColor
				end

				if not isColorGunInhale and coloredGunOrEgg and isValidMode then
					-- 彩弹枪，彩蛋没设置过颜色，初始状态
					if coloredGunAndEgeSelectedColor == -1  then
						tips:SetTextColor(255,255,255)
						tips2:SetTextColor(255,255,255)
						selection:SetColor(255,255,255)
						selection:SetTexUV("icon_colour_c.png")
						CurMainPlayer:setSelectedColor(-1)
					-- 设置过颜色，直接设置颜色
					else
						-- 更新玩家颜色
						CurMainPlayer:setSelectedColor(coloredGunAndEgeSelectedColor)
						curSelectedColor = coloredGunAndEgeSelectedColor

						local r,g,b
						b =  curSelectedColor% 256
						g = ((curSelectedColor)/256) % 256
						r = ((curSelectedColor)/(256*256)) % 256
		
						tips:SetTextColor(r,g,b)
						tips2:SetTextColor(r,g,b)
						selection:SetColor(r,g,b)
						selection:SetTexUV("icon_colour.png")
					end

				elseif brush and isValidMode then
						-- 更新玩家颜色
						CurMainPlayer:setSelectedColor(coloredBrushSelectedColor)
						curSelectedColor = coloredBrushSelectedColor

						local r,g,b
						b =  curSelectedColor% 256
						g = ((curSelectedColor)/256) % 256
						r = ((curSelectedColor)/(256*256)) % 256
		
						tips:SetTextColor(r,g,b)
						tips2:SetTextColor(r,g,b)
						selection:SetColor(r,g,b)
						selection:SetTexUV("icon_colour.png")
				-- 不是彩弹枪、彩蛋或者不是编辑、创造模式 直接设置颜色
				else
					local r,g,b
					b =  curSelectedColor% 256
					g = ((curSelectedColor)/256) % 256
					r = ((curSelectedColor)/(256*256)) % 256
	
					tips:SetTextColor(r,g,b)
					tips2:SetTextColor(r,g,b)
					selection:SetColor(r,g,b)
					selection:SetTexUV("icon_colour.png")
				end
            end

			-- 家园商店里面是卖各种颜色的染色方块
			if IsDyeableBlockLua(toolId) and WorldMgr then 
				if WorldMgr:getSpecialType() == HOME_GARDEN_WORLD then
					return
				end
			end

            getglobal("ColoreSelectedFrame"):Show()
		    background:SetTexUV("icon_colour.png")
            tips:SetText(GetS(6034))
            tips2:SetText(GetS(6034))
        else
            getglobal("ColoreSelectedFrame"):Hide()

			needshow = false
			if playermain_ctrl then
				playermain_ctrl:UpdateGunMagazine(currentCount, maxCount)
			end
        end

        if CurMainPlayer:getCurToolID() == ITEM_WATER_CANNON then
        	checkUpdateWaterCannonGunModelTexture(currentCount)
        end
		if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.RELOAD) then
			local goGunMagazine = getglobal("GunMagazine")
			if needshow then
				goGunMagazine:Show()
			else
				goGunMagazine:Hide()
			end
		end
		if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
			GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):SetBasicOldVisible(UIEditorDef.TREE_ITEM_TYPE.RELOAD, "reload", true)
		end
		GetInst("UgcMsgHandler"):GetToolsBar():dispatcher(SceneEditorUIDef.common.show_autoaiming)
	end

end

function SetColoredGunOrEggSelectedColor(color)
	-- 只在编辑和创造模式有效果
	if CurWorld and not CurWorld:isGameMakerMode() and not CurWorld:isCreativeMode() then
		return
	end

	if not CurMainPlayer then
		coloredGunAndEgeSelectedColor = -1
		return
	end

	local toolId = CurMainPlayer:getCurToolID()
	if toolId == ITEM_COLORED_GUN or toolId == ITEM_COLORED_EGG or toolId == ITEM_COLORED_EGG_SMALL then
		coloredGunAndEgeSelectedColor = color
	end
end

function SetColoredBrushColor(color)
	-- 只在编辑和创造模式有效果
	if CurWorld and not CurWorld:isGameMakerMode() and not CurWorld:isCreativeMode() then
		return
	end

	if not CurMainPlayer then
		coloredBrushSelectedColor = 0
		return
	end

	local toolId = CurMainPlayer:getCurToolID()
	if toolId == ITEM_COLOR_BRUSH then
		coloredBrushSelectedColor = color
	else
		coloredBrushSelectedColor = 0
	end
end

function GetColoredGunOrEggSelectedColor()
	-- 只在编辑和创造模式有效果
	if CurWorld and not CurWorld:isGameMakerMode() and not CurWorld:isCreativeMode() then
		return -1
	end

	local toolId = CurMainPlayer:getCurToolID()
	if toolId == ITEM_COLORED_GUN or toolId == ITEM_COLORED_EGG or toolId == ITEM_COLORED_EGG_SMALL or toolId == ITEM_COLOR_BRUSH then
		return coloredGunAndEgeSelectedColor
	else
		return -1
	end
end

--PC模式下鼠标滑轮滚动
function SprayPaintChange(isNext)
	--打开喷漆选择界面，道具栏不变动
	if not GetInst('MiniUIManager'):IsShown("GameSprayPaintAutoGen") then
		local index = ShortCut_SelectedIndex
		local step = isNext and -1 or 1
		index = index + step
		if index < 0 then
			index = MAX_SHORTCUT - 1
		end
		if index > MAX_SHORTCUT then
			index = 1
		end
		if CurMainPlayer ~= nil then
			CurMainPlayer:onSetCurShortcut(index)
			ShortCutFrame_Selected(index);
		end
	end
end

-- 更新喷漆道具
function UpdatePaintChangeBtn()
	if CurMainPlayer and CurMainPlayer:getCurToolID() == ITEM_PAINTTANK then
        local tips = getglobal("PaintChangeFrameTips")
        local background = getglobal("PaintChangeFrameBackground")
		local icon = getglobal("PaintChangeFrameIcon")

        tips:SetTextColor(255,255,255)

		tips:SetText(GetS(30612))
    
		background:SetTexUV("icon_colour.png")

		local spray_id = getglobal("PaintChangeFrame"):GetClientUserData(0)
		if spray_id and spray_id > 0 then
			local curPaintItem
			local data = GetInst("ShopPaintDataManager"):GetOwnedShopSprayPaintTbl()
			for k,v in pairs(data) do
				if v.spray_id == spray_id then
					curPaintItem = v
					break
				end
			end
			if SprayPaintMgr and CurMainPlayer and curPaintItem then
				SprayPaintMgr:setSprayPaintId(CurMainPlayer:getUin(), curPaintItem.spray_id)
				SetItemIcon(icon, curPaintItem.item_id)
				icon:Show()
			else
				icon:Hide()
			end
		else
			icon:Hide()
		end

		getglobal("PaintChangeFrame"):Show()
	else
		getglobal("PaintChangeFrame"):Hide()
	end
end

function UIHideFrameScreenshotBtn_OnClick()
	local id = CurWorld:getOWID();
	GongNengFrameScreenshotBtn_OnClick()
	-- 截屏分享中会hide一波然后show一波，这里分享后强行hide，反正分享界面会延迟出来。。。
	HideAllUI()
	--StartShareOnScreenshot('map', id);
	--getglobal("UIHideFrame"):Hide();
	--StartNewMapScreenshotShare(id)
end

----------------------------------------------------SurvivalGameNovice------------------------------------------------
--新手引导游戏内遮罩层点击事件处理
function SurvivalGameNovice_OnClick()
	if not NoviceClickSwitch then return end

	if not AccountManager:getNoviceGuideState("welcome") then
		getglobal("GuideTipsFrame"):Hide();
		AccountManager:setNoviceGuideState("welcome", true);
		AccountManager:setCurNoviceGuideTask(2);
		ShowCurNoviceGuideTask();
		this:Hide();
	end

end
-------------------------------------------------ExtremityTipsFrame------------------------------
function ExtremityTipsFrame_OnShow()
	HideAllFrame("ExtremityTipsFrame", false);
	local descRich = getglobal("ExtremityTipsFrameDesc");
	if (CurWorld:getCurMapID() == 2)  then  --太空世界系统弹框设置
		descRich:SetText(GetS(20207), 216, 187, 142);
		getglobal("ExtremityTipsFrameTitle"):SetText(GetS(20206));
		getglobal("ExtremityTipsFrameFightIcon"):SetTexUV("xtc_icon06");
		getglobal("ExtremityTipsFrameNotice"):Hide()
	else
		getglobal("ExtremityTipsFrameFightIcon"):SetTexUV("xtc_icon01");
		getglobal("ExtremityTipsFrameTitle"):SetText(GetS(3180));
		getglobal("ExtremityTipsFrameNotice"):Hide()
	--	descRich:SetPoint("TOPLEFT", "ExtremityTipsFrameBkg", "TOPLEFT", 174, 58);
		descRich:SetText(GetS(3182), 216, 187, 142);
	end

	if not getglobal("ExtremityTipsFrame"):IsReshow() and ClientCurGame.setOperateUI then
		ClientCurGame:setOperateUI(true);
	end
	if IsUGCEditing() then
		GetInst("MiniUIManager"):ShowUI("SceneEditorMainframeAutoGen");
	end
end

function ExtremityTipsFrame_OnHide()
--	HideAllFrame("ExtremityTipsFrame", false);
	if not getglobal("ExtremityTipsFrame"):IsRehide() then
		ClientCurGame:setOperateUI(false);
	end
end

function ExtremityTipsFrame_OnClick()
	getglobal("ExtremityTipsFrame"):Hide();
end

function newTotemMsgbox(x, y, z, mapid)
	local totempos = {x = x, y= y, z =z, mapid=mapid};
	MessageBox(5, GetS(9002), function(btn, totempos)
		if btn == 'left' then
			WorldMgr:addTotemPoint(totempos.x, totempos.y, totempos.z, totempos.mapid);
		end
	end, totempos);
end
----------------------------------------------------PlayMainFrame-----------------------------------------------------
t_allFrame =  {
		"CreateBackpackFrame",
		"StorageBoxFrame",
		"FurnaceOxyFrame",
		"RepairFrame",
		"CraftingTableFrame",
		"AchievementFrame",
		"EnchantFrame",
		"IntroduceFrame",
		"MonumentFrame",
		"MapFrame",
		"NpcTradeFrame",
		"StarConvertFrame",
		"ExtremityTipsFrame",
		"ArchiveGradeFrame",
		"RideFrame",
		"BattleEndFrame",
		"AccRideCallFrame",
		"FeedBackFrame",
		--"DevTools",
		"RoleAttrFrame",
		"RoomUIFrame",
		"FriendUIFrame",
		"GameSetFrame",
		"ActivityFrame",
		"AdvertFrame",
		"MarketActivityFrame",
		"FeedBackFrame",
		"KeyDescriptionFrame",
		"OutGameConfirmFrame",
		"ScreenEffectFrame",
		"InstructionParserFrame",
		"SignalParserFrame",
		"BlueprintFrame",
		"ActionLibraryFrame",
		"CharacterActionFrame",
		"MeasureDistanceFrame",
		"DeveloperStoreSkuFrame",
		"DeveloperStoreBuyItemFrame",
		"DeveloperStoreMapPurchaseFrame",
		"BluePrintDrawingFrame",
		-- "CustomModelFrame",
		"MapModelLibFrame", --ResourceCenterNewVersionSwitch
		"CreateMonsterFrame",
		"ChooseOrganismFrame",
		"TransferFrame",
		"ChooseTransferDestinationFrame",
		"GameSignEditFrame",
		"VehicleEditFrame",
		"ActorEditSelectModelFrame",
		"ActorEditFrame",
		"VehicleActioner",
		"FullyCustomModelSelect",
		"FullyCustomModelEditor",
		"scriptprint",
		"ToolModeFrame",
		-- "MapEdit",
		"DeveloperRuntimeInfo",
		-- "PackingCM",
		-- "PackingCMAjust",
		-- "PackingCMCreate",
		"ResourceCenter",
		"ResourceCenterMoveFile",
		"ResourceCenterSelectFolder",
		"ResourceCenterAddFolder",
		"ResourceShopPluginSelect",
		"ResourceShopKindSelect",
		"ResourceShop",
		"HomeMain",
		"AltarAwardsEdit",
		--"TopPurchaseInMap",
		-- "TriggerAdInMap",
		"Pot",
		"Craft",
		"Furnace",
		"CraftSelectMenu",
		"Inlay",
		"Merge",
		"Ldentify",
		"HolographicMainmenu",
		"HolographicCartoon",
        "HolographicFullScreen",
        "SleepNoticeFrame",
		"MItemTipsFrame",
		"ShopAdNpc",
		"NewSleepNoticeFrame",
		"FriendChatFrame",
	}

local t_allFrame_miniui = {
	MessageBoxFrame = {
		closeFunc = function ()
			GetInst("MessageBoxFrameMgr"):CloseUI()
		end,
	},
	GiftPackFrame = {
		closeFunc = function ()
			GetInst("GiftPackFrameMgr"):CloseUI()
		end,
	},
}

local t_DeathFrames = {
	}
	
local t_DeathFrames_miniui = {
		"ExtremityDeathFrameAutoGen", "DeathFrameAutoGen", "BattleDeathFrameAutoGen",
	}


--检查一下 是否可以隐藏该Frame
function checkCanHideFrame(frameName)
	if frameName ~= "MessageBoxFrame" then
		return true
	end

	--返回true 就是允许登录，false 就是不允许登录
	if AccountManager.is_policy_limit_login then
		return AccountManager:is_policy_limit_login()
	end

	return true
end

function HideAllFrame(frameName, isHideMain, isIgnoreMiniUI)
	local isShowHpInfo = HpBarFrame_IsShown()
	local isShowCompass = PixelMapInterface:IsShowCompass()
	local isDead = false;
	local isDeadFrame = false;
	for i=1, #(t_DeathFrames) do
		local deathFrame = getglobal(t_DeathFrames[i], true); --在这个接口里，没加载的UI不需要去加载
		if not isDead and deathFrame and deathFrame:IsShown() then
			isDead = true;
		end

		if not isDeadFrame and frameName == t_DeathFrames[i] then
			isDeadFrame = true;
		end
	end

	-- 判断 miniui 中新做的死亡界面
	if not isDead then
		for i=1, #(t_DeathFrames_miniui) do

			local isShown = GetInst("MiniUIManager"):IsShown(t_DeathFrames_miniui[i])
			if isShown then
				isDead = true;
			end

			if not isDeadFrame and frameName == t_DeathFrames_miniui[i] then
				isDeadFrame = true;
			end
		end
	end

	if isDead then
		if not isDeadFrame and frameName ~= nil then
			getglobal(frameName):Hide();
			return;
		end
		--[[
		if getglobal("SetMenuFrame"):IsShown() then
			getglobal("SetMenuFrame"):Hide();
			ClientCurGame:setInSetting(false);
		else

			getglobal("SetMenuFrame"):Show();
			ClientCurGame:setInSetting(true);
		end
		]]
	end

	local needHide = function(hideFrameName)
		local ignoreCfg = {
			["AccRideCallFrame"] = {"ToolModeFrame",};	--打开坐骑页面的时候不用隐藏'工具模式'页面
		};

		local list = ignoreCfg[frameName];
		if list then
			for i = 1, #list do
				if hideFrameName == list[i] then
					--忽略, 不用隐藏
					return false;
				end
			end
		end

		return true;
	end

	for i=1, #(t_allFrame) do
		if t_allFrame[i] ~= frameName then
			if needHide(t_allFrame[i]) and checkCanHideFrame(t_allFrame[i]) then
				local frame = getglobal(t_allFrame[i], true);	--在这个接口里，没加载的UI不需要去加载
				if frame and frame:IsShown() then
					local mvcFrame = GetInst("UIManager"):GetCtrl(t_allFrame[i]);
					if mvcFrame then
						if mvcFrame.CloseBtnClicked then
							print("kekeke mvc CloseBtnClicked");
							mvcFrame:CloseBtnClicked();
						else
							GetInst("UIManager"):Close(t_allFrame[i]); --更一般的关闭
						end
					else
						frame:Hide();
					end
				end
			end
		end
	end

	for key, value in pairs(t_allFrame_miniui) do
		if key ~= frameName then
			if needHide(key) and checkCanHideFrame(key) then
				if value.closeFunc then
					value.closeFunc()
				end
			end
		end
	end 

	if isHideMain then
		getglobal("GongNengFrame"):Hide();
		PlayMainFrameUIHide();
		--ClientCurGame:showOperateUI(false);
	end
	if IsInHomeLandMap and IsInHomeLandMap() then
		HomeLandGuideTaskCall("ShowUi", false)
	end

	if not isIgnoreMiniUI then
		GetInst("UGCCommon"):OnHideAllUI()
		-- 新UI code_by: huangfubin
		if gFunc_IsNewMiniUIEnable and gFunc_IsNewMiniUIEnable() then

			if isAbroadEvn() then
				--这里的接口会直接关闭首页，会导致非游戏内的显示变成黑屏。需要通过HideMiniLobby对状态进行重置
				GetInst("MiniUIManager"):HideAllUIWithoutFrameName({"BossLifeInfoAutoGen","MainV2LobbyCtrl", "StarStationInfoFrameAutoGen","TaskTrackCtrl", "MusicOpCompAutoGen","VehicleAssemblyTipsAutoGen", "DeveloperUIRoot"})
				HideMiniLobby()
			else
				GetInst("MiniUIManager"):HideAllUIWithoutFrameName({
					"StarStationInfoFrameAutoGen","TaskTrackCtrl", "BossLifeInfoAutoGen","playermainAutoGen","socchatmainAutoGen",
					"MusicOpCompAutoGen","VehicleAssemblyTipsAutoGen", "AIDebugMainAutoGen", "DeveloperUIRoot", "TeachingAutoGen",
					"GuideDialogAutoGen", "MaskAutoGen", "main_SummerActivityAutoGen", "main_AutumnActivityAutoGen", 
					"main_CollectSealsAutoGen", "main_Act92FishCollectAutoGen", "Act93_FreePurchaseAutoGen", "main_Act97FishRankAutoGen"})
			end
		end
	end

	if ClientCurGame and ClientCurGame:isInGame() and GetInst("QQMusicPlayerManager") then
		-- 重新显示出来
		if GetInst("QQMusicPlayerManager"):IsMusicPlayerOpened() then
			GetInst("QQMusicPlayerManager"):OpenUI()
		end
	end 
	
	if ClientCurGame and ClientCurGame:isInGame() and GetInst("MiniClubPlayerManager") then
		-- 重新显示出来
		if GetInst("MiniClubPlayerManager"):IsOpen() then
			GetInst("MiniClubPlayerManager"):OpenUI()
		end
	end

	if not isHideMain and ClientCurGame and ClientCurGame:isInGame() and isShowCompass then
		-- 重新显示出来
		if IsInHomeLandMap and IsInHomeLandMap() then
		else
			PixelMapInterface:ShowCompass();
		end
	end

	if ClientCurGame and ClientCurGame:isInGame() and CurWorld then

	end
	if ClientCurGame and ClientCurGame:isInGame() and CurWorld then
        -- 重新显示出来
        HpBarFrame_Shown(isShowHpInfo)
    end
end

function HideAllFrameMiniUI(frameName, isHideMain)
	local isDead = false
	local isDeadFrame = false
	for i=1, #(t_DeathFrames_miniui) do
		if not isDead and GetInst('MiniUIManager'):IsShown(t_DeathFrames_miniui[i]) then
			isDead = true
		end

		if not isDeadFrame and frameName == t_DeathFrames_miniui[i] then
			isDeadFrame = true
		end
	end

	if isDead then
		if not isDeadFrame and frameName ~= nil then
			GetInst('MiniUIManager'):CloseUI(frameName)
			return
		end
	end

	local needHide = function(hideFrameName)
		local ignoreCfg = {
			["AccRideCallFrame"] = {"ToolModeFrame",};	--打开坐骑页面的时候不用隐藏'工具模式'页面
		};

		local list = ignoreCfg[frameName];
		if list then
			for i = 1, #list do
				if hideFrameName == list[i] then
					--忽略, 不用隐藏
					return false;
				end
			end
		end

		return true;
	end

	for i=1, #(t_allFrame) do
		if t_allFrame[i] ~= frameName then
			if needHide(t_allFrame[i]) and checkCanHideFrame(t_allFrame[i]) then
				local frame = getglobal(t_allFrame[i], true);	--在这个接口里，没加载的UI不需要去加载
				if frame and frame:IsShown() then
					local mvcFrame = GetInst("UIManager"):GetCtrl(t_allFrame[i]);
					if mvcFrame then
						if mvcFrame.CloseBtnClicked then
							print("kekeke mvc CloseBtnClicked");
							mvcFrame:CloseBtnClicked();
						else
							GetInst("UIManager"):Close(t_allFrame[i]); --更一般的关闭
						end
					else
						frame:Hide();
					end
				end
			end
		end
	end

	for key, value in pairs(t_allFrame_miniui) do
		if key ~= frameName then
			if needHide(key) and checkCanHideFrame(key) then
				if value.closeFunc then
					value.closeFunc()
				end
			end
		end
	end 

	if isHideMain then
		getglobal("GongNengFrame"):Hide();
		PlayMainFrameUIHide();
		--ClientCurGame:showOperateUI(false);
	end
	if IsInHomeLandMap and IsInHomeLandMap() then
		HomeLandGuideTaskCall("ShowUi", false)
	end

	GetInst("UGCCommon"):OnHideAllUI()
	if gFunc_IsNewMiniUIEnable and gFunc_IsNewMiniUIEnable() then
		local ignoreFrames = GetNotGameFGUIList()
		if frameName ~= nil then
			--table.insert(ignoreFrames, frameName)
			local destIgnoreFrames = {}
			for k, v in pairs(ignoreFrames or {}) do
				if v then
					table.insert(destIgnoreFrames, k)
				end
			end
			table.insert(destIgnoreFrames, frameName)
			GetInst("MiniUIManager"):HideAllUIWithoutFrameName(destIgnoreFrames)
		else
			GetInst("MiniUIManager"):HideAllUI(ignoreFrames)
		end
	end

	if ClientCurGame and ClientCurGame:isInGame() and GetInst("QQMusicPlayerManager") then
		-- 重新显示出来
		if GetInst("QQMusicPlayerManager"):IsMusicPlayerOpened() then
			GetInst("QQMusicPlayerManager"):OpenUI()
		end
	end

	if ClientCurGame and ClientCurGame:isInGame() and GetInst("MiniClubPlayerManager") then
		-- 重新显示出来
		if GetInst("MiniClubPlayerManager"):IsOpen() then
			GetInst("MiniClubPlayerManager"):OpenUI()
		end
	end

	if not isHideMain and ClientCurGame and ClientCurGame:isInGame() then
		-- 重新显示出来
		if IsInHomeLandMap and IsInHomeLandMap() then
		else
			PixelMapInterface:ShowCompass();
		end
	end

	if ClientCurGame and ClientCurGame:isInGame() and CurWorld then

	end
end

function GetNotGameFGUIList()
	if isAbroadEvn() then
		return {
			MainV2LobbyCtrl = true,
			MapArchiveListAutoGen = true,
			GameHallMainAutoGen = true,
			GuideStepView_CreateRole = true,
			GuideStepView_Lobby = true,
			GuideStepView_MultiLobby = true,
			GuideStepView_MoreGame = true,
			GuideStepView_Single = true,
			TaskTrackCtrl = true,
		}
	else
		return {
			BotConversationsFrameAutoGen = true,
			TaskTrackCtrl = true,
			MusicOpCompAutoGen = true,
			AIDebugMainAutoGen = true,
			StarStationInfoFrameAutoGen = true,
			TeachingAutoGen = true,
			GuideDialogAutoGen = true,
			MaskAutoGen = true,
			playermainAutoGen = true,
			socchatmainAutoGen = true,
		}
	end
end

function ShowMainFrame()
	if GetIWorldConfig():getGameData("hideui") == 1 then return end

	if IsInHomeLandMap and IsInHomeLandMap() then
		ShowHomeMainUI()
		getglobal("GongNengFrame"):Hide();
	else
		getglobal("GongNengFrame"):Show();
	end

	-- UGC内容重新显示
	GetInst("UGCCommon"):AfterHideAllUI();
	PlayMainFrameUIShow();
	GetInst("UGCCommon"):RefreshEditUI()
end

function HideAllUI(isHideHideFrame, isChangeDisplayName)
	for i=1, #(t_UIName) do
		local frame = getglobal(t_UIName[i]);
		if frame and frame:IsShown() then
			frame:Hide();
		end
	end
	
	GetInst("StarStationManager"):closeStarStationView(3)
	for i=1, #(t_allFrame) do
		local frame = getglobal(t_allFrame[i]);
		if frame and frame:IsShown() and checkCanHideFrame(t_allFrame[i]) then
			local mvcFrame = GetInst("UIManager"):GetCtrl(t_allFrame[i]);
			if mvcFrame then
				if mvcFrame.CloseBtnClicked then
					print("kekeke mvc CloseBtnClicked");
					mvcFrame:CloseBtnClicked();
				else
					GetInst("UIManager"):Close(t_allFrame[i]); --更一般的关闭
				end
			else
				frame:Hide();
			end
			
			-- frame:Hide();
		end
	end

	for key, value in pairs(t_allFrame_miniui) do
		if key ~= frameName then
			if checkCanHideFrame(key) then
				if value.closeFunc then
					value.closeFunc()
				end
			end
		end
	end

	if CUR_WORLD_MAPID > 0 then
		-- if IsUIFrameShown("InstanceTaskFrame") then
		-- 	getglobal("InstanceTaskFrame"):Hide();
		-- end

		if GetInst("MiniUIManager"):IsShown("BossLifeInfoAutoGen") then
			GetInst("MiniUIManager"):HideUI("BossLifeInfoAutoGen")
			MiniLog("LLL BossLifeInfo hide!!")
			BossHpShown = 1
		end

		if IsUIFrameShown("PlotFrame") then
			getglobal("PlotFrame"):Hide();
		end
	end
	--ios 里面调用这个函数会崩溃
	--if not IsIosPlatform() then
	if CurMainPlayer then
		local changeNameFlag = true
		if isChangeDisplayName ~= nil then
			changeNameFlag = isChangeDisplayName
		end
		CurMainPlayer:setUIHide(true, false, changeNameFlag);
	end
	--end
	if getglobal("UIHideFrame") then 
		if not isHideHideFrame then
			getglobal("UIHideFrame"):Show();
		else
			getglobal("UIHideFrame"):Hide();
		end
	end 

	if IsInHomeLandMap and IsInHomeLandMap() then
		HomeLandGuideTaskCall("ShowUi", false)
	end
end
-----------------------------------------------------OpenGame----------------------------------------------------------
local NeedOpenMakerRunGame = false;
function OpenGameCloseBtn_OnClick()
	getglobal("OpenGame"):Hide();
	NeedOpenMakerRunGame = false;
end

function OpenGameOkBtnBtn_OnClick()
	ClientCurGame:hostStartGame();
	getglobal("OpenGame"):Hide();
	NeedOpenMakerRunGame = false;
end

function GetNeedOpenMarkRunGame()
	return NeedOpenMakerRunGame
end

function SetNeedOpenMarkRunGame(value)
	NeedOpenMakerRunGame = value
end

function OpenGame_OnLoad()
	OpenGame_AddGameEvent()
end

-- 定时器停止装扮邀请动画
local actorInviteTimer = nil
-- 是否继续显示装扮邀请动画
isPlayingactorInviteAni = false
--装扮互动按钮冷却时间
isActorInviteBtnCoolingTime = false
--互动装扮操作时间
MAX_ACTION_INVITE_TIME = 15
function OpenGame_AddGameEvent()
	SubscribeGameEvent(nil, GameEventType.UpdateActorInvite, function(context)
		NewEventBridgeOldEvent(GameEventType.UpdateActorInvite,context)
		arg1 = GameEventType.UpdateActorInvite
		OpenGame_OnEvent()
	end)
	SubscribeGameEvent(nil,GameEventType.WaitHostStartGame,function(context)
		NewEventBridgeOldEvent(GameEventType.WaitHostStartGame,context)
		arg1 = GameEventType.WaitHostStartGame
		OpenGame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.CloseHostStartGame,function(context)
		NewEventBridgeOldEvent(GameEventType.CloseHostStartGame,context)
		arg1 = GameEventType.CloseHostStartGame
		OpenGame_OnEvent()
	end )
end
function OpenGame_OnEvent()
	if arg1 == "GIE_WAITHOST_STARTGAME" and IsRoomOwner() then
		NeedOpenMakerRunGame = true;
		getglobal("OpenGame"):Show();
	elseif arg1 == "GIE_CLOSE_HOST_STARTGAME" and IsRoomOwner() then
		NeedOpenMakerRunGame = false;
		getglobal("OpenGame"):Hide();
	elseif arg1 == "GIE_UPDATE_ACTORINVITE" then
		if GetInst("actionExpressionManager"):IsOpenNew() then
			GetInst("actionExpressionManager"):HangleUpdateActorInviteEvent()
		else
			local ge = GameEventQue:getCurEvent();
			local info = {
				targetUin = ge.body.actorInvite.targetuin;
				actId = ge.body.actorInvite.actId;
				inviteType = ge.body.actorInvite.inviteType;
				lastTime = MAX_ACTION_INVITE_TIME + os.time()
			}
			--0:收到邀请 1：邀请接受 2：邀请拒绝 3：邀请超时
			if info.inviteType == 0 then
				local refuseTime = getkv("ActorInviteRefuseTime"..info.targetUin) or nil
				local timeSpace = 3*60 -- 3小时
				local time = getServerTime() - timeSpace*60
				if refuseTime and tonumber(refuseTime) > tonumber(time)then
					return;
				end
				--收到邀请提示
				ShowGameTips(GetS(15292))
				--播放动画
				getglobal("ActorInviteTipBtn"):Show()
				local Ani = getglobal("ActorInviteTipBtnAni")
				if Ani then
					local actInviteDef = getActInviteDefById(info.actId)
					if actInviteDef then
						local imgUrl = "ui/mobile/effect/ui_zbhz_"..actInviteDef.ActID..".png"
						Ani:SetTexture(imgUrl,true)
						Ani:SetUVAnimation(120, true)
					end
				end
				isPlayingactorInviteAni = true
				if actorInviteTimer then
					threadpool:kick(actorInviteTimer)
				end
				-- 一定时间后隐藏
				actorInviteTimer = threadpool:delay(MAX_ACTION_INVITE_TIME, function()
					isPlayingactorInviteAni = false
					getglobal("ActorInviteTipBtn"):Hide()
				end)
				setActorInviteInfo(info)
			elseif info.inviteType == 1 then
				--接受操作
				local uin = CurMainPlayer:getUin()
				local targetUin = info.targetUin;
				CurMainPlayer:playSkinAct(info.actId,uin, targetUin)
			elseif info.inviteType == 2 then
				ShowGameTips(GetS(15289))
			elseif info.inviteType == 3 then
				ShowGameTips(GetS(15290))
			end
		end
	elseif arg1 == "GE_WORLD_CHANGE" then
		--存档埋点
		Report418Event(3)
	end
end

function OpenGame_OnHide()
	if not getglobal("OpenGame"):IsRehide() then
		ClientCurGame:setOperateUI(false);
	end
end

function OpenGame_OnShow()
	if not getglobal("OpenGame"):IsReshow() then
		ClientCurGame:setOperateUI(true);
	end
end

-------------------------------------------------RocketUIFrame---------------------------------------------
local RocketInSpace = false;
function RocketUIFrameChange(isshow, posy)
	if isshow then
		if not getglobal("RocketUIFrame"):IsShown() then
			getglobal("RocketUIFrame"):Show();
		end

		if getglobal("MapFrame"):IsShown() then
			getglobal("RocketUIFrameLeft"):Hide();
		else
			getglobal("RocketUIFrameLeft"):Show();
		end

		if posy >= 25600 then
			posy = 25600;
			if not RocketInSpace then
				RocketInSpace = true;
				getglobal("RocketUIFrameMask"):Show();
				getglobal("RocketUIFrameMask"):SetBlendAlpha(0);
			end
		else
			if RocketInSpace then
				RocketInSpace = false;
			end
			getglobal("RocketUIFrameMask"):Hide();
		end

		local offsetY = math.ceil(posy/25600*190);
		getglobal("RocketUIFrameLeftIcon"):SetPoint("bottom", "RocketUIFrameLeftBkg", "bottom", 0, -3-offsetY);
	else
		getglobal("RocketUIFrame"):Hide();
		--getglobal("CompassBirthplace"):Hide();
		RocketInSpace = false;
	end
end

function RocketUIFrame_OnUpdate()
	if RocketInSpace then
		local alpha = getglobal("RocketUIFrameMask"):GetBlendAlpha() + 1/60;
		--print("kekeke RocketUIFrame_OnUpdate alpha", alpha);
		if alpha >= 1 then
			alpha = 1;
			RocketInSpace = false;
			getglobal("RocketUIFrameMask"):Hide();
			if CurMainPlayer then
				local rocket = CurMainPlayer:getRidingRocket();
				if rocket then
					local targetMapId = CurMainPlayer:getCurWorldMapId() == 0 and 2 or 0;
					if CurMainPlayer:getCurWorldMapId() >= 0 and CurMainPlayer:getCurWorldMapId() <= 2 then
						getglobal("CompassBirthplace"):Show();
					else
						getglobal("CompassBirthplace"):Hide();
                    end
					rocket:teleportMap(targetMapId);
				end
			end
		end

		GetMusicManager():SetMusicVolume(1-alpha);
		getglobal("RocketUIFrameMask"):SetBlendAlpha(alpha);
	end
end

--------------------------------------------------PlayShortcut-----------------------------------------

function PlayShortcut_OnLoad()
	this:setUpdateTime(0.1);
	this:RegisterEvent("GE_SPRAY_PAINT");
	PlayShortcut_AddGameEvent()

    for i=1,MAX_SHORTCUT do
		local ShortcutBtn = getglobal("ToolShortcut"..i);
		local ShortcutIcon = getglobal("ToolShortcut"..i.."Icon")
		if GetClientInfo():isMobile() then
			ShortcutBtn:SetPoint("left", "PlayShortcut", "left", (i-1)*80+85, -2);
			ShortcutBtn:SetSize(80,80);
			ShortcutIcon:SetSize(68,68);
		else
			ShortcutBtn:SetPoint("left", "PlayShortcut", "left", (i-1)*57+63, -2);
			ShortcutBtn:SetSize(57,57)
		end

		-- ShortcutIcon:SetSize(0.935,0.935)
		local numFont = getglobal("ToolShortcut"..i.."Num");
		local numFontBG = getglobal("ToolShortcut"..i.."TagBkg");
		if GetClientInfo():isPC() then
		--	numFont:Show();
		--	numFontBG:Show();
		--else
			numFont:Hide();
			numFontBG:Hide();
		end
	end
end

function PlayShortcut_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.BackPackChange,function(context)
		NewEventBridgeOldEvent(GameEventType.BackPackChange,context)
		arg1 = GameEventType.BackPackChange
		PlayShortcut_OnEvent()
	end )

	SubscribeGameEvent(nil,GameEventType.ShortcoutSelected,function(context)
		NewEventBridgeOldEvent(GameEventType.ShortcoutSelected,context)
		arg1 = GameEventType.ShortcoutSelected
		PlayShortcut_OnEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.SprayPaintChange,function(context)
		NewEventBridgeOldEvent(GameEventType.SprayPaintChange,context)
		arg1 = GameEventType.SprayPaintChange
		PlayShortcut_OnEvent()
	end)
end

ShortCutNoviceShow 	= false;
ShortCutOffset 		= 0;
function PlayShortcut_OnUpdate()
	if getglobal("PlayShortcut"):IsShown() and ShortCutNoviceShow then
		ShortCutOffset = ShortCutOffset + 25;
		if ShortCutOffset > 100 then
			ShortCutOffset = 100;
			ShortCutNoviceShow = false;
		end
		getglobal("PlayShortcut"):SetPoint("bottom", "PlayMainFrame", "bottom", 0, 100-ShortCutOffset);
	end

	--skill cd
	for i=1, MAX_SHORTCUT do
		local cdBkg = getglobal("ToolShortcut"..i.."CoolingBkg");
		local cdPrecent = getglobal("ToolShortcut"..i.."CoolingPercent");

		local grid_index = ClientBackpack:getShortcutStartIndex() + i - 1;
		local itemId = ClientBackpack:getGridItem(grid_index);
		local cd = 0;
		if CurMainPlayer ~= nil then 
			local totaltime = 1;
			 cd, totaltime = CurMainPlayer:getSkillCDNew(itemId, cd, totaltime);
			 if cd <= 0 then
				cd = CurMainPlayer:getSkillCD(itemId);
				if cd > 0 then
					totaltime = CurMainPlayer:getTotalSkillCD(itemId);
				end
			 end
			 if cd > 0 then 
				cdBkg:Show();
				if totaltime > 0 then
					local pro = cd/totaltime;
					local size = 85;
					if GetClientInfo():isPC() then
						size = 60;
					end
	
					if pro > 1.0 then
						pro = 1.0;
					end
					cdBkg:SetSize(size*0.82, size*pro*0.82);
					cdPrecent:SetText(string.format("%.1f",cd));
				end
			else
				cdBkg:Hide();
				cdPrecent:SetText("");
			end
		end 
		cdBkg:Hide()
		cdPrecent:Hide()
	end

	--actionInvite cd
	UpdateCountDown(1)
end

function ShortCutFrame_UpdateOneGrid(grid_index)
	local n = grid_index+1;
	if grid_index >= ClientBackpack:getShortcutStartIndex() then
		n = n - ClientBackpack:getShortcutStartIndex()
	end

	local ShortcutBtn = getglobal("ToolShortcut"..n);
	local ShortcutIcon = getglobal("ToolShortcut"..n.."Icon");
	local ShortcutNum = getglobal("ToolShortcut"..n.."Count");
	local ShortDurBkg = getglobal("ToolShortcut"..n.."DurBkg");
	local ShortDur = getglobal("ToolShortcut"..n.."Duration");

	--家园特殊处理
	local maxDuration = 0
	if IsInHomeLandMap and IsInHomeLandMap() then
		local itemId = ClientBackpack:getGridItem(grid_index)
		local itemSid = ClientBackpack:getGridSidStr(grid_index)
		if itemId > 0 then
			local itemData = GetInst("HomeLandDataManager"):GetBackpackItemByItemIdAndSid(itemId, itemSid) or {}
			local extData = itemData.goods_extend
			if extData and extData.max_durability_value and extData.now_durability_value then
				maxDuration = extData.max_durability_value --最大耐久

				--设置当前的耐久
				if CurMainPlayer and ClientBackpack then
					local grid = CurMainPlayer:getBackPack():index2Grid(grid_index)
					if grid then
						grid:setDuration(extData.now_durability_value)
					end
				end
			end
		end
	end

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:SetWeaponidxIcon(n - 1, grid_index)
	end

	UpdateGridContent(ShortcutIcon, ShortcutNum, ShortDurBkg, ShortDur, grid_index, maxDuration)

	local ban = getglobal("ToolShortcut"..n.."Ban");
	CheckItemIsBan(grid_index, ban, ShortcutIcon);
	ShortcutBtn:Hide()
	ShortcutIcon:Hide()
	ShortcutNum:Hide()
	ShortDurBkg:Hide()
	ShortDur:Hide()
end

function ShortCutFrame_UpdateAllGrids()
	if not ClientBackpack then
		return
	end
	local maxIndex = MAX_SHORTCUT-1;
	for i=0, maxIndex, 1 do
		ShortCutFrame_UpdateOneGrid(ClientBackpack:getShortcutStartIndex()+i)
	end
end

-- 音乐系统处理
function MusicItem_Selected()
	if IsUGCEditing() and UGCModeMgr:GetGameType() == UGCGAMETYPE_BUILD  then
		GetInst("SceneEditorMsgHandler"):dispatcher(SceneEditorUIDef.common.music_item_selected)
		return
	end

	--
	if GetInst("MiniUIManager"):IsShown("MusicOpCompAutoGen") then
		GetInst("MiniUIManager"):CloseUI("MusicOpCompAutoGen", true)
	end

	-- getglobal("MusicPlayModeBtn"):Hide();
	-- getglobal("MusicPreinstallBtn"):Hide();
	
	local itemId = ClientBackpack:getGridItem(ClientBackpack:getShortcutStartIndex()+ShortCut_SelectedIndex)
	local main_player_free = GetInst("MiniUIManager"):GetUI("main_player_freeAutoGen")
	if main_player_free then
		GetInst("MiniUIManager"):CloseUI("main_player_freeAutoGen")
	end

	if itemId and itemId > 0 then
		local itemDef = ItemDefCsv:get(itemId)
		if itemDef.Type == ITEM_TYPE_MUSIC then

			if GetInst("songBookDataManager") then
				GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/c_ingame"}, "MusicOpCompAutoGen")
				GetInst("MiniUIManager"):OpenUI("MusicOpComp", "miniui/miniworld/MusicOpComp", "MusicOpCompAutoGen", {disableOperateUI=true})
				local musicOpCompCtrl = GetInst("MiniUIManager"):GetCtrl("MusicOpComp")

				--联机状态
				if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
					if GetInst("songBookDataManager"):checkSystemIsOpen() then
						--getglobal("MusicPlayModeBtn"):Show()
						musicOpCompCtrl:VisibleMusicPlayModeBtn(true)
					end
				else
					--getglobal("MusicPlayModeBtn"):Show()
					musicOpCompCtrl:VisibleMusicPlayModeBtn(true)
				end
				--getglobal("MusicPreinstallBtn"):Show()
				musicOpCompCtrl:VisibleMusicPreinstallBtn(true)

				local data = GetInst("songBookDataManager"):getCurData()

				if data then
					local nameStr = DefMgr:filterString(data.name)
					--getglobal("MusicPreinstallBtnTips"):SetText(nameStr)
					musicOpCompCtrl:UpdateMusicPreintallBtnTips(nameStr)
				end
			end

		end
	end
end

function Update_MusicItem_select()
	if IsUGCEditing() and UGCModeMgr:GetGameType() == UGCGAMETYPE_BUILD  then
		return
	end

	--
	if GetInst("MiniUIManager"):IsShown("MusicOpCompAutoGen") then
		GetInst("MiniUIManager"):CloseUI("MusicOpCompAutoGen", true)
	end

	local itemId = ClientBackpack:getGridItem(ClientBackpack:getShortcutStartIndex()+ShortCut_SelectedIndex)
	if itemId > 0 then
		local itemDef = ItemDefCsv:get(itemId)
		if itemDef.Type == ITEM_TYPE_MUSIC then

			if GetInst("songBookDataManager") then
				GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/c_ingame"}, "MusicOpCompAutoGen")
				GetInst("MiniUIManager"):OpenUI("MusicOpComp", "miniui/miniworld/MusicOpComp", "MusicOpCompAutoGen", {disableOperateUI=true})
				local musicOpCompCtrl = GetInst("MiniUIManager"):GetCtrl("MusicOpComp")

				--联机状态
				if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
					if GetInst("songBookDataManager"):checkSystemIsOpen() then
						--getglobal("MusicPlayModeBtn"):Show()
						musicOpCompCtrl:VisibleMusicPlayModeBtn(true)
					end
				else
					--getglobal("MusicPlayModeBtn"):Show()
					musicOpCompCtrl:VisibleMusicPlayModeBtn(true)
				end
				--getglobal("MusicPreinstallBtn"):Show()
				musicOpCompCtrl:VisibleMusicPreinstallBtn(true)

				local data = GetInst("songBookDataManager"):getCurData()

				if data then
					local nameStr = DefMgr:filterString(data.name)
					--getglobal("MusicPreinstallBtnTips"):SetText(nameStr)
					musicOpCompCtrl:UpdateMusicPreintallBtnTips(nameStr)
				end
			end

		end
	end
end

function ShortCutFrame_Selected(index)
	--编辑模式扩展快捷栏以后 index可能大于7
	index = index % MAX_SHORTCUT
	
	getglobal("BackpackFrame") -- 加载触发优化 code_by:huangfubin 2023.11.14
	if getglobal("BackpackFramePackFrame"):IsShown() or getglobal("StorageBoxFrame"):IsShown() then
		return;
	end

	if ShortCut_SelectedIndex >= 0 then
		local selbox = getglobal("ToolShortcut"..(ShortCut_SelectedIndex+1).."Check");
		selbox:Hide()
	end

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:SwitchSelectWeapon(ShortCut_SelectedIndex, index)
	end

	ShortCut_SelectedIndex = index;
	if getglobal("PlayMainFrame"):IsShown() and CurWorld ~= nil and CurWorld:getOWID() == NewbieWorldId then
		local taskId = AccountManager:getCurNoviceGuideTask();
		if taskId == 12 or taskId == 13 or taskId == 17 or taskId == 18 then
			SetGridEffectForNoviceGuide();
		end
	end

	local selbox = getglobal("ToolShortcut"..(ShortCut_SelectedIndex+1).."Check");
	selbox:Hide()

	-- 音乐系统处理
	MusicItem_Selected()

	UpdatePaintChangeBtn() -- 喷漆道具显示更新
	if CurMainPlayer:getCurToolID() == ITEM_PAINTTANK then
		local spray_id = getglobal("PaintChangeFrame"):GetClientUserData(0)
		if spray_id and spray_id > 0 then
			Paint_ReportEvent("MINI_TOOL_BAR", "GraffitiChooseButton", "view")
		end
	end
end

function HideShortCutAllItemBoxTexture()
	for i=1, MAX_SHORTCUT do
		local boxTexture = getglobal("ToolShortcut"..i.."Check");
		boxTexture:Hide();
	end
end

function PlayShortcut_OnEvent()
	local ge = GameEventQue:getCurEvent();
	if arg1 == "GE_BACKPACK_CHANGE" then
		local grid_index = ge.body.backpack.grid_index;
		if grid_index>=ClientBackpack:getShortcutStartIndex() and grid_index<ClientBackpack:getShortcutStartIndex()+1000 then
			
			ShortCutFrame_UpdateOneGrid(grid_index);
			if getglobal("RoleFrame"):IsShown() then
				UpdateRoleFrameShortcutOneGrid(grid_index);
			elseif getglobal("CreateBackpackFrame"):IsShown() then
				UpdateCreateBackpackFrameShortcutOneGrid(grid_index);
			elseif not ResourceCenterNewVersionSwitch and getglobal("MapModelLibFrame"):IsShown() then
				MapModelShortCutFrame_UpdateOneGrid(grid_index);
			elseif ResourceCenterNewVersionSwitch and HasUIFrame("ResourceCenter") and getglobal("ResourceCenter"):IsShown() then
				GetInst("UIManager"):GetCtrl("ResourceCenter"):UpdateShortCut(grid_index);
			elseif HasUIFrame("HomelandBackpack") and getglobal("HomelandBackpack"):IsShown() then
				GetInst("UIManager"):GetCtrl("HomelandBackpack"):UpdateOneShortcut(grid_index)
			end
		elseif grid_index < 0 then
			ShortCutFrame_UpdateAllGrids();
			if getglobal("RoleFrame"):IsShown() then
				UpdateRoleFrameShortcutAllGrid()
			elseif getglobal("CreateBackpackFrame"):IsShown() then
				UpdateCreateBackpackFrameShortcutAllGrid();
			elseif HasUIFrame("HomelandBackpack") and getglobal("HomelandBackpack"):IsShown() then
				GetInst("UIManager"):GetCtrl("HomelandBackpack"):UpdateShortcuts()
			end
		end

		--快捷栏 or --背包栏
		if (grid_index>=ClientBackpack:getShortcutStartIndex() and grid_index<ClientBackpack:getShortcutStartIndex()+1000) 
		or (grid_index >= BACKPACK_START_INDEX and grid_index <= BACK_PACK_GRID_MAX) then
			if grid_index and grid_index >=0 then
				local itemId = ClientBackpack:getGridItem(grid_index)
				local itemNum = ClientBackpack:getItemCountInNormalPack(itemId)
				if itemId and itemId ~= 0 then
					local context = MNSandbox.SandboxContext():SetData_Number("itemId", itemId):SetData_Number("itemNum", itemNum)
					SandboxLua.eventDispatcher:Emit(nil, "ITEM_ADD",  context)
				end
			end
		end

		SetGridEffectForNoviceGuide();
			-- 音乐系统处理
		MusicItem_Selected()
	elseif arg1 == "GE_SHORTCUT_SELECTED" then
		ShortCutFrame_Selected(ge.body.shortcut.selectgrid);
	elseif arg1 == "GE_SPRAY_PAINT" then
		SprayPaintChange(ge.body.spraypaint.isNext)
	end
end

function PlayShortcut_OnShow()
	--加载一个模型用来生成avatar头像
	--local model = UIActorBodyManager:getAvatarBody(2003, false)
	--model:setBodyType(3)
	--AddAvatarDefaultParts(model) 
	--临时方案
	updateLetters = MAX_SHORTCUT;
	SetGridEffectForNoviceGuide();

	ShortCutFrame_UpdateAllGrids();

	if not IsUGCEditMode() then
		--工具模式界面刷新
		if CurWorld and CurWorld:isGameMakerToolMode() then
			print("PlayShortcut_OnShow:XXX:");
			GetInst("UIManager"):GetCtrl("ToolModeFrame"):Refresh();
		end
	end
    
	local startIndex = SHORTCUT_START_INDEX;
	if ClientBackpack then --偶现ClientBackpack为nil的情况
		startIndex = ClientBackpack:getShortcutStartIndex();
	end
    for i=1,MAX_SHORTCUT do
        local ShortcutBtn = getglobal("ToolShortcut"..i)
        ShortcutBtn:SetClientID(startIndex + i)
    end
end

function HideGridEffectForNoviceGuide()
	
end

function SetGridEffectForNoviceGuide()
	
end

function ToolShortcutPlace(grid_index)
	if getglobal("StorageBoxFrame"):IsShown() then
		local itemId = ClientBackpack:getGridItem(grid_index);
		if itemId > 0 then
			CurMainPlayer:storeItem(grid_index, 1);
		end
	end
end

function SetToolShortcutTexture(btnName)
	HideShortCutAllItemBoxTexture()

	if btnName ~= nil then
		local texture = getglobal(btnName.."Check");
		--texture:Show();
	end
end

--存档评分界面打开按钮
function ArchiveGradeBtn_OnClick()
	if IsStandAloneMode() then
		ShowGameTips(GetS(25832),3)
		return
	end
	if not getglobal("ArchiveGradeFrame"):IsShown() then
		getglobal("ArchiveGradeFrame"):Show();
	else
		getglobal("ArchiveGradeFrame"):Hide();
	end
end

--成就界面打开按钮
function PlayAchievementBtn_OnClick()
	local AchievementFrame = getglobal("AchievementFrame")
	local riddlesUI = GetInst("MiniUIManager"):GetUI("MiniUIRiddlesMain");
	if not AchievementFrame:IsShown() and not riddlesUI then
		AchievementFrameType = 1;
		AchievementFrame:Show();
		AchievementFrame:SetPoint("center", "$parent", "center", 0, 0);
	else
		AchievementFrame:Hide();
	end
end
--坐骑信息界面打开按钮
function AccRideCallBtn_OnClick()
	if ClassList["UnitTestManager"]:GetInst():GetIsTest() then
		ClassList["UnitTestManager"]:GetInst():Start()
		return
	end
	-- UGCGetInst("GameTaskMgr"):SendTaskEvent(GameTaskEnum.TaskEvent.ClickRideCallBtn)
	-- 当前玩家是否可召唤坐骑
	if not CheckPlayerActionState(ENABLE_CALLRINDER) then
		ShowGameTipsWithoutFilter(GetS(34212), 3);
		return;
	end
	local MiniUIManager = GetInst("MiniUIManager")
	local riddlesUI = MiniUIManager:GetUI("MiniUIRiddlesMain");

	if MiniUIManager:IsShown("AccRideCallBox") then
		MiniUIManager:CloseUI("AccRideCallBox")
	else
		if not riddlesUI then
			MiniUIManager:AddPackage({"miniui/miniworld/commonTexture", "miniui/miniworld/commonButton"}, "AccRideCallBoxAutoGen")
			MiniUIManager:OpenUI("AccRideCallBox", "miniui/miniworld/AccRideCallBox", "AccRideCallBoxAutoGen")

			local boxUI = MiniUIManager:GetUI("AccRideCallBoxAutoGen");
			if not boxUI then
				if getglobal("AccRideCallFrame"):IsShown() then
					getglobal("AccRideCallFrame"):Hide();
				else
					getglobal("AccRideCallFrame"):Show();
				end
			end
		end	
	end
end

--过滤不需要显示使用按钮的变身装扮
local function needShowRideAttackBtn( skinId )
	local filterIds = { 64, 75, 103 ,196, 197, 278, 306, 307, 333, 344}
	for _,id in ipairs(filterIds) do
		if id == skinId then 
			return false
		end 
	end

	local skinDef = RoleSkinCsv:get(skinId)
	if skinDef and skinDef["SkillButton"] == 1 then
		return false
	end

	return true
end

function SetAccRideChangeBtnVisible(visible)
	MiniLog("SetAccRideChangeBtnVisible ", visible, debug.traceback())
	-- body
	if visible then
		getglobal("AccRideChangeBtn"):Show()
	else
		getglobal("AccRideChangeBtn"):Hide()
	end
	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("AccRideChangeBtn"), visible)
	end
end

--点击坐骑变形按钮
function AccRideChangeBtn_OnClick()

	if DisabledExecute() then
		return
	end

	if CurMainPlayer then
		if CurMainPlayer:isSleeping() or CurMainPlayer:isRestInBed() then
			CurMainPlayer:dismountActor();
			if CurMainPlayer:isShapeShift() then
				getglobal("AccRideAttackBtn"):Hide()
				getglobal("AccRideAttackLeftBtn"):Hide()
			end
		end

		if CurMainPlayer:InTransform() then
			CurMainPlayer:resetActorBody()
			return
		end
		local nowTime = AccountManager:getSvrTime()
		-- local skinId = CurMainPlayer:getSkinID()
		local skinId = GetMyInMapCurSkinId()
		local skinDef = RoleSkinCsv:get(skinId)
		if skinDef and skinDef["ChangeType"] == 2 then

			local diff = nowTime - lastTimeCombineTransform
			if diff >= 30 then
				if not CurMainPlayer:tryShapeShift(skinId) then
					return
				end
				lastTimeCombineTransform = nowTime
			else
				local rest = 30 - diff
				ShowGameTips(GetS(30356, rest))
			end
			return
		end
		
		if skinDef and skinDef["ChangeType"] > 0 then
			local changeRideId = skinDef.ChangeContact and skinDef["ChangeContact"][0] or 0;
			if CurMainPlayer:isShapeShift() then
				CurMainPlayer:dismountActor();
				getglobal("AccRideAttackBtn"):Hide()
				getglobal("AccRideAttackLeftBtn"):Hide()
				--路障皮肤变身为人型 
				-- if changeRideId == 4652 then
					ActorComponentCallModule(CurMainPlayer,"EffectComponent","playBodyEffectByName",skinDef.Effect)
				-- end
			else
				local time =  CurMainPlayer:getAccountHorseLiveAge(changeRideId)/20;
				if time < 0 then
					time = math.ceil(0-time);
					ShowGameTips(GetS(100260, time));
					return;
				end
				CurMainPlayer:tryShapeShift(skinId)
				if GetClientInfo():isMobile() and needShowRideAttackBtn(skinId) then --64-红蜘蛛 坐骑飞行 加速按钮无效不显示
					getglobal("AccRideAttackBtn"):Show()
					getglobal("AccRideAttackLeftBtn"):Show()
				end
				--路障皮肤变身为车型 所有变身皮肤变形后 都需要停止播放人形特效
				if CurMainPlayer.getBody then	--changeRideId == 4652 and 
					local body = CurMainPlayer:getBody();
					if body and body.setSkinEffect3Playing then
						body:setSkinEffect3Playing(true)
					end
					ActorComponentCallModule(CurMainPlayer,"EffectComponent","stopBodyEffectForTrigger",skinDef.Effect)
				end
			end
		end

		if getglobal("AccRideChangeBtnEffect"):IsShown() then
			getglobal("AccRideChangeBtnEffect"):Hide()
		end
	end
end

--点击坐骑攻击按钮
function AccRideAttackBtn_OnClick()
	if DisabledExecute() then
		return
	end
	if CurMainPlayer then
		CurMainPlayer:doSpecialSkill();
	end
end

--换弹夹按钮
function AccChangeMagazineCallBtn_OnClick()
	-- if GetClientInfo():isMobile() then
        local itemid = CurMainPlayer:getCurToolID()
        if itemid == ITEM_COLORED_GUN or itemid == ITEM_COLORED_EGG or itemid == ITEM_COLORED_EGG_SMALL or itemid == ITEM_COLOR_BRUSH or IsDyeableBlockLua(itemid)  then
            local curOpId=0;
		    local val, val2=0,0;
		    curOpId, val = CurWorld:getRuleOptionID(33, curOpId, val);
		    curOpId, val2 = CurWorld:getRuleOptionID(12, curOpId, val2);
            if false == (CurWorld:isGameMakerRunMode() == true and val ~= 0 and val2 ~= 0) then
                --ShowPaletteFrame()
				SceneEditorUIInterface:OpenUI("PaletteFrameMain", "miniui/miniworld/PaletteFrame", "PaletteFrameAutoGen", {})
            end
        else
		    CurMainPlayer:setReloadMagazine();
        end
	-- end
end

--切换喷漆图案
function PaintChangeBtn_OnClick()
	-- if GetClientInfo():isMobile() then
        local itemid = CurMainPlayer:getCurToolID()
        if itemid == ITEM_PAINTTANK then
            GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common", 
					"miniui/miniworld/common_comp"
				},
				"GameSprayPaintAutoGen"
			)
			-- 资源大小写问题，提前加载，后续要注意miniui/miniworld/xxx文件的大小写是否对得上
    		GetInst("MiniUIManager"):OpenUI("GameSprayPaint","miniui/miniworld/gameSprayPaint","GameSprayPaintAutoGen", {})
        end
		Paint_ReportEvent("MINI_TOOL_BAR", "GraffitiChooseButton", "click")
	-- end
end

--指针罗盘
function Compass_OnLoad()
	Compass_AddGameEvent()
end

function Compass_OnShow()
	local getglobal = _G.getglobal;
	getglobal("CompassCircle1"):Hide()
	getglobal("CompassCircle2"):Hide()
	getglobal("CompassHintLine"):Hide()
	if CurWorld and CurWorld:getCurMapID() >= 0 and CurWorld:getCurMapID() <= 2 then 
		getglobal("CompassBirthplace"):Show();
	else
		getglobal("CompassBirthplace"):Hide();
	end
end
function Compass_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.CompassChange,function(context)
		NewEventBridgeOldEvent(GameEventType.CompassChange,context)
		arg1 = GameEventType.CompassChange
		Compass_OnEvent()
	end )
end
function Compass_OnEvent()
	if arg1 == "GIE_COMPASS_CHANGE" then
		local ge = GameEventQue:getCurEvent();
		local compassData = ge.body.compass;
		UpdateCompass(compassData);
	end
end
--  -1000表示没有这个点;
local Player_Max_Num = 40;
local compass_update_flag = true;
--客机从一个world进入另一个world，这里会额外进行了一次有效的UpdateCompass
-- 在GIE_LEAVE_WORLD时暂停UpdateCompass
-- 在GIE_ENTER_WORLD时2s之后重新打开UpdateCompass
function Manipulate_CompassUpdate(isBool)
	compass_update_flag = isBool
end

local t_MapUITotemCache = {};
function GetMapUITotem(i)
	if not t_MapUITotemCache[i] then
		t_MapUITotemCache[i] = getglobal("CompassTotem"..i);
	end
	return t_MapUITotemCache[i];
end

local t_MapUITransferCache = {};
function GetMapUITransfer(i)
	if not t_MapUITransferCache[i] then
		t_MapUITransferCache[i] = getglobal("CompassTransfer"..i);
	end
	return t_MapUITransferCache[i];
end

local t_MapUIOPlayerCache = {};
function GetMapUIPlayer(i)
	if not t_MapUIOPlayerCache[i] then
		t_MapUIOPlayerCache[i] = getglobal("CompassPlayer"..i);
	end
	return t_MapUIOPlayerCache[i];
end

local t_MapUIStationCache = {};
function GetMapUIStation(i)
	if not t_MapUIStationCache[i] then
		t_MapUIStationCache[i] = getglobal("CompassStarStation"..i);
	end
	return t_MapUIStationCache[i];
end

local t_ComassUIName =  --预留UI名字 
{
	"Compass",
	"CompassBkg",
	"CompassPointer",
	"CompassDeathplace",
	"CompassDay",
	"CompassNight",
	"CompassSpecialNight",
}

local t_CompassUICache = {}
function GetCompassUI(uiName)
	if not t_CompassUICache[uiName] then
		t_CompassUICache[uiName] = getglobal(uiName)
		--MiniLog("GetCompassUI frist ", uiName)
	end

	--MiniLog("GetCompassUI Cache ", uiName)
	return t_CompassUICache[uiName]
end

function ClearCompassUICache()
	t_CompassUICache = {}
end

--频繁用到的全局变量，节省全局变量查找时间
local GetCompassUI = _G.GetCompassUI;
local GetMapUITotem = _G.GetMapUITotem;
local GetMapUITransfer = _G.GetMapUITransfer;
local GetMapUIPlayer = _G.GetMapUIPlayer;
local GetMapUIStation = _G.GetMapUIStation;
local MAX_TOTEM_NUM = _G.MAX_TOTEM_NUM;
local MAX_TRANSFER_NUM = _G.MAX_TRANSFER_NUM;
local Player_Max_Num = _G.Player_Max_Num;
local MAX_STARTSTATION_MAEKPOINT_NUM = _G.MAX_STARTSTATION_MAEKPOINT_NUM;
function UpdateCompass(compassData)
	local ClientCurGame = _G.ClientCurGame;

	if ClientCurGame == nil then
		return;
	end
	if not GetCompassUI("Compass"):IsShown() then
		return;
	end

	local angle = 90 - compassData.angle;

	if compassData.rotmode == 0 then
		GetCompassUI("CompassBkg"):SetAngle(angle);
		GetCompassUI("CompassPointer"):SetAngle(0);
	else
		GetCompassUI("CompassPointer"):SetAngle(90-angle);
		GetCompassUI("CompassBkg"):SetAngle(90);
	end

	local CompassBoss = GetCompassUI("CompassBoss")
	if compassData.guidex == -1000 then
		CompassBoss:Hide();
	else
		CompassBoss:Show();
		CompassBoss:SetPoint("center", "CompassBkg", "center", compassData.guidex, compassData.guidey);
		if compassData.hasboss then
			CompassBoss:SetTexUV("lzy_09")
		else
			CompassBoss:SetTexUV("lzy_altar")
		end

		
		if CurMainPlayer:getCurWorldMapId() == 0 and compass_update_flag then -- 虚空boss
			if not GetCompassUI("InstanceTaskFrame"):IsShown() then
				GetCompassUI("InstanceTaskFrame"):Show();
			end
		end
	end

	GetCompassUI("CompassBirthplace"):SetPoint("center", "CompassBkg", "center", compassData.spawnx, compassData.spawny);

	local CompassDeathplace = GetCompassUI("CompassDeathplace")
	if compassData.deadx == -1000 then
		CompassDeathplace:Hide();
	else
		CompassDeathplace:Show();
		CompassDeathplace:SetPoint("center", "CompassBkg", "center", compassData.deadx, compassData.deady);
	end

	local totemnum = WorldMgr and  WorldManager.getTotemPointNum and WorldManager:getTotemPointNum() or 0;
	for i=1, MAX_TOTEM_NUM do
		local totem = GetMapUITotem(i);
		local totempos = compassData.totempos[i-1];
		if totempos.px == -1000 then
			totem:Hide();
		else
			totem:Show();
			totem:SetPoint("center", "CompassBkg", "center", totempos.px, totempos.py);
			local size = 22;
			size = size - math.ceil((totemnum - 1) * 1.7);
			if size < 8 then
			   size = 8;
			end
			totem:SetSize(size, size);
		end
	end
	local transfernum = 0;
	for i=1, MAX_TRANSFER_NUM do
		local transfer = GetMapUITransfer(i);
		local transferpos = compassData.transferpos[i-1];
		if transferpos.px == -1000 then
			transfer:Hide();
		else
			transfer:Show();
			transfer:SetPoint("center", "CompassBkg", "center", transferpos.px, transferpos.py);
			transfernum = transfernum + 1;
		end
	end
	--for i=1,transfernum do
	--	local transfer = GetCompassUI("CompassTransfer"..i);
	--	local size = 22;
	--	size = size - math.ceil((transfernum - 1) * 1.7);
	--	if size < 8 then
	--	   size = 8;
	--	end
	--	transfer:SetSize(size, size);
	--end


	if ClientCurGame:isInGame() then
		local hour = ClientCurGame:getGameTimeHour();
		local surviveDay = CurMainPlayer:getSurviveDay();
		local CompassDay = GetCompassUI("CompassDay")
		local CompassNight = GetCompassUI("CompassNight")

		if surviveDay % 4 == 0 and (hour >=18 or  hour < 6) then
			GetCompassUI("CompassSpecialNight"):Show();
			CompassNight:Hide();
			CompassDay:Hide();
		else
			GetCompassUI("CompassSpecialNight"):Hide();

			if hour >= 6 and hour < 20 then
				CompassDay:Show();
				CompassNight:Hide();
			else
				CompassDay:Hide();
				CompassNight:Show();
			end
		end
	end

	if ClientCurGame:isInGame() then
		local playerNum = ClientCurGame:getNumPlayerBriefInfo();
		if type(Player_Max_Num) == "number" then
			for i=1, Player_Max_Num do
				local player = GetMapUIPlayer(i);
				player:Hide();
				if i <= playerNum then
					local playerInfo = compassData.playerpos[i-1];
					if playerInfo.px ~= -1000 then
						player:Show();
						player:SetPoint("center", "CompassBkg", "center", playerInfo.px, playerInfo.py);
						local size = 16;
						if playerNum > 5 then
							size = size - math.ceil((playerNum - 5) * 0.3);
						end
						player:SetSize(size, size);
					end
				end
	
			end
		end
		
	end

	if ClientCurGame:isInGame() and MAX_STARTSTATION_MAEKPOINT_NUM then
		for i=1, MAX_STARTSTATION_MAEKPOINT_NUM do
			local starStation = GetMapUIStation(i);
			local pos = compassData.starstationpos[i-1];
			if pos.px == -1000 then
				starStation:Hide();
			else
				starStation:Show();
				starStation:SetPoint("center", "CompassBkg", "center",pos.px, pos.py);
			end
		end
	end
end

--打开小地图
function CompassOpenMap_OnClick()
	for i=1, #(t_UIName) do
		local frame = getglobal(t_UIName[i]);
		if t_UIName[i] == 'PlayMainFrame' then
			PlayMainFrameUIHide();
		else
			frame:Hide();
		end
	end
	GetInst("StarStationManager"):closeStarStationView(3)
	HideAllFrame("MapFrame", true);
	getglobal("MItemTipsFrame"):Hide();

	ClientCurGame:enableMinimap(true);
	getglobal("MapFrame"):Show();

    CurMainPlayer:setUIHide(true, true);

    if getglobal("SleepNoticeFrame"):IsShown() then
        setkv("Sleep_Notice_Frame_Show", true)
        getglobal("SleepNoticeFrame"):Hide()
    end
	if getglobal("NewSleepNoticeFrame"):IsShown() then
		setkv("NewSleep_Notice_Frame_Show", true)
		getglobal("NewSleepNoticeFrame"):Hide()
	end
	if getglobal("GunMagazine"):IsShown() then
		setkv("GUN_Magazine_Show", true)
		getglobal("GunMagazine"):Hide()
	end
end
------------------------------------------------------PlayerInfo-------------------------------------------------------
AutoOpenPlayerInfo = true;
local ApplyPermitsCD = -1;
function InitMultiPlayerInfoFrame()
	ApplyPermitsCD = -1;
	RoomInteractiveData.t_KickInfo = {};
end
--踢人
function PlayerInfoKickPlayerBtn_OnClick()
	local index = this:GetClientUserData(0);
	if IsRoomOwner() and index > 0 then 	--主机才能踢人
		local briefInfo = ClientCurGame:getPlayerBriefInfo(index-1);
		if briefInfo ~= nil then
			MessageBox(5, GetS(446, briefInfo.nickname));
			GetInst("MessageBoxFrameMgr"):SetClientUserData(0, briefInfo.uin);
			GetInst("MessageBoxFrameMgr"):SetClientUserData(1, 1);
			GetInst("MessageBoxFrameMgr"):SetClientString( "房间踢人" );
		end
	else
		ShowGameTips(GetS(448), 3);
	end
end

function AddTodayKickPlayerCount()
	local keyC = "KickPlayerCount"
	local keyD = "KickPlayerCountDate"

	local curDate = os.date("%m-%d")

	local saveC = getkv(keyC)
	local saveD = getkv(keyD)
	if saveD == curDate then
		setkv(keyC, (saveC or 0) + 1)
	else
		setkv(keyC, 1)
		setkv(keyD, curDate)
	end
end

function GetTodayKickPlayerCount()
	local keyC = "KickPlayerCount"
	local keyD = "KickPlayerCountDate"

	local curDate = os.date("%m-%d")

	local saveC = getkv(keyC)
	local saveD = getkv(keyD)
	if saveD == curDate then
		return saveC or 0
	else
		return 0
	end
end

--确认踢人
function ConfirmKickPlayer(uin, kickertype)
	print("kekeke ConfirmKickPlayer", uin);
	local briefInfo = ClientCurGame:findPlayerInfoByUin(uin);
	if briefInfo then
		if RentPermitCtrl:IsRentRoom() == false then
			AccountManager:sendToClientKickInfo(0, briefInfo.uin); --通知被踢者他被踢了
		end
		kickertype = kickertype == nil and 1 or kickertype
		local kickername = AccountManager:getNickName()
		table.insert(RoomInteractiveData.t_KickInfo, {uin=briefInfo.uin, name=briefInfo.nickname, time=0.5, kickername=kickername, kickertype = kickertype});
		AddTodayKickPlayerCount()

		if OneKeyCar then
			OneKeyCar:V2_AddRoomTickPlayer(briefInfo.uin)
		end
	end
end

--收到申请权限
function OnReceiveApplyPermits(uin)
	if PermitsCallModuleScript("getPlayerRoomCommonPermitsType") ~= ROOM_GAME_COMMON_SET_GUEST then
		local briefInfo = ClientCurGame:findPlayerInfoByUin(uin);
		if briefInfo then
			ChatDisplayTime = 10.0;
			local sp = AccountManager:getBlueVipIconStr(briefInfo.uin)..briefInfo.nickname;
			RoomInteractiveData:AddRoomChat({type="permits_msg", speaker=sp, uin=briefInfo.uin, id=RoomInteractiveData.msgId});
			RoomInteractiveData:UpdateRoomChat();
		end
	end
end

--拒绝
function MultiPlayerInfoReceiveApplyRefuseBtn_OnClick()

end

--同意
function MultiPlayerInfoReceiveApplyAgreeBtn_OnClick()

end

--申请权限
function MultiPlayerInfoApplyPermitsBtn_OnClick()
	if AccountManager:getMultiPlayer() == 2 and ClientCurGame:isInGame() then --客机才能申请
		if ApplyPermitsCD > 0 then
			ShowGameTips(GetS(4982), 3);
		else
			ApplyPermitsCD = 10;
			local uin = AccountManager:getUin();
			ClientCurGame:applyPermits(uin);
			ShowGameTips(GetS(1187), 3);
			getglobal("RoomUIFrameCenterFuncFrame"):Hide();
		end
	end
end

function OnRespApplyPermits(ret)
	if ret == 0 then
		ShowGameTips(GetS(4984), 3);
	elseif ret == 1 then
		ShowGameTips(GetS(4983), 3);
	end
	ApplyPermitsCD = -1;
end

function MultiPlayerInfoInfo_OnShow()

end

function MultiPlayerInfoSwitch_OnClick()

end


function MultiPlayerInfo_OnLoad()
	this:setUpdateTime(0.1);
end

function MultiPlayerInfo_OnEvent()

end

function MultiPlayerInfo_OnUpdate()
	if not ClientCurGame:isInGame() then return end

	local num = ClientCurGame:getNumPlayerBriefInfo()

	local MultiPlayerInfoInfo = getglobal("MultiPlayerInfoInfo")
	if AutoOpenPlayerInfo and num > 0 then
		AutoOpenPlayerInfo = false;
		if not MultiPlayerInfoInfo:IsShown() then
			getglobal("MultiPlayerInfoSwitchOn"):Hide();
			getglobal("MultiPlayerInfoSwitchOff"):Show();
			SetPlayerInfoState();
		end
	end
	if MultiPlayerInfoInfo:IsShown() and ClientCurGame:isInGame() then
		local num = ClientCurGame:getNumPlayerBriefInfo();
		for i=1, 40 do
			local player = getglobal("MultiPlayerInfoInfoInfoPlayer"..i);
			if i <= num then
				player:Show();
				local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
				if briefInfo ~= nil then
					local name 			= getglobal(player:GetName().."PlayerName");
					local hp 			= getglobal(player:GetName().."Hp");
					local hpBkg 		= getglobal(player:GetName().."HpBkg");
					local kick			= getglobal(player:GetName().."KickPlayerBtn");
					local speakerIcon	= getglobal(player:GetName().."SpeakerIcon");

					name:SetText(briefInfo.nickname);

					local ratio = briefInfo.hp/MainPlayerAttrib:getMaxHP();
					if ratio > 1 then ratio = 1 end
					if CurWorld:isGodMode() then
						hp:Hide();
						hpBkg:Hide();
					else
						hpBkg:Show();
						hp:SetSize(ratio*196, 3);
						if briefInfo.hp >= 15 then
							hp:SetColor(0, 255, 0);
						elseif briefInfo.hp >= 8 then
							hp:SetColor(255, 255, 0);
						else
							hp:SetColor(255, 0, 0);
						end

						if briefInfo.hp <= 0 then
							hp:Hide();
						else
							hp:Show();
						end
					end

					if IsRoomOwner() then 	--主机才显示踢人按钮
						kick:Show();
						kick:SetClientUserData(0, i);
					else
						kick:Hide();
						kick:SetClientUserData(0, 0);
					end

					local uiVipIcon1 = getglobal(player:GetName().."VipIcon1");
					local uiVipIcon2 = getglobal(player:GetName().."VipIcon2");
					local vipDisp = UpdateVipIcons(briefInfo.vipinfo, uiVipIcon1, uiVipIcon2);
					name:SetPoint("left", player:GetName(), "left", 27+vipDisp.nextUiOffsetX, 0);

					if briefInfo.YMspeakerswitch == 0 then
						speakerIcon:Hide()
					else
						speakerIcon:Show();
					end
				end
			else
				player:Hide();
			end
		end
	end

	local height = 255;
	if num > 5 then
		height = 255 + 51 * (num - 5);
	end
	getglobal("MultiPlayerInfoInfoInfoPlane"):SetSize(198, height);

	--踢人
	local t_RemoveIdx = {};
	for i=#RoomInteractiveData.t_KickInfo, 1, -1 do
		if RoomInteractiveData.t_KickInfo[i].time < 0 then
			if RentPermitCtrl:IsRentRoom() then
				RentPermitCtrl:KickPlayer(RoomInteractiveData.t_KickInfo[i].uin,1,RoomInteractiveData.t_KickInfo[i].name,RoomInteractiveData.t_KickInfo[i].kickertype)
			else
				AccountManager:requestRoomKickPlayer(RoomInteractiveData.t_KickInfo[i].uin);
				local text = GetS(500, RoomInteractiveData.t_KickInfo[i].name);
				ShowGameTips(text, 3);
				text = GetS(700, RoomInteractiveData.t_KickInfo[i].name);
				ClientCurGame:sendChat(text, 1);
			end
			table.remove(RoomInteractiveData.t_KickInfo, i);
		else
			RoomInteractiveData.t_KickInfo[i].time = RoomInteractiveData.t_KickInfo[i].time - arg1;
		end
	end
end

function MultiPlayerInfo_OnShow()

end

--为语音房 获得特殊标记
function GVoiceGenRoomToken()
    if ClientCurGame then
        local key = nil
        if ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then
            key = RentPermitCtrl:GetRentRoomID()
        end

        if "string" ~= type(key) or "" == key then
			if ClientCurGame.getHostUin then
				key = tostring(ClientCurGame:getHostUin())
			else
				key = ""
			end
        else
            if not string.match(key, '^%d+_%d+$') then
                key = gFunc_getmd5(key)
            end 
        end
        return key
    end

    return ''
end

local GVoiceGuideTime = 10;	--10s
function GVoiceGuide_OnUpdate()
	GVoiceGuideTime = GVoiceGuideTime - arg1;
	if GVoiceGuideTime < 0 then
		getglobal("GVoiceGuide"):Hide();
		AccountManager:setNoviceGuideState("gvoiceguide", true);
	end
end

function GVoiceJoinRoomBtn_OnShow()
	if not (GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):CheckAutoOpenVoice()) then
		return
	end
	if GYouMeVoiceMgr:isJoinRoom() then
		if not GetInst("TeamVocieManage"):isInTeamVocieRoom() then
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP)  then--xyang自定义UI
				getglobal("GVoiceJoinRoomBtn"):Hide();
				getglobal("SpeakerSwitchBtn"):Show();
				GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("SpeakerSwitchBtn"), true)
			end
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MICRO)  then--xyang自定义UI
				getglobal("MicSwitchBtn"):Show();
			end
		end
	end
end

function RefreshVoiceRoomBtns()
	if GYouMeVoiceMgr:isJoinRoom() then
		if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
			getglobal("GVoiceJoinRoomBtn"):Hide();
			GetInst("TeamVocieManage"):TeamButtonInit()
		end
	end
end

function CancelSoundMute()
	if GetClientInfo():isPC() then
		threadpool:delay(0.01,function()
			-- 焦点丢失会对静音会设置为true，导致输入框显示的时候背景音乐会被屏蔽
			GetClientInfo():setPause(false)
		end)
	end
end

function MultiChatBtn_OnClick()
	if isAbroadEvn() then
		AccelKey_Chat();
	else
		GetInst("ChatHelper"):OpenChatView()
		local chatViewCtrl =  GetInst("MiniUIManager"):GetCtrl("chat_view")
		if chatViewCtrl then
			chatViewCtrl:SetChatViewType(1)
			if not chatViewCtrl:CheckMobile() then
				chatViewCtrl:UpdateEditBoxStatus(true)
			else
				chatViewCtrl:ResetTween()
				chatViewCtrl:SetViewDelayClose()
			end
		end
	end
	getglobal("ChatViewGuide"):Hide()
	setkv("chatViewGuideStepTwo",1)
	CancelSoundMute()
end


function GVoiceJoinRoomBtn_OnClick()
	threadpool:work(function ()
		-- action_id:1 限制语音   action_id:2限制聊天
		if GetInst("CreditScoreService"):CheckLimitAction(GetInst("CreditScoreService"):GetTypeTbl().voice) then
			-- print("FriendChatSendBtn_OnClick 信用分过低，限制语音")
			return	
		end

        --过滤工具制作地图
        if WorldMgr and WorldMgr:isNewSandboxNodeGame() then
            return	
        end

		if isAbroadEvn() or (GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):CheckAutoOpenVoice()) then
			--处于禁言状态不能加入语音房间
			if CheckVoiceMuteStatus() then
				--判断现在是否可用 
				if isAbroadEvn() or checkCanOpenMicAndSpeaker() then
					if GYouMeVoiceMgr and GYouMeVoiceMgr:isInit() then
						GetIWorldConfig():setGameData("speakerswitch", GetInst("TeamVocieManage").define.close);
						GetIWorldConfig():setGameData("micswitch", GetInst("TeamVocieManage").define.close);
						local ret = GYouMeVoiceMgr:joinVoiceRoom();
						if ret ~= YOUME_SUCCESS then
							if GetClientInfo():isAndroid() and ret == YOUME_ERROR_REC_NO_PERMISSION_EXT then
								--android 拉起授权弹框 不要做任何提示
							else
								ShowGameTips( GetS(3675, ret) )
							end
						end
					else
						ShowGameTips( GetS(3675, -1) );
					end
				end
			end
		end
	
		
		local sid = "1001"
		if IsRoomOwner() then
			sid = "1003"
		end
		-- statisticsGameEvent(801);
	end)
end

function MicSwitchBtn_OnHide()
	if not (GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):CheckAutoOpenVoice() ) then
		return
	end
	if GYouMeVoiceMgr:isJoinRoom() then
		if getglobal("PlayShortcut"):IsShown() and not GetInst("TeamVocieManage"):isInTeamVocieRoom()  then
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then --xyang自定义UI
				getglobal("SpeakerSwitchBtn"):Show();
				GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("SpeakerSwitchBtn"), true)
			end
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MICRO)  then--xyang自定义UI
				getglobal("MicSwitchBtn"):Show();
			end
		end
	end
end

function MicSwitchBtn_OnClick()
	if isAbroadEvn() then
		--LLDO:13岁保护模式特殊处理: 不让点击, 点击飘字
		if IsProtectMode() then
			ShowGameTips(GetS(20211), 3);
			return;
		end
		GVoiceMicSwitch()
	end
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):MicSwitchBtn_OnClick()
	end
end



function SpeakerSwitchBtn_OnHide()
	if not (GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):CheckAutoOpenVoice() ) then
		return
	end
	if GYouMeVoiceMgr:isJoinRoom() then
		if getglobal("PlayShortcut"):IsShown() and not GetInst("TeamVocieManage"):isInTeamVocieRoom() then--xyang自定义UI
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
				getglobal("SpeakerSwitchBtn"):Show();
				GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("SpeakerSwitchBtn"), true)
			end
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MICRO)  then--xyang自定义UI
				getglobal("MicSwitchBtn"):Show();
			end
		end
	end
end

function SpeakerSwitchBtn_OnClick()

	if isAbroadEvn() then
		GVoiceSpeakerSwitch();
	else
		if GetInst("GameVoiceManage") then
			GetInst("GameVoiceManage"):SpeakerSwitchBtn_OnClick();
		end	
	end
end

function TeamMicSwitchBtn_OnClick()

	if isAbroadEvn() then
		if GetClientInfo():isAndroid() then
			if MINIW__CheckHasPermission and MINIW__CheckHasPermission(DevicePermission_MicroPhone) then
				GetInst("TeamVocieManage"):TeamupMainBottomLeftMenuMicSwitchBtClick(false)
			else
				MessageBox(5, GetS(50125), function (btn)
					if btn == "left" then
						if MINIW__OpenSystemSetting and GetClientInfo():isAndroid() then
							MINIW__OpenSystemSetting()
						end
					end
				end)
				return
			end
		elseif GetClientInfo():isApple() then
			if SdkManager.CheckHasPermission and SdkManager:CheckHasPermission(DevicePermission_MicroPhone) then
				GetInst("TeamVocieManage"):TeamupMainBottomLeftMenuMicSwitchBtClick(false)
			else
				-- 没有权限
				MessageBox(5, GetS(50125), function (btn)
					if btn == "left" then
						if SdkManager.OpenSystemSetting and GetClientInfo():isApple() then
							SdkManager:OpenSystemSetting();
						end
					end
				end)
				return
			end
		else --PC
			GetInst("TeamVocieManage"):TeamupMainBottomLeftMenuMicSwitchBtClick(false)
		end

	else
		GetInst("TeamVocieManage"):TeamupMainBottomLeftMenuMicSwitchBtClick(false)
	end

end

function TeamSpeakerSwitchBtn_OnClick()
	GetInst("TeamVocieManage"):TeamupMainBottomLeftMenuSpeakerSwitchBtn(false)
end





function VoiceTipsFrame_OnLoad()
	this:setUpdateTime(0.05);
end

function VoiceTipsFrame_OnUpdate()
	local alpha = getglobal("VoiceTipsFrameIcon"):GetBlendAlpha() - 0.1;
	if alpha < 0 then
		alpha = 0;
		getglobal("VoiceTipsFrame"):Hide();
	end

	getglobal("VoiceTipsFrameIcon"):SetBlendAlpha(alpha);
end

function ReceiveVoiceBtn_OnClick()
end

function ReceiveVoiceBtn_OnMouseDown()
	YvMgr:recordStart("", "");
end

function ReceiveVoiceBtn_OnMouseUp()
	YvMgr:stopRecord();
end

------------------------------------------队伍设置------------------------------------------
TeamSetterCtrl = {
	ColorDefine = {
		{ r = 255, 	g = 249, 	b = 235},	--0:无队伍
		{ r = 255, 	g = 87, 	b = 69 },	--1.红
		{ r = 69, 	g = 139, 	b = 225 },	--2.蓝
		{ r = 37, 	g = 198, 	b = 105 },	--3.绿
		{ r = 255, 	g = 210, 	b = 0 },	--5.黄
		{ r = 255, 	g = 128, 	b = 64 }, 	--6.橙
		{ r = 163, 	g = 73, 	b = 164 },	--4.紫
	},

	getBaseSettingMgr = function(self)
		if WorldMgr then
			local BaseSettingMgr = WorldMgr:getBaseSettingManager();
			return BaseSettingMgr;
		end

		return nil;
	end,

	getTeamsNum = function(self)
		local BaseSettingMgr = self:getBaseSettingMgr();
		local num = 0;
		if BaseSettingMgr then
			for i = 1, 6 do
				if BaseSettingMgr:getTeamEnable(i) then
					num = num + 1
				end
			end
			-- num = BaseSettingMgr:getEnabledTeamsNum(); -- 这个接口获取的数据不准确
		end

		return num;
	end,

	getEnabledTeamIdByIndex = function(self, index)
		local BaseSettingMgr = self:getBaseSettingMgr();
		local TeamId = 0;
		if BaseSettingMgr then
			TeamId = BaseSettingMgr:getEnabledTeamIdByIndex(index);
		end

		return TeamId;
	end,

	getIndexByTeamId = function(self, teamId)
		local BaseSettingMgr = self:getBaseSettingMgr();
		local index = 0;
		if BaseSettingMgr then
			index = BaseSettingMgr:getIndexByTeamId(teamId);
		end

		return index;
	end,

	getColorCfgByIndex = function(self, index)
		local TeamId = self:getEnabledTeamIdByIndex(index);
		local colorCfg = self.ColorDefine[TeamId + 1];
		return colorCfg;
	end
};

--战场信息面板
function BattleBtn_OnLoad()
	BattleBtn_AddGameEvent()
end

function BattleBtn_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.CustomGameStage,function(context)
		NewEventBridgeOldEvent(GameEventType.CustomGameStage,context)
		arg1 = GameEventType.CustomGameStage
		BattleBtn_OnEvent()
	end )
end
function BattleBtn_OnEvent()
	if arg1 == "GE_CUSTOMGAME_STAGE" then
		if getglobal("BattleBtn"):IsShown() then
			local ge = GameEventQue:getCurEvent();
			local stage = ge.body.cgstage.stage;
			local gametime = ge.body.cgstage.gametime;

			if stage == 3 or stage == 4 then
				if stage == 4 then
					getglobal("BattleBtnTime"):SetText(GetS(3191));
				end

				--击杀数量
				-- local teamNum = ClientCurGame:getNumTeam();
				local teamNum = TeamSetterCtrl:getTeamsNum();
				if teamNum == 0 or CurMainPlayer:getTeam() == 0 then	--无队伍
					local score = ClientCurGame:getTeamScore(0);
					getglobal("BattleBtnScore1"):SetText(score);
					getglobal("BattleBtnScore1"):SetTextColor(255, 249, 235);
				else
					for i = 1, teamNum do
						local scoreFont = getglobal("BattleBtnScore"..i);
						local teamId = TeamSetterCtrl:getEnabledTeamIdByIndex(i);
						-- local score = ClientCurGame:getTeamScore(i);
						local score = ClientCurGame:getTeamScore(teamId);
						local colorCfg = TeamSetterCtrl:getColorCfgByIndex(i);

						scoreFont:SetText(score);
						scoreFont:SetTextColor(colorCfg.r, colorCfg.g, colorCfg.b);
					end
				end

				if stage == 4 and gametime ~= 0 then return end
				if stage ~= 4 then
					local s = CurWorld:getCustonGameTime();
					local timeText = "";
					if s == 0 then
						timeText = GetS(680);
					else
						s = s - math.floor(ge.body.cgstage.gametime/20);
						local min = math.floor(s/60);
						s = s - min*60;
						timeText = min.."m"..s.."s";
					end

					getglobal("BattleBtnTime"):SetText(timeText);
				end
			else
				getglobal("BattleBtnTime"):SetText(GetS(741));
				-- local teamNum = ClientCurGame:getNumTeam();
				local teamNum = TeamSetterCtrl:getTeamsNum();
				if teamNum == 0 or CurMainPlayer:getTeam() == 0 then	--无队伍
					local score = ClientCurGame:getTeamScore(0);
					getglobal("BattleBtnScore1"):SetText(score);
					getglobal("BattleBtnScore1"):SetTextColor(255, 249, 235);
				else
					for i=1, teamNum do
						local scoreFont = getglobal("BattleBtnScore"..i);
						local score = ClientCurGame:getTeamScore(i);
						local colorCfg = TeamSetterCtrl:getColorCfgByIndex(i);

						scoreFont:SetText(score);
						scoreFont:SetTextColor(colorCfg.r, colorCfg.g, colorCfg.b);
					end
				end
			end

			local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
			if myBriefInfo ~= nil and  myBriefInfo.teamid ~= 999 then
				local score = myBriefInfo.cgamevar and  myBriefInfo.cgamevar[0] or 0
				if GetInst("WeekendCarnivalMgr") then
					GetInst("WeekendCarnivalMgr"):updateTeamScore(score)
				end
			end
		end
	end
end

function BattleBtn_OnShow()
	getglobal("BattleBtn"):SetFrameStrataInt(2);
	getglobal("BattleBtn"):SetFrameLevel(3000);
	getglobal("BattleBtnTime"):Show();

	for i=1, Team_Max_Num do
		local scoreFont = getglobal("BattleBtnScore"..i);
		scoreFont:SetText("0");
	end
	local type = ClientCurGame and ClientCurGame.getRuleOptionVal and ClientCurGame:getRuleOptionVal(22);	--时间结束的胜负
	if type == 0 then
		getglobal("BattleBtnTime"):SetTextColor(255, 255, 255);
	elseif type == 1 then
		getglobal("BattleBtnTime"):SetTextColor(0, 255, 0);
	elseif type == 2 then
		getglobal("BattleBtnTime"):SetTextColor(255, 0, 0);
	end
	getglobal("BattleBtnTime"):SetText("0");
end

function BattleBtn_OnClick()
	if GetInst("MiniUIManager"):IsShown("BattleFrameAutoGen") then --已结算
		GetInst("MiniUIManager"):CloseUI("BattleFrameAutoGen");
	else
		GetInst("MiniUIManager"):OpenUI("BattleFrame", "miniui/miniworld/ugc_battleFrame", "BattleFrameAutoGen", {});
	end

	if getglobal("BattleEndFrameScoreboardBtnUvA"):IsShown() then
		getglobal("BattleEndFrameScoreboardBtnUvA"):Hide();
	end
end

------------------------------------BattlePrepareFrame----------------------------------------------------------------
function BattlePrepareFrameStartGame_OnClick()
	ClientCurGame:hostStartGame();
end

function BattlePrepareFrame_OnLoad()
	this:setUpdateTime(0.1);
end

function BattlePrepareFrame_OnUpdate()
	if BattleCoundBlinkingTime > 0 then
		 if getglobal("BattlePrepareFrameTips"):IsShown() then
		 	getglobal("BattlePrepareFrameTips"):Hide();
		 else
		 	getglobal("BattlePrepareFrameTips"):Show();
		 end

		 BattleCoundBlinkingTime = BattleCoundBlinkingTime-1;
	end
end

function BattlePrepareFrame_OnShow()
	InitBattlePrepareFrame();
end

function InitBattlePrepareFrame()
	local num = ClientCurGame:getRuleOptionVal(11); -- 开启人数
	getglobal("BattlePrepareFrameStartMinimum"):SetText(GetS(1339, num));
	getglobal("BattlePrepareFrameTips"):SetText(GetS(1343));
	getglobal("BattlePrepareFrameStartGame"):Hide();
	OnChangeNumOfPlayers();
end

function GetCurRoomPlayerNum()
	local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
	local num = myBriefInfo.teamid == 999 and 0 or 1; --裁判不算玩家人数

	local playernum = ClientCurGame:getNumPlayerBriefInfo();
	for i = 1, playernum do
		local BriefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
		if BriefInfo ~= nil and BriefInfo.teamid ~= 999 then
			num = num + 1;
		end
	end

	return num;
end

function OnChangeNumOfPlayers(playerNum)
	if CurWorld and CurWorld:isGameMakerRunMode() then
		if getglobal("BattlePrepareFrame"):IsShown() then
			local num = GetCurRoomPlayerNum();	--房间现有人数
			num = playerNum or num
			getglobal("BattlePrepareFramePlayersNum"):SetText(GetS(1340, num, ClientCurGame:getMaxPlayerNum()));
	
			if ClientCurGame:getGameStage() > 1 or ClientCurGame:getRuleOptionVal(10) ~= 0 then return end 	--非准备阶段或不是房主开启房间
	
			if num >= ClientCurGame:getRuleOptionVal(11) then	--人满了
				if IsRoomOwner() or IsCloudServerRoomOwner() then
					if not getglobal("BattlePrepareFrameStartGame"):IsShown() then
						getglobal("BattlePrepareFrameTips"):SetText("");
						getglobal("BattlePrepareFrameStartGame"):Show();
	
						NeedOpenMakerRunGame = true;
						getglobal("OpenGame"):Show();
						if GetInst("MiniUIManager") then				
							GetInst("MiniUIManager"):PushUIViewToHistory("OpenGame", {enableEscapeClose = true,
							onAccelKey_Escape = OpenGameCloseBtn_OnClick
							}, true, true);						
						end
					end
				else
					getglobal("BattlePrepareFrameTips"):SetText(GetS(1341));
				end
			elseif num < ClientCurGame:getRuleOptionVal(11) then 	--人数不足
				if IsRoomOwner() or IsCloudServerRoomOwner() then
					if getglobal("BattlePrepareFrameStartGame"):IsShown() then
						getglobal("BattlePrepareFrameTips"):SetText(GetS(1343));
						getglobal("BattlePrepareFrameStartGame"):Hide();
	
						NeedOpenMakerRunGame = false;
						getglobal("OpenGame"):Hide();
					end
				else
					getglobal("BattlePrepareFrameTips"):SetText(GetS(1343));
				end
			end
		end
	end
	
	if getglobal("RoomUIFrame") and getglobal("RoomUIFrame"):IsShown() then
		RoomUIFramePlayerInfo_OnUpdate();
	end
end

function OnUpdateNumOfPlayers(num)
end

function OnChangeGameStage(stage)
	print("kekeke OnChangeGameStage", stage);
	if stage == 3 then --游戏开启阶段
		getglobal("BattlePrepareFrame"):Hide();

		threadpool:work(function ()
			local tryTime = 10;
			while ClientCurGame and not ClientCurGame.getRuleOptionVal and tryTime > 0 do
				threadpool:wait(1);
				tryTime = tryTime - 1;
			end

			if IsShowBattleBtn() and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SCORE) then--xyang自定义UI
				--getglobal("BattleBtn"):Show();
				SetBattleBtn();
			end
		end)

	end

	if stage > 1 and NeedOpenMakerRunGame and getglobal("OpenGame"):IsShown() then
		NeedOpenMakerRunGame = false;
		getglobal("OpenGame"):Hide();
	end
end
-----------------------------------------------------------------------------------------------------------------------

-- 这个全局函数 BuffIconFramePageCtrl:PlayerBuff_OnUpdate 中还在调用
function StarveBuffChange(hasStarveBuff)
	if UGCModeMgr and UGCModeMgr:IsEditing() then
		return 
	end	
	local hpWidth = getglobal("PlayerHPBarCur"):GetWidth();
	local foodWidth = getglobal("PlayerHungerBarCur"):GetWidth();

	if hasStarveBuff then
		getglobal("PlayerHPBarIcon"):SetTexUV("icon_heart");
		getglobal("PlayerHPBarCur"):SetTexUV("img_blood_strip_red");

		getglobal("PlayerHungerBarIcon"):SetTexUV("icon_chicken_leg");
		getglobal("PlayerHungerBarCur"):SetTexUV("img_blood_strip_yellow");
	else
		getglobal("PlayerHPBarIcon"):SetTexUV("icon_heart");
		getglobal("PlayerHPBarCur"):SetTexUV("img_blood_strip_red");

		getglobal("PlayerHungerBarIcon"):SetTexUV("icon_chicken_leg");
		getglobal("PlayerHungerBarCur"):SetTexUV("img_blood_strip_yellow");
	end
	getglobal("PlayerHPBarCur"):ChangeTexUVWidth(hpWidth);
	getglobal("PlayerHungerBarCur"):ChangeTexUVWidth(foodWidth);
end

-------------------------------------------------------------------------
local HP = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.HP = HP;

function HP:onLoad()
	self:AddGameEvent()

	this:setUpdateTime(0.05);

	self:setBarUI("PlayerHPBarBkg");
	self:setCurValueUI("PlayerHPBarCur");
	self:setLossValueUI("PlayerHPBarLoss");
	self:setOverflowValueUI("PlayerHPBarOverflow");
	self:setCurOverflowValueUI("PlayerHPBarCurOverflow");
	self:setCursorUI("PlayerHPBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	-- self:debug(true);

	self.ui = getglobal("PlayerHPBar")
	self.fsLifeNum = getglobal("PlayerHPBarLifeNum")
	self.fsRatio = getglobal("PlayerHPBarRatio")
end

function HP:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)
end

function HP:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function HP:change()
    if not MainPlayerAttrib then return end
	
	local ceil = math.ceil
	local floor = math.floor
	local cur = MainPlayerAttrib:getHP();
	local max = MainPlayerAttrib:getMaxHP();
	local overflow = MainPlayerAttrib:getOverflowHP();

	self:set(floor(cur));
	self:setMax(ceil(max));
	self:setOverflow(ceil(overflow));

	-- print("HP:change(): " + self.m_iOld + " -> " + self.m_iCur + " | " + cur + " | " + self.m_bIsAnimating);

	local szFormat = "%d/%d"
	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	if cur < 1 then
		cur = ceil(cur);
	else
		cur = floor(cur);
	end
	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
		local curArmor = CurMainPlayer:getArmor();
		local armor = 0;
		local armorUi = getglobal("PlayerArmorBarRatio");
		if curArmor < 1 then
			armor = ceil(curArmor);
		else
			armor = floor(curArmor);
		end
		armorUi:Hide();
		-- if armor > 0 and armorUi then
		-- 	armorUi:Show();
		-- 	armorUi:SetText(string.format("%d(+%d)", cur, armor));
		-- 	self.fsRatio:SetText("");
		-- else
		-- 	armorUi:Hide();
		-- 	self.fsRatio:SetText(cur);
		-- end
		if armor > 0 then
			self.fsRatio:SetText(string.format("%d(+%d)", cur, armor));
		else
			self.fsRatio:SetText(floor(cur));
		end
	else
		szRatio = szFormat:format(cur, ceil(max));
		self.fsRatio:SetText(szRatio);
	end


	local lifeNum = CurMainPlayer:getLeftLifeNum();
	if lifeNum >= 0 then
		self.fsLifeNum:SetText(CurMainPlayer:getLeftLifeNum());
	else
		self.fsLifeNum:SetText("");
	end

	self:notifyChange();
end

-------------------------------------------------------------------------

local Strength = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.Strength = Strength;

function Strength:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05);

	self:setBarUI("PlayerStrengthBarBkg");
	self:setCurValueUI("PlayerStrengthBarCur");
	self:setLossValueUI("PlayerStrengthBarLoss");
	self:setOverflowValueUI("PlayerStrengthBarOverflow");
	self:setCurOverflowValueUI("PlayerStrengthBarCurOverflow");
	self:setCursorUI("PlayerStrengthBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	-- self:debug(true);

	self.fsRatio = getglobal("PlayerStrengthBarRatio")
	self.texExhaustionWarning = getglobal("PlayerStrengthBarExhaustionWarning")
	self.texCur = getglobal("PlayerStrengthBarCur")

	self.CurColorAnimator = AnimatorFactory:newColorAnimator(self)
		:setRepeatCount(99999)
		:setReverseMode(true)
		:setDuration(1)
		:setOldRGB(255, 255, 255)
		:setNewRGB(255, 100, 100)
		-- :debug(true)
		:setUI(self.texCur)

	self.ExhaustionWarningAnimator = AnimatorFactory:newAlphaAnimator(self)
		:setRepeatCount(4)
		:setReverseMode(true)
		:setDuration(0.5)
		:setAlphaFrom(0)
		:setAlphaTo(1)
		-- :debug(true)
		:setUI(self.texExhaustionWarning)

end

function Strength:onUpdate()
	self.super.onTick(self);
	self.CurColorAnimator:onUpdate(arg1)
	self.ExhaustionWarningAnimator:onUpdate(arg1);
end

function Strength:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.FlashExhaustionWarning,function(context)
		NewEventBridgeOldEvent(GameEventType.FlashExhaustionWarning,context)
		arg1 = GameEventType.FlashExhaustionWarning
		self:onEvent()
	end)
	
end

function Strength:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	elseif arg1 == "GE_FLASH_EXHAUSTION_WARNING" then
		self:flashExhaustionWarning();
	end
end

function Strength:onAnimationEnd(Animator)
	if Animator == self.ExhaustionWarningAnimator then
		self.texExhaustionWarning:Hide();
	end
end

function Strength:onAnimationCancel(Animator)
	if Animator == self.CurColorAnimator then
		self.texCur:SetColor(255, 255, 255);
	end
end

function Strength:change()
	if not MainPlayerAttrib then return end
	-- print("Strength:change(): m_iOld = " + self.m_iOld + " | m_iCur = " + self.m_iCur);

	local ceil = math.ceil
	local floor = math.floor
	local cur = MainPlayerAttrib:getStrength();
	local max = MainPlayerAttrib:getMaxStrength();
	local overflow = MainPlayerAttrib:getOverflowStrength();

	self:set(floor(cur));
	self:setMax(ceil(max));
	self:setOverflow(ceil(overflow));

	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
		local curPerseverance = CurMainPlayer:getPerseverance();
		local Perseverance = 0;
		local PerseveranceUi = getglobal("PlayerPerseveranceBarRatio");
		if curPerseverance < 1 then
			Perseverance = ceil(curPerseverance);
		else
			Perseverance = floor(curPerseverance);
		end
		PerseveranceUi:Hide();
		-- if Perseverance > 0 and PerseveranceUi then
		-- 	PerseveranceUi:Show();
		-- 	PerseveranceUi:SetText(string.format("%d(+%d)", floor(cur), Perseverance));
		-- 	self.fsRatio:SetText("");
		-- else
		-- 	PerseveranceUi:Hide();
		-- 	self.fsRatio:SetText(floor(cur));
		-- end
		if Perseverance > 0 then
			self.fsRatio:SetText(string.format("%d(+%d)", floor(cur), Perseverance));
		else
			self.fsRatio:SetText(floor(cur));
		end
	else
		szRatio = string.format("%d/%d", floor(cur), ceil(max + overflow));
		self.fsRatio:SetText(szRatio);
	end

	if MainPlayerAttrib:isExhausted() then
		self.texCur:SetTexUV("img_blood_strip_yellow_h.png");
		if not self.CurColorAnimator:isAnimating() then
			self.CurColorAnimator:start();
		end
	else
		self.texCur:SetTexUV("img_blood_strip_yellow.png");
		self.CurColorAnimator:cancel();
	end

	self:notifyChange();
end

function Strength:flashExhaustionWarning()
	if not MainPlayerAttrib:isExhausted() then
		self.texExhaustionWarning:Hide();
		if self.ExhaustionWarningAnimator:isAnimating() then
			self.ExhaustionWarningAnimator:cancel();
		end
		return
	end

	if self.ExhaustionWarningAnimator:isAnimating() then
		return
	end
	
	-- ShowGameTips(GetS(1571));
	self.texExhaustionWarning:Show();
	self.ExhaustionWarningAnimator:start();
end

--------------------------------------------------------------------
local ArmorAnim = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.ArmorAnim = ArmorAnim;

function ArmorAnim:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05);

	self:setBarUI("PlayerArmorBarBkg");
	self:setCurValueUI("PlayerArmorBarCur");
	self:setLossValueUI("PlayerArmorBarLoss");
	self:setOverflowValueUI("PlayerArmorBarOverflow");
	self:setCurOverflowValueUI("PlayerArmorBarCurOverflow");
	self:setCursorUI("PlayerArmorBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	--self:debug(true);

	self.ui = getglobal("PlayerArmorBar")
	self.fsRatio = getglobal("PlayerArmorBarRatio")
	

	getglobal("PlayerArmorBarBkg"):Hide();
	getglobal("PlayerArmorBarIcon"):Hide();
	getglobal("PlayerArmorBarRatio"):Hide();
end
function ArmorAnim:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)

end
function ArmorAnim:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function ArmorAnim:change()
	if not CurMainPlayer then return end
	local ceil = math.ceil
	local floor = math.floor
	local cur = CurMainPlayer:getArmor();
	local max = 100;
	local overflow = 0;

	self:set(floor(cur));
	self:setMax(ceil(max));
	self:setOverflow(ceil(overflow));

	-- print("RideHP:change(): " + self.m_iOld + " -> " + self.m_iCur + " | " + cur + " | " + self.m_bIsAnimating);

	local szFormat = "%d/%d"
	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	-- if cur < 1 then
	-- 	szRatio = szFormat:format(ceil(cur), ceil(max));
	-- else
	-- 	szRatio = szFormat:format(floor(cur), ceil(max));
	-- end
	-- if cur < 1 then
	-- 	szRatio = ceil(cur);
	-- else
	-- 	szRatio = floor(cur);
	-- end
	-- self.fsRatio:SetText(szRatio);

	self:notifyChange();
end

-------------------------------------------------------------------------
local PerseveranceAnim = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.PerseveranceAnim = PerseveranceAnim;

function PerseveranceAnim:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05);

	self:setBarUI("PlayerPerseveranceBarBkg");
	self:setCurValueUI("PlayerPerseveranceBarCur");
	self:setLossValueUI("PlayerPerseveranceBarLoss");
	self:setOverflowValueUI("PlayerPerseveranceBarOverflow");
	self:setCurOverflowValueUI("PlayerPerseveranceBarCurOverflow");
	self:setCursorUI("PlayerPerseveranceBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	--self:debug(true);

	self.ui = getglobal("PlayerPerseveranceBar")
	self.fsRatio = getglobal("PlayerPerseveranceBarRatio")

	getglobal("PlayerPerseveranceBarBkg"):Hide();
	getglobal("PlayerPerseveranceBarIcon"):Hide();
	getglobal("PlayerPerseveranceBarRatio"):Hide();
end
function PerseveranceAnim:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)

end
function PerseveranceAnim:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function PerseveranceAnim:change()
	if not CurMainPlayer then return end
	local ceil = math.ceil
	local floor = math.floor
	local cur = CurMainPlayer:getPerseverance();
	local max = 100;
	local overflow = 0;

	self:set(floor(cur));
	self:setMax(ceil(max));
	self:setOverflow(ceil(overflow));

	-- print("RideHP:change(): " + self.m_iOld + " -> " + self.m_iCur + " | " + cur + " | " + self.m_bIsAnimating);

	local szFormat = "%d/%d"
	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	-- if cur < 1 then
	-- 	szRatio = szFormat:format(ceil(cur), ceil(max));
	-- else
	-- 	szRatio = szFormat:format(floor(cur), ceil(max));
	-- end
	-- if cur < 1 then
	-- 	szRatio = ceil(cur);
	-- else
	-- 	szRatio = floor(cur);
	-- end
	-- self.fsRatio:SetText(szRatio);

	self:notifyChange();
end
-------------------------------------------------------------------------
local RideHP = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.RideHP = RideHP;

function RideHP:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05);

	self:setBarUI("RideHPBarBkg");
	self:setCurValueUI("RideHPBarCur");
	self:setLossValueUI("RideHPBarLoss");
	self:setOverflowValueUI("RideHPBarOverflow");
	self:setCurOverflowValueUI("RideHPBarCurOverflow");
	self:setCursorUI("RideHPBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	self:debug(true);

	self.ui = getglobal("RideHPBar")
	self.fsRatio = getglobal("RideHPBarRatio")

end

function RideHP:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)
end

function RideHP:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function RideHP:change()
	if not CurMainPlayer then return end
	local ride = CurMainPlayer:getRidingHorse();
	if ride == nil then 
		return 
	end

	local ceil = math.ceil
	local floor = math.floor
	local cur = ride:getMobAttrib():getHP();
	local max = ride:getMobAttrib():getMaxHP();
	local overflow = 0;

	self:set(floor(cur));
	self:setMax(ceil(max));
	self:setOverflow(ceil(overflow));

	-- print("RideHP:change(): " + self.m_iOld + " -> " + self.m_iCur + " | " + cur + " | " + self.m_bIsAnimating);

	local szFormat = "%d/%d"
	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	if cur < 1 then
		szRatio = szFormat:format(ceil(cur), ceil(max));
	else
		szRatio = szFormat:format(floor(cur), ceil(max));
	end
	self.fsRatio:SetText(szRatio);

	self:notifyChange();
end


-- 虚空能量条
local RideVacantBar = ComplexAnimatorFactory:newOverflowBarAnimator()
PlayMain.RideVacantBar = RideVacantBar

function RideVacantBar:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05)

	self:setBarUI("RideVacantBarBkg")
	self:setCurValueUI("RideVacantBarCur")
	self:setLossValueUI("RideVacantBarLoss")
	self:setOverflowValueUI("RideVacantBarOverflow")
	self:setCurOverflowValueUI("RideVacantBarCurOverflow")
	self:setCursorUI("RideVacantBarCursor")

	self:setMin(0)
	self:setValueChangeCount(10)

	-- self:debug(true);

	self.fsRatio = getglobal("RideVacantBarRatio")
end

function RideVacantBar:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)
end

function RideVacantBar:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function RideVacantBar:change()
	if not CurMainPlayer then return end
	local ride = CurMainPlayer:getRidingHorse();
	if ride == nil then 
		return 
	end

	local ceil = math.ceil
	local floor = math.floor
	-- local cur = ride:getMobAttrib():getHP();
	-- local max = ride:getMobAttrib():getMaxHP();
	local cur = ActorComponentCallModule(ride,"VacantComponent","getCurVacantEnergy") or 0
	local max = ActorComponentCallModule(ride,"VacantComponent","getMaxVacantEnergy") or 0
	local overflow = 0;
	
	self:set(floor(cur))
	self:setMax(ceil(max))
	self:setOverflow(ceil(overflow))

	-- ShowGameTips("cur="..cur)
	-- print("RideHP:change(): " + self.m_iOld + " -> " + self.m_iCur + " | " + cur + " | " + self.m_bIsAnimating);

	local szFormat = "%d/%d"
	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	if cur < 1 then
		szRatio = szFormat:format(ceil(cur), ceil(max));
	else
		szRatio = szFormat:format(floor(cur), ceil(max));
	end
	self.fsRatio:SetText(szRatio);

	self:notifyChange();
end
-------------------------------------------------------------------------
function RideChargeFrame_OnLoad()
	RideChargeFrame_AddGameEvent()
	for i=1, 5 do
		local line = getglobal("RideChargeFrameLine"..i);
		if GetClientInfo():isPC() then
			line:SetSize(4, 14);
			line:SetPoint("lefe", "RideChargeFrameBkg", "left", 37+(i-1)*27, -9);
		else
			line:SetSize(4, 19);
			line:SetPoint("lefe", "RideChargeFrameBkg", "left", 48+(i-1)*43, -10);
		end

		local line = getglobal("BallChargeFrameLine"..i);
		if GetClientInfo():isPC() then
			line:SetSize(4, 14);
			line:SetPoint("lefe", "BallChargeFrameBkg", "left", 37+(i-1)*27, -8);
		else
			line:SetSize(4, 19);
			line:SetPoint("lefe", "BallChargeFrameBkg", "left", 48+(i-1)*43, -10);
		end
	end

end

function RideChargeFrame_AddGameEvent()

	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		RideChargeFrame_OnEvent()
	end)

end

local function RideChargeFrame_ResetCommonChargeUI(chargeUI, chargeUIIcon)
	if chargeUI then
		chargeUI:SetTextureHuiresXml("ui/mobile/texture2/old_operateframe.xml");
		chargeUI:SetTexUV("04.png");
	end

	if chargeUIIcon then
		chargeUIIcon:SetTextureHuiresXml("ui/mobile/texture2/outgame2/uitex3.xml")
		chargeUIIcon:SetTexUV("sdjm_icon_shuxing02.png")
		chargeUIIcon:SetSize(40, 40)
	end
end

function RideChargeFrame_OnEvent()
	if not CurMainPlayer then return end
	local width = 163;
	local height = 14;
	if GetClientInfo():isMobile() then
		width = 257;
		height = 17;
	end


	if arg1 == "GE_PLAYERATTR_CHANGE" then
		local ride = CurMainPlayer:getRidingHorse();
		if ride ~= nil then
			local curCharge = ride:getCurCharge();
			local maxCharge = ride:getMaxCharge();
			local ratio = curCharge/maxCharge;
			if curCharge > 0 and not getglobal("CharacterActionFrame"):IsShown() then
				getglobal("RideChargeFrame"):Show();
			else
				getglobal("RideChargeFrame"):Hide();
			end

			local chargeUI = getglobal("RideChargeFrameCharge");
			local chargeUIBkg = getglobal("RideChargeFrameBkg")
			local chargeUIIcon = getglobal("RideChargeFrameChargeIcon")
			local  isShowLine = true;
			--local triggerSkillChargeRatio = 0.67;
			--if ratio >= triggerSkillChargeRatio then

			-- 竹蜻蜓
			local bambo = false
			if ride:getHorseDef().ID == 4509 or ride:getHorseDef().ID == 4510 then
				if ride:getBamboDragonFlyState() == 1 or ratio == 1 then
					bambo = true
				end
				if ride:getBamboDragonFlyState() == 0 and ratio == 1 then
					getglobal("RideChargeFrame"):Hide()
				elseif not getglobal("CharacterActionFrame"):IsShown() then
					getglobal("RideChargeFrame"):Show()
				end
			end

			local pVacantComponent = ride:GetComponentByName("VacantComponent")
			if ride:isTriggerSkillCharge() or bambo then
				chargeUI:SetTextureHuiresXml("ui/mobile/texture2/outgame.xml");
				chargeUI:SetTexUV("sjb_jindu.png");
				isShowLine = false or bambo;
			elseif pVacantComponent and ActorComponentCallModule(ride,"VacantComponent","IsVacantType") then
				chargeUI:SetTextureHuiresXml("ui/mobile/texture0/operate.xml")
				chargeUI:SetTexUV("img_energy01.png")

				if ride:getHorseDef().ID == 3245 then -- 虚空团子
					chargeUIIcon:SetTextureHuiresXml("ui/mobile/texture0/operate.xml")
        			chargeUIIcon:SetTexUV("icon_xk_tuanzi.png")
					chargeUIIcon:SetSize(38, 41)
				elseif ride:getHorseDef().ID == 3262 then -- 虚空沃沃兽
					chargeUIIcon:SetTextureHuiresXml("ui/mobile/texture0/operate.xml")
        			chargeUIIcon:SetTexUV("icon_xk_wowoshou.png")
					chargeUIIcon:SetSize(62, 37)
				else
					RideChargeFrame_ResetCommonChargeUI(chargeUI, chargeUIIcon)
				end
			else
				RideChargeFrame_ResetCommonChargeUI(chargeUI, chargeUIIcon)
			end

			for i=1, 5 do
				local line = getglobal("RideChargeFrameLine"..i);
				if isShowLine then
					line:Show();
				else
					line:Hide();
				end
			end

			getglobal("RideChargeFrameCharge"):ChangeTexUVWidth(ratio*257);
			getglobal("RideChargeFrameCharge"):SetSize(ratio*width, height);
			getglobal("RideChargeFrameChargeShield"):ChangeTexUVWidth(ratio*257);
			if ride:getShieldCoolingTicks() <= 0 then
				local shieldSize = ratio*width > 60 and 60 or ratio*width;
				getglobal("RideChargeFrameChargeShield"):SetSize(shieldSize, height);
			else
				getglobal("RideChargeFrameChargeShield"):SetSize(0, height);
			end

		else
			if getglobal("RideChargeFrame"):IsShown() then
				getglobal("RideChargeFrame"):Hide();
			end
		end
	end
end
-------------------------------------------------------------------------
function OnBallChargeChange(charge)
	local width = 168;
	local height = 14;
	if GetClientInfo():isMobile() then
		width = 257;
		height = 17;
	end

	local ratio = charge/100;
	charge = charge == 0 and 1 or charge;
	if charge > 0 and not getglobal("CharacterActionFrame"):IsShown() then
		getglobal("BallChargeFrame"):Show();
		getglobal("BallChargeFrameAimRegion"):Hide();
		getglobal("BallChargeFrameAimRegionBg"):Hide();
		getglobal("BallChargeFrameCharge"):ChangeTexUVWidth(ratio*257);
		getglobal("BallChargeFrameCharge"):SetSize(ratio*width, height);
	else
		getglobal("BallChargeFrame"):Hide();
	end


end

--LLDO:是否在毒气区
function IsInsideStarDuqi()
	if MainPlayerAttrib == nil then return end
	local bRet = true;
	local num = MainPlayerAttrib:getBuffNum();
	Log("IsInsideStarDuqi, num = " .. num);

	for i = 1, num do
		local info = MainPlayerAttrib:getBuffInfo(i-1);
		Log("IsInsideStarDuqi, id = " .. info.buffid);

		if info and info.buffid and (info.buffid == 65 or info.buffid == 66) then
			--氧气区buff
			Log("IsInsideStarDuqi: 1111");
			bRet = false;
			break;
		end
	end

	if true == bRet then
		if CurMainPlayer:isInsideNoOxygenBlock() then
			--在毒气区
			Log("IsInsideStarDuqi: 2222");
			bRet = true;
		else
			bRet = false;
		end
	end

	return bRet;
end

-------------------------------------------------------------------------
function PlayerOxygenPackage_OnLoad()
	local Anim = getglobal("PlayerOxygenPackageBarAnim");
	local AnimRed = getglobal("PlayerOxygenPackageBarAnimRed");
	AnimRed:SetUVAnimation(33, true)
	Anim:SetUVAnimation(83, true)
end

local function isShowOxygenPackage()
	return MainPlayerAttrib:isEquipOxygenpack()
end

local function UpdateOxygenPackageValue()
	if not isShowOxygenPackage() then return false end

	local PlayerOxygenPackageBar = getglobal("PlayerOxygenPackageBar");
	local redProcess = getglobal("PlayerOxygenPackageBarRed");
	local blueProcess = getglobal("PlayerOxygenPackageBarBlue");
	local ratio = getglobal("PlayerOxygenPackageBarRatio");
	local AnimRed = getglobal("PlayerOxygenPackageBarAnimRed");
	local curvalue = MainPlayerAttrib:getEquipItemDuration(EQUIP_SLOT_TYPE.EQUIP_PIFENG)
	local maxValue = MainPlayerAttrib:getEquipItemMaxDuration(EQUIP_SLOT_TYPE.EQUIP_PIFENG)
	local percent = curvalue/maxValue

	local value = math.floor(percent*100)
	if value <= 30 then
		redProcess:Show()
		AnimRed:Show()
		blueProcess:Hide()
	else
		AnimRed:Hide()
		blueProcess:Show()
		redProcess:Hide()
	end

	local width = 268
	local height = 16

	blueProcess:SetSize(width*percent, height)
	redProcess:SetSize(width*percent, height)
	ratio:SetText(string.format("%d/%d", value, 100))

	return true
end

function PlayOxygenBar_OnLoad()
	PlayOxygenBar_AddGameEvent()
end


local isShowOxygen = false;
local forceInWater = false;
local showDuqiIcon = false;

local function updateOxygenValue()
	if not isShowOxygen then return end
	local PlayerOxygenBar = getglobal("PlayerOxygenBar");
	local PlayerDuqiBar = getglobal("PlayerDuqiBar");
	local PlayerOxygenPackageBar = getglobal("PlayerOxygenPackageBar");

	-- Log("MainPlayerAttrib:getOxygen = " .. MainPlayerAttrib:getOxygen());
	PlayerOxygenBar:SetCurValue(MainPlayerAttrib:getOxygen()/10, false);

	--毒气泡
	local value = 10 - MainPlayerAttrib:getOxygen();
	if showDuqiIcon then
		showDuqiIcon = false;
		value = 0;
	end
	PlayerDuqiBar:SetCurValue(value / 10, false);
end

local function checkShowOxygenBarShow()
	if UGCModeMgr and UGCModeMgr:IsEditing() then
		return 
	end	
	local PlayerOxygenBar = getglobal("PlayerOxygenBar");
	local PlayerDuqiBar = getglobal("PlayerDuqiBar");
	local PlayerOxygenPackageBar = getglobal("PlayerOxygenPackageBar");
	local duqizhao = getglobal("StarDuqiZhao");

	local isInLiqiud = false
	if 	CurMainPlayer and
		(CurMainPlayer:isInWater() or CurMainPlayer:getCurMapID() == BlockUtil.G.MAPID_MENGYANSTAR)
	then
		isInLiqiud = true
	end

	if (isInLiqiud or isShowOxygen) and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.OXYGEN) then
		if isShowOxygenPackage() then
			PlayerOxygenPackageBar:Show()
			PlayerOxygenBar:Hide()
			PlayerDuqiBar:Hide()
			UpdateOxygenPackageValue();
		else
			PlayerOxygenPackageBar:Hide()
			PlayerOxygenBar:Show();
			PlayerOxygenBar:SetClientUserData(0, 1);

			--LLDO:毒气泡
			if IsInsideStarDuqi() then
				showDuqiIcon = true;
				PlayerDuqiBar:Show();
				PlayerDuqiBar:SetClientUserData(0, 1);
				duqizhao:Show();
			else
				if duqizhao:IsShown() then
					duqizhao:Hide();
				end
			end
			updateOxygenValue()
		end
	else
		PlayerOxygenPackageBar:Hide()
		PlayerOxygenBar:Hide();
		PlayerOxygenBar:SetClientUserData(0, 0);
		--毒气泡
		PlayerDuqiBar:Hide();
		PlayerDuqiBar:SetClientUserData(0, 0);
		duqizhao:Hide();
	end
end


function PlayOxygenBar_AddGameEvent()
	local PlayerOxygenBar = getglobal("PlayerOxygenBar");
	local PlayerDuqiBar = getglobal("PlayerDuqiBar");
	local duqizhao = getglobal("StarDuqiZhao");
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		updateOxygenValue()
		PlayOxygenBar_OnEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.ShowOxygen,function(context)
		NewEventBridgeOldEvent(GameEventType.ShowOxygen,context)
		arg1 = GameEventType.ShowOxygen
		local paramData = context:GetParamData()
		local isShow = paramData.isShow
		if isShow and not CurWorld:isGodMode()then
			isShowOxygen = true
		else
			isShowOxygen = false
		end
		checkShowOxygenBarShow()
		UpdateGuideSwim(isShow)

		PlayOxygenBar_OnEvent()
	end)
end

function PlayerOxygenPackage_OnUpdate()
	checkShowOxygenBarShow()
end

function PlayOxygenBar_OnUpdate()
	checkShowOxygenBarShow()
end

function PlayOxygenBar_OnEvent()
	local PlayerOxygenBar = getglobal("PlayerOxygenBar");
	local PlayerDuqiBar = getglobal("PlayerDuqiBar");
	local duqizhao = getglobal("StarDuqiZhao");

	if arg1 == "GE_PLAYERATTR_CHANGE" then
		updateOxygenValue()
	elseif arg1 == "GE_SHOW_OXYGEN" then
		local ge = GameEventQue:getCurEvent()
		if ge.body.oxygen.show and not CurWorld:isGodMode()then
			isShowOxygen = true
		else
			isShowOxygen = false;
		end
		checkShowOxygenBarShow()
		UpdateGuideSwim(ge.body.oxygen.show);
	end
end

function UpdateGuideSwim(inWater)
	local num = GetIWorldConfig():getGameData("guideswim");
	if num < 3 then
		if GetClientInfo():isPC() then
			getglobal("PlayerOxygenBarTips"):SetText(GetS(3560));
		else
			getglobal("PlayerOxygenBarTips"):SetText(GetS(3559));
		end
		getglobal("PlayerOxygenBarTipsBkg"):Show();
		getglobal("PlayerOxygenBarTips"):Show();
	else
		getglobal("PlayerOxygenBarTipsBkg"):Hide();
		getglobal("PlayerOxygenBarTips"):Hide();
	end

	if not inWater then
		if forceInWater and num < 3 then
			num = num + 1;
			GetIWorldConfig():setGameData("guideswim", num);
		end
	else
		forceInWater = true;
	end
end

-------------------------------------------------------------------------



--------------------------------------跳跃蓄力begin---------------------------------------------
function JumpChargeFrame_OnLoad()
	for i=1, 5 do
		local line = getglobal("JumpChargeFrameLine"..i);
		if GetClientInfo():isPC() then
			line:SetSize(4, 14);
			line:SetPoint("left", "JumpChargeFrameBkg", "left", 37+(i-1)*27, 0);
		else
			line:SetSize(4, 19);
			line:SetPoint("left", "JumpChargeFrameBkg", "left", 48+(i-1)*43, 0);
		end
		line:Show();
	end
	local onEvent = nil
	SubscribeGameEvent(nil,GameEventType.JumpChargeUpdate,function(context)
		NewEventBridgeOldEvent(GameEventType.JumpChargeUpdate,context)
		arg1 = GameEventType.JumpChargeUpdate
		local param = context:GetParamData()
		
		if onEvent then
			onEvent(param)
		end
	end)

	onEvent = function(param)
		if not CurMainPlayer or not param then 
			return 
		end
		local curCharge = param.curCharge;
		local maxCharge = param.maxCharge;

		--MiniLog("curCharge:", curCharge)
		--MiniLog("maxCharge:", maxCharge)
		
		local width = 163;
		local height = 14;
		if GetClientInfo():isMobile() then
			width = 257;
			height = 17;
		end

		if arg1 == "GE_JUMP_CHARGE_UPDATE" then
			local strFrameName = "JumpChargeFrame"
			local frame = getglobal(strFrameName);
			
			local ratio = curCharge/maxCharge;
			if curCharge > 0 and not getglobal("CharacterActionFrame"):IsShown() then
				frame:Show();
			else
				frame:Hide();
				return;
			end

			-- for i=1, 5 do
			-- 	local line = getglobal(string.format("%sLine%d", strFrameName, i));
			-- 	line:Show();
			-- end
			local chargeUIIcon = getglobal(strFrameName .. "ChargeIcon")
			chargeUIIcon:SetTextureHuiresXml("ui/mobile/texture0/operate.xml")
			chargeUIIcon:SetTexUV("super_jump.png")
			chargeUIIcon:SetSize(47, 49)

			local chargeUI = getglobal(strFrameName .. "Charge");
			chargeUI:SetTextureHuiresXml("ui/mobile/texture0/operate.xml")
			chargeUI:SetTexUV("img_energy01.png")
			chargeUI:ChangeTexUVWidth(ratio*257);
			chargeUI:SetSize(ratio*width, height);

			local shieldUI = getglobal(strFrameName .. "ChargeShield");
			local shieldSize = ratio*width > 60 and 60 or ratio*width;
			shieldUI:ChangeTexUVWidth(ratio*257);
			shieldUI:SetSize(shieldSize, height);
			shieldUI:Hide()
		end
	end
end

--------------------------------------跳跃蓄力end---------------------------------------------
local Hunger = ComplexAnimatorFactory:newOverflowBarAnimator();
PlayMain.Hunger = Hunger;

function Hunger:onLoad()
	self:AddGameEvent()
	this:setUpdateTime(0.05);

	self:setBarUI("PlayerHungerBarBkg");
	self:setCurValueUI("PlayerHungerBarCur");
	self:setLossValueUI("PlayerHungerBarLoss");
	self:setOverflowValueUI("PlayerHungerBarOverflow");
	self:setCurOverflowValueUI("PlayerHungerBarCurOverflow");
	self:setCursorUI("PlayerHungerBarCursor");

	self:setMin(0);
	self:setValueChangeCount(10);

	-- self:debug(true);

	self.fsRatio = getglobal("PlayerHungerBarRatio")
end

function Hunger:AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		self:onEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.PlayerAttrReset,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrReset,context)
		arg1 = GameEventType.PlayerAttrReset
		self:onEvent()
	end)
end

function Hunger:onEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		self:change();
	elseif arg1 == "GE_PLAYERATTR_RESET" then
		self:reset();
	end
end

function Hunger:change()
	-- print("Hunger:change(): m_iOld = " + self.m_iOld + " | m_iCur = " + self.m_iCur);
	if not MainPlayerAttrib then return end

	local ceil = math.ceil
	local floor = math.floor
	local cur = MainPlayerAttrib:getFoodLevel();
	local max = MainPlayerAttrib:getFoodMaxLevel();

	self:setMax(ceil(max));
	self:set(floor(cur));

	local szRatio
	if cur <= 0.001 then
		cur = 0;
	end
	szRatio = string.format("%d/%d", floor(cur), ceil(max));
	self.fsRatio:SetText(szRatio);

	self:notifyChange();
end
-------------------------------------------------------------------------

function PlayerExpBarStar_OnClick()
	if IsStandAloneMode() then return end
	if gIsSingleGame then return end
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common"}, "StarConvertFrameAutoGen")
	GetInst("MiniUIManager"):OpenUI("StarConvertFrame", "miniui/miniworld/StarConvertFrame", "StarConvertFrameAutoGen", {});
	-- local StarConvertFrame = getglobal("StarConvertFrame");
	-- if not StarConvertFrame:IsShown() then
		-- StarConvertFrame:Show();
	-- end
end

function PlayerExpBar_OnLoad()
	PlayerExpBar_AddGameEvent()
end

function PlayerExpBar_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.PlayerAttrChange,function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerAttrChange,context)
		arg1 = GameEventType.PlayerAttrChange
		PlayerExpBar_OnEvent()
	end)
end

function PlayerExpBar_OnEvent()
	if arg1 == "GE_PLAYERATTR_CHANGE" then
		if not MainPlayerAttrib then return end
		local exp = MainPlayerAttrib:getExp();

		if exp ~= CurPlayerExpVal then
			SetExpBar();
			CurPlayerExpVal = exp;
		end
	end
end

function PlayerExpBar_OnShow()
	getglobal("PlayerExpBar"):Hide()
    if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
        getglobal("PlayerExpBar"):Hide();
    end
	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("PlayerExpBar"), true)
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("PlayerLevelBar"), false)
	end
end

function SetExpBar()
	getglobal("PlayerExpBar"):Hide()
	--[[
	local exp = MainPlayerAttrib:getExp();
	local starNum = math.floor(exp/EXP_STAR_RATIO);

	getglobal("PlayerExpBarStarText"):SetText(starNum);
	if starNum >= 1000 and not getglobal("StarConvertFrame"):IsShown() then
		getglobal("PlayerExpBarStarText"):SetText("999+");
	end

	local expBarVal = (exp - starNum * EXP_STAR_RATIO) / EXP_STAR_RATIO ;
	getglobal("PlayerExpBarExp"):SetWidth(500*expBarVal);

	local uv = getglobal("PlayerExpBarUVAnimationTex");
	uv:SetPoint("right", "PlayerExpBarExp", "right", 20, 0);
	uv:SetUVAnimation(80, false);
	uv:Show();

	local curStarNum = math.floor(CurPlayerExpVal/EXP_STAR_RATIO);
	if starNum > curStarNum then
		--星星特效
		local starUV = getglobal("PlayerExpBarStarUV");
		starUV:SetUVAnimation(100, false);
		starUV:Show();
		GetMusicManager():PlaySound2D("sounds/ui/info/experience_star.ogg", 1);
	end

	--角色界面星星进度
	SetRoleFrameStarNum(expBarVal, starNum);
	--]]
end

function SetRoleFrameStarNum(expBarVal, starNum)
	if GetInst("RoleAttrManager"):IsOpenNewRoleFrame() then
		if GetInst("MiniUIManager"):IsShown("RoleFrame") then
			GetInst("MiniUIManager"):GetCtrl("RoleFrame"):SetRoleFrameStarNum(expBarVal, starNum)
		end
	else
		local RoleFrameStarNum = getglobal("RoleFrameStarNum");
		local RoleFrameStarPro = getglobal("RoleFrameStarNumPro");
		RoleFrameStarPro:SetWidth(120 * expBarVal);
		RoleFrameStarNum:SetText(starNum);
	end
end

-----------------------------------等级经验条-----------------------------------
local m_PlayerLevelExpSwitch = true;	--开关
function PlayerLevelExp_OnLoad()
	if m_PlayerLevelExpSwitch then

		PlayerLevelExp_AddGameEvent()
	end
end

function PlayerLevelExp_OnShow()
	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("PlayerExpBar"), false)
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("PlayerLevelBar"), true)
	end
end

function PlayerLevelExp_AddGameEvent()
	SubscribeGameEvent(nil, GameEventType.PlayerGainLevelExp, function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerGainLevelExp,context)
		arg1 = GameEventType.PlayerGainLevelExp
		PlayerGainLevelExp_OnEvent()
	end)
end

--事件响应: 玩家获得等级经验
function PlayerGainLevelExp_OnEvent()
	if m_PlayerLevelExpSwitch then
		if arg1 == "GE_PLAYER_GAIN_LEVEL_EXP" then
			-- 无经验模式，不能获取经验
			local gameRule = WorldMgr:getGameMakerManager()
			if gameRule and gameRule:isNoExpMode() then
				return
			end

			local ge 	= GameEventQue:getCurEvent();
			local state = ge.body.playerLevelExp.state;
			local uin = ge.body.playerLevelExp.uin

			SetLevelBar(true);

			if state == 1 then
				--1.升级
				PlayerGainLevelExp_UpgradeEffect(uin);
			elseif state == 2 then
				--2.获得经验
			end
		end
	end
end

--升级特效
function PlayerGainLevelExp_UpgradeEffect(uin)
	local curLevel = MainPlayerAttrib:getCurLevel();
	local mainUin = CurMainPlayer and CurMainPlayer:getUin() or uin
	if mainUin == uin then
		ShowGameTips(GetS(34248,tostring(curLevel)));
	end
	
	if uin and uin > 0 and ClientCurGame and ClientCurGame.getPlayerByUin then
		player = ClientCurGame:getPlayerByUin(uin);
		if player then
			ActorComponentCallModule(player,"EffectComponent","playBodyEffectByName","Lvup")
			ActorComponentCallModule(player,"EffectComponent","setBodyEffectScale","Lvup", 2.0)
			ActorComponentCallModule(player,"SoundComponent","playSound","npc.lvup", 1.0, 1.0) 
		end
	end
end

function SetLevelBar(bPlayEffect)
	if m_PlayerLevelExpSwitch then
		local pro = getglobal("PlayerLevelBarPro");
		local effect = getglobal("PlayerLevelBarEffect");
		local rate = getglobal("PlayerLevelBarRate");
		local level = getglobal("PlayerLevelBarLevel");

		local curExp = MainPlayerAttrib:getCurLevelExp();
		local curLevel = MainPlayerAttrib:getCurLevel();
		local maxLevel = MainPlayerAttrib:getMaxLevel();
		local upLevelExp = 0;
		local width = 0;
		local maxWidth = 500;

		if curLevel >= maxLevel then
			--已经是最高级
			upLevelExp = MainPlayerAttrib:getLevelExp(maxLevel);
			curExp = upLevelExp;
			width = maxWidth;
		else
			upLevelExp = MainPlayerAttrib:getLevelExp(curLevel);
			width = maxWidth * curExp / upLevelExp;
		end

		width = width > maxWidth and maxWidth or width;
		width = width > 0 and width or 0;

		level:SetText("Lv" .. curLevel);
		rate:SetText(curExp .. "/" .. upLevelExp);
		pro:SetWidth(width);

		if bPlayEffect then
			effect:SetPoint("right", "PlayerLevelBarPro", "right", 20, 0);
			effect:SetUVAnimation(80, false);
			effect:Show();
		end

		getglobal("RoleFrame") -- 加载触发优化 code_by:huangfubin 2023.11.14
		--角色界面等级经验条
		local RoleFrameLevelBar = getglobal("RoleFrameLevelBar");
		local RoleFrameLevelPro = getglobal("RoleFrameLevelBarPro");
		local RoleFrameLevelLvl = getglobal("RoleFrameLevelBarLevel");
		local RoleFrameLevelRate = getglobal("RoleFrameLevelBarRate");
		
		local isOpenLevelMode = false;
		if WorldMgr then
			local BaseSettingMgr = WorldMgr:getBaseSettingManager();
			if BaseSettingMgr and BaseSettingMgr:isOpenLevelModel() then
				isOpenLevelMode = true;
			end
		end

		if isOpenLevelMode and CurWorld and CurWorld:isGameMakerRunMode() then
			RoleFrameLevelBar:Show();
			RoleFrameLevelBarWidth = 322 * curExp / upLevelExp;
			RoleFrameLevelBarWidth = RoleFrameLevelBarWidth > 322 and 322 or RoleFrameLevelBarWidth;
			RoleFrameLevelPro:SetWidth(RoleFrameLevelBarWidth);
			RoleFrameLevelLvl:SetText("Lv" .. curLevel);			
			RoleFrameLevelRate:SetText(curExp .. "/" .. upLevelExp);
		else
			RoleFrameLevelBar:Hide();
		end
	end
end

function PlayerHPBar_ShowOrHide(bShow)
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		bShow = false
	end
	if bShow then
		--getglobal("PlayerHPBar"):Show()
		HpBarFrame_ShowHpBar(true)
	else
		--getglobal("PlayerHPBar"):Hide()
		HpBarFrame_ShowHpBar(false)
	end
end
function PlayerHUBar_ShowOrHide(bShow)
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		bShow = false
	end
	if bShow then
		--getglobal("PlayerHPBar"):Show()
		HpBarFrame_ShowHpBar(true)
	else
		--getglobal("PlayerHPBar"):Hide()
		HpBarFrame_ShowHpBar(false)
	end
end
function RideHPBar_ShowOrHide(bShow)
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		bShow = false
	elseif IsUGCEditing() then
		bShow = false
	end
	local RideHPBarVisible = UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.RIDEHPBAR);
	if bShow then
		--坐骑血量也要可以通过UI编辑器控制显示隐藏
		if RideHPBarVisible then
			getglobal("RideHPBar"):Show()
		else
			getglobal("RideHPBar"):Hide()
		end
		if CurMainPlayer then
			local ride = CurMainPlayer:getRidingHorse();
			if ride and ride:GetComponentByName("VacantComponent") and ActorComponentCallModule(ride,"VacantComponent","IsVacantType") then
				getglobal("RideVacantBar"):Show() -- 显示虚空能量条
			end
		end
	else
		getglobal("RideHPBar"):Hide()
		getglobal("RideVacantBar"):Hide() -- 隐藏虚空能量条
	end

	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("RideHPBar"), bShow)
	end
end

function PlayerHungerBar_ShowOrHide(bShow)
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		bShow = false
	end
	if bShow then
		--getglobal("PlayerHungerBar"):Show()
		HpBarFrame_ShowHuBar(true)
	else
		--getglobal("PlayerHungerBar"):Hide()
		HpBarFrame_ShowHuBar(false)
	end
end

function InteractiveBtn_ShowOrHide(bShow)
	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
		bShow = false
	end
	if bShow then
		getglobal("InteractiveBtn"):Show()
	else
		getglobal("InteractiveBtn"):Hide()
	end
end

--显示或隐藏
function LevelExpBar_ShowOrHide(bShow)
	local levelBar = getglobal("PlayerLevelBar");
	local expBar = getglobal("PlayerExpBar");

	if bShow and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.EXP) then
		local isOpenLevelMode = false;
		if WorldMgr then
			local BaseSettingMgr = WorldMgr:getBaseSettingManager();
			if BaseSettingMgr and BaseSettingMgr:isOpenLevelModel() then
				isOpenLevelMode = true;
			end
		end
		
		if isOpenLevelMode then
			levelBar:Show();
			expBar:Hide();
		else
			levelBar:Hide();
			expBar:Show();
		end
	else
		levelBar:Hide();
		expBar:Hide();
	end
end


--潜行
function PlayMainFrameSneakBtn_OnClick()
	if getglobal("PlayMainFrameSneak"):IsChecked() then
		if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
			CurMainPlayer:changeMoveFlag(1, false);
		else
			CurMainPlayer:setSneaking(false);
		end
		ShowGameTips(GetS(104), 3);
		BPReportSneaking(false)
	else
		if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
			CurMainPlayer:changeMoveFlag(1, true);
		else
			CurMainPlayer:setSneaking(true);
		end
		ShowGameTips(GetS(105), 3);
		BPReportSneaking(true)
	end
end

function PlayMainFrameSneakBtn_OnMouseDown()
	if CurMainPlayer.triggerInputEvent then
		CurMainPlayer:triggerInputEvent(16,"Down")		
	end
end

function PlayMainFrameSneakBtn_OnMouseUp()
	if CurMainPlayer.triggerInputEvent then
		CurMainPlayer:triggerInputEvent(16,"Up")
	end
end

function PlayMainFrameSneakBtn_OnMouseDownUpdate()
	if CurMainPlayer.triggerInputEvent then
		CurMainPlayer:triggerInputEvent(16,"OnPress")
	end
end

--坐骑
function PlayMainFrameRideBtn_OnClick()
    if CurMainPlayer:isShapeShift() then
        getglobal("AccRideAttackBtn"):Hide()
        getglobal("AccRideAttackLeftBtn"):Hide()
    end
	CurMainPlayer:dismountActor();
end

--获取联机房内睡觉总人数
function TotalSleepingNums()
	local num = 1
	if ClientCurGame and ClientCurGame.getNumPlayerBriefInfo then
		num = ClientCurGame:getNumPlayerBriefInfo() + 1
	end
	return num
end

--获取当前睡觉人数
function CurrentSleepingNums()
	local num = 0
	if CurWorld and GetWorldActorMgr(CurWorld) then
		num = GetWorldActorMgr(CurWorld):sleepingMembers()
	end
	return num
end

--主动触发睡觉按钮
function PlayMainFrameSleepBtn_OnClick()
    if CurMainPlayer and CurMainPlayer.trySleep then
		CurMainPlayer:trySleep()
	end
	local frame = getglobal("SleepNoticeFrame")
	if frame and frame:IsShown() then
		local title = getglobal("SleepNoticeFrameTitle")
		local sleepBtn = getglobal("SleepNoticeFrameLeftBtn")
		local leaveBtn = getglobal("SleepNoticeFrameRightBtn")
		local middleBtn = getglobal("SleepNoticeFrameMiddleBtn")
		local waitingBtn = getglobal("SleepNoticeFrameLeftBtnWaiting")
		if title and sleepBtn and leaveBtn and middleBtn and waitingBtn then
			if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
				--联机
				if sleepBtn:IsShown() then
					sleepBtn:Hide()
				end
				waitingBtn:Show()
			else
				--单机
				title:SetText("睡觉中...")
				sleepBtn:Hide()
				leaveBtn:Hide()
				middleBtn:Show()
			end
		end
	end
end

function PlayMainFrameFlyBtn_OnClick()
	--[[
	local PlayMainFrameFlyDown = getglobal("PlayMainFrameFlyDown")
	if PlayMainFrameFlyDown:IsShown() then
		PlayMainFrameFlyDown:Hide();
		CurMainPlayer:setFlying(false);
	else
		PlayMainFrameFlyDown:Show();
		CurMainPlayer:setFlying(true);
	end
	]]
	if DisabledExecute() then
		return
	end
	if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
		CurMainPlayer:changeMoveFlag(0, not CurMainPlayer:getFlying());
	else
		CurMainPlayer:setFlying(not CurMainPlayer:getFlying());
	end
	-- 首次切换飞行模式提示
	if CurWorld and ShowFlyTips then
		ShowFlyTips()
	end
	--切换飞行模式打断互动动作
	local body = CurMainPlayer:getBody();
	--联机模式下且正在播放互动动作
	if AccountManager and AccountManager:getMultiPlayer() > 0 and body.clearAction 
		and body.isPlayingSkinAct and body:isPlayingSkinAct() then
		body:clearAction();
	end
	--[[
	local PlayMainFrameFlyUp = getglobal("PlayMainFrameFlyUp")
	if PlayMainFrameFlyUp:IsShown() then
		PlayMainFrameFlyUp:Hide()
	else
		PlayMainFrameFlyUp:Show()
	end
	]]
end

function PlayMainFrameFlyDown_OnMouseUp()
	CurMainPlayer:cancelMoveUp(-1)
end
function PlayMainFrameFlyDown_OnMouseDown()
	CurMainPlayer:setMoveUp(-1)
end
function PlayMainFrameFlyDown_OnClick()
end

local FrontFlyUpPosX = -1;
local FrontFlyUpPosY = -1;
function PlayMainFrameFlyUp_OnMouseUp()
	CurMainPlayer:cancelMoveUp(1);
	FrontFlyUpPosX = -1;
	FrontFlyUpPosY = -1;
end

function PlayMainFrameFlyUp_OnMouseDown()
	CurMainPlayer:setMoveUp(1)
end

function PlayMainFrameFlyUp_OnClick()
end

function CanOpenBackpack()
	if CurWorld and CurWorld:getOWID() ~= NewbieWorldId then return true end

	local lv = AccountManager:getCurGuideLevel();
	local step = AccountManager:getCurGuideStep();
	if lv == 1 then
		if step ~= 19 then
			return false;
		end
	end

	return true;
end

function PlayMainFrameBackpackBtn_OnClick_Proc()
	if GetInst("RoleAttrManager"):IsOpenNewRoleFrame() then
		GetInst("RoleAttrManager"):OpenRoleAttrFrame()
	else
		getglobal("RoleAttrFrame"):Hide();
		if ClientCurGame.showOperateUI then
			ClientCurGame:showOperateUI(false);
		end
	end
end

function PlayMainFrameBackpackBtn_OnClick()
	if not CanOpenBackpack() then return end

	local StorageBoxFrame = getglobal("StorageBoxFrame");
	if StorageBoxFrame:IsShown() then
		StorageBoxFrame:Hide()
	end
	
	if IsInHomeLandMap and IsInHomeLandMap() then
		-- 家园背包按钮click埋点上报
		Homeland_StandReport_MainUIView("HomelandBackpack", "click")
		--家园 就打开家园背包
		GetInst("UIManager"):Open("HomelandBackpack")
	else
		if CurWorld:isGodMode() then
			local CreateBackpackFrame = getglobal("CreateBackpackFrame")
			if CreateBackpackFrame:IsShown() then
				CreateBackpackFrame:Hide();
			else
				CreateBackpackFrame:Show();
				--ClientCurGame:setOperateUI(true);
				if ClientCurGame.showOperateUI then
					ClientCurGame:showOperateUI(false);
				end
			end
		else
			if UGCGetInst("GameTaskMgr"):GetIsInSingPlayerGuide() then
				if Service and Service.GameObject then
					Service.GameObject:PushCustomEvent("PlayMainFrameBackpackBtnOnClick")
				end
			end

			if getglobal("RoleAttrFrame"):IsShown() then
				getglobal("RoleAttrFrame"):Hide();
			elseif GetInst("MiniUIManager"):IsShown("RoleAttrFrame") then
				GetInst("MiniUIManager"):CloseUI("RoleAttrFrameAutoGen")
			else
				--开发者:限制玩家打开背包
				if not checkCanOpenBackpack() then
					return;
				end
	
				AdventureMode_OperCtrlPanel(1, "PlayMainFrameBackpackBtn_OnClick_Proc")
			end
		end
	end
	--打开背包2 LYX
	OpenBackpackSandboxEvent()
end

--开发者接口:检查玩家是否可以打开背包
function checkCanOpenBackpack()
	if CurWorld:isGameMakerRunMode() then
		if nil == CurMainPlayer.checkCanOpenBackpack then
			return true;
		end
		
		if not CurMainPlayer:checkCanOpenBackpack() then
			ShowGameTips('你被限制无法打开背包');
			return false;
		end
	end

	return true;
end

function PlayMainFrameGuideSkip_OnClick()
	if GetInst("MessageBoxFrameMgr"):IsShown() then
		--统计
		-- statisticsGameEvent(901, "%s", "HideNoviceGuideExitTips","save",true);
		GetInst("MessageBoxFrameMgr"):CloseUI();
	else
		--统计
		-- statisticsGameEvent(901, "%s", "ShowNoviceGuideExitTips","save",true);
		if CurWorld and CurWorld:getOWID() == NewbieWorldId2 and 
			getglobal("NewbieSkinTryPlay") and getglobal("NewbieSkinTryPlay"):IsShown()then
			return
		end
		MessageBox(16, GetS(3763));
		GetInst("MessageBoxFrameMgr"):SetClientString( "新手引导退出" );
	end
end

function GuideSkip()
	--统计
	-- local step = AccountManager:getCurGuideStep();
	-- if IsFirstEnterNoviceGuide then
		-- if step ~= nil then
		-- 	statisticsGameEvent(901, "%s", "ExitNoiceGuide", "%d", step,"save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));

		-- else
		-- 	statisticsGameEvent(901, "%s", "ExitNoiceGuide", "%d", 99,"save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));
		-- end

		-- if step == 7 then
		-- 	statisticsGameEvent(901, "%s", "ExitNoiceGuidePlot", "%d", NewGuideInfo.statisticsPlotDialogIdx,"save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));

		-- elseif step == 16 then
		-- 	statisticsGameEvent(901, "%s", "ExitNoiceGuidePlot", "%d", NewGuideInfo.statisticsPlotDialogIdx,"save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));

		-- elseif step == 24 then
		-- 	statisticsGameEvent(901, "%s", "ExitNoiceGuidePlot", "%d", NewGuideInfo.statisticsPlotDialogIdx,"save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));
		-- end
		-- --总的统计
		-- statisticsGameEvent(901, "%s", "ExitNoiceGuideSum","save",true,"%s",os.date("%Y%m%d%H%M%S",os.time()));
	-- end
	--埋点，中途退出 设备码,是否首次进入教学地地图,用户类型,语言
	-- statisticsGameEventNew(961,GetClientInfo():getDeviceID(),(IsFirstEnterNoviceGuide and not enterGuideAgain) and 1 or 2,
	-- true and (GetClientInfo():isFirstEnterGame() and 1 or 2),tostring(get_game_lang()))
	--埋点，返回存档界面 设备码,返回存档来源,是否首次进入教学地地图,用户类型,语言		
	-- statisticsGameEventNew(963,GetClientInfo():getDeviceID(),2,(IsFirstEnterNoviceGuide and not enterGuideAgain) and 1 or 2,
	-- true and (GetClientInfo():isFirstEnterGame() and 1 or 2),tostring(get_game_lang()))
	StatisticsTools:send(true, true)
	IsSkipFromGuideOrFirstMap = true
	IsFirstEnterNoviceGuide = false;

	if CurWorld and CurWorld:getOWID() == NewbieWorldId then
		GoToMainMenu()
	elseif CurWorld and CurWorld:getOWID() == NewbieWorldId2 then
		local ctrl = GetInst("UIManager"):GetCtrl("RookieGuide")
		if ctrl then
			ctrl:Refresh()
		end
		if GetInst("mainDataMgr"):GetSwitch() == false and  NewbieGuideManager:GetPlayerTypeID() == NewbieGuideManager.NEW_PLAYER_TYPEID  then
			if not NewbieGuideManager:RequestSelectSkinPlay() then
				GoToMainMenu()
			end
		else
			GoToMainMenu()
		end
	end

	GongNengFrame_SetVipBtnForceHide(false);
	getglobal("GongNengFrameQQBlueVipBtn"):Show();
	getglobal("GongNengFrameStoreGNBtn"):Show();
	getglobal("GongNengFrameActivityGNBtn"):Show();
	--getglobal("GongNengFrameSetGNBtn"):Show();
	if friendservice.enabled then
		getglobal("GongNengFrameFriendBtn"):Show();
	else
		if IsInHomeLandMap and IsInHomeLandMap() then
			InteractiveBtn_ShowOrHide(false);
		elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.INVITE) then--xyang自定义UI
			InteractiveBtn_ShowOrHide(false);
		else
			InteractiveBtn_ShowOrHide(true);
		end
	end
	AccountManager:setCurGuideStep(1);
	KillAllCutScene();

	SetGuideStep(nil)

	if IsStandAloneMode("") then
		MessageBox(4, GetS(25835))
	end

	DeepLinkQueue:dequeue();
end


function GuideSkip_Edu(skipType)--0为跳过新手引导，1为正常结束新手引导
	if GetInst("MessageBoxFrameMgr"):IsShown() then
		GetInst("MessageBoxFrameMgr"):CloseUI()
	end

	HideUI2GoMainMenu();
    ClearInstanceData();
    ClearMultiLangEdit();
    HideVehicleTips();

	AccountManager:setCurGuideStep(1);
	KillAllCutScene();

	SetGuideStep(nil)	

	CurMainPlayer:setMoveInputActive(false);

	--GetClientGameManagerPtr():gotoGame("none");

	if not GetClientInfo():isPC() then
        if MCodeMgr then
            local tParam = {0, "MiniWorldGuideFinished",skipType}
            MCodeMgr:miniCodeCallBack(-1001, JSON:encode(tParam));
        end
    end
	MiniCodeApi:quitWorld(true, nil, true, nil, {});
end

function PlayMainFrame_OnLoad()
	PlayMainFrame_AddGameEvent()
	this:setUpdateTime(0.05);
	getglobal("BattleCountDownFrame"):setUpdateTime(0.05);
	getglobal("ScreenEffectFrame"):setUpdateTime(0.05);
	getglobal("OverLookArrowFrame"):setUpdateTime(0.05);

	--PC和Mobile,MultiPlayerInfo的区别处理

	if GetClientInfo():isPC() then
		getglobal("MultiPlayerInfoInfo"):SetHeight(285);
		getglobal("MultiPlayerInfoInfoTips"):Show();
	else
		getglobal("MultiPlayerInfoInfo"):SetHeight(269);
		getglobal("MultiPlayerInfoInfoTips"):Hide();
	end

	getglobal("RocketUIFrame"):setUpdateTime(0.05);

end

-- 神秘方砖id和奥特曼背景音乐对应表
local altmanMusicCfg = {
    [471] = "sounds/music/bgm_urutoramanDiga.ogg",
    [472] = "sounds/music/bgm_urutoramanDaina.ogg",
    [473] = "sounds/music/bgm_urutoramanGaia.ogg",
    [474] = "sounds/music/bgm_urutoramanZero.ogg",
    [475] = "sounds/music/bgm_urutoramanGinga.ogg",
}

local function SurviveGameModHandleMiniMap(datas)
	if Service and Service.World and datas then
		local markDatas = {};
		local ctrl1 = GetInst("MiniUIManager"):GetCtrl("main_minimap")
		if(ctrl1)then
			markDatas = ctrl1:getCustomMarker(GetS(80246))
		end
		local function checkDataHave(singleData)
			for index, value in ipairs(markDatas) do
				if value.x == singleData.x and value.z == singleData.z then
					return true;
				end
			end
			return false;
		end	
		local addDatas = {};
		for index, value in ipairs(datas) do
			if not checkDataHave(value) then
				table.insert(addDatas, value);
			end
		end
		if #addDatas > 0 then
			--MiniLog("caozegang addV:", addV.x, addV.z);
			ShowGameTips(GetS(9403121), 3);
			--第一个是最近的, 要追踪
			Service.World:AddCustomMarker(nil, "ui://c_ingame/icon_city", GetS(80246), addDatas[1].x, addDatas[1].z, 4, true);
			for i=2, #addDatas do
				Service.World:AddCustomMarker(nil, "ui://c_ingame/icon_city", GetS(80246), addDatas[i].x, addDatas[i].z, 4);
			end
		elseif #datas > 0 then
			ShowGameTips(GetS(9403122), 3);
		else
			ShowGameTips(GetS(9403120), 3);
		end
	end
end

-- 记录下当前播放的背景音乐
lastMusicName = ""
-- 定时器停止奥特曼背景音乐
local altmanMusicTimer = nil
-- 是否正在播放奥特曼背景音乐
isPlayingAtlmanMusic = false

CUR_WORLD_MAPID = 0;		-- 0主世界 大于0为副本
function PlayMainFrame_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.FlyModeChange,function(context)
		NewEventBridgeOldEvent(GameEventType.FlyModeChange,context)
		arg1 = GameEventType.FlyModeChange
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.InfoTips,function(context)
		NewEventBridgeOldEvent(GameEventType.InfoTips,context)
		arg1 = GameEventType.InfoTips
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.EnterWorld,function(context)
		NewEventBridgeOldEvent(GameEventType.EnterWorld,context)
		arg1 = GameEventType.EnterWorld
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.UpdateBossState,function(context)
		NewEventBridgeOldEvent(GameEventType.UpdateBossState,context)
		arg1 = GameEventType.UpdateBossState
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil, GameEventType.UpdateCalledBossState, function(context)
		NewEventBridgeOldEvent(GameEventType.UpdateCalledBossState,context)
		arg1 = GameEventType.UpdateCalledBossState
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil,GameEventType.LeaveWorld,function(context)
		NewEventBridgeOldEvent(GameEventType.LeaveWorld,context)
		arg1 = GameEventType.LeaveWorld
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.OpenContainer,function(context)
		NewEventBridgeOldEvent(GameEventType.OpenContainer,context)
		arg1 = GameEventType.OpenContainer
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil,GameEventType.CloseContainer,function(context)
		NewEventBridgeOldEvent(GameEventType.CloseContainer,context)
		arg1 = GameEventType.CloseContainer
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.NeedContainerPassword,function(context)
		NewEventBridgeOldEvent(GameEventType.NeedContainerPassword,context)
		arg1 = GameEventType.NeedContainerPassword
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.ToggleGameMode,function(context)
		NewEventBridgeOldEvent(GameEventType.ToggleGameMode,context)
		arg1 = GameEventType.ToggleGameMode
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.RidingChange,function(context)
		NewEventBridgeOldEvent(GameEventType.RidingChange,context)
		arg1 = GameEventType.RidingChange
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.Statistic,function(context)
		NewEventBridgeOldEvent(GameEventType.Statistic,context)
		arg1 = GameEventType.Statistic
		PlayMainFrame_OnEvent()
	end )
	SubscribeGameEvent(nil, GameEventType.RidingEnterWaterOrLand, function(context)
		NewEventBridgeOldEvent(GameEventType.RidingEnterWaterOrLand,context)
		arg1 = GameEventType.RidingEnterWaterOrLand
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.PlayerShapeShift, function(context)
		NewEventBridgeOldEvent(GameEventType.PlayerShapeShift,context)
		arg1 = GameEventType.PlayerShapeShift
		PlayMainFrame_OnEvent()
	end)

	SubscribeGameEvent(nil, GameEventType.PreOpenEditFcmUI, function(context)
		NewEventBridgeOldEvent(GameEventType.PreOpenEditFcmUI,context)
		arg1 = GameEventType.PreOpenEditFcmUI
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.OpenEditFullyCustomModel, function(context)
		NewEventBridgeOldEvent(GameEventType.OpenEditFullyCustomModel,context)
		arg1 = GameEventType.OpenEditFullyCustomModel
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.CloseEditFullyCustomModel, function(context)
		NewEventBridgeOldEvent(GameEventType.CloseEditFullyCustomModel,context)
		arg1 = GameEventType.CloseEditFullyCustomModel
		PlayMainFrame_OnEvent()
	end)

	SubscribeGameEvent(nil, GameEventType.NpcShopOpen, function(context)
		NewEventBridgeOldEvent(GameEventType.NpcShopOpen,context)
		arg1 = GameEventType.NpcShopOpen
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.OpenEditActorModel, function(context)
		NewEventBridgeOldEvent(GameEventType.OpenEditActorModel,context)
		arg1 = GameEventType.OpenEditActorModel
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil,GameEventType.MainPlayerIde,function(context)
		NewEventBridgeOldEvent(GameEventType.MainPlayerIde,context)
		arg1 = GameEventType.MainPlayerIde
		PlayMainFrame_OnEvent()
	end )

	SubscribeGameEvent(nil, GameEventType.PlayAltmanMusic, function(context)
		NewEventBridgeOldEvent(GameEventType.PlayAltmanMusic,context)
		arg1 = GameEventType.PlayAltmanMusic
		PlayMainFrame_OnEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.BuffChange,function(context)
		NewEventBridgeOldEvent(GameEventType.BuffChange,context)
		arg1 = GameEventType.BuffChange
		PlayMainFrame_OnEvent()
	end )

	SubscribeGameEvent(nil, GameEventType.QQMusicPlayer, function(context)
		NewEventBridgeOldEvent(GameEventType.QQMusicPlayer,context)
		arg1 = GameEventType.QQMusicPlayer
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.MiniClubPlayer, function(context)
		NewEventBridgeOldEvent(GameEventType.MiniClubPlayer,context)
		arg1 = GameEventType.MiniClubPlayer
		PlayMainFrame_OnEvent()
	end)
	SubscribeGameEvent(nil, GameEventType.CustomGameStage, function(context)
		NewEventBridgeOldEvent(GameEventType.CustomGameStage,context)
		arg1 = GameEventType.CustomGameStage
		PlayMainFrame_OnEvent()
	end)
	
	SubscribeGameEvent(nil, GameEventType.ManualEmitterUpdate, function(context)
		NewEventBridgeOldEvent(GameEventType.ManualEmitterUpdate,context)
		arg1 = GameEventType.ManualEmitterUpdate
		PlayMainFrame_OnEvent()
	end)

	SubscribeGameEvent(nil, GameEventType.EasyModeGuideCheck, function(context)
		NewEventBridgeOldEvent(GameEventType.EasyModeGuideCheck,context)
		arg1 = GameEventType.EasyModeGuideCheck
		PlayMainFrame_OnEvent()
	end)

	SubscribeGameEvent(nil,GameEventType.SurviveGameLoaded,function(context)
		NewEventBridgeOldEvent(GameEventType.SurviveGameLoaded,context)
		arg1 = GameEventType.SurviveGameLoaded
		PlayMainFrame_OnEvent()
	end )

	SubscribeGameEvent(nil,GameEventType.HostConnectProxy,function(context)
		NewEventBridgeOldEvent(GameEventType.HostConnectProxy,context)
		arg1 = GameEventType.HostConnectProxy
		PlayMainFrame_OnEvent()
	end )

	SubscribeGameEvent(nil,GameEventType.CityDataLoad,function(context)
		-- NewEventBridgeOldEvent(GameEventType.CityDataLoad,context)
		-- arg1 = GameEventType.CityDataLoad
		-- PlayMainFrame_OnEvent()
		local paramData = context:GetParamData()
		local jsonStr = paramData.data_str;
		local ret, data = pcall(JSON.decode, JSON, jsonStr);
		if (ret and data) then
			--处理数据
			SurviveGameModHandleMiniMap(data.data);
		end
	end )

	if isAbroadEvn() then	
		--增加一个展示小黑板内容的事件监听 code_by:huangfubin 2023.1.17
		SandboxLua.eventDispatcher:CreateEvent(nil, "VIEW_SIGN_CONTENT")
		SandboxLua.eventDispatcher:SubscribeEvent(nil, "VIEW_SIGN_CONTENT", function(context)
			local paramData = context:GetParamData()
			GetInst("UIManager"):Open("CommonHelp", {textTitle=GetS(21686), textContent=paramData.text})
		end)
	end

	-- 	BattleDeathFrameManager DeathFrameManager 订阅死亡事件
	if GetInst("BattleDeathFrameManager") then 
		GetInst("BattleDeathFrameManager"):InitUI();
	end
	
	if GetInst("DeathFrameManager") then 
		GetInst("DeathFrameManager"):InitUI({})
	end
end


local netHandle = {
	-- traceHandle;
	-- playerCloseUIHandle;
	-- actorShowExchangeItemHandle;
	-- desertBussinessManDealHandle;
	-- actorShowHeadIconByPathHandle;
	-- playerHandle
}
local playerExploreTaskMgr = {
	--timer,
	--lastPos,
	--lasttick
}


-- tips显示计时key
local tipsOnShowSchedulerKeys = {}

function UnRegisterTipsSchedulerEvent(text)
	if text and tipsOnShowSchedulerKeys[text] then 
        GetInst("MiniUIScheduler"):unreg(tipsOnShowSchedulerKeys[text])
        tipsOnShowSchedulerKeys[text] = nil
    end
end

function UnRegisterAllTipsSchedulerEvents()
	for key, value in pairs(tipsOnShowSchedulerKeys) do
		if value then
			GetInst("MiniUIScheduler"):unreg(value)
        	tipsOnShowSchedulerKeys[key] = nil
		end
	end
end

function RegisterTipsSchedulerEvent(text)
	if text then
		UnRegisterTipsSchedulerEvent(text)
		tipsOnShowSchedulerKeys[text] = GetInst("MiniUIScheduler"):regGloabel(function ()
			UnRegisterTipsSchedulerEvent(text)
		end, tipsDisplayTime, 0, 0, false)
	end
end

function PlayMainFrame_OnEvent()
	if arg1 == "GIE_FLYMODE_CHANGE" then
		if CurWorld and CurWorld:isGodMode() or CurMainPlayer:isInSpectatorMode() then
			if not CurMainPlayer:getFlying() then
				CurMainPlayer:setMoveUp(0);

				local PlayMainFrameFlyDown = getglobal("PlayMainFrameFlyDown");
				local PlayMainFrameFlyUp = getglobal("PlayMainFrameFlyUp");

				if PlayMainFrameFlyDown:IsShown() then
					PlayMainFrameFlyDown:ClearPushState();
					UIFrameMgr:frameHide(PlayMainFrameFlyDown);
				end
				if PlayMainFrameFlyUp:IsShown() then
					PlayMainFrameFlyDown:ClearPushState();
					UIFrameMgr:frameHide(PlayMainFrameFlyUp);
				end

				local PlayMainFrameFly = getglobal("PlayMainFrameFly")
				if PlayMainFrameFly:IsChecked() then
					PlayMainFrameFly:SetChecked(false);
				end
			end
		end

		if CurWorld and CurWorld:getOWID() ~= NewbieWorldId and CurWorld:getOWID() ~= NewbieWorldId2 and GetClientInfo():isMobile() then
			if CurMainPlayer:isFlying() then
				if getglobal("AccRideCallBtn"):IsShown() then
					getglobal("AccRideCallBtn"):Hide();
				end
				--变形按钮
				if getglobal("AccRideChangeBtn"):IsShown() then
					SetAccRideChangeBtnVisible(false);
				end
				--召唤按钮
				if getglobal("AccSummonBtn"):IsShown() then
					SetAccSummonBtnVisible(false);
				end
			else
				if not getglobal("AccRideCallBtn"):IsShown() and CurMainPlayer:getMountType()~=MOUNT_DRIVE and  not MapEditManager:GetIsStartEdit() then
					if not isEducationalVersion and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
						if isAbroadEvn() then
							if IsNeedShowAccRideCallBtn() then
								getglobal("AccRideCallBtn"):Hide();
							end
						else
							getglobal("AccRideCallBtn"):Hide();
						end
					end
				end
				--变形按钮
				-- local skinId = CurMainPlayer:getSkinID()
				local skinId = GetMyInMapCurSkinId()
				local skinDef = RoleSkinCsv:get(skinId)
				local isMapEditShown = GetInst("MiniUIManager"):IsShown("MapEditAutoGen")
				if not getglobal("AccRideChangeBtn"):IsShown() and skinDef and skinDef["ChangeType"] > 0 and not isMapEditShown and
				UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
					SetAccRideChangeBtnVisible(true);
				end
				if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then
					SetAccSummonBtnVisible(true)
				else
					SetAccSummonBtnVisible(false)
				end
			end
		end

		--变形按钮
		if getglobal("AccRideChangeBtn"):IsShown() then
			if getglobal("AccRideCallBtn"):IsShown() then
				getglobal("AccRideChangeBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
			else
				getglobal("AccRideChangeBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
			end
		end

		if getglobal("AccRideChangeBtn"):IsShown() then--如果变形按钮显示，设置召唤按钮到变形按钮左边
			getglobal("AccSummonBtn"):SetPoint("right", "AccRideChangeBtn", "left", -3, 0)
		else
			if getglobal("AccRideCallBtn"):IsShown() then
				getglobal("AccSummonBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
			else
				getglobal("AccSummonBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
			end
		end

		-- 首次切换飞行模式提示
		if CurWorld and ShowFlyTips then
			ShowFlyTips()
		end
	elseif arg1 == "GIE_INFO_TIPS" then
		local ge 	= GameEventQue:getCurEvent();
		local text 	= ge.body.infotips.info;
		if not tipsOnShowSchedulerKeys[text] then
			ShowGameTips(text, 3);
			RegisterTipsSchedulerEvent(text)
		end
	elseif arg1 == "GIE_ENTER_WORLD" then
		local ge = GameEventQue:getCurEvent();
		local mapid = ge.body.enterworld.mapid;
		CUR_WORLD_MAPID = mapid;
		EnterWorld(mapid);
		Streaming.enterWorld();
		SetPlayerPurchaseFlag(false);
		ArchiveLoadGameEnvent();
		GetInst("ResourceDataManager"):SetIsFromLobby(ResourceCenterOpenFrom.FromMap)
		SaveCurWorldArchivePropSetting(true);
		gCreateRoomWorldID = nil
		threadpool:work(function ()
			CurMainPlayer:addAchievement(1, ACHIEVEMENT_ENTER_WORLD, mapid)
			local guideControl = GetInst("ForceGuideStepControl")
			if (not guideControl) or (not guideControl:GetNeedForceGuide()) then
				CurMainPlayer:updateTaskSysProcess(TASKSYS_START_GAME)
			end
			CurMainPlayer:updateTaskSysProcess(TASKSYS_ENTER_WORLD, mapid)
		end)
		SandboxLua.eventDispatcher:Emit(nil, "ENTER_WORLD",  MNSandbox.SandboxContext():SetData_Number("code", 0))
		SurviveGameEnterSubscibeMsgHandle();
	elseif arg1 == "GIE_UPDATE_BOSS_STATE" then
		local ge = GameEventQue:getCurEvent();
		local hp = ge.body.bossstate.hp;
		local monsterId = ge.body.bossstate.id;
		UpdateBossHpFrame(hp, monsterId);
		PlayMusicByBoss(hp, monsterId);
	elseif arg1 == "GIE_UPDATE_CALLED_BOSS_STATE" then
		local ge = GameEventQue:getCurEvent();
		local hp = ge.body.bossscalledtate.hp;
		local monsterId = ge.body.bossscalledtate.id;
		local timestep = ge.body.bossscalledtate.timestep;
		UpdateBossHpFrame2(hp, timestep, monsterId);
	elseif arg1 == "GIE_LEAVE_WORLD" then
		Streaming.leaveWorld();
		local ge = GameEventQue:getCurEvent();
		local mapid = ge.body.enterworld.mapid;
		-- if getglobal("InstanceTaskFrame"):IsShown() then
		-- 	getglobal("InstanceTaskFrame"):Hide();
		-- end
		if GetInst("MiniUIManager"):IsShown("BossLifeInfoAutoGen") then
			GetInst("MiniUIManager"):HideUI("BossLifeInfoAutoGen")
		end
		if getglobal("IntroduceFrame"):IsShown() then
			getglobal("IntroduceFrame"):Hide();
		end
		GetInst("MiniUIManager"):CloseUI("TaskTrackCtrl")
		SandboxLua.eventDispatcher:Emit(nil, "LEAVE_WORLD",  MNSandbox.SandboxContext():SetData_Number("code", 0))
		SurviveGameLeaveUnSubscibeMsgHandle();

		lastMusicName = ""

		if GetInst("UgcMsgHandler") then
			GetInst("UgcMsgHandler"):ClearUpGameUI()
		end
	elseif arg1 == "GE_OPEN_CONTAINER" then	--打开相应的容器
		local ge = GameEventQue:getCurEvent();
		local baseindex = ge.body.opencontainer.baseindex;
		local blockId = ge.body.opencontainer.blockid;
		local isRideContainer = ge.body.opencontainer.isRideContainer;
		local blockpos = {x = ge.body.opencontainer.posx or 0, y = ge.body.opencontainer.posy or 0, z = ge.body.opencontainer.posz or 0};	--箱子坐标位置

		MiniLog("GE_OPEN_CONTAINER", baseindex, blockId, blockpos, isRideContainer)
		-- 维修台
		if blockId == 2412 then
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "itemrepairAutoGen")
			GetInst("MiniUIManager"):OpenUI("itemrepair", "miniui/miniworld/itemrepair", "itemrepairAutoGen",{ge = ge})
			return
		end
		--分解机
		if blockId == 2429 or blockId == 2430 then
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "decompositionAutoGen")
			GetInst("MiniUIManager"):OpenUI("decomposition", "miniui/miniworld/soclogin", "decompositionAutoGen",{disableOperateUI = false,ge = ge})
			return
		end

		--研究台
		if blockId == 2413 then
			local researchpage = GetInst("MiniUIManager"):GetCtrl("researchpage")
			if researchpage then
				researchpage:OpenContainer(ge)
			end

			GetInst("MiniUIManager"):ShowUI("researchpageAutoGen")
			return
		end

		if blockId == 2411 then --领地柜
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "territoryAutoGen")
			GetInst("MiniUIManager"):OpenUI("territory", "miniui/miniworld/soclogin", "territoryAutoGen",{disableOperateUI = false,ge = ge})
			return
		end

		if blockId == 2414 then
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "waterstorageAutoGen")
			GetInst("MiniUIManager"):OpenUI("waterstorage", "miniui/miniworld/soclogin", "waterstorageAutoGen",{disableOperateUI = false,blockpos = blockpos, id = blockId,ge = ge})
			return
		end

		-- 工作台
		if blockId == 2401 or blockId == 2402 or blockId == 2403 then
			local blockLevel = {
				[2401] = 1,
				[2402] = 2,
				[2403] = 3
			}

			local techtreepage = GetInst("MiniUIManager"):GetCtrl("techtreepage")
			if not techtreepage then
				GetInst("MiniUIManager"):OpenUI("techtreepage", "miniui/module/soclogin", "techtreepageAutoGen",{disableOperateUI = false})
				techtreepage = GetInst("MiniUIManager"):GetCtrl("techtreepage")
				techtreepage:SetTreeLevel(blockLevel[blockId])
				return
			end

			techtreepage:OpenContainer(ge)
			GetInst("MiniUIManager"):ShowUI("techtreepageAutoGen")
			techtreepage:SetTreeLevel(blockLevel[blockId])
			return
		end

		if baseindex == STOVE_START_INFEX then
			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/c_login"
				}, 
				"StoveFrameAutoGen"
			)
			--"miniui/module/Game/StoveFrame/StoveFrameAutoGen.lua",
			GetInst("MiniUIManager"):OpenUI("StoveFrame", "miniui/miniworld/Stove", "StoveFrameAutoGen", blockpos);
		elseif baseindex == FURNACE_START_INDEX then
			--ClientCurGame:setOperateUI(true);
			--GetInst("UIManager"):Open("Furnace",{itemId = blockId})
			-- GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/c_innerCommon"},"MiniUIFurnaceMain")
			-- GetInst("MiniUIManager"):OpenUI("main_furnace","miniui/miniworld/compose","MiniUIFurnaceMain", {itemId = blockId})		

			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "furnacemainAutoGen")
			GetInst("MiniUIManager"):OpenUI("furnacemain", "miniui/miniworld/furnacemain", "furnacemainAutoGen",{fullScreen = {Type="IgnoreEdge"}, blockid = blockId,ge = ge})
		elseif baseindex==SENSOR_START_INDEX then --感应方块、红外感应方块fgui --codeby renjie
			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/common_comp",
					"miniui/miniworld/adventure",
					"miniui/miniworld/c_login"
				},
				"inductionFrameAutoGen"
			)
			GetInst("MiniUIManager"):OpenUI("inductionFrame","miniui/miniworld/adventure","inductionFrameAutoGen",{ge})	
		elseif baseindex==STORAGE_START_INDEX then
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "furnacemainAutoGen")
			GetInst("MiniUIManager"):OpenUI("storagebox", "miniui/miniworld/storagebox", "storageboxAutoGen",{fullScreen = {Type="IgnoreEdge"}, blockid = blockId})
		elseif baseindex == EMITTER_START_INDEX 
			or baseindex == FUNNEL_START_INDEX 
			or baseindex == DETECTIONPIPE_START_INDEX then --储物箱、柜、发射器fgui

			SetBoxContainerInfo(baseindex, blockId, blockpos);--修复开宝箱没触发成就上报
			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/common_comp",
					"miniui/miniworld/adventure",
					"miniui/miniworld/ArchiveInfoDetail"
				},
				"storageBoxFrameAutoGen"
			)
			GetInst("MiniUIManager"):OpenUI("storageBoxFrame","miniui/miniworld/adventure","storageBoxFrameAutoGen",{ge})	
		elseif baseindex == FUNNEL_START_INDEX  or baseindex == EMITTER_START_INDEX or baseindex == SENSOR_START_INDEX or baseindex == COLLIDER_START_INDEX then
			SetBoxContainerInfo(baseindex, blockId, blockpos,true,isRideContainer);
			-- GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/c_ingame","miniui/miniworld/common_comp","miniui/miniworld/adventure","miniui/miniworld/ArchiveInfoDetail"})
			-- GetInst("MiniUIManager"):OpenUI("storageBoxFrame","miniui/miniworld/adventure","storageBoxFrameAutoGen",{ge})
		elseif baseindex == NPCTRADE_START_INDEX then
			--UIFrameMgr:frameShow(getglobal("NpcTradeFrame"));
			-- GetInst("MiniUIManager"):AddPackage(
			-- 	{
			-- 		"miniui/miniworld/common",
			-- 		"miniui/miniworld/c_ingame",
			-- 		"miniui/miniworld/c_login"
			-- 	}, 
			-- 	"NpcTradeFrameAutoGen"
			-- )
			-- GetInst("MiniUIManager"):OpenUI("NpcTradeFrame", "miniui/miniworld/adventure", "NpcTradeFrameAutoGen", {});

			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/socTrade"}, "ui_npc_tradeAutoGen")
			GetInst("MiniUIManager"):OpenUI("ui_npc_trade", "miniui/miniworld/socTrade", "ui_npc_tradeAutoGen", {ge = ge})

			--ClientCurGame:setOperateUI(true);
		elseif baseindex == HORSE_EQUIP_INDEX then
			UIFrameMgr:frameShow(getglobal("RideFrame"));
			--ClientCurGame:setOperateUI(true);
		elseif baseindex == FURNACE_OXY_START_INDEX then
			--LLDO:打开氧气炉
			NewFurnaceOxyFrame_OnShow(ge)--新的氧气提炼装置界面
			
		elseif baseindex == BLUEPRINT_START_INDEX then
			--打开"蓝图工作台"
			Log("OpenBlueprint:");
			-- UIFrameMgr:frameShow(getglobal("BlueprintFrame"));
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/ugc_common"},"main_blueprintAutoGen")
			GetInst("MiniUIManager"):OpenUI("main_blueprint","miniui/miniworld/ugc_blueprint","main_blueprintAutoGen")
		elseif baseindex == INTERPRETER_START_INDEX then
			--打开信号解析器
			--UIFrameMgr:frameShow(getglobal("InstructionParserFrame"));
			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/c_login"
				}, 
				"InstructionParserFramePageAutoGen"
			)
			GetInst("MiniUIManager"):OpenUI("InstructionParserFramePage", "miniui/miniworld/adventure", "InstructionParserFramePageAutoGen", {});
		elseif baseindex == BUILDBLUEPRINT_START_INDEX then
			--打开图纸建造方块
			UIFrameMgr:frameShow(getglobal("BluePrintDrawingFrame"));
		elseif baseindex == CUSTOMMODEL_START_INDEX then
			-- CurOpenCustomModelBlockId = blockId;
			-- UIFrameMgr:frameShow(getglobal("CustomModelFrame"));
			GetInst("MiniUIManager"):OpenUI("CustomModelMainFrame", "miniui/miniworld/ugc_custommodel", "CustomModelAutoGen", {blockID = blockId, disableOperateUI = false});
		elseif baseindex == BRUSHMONSTER_START_INDEX then
			--UIFrameMgr:frameShow(getglobal("CreateMonsterFrame"));
			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/c_login"
				}, 
				"CreateMonsterFramePageAutoGen"
			)
			GetInst("MiniUIManager"):OpenUI("CreateMonsterFramePage", "miniui/miniworld/adventure", "CreateMonsterFramePageAutoGen", {pos = blockpos});
		elseif baseindex == SIGNS_START_INDEX then
			OpenGameSignEditFrame(blockId,blockpos)
		elseif baseindex == ACTIONER_START_INDEX then
			-- local param = {};
 			-- param.disableOperateUI = false;
			--param.isVehicle = false
			--if OpenContainer then
			--	param.nodes = OpenContainer:getActionerNodeStr()
			--else
			--	param.nodes = ""
			--end
			-- GetInst("UIManager"):Open("VehicleActioner",param)

			OpenVehicleActioner()

		--elseif baseindex == VEHICLE_START_INDEX then
		--	UIFrameMgr:frameShow(getglobal("VehicleEditFrame"));
		elseif baseindex == SENSOR_VALUE_START_INDEX then
			--UIFrameMgr:frameShow(getglobal("ContainerSensorValueFrame"));
			local param = {};
			param.blockid = blockId;
		   --param.isVehicle = false
		   --if OpenContainer then
		   --	param.nodes = OpenContainer:getActionerNodeStr()
		   --else
		   --	param.nodes = ""
		   --end

		   --GetInst("UIManager"):Open("ContainerSensorValue",param)--老ui
		   --光照传感器fgui codeby renjie

			GetInst("MiniUIManager"):AddPackage(
				{
					"miniui/miniworld/common",
					"miniui/miniworld/c_ingame",
					"miniui/miniworld/common_comp",
					"miniui/miniworld/adventure",
					"miniui/miniworld/c_login"
				},
				"lightSensorFrameAutoGen"
			)
			GetInst("MiniUIManager"):OpenUI("lightSensorFrame","miniui/miniworld/adventure","lightSensorFrameAutoGen",param)	
		elseif baseindex == TOMBSTONE_STRAT_INDEX then
			--OpenGameSignEditFrame(blockId)
			local param = {bid = blockId}
			OpenTombStoneUI(param)
		elseif baseindex == ALTAR_STRAT_INDEX then
			local param = {};
			param.blockid = blockId;
			local usenew  = true
			if(usenew)then
				GetInst("MiniUIManager"):AddPackage(
					{
						"miniui/miniworld/common",
						"miniui/miniworld/c_ingame",
						"miniui/miniworld/common_comp",
						"miniui/miniworld/c_login"
					},
					"AltarAwardsFrameAutoGen"
				)
				GetInst("MiniUIManager"):OpenUI("AltarAwardsFrame","miniui/miniworld/adventure","AltarAwardsFrameAutoGen",param)
			else
				GetInst("UIManager"):Open("AltarAwardsEdit",param)
			end
		elseif baseindex == MANUAL_EMITTER_START_INDEX then
			ShowUseEmitterUI();
		end
	elseif arg1 == "GE_NEED_CONTAINER_PASSSWORD" then
		local ge = GameEventQue:getCurEvent();
		local x = ge.body.needContainerpassword.pos_x
		local y = ge.body.needContainerpassword.pos_y
		local z = ge.body.needContainerpassword.pos_z
		local state = ge.body.needContainerpassword.state --0房主没有初始化密码 1请输入密码 2密码输入失败
		AirlinerOpenPasswordBox(state)


	elseif arg1 == "GE_CLOSE_CONTAINER" then
		local ge = GameEventQue:getCurEvent();
		if ge.body.opencontainer then
			local baseindex = ge.body.opencontainer.baseindex;
			if GetInst("MiniUIManager"):IsShown("itemrepairAutoGen") then
				GetInst("MiniUIManager"):CloseUI("itemrepairAutoGen")
				return
			end

			--分解机
			if GetInst("MiniUIManager"):IsShown("decompositionAutoGen") then
				GetInst("MiniUIManager"):CloseUI("decompositionAutoGen")
				return
			end

			--研究台
			if GetInst("MiniUIManager"):IsShown("researchpageAutoGen") then
				GetInst("MiniUIManager"):HideUI("researchpageAutoGen")
				return
			end

			if GetInst("MiniUIManager"):IsShown("territoryAutoGen") then
				GetInst("MiniUIManager"):CloseUI("territoryAutoGen")
				return
			end

			if GetInst("MiniUIManager"):IsShown("waterstorageAutoGen") then
				GetInst("MiniUIManager"):CloseUI("waterstorageAutoGen")
				return
			end

			if GetInst("MiniUIManager"):IsShown("techtreepageAutoGen") then
				GetInst("MiniUIManager"):HideUI("techtreepageAutoGen")
				return
			end

			--箱子
			if GetInst("MiniUIManager"):IsShown("storageboxAutoGen") then
				GetInst("MiniUIManager"):CloseUI("storageboxAutoGen")
				return
			end

			if baseindex == FURNACE_START_INDEX then
				-- UIFrameMgr:frameHide(getglobal("FurnaceFrame"));
				GetInst("UIManager"):Hide("Furnace")
			elseif baseindex == FUNNEL_START_INDEX  or baseindex == INTERPRETER_START_INDEX or baseindex == EMITTER_START_INDEX or baseindex == SENSOR_START_INDEX or baseindex == COLLIDER_START_INDEX then
				UIFrameMgr:frameHide(getglobal("StorageBoxFrame"));
			elseif baseindex == NPCTRADE_START_INDEX then
				UIFrameMgr:frameHide(getglobal("NpcTradeFrame"));
			elseif baseindex == HORSE_EQUIP_INDEX then
				UIFrameMgr:frameHide(getglobal("RideFrame"));
			elseif baseindex == INTERPRETER_START_INDEX then
				UIFrameMgr:frameHide(getglobal("InstructionParserFrame"));
			elseif baseindex == SENSOR_VALUE_START_INDEX then 
				UIFrameMgr:frameHide(getglobal("ContainerSensorValueFrame"));
			elseif baseindex == MANUAL_EMITTER_START_INDEX then
				HideUseEmitterUI();
			end
		end
		
	elseif arg1 == "GE_TOGGLE_GAMEMODE" then
		HideAllFrame(nil, false);
		if CurWorld:isGodMode() and not GetClientInfo():isPC() then
			ClientBackpack:setCreateModeShortCut();
		else
			if CurMainPlayer:getFlying() then
				if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
					CurMainPlayer:changeMoveFlag(0, false);
				else
					CurMainPlayer:setFlying(false);
				end
			end
		end
		PlayMainFrameUIShow();
		--切换模式需要重置一下
		GetInst("TriggerMapInteractiveInterFace"):LeveGameGame()
		GetInst("NewDeveloperStoreInterface"):LeveaGame()
		GetInst("TriggerMapInteractiveInterFace"):InGame()
		GetInst("NewDeveloperStoreInterface"):InGame()
		if CurWorld:isGameMakerMode() then
			LivingToolMgr:showAllPreviewLives(true)
			--召唤一下自己的宠物，因为在load那里被干掉了
			-- 不保存
			if ClientCurGame:getRuleOptionVal(GMRULE_SAVEMODE) == 1 then
				HomelandCallModuleScript("HomelandPetModule","setSummonPet")
			end
			--编辑模式下如果在音乐方块中则复原到原来模型
			if GetInst("MiniUIManager") then
				if GetInst("MiniUIManager"):GetCtrl("clubMain") then
					LeaveMusicClub()
				end
				if DealMusicClubIns then
					if DealMusicClubIns.setOnce then
						DealMusicClubIns:setOnce()
					end
				end
			end
		elseif CurWorld:isGameMakerRunMode() then
			LivingToolMgr:showAllPreviewLives(false)
			LivingToolMgr:createLivesInWorld();
			TriggerObjLibMgr:CreateDropItemInWorld(true);
			CurWorld:quitToolMode();
			if AccountManager then
				if AccountManager:getMultiPlayer() == 0 then
					if CurMainPlayer then
						if DealMusicClubIns then
							if DealMusicClubIns.updateAreaId then
								DealMusicClubIns:updateAreaId(CurMainPlayer:getCurWorldMapId());
							end
						end
					end
				end
			end

			--转变玩法模式的时候去除下选中的状态
			if TriggerObjLibMgr and TriggerObjLibMgr.getDisplayBoardMgr then
				local boardmgr = TriggerObjLibMgr:getDisplayBoardMgr()
				if boardmgr and boardmgr.ShowDisplayBoardSelectView then
					boardmgr:ShowDisplayBoardSelectView();
				end
			end
		end
		ShowToActivityFrame()
	--	getglobal("PlayMainFrame"):Show();
		GetInst("UGCCommon"):ToggleGameMode()
		--更新avatar
		if isAbroadEvn() then
			--修复开发者地图，从玩法切换到编辑模式，avatar被重置的bug
			if GetInst("eventMgr") then
				print("更新avatar 刷新装扮")
				GetInst("eventMgr"):dispatch(EnumGameEvent.Player_Seat_Info_Refresh)
			end
		end
		-- GetInst("GuideMgr"):PushEvent(GuideCfg.Event.ToggleGameMode)--引导屏蔽
	elseif arg1 == "GIE_RIDING_CHANGE" then
		local ge = GameEventQue:getCurEvent();
		local rideType = ge.body.ridingchange.ridetype;
		if CurMainPlayer then
			UpdateMainFucBtn(rideType);
			SetMainUIState();
		end
	elseif arg1 == "GE_STATISTIC" then
		local ge = GameEventQue:getCurEvent();
		local id = ge.body.statisticinfo.id;
		local worldtype = ge.body.statisticinfo.worldtype;
		local p1 = ge.body.statisticinfo.param1;
		local p2 = ge.body.statisticinfo.param2;
		local p3 = ge.body.statisticinfo.param3;
		local p4 = ge.body.statisticinfo.param4;
		local p5 = ge.body.statisticinfo.param5;
		local p6 = ge.body.statisticinfo.param6;
		local p7 = ge.body.statisticinfo.param7;

		if p1 and p1 == "param_to_str" then
			statisticsInGameToStr(id, worldtype, p2, p3, p4, p5, p6, p7);
		else
			statisticsInGame(id, worldtype, p1, p2, p3, p4, p5, p6, p7);
		end 
	elseif arg1 == "GIE_RIDING_ENTERWATERORLAND" then
		local ge = GameEventQue:getCurEvent();
		local bInWater = (ge.body.gameevent.result == 1)
		if bInWater then
			local baricon = getglobal("ChickenEnergyFrameIcon")
			baricon:SetTextureHuiresXml("ui/mobile/texture2/outgame2/uitex3.xml");
			baricon:SetTexUV("sdjm_icon_shuxing02.png");
			getglobal("ChickenEnergyFrame"):Show();
		else
			getglobal("ChickenEnergyFrame"):Hide();
		end
	elseif arg1 == "GE_PLAYER_SHAPE_SHIFT" then
		local ge = GameEventQue:getCurEvent();
		local state = ge.body.shapeshiftinfo.state
		--变形TODO
		if state then
			--隐藏黄框
			if ShortCut_SelectedIndex >= 0 then
				getglobal("ToolShortcut"..(ShortCut_SelectedIndex+1).."Check"):Hide()
			end
		else
			if ShortCut_SelectedIndex >= 0 then
				--getglobal("ToolShortcut"..(ShortCut_SelectedIndex+1).."Check"):Show()
			end
		end
	elseif arg1 == "GE_OPEN_EDIT_ACTORMODEL" then 
		--getglobal("ActorSelectEditFrame"):Show();

		--打开生物模型编辑界面
		if GetInst("MiniUIManager"):GetUI("ActorModelEditAutoGen") then
			GetInst("MiniUIManager"):ShowUI("ActorModelEditAutoGen")
		else
			GetInst("MiniUIManager"):OpenUI("ActorModelEdit", "miniui/miniworld/ugc_actorModelEdit", "ActorModelEditAutoGen")
		end

	elseif arg1 == "GE_MAINPLAYER_DIE" then 
		if ClientCurGame:isInGame() and (ClientCurGame:getGameStage() == 4 or (CurMainPlayer ~= nil and CurMainPlayer.getGameResults and CurMainPlayer:getGameResults()> 0) ) then return end

		if not CurWorld then return end

		if AvatarSummonIndex > 0 then
			if CurWorld and CurWorld:isRemoteMode() then
				local params = {objid = CurMainPlayer:getObjId(),summonid = 0}
				SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.BUZZ.AVATAR_SUMMON_TOHOST, params)
			else
				CurMainPlayer:avatarSummon(0)
			end
			AvatarSummonIndex = 0
		end
    -- 播放奥特曼背景音乐
    elseif arg1 == "GIE_PLAY_ALTMANMUSIC" then 
        local ge = GameEventQue:getCurEvent()
		local blockid = ge.body.playAltmanMusic.blockId
        if altmanMusicCfg[blockid] then
            GetMusicManager():StopMusic()
			GetMusicManager():PlayMusic(altmanMusicCfg[blockid], false)

			isPlayingAtlmanMusic = true

			if altmanMusicTimer then
				threadpool:kick(altmanMusicTimer)
			end
            -- 100秒后切回之前的bgm
            altmanMusicTimer = threadpool:delay(100, function()
                GetMusicManager():StopMusic()
                GetMusicManager():PlayMusic(lastMusicName, true)

				isPlayingAtlmanMusic = false
            end)
        end
    -- buff变化
    elseif arg1 == "GIE_BUFF_CHANGE" then 
        local ge = GameEventQue:getCurEvent()
		local buffId = ge.body.buffChange.buffId
        local changeType = ge.body.buffChange.changeType

        -- changeType : 0-添加 1-移除 2-移除所有
        -- 监测光之力buff消失
        if buffId == 96 and (changeType == 1 or changeType == 2) then
            GetMusicManager():StopMusic()
			GetMusicManager():PlayMusic(lastMusicName, true)

			isPlayingAtlmanMusic = false

            if altmanMusicTimer then
                threadpool:kick(altmanMusicTimer)
            end
        end
	elseif arg1 == "GIE_QQMUSIC_PLAYER" then
		local ge = GameEventQue:getCurEvent()
		if GetInst("QQMusicPlayerManager") then
			GetInst("QQMusicPlayerManager"):HandleEvent(ge)
		end
	elseif arg1 == "GIE_MINICLUB_PLAYER" then
		local ge = GameEventQue:getCurEvent()
		if GetInst("MiniClubPlayerManager") then
			GetInst("MiniClubPlayerManager"):HandleEvent(ge)
		end
	elseif arg1 == "GE_CUSTOMGAME_STAGE" then
		local ge = GameEventQue:getCurEvent();
		local stage = ge.body.cgstage.stage;
		local gametime = ge.body.cgstage.gametime;
		if YearMonsterGameOnStageChange then
			YearMonsterGameOnStageChange(stage)
		end

		if OneKeyCar and OneKeyCar.V2_Gaming then
			OneKeyCar:V2_Gaming(stage)
		end
	elseif arg1 == "GE_MANUAL_EMITTER_UPDATE" then
		local ge = GameEventQue:getCurEvent();
		local id = ge.body.manualEmitterUpdate.type;
		local time = ge.body.manualEmitterUpdate.time;
		local totaltime = ge.body.manualEmitterUpdate.total;
		if GetInst("MiniUIManager"):IsShown("ManualEmitterMain") then
			local ctrl = GetInst("MiniUIManager"):GetCtrl("ManualEmitterMain");
			if ctrl then
				ctrl:UpdateItemIcon(id, time, totaltime);
			end
		end
	elseif arg1 == "GE_EASYMODE_GUIDE_CHECK" then
		local ge = GameEventQue:getCurEvent();
		local deathTimes = ge.body.easyModeGuideCheck.deathTimes;
		Playmain_OnEasyModeGuideCheck(deathTimes);
	elseif arg1 == "GE_SURVIVE_GAME_LOADED" then
		local ge = GameEventQue:getCurEvent();
		local mapid = ge.body.surviveGameLoaded.mapid;

		-- 每次进房间重置
		addOutAccessPermit = false
		addOutAccessSuccess = false
		addOutAccessInfo = nil
		-- 联机
		if AccountManager:getMultiPlayer() > 0 and not IsInTeamupMode() and WorldMgr then
			local roomid = 0
			if ROOM_SERVER_RENT == GetClientInfo():getRoomHostType() then
				roomid = RentPermitCtrl:GetRentRoomID()
			else
				roomid = AccountManager:getUin()
			end
			local roomInfo = {mapid = mapid, roomid = roomid}
			-- 获取宇宙配置管理是否允许降落，允许才上报房间信息
			if WorldMgr:getGameMode() == g_WorldType.OWTYPE_SINGLE --[[and worldDesc.realowneruin == AccountManager:getUin()]] then
				GetInst("CosmicCenterMgr"):InterceptCosmicTransmit(roomInfo, function()
					-- 检查是否有激活的二级星站
					local hasTargetStarStation = false
					-- StarStationTransferMgr:loadMapStarStationData(mapid)
					local num = StarStationTransferMgr:getStarStationDefNum()
					for i = 1, num do
						local transferDef = StarStationTransferMgr:getStarStationDefByIndex(i - 1)
						if transferDef and transferDef.isActive then
							local carbinNum = transferDef:getCabinCount()
							for j = 1, carbinNum do
								local cabinDef = transferDef:getCabinDefByIndex(j - 1)
								if cabinDef then
									-- 是当前地图存档主
									if cabinDef.cabinLevel == 2 then
										hasTargetStarStation = true
										break
									end
								end
							end
						end
					end

					-- 上传满足有激活二级星站的房间信息
					if hasTargetStarStation then
						addOutAccessPermit = true
						AddOutAccessByRoomInfo(mapid, roomid)
					else
						MiniLog("StarStation add_out_access failed!没有二级星站")
					end
				end, true)
			end
		else
			if AccountManager:getMultiPlayer() == 0 then
				MiniLog("StarStation add_out_access failed!单机房间")
			end

			if IsInTeamupMode() then
				MiniLog("StarStation add_out_access failed!组队房间")
			end
		end
	elseif arg1 == "GE_HOST_CONNECT_PROXY_SUCCESS" then
		local ge = GameEventQue:getCurEvent();
		local ready = ge.body.hostConnectProxy.ready;
		local uin = ge.body.hostConnectProxy.uin;
		if not addOutAccessSuccess and ready and (uin == AccountManager:getUin()) and addOutAccessInfo and type(addOutAccessInfo) == "table" then
			AddOutAccessByRoomInfo(addOutAccessInfo.mapid, addOutAccessInfo.roomid)
		end
	end

	-- if FullyCustomModelSelect_OnEvent then
	-- 	FullyCustomModelSelect_OnEvent(arg1);
	-- end
	FCM_OnEvent(arg1)
end

-- 上传满足有激活二级星站的房间信息
function AddOutAccessByRoomInfo(mapid, roomid)
	if not addOutAccessPermit then return end
	-- local web = ClientUrl:GetUrlString("HttpStudioDevelopStore") .. "/miniw/spacemini"
	local web = ClientUrl:GetUrlString("HttpCommon", "miniw/spacemini")

	local uin = AccountManager:getUin()
	local s2, s2t, s2tpure = get_login_sign()
	local now = getServerTime()
	local md5 = gFunc_getmd5(now .. s2 .. uin)

	local url = string.format("%s?act=add_out_access&from=%d&uin=%d&s2t=%s&time=%d&auth=%s", web, 2, uin, s2tpure, now, md5)
	local param = {
		atype = 1,
		roomid = roomid,
		mapid = mapid,
		mapname = "",
		op_uin = uin,
	}
	local param_s = ""
	for key, value in pairs(param) do
		if type(value) == 'string' then 
			value = gFunc_urlEscape(value);
		end
		local ptemp = string.format("%s=%s", key, tostring(value))
		param_s = param_s .. "&" .. ptemp;
	end
	url = url..param_s

	local RespCb = function(ret)
		if ret then
			if ret.code == ErrorCode.OK then
				addOutAccessSuccess = true
			else
				-- 上报房间报错，记录下当前上报信息
				addOutAccessSuccess = false
				addOutAccessInfo = {
					mapid = mapid,
					roomid = roomid,
				}

				-- ShowGameTips(ret.msg)
			end
		end
	end
	ns_http.func.rpc(url, RespCb, nil, nil, ns_http.SecurityTypeHigh, true)
end

--显示睡觉提示弹窗
function ShowSleepNoticeFrame()
	--if FesivialActivity:TonightIsTiangou() then -- 天狗食月不能睡觉
	--	ShowGameTips(GetS(86019))
	--	return
	--end
    --if ClientCurGame and ClientCurGame.setOperateUI then
	--	ClientCurGame:setOperateUI(true)
	--end
	--local getOutOfBed = getglobal("PlayMainFrameBedName")
	--if getOutOfBed then
	--	getOutOfBed:SetText(GetS(86021))
	--	getOutOfBed:Show()
	--end
	--local frame = getglobal("SleepNoticeFrame")
	--if frame then
	--	frame:Show()
	--	local title = getglobal("SleepNoticeFrameTitle")
	--	local sleepBtn = getglobal("SleepNoticeFrameLeftBtn")
	--	local leaveBtn = getglobal("SleepNoticeFrameRightBtn")
	--	local middleBtn = getglobal("SleepNoticeFrameMiddleBtn")
	--	local waitingBtn = getglobal("SleepNoticeFrameLeftBtnWaiting")
	--	if title and sleepBtn and leaveBtn and middleBtn and waitingBtn then
	--		waitingBtn:Hide()
	--		middleBtn:Hide()
	--		sleepBtn:Show()
	--		leaveBtn:Show()
	--		--标题信息的处理逻辑
	--		if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
	--			--联机
	--			threadpool:work(function ()
	--				local num = 0
	--				local totalNum = TotalSleepingNums()
	--				-- 打开循环开关，在Hide的时候再关闭
	--				checkingSleepingPlayers = true
	--				while checkingSleepingPlayers do
	--					num = CurrentSleepingNums()
	--					totalNum = TotalSleepingNums()
    --                    getglobal("SleepNoticeFrameTitle"):SetText(tostring(num).."/"..tostring(totalNum).."人准备睡觉")
    --                    if num == totalNum then
    --                        getglobal("SleepNoticeFrameTitle"):SetTextColor(90, 234, 10)
    --                    else
    --                        getglobal("SleepNoticeFrameTitle"):SetTextColor(250, 122, 15)
    --                    end
    --                    
	--					threadpool:wait(0.5)
	--				end
	--			end)
	--		else
	--			--单机
    --            getglobal("SleepNoticeFrameTitle"):SetText("准备睡觉啦~")
    --            getglobal("SleepNoticeFrameTitle"):SetTextColor(90, 234, 10)
	--		end
	--	end
	--end
	----新冒险模式睡觉修改
	---- if LuaInterface:shouldUseNewHpRule() then--如果是冒险模式和创造转冒险就进行隐藏
	--	
	---- end
	--HideSleepNoticeFrame()
	--PlayMainFrameSleepBtn_OnClick()
	--NewShowSleepNoticeFrame()
end

function NewShowSleepNoticeFrame() 
	--local sleepnum = getglobal("NewSleepNoticeFrameNum")
	-- local icon = getglobal("NewSleepNoticeFrameTopBkg")
	-- local newsleepframe = getglobal("NewSleepNoticeFrame")
	-- if sleepnum and icon and newsleepframe then
	-- 	if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
	-- 	--联机
	-- 		newsleepframe:Show()
	-- 		threadpool:work(function ()
	-- 			local num = 0
	-- 			local totalNum = TotalSleepingNums()
	-- 			-- 打开循环开关，在Hide的时候再关闭
	-- 			checkingSleepingPlayers = true
	-- 			while checkingSleepingPlayers do
	-- 				num = CurrentSleepingNums()
	-- 				totalNum = TotalSleepingNums()
	-- 				sleepnum:SetText(tostring(num).."/"..tostring(totalNum))
	-- 				threadpool:wait(0.5)
	-- 			end
	-- 		end)
	-- 	else
	-- 	--单机
	-- 	newsleepframe:Hide()
	-- 	end
	-- end


	--if ClientCurGame:isInGame() and AccountManager:getMultiPlayer() > 0 then
	--if  AccountManager:getMultiPlayer() > 0 then --睡觉新UI 隐藏了老UI
	--	--联机
	--	GetInst("MiniUIManager"):AddPackage(
	--		{
	--			"miniui/miniworld/common",
	--			"miniui/miniworld/c_ingame",
	--			"miniui/miniworld/common_comp"
	--		},
	--		"newSleepNoticeFrameAutoGen"
	--	)
	--	GetInst("MiniUIManager"):OpenUI("newSleepNoticeFrame","miniui/miniworld/adventure","newSleepNoticeFrameAutoGen")
	--	
	--else
	--	--单机
	--	GetInst("MiniUIManager"):CloseUI("newSleepNoticeFrameAutoGen")
	--end


end

function isNewBoxFrame(blockid) --code by renjie 替换了FGUI的箱子
	local adventureBox={730,734,735,757,758,759,801,969,974,979,1231,1180,1181,371,998,999,390008,150022,1035}
	-- 遍历数组
	for k,v in ipairs(adventureBox) do
	  if blockid == v then
	  	return true;
	  end
	end

	if blockid >= 200370 then -- 200370 之后的箱子都是新增的，用新的箱子UI，重构完成之前方便策划扩展
		return true;
	end
	
	return false;
end

function HideSleepNoticeFrame()
    if ClientCurGame and ClientCurGame.setOperateUI then
		if not IsUGCEditing() then
			ClientCurGame:setOperateUI(false)
		end
	end
	local frame = getglobal("SleepNoticeFrame")
	if frame and frame:IsShown() then
        frame:Hide()
        checkingSleepingPlayers = false
	end
	
	GetInst("MiniUIManager"):CloseUI("newSleepNoticeFrameAutoGen") --关闭新UI

end

function HideNewSleepNoticeFrame(ignoreOperateUI)
	if ClientCurGame and ClientCurGame.setOperateUI and not ignoreOperateUI then
		ClientCurGame:setOperateUI(false)
	end
	local newframe = getglobal("NewSleepNoticeFrame")
	if newframe and newframe:IsShown() then
        newframe:Hide()
        checkingSleepingPlayers = false
	end
	
    GetInst("MiniUIManager"):CloseUI("newSleepNoticeFrameAutoGen") --关闭新UI

end

function ShowBedBtn(visible)
	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:ShowBedBtn(visible)
	end
end

function ShowRideBtn(visible)
	-- getglobal("PlayMainFrameRide"):Show()
	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:ShowRideBtn(visible)
	end
end

function UpdateMainFucBtn(rideType)
	getglobal("PlayMainFrameFly"):Hide();
	getglobal("PlayMainFrameSneak"):Hide();
	RideHPBar_ShowOrHide(false);
	PlayerHPBar_ShowOrHide(false);
	ShowRideBtn(false);
	ShowBedBtn(false);
    getglobal("ChickenEnergyFrame"):Hide();
	-- if LuaInterface:shouldUseNewHpRule() then--如果是冒险模式和创造转冒险就进行隐藏
	HideNewSleepNoticeFrame()
	-- else
	-- 	HideSleepNoticeFrame()
	-- end
	setVehicleUI(false)
	HideUseEmitterUI();
	--if GetClientInfo():isMobile() then
	--	getglobal("Mobile_Rect_VehicleControlFrame"):Hide()
	--end
	local type = CurMainPlayer:getMountType();

	if type > 0 then	--坐骑、椅子、床
        if CurMainPlayer:getOPWay() == 0 and not CurMainPlayer:isShapeShift() then
			if type == 2 then --如果是床就显示下床按钮
				ShowBedBtn(true);
			else
				ShowRideBtn(true);
			end
			if type == MOUNT_SLEEP and CurWorld and CurWorld.isDaytime and not CurWorld:isDaytime() then
				-- 只有不是白天且躺在床上时才可以显示睡觉提示弹窗
				if true --[[LuaInterface:shouldUseNewHpRule()--]] then--如果是冒险模式和创造转冒险就进行隐藏
					PlayMainFrameSleepBtn_OnClick()
					NewShowSleepNoticeFrame()
				else
					ShowSleepNoticeFrame()
				end
			end
		end
		if type == MOUNT_RIDE then		--坐骑、矿车、船
			local ride = CurMainPlayer:getRidingHorse()
			if ride ~= nil then
				if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
				else
					PlayMain.RideHP:reset();
					RideHPBar_ShowOrHide(true);
					if ride:hasHorseSkill(HORSE_SKILL_MUTATE_FLY) then    --飞鸡坐骑
						getglobal("ChickenEnergyFrame"):Show();
					end
				end
			else
				if not CurWorld:isCreativeMode() and not CurWorld:isGameMakerMode() then
					if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
						PlayerHPBar_ShowOrHide(false)
					elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
						PlayerHPBar_ShowOrHide(false)
					else
						PlayerHPBar_ShowOrHide(true)
					end
				end
			end
		elseif type == MOUNT_DRIVE then 	--物理机械
			--一期没有血量显示，先注释掉
			--local vehicle = CurMainPlayer:getDrivingVehicle()
			--if vehicle ~= nil then
			--	local hp = math.ceil(vehicle:getLifeCurrent());
			--	local maxHP = vehicle:getLifeLimit()
			--	getglobal("RideHPBarCur"):SetWidth(266*hp/maxHP);
			--	getglobal("RideHPBarRatio"):SetText(hp.."/"..maxHP);
			--	RideHPBar_ShowOrHide(true);
			--end
			ShowRideBtn(false);
			ShowBedBtn(false);
			setVehicleUI(true)
			if GetInst("MiniUIManager"):IsShown("PackingCMAutoGen") then --如果微缩工具在使用则关闭
				GetInst("MiniUIManager"):GetCtrl("PackingCM"):OnCloseBtnClicked()
			end
			-- if IsUIFrameShown("MapEdit") then --如果地形编辑工具在使用则关闭
			-- 	GetInst("UIManager"):GetCtrl("MapEdit"):CloseBtnClicked()
			-- end
			if GetInst("MiniUIManager"):IsShown("MapEditAutoGen") then --如果地形编辑工具在使用则关闭
				GetInst("MiniUIManager"):GetCtrl("MapEdit"):CloseUI()
			end
		elseif type == MOUNT_MANIPULATE_EMITTER then
			if CurMainPlayer:getUsingEmitter() then
				ShowRideBtn(true);
				ShowBedBtn(false);
				ShowUseEmitterUI();
			else
				ShowRideBtn(false);
				HideUseEmitterUI();
			end
		end

		if CurWorld:isGodMode() then
			if CurMainPlayer:isFlying() then
				if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
					CurMainPlayer:changeMoveFlag(0, false);
				else
					CurMainPlayer:setFlying(false);
				end
			end
		else
			local rocket = CurMainPlayer:getRidingRocket();
			if type ~= MOUNT_RIDE or rocket then
				if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
					PlayerHPBar_ShowOrHide(false);
				elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
					PlayerHPBar_ShowOrHide(false);
				else
					PlayerHPBar_ShowOrHide(true);
				end
			end
		end
	else
		if CurWorld:isGodMode()  then
			if CurMainPlayer:getOPWay() == 0 and GetClientInfo():isMobile() then
				getglobal("PlayMainFrameFly"):Show();
			end
		else
			if GetClientInfo():isMobile() and CurMainPlayer:getOPWay() == 0 then
				if not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SNEAK) or not CheckPlayerActionState(ENABLE_SNEAK) then
					getglobal("PlayMainFrameSneak"):Hide();
				else
					getglobal("PlayMainFrameSneak"):Show();
				end
			end
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
				PlayerHPBar_ShowOrHide(false);
			elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
				PlayerHPBar_ShowOrHide(false);
			else
				PlayerHPBar_ShowOrHide(true);
			end
		end
	end
end

function UpdateUIBtnByOPWayChange()
	if CurMainPlayer:getOPWay() == 1 then --球模式
		getglobal("PlayMainFrameSneak"):Hide();
		ShowRideBtn(false)
		ShowBedBtn(false);
		getglobal("PlayMainFrameFly"):Hide();

		if GetClientInfo():isPC() and not getkv("ball_operate_guide") then
			ShowBallOperateTipsTime = 10;
			getglobal("PcGuideKeySightModeTips"):SetText(GetS(1348));
			getglobal("PcGuideKeySightMode"):Show();
			setkv("ball_operate_guide", true);
		end

		if CurWorld and CurWorld:isGodMode() then
			if CurMainPlayer and CurMainPlayer:isFlying() then
				if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
					CurMainPlayer:changeMoveFlag(0, false);
				else
					CurMainPlayer:setFlying(false);
				end
			end
		end
	elseif CurMainPlayer:getOPWay() == 3 then
		getglobal("PlayMainFrameSneak"):Hide();
		ShowRideBtn(false)
		ShowBedBtn(false);
		getglobal("PlayMainFrameFly"):Hide();

		if GetClientInfo():isPC() and not getkv("basketball_operate_guide") then
			ShowBallOperateTipsTime = 10;
			getglobal("PcGuideKeySightModeTips"):SetText(GetS(29999));
			local lWidth = getglobal("PcGuideKeySightModeTips"):GetTextExtentWidth(GetS(29999));
			getglobal("PcGuideKeySightMode"):SetWidth(lWidth+30);
			getglobal("PcGuideKeySightMode"):Show();
			setkv("basketball_operate_guide", true);
		end

		if CurWorld and CurWorld:isGodMode() then
			if CurMainPlayer and CurMainPlayer:isFlying() then
				if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
					CurMainPlayer:changeMoveFlag(0, false);
				else
					CurMainPlayer:setFlying(false);
				end
			end
		end
	elseif CurMainPlayer:getOPWay() == 5 then
		getglobal("PlayMainFrameSneak"):Hide();
		ShowRideBtn(false)
		ShowBedBtn(false);
		getglobal("PlayMainFrameFly"):Hide();

		if GetClientInfo():isPC() and not getkv("pushsnowball_operate_guide") then
			ShowBallOperateTipsTime = 10;
			getglobal("PcGuideKeySightModeTips"):SetText(GetS(86042));
			local lWidth = getglobal("PcGuideKeySightModeTips"):GetTextExtentWidth(GetS(86042));
			getglobal("PcGuideKeySightMode"):SetWidth(lWidth+30);
			getglobal("PcGuideKeySightMode"):Show();
			setkv("pushsnowball_operate_guide", true);
		end

		if CurWorld and CurWorld:isGodMode() then
			if CurMainPlayer and CurMainPlayer:isFlying() then
				if CurMainPlayer.isNewMoveSyncSwitchOn and CurMainPlayer:isNewMoveSyncSwitchOn() then
					CurMainPlayer:changeMoveFlag(0, false);
				else
					CurMainPlayer:setFlying(false);
				end
			end
		end
	else
		if CurMainPlayer:getMountType() > 0 then
			if CurMainPlayer:getMountType() ~= MOUNT_DRIVE then
				ShowRideBtn(true)
			end
		else

			if CurWorld then
				if CurWorld:isGodMode() then
					if GetClientInfo():isMobile() then
						getglobal("PlayMainFrameFly"):Show();
					end
				else
					if GetClientInfo():isMobile() then
						if not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SNEAK) or not CheckPlayerActionState(ENABLE_SNEAK) then
							getglobal("PlayMainFrameSneak"):Hide();
						else
							getglobal("PlayMainFrameSneak"):Show();
						end
					end
				end
			end
		end
	end
end

TraceBlockIsShow = false;

local t_BGM = {
	[0] = {
		day = {
			"sounds/music/bgm_day_1.ogg",
			"sounds/music/bgm_day_2.ogg",
			"sounds/music/bgm_day_3.ogg",
			"sounds/music/bgm_day_4.ogg",
			"sounds/music/bgm_day_5.ogg",
		},
		night = {
			"sounds/music/bgm_night_1.ogg",
			"sounds/music/bgm_night_2.ogg",
			"sounds/music/bgm_night_3.ogg",
			"sounds/music/bgm_night_4.ogg",
			"sounds/music/bgm_night_5.ogg",
		},
	},
	[1] = {
		day = {
			"sounds/music/bgm_earthcore_day_1.ogg",
		},
		night = {
			"sounds/music/bgm_earthcore_night_1.ogg",
		}
	},
	[2] = {
		day = {
			"sounds/music/bgm_planet_day_1.ogg",
			"sounds/music/bgm_planet_day_2.ogg",
			"sounds/music/bgm_planet_day_3.ogg",
			"sounds/music/bgm_planet_day_4.ogg",
		},
		night = {
			"sounds/music/bgm_planet_night_1.ogg",
			"sounds/music/bgm_planet_night_2.ogg",
			"sounds/music/bgm_planet_night_3.ogg",
		},
	},
}

-- 背景环境音
local t_BGMAmbient = {
	day = {
		[BIOME_RAINFOREST] = {"sounds/env/Jungleday.ogg"},
		[BIOME_VOLCANO] = {"sounds/env/volcano.ogg"},
		[BIOME_AIR_PLAINS] = {"sounds/env/skypiea.ogg"},
		[BIOME_BEACH] = {"sounds/env/SeaWaves.ogg"},
	},
	night = {
		[BIOME_RAINFOREST] = {"sounds/env/Junglenight.ogg"},
		[BIOME_FOREST] = {"sounds/env/Junglenight.ogg"},
		[BIOME_PLAINS] = {"sounds/env/Junglenight.ogg"},
		[BIOME_SWAMPLAND] = {"sounds/env/Junglenight.ogg"},
		[BIOME_JUNGLE] = {"sounds/env/Junglenight.ogg"},
		[BIOME_AIR_PLAINS] = {"sounds/env/Junglenight.ogg"},
		[BIOME_PLAINS_ARID] = {"sounds/env/Junglenight.ogg"},
		[BIOME_PLAINS_DANDELION] = {"sounds/env/Junglenight.ogg"},
		[BIOME_PLAINS_RAPESEED] = {"sounds/env/Junglenight.ogg"},
		[BIOME_FOREST_LAVENDER] = {"sounds/env/Junglenight.ogg"},
		[BIOME_FOREST_FOXTAIL] = {"sounds/env/Junglenight.ogg"},
		[BIOME_FOREST_CHRYSANTH] = {"sounds/env/Junglenight.ogg"},
		[BIOME_BASIN_RICE] = {"sounds/env/Junglenight.ogg"},
		[BIOME_SWAMPLAND_RIVERSIDE] = {"sounds/env/Junglenight.ogg"},
		[BIOME_AIRISLAND_SHINE] = {"sounds/env/Junglenight.ogg"},
		[BIOME_ISLAND_LAND_TULIP] = {"sounds/env/SeaWaves.ogg"},
		[BIOME_ISLAND_SHORE_TULIP] = {"sounds/env/SeaWaves.ogg"},
		[BIOME_VOLCANO] = {"sounds/env/volcano.ogg"},
		-- [BIOME_AIR_PLAINS] = {"sounds/env/skypiea.ogg"},
		[BIOME_BEACH] = {"sounds/env/SeaWaves.ogg"},
	}
}

-- 虚空之夜BGM
local BGMVoidNight = "sounds/music/bgm_void_night.ogg"
local BGMZombieWave = "sounds/music/bgm_zombie_night.ogg"

PlayBossMusic = false;
MusicFrequency = 300;
LeaveRoomType = 0;		--1主机关闭房间 2主机退出游戏
SendMsgWaitTime = 0;
_G.LastBiomeType = -1;
local lastMapID = 0;

if true and GetClientInfo():isPC() and not AccountManager:getNoviceGuideState("guidekey") then
	GuideKeyountdown = 60; --1分钟
end

function PlayMusicByBoss(code, bossId)
	if code == -1 then
		if not isPlayingAtlmanMusic then
			GetMusicManager():StopMusic()
		end
		PlayBossMusic = false;
		MusicFrequency = -1;
	elseif code == -2 then
		if not isPlayingAtlmanMusic then
			GetMusicManager():StopMusic()
		end
		PlayBossMusic = true;
		if bossId >= 3502 and bossId <= 3504 then
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/boss_earthcore_1.ogg", true)
			end
            lastMusicName = "sounds/music/boss_earthcore_1.ogg"
		elseif bossId == 3510 or bossId == 3514 then
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/boss_golem_1.ogg", true)
			end
            lastMusicName = "sounds/music/boss_golem_1.ogg"
		elseif bossId == 3515 then
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/boss_voidwalker_1.ogg", true)
			end
            lastMusicName = "sounds/music/boss_voidwalker_1.ogg"
		elseif bossId == 3516 then
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/boss_voidwalker_2.ogg", true);
			end
            lastMusicName = "sounds/music/boss_voidwalker_2.ogg"
		elseif bossId == 3519 then
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/boss_nianshou.ogg", true);
			end
            lastMusicName = "sounds/music/boss_nianshou.ogg"
		end
	end
end

local function getCurBiomeType()
	if CurMainPlayer then
		local x, y, z = CurMainPlayer:getPosition(0,0,0)
		x = math.floor(x / 100)
		z = math.floor(z / 100)
		if CurWorld.getBiomeType then
			local biometype = CurWorld:getBiomeType(x, z)
			if biometype >= BIOME_VOLCANO and biometype <= BIOME_VOLCANO_CORE then -- 都是火山地形
				return BIOME_VOLCANO
			end
			return biometype
		end
	end
	return 0
end

local begin_time=0;
local flag=1;
local BGMSound_ChangeVal = 0.01;
local BGMSound_CurVal = 0;
local checkTimeTbl = {
	checkBiomeTypeTime = 0,
	checkKickPlayerTime = 0,
	checkUpdatePlaymate = 0,
	checkRestOrSleepTime = 0,
	checkStopActorInviteActTime = 0,
	checkOnceSecondTick = 0, --一秒一次 
}
previousBiometype = nil
function PlayMainFrame_OnUpdate()
	local musicMode = 0;
	local id = 0;

    if previousBiometype == nil then
        previousBiometype = getCurBiomeType() -- 第一次进来先获取一次
    end

    local curBiometype = previousBiometype -- 使用之前保存的 biometype 变量

    if checkTimeTbl.checkBiomeTypeTime >= 1 then -- 检测地形改成1秒检测一次提高效率
        checkTimeTbl.checkBiomeTypeTime = 0
        curBiometype = getCurBiomeType()
        previousBiometype = curBiometype -- 保存当前的 biometype 以备下次使用
    else
        checkTimeTbl.checkBiomeTypeTime = checkTimeTbl.checkBiomeTypeTime + arg1
    end

	local ugcEditing = UGCModeMgr and UGCModeMgr:IsEditing()

	musicMode,id = CurWorld:getBGMusicMode(id);
	if musicMode == 0 and CurWorld:getOWID() ~= NewbieWorldId and CurWorld:getOWID() ~= NewbieWorldId2 then
		PlayMainFrame_ProcessPlayBGM(curBiometype)
	end

	if curBiometype then
		UpdateBiomeAmbient(curBiometype) -- 氛围罩
		LastBiomeType = curBiometype
	end

	BGMSound_CurVal = BGMSound_CurVal + BGMSound_ChangeVal;
	if BGMSound_CurVal < 0 then
		--print("kgq BGMSound_CurVal", BGMSound_CurVal)
		GetMusicManager():PlayBGMSound2D2DControl("", BGMSound_CurVal, true);
	elseif BGMSound_CurVal <= 1 then
		--print("kgq BGMSound_CurVal", BGMSound_CurVal)
		GetMusicManager():SetBGMSoundVolume(BGMSound_CurVal);
	end

	-- 黑名单踢人
	if not ugcEditing then 
		checkTimeTbl.checkKickPlayerTime = checkTimeTbl.checkKickPlayerTime + arg1
		if checkTimeTbl.checkKickPlayerTime >= 1 then -- 改成每秒检测一次
			checkTimeTbl.checkKickPlayerTime = 0
			QueryAndKickPlayerInBlackList()
		end
		
	end

	--最近玩伴添加
	if not ugcEditing then
		checkTimeTbl.checkUpdatePlaymate = checkTimeTbl.checkUpdatePlaymate + arg1
		if checkTimeTbl.checkUpdatePlaymate >= 1 then -- 改成每秒检测一次
			checkTimeTbl.checkUpdatePlaymate = 0
			UpdatePlaymateInGame()
		end
		
	end

	--新手
	if CurWorld:getOWID() == NewbieWorldId then
		if AccountManager:getCurNoviceGuideTask() == 4 and not TraceBlockIsShow then
			TraceBlockIsShow = CurMainPlayer:beginTraceBlock(650);
		elseif AccountManager:getCurNoviceGuideTask() == 6 and not TraceBlockIsShow then
			TraceBlockIsShow = CurMainPlayer:beginTraceBlock(200);
		end
		--临时方案
		getglobal("CharacterActionBtn"):Hide()
	end

	-- 根据FPS调整视距
	if SetViewDistanceByFPS and not CurMainPlayer:isFlying() then
		SetViewDistanceByFPS()
	end

	if SwapBlinkBtnName ~= nil then
		local blinkTexture = getglobal(SwapBlinkBtnName.."Check");
		if SwapBlinkTime > 0 then
			SwapBlinkTime =  SwapBlinkTime - 1;
			if blinkTexture:IsShown() then
				blinkTexture:Hide();
			else
				blinkTexture:Show();
			end
		else
			if blinkTexture:IsShown() then
				blinkTexture:Hide();
			end
			SwapBlinkBtnName = nil;
			SwapBlinkTime = 4;
		end
	end

	if GetClientInfo():isPC() and not AccountManager:getNoviceGuideState("guidekey") then
		GuideKeyountdown = GuideKeyountdown - arg1;
		if GuideKeyountdown < 0 then
			AccountManager:setNoviceGuideState("guidekey", true);
			GuideKey_Finish();
		end
	end

	if ApplyPermitsCD > 0 then
		ApplyPermitsCD = ApplyPermitsCD - arg1;
		if ApplyPermitsCD <= 0 then
			ApplyPermitsCD = -1;
		end
	end

	--房间退出信息
	if SendMsgWaitTime > 0 then
		SendMsgWaitTime = SendMsgWaitTime - arg1;
		if SendMsgWaitTime <= 0 then
			if LeaveRoomType == 1 then
				GoToMainMenu();
			elseif LeaveRoomType == 2 then
				GameExit();
			elseif LeaveRoomType == 3 then

			end
		end
	end



	if ShowBallOperateTipsTime > 0 then
		ShowBallOperateTipsTime = ShowBallOperateTipsTime - arg1;
		if ShowBallOperateTipsTime < 0 then
			getglobal("PcGuideKeySightMode"):Hide();
			getglobal("PcGuideKeySightModeTips"):SetText(GetS(3839));
			getglobal("PcGuideKeySightMode"):SetWidth(420);
		end
	end

	local tipsFrame=getglobal("PhysxTipsFrame")
	local bkg=getglobal("PhysxTipsFrameBkg");
	local name=getglobal("PhysxTipsFrameName")

	if CurWorld then
		if CurWorld:isGodMode() and GetIWorldConfig():getGameData("physxparam")==1 and DebugMgr:getMapStability()<30 and begin_time==0 then
			begin_time=os.time();
		end
		
		-- 刷新方块血条
		local blockHP = CurWorld:isShowBlockHealth()
		if blockHP then
			local playermainctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
			if playermainctrl then
				playermainctrl.block_health_bar_ctrl:OnRefreshProgress(blockHP)
			end
		end
	end

	--local tipsFrame=getglobal("PhysxTipsFrame");
	local cur_lang=get_game_lang();
	local end_time=os.time();
	local staytime= end_time-begin_time;
	local mobile_offset = 0;		--手机版字体较小，提示框做适当偏移

	if GetClientInfo():isMobile() then
		mobile_offset = -60;
	end
	if CurWorld then
		if CurWorld:isGodMode() then
			if GetIWorldConfig():getGameData("physxparam")==0 then
				tipsFrame:Hide()
			else
				if DebugMgr:getMapStability()<30 then
					if cur_lang==0 or cur_lang==2 then
						if DebugMgr:IsDevBuild() == true and GetIWorldConfig():getGameData("fpsbuttom")==1 then
							tipsFrame:SetPoint("topleft", "PlayMainFrame", "topleft", 285 + mobile_offset, 40)
						elseif DebugMgr:IsDevBuild() == true or GetIWorldConfig():getGameData("fpsbuttom")==1 then
							tipsFrame:SetPoint("topleft", "PlayMainFrame", "topleft", 173 + mobile_offset, 40)
						elseif DebugMgr:IsDevBuild() == false and GetIWorldConfig():getGameData("fpsbuttom")==0 then
							tipsFrame:SetPoint("topleft", "PlayMainFrame", "topleft", 5, 40)
						end

					else
						--其他语言字体较小，提示框位置需要调整
						if DebugMgr:IsDevBuild() == true and GetIWorldConfig():getGameData("fpsbuttom")==1 then
							tipsFrame:SetPoint("TOPLEFT", "PlayMainFrame", "TOPLEFT", 205 + mobile_offset, 40)
						elseif DebugMgr:IsDevBuild() == true or GetIWorldConfig():getGameData("fpsbuttom")==1 then
							tipsFrame:SetPoint("TOPLEFT", "PlayMainFrame", "TOPLEFT", 120 + mobile_offset, 40)
						elseif DebugMgr:IsDevBuild() == false and GetIWorldConfig():getGameData("fpsbuttom")==0 then
							tipsFrame:SetPoint("TOPLEFT", "PlayMainFrame", "TOPLEFT", 5 , 40)
						end
					end
					bkg:SetPoint("topleft", "PhysxTipsFrame", "topleft", -2, -5)
					name:SetPoint("center", "PhysxTipsFrameBkg", "center", 0, 3)
					if staytime<=3 and flag==1 then
						tipsFrame:Show();
					else
						begin_time=0;
						flag=0;
						tipsFrame:Hide();
					end
				else
					begin_time=0;
					flag=1;
					tipsFrame:Hide()
				end
			end
		else
			tipsFrame:Hide()
		end
	else
		tipsFrame:Hide()
	end

	--if CurMainPlayer and CurMainPlayer:getMountType() == MOUNT_DRIVE then
	--	setVehicleUI(true)
	--end
	if CurWorld and CurWorld:isGameMakerRunMode()  then
		if ClientCurGame:getGameStage() ~= CGAME_STAGE_SHOWINTROS then
			managePassPortCountDown();
		end
	else
		managePassPortCountDown();
    end
    
	checkTimeTbl.checkRestOrSleepTime = checkTimeTbl.checkRestOrSleepTime + arg1
	if checkTimeTbl.checkRestOrSleepTime >= 1 then -- 改成每秒检测一次
		checkTimeTbl.checkRestOrSleepTime = 0
	
		local daytime = CurWorld:getHours()
		local hour = math.floor(daytime)
		local min = math.floor((daytime-hour)*60)
		
		--游戏时间过渡到18点时，马上显示睡觉UI界面
		--躺床上或者睡觉
		local RestOrSleep = (CurMainPlayer.isRestInBed and CurMainPlayer:isRestInBed()) or (CurMainPlayer.isSleeping and CurMainPlayer:isSleeping())

		if CurWorld and hour == 18 and min == 0 and (CurMainPlayer.isRestInBed and CurMainPlayer:isRestInBed() and (not getglobal("SleepNoticeFrame"):IsShown())) then
			if true --[[LuaInterface:shouldUseNewHpRule()--]] then--如果是冒险模式和创造转冒险就进行隐藏
				PlayMainFrameSleepBtn_OnClick()
				NewShowSleepNoticeFrame()
			else
				ShowSleepNoticeFrame()
			end
			CurMainPlayer:getLivingAttrib():removeBuff(80);
		elseif CurWorld and hour == 6 and min == 0 and RestOrSleep and getglobal("SleepNoticeFrame"):IsShown() then
			if true --[[LuaInterface:shouldUseNewHpRule()--]] then --如果是冒险模式和创造转冒险就进行隐藏
				HideNewSleepNoticeFrame()
			else
				HideSleepNoticeFrame()
				CurMainPlayer:getLivingAttrib():addBuff(80, 1);
			end
		end
	end
	CheckShowWaterPressureGuage() 
	if not ugcEditing then 
		UpdateRiddleBird() 

		checkTimeTbl.checkStopActorInviteActTime = checkTimeTbl.checkStopActorInviteActTime + arg1
		if checkTimeTbl.checkStopActorInviteActTime >= 0.25 then -- 改成每0.25秒检测一次
			--检测到移动后停止互动动作
			stopActorInviteAct()
		end
		-- Show_PlayMainAskAddFriend()

		
		checkShowOxygenBarShow() 
		CheckShowThermometerFrame()--显示温度计
	end
	CheckShowDigProFrame()--显示挖掘进度条

	
	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl.player_revive_ctrl:OnRefreshProgress()
	end

	--一秒一次的检测
	if checkTimeTbl.checkOnceSecondTick >= 1 then 
		checkTimeTbl.checkOnceSecondTick = 0
	else
		checkTimeTbl.checkOnceSecondTick = checkTimeTbl.checkOnceSecondTick + arg1
	end
end

isPlayingBGM = false
BGMFrequency = 20
function PlayMainFrame_ProcessPlayBGM(curBiometype)
	if not PlayBossMusic then
		-- 虚空之夜播放专用BGM
		if WorldMgr and WorldMgr.isVoidNight and WorldMgr:isVoidNight() then
			if BGMVoidNight and lastMusicName ~= BGMVoidNight then
				lastMusicName = BGMVoidNight
				GetMusicManager():StopMusic()
				GetMusicManager():PlayMusic(BGMVoidNight, true)

				-- MiniLog("Play Void Night BGM")
				MusicFrequency = 0
			else
			end

			--在[虚空之夜]外出探索（虚空之夜活动任务上报）
			HollowNightActivity_HollowNightStatusChange(true)
			if CurMainPlayer.isSleeping and not CurMainPlayer:isSleeping() then
				HollowNightActivity_GameEventReport("explore_on", 0)
			end
		elseif WorldMgr and WorldMgr.isZombieWave and WorldMgr:isZombieWave() then
			-- 尸潮，播放BGM
			if BGMZombieWave and lastMusicName ~= BGMZombieWave then
				lastMusicName = BGMZombieWave
				GetMusicManager():StopMusic()
				GetMusicManager():PlayMusic(BGMZombieWave, true)
				MusicFrequency = 0
			end
		else
			MusicFrequency = MusicFrequency - arg1;
			if CurWorld and CurWorld:getCurMapID() ~= lastMapID then
				lastMapID = CurWorld:getCurMapID();
				if CurWorld:getCurMapID() ~= 2 then  --去萌眼星之后，会先播放bgm_planet_land
					MusicFrequency = -1
				end
			end
			if MusicFrequency < 0 and not FesivialActivity:TonightIsTiangou() then
				MusicFrequency =  math.random(250, 300);
				
				local curMapId = CurWorld and CurWorld:getCurMapID() or 0;
				local hour = ClientCurGame:getGameTimeHour();
				local timeType = (hour>=7 and hour<=18) and "day" or "night";
				local t = t_BGM[curMapId][timeType];

				if not isPlayingAtlmanMusic then
					GetMusicManager():StopMusic()
				end
				local randomIndex = math.random(1, #(t));
				print("kekeke playMusic", t, randomIndex, t[randomIndex]);

				MiniLog("Play Normal " .. t[randomIndex] .. " BGM")

				if not isPlayingAtlmanMusic then
					GetMusicManager():PlayMusic(t[randomIndex], false)
					lastMusicName = t[randomIndex]
				end
			end

			--虚空之夜活动上报相关
			if WorldMgr and WorldMgr.isAdventureMode and WorldMgr:isAdventureMode() then
				HollowNightActivity_HollowNightStatusChange(false)
			end
		end
	end

	BGMFrequency = BGMFrequency - arg1;
	-- 玩家低于55格就停止背景音，20秒检测一次
	if BGMFrequency < 0 then
		local playerPos = CurMainPlayer:getPosition()
		if playerPos.y < 5500 then
			GetMusicManager():PlayBGMSound2D2DControl(null, 0, false)
		end
	end
	--print("kekeke curBiometype", curBiometype, LastBiomeType);
	if (curBiometype and curBiometype ~= LastBiomeType)   then -- 部分地形音效特别(雨林、火山、空岛等)
		local playerPos = CurMainPlayer:getPosition()
		if playerPos.y < 5500 then
			return
		end
		if BGMSound_CurVal < 0 then
			BGMSound_CurVal = 0;
		elseif BGMSound_CurVal > 1 then
			BGMSound_CurVal = 1;
		end
		local Biome_BGMVal = BGMSound_CurVal * 0.1
		local hour = ClientCurGame:getGameTimeHour();
		local timeType = (hour>=7 and hour<=18) and "day" or "night";
		local sound = t_BGMAmbient[timeType][curBiometype] or "";
		if sound ~= "" then
			local randomIndex = math.random(1, #(sound));
			sound = sound[randomIndex];
			BGMSound_ChangeVal = 0.2;
			GetMusicManager():PlayBGMSound2D2DControl(sound, Biome_BGMVal, true)
		else
			BGMSound_ChangeVal = -0.025;
		end
	end
end

-- 更新氛围罩
function UpdateBiomeAmbient(biometype)
	if biometype == LastBiomeType then
		return
	end

	-- 火山氛围罩   -- 策划说效果不好看，暂时去掉
	-- if biometype == BIOME_VOLCANO then
	-- 	getglobal("VolcanoAmbient"):Show()
	-- elseif lastBiomeType == BIOME_VOLCANO then
	-- 	getglobal("VolcanoAmbient"):Hide()
	-- end
end

function EnterWorld(mapid)
	local curWorldId = nil
	HideMiniLobby(true); --由于跳烈焰星会不进if隐藏minilobby，现在提出来 --海外新UI做的大厅进入地图时要做清理，所以传参true
	if mapid == 0 or mapid == 2 then	--进入了主世界或者太空
		-- if getglobal("MiniLobbyFrame"):IsShown() then
		-- 	getglobal("MiniLobbyFrame"):Hide();
		-- end
		
		UpdateTaskTrackFrame();			--刷新成就追踪面板
		-- if getglobal("InstanceTaskFrame"):IsShown() then
		-- 	getglobal("InstanceTaskFrame"):Hide();
		-- end

		if GetInst("MiniUIManager"):IsShown("BossLifeInfoAutoGen") then
			GetInst("MiniUIManager"):HideUI("BossLifeInfoAutoGen")
		end
		if getglobal("IntroduceFrame"):IsShown() then
			getglobal("IntroduceFrame"):Hide();
        end
        
        local worldid = 0
        local isRoomOwner = IsRoomOwner()
        if isRoomOwner or AccountManager:getMultiPlayer() == 0 then   -- 单机或房主
            local wdesc = AccountManager:getCurWorldDesc();
            if wdesc then
                worldid = wdesc.fromowid
                if wdesc.fromowid == 0 then
                    worldid = wdesc.worldid
                end
            end
			curWorldId = worldid
        else    -- 客机
            worldid = DeveloperFromOwid
			curWorldId = worldid
            -- 云服客机不能只用worldId当作标识，否则会导致同一张图开不同云服，只有第一个有弹窗
            local csroomid = GetCurrentCSRoomId()

            if csroomid and csroomid ~= "" then
                worldid = ""..worldid.."_"..csroomid
            end
        end

		if worldid and worldid ~= 0 then
			local mapflage="ExtremityTipsFrame_Space".."_"..worldid;
			if mapid==2 and not getkv(mapflage) then --进入太空世界显示系统弹框
				setkv(mapflage,true);
				if GetRecordPkgManager():isRecordPlaying() == false then
					OpenExtremityTipsFrame()
					-- getglobal("ExtremityTipsFrame"):Show();
				end
			end
		end

		if mapid == 0 then
			PlayBGMusicByMainWorld();
		elseif mapid == 2 then
			MusicFrequency = 77;
			if not isPlayingAtlmanMusic then
				GetMusicManager():PlayMusic("sounds/music/bgm_planet_land.ogg", false)
			end
            lastMusicName = "sounds/music/bgm_planet_land.ogg"
		end
	else			--进入了副本
		-- getglobal("TaskTrackFrame"):Hide();			--隐藏成就追踪面板 --改版后就不隐藏了 code_by:huangfubin
		-- SetCurIntanceGoal(mapid);
		-- getglobal("InstanceTaskFrame"):Show();
	end

	if ResetTaskTrackSys then
		ResetTaskTrackSys()
	end
	
	if GetInst("TaskSystemManager") then
		GetInst("TaskSystemManager"):UpdateTrack()
	end
	
	--科技树初始化
	local techTreeCtrl = GetInst("MiniUIManager"):GetCtrl("techtreepage")
	if not techTreeCtrl then
		GetInst("MiniUIManager"):OpenUI("techtreepage", "miniui/module/soclogin", "techtreepageAutoGen",{disableOperateUI = false})
		GetInst("MiniUIManager"):HideUI("techtreepageAutoGen")
	end

	MiniLogWarning("EnterWorld researchpage start -----")
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/soclogin"}, "researchpageAutoGen")
	MiniLogWarning("EnterWorld researchpage add package -----")
	GetInst("MiniUIManager"):OpenUI("researchpage", "miniui/miniworld/soclogin", "researchpageAutoGen",{disableOperateUI = false})
	MiniLogWarning("EnterWorld researchpage open ui -----")
	GetInst("MiniUIManager"):HideUI("researchpageAutoGen")

	MiniLogWarning("EnterWorld researchpage end -----")
	SetCurIntanceGoal(mapid)
	getglobal("CharacterActionBtn"):Show()
	--刷新表情数据
	UpdataActionDate()
	if showActorInviteReddot() then 
		getglobal("CharacterActionRedTag"):Show()
	else
		getglobal("CharacterActionRedTag"):Hide()
	end 
	lastTimeCombineTransform = 0
	--租赁服
	--RentPermitCtrl:ShowRentNoticePopup()
	AvatarSummonIndex = 0

	MobileTipsShowed = false	--首次进入地图，重置免流量显示状态
	--音乐方块进入地图切换地图等操作
	if AccountManager then
		local isRoomOwner = IsRoomOwner()
		if AccountManager:getMultiPlayer() == 0 or isRoomOwner  then --主机和单机
			if CurMainPlayer then
				if DealMusicClubIns then
					if DealMusicClubIns.updateAreaId then
						DealMusicClubIns:updateAreaId(mapid);
						--mapid
					end
				end
			end
		else --客机
			if MusicClubSyncIns then
				MusicClubSyncIns:ReqAreaPos(GetMyUin(),mapid);
			end
		end
	end
	
	-- 进入地图记录时间
	local cInterface = GetInst("CreationCenterInterface")
	if cInterface then 
		local wdesc = AccountManager:getCurWorldDesc()
		cInterface:EnterDvpModeTime(wdesc)
	end 
	GetInst("TriggerMapInteractiveInterFace"):InGame()
	GetInst("NewDeveloperStoreInterface"):InGame()
	GetInst("GameTimeLenthReport"):InGame()
	GetInst("HideUnmoderatedTextManger"):InGame()
	if GetInst("BestPartnerManager") then
		GetInst("BestPartnerManager"):InGame()
	end
	if isAbroadEvn() then
		-- 海外差异化处理
	else
		-- 进入地图打开聊天框
		GetInst("ChatHelper"):OpenChatView()
	end
	GetInst("NewDeveloperStoreInterface"):InGame()
	if GetInst("GameVoiceManage") then
		 GetInst("GameVoiceManage"):EnterGame()
	end
end

function SetMainUIState()
	--这里偶现报nil加保护
	if not CurMainPlayer then return end
	local ride = CurMainPlayer:getRidingHorse();
	local vehicle = CurMainPlayer:getDrivingVehicle();
	local mountType = CurMainPlayer:getMountType()
	if CurWorld:isCreativeMode() then	--创造模式
		getglobal("PlayMainFrameSneak"):Hide();				--潜行
		PlayerHPBar_ShowOrHide(false);				--血条
		--getglobal("PlayerStrengthBar"):Hide();				--体力
		HpBarFrame_ShowStBar(false)
		--PlayerHUBar_ShowOrHide(false)
		PlayerHungerBar_ShowOrHide(false);				--饥饿度
		-- getglobal("PlayerExpBar"):Hide();				--经验条
		LevelExpBar_ShowOrHide(false);
		getglobal("GongNengFrameRuleSetGNBtn"):Hide();			--玩家编辑设置
		getglobal("GongNengFramePluginLibBtn"):Hide();			--插件库

		if mountType > 0 then			--坐骑、矿车、床
			getglobal("PlayMainFrameFly"):Hide();			--飞行按钮
			if mountType~=MOUNT_DRIVE then
				if mountType == 2 --[[and LuaInterface:shouldUseNewHpRule()--]] then
					ShowBedBtn(true);
				else
					ShowRideBtn(true)	--上下坐骑按钮
				end		
			end
		else
			--if LuaInterface:shouldUseNewHpRule() then
			ShowBedBtn(false);
			--end
			ShowRideBtn(false)			--上下坐骑按钮
			if CurMainPlayer:getOPWay() == 0 then
				if GetClientInfo():isMobile() then
				   getglobal("PlayMainFrameFly"):Show();			--飞行按钮
			    end
			else
				getglobal("PlayMainFrameFly"):Hide();
			end
		end
		if ride ~= nil then						--骑着坐骑
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
			else
				RideHPBar_ShowOrHide(true);			--坐骑血条
				PlayMain.RideHP:reset();
				if ride:hasHorseSkill(HORSE_SKILL_MUTATE_FLY) then    --飞鸡坐骑
					getglobal("ChickenEnergyFrame"):Show();
				end
			end
		else
			RideHPBar_ShowOrHide(false);			--坐骑血条
			getglobal("ChickenEnergyFrame"):Hide();     --小鸡坐骑能量条
		end
	elseif CurWorld:isGameMakerMode() then	--玩家编辑模式
		getglobal("PlayMainFrameSneak"):Hide();				--潜行
		PlayerHPBar_ShowOrHide(false);				--血条
		--getglobal("PlayerStrengthBar"):Hide();				--体力
		HpBarFrame_ShowStBar(false)
		--PlayerHUBar_ShowOrHide(false)
		PlayerHungerBar_ShowOrHide(false);				--饥饿度
		-- getglobal("PlayerExpBar"):Hide();				--经验条
		LevelExpBar_ShowOrHide(false);
		local wdesc = AccountManager:getCurWorldDesc();
		if AccountManager:getMultiPlayer() ~= 0 or (wdesc and wdesc.realowneruin ~= AccountManager:getUin()) then --联机或者下载的地图
			getglobal("GongNengFrameRuleSetGNBtn"):Hide();		--玩家编辑设置
			getglobal("GongNengFramePluginLibBtn"):Hide();			--插件库
		else
			if not (UGCModeMgr and UGCModeMgr:IsUGCMode()) then
				getglobal("GongNengFrameRuleSetGNBtn"):Show();
				if EnableDeveloper then
					getglobal("GongNengFramePluginLibBtn"):Show();			--插件库
				end
			else
				getglobal("GongNengFrameRuleSetGNBtn"):Hide();
				getglobal("GongNengFramePluginLibBtn"):Hide();			--插件库
			end

			--LLDO:红点,editmode
			local retTag = getglobal("GongNengFrameRuleSetGNBtnRedTag");
            if not AccountManager:getNoviceGuideState("editmode") then
                retTag:Show();
            else
                retTag:Hide();
            end
		end
		if GetClientInfo():isMobile() then
			if mountType > 0 then       			--坐骑、矿车、床
				if mountType ~= MOUNT_DRIVE then
					getglobal("PlayMainFrameSneak"):Hide();--潜行按钮
					if mountType == 2 --[[and LuaInterface:shouldUseNewHpRule()--]] then--床并且是冒险模式
						ShowBedBtn(true);
					else
						ShowRideBtn(true)	--上下坐骑按钮
					end			
				end
			else
				ShowBedBtn(false);
				ShowRideBtn(false)			--上下坐骑按钮
				if CurMainPlayer:getOPWay() == 0 then
					getglobal("PlayMainFrameFly"):Show();			--飞行按钮
				else
					getglobal("PlayMainFrameFly"):Hide();
				end
			end
		end

		if ride ~= nil then						--骑着坐骑
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
			else
				PlayMain.RideHP:reset();
				RideHPBar_ShowOrHide(true);			--坐骑血条
				if ride:hasHorseSkill(HORSE_SKILL_MUTATE_FLY) then    --飞鸡坐骑
					getglobal("ChickenEnergyFrame"):Show();
				end
			end
		elseif vehicle ~= nil then
			--RideHPBar_ShowOrHide(true);			--坐骑血条
			--local hp = math.ceil(vehicle:getLifeCurrent());
			--local maxHP = vehicle:getLifeLimit()
			--getglobal("RideHPBarCur"):SetWidth(266*hp/maxHP);
			--getglobal("RideHPBarRatio"):SetText(hp.."/"..maxHP);
		else
			RideHPBar_ShowOrHide(false);			--坐骑血条
			getglobal("ChickenEnergyFrame"):Hide();     --小鸡坐骑能量条
		end
	else
		getglobal("PlayMainFrameFly"):Hide();				--飞行按钮
		getglobal("GongNengFrameRuleSetGNBtn"):Hide();			--玩家编辑设置
		getglobal("GongNengFramePluginLibBtn"):Hide();			--插件库
		-- 饥饿值体力值新标志位，用于支持两种模式都不显示的情况
		local sfFlag = MainPlayerAttrib:strengthFoodShowState()
		if sfFlag > 0 then
			if MainPlayerAttrib:useCompatibleStrength() then
				if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
					--getglobal("PlayerStrengthBar"):Hide();				--体力值
					HpBarFrame_ShowStBar(false)
				elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
					--getglobal("PlayerStrengthBar"):Hide();				--体力值
					HpBarFrame_ShowStBar(false)
				else
					--getglobal("PlayerStrengthBar"):Show();
					HpBarFrame_ShowStBar(true)
				end
				PlayerHungerBar_ShowOrHide(false);
			else
				if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
					PlayerHungerBar_ShowOrHide(false);				--饥饿度
				elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HUNGER) then --xyang自定义UI
					PlayerHungerBar_ShowOrHide(false);
				else
					PlayerHungerBar_ShowOrHide(true);
				end
				--getglobal("PlayerStrengthBar"):Hide();
				HpBarFrame_ShowStBar(false)
			end
		else
			--getglobal("PlayerStrengthBar"):Hide();
			HpBarFrame_ShowStBar(false)
			PlayerHungerBar_ShowOrHide(false);
		end
		if GetClientInfo():isMobile() then
			if mountType > 0 then	--坐骑、矿车、床
				if mountType ~= MOUNT_DRIVE then
					getglobal("PlayMainFrameSneak"):Hide();--潜行按钮
					if mountType == 2 --[[and LuaInterface:shouldUseNewHpRule()--]] then--床并且是冒险模式
						ShowBedBtn(true);
					else
						ShowRideBtn(true)	--上下坐骑按钮
					end			
				end
			else
				ShowBedBtn(false);
				ShowRideBtn(false)			--上下坐骑按钮
				if not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SNEAK) or not CheckPlayerActionState(ENABLE_SNEAK) then
					getglobal("PlayMainFrameSneak"):Hide();		--潜行按钮
				else
					getglobal("PlayMainFrameSneak"):Show();
				end
			end
		end

		if ride ~= nil then						--骑着坐骑
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
			else
				RideHPBar_ShowOrHide(true);			--坐骑血条
				PlayMain.RideHP:reset();

				PlayerHPBar_ShowOrHide(false);			--人物血条
				if ride:hasHorseSkill(HORSE_SKILL_MUTATE_FLY) then    --飞鸡坐骑
					getglobal("ChickenEnergyFrame"):Show();
				end
			end
			--getglobal("PlayerStrengthBar"):Hide();
			HpBarFrame_ShowStBar(false)
			--PlayerHUBar_ShowOrHide(false)
            PlayerHungerBar_ShowOrHide(false);
		else
			RideHPBar_ShowOrHide(false);			--坐骑血条
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
				PlayerHPBar_ShowOrHide(false);			--人物血条
			elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
				PlayerHPBar_ShowOrHide(false);
			else
				PlayerHPBar_ShowOrHide(true);			--人物血条
			end
			getglobal("ChickenEnergyFrame"):Hide();     --小鸡坐骑能量条
		end

		local starbkg1 = getglobal("PlayerExpBarStarBkg1");

		if CurMainPlayer ~= nil and CurMainPlayer:isInSpectatorMode() then
			starbkg1:Hide();
			-- getglobal("PlayerExpBar"):Hide();				--经验条
			LevelExpBar_ShowOrHide(false);
		else
			starbkg1:Show();
			-- getglobal("PlayerExpBar"):Show();				--经验条
			if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.EXP) then --xyang自定义UI
				local isOpenLevelMode = WorldMgr:canOpenLevelMode()
				-- 无经验模式，不显示星星条
				if not isOpenLevelMode then
					LevelExpBar_ShowOrHide(false);
				else
					LevelExpBar_ShowOrHide(true);
				end
			end
		end

		if CurWorld:isCreateRunMode() then
			starbkg1:SetTexUV("juese_xingxing03.png");
		elseif CurWorld:isExtremityMode() then
			starbkg1:SetTexUV("juese_xingxing02.png");
		elseif CurWorld:isFreeMode() then
			starbkg1:SetTexUV("juese_xingxing06.png");
		elseif CurWorld:isGameMakerRunMode() then
			starbkg1:SetTexUV("juese_xingxing04.png");
		elseif CurWorld:isSurviveMode() then
			starbkg1:SetTexUV("juese_xingxing05.png");
		elseif CurWorld:isCreativeMode() or CurWorld:isGameMakerMode() then
			starbkg1:Hide();
		end
	end

	if CurMainPlayer:getOPWay() == 1 or CurMainPlayer:getOPWay() == 3 then      --球模式下，下坐骑、潜行按钮隐藏
		ShowRideBtn(false)
		ShowBedBtn(false);
		getglobal("PlayMainFrameSneak"):Hide();
	end

	GongNengFrame_OnBtnVisibleChange();
	

end

function SetAttriVal()
	--血量
	--local lifeNum = CurMainPlayer:getLeftLifeNum();
	--if lifeNum >= 0 then
	--	getglobal("PlayerHPBarLifeNum"):SetText(CurMainPlayer:getLeftLifeNum());
	--	if(GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--		GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):setHp(CurMainPlayer:getLeftLifeNum())
	--	end
	--else
	--	if(GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--		GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):setHp(0)
	--	end
	--	getglobal("PlayerHPBarLifeNum"):SetText("");
	--end
	--getglobal("PlayerHPBar"):SetCurValue(math.ceil(MainPlayerAttrib:getHP())/MainPlayerAttrib:getMaxHP(), true)
	--CurPlayerHp = math.ceil(MainPlayerAttrib:getHP());
	--饥饿度
	--getglobal("PlayerHungerBar"):SetCurValue(MainPlayerAttrib:getFoodLevel()/20, true);
	--CurPlayerFoodLevel = MainPlayerAttrib:getFoodLevel();
	--CurPlayerFoodSatLevel = MainPlayerAttrib:getFoodSatLevel();
	--经验值
	CurPlayerExpVal = MainPlayerAttrib:getExp();
	local starNum = math.floor(CurPlayerExpVal/EXP_STAR_RATIO);
	if starNum < 1000 then
		getglobal("PlayerExpBarStarText"):SetText(starNum);
	else
		getglobal("PlayerExpBarStarText"):SetText("999+");
	end
	local expBarVal = (CurPlayerExpVal - starNum * EXP_STAR_RATIO) / EXP_STAR_RATIO ;
	getglobal("PlayerExpBarExp"):SetWidth(500*expBarVal);

	SetRoleFrameStarNum(expBarVal, starNum);
	--PlayMain.Exp:onSetAttribVal();
	SetLevelBar(false);
end

function SetGVoiceBtnState()
	local isJoinRoom = GYouMeVoiceMgr:isJoinRoom()
	local YouMeVocieEnable = GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):YouMeVocieCanEnable()
	if isAbroadEvn() then
		YouMeVocieEnable =  GYouMeVoiceMgr:isJoinRoom() and GetChannelConfig():YouMeVocieEnable() and not RoomInteractiveData:IsSocialHallRoom()
	end
	if GYouMeVoiceMgr and isJoinRoom and YouMeVocieEnable then
		local btnInited = false
		if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
			GetInst("TeamVocieManage"):TeamButtonInit();
			btnInited = true

			if isAbroadEvn() then
				getglobal("SpeakerSwitchBtn"):Show();
			end
		end
		if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MICRO)  then--xyang自定义UI
			if not btnInited then
				GetInst("TeamVocieManage"):TeamButtonInit();
			end
			if isAbroadEvn() then
				getglobal("MicSwitchBtn"):Show();
			end
		else
			getglobal("MicSwitchBtn"):Hide();
		end

		if isAbroadEvn() then
			if GetIWorldConfig():getGameData("micswitch") == 0 then
				--关闭麦克风
				local ret = YouMeVoiceMgr:setMicrophoneMute(true);
				if ret == -2 then		--国战语音 没有麦克风权限不能打开麦克风
					OnSetMicrophoneMute(YOUME_EVENT_LOCAL_MIC_OFF)
				end
			else
				--打开麦克风
				local ret = YouMeVoiceMgr:setMicrophoneMute(false);
				if ret == -2 then		--国战语音 没有麦克风权限不能打开麦克风
					OnSetMicrophoneMute(YOUME_EVENT_LOCAL_MIC_OFF)
				end
			end
			--设置扬声器
			if GetIWorldConfig():getGameData("speakerswitch") == 0 then
				--关闭扬声器
				print("SetGVoiceBtnState 关闭扬声器")
				YouMeVoiceMgr:setSpeakerMute(true);
			else
				--打开扬声器
				print("SetGVoiceBtnState 打开扬声器")
				YouMeVoiceMgr:setSpeakerMute(false);
			end
		end

		--引导
		if not AccountManager:getNoviceGuideState("gvoiceguide") then
			GVoiceGuideTime = 10;
			if GetClientInfo():isPC() then
				getglobal("GVoiceGuideText"):SetText(GetS(3793));
			else
				getglobal("GVoiceGuideText"):SetText(GetS(3794));
			end
			getglobal("GVoiceGuide"):Show();
		end
	else
		getglobal("MicSwitchBtn"):Hide();
		getglobal("SpeakerSwitchBtn"):Hide();
	end
end

function HideUI2NewbieWorld()
	local lv = AccountManager:getCurGuideLevel();
	local step = AccountManager:getCurGuideStep();

	if CurWorld and CurWorld:getOWID() == NewbieWorldId then
		if lv == 1 and step == 23 then
			getglobal("PlayShortcut"):Hide();
			getglobal("PlayMainFrameBackpack"):Hide();
		else
			getglobal("PlayShortcut"):Hide();
			getglobal("PlayMainFrameBackpack"):Hide();
		end
	else
		getglobal("PlayShortcut"):Hide();
		getglobal("PlayMainFrameBackpack"):Hide();
	end

	if AccountManager:getCurNoviceGuideTask() < 17 then
		PlayerHPBar_ShowOrHide(false);
	else
		if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
			PlayerHPBar_ShowOrHide(false);
		elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.HPBAR) then --xyang自定义UI
			PlayerHPBar_ShowOrHide(false);
		else
			PlayerHPBar_ShowOrHide(true);
		end
	end
	-- getglobal("PlayerExpBar"):Hide();
	LevelExpBar_ShowOrHide(false);
	--getglobal("PlayerStrengthBar"):Hide();
	HpBarFrame_ShowStBar(false)
	--PlayerHUBar_ShowOrHide(false)
	PlayerHungerBar_ShowOrHide(false);
	getglobal("PlayerOxygenBar"):Hide();
	getglobal("TaskTrackFrame"):Hide();
	getglobal("PlayMainFrameSneak"):Hide();
	getglobal("PlayMainFrameFly"):Hide();
	ShowRideBtn(false)
	ShowBedBtn(false);
	RideHPBar_ShowOrHide(false);
	getglobal("Compass"):Hide();
	getglobal("ArchiveGradeBtn"):Hide();
	getglobal("PlayAchievementBtn"):Hide();
	getglobal("AdventureNoteBtn"):Hide();
	getglobal("EditorBackBtn"):Hide();
	GongNengFrame_SetVipBtnForceHide(true);

	getglobal("AccRideCallBtn"):Hide();
	SetAccSummonBtnVisible(false);
	getglobal("MultiPlayerInfo"):Hide();
	getglobal("MultiChatBtn"):Hide();
	getglobal("MicSwitchBtn"):Hide();
	getglobal("SpeakerSwitchBtn"):Hide();
	getglobal("ReceiveVoiceBtn"):Hide();
	getglobal("GVoiceJoinRoomBtn"):Hide();

	-- if lv == 1 and step == 23 then
	-- 	getglobal("PlayMainFrameBackpack"):Show();
	-- else
	-- 	getglobal("PlayMainFrameBackpack"):Hide();
	-- end

	getglobal("BattleBtn"):Hide();

	if GetClientInfo():isPC() then
		getglobal("PlayMainFrameGuideSkip"):Hide();
		getglobal("PlayMainFrameGuideSkipPc"):Show();
	else
		getglobal("PlayMainFrameGuideSkip"):Show();
		getglobal("PlayMainFrameGuideSkipPc"):Hide();
	end

	getglobal("PcGuideKeyTips"):Hide();
	getglobal("GongNengFrame"):Hide();
end

--@soc2024
function SetSocUI()
	--语音
	getglobal("MicSwitchBtn"):Hide();
	--成就
	getglobal("PlayAchievementBtn"):Hide()
	--扬声器
	getglobal("SpeakerSwitchBtn"):Hide()
	--语音加入房间
	getglobal("GVoiceJoinRoomBtn"):Hide()
	getglobal("AccChangeColorBtn"):Hide()
	--编辑器
	getglobal("EditorBackBtn"):Hide()
	getglobal("GasInfo"):Hide()

    --游戏设置
    getglobal("GongNengFrameSettingBtn"):Hide()
    --拍照按钮
    getglobal("GongNengFrameOpenCameraModeBtn"):Hide()
    --邀请朋友
    getglobal("GongNengFrameInvite"):Hide()
    --红包
    getglobal("GongNengFrameOpenRedPocket"):Hide()
    --聊天
    getglobal("MultiChatBtn"):Hide()
        --菜单箭头
    getglobal("GongNengFrameMenuArrow"):Hide()
    --邀请窗口
    getglobal("InteractiveBtn"):Hide()
  
	--任务书?
	getglobal("ArchiveGradeBtn"):Hide();	        
	--星星的进度
	getglobal("PlayerExpBar"):Hide();				--经验条
	getglobal("PlayerExpBarStar"):Hide();
	getglobal("RoleFrameStarBkg"):Hide();
	getglobal("RoleFrameStarNum"):Hide()
	getglobal("RoleFrameStarNumBkg"):Hide()
	getglobal("RoleFrameStarNumPro"):Hide()
	--任务书
	getglobal("AdventureNoteBtn"):Hide()

	--坐骑
	getglobal("AccRideCallBtn"):Hide()
	ShowRideBtn(false)
	--getglobal("AccRideChangeBtn"):Hide();
	--getglobal("TaskTrackFrame"):Hide();

	GetInst("MiniUIManager"):HideUI("TaskTrackCtrl")
	--local GuideUIMgr =  GetInst("ForceGuideUIMgr")
    --GuideUIMgr:OnGuideBegin()
    --GuideUIMgr:GuideHideAllUI()
	--中间的比分
	getglobal("BattleBtn"):Hide();
	--tool 左边的表情
	getglobal("CharacterActionBtn"):Hide();
	--tool 右边的背包
	getglobal("PlayMainFrameBackpack"):Hide();
	--隐藏坐骑
	getglobal("AccRideCallBtn"):Hide();
end

function PlayMainFrameUIShow()
	if isEducationalVersion then
		getglobal("GongNengFrameMenuArrow"):Hide();--星工场需要主动隐藏
	end
	if not ClientCurGame then
		return;
	end

	if ClientCurGame.showOperateUI then
		ClientCurGame:showOperateUI(true);
	end

	GetInst("MiniUIManager"):ShowUI("playermainAutoGen")

	--改为判断手持道具决定是否要隐藏
	if CurMainPlayer then
		local itemId = CurMainPlayer:getCurToolID()
		local newGun = CurMainPlayer:GetGunHoldState() == 2;
		if itemId == ITEM_COLORED_GUN or itemId == ITEM_COLORED_EGG or itemId == ITEM_COLORED_EGG_SMALL or itemId == ITEM_COLOR_BRUSH or newGun then

		elseif CurWorld and  CurWorld:isGodMode() and IsDyeableBlockLua(itemId) then
		else
			getglobal("ColoreSelectedFrame"):Hide()
			getglobal("GunMagazine"):Hide()
			GetInst("UgcMsgHandler"):GetToolsBar():dispatcher(SceneEditorUIDef.common.hide_autoaiming)
		end
	end

	getglobal("VoiceTipsFrame"):Hide();
	getglobal("PlayMainFrameFlyDown"):Hide();
	getglobal("PlayMainFrameFlyUp"):Hide();
	getglobal("TeamMicSwitchBtn"):Hide();
	getglobal("TeamSpeakerSwitchBtn"):Hide();
	if CurWorld and (CurWorld:getOWID() == NewbieWorldId or CurWorld:getOWID() == NewbieWorldId2) then
		HideUI2NewbieWorld();
		--埋点，进入教学地图 设备码,是否首次进入教学地地图,用户类型,语言
		-- statisticsGameEventNew(958,GetClientInfo():getDeviceID(),(IsFirstEnterNoviceGuide and not enterGuideAgain) and 1 or 2,
		-- true and (GetClientInfo():isFirstEnterGame() and 1 or 2),tostring(get_game_lang()))		
		StatisticsTools:send(true, true)
	else
		getglobal("PlayMainFrameGuideSkip"):Hide();
		getglobal("PlayMainFrameGuideSkipPc"):Hide();
		--getglobal("PlayShortcut"):Show();		--快捷栏
		--getglobal("PlayMainFrameBackpack"):Show();	--背包
		if IsInHomeLandMap and IsInHomeLandMap() then --家园地图不显示
			PixelMapInterface:HideCompass()
			if HideChangeViewBtn then 
				HideChangeViewBtn() 
			end
		elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
			PixelMapInterface:HideCompass()
			if HideChangeViewBtn then 
				HideChangeViewBtn() 
			end
		else
			if LuaInterface:getApiId() ~= 999 then
				PixelMapInterface:ShowMainMiniMap()
				--PixelMapInterface:ShowMainMiniMap() 会改OperateUI
				--ClientCurGame:setOperateUI(true)
			else
				PixelMapInterface:ShowCompass()			--小地图
			end
			if isAbroadEvn() then
				ShowChangeViewBtn()
			else
				HideChangeViewBtn()
			end
		end

		if CurMainPlayer ~= nil and CurMainPlayer:isInSpectatorMode() then
			getglobal("PlayShortcut"):Hide();		--快捷栏
			getglobal("PlayMainFrameBackpack"):Hide();	--背包
		elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SHORTCUT) then--xyang自定义UI
			getglobal("PlayShortcut"):Hide();		--快捷栏
			getglobal("PlayMainFrameBackpack"):Hide();	--背包
		else
			if not IsUGCEditing() then
				getglobal("PlayShortcut"):Hide();		--快捷栏
				getglobal("PlayMainFrameBackpack"):Hide();	--背包
			end
		end

		getglobal("GongNengFrameStoreGNBtn"):Show();	--商店
		if isAbroadEvn() then	
			if getkv("Lobby_ReqConfig") or GetInst("MiniUIGuideMgr"):CheckGuideIsFinished() then 
				getglobal("GongNengFrameActivityGNBtn"):Show();	--活动
			else
				getglobal("GongNengFrameActivityGNBtn"):Hide();	--活动
			end
		else
			getglobal("GongNengFrameActivityGNBtn"):Show();	--活动
		end
		getglobal("GongNengFrameFriendBtn"):Show();
		
        UpdatePaintChangeBtn()
		--开启追踪主线任务，显示追踪任务面板
		if IsOpenTrack then
			local curTrack = AchievementMgr:getCurTrackID();
			if curTrack > 0 then
				AchievementMgr:setCurTrackID(curTrack);
			else
				local curMainTaskId = GetCurMainTaskId();
				AchievementMgr:setCurTrackID(curMainTaskId);
			end
			UpdateTaskTrackFrame();
		else
			getglobal("TaskTrackFrame"):Hide();
		end

		SetMainUIState();
		getglobal("MicSwitchBtn"):Hide();
		getglobal("SpeakerSwitchBtn"):Hide()
		getglobal("GVoiceJoinRoomBtn"):Hide();
		--房间
		if AccountManager:getMultiPlayer() ~= 0 then
			--getglobal("MultiPlayerInfo"):Show();
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
				InteractiveBtn_ShowOrHide(false);
				getglobal("MultiChatBtn"):Hide();
				GetInst("MiniUIManager"):CloseUI("chat_viewAutoGen")
			elseif RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then  --社交大厅不显示
				InteractiveBtn_ShowOrHide(false);
				getglobal("MultiChatBtn"):Hide();
				GetInst("MiniUIManager"):CloseUI("chat_viewAutoGen")
			else
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.INVITE) then--xyang自定义UI
					InteractiveBtn_ShowOrHide(true);
				else
					InteractiveBtn_ShowOrHide(false);
				end
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.CHAT) then--xyang自定义UI
					--getglobal("MultiChatBtn"):Show();
				else
					getglobal("MultiChatBtn"):Hide();
				end
			end
			if PlayAchievementBtnCanShow() then
				getglobal("PlayAchievementBtn"):Show();
			else
				getglobal("PlayAchievementBtn"):Hide();
			end
			getglobal("ArchiveGradeBtn"):Hide();
			getglobal("ArchiveGradeFinishBtn"):Hide();
			if PlayAdventureNoteBtnCanShow() and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
				getglobal("AdventureNoteBtn"):Hide();
			else
				getglobal("AdventureNoteBtn"):Hide();
			end
			if IsInHomeLandMap and IsInHomeLandMap() then
					--家园不做显示
			--elseif RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then 
				--社交大厅不显示
			elseif isAbroadEvn() and GYouMeVoiceMgr and GYouMeVoiceMgr:isInit() and not GYouMeVoiceMgr:isJoinRoom() and GetChannelConfig():YouMeVocieEnable() and not RoomInteractiveData:IsSocialHallRoom() then
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
					getglobal("GVoiceJoinRoomBtn"):Hide();
				end				
			elseif isAbroadEvn() and GYouMeVoiceMgr and GYouMeVoiceMgr:isJoinRoom() and not RoomInteractiveData:IsSocialHallRoom() then
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
					getglobal("SpeakerSwitchBtn"):Hide();
				end	
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MICRO) then--xyang自定义UI
					getglobal("MicSwitchBtn"):Hide();
				end		
			elseif GYouMeVoiceMgr and GYouMeVoiceMgr:isInit() and not GYouMeVoiceMgr:isJoinRoom() and ( GetInst("GameVoiceManage") and GetInst("GameVoiceManage"):CheckAutoOpenVoice() ) then
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
					
					--退出组队房间
					getglobal("GVoiceJoinRoomBtn"):Hide();

					if not GVoiceJoinRoomBtnShown then
						GVoiceJoinRoomBtnShown = true
					end
				end
			elseif GYouMeVoiceMgr and GYouMeVoiceMgr:isJoinRoom() then
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
					GetInst("TeamVocieManage"):TeamButtonInit()
				end
			end
		else
			InteractiveBtn_ShowOrHide(false);
			getglobal("MultiPlayerInfo"):Hide();
			if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
				getglobal("MultiChatBtn"):Hide();
			elseif RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then --社交大厅不显示
				InteractiveBtn_ShowOrHide(false);
				getglobal("MultiChatBtn"):Hide();
				GetInst("MiniUIManager"):CloseUI("chat_viewAutoGen")
			elseif not ifNetworkStateOK() then
				getglobal("MultiChatBtn"):Hide();--网络不可用不显示
			else
				if UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.CHAT) then
					getglobal("MultiChatBtn"):Hide();
				else
					getglobal("MultiChatBtn"):Hide();
				end
			end
			getglobal("ReceiveVoiceBtn"):Hide();
			if PlayAchievementBtnCanShow() then
				getglobal("PlayAchievementBtn"):Hide();
			else
				getglobal("PlayAchievementBtn"):Hide();
			end
			if PlayAdventureNoteBtnCanShow() and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MAP) then--xyang自定义UI
				getglobal("AdventureNoteBtn"):Hide();
			else
				getglobal("AdventureNoteBtn"):Hide();
			end
			getglobal("GVoiceJoinRoomBtn"):Hide();
		end

		if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
			getglobal("AccRideCallBtn"):SetPoint("bottomright", "PlayMainFrame", "bottomright", -9, -246)
		end
		--坐骑召唤
		if HasAnyRideOrPet() and not isEducationalVersion and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
			getglobal("AccRideCallBtn"):Hide();
		else
			getglobal("AccRideCallBtn"):Hide();
		end
		if GetClientInfo():isMobile() then
			if CurMainPlayer:isFlying() then
				if getglobal("AccRideCallBtn"):IsShown() then
					getglobal("AccRideCallBtn"):Hide();
				end
			else
				if not getglobal("AccRideCallBtn"):IsShown() and not isEducationalVersion and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
					if isAbroadEvn() then
						if IsNeedShowAccRideCallBtn() then
							getglobal("AccRideCallBtn"):Hide();
						end
					else
						getglobal("AccRideCallBtn"):Hide();
					end
				end
			end
		end

		--变形按钮
		if getglobal("AccRideCallBtn"):IsShown() then
			getglobal("AccRideChangeBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
		else
			getglobal("AccRideChangeBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
		end

		-- local skinId = CurMainPlayer:getSkinID()
		local skinId = GetMyInMapCurSkinId()
		local skinDef = RoleSkinCsv:get(skinId)
		if skinDef and skinDef["ChangeType"] > 0 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
			SetAccRideChangeBtnVisible(true)
			local t = getkv("first_use_change_skin") or  {}
			local showEffect = true
			for i = 1, #t do
				if t[i] == skinId then
					showEffect = false
				end
			end
			if showEffect then
				getglobal("AccRideChangeBtnEffect"):SetUVAnimation(100, true)
				table.insert(t, skinId)
				setkv("first_use_change_skin", t)
			else
				getglobal("AccRideChangeBtnEffect"):Hide()
			end

		else
			SetAccRideChangeBtnVisible(false)
		end

		if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then
			SetAccSummonBtnVisible(true)
		else
			SetAccSummonBtnVisible(false)
		end

		if getglobal("AccRideChangeBtn"):IsShown() then--如果变形按钮显示，设置召唤按钮到变形按钮左边
			getglobal("AccSummonBtn"):SetPoint("right", "AccRideChangeBtn", "left", -3, 0)
		else
			if getglobal("AccRideCallBtn"):IsShown() then
				getglobal("AccSummonBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
			else
				getglobal("AccSummonBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
			end
		end

		if CurMainPlayer:isShapeShift() and GetClientInfo():isMobile() and needShowRideAttackBtn(skinId) then --64-红蜘蛛 坐骑飞行 加速按钮无效不显示
			getglobal("AccRideAttackBtn"):Show()
			getglobal("AccRideAttackLeftBtn"):Show()
		else
			getglobal("AccRideAttackBtn"):Hide()
			getglobal("AccRideAttackLeftBtn"):Hide()
		end

		SetAttriVal();
		if IsInHomeLandMap and IsInHomeLandMap() then
			ShowHomeMainUI()
			HomeLandGuideTaskCall("ShowUi", true)
			getglobal("GongNengFrame"):Hide();
		elseif GetInst("MiniUIManager"):IsShown("VisualCodeMainViewAutoGen") then
			getglobal("GongNengFrame"):Hide();
		else
			getglobal("GongNengFrame"):Show();
		end
		--[[--在玩法/编辑模式切换时Frame的状态一直是OnShow
		if not getglobal("GongNengFrame"):IsShown() then
			getglobal("GongNengFrame"):Show();
		end
		--]]

		UpdatePlaymainForIosReview();

		SwitchGongNengFrameMenu(false);

		--玩法模式队伍和比分
		getglobal("BattleBtn"):Hide();
		getglobal("BattlePrepareFrame"):Hide();

		if CurWorld:isGameMakerRunMode() then
			-- 开局介绍
			local showIntrosOrSelect = false
			if ClientCurGame:getGameStage() <= CGAME_STAGE_RUN then
				local newPlayerFlag = true
				if CurMainPlayer and CurMainPlayer.isNewPlayer then
					newPlayerFlag = CurMainPlayer:isNewPlayer()
				end
				local needShowIntro, needShowTeams = false, false;
				local makermgr = WorldMgr:getGameMakerManager();
				if newPlayerFlag and makermgr and makermgr.getNeedShowPrepareFrame then 
					needShowIntro = makermgr:getNeedShowPrepareFrame(1)
					needShowTeams = makermgr:getNeedShowPrepareFrame(2)
				end
				if needShowIntro then
					makermgr:setCustomGameStage(CGAME_STAGE_SHOWINTROS);
					showIntrosOrSelect = true
				elseif needShowTeams then
					makermgr:setCustomGameStage(CGAME_STAGE_SELECTTEAM);
					showIntrosOrSelect = true
				end
			end
			
			-- 原版内容
			local is_game_turn_based = showIntrosOrSelect
			if not showIntrosOrSelect then
				if ClientCurGame:getGameStage() < CGAME_STAGE_RUN and AccountManager:getMultiPlayer() ~= 0 then --开始游戏前
					getglobal("BattlePrepareFrame"):Show();
					is_game_turn_based = true
				elseif IsShowBattleBtn() and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.SCORE) then--xyang自定义UI
					--getglobal("BattleBtn"):Show();
					SetBattleBtn();
					is_game_turn_based = false
				end
			end

			if is_game_turn_based then
				if OneKeyCar then
					OneKeyCar:V2_SetGameTurnBased(true)
				end
			end
		end

		--pc键位引导
		if GetClientInfo():isPC() and not AccountManager:getNoviceGuideState("guidekey") and not isEducationalVersion then
			getglobal("PcGuideKeyTips"):Show();
		else
			getglobal("PcGuideKeyTips"):Hide();
		end

		if isAbroadEvn() and RentPermitCtrl and RentPermitCtrl.rentRoomInfo and RentPermitCtrl.rentRoomInfo.isCloudArchive then
			-- 云存档一定是隐藏的
			getglobal("OpenGame"):Hide();
			getglobal("BattlePrepareFrameStartGame"):Hide();
		else
			if NeedOpenMakerRunGame then
				getglobal("OpenGame"):Show();
			end	
		end

		SpectatorModeChange();
		--喷漆红点检测显示背包红点
		local uin = AccountManager:getUin()
		if getkv("BackPackPaint_Onlick"..uin) then
			getglobal("PlayMainFrameBackpackRedTag"):Hide()
		else
			getglobal("PlayMainFrameBackpackRedTag"):Hide()
		end

	end

	if GetClientInfo():isPC() then
        getglobal("PlayMainFrameFly"):Hide();
        ShowRideBtn(false)
		ShowBedBtn(false);
		--躺床上或者坐骑
		local ride = CurMainPlayer:getRidingHorse()
		if ride ~= nil then
			ShowRideBtn(true)
		end
		if CurMainPlayer.isRestInBed and CurMainPlayer:isRestInBed() then
			ShowBedBtn(true);
		end
	end

	if CurMainPlayer:getMountType() == MOUNT_DRIVE then
		setVehicleUI(true)
	else
		setVehicleUI(false)
	end

	--工具模式界面刷新
	if CurWorld:isGameMakerToolMode() and GetInst("UIManager"):GetCtrl("ToolModeFrame") then
		print("PlayMainFrameUIShow:XXX:");
		GetInst("UIManager"):GetCtrl("ToolModeFrame"):Refresh();
	end
	--教育版不显示坐骑
	if isEducationalVersion then
		getglobal("AccRideCallBtn"):Hide();
	end	
	--跳舞方块内不能显示坐骑按钮和血条
	if GetInst("MiniUIManager"):GetCtrl("clubMain") then
		getglobal("AccRideCallBtn"):Hide();
		SetAccRideChangeBtnVisible(false);
		SetAccSummonBtnVisible(false);
		PlayerHPBar_ShowOrHide(false);
		PlayerHungerBar_ShowOrHide(false);
		if not GetInst("MiniUIManager"):GetCtrl("clubMain").SwicthBack then
			getglobal("PlayShortcut"):Hide();
			getglobal("PlayMainFrameBackpack"):Hide();
			getglobal("PlayerExpBarStar"):Hide();
			getglobal("PlayerExpBar"):Hide();
			getglobal("PhraseLibraryFrameUnFold"):Hide();
		end
		LevelExpBar_ShowOrHide(false);
	end

	--运行模式下的编辑按钮
	if UGCModeMgr and UGCModeMgr:IsRunning() and getglobal("EditorBackBtn") then
		if (CanChangeGameMode() or _isTempMap) and not isInTeam then
			getglobal("EditorBackBtn"):Show();
			local edge = UIFrameMgr:GetScreenEdge()
			local scaleX = UIFrameMgr:GetScreenScaleX() 
			if scaleX == 0 then
				scaleX = 1
			end
			local x = 50 - edge / scaleX
			getglobal("EditorBackBtn"):SetPoint("bottomleft", "PlayMainFrame", "bottomleft", x, -35)
		else
			getglobal("EditorBackBtn"):Hide();
		end
	else
		getglobal("EditorBackBtn"):Hide();
	end
	
	--显示变色按钮
	ShowChangeColorBtn()

	-- 初始化下区域设置状态
	GetInst("MiniClubInterface"):SetIsMiniClub(false)

	GetInst("HideUnmoderatedTextManger"):initData()

	if GetInst("ShareArchiveInterface").StartTryPlayTime then
		GetInst("ShareArchiveInterface"):StartTryPlayTime()
	end

	GetInst("MiniUIManager"):ShowUI("ToolsBarAutoGen")
	if GetInst("UgcMsgHandler"):GetToolsBar() then
		GetInst("UgcMsgHandler"):GetToolsBar():dispatcher(SceneEditorUIDef.common.show_autoaiming)
	end

	RoleSkin_Helper:SetRoleSkinGeniusBuff()

	UpdateMainFucBtn(0);
end

function UpdatePlaymainForIosReview()
	if IsInIosSpecialReview() then
		getglobal("GongNengFrameFriendBtn"):Hide();
		InteractiveBtn_ShowOrHide(false);
		--getglobal("GongNengFrameSetGNBtn"):Show();
		getglobal("GongNengFrameStoreGNBtn"):Hide();
		getglobal("GongNengFrameActivityGNBtn"):Hide();
		getglobal("SetMenuFrameLeftSetFrameCreateRoomBtn"):Hide();
	end
end


function GuideKey_Finish()
	getglobal("PcGuideKeyTips"):Hide();
end

function IsShowBattleBtn()
	if not CurWorld then 
		-- 处理lua报错
		return false 
	end
	local optionId=0;
	local val=0;
	optionId, val = CurWorld:getRuleOptionID(30, optionId, val);		--显示比分和时间的规则选项
	if val == 1 then
		return true;
	else
		return false;
	end
end

function  PlayMainFrameUIHide()
	getglobal("PlayShortcut"):Hide();
	PlayerHPBar_ShowOrHide(false);
	--getglobal("PlayerStrengthBar"):Hide();
	HpBarFrame_ShowStBar(false)
	--PlayerHUBar_ShowOrHide(false)
	-- getglobal("PlayerExpBar"):Hide();
	LevelExpBar_ShowOrHide(false);
	PlayerHungerBar_ShowOrHide(false);
--	getglobal("PlayerOxygenBar"):Hide();
	getglobal("TaskTrackFrame"):Hide();
	getglobal("PlayMainFrameSneak"):Hide();
	getglobal("PlayMainFrameFly"):Hide();
	ShowRideBtn(false)
	ShowBedBtn(false);
	RideHPBar_ShowOrHide(false);
	getglobal("Compass"):Hide();
	getglobal("ArchiveGradeFinishBtn"):Hide();
	getglobal("ArchiveGradeBtn"):Hide();
	getglobal("PlayAchievementBtn"):Hide();
	getglobal("AdventureNoteBtn"):Hide();
	getglobal("EditorBackBtn"):Hide();
	if friendservice.enabled then
		getglobal("GongNengFrameFriendBtn"):Hide();
	else
		InteractiveBtn_ShowOrHide(false);
	end
	getglobal("AccRideCallBtn"):Hide();
	SetAccSummonBtnVisible(false);
	getglobal("MultiPlayerInfo"):Hide();
	getglobal("MultiChatBtn"):Hide();
	getglobal("MicSwitchBtn"):Hide();
	getglobal("SpeakerSwitchBtn"):Hide();
	getglobal("GVoiceJoinRoomBtn"):Hide();
	getglobal("ReceiveVoiceBtn"):Hide();
	getglobal("PlayMainFrameBackpack"):Hide();
	getglobal("BattleBtn"):Hide();
	getglobal("RocketUIFrame"):Hide();
	getglobal("EncryptFrame"):Hide();
	SetAccRideChangeBtnVisible(false);
	getglobal("AccRideAttackBtn"):Hide()
	getglobal("AccRideAttackLeftBtn"):Hide()
    getglobal("GongNengFrameDeveloperStoreBtn"):Hide()
	getglobal("PaintChangeFrame"):Hide()
	GetInst("MiniUIManager"):HideUI("ToolsBarAutoGen")
	HideUseEmitterUI();
end

gPassPortEndTime = -2
gPassPortLastTime = 0
function PlayMainFrame_OnShow()
	print("PlayMainFrame_OnShow:");
	--进入地图时加载脚本
	threadpool:work(function()
		GetInst("LuaRequireMgr"):MapLoadingRequire()
	end)
	--添加联机状态水印
	local WMCtrl = GetInst("UIManager"):GetCtrl("WaterMark")
	if WMCtrl and WMCtrl.UpdateGameTypeStr then
		WMCtrl:UpdateGameTypeStr()
	end

	gPassPortEndTime = -2
	
	GVoiceJoinRoomBtnShown = false --每次show playmain的时候重置状态
	if CurWorld then
		print("function PlayMainFrame_OnShow isAbroadEvn()", isAbroadEvn())
		if isAbroadEvn() then	

			getglobal("TeamSpeakerSwitchBtn"):Hide()
			getglobal("TeamMicSwitchBtn"):Hide()


			print("function PlayMainFrame_OnShow RoomInteractiveData:IsSocialHallRoom()()", RoomInteractiveData:IsSocialHallRoom(), YouMeVoiceMgr:isJoinRoom())

			if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then
				if YouMeVoiceMgr:isJoinRoom() then 
					getglobal("TeamSpeakerSwitchBtn"):Show()
					getglobal("TeamMicSwitchBtn"):Show()
				end
			end
	
			local owid = CurWorld:getOWID()
			if owid then
				setkv(G_V2MapArchiveConstants.LOCAL_KV_KEY.LAST_PLAY_TIME .. tostring(owid), os.time())
				setkv(G_V2MapArchiveConstants.LOCAL_KV_KEY.PLAY_TIME_CHECK_POINT_TIME .. tostring(owid), os.time())
	
				if GetInst("NewMapCommonControl") then
					GetInst("NewMapCommonControl"):StartAddupPlayTime(owid);
				end
			end
		end

		if NewbieWorldId2 == CurWorld:getOWID() then
			if ClientCurGame.showOperateUI then
				ClientCurGame:showOperateUI(true);
			end
			if not (getkv(AccountManager:getUin().."_finishNewGuide") == 1 or IsSkipFromGuideOrFirstMap) and not isEducationalVersion  then
				GetInst("UIManager"):Open("RookieGuide")
				GetInst("UIManager"):GetCtrl("RookieGuide"):showType({type=4})
				if ClientCurGame and not ClientCurGame:isOperateUI() then
					ClientCurGame:setOperateUI(true)
				end
			end
			getglobal("BattlePrepareFrame"):Hide();
		end
		if  not CurWorld:isSurviveMode() or CurWorld:isFreeMode() then
			getglobal("PlayMainFrameBackpackNormal"):SetTexUV("icon_backpack")
			getglobal("PlayMainFrameBackpackPushed"):SetTexUV("icon_backpack")
		else
			getglobal("PlayMainFrameBackpackNormal"):SetTexUV("icon_robot")
			getglobal("PlayMainFrameBackpackPushed"):SetTexUV("icon_robot")
		end
	end

	-- 进入游戏事件上报 by fym
	EnterGameEventReport()
	--MiniBase进入游戏通知
    SandboxLua.eventDispatcher:Emit(nil, "MiniBase_GameLaunchFinish",  MNSandbox.SandboxContext():SetData_Number("code", 0))

	local sceneID = "" 
	if IsRoomOwner() or AccountManager:getMultiPlayer() == 0 then
		--主机
		sceneID = "1003"
	else
		--客机
		sceneID = "1001"
	end
	
	threadpool:work(function()
		-- 2022/03.30 codeby fym 获取地图场景使用到的广告数据
		local reviveAdPositionId, authorUin, mapId = GetInst("DeathFrameManager"):GetReviveAdPositionId()
		if reviveAdPositionId == 105 then
			if ad_data_new.allSenceIdList then
				ad_data_new.getAdInfoBySence(ad_data_new.allSenceIdList.developerMap)
			end
		else
			if ad_data_new.allSenceIdList then
				ad_data_new.getAdInfoBySence(ad_data_new.allSenceIdList.map)
			end
		end
	end)

	ShopInit()

	NeedOpenMakerRunGame = false;
	getglobal("OpenGame"):Hide();
	getglobal("RocketUIFrame"):Hide();

	PlayMainFrameUIShow();
	InitScreenEffect();
	if GetClientInfo():isMobile() == false then
	   getglobal("PlayMainFrameSneak"):Hide();
	else
		local st = getglobal("SetSightingTelescopeBtn")
		local stR = getglobal("SetSightingTelescopeBtnRight")
		local modeRightSightVisible = UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MODRIGHTSIGHTING);
		local rightSightVisible = false;
		if CurMainPlayer:hasSightingTelescope() then
			if CurMainPlayer and CurMainPlayer:GetGunHoldState() == 2 then
				st:Hide()
				rightSightVisible = true;
				if modeRightSightVisible then
					stR:Show()
				end
			else
				st:Show()
				stR:Hide()
				rightSightVisible = false;
			end
		else
			st:Hide()
			stR:Hide()
			rightSightVisible = false;
		end
		if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
			GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("SetSightingTelescopeBtnRight"), rightSightVisible)
		end
	end

	getglobal("BattleCountDownFrame"):Hide();
	getglobal("GasInfo"):Hide();

	--LLDO:初始化玩家profile
	KillInfoFrame_InitProfile();

	if AccountManager:getMultiPlayer() ~= 0 then
		SetAllForbidSpeakerBtnStatus();
	end

	--if CurMainPlayer:getMountType() == MOUNT_DRIVE then
	--	setVehicleUI(true)
	--end
	AccountGameModeClass:UpGameModeUI();
	getglobal("PlayMainFramePassPortCountDownFrame"):Hide()
    getglobal("CSStateNoticeFrame"):Hide()
    
    if IsUIFrameShown("MiniWorksFrame") then
        getglobal("MiniWorksFrame"):Hide()
    end
	if IsUIFrameShown("LobbyFrame") then
        getglobal("LobbyFrame"):Hide()
    end
	if GetInst("QQMusicPlayerManager") then
		GetInst("QQMusicPlayerManager"):InitUI();--Xyang 初始化音乐播放器
	end
	
	if GetInst("MiniClubPlayerManager") and GetInst("MiniClubPlayerManager"):IsOpen() then
		GetInst("MiniClubPlayerManager"):OpenUI()
	end
	if GetInst("QQMusicPlayerManager") then
		GetInst("QQMusicPlayerManager"):OnEnterGame()
	end

	--重置下传送弹窗显示数据
	if GetInst("CloudPortalInterface") then
		GetInst("CloudPortalInterface"):OnEnterGame();
	end

	if GetInst("MatchPartyInterface") then
		GetInst("MatchPartyInterface"):ShowReMatchBtn()
	end

	RoomUIFrame_SetChatBubbleSwitchState()
	
	--请求免流量包开通状态
	ReqMobileDataPackageState()

	ShowToActivityFrame()
	if GetInst("BestPartnerManager") then
		GetInst("BestPartnerManager"):InGameReport();
	end

	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):HideTip();
	end
	
	Update_MusicItem_select()
	
	GetInst("UGCCommon"):UIEnterWorldShowEx()
	if isAbroadEvn() then	
		if Universe_PlayMainFrame_OnShow then
			Universe_PlayMainFrame_OnShow()
		end
	end
	
	-- 秋季活动地图内入口按钮
	local autActInst = GetInst("AutumnActivityInterface")
	if autActInst then 
		autActInst:OnInGameBtnShow()
	end 

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")

	if not playermain_ctrl then
		GetInst("MiniUIManager"):OpenUI("playermain", "miniui/miniworld/soclogin", "playermainAutoGen", {disableOperateUI = true})
		playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	end
	SetSocUI()
	local ugcEditing = UGCModeMgr and UGCModeMgr:IsEditing()
	if ugcEditing then
		--playermain_ctrl:HideUI()
		GetInst("MiniUIManager"):HideUI("playermainAutoGen")
	else 
		--playermain_ctrl:ShowUI()
		GetInst("MiniUIManager"):ShowUI("playermainAutoGen")
		local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
		if playermain_ctrl then
			playermain_ctrl:SetUISleep()
		end
	end
end

function ReqMobileDataPackageState()
	if IsInHomeLandMap and IsInHomeLandMap() then
		--家园模式下不弹出
		return
	end

	--UI每次展示都会检查状态，这里检查是不是进入地图后第一次展示
	if MobileTipsShowed then
		return
	end

	--第一次检查状态后
	MobileTipsShowed = true

	--检查当前网络运营商
	if get_game_env() ~= 1 then
		--不是移动网络都不用弹
		if GetClientInfo():getNetworkState() ~= 2 then return end
	end

	local uin_ = AccountManager:getUin() or get_default_uin()
	local sign_, s2t_, pure_s2t_ = get_login_sign()
	local time_ = os.time()
	local auth_ = gFunc_getmd5(time_ .. sign_ .. uin_)
	local default_param = {
	   uin = uin_,
	   auth = auth_,
	   time = time_,
	   s2t  = pure_s2t_
	}
	local url = ""
	if get_game_env() == 1 then
		url = "https://h5.miniworldplus.com/api/traffic/user/getStatus?"
	else
		url = "https://actapi.mini1.cn/api/flow/user/getStatus?"
	end
	for k, v in pairs(default_param) do
		if k ~= "uin" then
			url = url .. "&" .. k .. "=" .. v
		else
            url = url .. k .. "=" .. v
		end
    end
	url = url ..s2t_

	local _callback = function(retstr)
		local ret = JSON:decode(retstr)
		if ret and ret.code == 200 then
			if ret.data == 0 then
				CheckMobileDataPackageState()
			end
		end
	end

	ns_http.func.rpc_string_raw_ex(url, _callback);
end

function CheckMobileDataPackageState()
	local uid = AccountManager:getUin() or ""
	--检查配置开关是否打开
	if ns_version and ns_version.mobile_data_package.open == 1 and check_apiid_ver_conditions(ns_version.mobile_data_package) then
		if ns_version.mobile_data_package.clear_record == 1 then
			--配置清零开关打开，则清除本地持久化数据
			setkv("mobile_data_day_"..uid,nil)
			setkv("mobile_data_total_"..uid,0)
		end
		local total = getkv("mobile_data_total_"..uid) or 0
		--判断当前弹出次数是否小于配置总数
		if total < ns_version.mobile_data_package.total_show_limit then
			local lastTime,curTime,count
			local isNextDay = false
			if getkv("mobile_data_day_"..uid) then
				lastTime = os.date("%Y.%m.%d", getkv("mobile_data_day_"..uid).time):split('.')--getkv("mobile_data_day")
				count = getkv("mobile_data_day_"..uid).count
			else
				lastTime = {"0","0","0"}
				count = 0
			end
			local curTime = os.date("%Y.%m.%d", AccountManager:getSvrTime()):split('.')
			for i = #curTime, 1, -1 do
				if lastTime[i] ~= curTime[i] then
					isNextDay = true
					count = 0
					break
				end
			end
			--确定隔天
			if isNextDay then
				local param = {}
				param.time = AccountManager:getSvrTime()
				param.count = 1
				setkv("mobile_data_day_"..uid,param)
			else
				if count >= 2 and (count - 2) < ns_version.mobile_data_package.day_show_limit then
					MobileDataPackageShowTips()
					total = total + 1
					setkv("mobile_data_total_"..uid,total)
				end 
				local param = {}
				param.time = AccountManager:getSvrTime()
				param.count = count + 1
				setkv("mobile_data_day_"..uid,param)
			end
		end
	end
end

function MobileDataPackageShowTips()
	local sceneID = "1003" 
	local mapId = G_GetFromMapid()
	local uid = AccountManager:getUin()
	
	GetInst("MiniUIManager"):OpenUI(
		"CommonTips",
		"miniui/miniworld/CommonTips", 
		"CommonTipsAutoGen",
		{titleText = GetS(7000001),btnText = GetS(7000002),cb = function()
			local uiJumpHelper = GetInst("UIJumpHelper")
			if uiJumpHelper then
				GetInst("UIJumpHelper"):Jump(101000,{tab = 7, isRechargeFlow = true})
			else
				g_jump_ui_switch[1004]() 
			end

		end,time = 5})
end

-- 进入游戏事件上报 by fym
function EnterGameEventReport()
	if not isAbroadEvn() then	
		local standby3 = GetInst("GameVoiceManage") and GetInst("GameVoiceManage") :GetGameVocieType();
		local standby4 = GetInst("TeamVocieManage") :GeTeamId();
	end
	if CurWorld then
		-- 房间类型，地图id
		local cid = "0"	
		local stanby1_1, stanby1_2, stanby1_3 = "", "", ""
		-- 房主或者单机模式进入地图显示游戏栏目显示上报 by fym
		if AccountManager:getMultiPlayer() == 0 or IsRoomOwner() then
			if AccountManager:getMultiPlayer() == 0 then
				-- 单机
				stanby1_2 = 1
			else				
				if IsArchiveMapCollaborationMode() then					
					-- 好友协作模式
					stanby1_2 = 3
				else
					-- 普通联机
					stanby1_2 = 2
				end
			end
			local worldInfo = AccountManager:findWorldDesc(CurWorld:getOWID())
			if worldInfo then
				if worldInfo.realowneruin > 1 and worldInfo.owneruin ~= worldInfo.realowneruin then
					cid = worldInfo.fromowid
					stanby1_1 = 2 -- 2：别人地图 
				elseif worldInfo.worldid then
					cid = worldInfo.worldid
					stanby1_1 = 1 -- 1:自己地图
				end

				if worldInfo.worldtype then
					if worldInfo.worldtype == 0 then -- 冒险
						stanby1_3 = 1
					elseif worldInfo.worldtype == 1 then -- 创造
						stanby1_3 = 2
					else  -- 开发者
						stanby1_3 = 3  
					end
				end
			end
			local reportb ={
				cid = tostring(cid),
				standby1 = stanby1_1..stanby1_2..stanby1_3,
				standby3 = standby3,
				standby4 = standby4,
				}
		-- 客机
		else
			if ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then	

			else
				-- 进入普通房间显示游戏栏目显示上报 by fym
				local roomDesc = AccountManager:getCurHostRoom()
				if roomDesc then
					-- 地图id
					if roomDesc.fromowid and roomDesc.fromowid > 0 then
						cid = tostring(roomDesc.fromowid)
					elseif roomDesc.owid and roomDesc.owid > 0 then
						cid = tostring(roomDesc.owid)
					elseif roomDesc.wid and roomDesc.wid > 0 then 			
						cid = tostring(roomDesc.wid)
					elseif roomDesc.map_type then
						cid = roomDesc.map_type
					end
					
					-- 房间模式connect_mode = 0:公开房间 , 1:协作模式
					if roomDesc.connect_mode then
						stanby1_1 = roomDesc.connect_mode + 1
					end
					-- PC大房间: 人数>6
					if roomDesc.maxplayers and roomDesc.maxplayers > 6 then
						stanby1_2 = 4
					elseif roomDesc.extraData then
						local t_extra = JSON:decode(roomDesc.extraData)
						if t_extra then
							if t_extra.platform then
								-- PC服务器
								if t_extra.platform == 1 then
									stanby1_2 = 3
								-- 手机服务器
								else
									stanby1_2 = 2
								end
							end
						end
					end
				end
			end
		end
	end
end

function PlayMainFrame_OnHide()
	if SwapBlinkBtnName ~= nil then
		local blinkTexture = getglobal(SwapBlinkBtnName.."Check");
		if blinkTexture:IsShown() then
			blinkTexture:Hide();
		end
		SwapBlinkBtnName = nil;
	end

	BattleCoundBlinkingTime = 0;

	--工具模式界面
	GetInst("UIManager"):Close("ToolModeFrame");	--工具模式
	GetInst("UIManager"):Close("ToolObjLib");		--对象库

	--载具控制界面
	GetInst("UIManager"):Close("VehicleDriveMode");

	getglobal("CSStateNoticeFrame"):Hide()

	--在加载地图的时候 网络断开了 弹出的选队界面关不掉 所以在关闭mainFrame的时候 再判断关闭一次 顺带将开局介绍也关掉了
	ClosePreStartGameFrame()
	PlayMain.HP:reset();
	PlayMain.Strength:reset();
	PlayMain.RideHP:reset();
	PlayerHungerBar_ShowOrHide(false);
	PlayerHPBar_ShowOrHide(false);
	HpBarFrame_ShowStBar(false);

	if altmanMusicTimer then
		threadpool:kick(altmanMusicTimer)
	end
	isPlayingAtlmanMusic = false

	if getglobal("RoomMatchBtn") then
		getglobal("RoomMatchBtn"):Hide()
	end

	GetInst("MiniUIManager"):HideUI("chat_viewAutoGen")
	GetInst("MiniUIManager"):HideUI("playermainAutoGen")
	PixelMapInterface:HideCompass();
	GetInst("MiniUIManager"):CloseUI("thermometerFrameAutoGen")--释放温度计ui资源
	GetInst("MiniUIManager"):CloseUI("digproFrameAutoGen")--释放挖掘进度条ui资源
	GetInst("MiniUIManager"):CloseUI("QQMusicPlayerAutoGen")
	--重置tips计时器keys
	UnRegisterAllTipsSchedulerEvents()

	--if GetInst("MiniUIManager"):IsShown("MusicOpCompAutoGen") then
		GetInst("MiniUIManager"):CloseUI("MusicOpCompAutoGen")
	--end
	if isAbroadEvn() then
		if Universe_PlayMainFrame_OnHide then
			Universe_PlayMainFrame_OnHide()
		end
	end
end

function SetBattleBtn()
	-- local teamNum = ClientCurGame:getNumTeam();
	local teamNum = TeamSetterCtrl:getTeamsNum();
	if teamNum == 0 then teamNum = 1 end

	local teamId = CurMainPlayer:getTeam();
	if teamId == 0 then teamNum = 1 end

	local teamIndex = TeamSetterCtrl:getIndexByTeamId(teamId);
	getglobal("BattleBtnMyTeamBkg"):Hide();
	local myTeamScoreName = nil;
	for i=1, Team_Max_Num do
		local score = getglobal("BattleBtnScore"..i);
		if i <= teamNum then
			score:Show();
			if i == teamIndex then
				myTeamScoreName = score:GetName();
			end
		else
			score:Hide();
		end

		local colon = nil;
		if i ~= 6 then
		 	colon = getglobal("BattleBtnColon"..i);
		end

		if colon ~= nil then
			if i <= teamNum-1 then
				colon:Show();
			else
				colon:Hide();
			end
		end
	end

	local width = 102 + (teamNum-1)*80;

	getglobal("BattleBtn"):SetWidth(width);

	if teamId == 0 then
		myTeamScoreName = getglobal("BattleBtnScore1"):GetName()
	end

	if myTeamScoreName then
		getglobal("BattleBtnMyTeamBkg"):SetPoint("Center", myTeamScoreName, "Center", 0, -22);
		getglobal("BattleBtnMyTeamBkg"):Show();
		getglobal("BattleBtnMyTeamBkg1"):SetPoint("Center", myTeamScoreName, "Center", 0, 20);
		getglobal("BattleBtnMyTeamBkg1"):Show();
	end
end

--改变队伍函数
function OnChangeTeam()
	Log("OnChangeTeam");
	getglobal("BattleBtnMyTeamBkg"):Hide();
	local myTeamScoreName = nil;
	local teamId = CurMainPlayer:getTeam();
	local teamIndex = TeamSetterCtrl:getIndexByTeamId(teamId);
	for i=1, Team_Max_Num do
		local score = getglobal("BattleBtnScore"..i);

		if i == teamIndex then
			myTeamScoreName = score:GetName();
		end
	end

	if teamId == 0 then
		myTeamScoreName = getglobal("BattleBtnScore1"):GetName()
	end

	if myTeamScoreName then
		getglobal("BattleBtnMyTeamBkg"):SetPoint("Center", myTeamScoreName, "Center", 0, -22);
		getglobal("BattleBtnMyTeamBkg"):Show();
		getglobal("BattleBtnMyTeamBkg1"):SetPoint("Center", myTeamScoreName, "Center", 0, 20);
		getglobal("BattleBtnMyTeamBkg1"):Show();
	end

	if not GetInst("TeamVocieManage"):isInTeamVocieRoom() and GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):DealChangeTeam()
	end
end


function BattleCountDownFrame_OnUpdate()
	if getglobal("BattleCountDownFrame"):IsShown() then
		local alpha = getglobal("BattleCountDownFrameIcon"):GetBlendAlpha() - 0.04;
		if alpha < 0 then
			getglobal("BattleCountDownFrame"):Hide();
		else
			getglobal("BattleCountDownFrameIcon"):SetBlendAlpha(alpha);
		end
	end
end

local t_BattleCoolDownIconInfo = {
	[10] = {iconName="wfms_shuzi010", width=238, height=176},
	[9] = {iconName="wfms_shuzi09", width=238, height=176},
	[8] = {iconName="wfms_shuzi08", width=238, height=176},
	[7] = {iconName="wfms_shuzi07", width=238, height=176},
	[6] = {iconName="wfms_shuzi06", width=238, height=176},
	[5] = {iconName="wfms_shuzi05", width=238, height=176},
	[4] = {iconName="wfms_shuzi04", width=238, height=176},
	[3] = {iconName="wfms_shuzi03", width=238, height=176},
	[2] = {iconName="wfms_shuzi02", width=238, height=176},
	[1] = {iconName="wfms_shuzi01", width=238, height=176},
	[0] = {iconName="wfms_go", width=294, height=131},
}

function BattleCountDown(code, isrocket)
	if getglobal("BattlePrepareFrame"):IsShown() then
		if getglobal("BattlePrepareFrameStartGame"):IsShown() then
			getglobal("BattlePrepareFrameStartGame"):Hide();
		end
		if code >= 0 then
			getglobal("BattlePrepareFrameTips"):SetText(GetS(1342, code));
		end
	end

	if code == 5 and not isrocket then
		BattleCoundBlinkingTime = 4;
	else
		BattleCoundBlinkingTime = 0;
	end

	if code <= 3 or isrocket then
		if t_BattleCoolDownIconInfo[code] then
			getglobal("BattleCountDownFrameIcon"):SetBlendAlpha(1);
		--	getglobal("BattleCountDownFrameIcon"):SetTexUV(t_BattleCoolDownIconInfo[code].iconName);
			getglobal("BattleCountDownFrameIcon"):SetTexture("ui/mobile/texture2/bigtex/"..t_BattleCoolDownIconInfo[code].iconName..".png", false, false)
			getglobal("BattleCountDownFrameIcon"):SetSize(t_BattleCoolDownIconInfo[code].width, t_BattleCoolDownIconInfo[code].height);

			if not getglobal("BattleCountDownFrame"):IsShown() then
				getglobal("BattleCountDownFrame"):Show();
			end
		else
			getglobal("BattleCountDownFrame"):Hide();
		end
	end
end

function AdventureNoteBtn_OnClick()
	if GetInst("MiniUIManager"):IsShown("AdvantureNoteAutoGen") then 
		GetInst("MiniUIManager"):CloseUI("AdvantureNoteAutoGen")
	else
		SceneEditorUIInterface:OpenUI("AdvantureNote","miniui/miniworld/NpcTalkSet","AdvantureNoteAutoGen", {})
	end
end

function EditorBackBtn_OnClick()
	if GetInst("UGCCommon"):turnModel(2) then
		if ClientCurGame then
			ClientCurGame:changePlayerTeam(AccountManager:getUin(), 0) --模式切换重置队伍属性
		end
		SceneEditorRemovePreviewTip()
		ChangeGameModeBtn_OnClick()
	end
end

function PlayAdventureNoteBtnCanShow()
	if IsInHomeLandMap and IsInHomeLandMap() then --家园中不显示
		return false
	end

	if RoomInteractiveData and RoomInteractiveData:IsSocialHallRoom() then 
		--社交大厅不显示
		return false
	end

	if isAbroadEvn() then
		-- 海外代码在进入游戏前会走一次MiniUIManager:removeAllUI() -> GameSettlementView:Reset() -> PlayAdventureNoteBtnCanShow
		-- 此时存在CurWorld 为空的情况，这个地方做个容错
		if CurWorld then
			if CurWorld:isGameMakerRunMode() or CurWorld:isFreeMode() then
				return true;
			else
				return false;
			end
		else
			return false;
		end
	else
		if CurWorld:isGameMakerRunMode() or CurWorld:isFreeMode() then
			return true;
		else
			return false;
		end
	end

end

function PlayAchievementBtnCanShow()
	-- if IsShowTaskSystem() then
	-- 	return false
	-- end	

	-- if WorldMgr and not WorldMgr.isAdventureMode() then
    --     return false
    -- end

	-- 永久关闭成就系统 code_by:liya
	return false;
end

local ScreenEffectFrameLoopShow = false;
local ScreenEffectFrameAlphaIncSpeed = 0.2;
function ScreenEffect_OnUpdate()
	if getglobal("ScreenEffectFrame"):IsShown() then
		local alpha = getglobal("ScreenEffectFrameBkg"):GetBlendAlpha() - ScreenEffectFrameAlphaIncSpeed;
		if ScreenEffectFrameLoopShow then
			if alpha < 0.05 then
				alpha = 0.05;
				ScreenEffectFrameAlphaIncSpeed = 0-ScreenEffectFrameAlphaIncSpeed;
			elseif alpha > 0.95 then
				alpha = 0.95;
				ScreenEffectFrameAlphaIncSpeed = 0-ScreenEffectFrameAlphaIncSpeed;
			end
		else
			if alpha < 0 then
				alpha = 0;
				getglobal("ScreenEffectFrame"):Hide();
			end
		end

		getglobal("ScreenEffectFrameBkg"):SetBlendAlpha(alpha);
	end
end

function ShowScreenEffect(type, isLoop, incSpeed)
	if not getglobal("ScreenEffectFrame"):IsShown() then
		if isLoop ~= nil then
			ScreenEffectFrameLoopShow = isLoop;
		else
			ScreenEffectFrameLoopShow = false;
		end

		if incSpeed then
			ScreenEffectFrameAlphaIncSpeed = incSpeed;
		else
			ScreenEffectFrameAlphaIncSpeed = 0.2;
		end

		if type == 1 then
			--getglobal("ScreenEffectFrameBkg"):SetTexture("ui/mobile/texture0/outgame/speedline.png");
			getglobal("ScreenEffectFrameBkg"):SetTextureHuiresXml("ui/mobile/texture0/outgame.xml");
			getglobal("ScreenEffectFrameBkg"):SetTexUV("speedline.png");
		elseif type == 2 then
			--getglobal("ScreenEffectFrameBkg"):SetTexture("ui/mobile/texture0/outgame/powerline.png");
			getglobal("ScreenEffectFrameBkg"):SetTextureHuiresXml("ui/mobile/texture0/outgame.xml");
			getglobal("ScreenEffectFrameBkg"):SetTexUV("powerline.png");
		elseif type == 3 then
			getglobal("ScreenEffectFrameBkg"):SetTexture("ui/mobile/texture0/bigtex/fog.png");
			--getglobal("ScreenEffectFrameBkg"):SetTextureHuiresXml("ui/mobile/texture/uitex.xml");
			--getglobal("ScreenEffectFrameBkg"):SetTexUV("fog.png");
		end

		getglobal("ScreenEffectFrameBkg"):SetBlendAlpha(1.0);
		getglobal("ScreenEffectFrame"):Show();
	end
end

function InitScreenEffect()
	getglobal("ScreenEffectFrame"):Hide();
end

function ViewModeChange()
	--锁定俯视角箭头
	if CurMainPlayer and CurMainPlayer:getViewMode() ~= 3 then
		getglobal("OverLookArrowFrame"):Hide();
	elseif not getglobal("OverLookArrowFrame"):IsShown() then
		getglobal("OverLookArrowFrame"):Show();
	end

	local modelId = CurMainPlayer:getViewMode()

	if modelId == 0 then
		modelId = 1 --主视角
	elseif modelId == 2 then
		modelId = 2 --正视角
	elseif modelId == 1 then
		modelId = 3 --背视角
	elseif modelId == 3 then
		modelId = 4 --动作视角
	end
	if SetFrameOnGetViewChange then
		SetFrameOnGetViewChange(modelId)
	end
end

function OverLookArrowFrame_OnUpdate()
	if CurMainPlayer then
		local angle = CurMainPlayer:getOverLookAngleToScreen();
		getglobal("OverLookArrowFrameBkg"):SetAngle(angle);
	end
end
-----------------------------------------------------TaskTrackFrame---------------------------------------------
function TaskTrackFrame_OnShow()

end
local mouseDown = false
function GetTaskTrackMouseDown()
	return mouseDown
end

function SetTaskTrackMouseDown(state)
	mouseDown = state
end

function TaskTrackFrame_OnMouseDown()
	mouseDown = true
	OnClickAnimation("OnMouseDown",{"TaskTrackFrameBkg"},{scale=0.95})
end

function TaskTrackFrame_OnClick()
	if not getglobal("AchievementFrame"):IsShown() then
		getglobal("AchievementFrame"):Show();
	end
	local achievementId = AchievementMgr:getCurTrackID();
	-- UpdateAchievementFrameById(achievementId);
	ShowAchievementById(achievementId);
end

g_TaskFrameStartPosX = 12
g_TaskFrameStartPosY = 140
g_TaskFrameStartCurX = 12
g_TaskFrameStartCurY = 140
function TaskTrackFrame_OnMouseMove()
	if not mouseDown then
		return
	end
	g_TaskFrameStartCurX = g_TaskFrameStartPosX - arg1 + arg3
	g_TaskFrameStartCurY = g_TaskFrameStartPosY - arg2 + arg4
	getglobal("TaskTrackFrame"):SetPoint("topleft", "PlayMainFrame", "topleft", g_TaskFrameStartCurX, g_TaskFrameStartCurY )
end

function TaskTrackFrame_OnMouseUp()
	mouseDown = false
	if g_TaskFrameStartCurX < 1 then
		g_TaskFrameStartCurX = 1
	end
	local fScale = UIFrameMgr:GetScreenScale();
	local screenWidth = GetScreenWidth() / fScale
	local screenHeight = GetScreenHeight() / fScale
	if g_TaskFrameStartCurX > (screenWidth - 253) then
		g_TaskFrameStartCurX = (screenWidth - 253)
	end
	if g_TaskFrameStartCurY < 1 then
		g_TaskFrameStartCurY = 1
	end
	if g_TaskFrameStartCurY > (screenHeight - 106) then
		g_TaskFrameStartCurY = (screenHeight - 106)
	end
	getglobal("TaskTrackFrame"):SetPoint("topleft", "PlayMainFrame", "topleft", g_TaskFrameStartCurX, g_TaskFrameStartCurY )
	g_TaskFrameStartPosX = g_TaskFrameStartCurX
	g_TaskFrameStartPosY = g_TaskFrameStartCurY
	OnClickAnimation("OnMouseUp",{"TaskTrackFrameBkg"},{scale=0.95})
end

function ResetTaskTrackFrame()
	g_TaskFrameStartPosX = 12
	g_TaskFrameStartPosY = 140
	g_TaskFrameStartCurX = 12
	g_TaskFrameStartCurY = 140
	getglobal("TaskTrackFrame"):SetPoint("topleft", "PlayMainFrame", "topleft", g_TaskFrameStartCurX, g_TaskFrameStartCurY )
end

--获取当前执行的主线任务ID
function GetCurMainTaskId()
	-- local t_MainTask = {1000,1001,1003,1006,1008,1009,1010,1012,1014,1015,1016,1017,1018,1022,1023,1024,1025,1138,1139,1040,1041,1042,1172,1173,1143};
	-- for i=1,#(t_MainTask) do
	-- 	local achievementId = t_MainTask[i];
	-- 	local achievementDef = AchievementMgr:getAchievementDef(achievementId);
	-- 	if achievementDef ~= nil and 2 == achievementDef.Group then	--主线任务
	-- 		if AchievementMgr:getAchievementState(achievementDef.ID) == ACTIVATE_UNCOMPLETE then
	-- 			local num = achievementDef.GoalNum;
	-- 			local arryNum = AchievementMgr:getAchievementArryNum(achievementDef.ID);
	-- 			if arryNum < num then
	-- 				return achievementDef.ID;
	-- 			end
	-- 		end
	-- 	end
	-- end
	if GetClientInfo():isEditorMode() then
		return
	end
	
	if not AchievementMgr.getAchievementIDsByGroup then
		return 0 --C++没合并的时候做个容错
	end

	local strIDs = AchievementMgr:getAchievementIDsByGroup(2) --code_by:huangfubin
	local t_MainTask = StringSplit(strIDs, ',')
	if type(t_MainTask)~="table" then return 0 end

	table.sort(t_MainTask, function(a,b)
		local numa = tonumber(a) or 999999999
		local numb = tonumber(b) or 999999999
		return numa < numb --主要考虑移动端顺序不是从小到大
	end)

	local passTask = {}
	local function findNextTrack(achievementDef)
		if achievementDef ~= nil and 2 == achievementDef.Group then	--主线任务
			if passTask[achievementDef.ID] then
				return passTask[achievementDef.ID]
			end
			passTask[achievementDef.ID] = 0 --避免重复查找

			local achievementState = AchievementMgr:getAchievementState(CurMainPlayer:getObjId() ,achievementDef.ID)
			if achievementState == ACTIVATE_UNCOMPLETE then
				local num = achievementDef.GoalNum
				local arryNum = AchievementMgr:getAchievementArryNum(CurMainPlayer:getObjId(), achievementDef.ID)
				if arryNum < num then
					passTask[achievementDef.ID] = achievementDef.ID
					return achievementDef.ID --未完成
				else
					local nextAchievementDef = AchievementMgr:getAchievementDef(achievementDef.NextTrackID)
					if nextAchievementDef then
						local nextID = findNextTrack(nextAchievementDef) 
						passTask[achievementDef.ID] = nextID
						return nextID
					end --已完成，未领取奖励，追踪下一个任务
				end
			elseif achievementState > ACTIVATE_UNCOMPLETE then
				local nextAchievementDef = AchievementMgr:getAchievementDef(achievementDef.NextTrackID)
				if nextAchievementDef then
					local nextID = findNextTrack(nextAchievementDef) 
					passTask[achievementDef.ID] = nextID
					return nextID
				end --找下一个追踪任务
			-- else
			-- 	passTask[achievementDef.ID] = achievementDef.ID
			-- 	return achievementDef.ID --默认返回追踪任务 --不能这样
			end
		end
		return 0
	end
	
	for i=1,#(t_MainTask) do
		local achievementId = tonumber(t_MainTask[i]) or 0
		local achievementDef = AchievementMgr:getAchievementDef(achievementId);
		local id = findNextTrack(achievementDef)
		if id > 0 then
			return id
		end
	end

	return 0
end

function UpdateTaskTrackFrame()
	if IsShowTaskSystem and IsShowTaskSystem() then
		return 
	end
	--if AccountManager:getMultiPlayer() ~= 0 then  return end
	if not CurWorld then return end
	if CurWorld:isGodMode() then return end
	-- if CUR_WORLD_MAPID > 0 and CUR_WORLD_MAPID ~= 2 then return end --都显示 code_by:huangfubin
	if CurWorld:getOWID() == NewbieWorldId or CurWorld:getOWID() == NewbieWorldId2 then return end
	local achievementId = AchievementMgr:getCurTrackID();
	local achievementDef = DefMgr:getAchievementDef(achievementId);
	local TaskTrackFrame = getglobal("TaskTrackFrame")
	if achievementId > 0 and achievementDef ~= nil then
		TaskTrackFrame:Show();
		local num = achievementDef.GoalNum;
		local arryNum = AchievementMgr:getAchievementArryNum(CurMainPlayer:getObjId(), achievementDef.ID);
		local szText = achievementDef.TrackDesc.."#n("..arryNum.."/"..num..")";
		if arryNum >= num then
		--	szText = achievementDef.TrackDesc.."#n(已完成)";
		--	getglobal("AchievementFinishTipsFrame"):Show();
		end
		getglobal("TaskTrackFrameTitle"):SetText(achievementDef.Name)
		getglobal("TaskTrackFrameDesc"):SetText(szText, 255, 255, 255);
		SetItemIcon(getglobal("TaskTrackFrameIcon"), achievementDef.IconID);
		UpdateTaskReward(achievementId)
	else
		TaskTrackFrame:Hide();
	end
end

function TaskTrackRewardItemBtn_Onclick()
	local itemId = this:GetClientUserData(0)
    local name = this:GetName()
    SetMTipsInfo(-1, name, false, itemId);
end

function UpdateTaskReward(achievementId)
	local achievementDef = AchievementMgr:getAchievementDef(achievementId)
    if achievementDef == nil then
        return
    end
    local hasReward = false --没有奖励的时候，领取奖励按钮
    for i = 1, 2 do
        local rewardIcon = getglobal("TaskTrackFrameReward" .. i)
        local numFont = getglobal("TaskTrackFrameRewardNum" .. i)
		local rewardItemBtn = getglobal("TaskTrackFrameRewardItemBtn" .. i)
        if achievementDef.RewardID[i - 1] > 0 then
            hasReward = true
            if achievementDef.RewardType[i - 1] == 0 then
                SetItemIcon(rewardIcon, achievementDef.RewardID[i - 1])
				rewardItemBtn:SetClientUserData(0, achievementDef.RewardID[i - 1]) --用来标记奖励物品的itemID;
            elseif achievementDef.RewardType[i - 1] == 1 then
                rewardIcon:SetTextureHuiresXml("ui/mobile/texture2/common_icon.xml")
                rewardIcon:SetTexUV("icon_xingxing.png")
				rewardItemBtn:SetClientUserData(0, -1) --用来标记星星;
            elseif achievementDef.RewardType[i - 1] == 2 then
                rewardIcon:SetTextureHuiresXml("ui/mobile/texture2/common_icon.xml")
                rewardIcon:SetTexUV("icon_coin")
				rewardItemBtn:SetClientUserData(0, -2) --用来标记迷你币;
            end
            numFont:SetText("×" .. achievementDef.RewardNum[i - 1])
        else
            rewardIcon:SetTextureHuires(GetItemIconMgr():getNullItemIcon())
            numFont:SetText("")
			rewardItemBtn:SetClientUserData(0, 0) 
        end
    end
end

function SetSightingTelescopeFrame(visible)
    local set_btn = getglobal("SetSightingTelescopeBtn")
	local set_btnR = getglobal("SetSightingTelescopeBtnRight")
	local modeRightSightVisible = UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MODRIGHTSIGHTING);
	local rightSightVisible = false;
    if GetClientInfo():isMobile() and CurMainPlayer:hasSightingTelescope() then
		if CurMainPlayer and CurMainPlayer:GetGunHoldState() == 2 then
			set_btn:Hide()
			rightSightVisible = true;
			if modeRightSightVisible then
				set_btnR:Show()
			end
		else
			set_btn:Show()
			set_btnR:Hide()
			rightSightVisible = false;
		end
    else
        set_btn:Hide()
		set_btnR:Hide()
		rightSightVisible = false;
    end
	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("SetSightingTelescopeBtnRight"), rightSightVisible)
	end
end

function SetSightingTelescopeFrame_OnClick()
    CurMainPlayer:setSightingTelescope();
end

--LLDO:展示击杀信息--------------------------------------------------------------------------------------------
local KillInfoFrameDisplayTime = 0;
local KillInfoFrame_CWKills = 1;
local KillInfoFrame_DescStrID = {6365, 6366, 6367, 6368, 6369, 6370, 6371, 6372, 6373, 6374};
local KillInfoFrame_TeamInfo ={
		{name=748, r=255, g=249, b=235},--白
		{name=713, r=237, g=73, b=22},	--红
		{name=714,r=4, g=255, b=246},	--蓝
		{name=715,r=26, g=238, b=22},	--绿
		{name=717,r=237, g=223, b=22},	--黄
		{name=718,r=237, g=144, b=22},	--橙
		{name=716,r=194, g=22, b=237},	--紫
		};

function KillInfoFrame_OnShow()

end

function KillInfoFrame_OnHide()
	-- body
	GetInst("MiniUIManager"):CloseUI("zhongzhongyijiAutoGen")
end
		
function KillInfoFrame_OnUpdate()
	KillInfoFrameDisplayTime = KillInfoFrameDisplayTime - arg1
	local bkg = getglobal("KillInfoFrameTxBkg");

	if KillInfoFrameDisplayTime <= 3 then
		Log("KillInfoFrame_OnUpdate: KillInfoFrameDisplayTime = " .. KillInfoFrameDisplayTime);

		local alpha1 = bkg:GetBlendAlpha();
		alpha1 = alpha1 - 0.25*arg1/3;
		if alpha1 < 0 then
			alpha1 = 0;
		end

		bkg:SetBlendAlpha(alpha1);

		if KillInfoFrameDisplayTime <= 0 then
			this:Hide();
			bkg:SetBlendAlpha(0.25);
		end
	end
end

function ShowKillInfoFrame(beKillUin, CWKills)
	--beKillUin:被击杀者的uin, CWKills:连杀数量
	Log("ShowKillInfoFrame:");
	Log("beKillUin = " .. beKillUin);
	Log("CWKills = " .. CWKills);

	local myUin = GetMyUin();
	local otherUin = beKillUin;
	KillInfoFrame_CWKills = CWKills or 1;
	
	--拉取自己个人信息。
	KillInfoFrame_GetProfile(myUin, Resp_ShowKillInfoFrame_GetMyProfile, otherUin);
	--埋点击杀玩家
	Report418Event(2);
end

--获取击杀用户信息
-- function KillInfoFrame_GetPlayerInfo(myUin, otherUin, CWKills)
-- 	if myUin and otherUin and CWKills then
-- 		Log("KillInfoFrame_GetPlayerInfo:");
-- 		Log("myUin = " .. myUin .. ", otherUin = " .. otherUin .. ", CWKills = " .. CWKills);

-- 		if AccountManager.other_baseinfo then
-- 			local errCode1, myBaseInfo = AccountManager:other_baseinfo(myUin);
-- 			var_dump(myBaseInfo);
-- 			local errCode2, otherBaseInfo = AccountManager:other_baseinfo(otherUin);

-- 			if errCode1 and errCode2 and errCode1 == 0 and errCode2 == 0 then
-- 				var_dump(myBaseInfo);
-- 				var_dump(otherBaseInfo);

-- 				--1. 设置头像
-- 				KillInfoFrame_SetHead(myUin, "KillInfoFrameOtherHead", myBaseInfo);
-- 				KillInfoFrame_SetHead(otherUin, "KillInfoFrameMyHead", otherBaseInfo);
-- 			end
-- 		end
-- 	end
-- end

--拉取个人信息
g_KillInfoFrame_AllProfile = {};

--开房间或加入房间的时候, 将g_KillInfoFrame_AllProfile清空
function KillInfoFrame_InitProfile()
	g_KillInfoFrame_AllProfile = nil;
	g_KillInfoFrame_AllProfile = {};
end

function KillInfoFrame_GetProfile(_op_uin, _callback, _userdata)
	local uin_ = getLongUin(_op_uin);
	local url_ = g_http_root_map .. 'miniw/profile?act=getProfile&op_uin=' .. uin_.. '&' .. 'fast=110' .. '&' .. http_getS1Map();
	Log( "url_ = "..url_ );

	if _callback then
		--如果个人信息已经有了, 则不用重复拉取?????????????????
		local data = {userdata = _userdata, bFlag = false, otherUin = _op_uin};
		if g_KillInfoFrame_AllProfile and #g_KillInfoFrame_AllProfile > 0 then
			for i = 1, #g_KillInfoFrame_AllProfile do
				if _op_uin == g_KillInfoFrame_AllProfile[i].profile.uin then
					data.bFlag = true;	--已经存在.
					Log("KillInfoFrame_GetProfile: Have Existed!!!uin = " .. _op_uin);
					_callback(g_KillInfoFrame_AllProfile[i], data);
					return;
				end
			end
		end

		ns_http.func.rpc( url_, _callback, data,nil,ns_http.SecurityTypeHigh);
	end
end

--拉取自己个人信息回调
function Resp_ShowKillInfoFrame_GetMyProfile(ret, data)
	Log("Resp_ShowKillInfoFrame_GetMyProfileInfo:");
	--data = {userdata = _userdata, bFlag = true};
	local bFlag = data.bFlag;
	local _otherUin = data.userdata;

	if ret and ret.ret == 0 and ret.profile then
		Log("_otherUin = " .. _otherUin);
		if bFlag == false then
			--不存在, 存入全局变量中.
			Log("LLLOG:Don`t Existed !!");
			table.insert(g_KillInfoFrame_AllProfile, ret);
		end

		--拉取自己的信息成功, 再拉取别人的.
		KillInfoFrame_GetProfile(_otherUin, Resp_ShowKillInfoFrame_GetOtherProfile, ret);
	end
end

--拉取被杀者的个人信息回调
function Resp_ShowKillInfoFrame_GetOtherProfile(ret, data)
	Log("Resp_ShowKillInfoFrame_GetOtherProfile:");
	--data = {userdata = _userdata, bFlag = true};
	--加个判断，可能在网络情况差情况下退出游戏还会显示击杀
	if not  (ClientCurGame and ClientCurGame:isInGame()) then
		return
	end

	if GetInst("MiniUIManager"):IsShown("MatchTypeChoose") then 
		return
	end 

	local myRet = data.userdata;
	local bFlag = data.bFlag;
	local otherUin = data.otherUin;

	local killinfoFrame = getglobal("KillInfoFrame");
	local myName = getglobal("KillInfoFrameMyName");
	local otherName = getglobal("KillInfoFrameOtherName");

	if ret and ret.ret == 0 and ret.profile then
		Log("myRet:");
		var_dump(myRet);
		Log("ret:");
		var_dump(ret.profile);

		if bFlag == false then
			--不存在, 存入全局变量中.
			Log("LLLOG:Don`t Existed !!");
			table.insert(g_KillInfoFrame_AllProfile, ret);
		end
		--var_dump(g_KillInfoFrame_AllProfile);

		if killinfoFrame:IsShown() then
			killinfoFrame:Hide()
		end
		killinfoFrame:Show();
		KillInfoFrameDisplayTime = 5.0;

		--拉取玩家信息, 参考函数:UpdateBattleInfo();
		local num = ClientCurGame:getNumPlayerBriefInfo();
		local myBriefInfo = ClientCurGame:getPlayerBriefInfo(-1);	--自己
		local otherBriefInfo = nil;
		for i=1, num do
			otherBriefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
			if otherBriefInfo.uin == otherUin then
				break;
			end
		end

		--1. 名字
		if myRet.profile.RoleInfo and ret.profile.RoleInfo then
			print("AllOK:");
			local myNameStr = myRet.profile.RoleInfo.NickName or "";
			local otherNameStr = ret.profile.RoleInfo.NickName or "";
			KillInfoFrame_SetName("KillInfoFrameMyName", myNameStr, myBriefInfo.teamid + 1);
			KillInfoFrame_SetName("KillInfoFrameOtherName", otherNameStr, otherBriefInfo.teamid + 1);

			--2. 头像
			KillInfoFrame_SetHead(ret.profile.uin, "KillInfoFrameOtherHead", ret);
			KillInfoFrame_SetHead(GetMyUin(), "KillInfoFrameMyHead", myRet);

			local player = GetWorldActorMgr(CurWorld):findActorByWID(myBriefInfo.uin)
			if not player then
				return
			end
			local itemid = player:getCurToolID()
			local skinId = WeaponSkin_HelperModule:GetSkinID(myRet.profile.uin, itemid)
			--3. 击杀描述
			local config = ns_shop_all_skinid_weaponskin_config[skinId] and ns_shop_all_skinid_weaponskin_config[skinId][1] or nil

			if config and config.EffectBtn ~= 0 then
				getglobal("KillInfoFrameDesc"):Hide()
				getglobal("KillInfoFrameBkg"):Hide()
				getglobal("KillInfoFrameTxBkg"):Hide()
				getglobal("KillInfoFrameDesc"):Hide()
				getglobal("KillInfoFrameMyHead"):SetPoint("topleft", "KillInfoFrame", "topleft", 61, 0)
				getglobal("KillInfoFrameOtherHead"):SetPoint("topright", "KillInfoFrame", "topright", -85, 0)
				
				GetInst("MiniUIManager"):CloseUI("zhongzhongyijiAutoGen")
				GetInst("MiniUIManager"):OpenUI(config.EffectName.."jisha","miniui/miniworld/zhongzhongyiji","zhongzhongyijiAutoGen", {
					killNum = KillInfoFrame_CWKills > 10 and 10 or KillInfoFrame_CWKills,
					disableOperateUI = true,
					keep = true})
			else
				getglobal("KillInfoFrameDesc"):Show()
				getglobal("KillInfoFrameBkg"):Show()
				getglobal("KillInfoFrameTxBkg"):Show()
				getglobal("KillInfoFrameDesc"):Show()
				getglobal("KillInfoFrameMyHead"):SetPoint("topleft", "KillInfoFrame", "topleft", 35, 0)
				getglobal("KillInfoFrameOtherHead"):SetPoint("topright", "KillInfoFrame", "topright", -35, 0)
				local descObj = getglobal("KillInfoFrameDesc");
				local strDesc = "";
				if KillInfoFrame_CWKills <= #KillInfoFrame_DescStrID then
					strDesc = GetS(KillInfoFrame_DescStrID[KillInfoFrame_CWKills]);
				else
					strDesc = GetS(KillInfoFrame_DescStrID[#KillInfoFrame_DescStrID]);
				end
				descObj:SetText(strDesc);
			end

		end
	end
end

--设置头像
function KillInfoFrame_SetHead(uin, strHeadBtn, ret)
	uin = ret.profile.uin or uin or 1;
	local model = ret.profile.RoleInfo.Model or 0;
	local skinid = ret.profile.RoleInfo.SkinID or 0;
	local hasAvatar = ret.profile.RoleInfo.HasAvatar or 0;
	local strHeadIcon = strHeadBtn .. "Icon";
	local strHeadFrame = strHeadBtn .. "IconFrame"
	local headObj = getglobal(strHeadIcon);


	if ret.profile.header and ret.profile.header.url then
		SetUserHeadIconByUrl(ret.profile.header.url, ret.profile.header.checked, uin, headObj);
	else
		HeadCtrl:SetPlayerHeadByUin(strHeadIcon,uin,model,skinid,hasAvatar);
	end
	--头像框
	HeadFrameCtrl:SetPlayerheadFrameName(strHeadFrame,ret.profile.head_frame_id);
end

--设置名字
function KillInfoFrame_SetName(nameUi, nameStr, teamId)
	Log("KillInfoFrame_SetName: nameStr = " .. nameStr .. ", teamId = " .. teamId);
	local nameObj = getglobal(nameUi);
	nameObj:SetText(nameStr);
	nameObj:SetTextColor(KillInfoFrame_TeamInfo[teamId].r, KillInfoFrame_TeamInfo[teamId].g, KillInfoFrame_TeamInfo[teamId].b);
end

--LLDO:展示击杀信息:end--------------------------------------------------------------------------------------------

function GasInfo_OnLoad()
	GasInfo_AddGameEvent()
end
function GasInfo_AddGameEvent()
	SubscribeGameEvent(nil,GameEventType.SyncSasTime,function(context)
		NewEventBridgeOldEvent(GameEventType.SyncSasTime,context)
		arg1 = GameEventType.SyncSasTime
		GasInfo_OnEvent()
	end )
	SubscribeGameEvent(nil,GameEventType.SyncPlayerNum,function(context)
		NewEventBridgeOldEvent(GameEventType.SyncPlayerNum,context)
		arg1 = GameEventType.SyncPlayerNum
		GasInfo_OnEvent()
	end )
end
function GasInfo_OnEvent()
    local ge = GameEventQue:getCurEvent();
    local gasframe = getglobal("GasInfo")

    --检查是否显示
    if CurWorld ~= nil and CurWorld:isGameMakerRunMode() then
        local curOpId, val = 0, 0;
        curOpId, val = CurWorld:getRuleOptionID(35, curOpId, val);
        if not val then
            gasframe:Hide()
            return
        end
    else
        gasframe:Hide()
        return
    end

    gasframe:Show()

    if arg1 == "GE_SYNC_GAS_TIME" then
        local stage = ge.body.gastimeinfo.stage;
        local cur_t = ge.body.gastimeinfo.cur_time;
        local beg_t = ge.body.gastimeinfo.beg_time;
        local end_t = ge.body.gastimeinfo.end_time;

        --设置时间标题
        if stage == 1 then
            getglobal("GasInfoTimeTitle"):SetText(GetS(8011))
        elseif stage == 2 then
            getglobal("GasInfoTimeTitle"):SetText(GetS(8012))
        elseif stage == 3 then
            getglobal("GasInfoTimeTitle"):SetText(GetS(8015))
	elseif stage == 4 then
		getglobal("GasInfoTimeTitle"):SetText(GetS(8019))
	elseif stage == 5 then
		getglobal("GasInfoTimeTitle"):SetText(GetS(8020));
		getglobal("GasInfoTime"):SetText("");
        end

        --设置时间和进度条
	if stage ~= 5 then
		local s, m;
		local ratio;
		s = end_t - cur_t;
		ratio = s / (end_t - beg_t)
		if s >= 0 then
		    m = math.floor(s/60);
		    s = s - m*60;

		    getglobal("GasInfoTime"):SetText(m..":"..s)
		    --getglobal("GasInfoRemainTime"):SetWidth(getglobal("GasInfoFullTime"):GetWidth()*ratio);
		end
	end
    elseif arg1 == "GE_SYNC_PLAYER_NUM" then
        local alive = ge.body.playerinfo.alive;
        local all = ge.body.playerinfo.all;

        getglobal("GasInfoPlayerNumber"):SetText(alive.."/"..all)
    end
end

function GasInfo_OnUpdate()
    local gasframe = getglobal("GasInfo")

    --检查是否显示
    if CurWorld == nil or not CurWorld:isGameMakerRunMode() then
		if gasframe then
        	gasframe:Hide()
		end
    end

end



--LLDO:小鸡坐骑能量条--------------------------------------------------------------------------------------------
function ChickenEnergyFrame_OnUpdate()

	local ride = CurMainPlayer:getRidingHorse();
	if ride == nil then
	   return;
	end

	local isRush = ride:hasWaterSkill(4)
	local energyBar = getglobal("ChickenEnergyFrameBar");
	local energy = ride:getEnergy();
	local isFatigue = ride:isTired();
	if not isRush then
		local baricon = getglobal("ChickenEnergyFrameIcon")
		baricon:SetTextureHuiresXml("ui/mobile/texture2/old_operateframe.xml");
		baricon:SetTexUV("zq_fly.png");
	end

	if not isFatigue then
		energyBar:SetTextureHuiresXml("ui/mobile/texture2/outgame.xml");
		energyBar:SetTexUV("zq_jindu.png");

		if GetClientInfo():isMobile() then
			energyBar:SetSize(energy*2.46,13);
		else
			energyBar:SetSize(energy*1.66,13);
		end
	else
		energyBar:SetTextureHuiresXml("ui/mobile/texture2/outgame.xml");
		energyBar:SetTexUV("sjb_jindu.png");

		if GetClientInfo():isMobile() then
			energyBar:SetSize(energy*2.46,13);
		else
			energyBar:SetSize(energy*1.66,13);
		end
	end
end



--人物动作表情
function CharacterActionBtn_OnClick()
	if true then
		GetInst("PlayerExpressionManager"):OpenActionExpression()
		return 
	end
	if IsInHomeLandMap() then
		-- 家园快捷短语按钮click埋点上报
		Homeland_StandReport_MainUIView("ShortcutPhrase", "click")
	end

	if GetInst("actionExpressionManager"):IsOpenNew() then
		if GetInst("MiniUIManager"):IsShown("actionExpression") then
			GetInst("actionExpressionManager"):CloseActionExpression()
			Homeland_StandReportSingleEvent("PHRASE", "Close", "click", {})
		else
			GetInst("actionExpressionManager"):OpenActionExpression()
		end
	else
		if getglobal("CharacterActionFrame"):IsShown() then
			if getglobal("ActionLibraryFrame"):IsShown() then
				getglobal("ActionLibraryFrame"):Hide();
			end
			getglobal("CharacterActionFrame"):Hide();
			--家园埋点
			Homeland_StandReportSingleEvent("PHRASE", "Close", "click", {})
		else
			getglobal("CharacterActionFrame"):Show();
		end
	end

	setkv("IsShownCharacterActionRedTag",1)
	getglobal("CharacterActionRedTag"):Hide()

end

--装扮互动被邀请点击
function ActorBeInviteBtn_OnClick()
	getglobal("ActorInviteTipBtn"):Hide()
	getglobal("CharacterActionFrame"):Show();
	ShowActorInvite(true)
end

--自动演奏，自由演奏被点击
function MusicPlayModeBtn_OnClick()
	if CurMainPlayer:isDead() then
        return
    end

	--禁止操作时不允许开始演奏
	if CurMainPlayer then
		local attrib = CurMainPlayer:getLivingAttrib()
		if attrib and attrib:getBuffEffectBankInfo(2034) then
			return
		end
	end

	if GetInst("MiniUIManager"):IsShown("DeathFrameAutoGen") then
		return
	end

	if not GetInst("MiniUIManager"):IsShown("MusicOpCompAutoGen") then
		return
	end

	local musicOpCompCtrl = GetInst("MiniUIManager"):GetCtrl("MusicOpComp")
	if not musicOpCompCtrl or not musicOpCompCtrl:IsMusicPlayModeBtnVisible() then
		return
	end
	
	local isShow = GetInst("MiniUIManager"):IsShown("StarStationInfoFrameAutoGen")

	if CurMainPlayer and CurMainPlayer:isShapeShift() then
		ShowGameTips(GetS(130026))
		return
	end

	local ride = CurMainPlayer:getRidingHorse();
	if ride or isShow then
		ShowGameTips(GetS(130025))
		return
	end

	local itemId = ClientBackpack:getGridItem(ClientBackpack:getShortcutStartIndex()+ShortCut_SelectedIndex)

	if GetInst("MiniUIManager"):IsShown("main_player_free") then
		GetInst("MiniUIManager"):CloseUI("main_player_freeAutoGen")
	end

	if itemId > 0 then
		local itemDef = ItemDefCsv:get(itemId)

		if itemDef.Type == ITEM_TYPE_MUSIC then
			-- CurMainPlayer:playAct(600108)
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common"},"main_player_freeAutoGen")
			GetInst("MiniUIManager"):OpenUI(
				"main_player_free", 
				"miniui/miniworld/music_roleplay", 
				"main_player_freeAutoGen",{id=itemId}
				)

			CurMainPlayer:setViewMode(2)
		end
	end
end

function MusicPreinstallBtn_OnClick()
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common_comp"},"main_songbookAutoGen")

	GetInst("MiniUIManager"):OpenUI(
		"main_songbook", 
		"miniui/miniworld/music_roleplay", 

		"main_songbookAutoGen",{callback = function()

			local data = GetInst("songBookDataManager"):getCurData()

			if data then
				local nameStr = DefMgr:filterString(data.name)
				--getglobal("MusicPreinstallBtnTips"):SetText(nameStr)
				local musicOpCompCtrl = GetInst("MiniUIManager"):GetCtrl("MusicOpCompAutoGen")
				if musicOpCompCtrl then
					musicOpCompCtrl:UpdateMusicPreintallBtnTips(nameStr)
				end
			end
		end}
	)
end

----------------------------------------------------------物理机械：键位控制-------------------------------------------------------
--PC端键位控制管理
local PC_VehicleKeyMgr = {

	MAX_KEY_PC = 21,
	MAX_KEY_FIRSTLINE_PC = 16,

	--存储可用键位信息的table：一期固定21键位，不可自定义增删编辑
	key = {
		--{id=xx, name="xx", value=xx}

	},

	t_del = {

	},

	--键位初始化：一期没有自定义编辑，Init一次就行了
	InitKey = function(self)
		print("PC VehicleKeyMgr key_Init:")
		self.key = {	--总键位
			{id=1,		name="W",			value=87,	uv="icon_speedup"},
			{id=2,		name="A",			value=65,	uv="icon_turnleft"},
			{id=3,		name="S",			value=83,	uv="icon_speeddown"},
			{id=4,		name="D",			value=68,	uv="icon_turnright"},
			--{id=5,		name="1",			value=49,	},
			--{id=6,		name="2",			value=50,	},
			--{id=7,		name="3",			value=51,	},
			--{id=8,		name="4",			value=52,	},
			--{id=9,		name="5",			value=53,	},
			--{id=10,		name="6",			value=54,	},
			--{id=11,		name="7",			value=55,	},
			--{id=12,		name="8",			value=56,	},
			--{id=13,		name="Q",			value=81,	},
			--{id=14,		name="E",			value=69,	},
			{id=5,		name="R",			value=82,	uv="icon_turnover"},
			--{id=16,		name="Z",			value=90,	},
			--{id=17,		name=GetS(12010),	value=150,	},
			--{id=18,		name=GetS(12011),	value=151,	},
			--{id=19,		name="Space",		value=32,	},
			--{id=20,		name="Ctrl",		value=17,	},
			{id=6,		name="Shift",		value=16,	uv="icon_getout"},
		}

		

	end,


	GetKeyTable = function(self)
		return self.key
	end,

	--键位数量变化后，自适应排版：一期先只对不同数量的按键做居中处理
	UpdateKeyCtrl = function(self)
		print("PC VehicleKeyMgr UpdateKeyCtrl:")
		local key = self.key
		self.t_del = {}
		local vehicle = CurMainPlayer and CurMainPlayer:getDrivingVehicle() or nil
		if vehicle then	
			if vehicle:getEngineNum() <= 0 then
				for i=1,#(key) do
					if key[i].name == "W" or key[i].name == "S" then
						table.insert(self.t_del, i)
					end
				end
			end

			if vehicle:getSteeringSwitch() == false then
				for i=1,#(key) do
					if key[i].name == "A" or key[i].name == "D" then
						table.insert(self.t_del,i)
					end
				end
			end
		end

		if #(self.t_del) > 0 then
			table.sort(self.t_del,function(a,b) return a > b end)
			self:DeleteKey()
		end
		self:UpdateKeyUI()

		
	end,

	--刷新键位UI
	UpdateKeyUI = function(self)
		local btn_name = "PC_VehicleShortCut"
		print("Current Key:",self.key)
		for i=1,self.MAX_KEY_PC do
			if i <= #(self.key) then
				getglobal(btn_name..i.."Name"):SetText(self.key[i].name)
				getglobal(btn_name..i.."Icon"):SetTexUV(self.key[i].uv)
			end
		end

		local key_num = #(self.key)
		print("PC VehicleKeyMgr key_UpdatePos:",key_num)
		local btn_name = "PC_VehicleShortCut"
		for i=1,self.MAX_KEY_PC do
			if i<=key_num then
				getglobal(btn_name..i):Show()
			else
				getglobal(btn_name..i):Hide()
			end
		end

		local firstline_key = key_num
		local line = 1
		if key_num > self.MAX_KEY_FIRSTLINE_PC then
			firstline_key = self.MAX_KEY_FIRSTLINE_PC
			line = 2
		end
		
		--自适应排版 居中
		local btn_width = 61
		local interval  = 12
		local width = ((btn_width + interval) * firstline_key - interval);
		getglobal("PC_VehicleControlFrame"):SetWidth(width)
		getglobal("PC_VehicleControlFrame"):SetHeight(btn_width+(btn_width+10)*(line-1))
		for i=1,firstline_key do
			getglobal(btn_name..i):SetPoint("bottomleft","PC_VehicleControlFrame","bottomleft",(i-1)*(btn_width+interval),0)
		end
		for i= self.MAX_KEY_FIRSTLINE_PC + 1, key_num do
			if i < 19 then
				getglobal(btn_name..i):SetPoint("bottom",btn_name..tostring(i-14),"top",0,-10)
			else
				getglobal(btn_name..i):SetPoint("bottom",btn_name..tostring(i-6),"top",0,-10)
			end
		end
	end,

	DeleteKey = function(self)
		if #(self.t_del) == 0 then return end
		print("DeleteKey:",self.t_del)
		for i=1, #(self.t_del) do
			table.remove(self.key, self.t_del[i])
		end
		self.t_del = {}
	end,

	--按下键位，触发设置效果.input: key表索引
	TiggerKeyPressed = function(self, idx, ispressed)
		
		if self.key[idx] and self.key[idx].value then
			local value = self.key[idx].value
			----print("PC VehicleKeyMgr TriggerKeyPressed:",idx,ispressed,value)
			if value == _G.vehicle_config.left_key then					--左转
				VehicleControlInput:setSteerLeftKeyPressed(ispressed)
			elseif value == _G.vehicle_config.right_key then 			--右转
				VehicleControlInput:setSteerRightKeyPressed(ispressed)
			elseif value == _G.vehicle_config.forward_key then			--前进
				VehicleControlInput:setAccelKeyPressed(ispressed)
			elseif value == _G.vehicle_config.backward_key then			--后退
				VehicleControlInput:setBrakeKeyPressed(ispressed)
			elseif value == _G.vehicle_config.leave_key then 			--下车
				CurMainPlayer:dismountActor();
			elseif value == _G.vehicle_config.reset_key then 			--重置
				local vehicle = CurMainPlayer:getDrivingVehicle()
				if vehicle then
					vehicle:reset()
				end
			end
		end
	end,
	

}

--mobile端键位控制管理
local Mobile_VehicleKeyMgr = {
	MAX_KEY_MOBILE = 21,		--键位数量上限
	MAX_KEY_MOBILE_RECT = 8,	--方形按钮数量上限
	MAX_KEY_MOBILE_CIRCLE = 8,	--圆形按钮数量上限

	--存储可用键位信息的table：一期固定21键位，不可自定义增删编辑
	key = {
		
	},

	Init = function(self)
		print("Mobile VehicleKey Mgr: Init")
		self.key = {	--总键位
			--{id=1,		name="W",			value=87,	},
			--{id=2,		name="A",			value=65,	},
			--{id=3,		name="S",			value=83,	},
			--{id=4,		name="D",			value=68,	},
			--{id=5,		name="1",			value=49,	},
			--{id=6,		name="2",			value=50,	},
			--{id=7,		name="3",			value=51,	},
			--{id=8,		name="4",			value=52,	},
			--{id=9,		name="5",			value=53,	},
			--{id=10,		name="6",			value=54,	},
			--{id=11,		name="7",			value=55,	},
			--{id=12,		name="8",			value=56,	},
			--{id=13,		name="Q",			value=81,	},
			--{id=14,		name="E",			value=69,	},
			{id=1,		name="R",			value=82,	},
			--{id=16,		name="Z",			value=90,	},
			--{id=17,		name=GetS(12010),	value=150,	},
			--{id=18,		name=GetS(12011),	value=151,	},
			--{id=19,		name="Space",		value=32,	},
			--{id=20,		name="Ctrl",		value=17,	},
			{id=2,		name="Shift",		value=16,	},
		}
	end,

	UpdateKeyPos = function(self)
		--local key_num = #(self.key)
		print("Mobile Vehicle Mgr: UpdateKeyPos")
		getglobal("Mobile_Circle_VehicleControlFrameBtn3"):Show()
		getglobal("Mobile_Circle_VehicleControlFrameBtn5"):Show()
		local icon3 = getglobal("Mobile_Circle_VehicleControlFrameBtn3Icon")
		local icon5 = getglobal("Mobile_Circle_VehicleControlFrameBtn5Icon")
		icon3:SetTexUV("icon_turnover")
		icon5:SetTexUV("icon_getout")
		--local rect_btn_name = "Mobile_VehicleShortCut"
		--local btn_width = 61
		--local interval  = 12
		--local width = (btn_width + interval) * 2 - interval
		--getglobal("Mobile_Rect_VehicleControlFrame"):SetWidth(width)
		--getglobal("Mobile_Rect_VehicleControlFrame"):SetHeight(btn_width + interval)
		--for i=1,8 do
		--	getglobal(rect_btn_name..i.."Name"):Hide()
		--	if i < 3 then
		--		getglobal(rect_btn_name..i):SetPoint("bottomleft","Mobile_Rect_VehicleControlFrame","bottomleft",(i-1)*(btn_width+interval),0)
		--		getglobal(rect_btn_name..i):Show()
		--	else
		--		getglobal(rect_btn_name..i):Hide()
		--	end
		--end

	end,

	TiggerKeyPressed = function(self, idx, ispressed)
		if idx == 3 then idx = 1 end
		if idx == 5 then idx = 2 end
		print("Mobile VehicleKeyMgr TriggerKeyPressed:",idx,ispressed)
		if self.key[idx] and self.key[idx].value then
			local value = self.key[idx].value

			if value == _G.vehicle_config.leave_key then 			--下车
				CurMainPlayer:dismountActor();
			elseif value == _G.vehicle_config.reset_key then 			--重置
				local vehicle = CurMainPlayer:getDrivingVehicle()
				if vehicle then
					vehicle:reset()
				end
			end
		end
	end,



}

function VehicleKeyMgrGetInstance(platform)
	print("VehicleKeyMgrGetInstance:",platform);
	if platform == "PC" then
		return PC_VehicleKeyMgr;
	elseif platform == "Mobile" then
		return Mobile_VehicleKeyMgr;
	end
end

function PC_VehicleControlFrame_OnShow( ... )
	if not getglobal("PC_VehicleControlFrame"):IsReshow() then
		local keyMgr = VehicleKeyMgrGetInstance("PC")
		if keyMgr then
			keyMgr:InitKey();
			keyMgr:UpdateKeyCtrl();
		end
	end

end

function VehicleControlBtnTemplate_OnMouseDown( ... )
	local idx = this:GetClientID()
	local keyMgr = nil;
	if GetClientInfo():isPC() then
		keyMgr = VehicleKeyMgrGetInstance("PC")
	elseif GetClientInfo():isMobile() then
		keyMgr = VehicleKeyMgrGetInstance("Mobile")
	end
	if not keyMgr then return end
	keyMgr:TiggerKeyPressed(idx,true)
	
end

function VehicleControlBtnTemplate_OnMouseUp( ... )
	local idx = this:GetClientID()
	local keyMgr = nil;
	if GetClientInfo():isPC() then
		keyMgr = VehicleKeyMgrGetInstance("PC")
	elseif GetClientInfo():isMobile() then
		keyMgr = VehicleKeyMgrGetInstance("Mobile")
	end
	if not keyMgr then return end
	keyMgr:TiggerKeyPressed(idx,false)
end

function MobileVehicleCircleBtnTemplate_OnClick( ... )
	local idx = this:GetClientID()
	local keyMgr = VehicleKeyMgrGetInstance("Mobile")
	if keyMgr then
		keyMgr:TiggerKeyPressed(idx,true)
	end
end

------------------------------------------------移动端键位-----------------------------------------------------------------------------
local MAX_KEY_MOBILE_RECT = 8;

function Mobile_Circle_VehicleControlFrame_OnShow( ... )
	if not getglobal("Mobile_Rect_VehicleControlFrame"):IsReshow() then
		local keyMgr =  VehicleKeyMgrGetInstance("Mobile")
		if not keyMgr then return end
		keyMgr:Init()
		keyMgr:UpdateKeyPos()
	end

end


--------------------------------------------------物理机械：血量/速度/油耗/键位等显示隐藏---------------------------------------
local t_NeedHide = {}
function setVehicleUI(isshown)

	--if GetClientInfo():isPC() then
	--	vehicleUI = getglobal("PC_VehicleControlFrame")
	--elseif GetClientInfo():isMobile() then
	--	vehicleUI = getglobal("Mobile_Circle_VehicleControlFrame")
	--end
	if not CurMainPlayer then
		return
	end

    local vehicle = CurMainPlayer:getDrivingVehicle()
	local t_vehiclestate = {
		getglobal("VehicleState"),
	}
	local t_vehiclespeedstate = {
		getglobal("VehicleSpeedState"),
	}
	for i = 1, #(t_vehiclestate) do
		t_vehiclestate[i]:Hide()
	end
	for i = 1,#(t_vehiclespeedstate) do 
		t_vehiclespeedstate[i]:Hide()
	end
	local t_HideFrame = {
		--getglobal("AccRideCallBtn"),
		--getglobal("PlayShortcut"),
		getglobal("GunMagazine"),
		getglobal("CharacterActionFrame"),
		--getglobal("PlayMainFrameRide"),
		--getglobal("PlayMainFrameBackpack"),
		getglobal("ColoreSelectedFrame"),
		getglobal("ToolModeFrame"),
	}

	if not CurMainPlayer then return end
	if ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleController") then
		table.insert(t_HideFrame,getglobal("PlayShortcut"))
		table.insert(t_HideFrame,getglobal("PlayMainFrameBackpack"))
		if UGCModeMgr and UGCModeMgr:IsEditing() then  -- 编辑地图
			SceneEditorUIInterface:HideNode()
			GetInst("MiniUIManager"):HideMiniUI()
		end
	end
	

	if isshown == true then
		if #(t_NeedHide) == 0 then
			for i=1,#(t_HideFrame) do
				if t_HideFrame[i]:IsShown() then
					print("t_HideFrame",i)
					table.insert(t_NeedHide,t_HideFrame[i])
				end
			end
		end

		for i = 1, #(t_NeedHide) do
			local mvcFrame = GetInst("UIManager"):GetCtrl(t_HideFrame[i]:GetName());
			if mvcFrame then
				GetInst("UIManager"):Close(t_HideFrame[i]:GetName());
			else
				t_NeedHide[i]:Hide()
			end
		end
		
		getglobal("PlayMainFrameDismountVehicle"):Hide();
		if ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleController") then
			local param = {disableOperateUI = true}
			GetInst("UIManager"):Open("VehicleDriveMode",param)
		else
			GetInst("UIManager"):Close("VehicleDriveMode")
			if CurMainPlayer:getMountType() == MOUNT_DRIVE and GetClientInfo():isMobile() then
				getglobal("PlayMainFrameDismountVehicle"):Show();
			end
		end
		
		if ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleDriver") and vehicle:hasFuel()then
			for i = 1, #(t_vehiclestate) do
				t_vehiclestate[i]:Show()
			end
		end
		
		if ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleDriver") then
			for i = 1,#(t_vehiclespeedstate) do 
				t_vehiclespeedstate[i]:Show()
			end
		end 

		if vehicle:getEngineType() then
			for i = 1, #(t_vehiclestate) do
				t_vehiclestate[i]:Hide()
			end
		end
		
		getglobal("AccRideCallBtn"):Hide();

	else
		if #(t_NeedHide) > 0 then
			for i=1,#(t_NeedHide) do
				if not t_NeedHide[i]:IsShown() then
					local mvcFrame = GetInst("UIManager"):GetCtrl(t_HideFrame[i]:GetName());
					if mvcFrame then
						GetInst("UIManager"):Open(t_HideFrame[i]:GetName());
					else
						t_NeedHide[i]:Show()
					end
				end
			end
			t_NeedHide = {}
		end

		if UGCModeMgr and UGCModeMgr:IsEditing() then  -- 编辑地图
			GetInst("MiniUIManager"):ShowMiniUI()
			SceneEditorUIInterface:ShowNode()
		end

		GetInst("UIManager"):Close("VehicleDriveMode")

		for i = 1, #(t_vehiclestate) do
			t_vehiclestate[i]:Hide()
		end

		if not isEducationalVersion then
			--新手地图中隐藏 坐骑按钮
			local _show = true
			if isAbroadEvn() then
				_show = IsNeedShowAccRideCallBtn()
			end
			if _show and CurWorld and CurWorld:getOWID() ~= NewbieWorldId and CurWorld:getOWID() ~= NewbieWorldId2 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
				getglobal("AccRideCallBtn"):Hide();
			else
				getglobal("AccRideCallBtn"):Hide();
			end
		end

		--变形按钮
		if getglobal("AccRideCallBtn"):IsShown() then
			getglobal("AccRideChangeBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
		else
			getglobal("AccRideChangeBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
		end

		if getglobal("PlayMainFrameDismountVehicle"):IsShown() then
			getglobal("PlayMainFrameDismountVehicle"):Hide();
		end


	end

	local vehicle = CurMainPlayer:getDrivingVehicle()
	if vehicle==nil or vehicle:getMinPartsCost() == 999 or not ActorComponentCallModule(CurMainPlayer,"RiddenComponent","isVehicleDriver") then
		getglobal("VehicleStateFuelScale"):Hide()
		getglobal("VehicleStateFuelCursor"):Hide()
		getglobal("VehicleStateFuelIcon"):Hide()
	else
		getglobal("VehicleStateFuelScale"):Show()
		getglobal("VehicleStateFuelCursor"):Show()
		getglobal("VehicleStateFuelIcon"):Show()
	end

	--教育版不显示坐骑
	if isEducationalVersion then
		getglobal("AccRideCallBtn"):Hide();
	end
end

--------------------------------------------------------车辆状态：速度、油耗...------------------------------------------------------
function VehicleState_OnLoad( ... )
	this:setUpdateTime(0.25)
end

function VehicleState_OnUpdate( ... )
	getglobal("VehicleStateFuelCursor"):SetPoint("bottom","VehicleStateFuelScale","bottomleft",200,-11)
	local vehicle = CurMainPlayer:getDrivingVehicle()
	print("_curspeed","987")
	if CurMainPlayer:getMountType() == MOUNT_DRIVE and vehicle ~= nil then
		print("_curspeed","213")
		local curSpeed = vehicle:getCurSpeedShow()
		getglobal("VehicleCurSpeed"):SetText(curSpeed)
		
		local fuelrate = vehicle:getMinPartsCost()
		if fuelrate then
			local scale = getglobal("VehicleStateFuelScale"):GetWidth()
			--print("fuelrate:",fuelrate)
			local width = (scale-6)*fuelrate;
			getglobal("VehicleStateFuelCursor"):SetPoint("bottom","VehicleStateFuelScale","bottomleft",width+3,-11)
		end
	end
end

function OnBasketBallChargeChange(charge,adjust_region_min,adjust_region_max)
	local width = 168;
	local height = 14;
	if GetClientInfo():isMobile() then
		width = 257;
		height = 17;
	end

	local ratio = charge/100;

	charge = charge == 0 and 1 or charge;
	if charge > 0 and not getglobal("CharacterActionFrame"):IsShown() then
		getglobal("BallChargeFrame"):Show();
		local charge_width = ratio*width
		if adjust_region_min ~= -1 and adjust_region_max ~= -1 then
			local aim_minx_ration = adjust_region_min/100
			local aim_maxx_ration = adjust_region_max/100
			local aim_width = (aim_maxx_ration - aim_minx_ration)*width
			local pc_aim_width = (aim_maxx_ration - aim_minx_ration)*257
			if charge >= adjust_region_min and
			   charge <= adjust_region_max then
				getglobal("BallChargeFrameAimRegion"):SetPoint("left","BallChargeFrameBkg","left",aim_minx_ration*width,0);
				getglobal("BallChargeFrameAimRegion"):ChangeTexUVWidth(pc_aim_width);
				getglobal("BallChargeFrameAimRegion"):SetSize(charge_width - aim_minx_ration*width , height);
				getglobal("BallChargeFrameAimRegion"):Show();
			end
			if charge < adjust_region_min then
				getglobal("BallChargeFrameAimRegion"):Hide();
			end
			getglobal("BallChargeFrameAimRegionBg"):SetPoint("left","BallChargeFrameBkg","left",aim_minx_ration*width,0);
			getglobal("BallChargeFrameAimRegionBg"):ChangeTexUVWidth(pc_aim_width);
			getglobal("BallChargeFrameAimRegionBg"):SetSize(aim_width, height);
			getglobal("BallChargeFrameAimRegionBg"):Show();
		else
			getglobal("BallChargeFrameAimRegion"):Hide();
			getglobal("BallChargeFrameAimRegionBg"):Hide();
		end
		
		getglobal("BallChargeFrameCharge"):ChangeTexUVWidth(ratio*257);
		--getglobal("BallChargeFrameCharge"):ChangeTexUVWidth(charge_width);
		getglobal("BallChargeFrameCharge"):SetSize(charge_width, height);
	else
		getglobal("BallChargeFrame"):Hide();
	end

end


function ChangePlayerCallBack()
	if not CurMainPlayer then
		return
	end

	-- local skinId = CurMainPlayer:getSkinID()
	local skinId = GetMyInMapCurSkinId()
	local skinDef = RoleSkinCsv:get(skinId)
	if skinDef and skinDef["ChangeType"] > 0 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
		SetAccRideChangeBtnVisible(true)
		if CurMainPlayer:isShapeShift() then
			if GetClientInfo():isMobile() and needShowRideAttackBtn(skinId)  then --64-红蜘蛛 坐骑飞行 加速按钮无效不显示
				getglobal("AccRideAttackBtn"):Show()
				getglobal("AccRideAttackLeftBtn"):Show()
			end
		else
			getglobal("AccRideAttackBtn"):Hide()
			getglobal("AccRideAttackLeftBtn"):Hide()
		end
	else
		SetAccRideChangeBtnVisible(false)
	end
	if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then
		SetAccSummonBtnVisible(true)
	else
		if AvatarSummonIndex > 0 then
			if CurWorld and CurWorld:isRemoteMode() then
				local params = {objid = CurMainPlayer:getObjId(),summonid = 0}
				SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.BUZZ.AVATAR_SUMMON_TOHOST, params)
			else
				CurMainPlayer:avatarSummon(0)
			end
			AvatarSummonIndex = 0
		end
		SetAccSummonBtnVisible(false)
	end
	if getglobal("AccRideChangeBtn"):IsShown() then--如果变形按钮显示，设置召唤按钮到变形按钮左边
		getglobal("AccSummonBtn"):SetPoint("right", "AccRideChangeBtn", "left", -3, 0)
	else
		if getglobal("AccRideCallBtn"):IsShown() then
			getglobal("AccSummonBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
		else
			getglobal("AccSummonBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
		end
	end

	--显示变色按钮
	ShowChangeColorBtn()

	RoleSkin_Helper:SetRoleSkinGeniusBuff()

	if (GetInst("MiniUIManager"):IsShown("main_actionExpressionAutoGen")) then
		GetInst("MiniUIManager"):GetCtrl("main_actionExpression"):RefreshView()
	end
end
--function OpenVehicleActioner()
--	print("OpenVehicleActioner")
--	local param = {};
-- 	param.disableOperateUI = false;
-- 	param.isVehicle = true;
--	GetInst("UIManager"):Open("VehicleActioner",param)
--end

--------------------------------------------
------------CSNoticeFrame  begin------------
--------------------------------------------
function CSNoticeFrame_OnShow()
	HideAllFrame("CSNoticeFrame", false);

	if not getglobal("CSNoticeFrame"):IsReshow() and ClientCurGame.setOperateUI then
		ClientCurGame:setOperateUI(true);
	end
end

function CSNoticeFrame_OnHide()
	if not getglobal("CSNoticeFrame"):IsRehide() then
		ClientCurGame:setOperateUI(false);
	end
end

---------------通行证倒计时---------------
function managePassPortCountDown()

	if gPassPortEndTime < 0 then return end

	local nowTime = AccountManager:getSvrTime()
	if nowTime == gPassPortLastTime then return end
	gPassPortLastTime = nowTime
	local timelab = getglobal("PlayMainFramePassPortCountDownFrameTime")
	timelab:SetText(getCountDownTime(gPassPortEndTime))

	local countdown = getglobal("PlayMainFramePassPortCountDownFrame")
	if gPassPortEndTime <= nowTime then
		gPassPortEndTime = -2
		if ClientCurGame and ClientCurGame:isInGame() then
			getglobal("PassPortConfirmBuyFrame"):Show()
		end
		countdown:Hide()
	elseif not countdown:IsShown() then
		countdown:Show()
	end
end

function getCountDownTime(endTime)
	local nowTime = AccountManager:getSvrTime()
	local leftSecond = endTime - nowTime
	if leftSecond <= 0 then return GetS(23041, 0, "00:00:00") end
	
	local day, countdown = 0, ""
	if leftSecond > 86400 then
		day = math.floor(leftSecond / 86400)
		leftSecond = leftSecond%86400
	end

	countdown = countdown .. string.format("%02d:", math.floor(leftSecond/3600))
	leftSecond = leftSecond%3600

	countdown = countdown .. string.format("%02d:", math.floor(leftSecond/60))
	leftSecond = leftSecond%60

	countdown = countdown .. string.format("%02d", leftSecond)

	return GetS(23041, day, countdown)
end


function OnActorHorseMounted(isMounted)
	if GetClientInfo():isMobile() then
		local itemid = CurMainPlayer:getCurToolID()
		local itemDef = ItemDefCsv:get(itemid);
		--小地图显示的时候 或使用点射式的枪时 不显示使用技能按钮 
		if isMounted and not getglobal("MapFrame"):IsShown() and
			(not itemDef or (itemDef ~= nil and itemDef.UseTarget ~= ITEM_USE_GUN)) then
			getglobal("AccRideAttackBtn"):Show()
			getglobal("AccRideAttackLeftBtn"):Show()
		else
			getglobal("AccRideAttackBtn"):Hide()
			getglobal("AccRideAttackLeftBtn"):Hide()
		end
	end
end

-- 显示开局介绍界面
function BattleIntroShow()
	if CurWorld and CurWorld:isGameMakerRunMode() then
		if not IsUIFrameShown("GameStartShow") then
			GetInst("UIManager"):Open("GameStartShow");
		end
	end
end

-- 显示队伍选择界面
function TeamSelectedShow(sec)
	if not CurWorld or not CurWorld:isGameMakerRunMode() then
		return;
	end
	if sec > 0 and not IsUIFrameShown("SelectTeam") then
		GetInst("UIManager"):Open("SelectTeam");
	end
	local teamCtrl = GetInst("UIManager"):GetCtrl("SelectTeam");
	if not teamCtrl then return end

	if sec > 0 then --刷新时间
		teamCtrl:TimeUpdate(sec)
	else --关闭界面
		teamCtrl:RandomBtnClick()
	end
end

-- 更新队伍选择界面
function TeamSelectedUpdate(sec)
	if not CurWorld or not CurWorld:isGameMakerRunMode() then
		return;
	end
	local teamCtrl = GetInst("UIManager"):GetCtrl("SelectTeam");
	if not teamCtrl or not IsUIFrameShown("SelectTeam") then return end

	if sec > 0 then --刷新时间
		teamCtrl:TimeUpdate(sec)
	else --关闭界面
		teamCtrl:RandomBtnClick()
	end
end

-- 关闭开局介绍队伍选择界面
function ClosePreStartGameFrame()
	if IsUIFrameShown("GameStartShow") then
		GetInst("UIManager"):Close("GameStartShow");
	end
	if IsUIFrameShown("SelectTeam") then
		GetInst("UIManager"):Close("SelectTeam");
	end
end

-- 检测skin是否可以使用
function CheckSkinCanUse(skinId)
	if skinId == 0 then
		return true
	end
	if skinId == 324 then
		skinId = 323
	end
	local skinTime = AccountManager:getAccountData():getSkinTime(skinId)
	local id_costDefs = AccountManager:get_skincostdef()[skinId]
	local bVipSkin = false
	if id_costDefs then 
		local key, costDef = next(id_costDefs)
		if costDef.VipType == 1 then
			bVipSkin = true
		end
	end
	local bVip = GetInst('MembersSysMgr'):IsMember()
	if bVipSkin then 
		skinTime = bVip and -1 or 0
	end
	if skinTime == 0 then
		-- 变形金刚变身的皮肤
		if skinId == 91 then
			skinTime = AccountManager:getAccountData():getSkinTime(90)
		end
		if skinId == 97 then
			skinTime = AccountManager:getAccountData():getSkinTime(96)
		end
	end
	return skinTime ~= 0
end


--被语音警告
function VoiceWarnningCallback(warnData)
	print("voice warnning", warnData);
	local isshow =false;
	if  MessageBox ~= nil and GetInst("TeamVocieManage"):isInTeamVocieRoom() then
		isshow = true
	elseif MessageBox ~= nil and ClientCurGame:isInGame() and GYouMeVoiceMgr:isInChannel(warnData.roomId or 0) ~= 0 then
		isshow = true
	end

	if isshow then
		ShowGameTipsWithoutFilter(GetS(10721))
	else
		print("警告: 玩家不在对应的语音房间中！")
	end
end

--被禁言语音功能后的回调
function VoiceMuteCallback(muteInfo)
	print("mute call back", muteInfo);
	--存储禁言数据
	ns_data.muteData[1] = 1;
	ns_data.muteData[2] = muteInfo.mute_time;
	if GetInst("TeamVocieManage"):isInTeamVocieRoom() then
		local delayTime = (ns_data.muteData[2] and {ns_data.muteData[2] - getServerNow()} or {0})[1];
			print("delay time: ", delayTime);
			if delayTime <= 0 then --禁言时效过期
				return;
			end
		 --关闭麦克风
		 --if ClientMgr:getGameData("micswitch")  == self.define.open then
            GYouMeVoiceMgr:setMicrophoneMute(true);
        --end
		
		if MessageBox ~= nil then
			ShowGameTipsWithoutFilter( GetS(10724, string.format("%02d:%02d:%02d", math.floor(delayTime / 3600), math.floor(delayTime % 3600 / 60), delayTime % 3600 % 60)))
		end		
	else
		if ClientCurGame:isInGame() then
			print("server time: ", getServerNow());
			local delayTime = (ns_data.muteData[2] and {ns_data.muteData[2] - getServerNow()} or {0})[1];
			print("delay time: ", delayTime);
			if delayTime <= 0 then --禁言时效过期
				return;
			end
			local isInRoom = (GYouMeVoiceMgr and GYouMeVoiceMgr:isInChannel(muteInfo.roomId or 0) ~= 0);
			--强制退出语音房间
			--退出房间并重新设置相关按钮状态
			if GYouMeVoiceMgr then
				GYouMeVoiceMgr:quitRoom();
			end
			if IsInHomeLandMap and IsInHomeLandMap() then
				getglobal("GVoiceJoinRoomBtn"):Hide();
			elseif not UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.INVITE) then--xyang自定义UI
				getglobal("GVoiceJoinRoomBtn"):Hide();
			else
				getglobal("GVoiceJoinRoomBtn"):Show(); --加入房间按钮显示
			end
			getglobal("MicSwitchBtn"):Hide(); --麦克风按钮隐藏
			getglobal("SpeakerSwitchBtn"):Hide(); --扬声器按钮隐藏
	
			--是同一个房间则显示提示消息
			if isInRoom and MessageBox ~= nil then
				ShowGameTipsWithoutFilter( GetS(10724, string.format("%02d:%02d:%02d", math.floor(delayTime / 3600), math.floor(delayTime % 3600 / 60), delayTime % 3600 % 60)))
			end						   
		else
			print("禁言: 玩家不在对应的语音房间中！")
		end
	end
	
end

--被禁言后点击语音按钮需要弹出弹窗提示，这里不使用原有的IsGameFunctionProhibited，重新写一个
function CheckVoiceMuteStatus()
	if get_game_env() >= 10 then --海外版不检测禁言
		return true
	end
	if ns_data.muteData then
		if ns_data.muteData[1] == nil then
			if GetMuteFlag == 0 then
				SetGetMuteFlag();
				WWW_GetMuteData(true, function(succ)
					SetGetMuteFlag();
					if succ then
						if GetInst("TeamVocieManage"):isInTeamVocieRoom() then
						
						else
							GVoiceJoinRoomBtn_OnClick();
						end
					end
				end);
			end
			return false;
		elseif ns_data.muteData[1] == -1 then
			return true;
		else
			local delayTime = (ns_data.muteData[2] and {ns_data.muteData[2] - getServerNow()} or {0})[1];
			if delayTime <= 0 then --禁言时效过期
				return true;
			end
			MessageBox(4, GetS(10724, string.format("%02d:%02d:%02d", math.floor(delayTime / 3600), math.floor(delayTime % 3600 / 60), delayTime % 3600 % 60)))
			return false;
		end
	end
	return false;
end

function VoiceAccountFreeze(callback, data)
	if IsRoomOwner() then
		threadpool:work(function ()
			AccountManager:sendToClientKickInfo(2);
			if not PlatformUtility:isPureServer() then
				SafeCallFunc(GetInst("ArchiveLobbyRecordManager").CacheAddRecord, GetInst("ArchiveLobbyRecordManager"))
			end
			threadpool:wait(0.5);
			callback(data);
		end)
	else
		callback(data);
	end
end

function SetGetMuteFlag()
	GetMuteFlag = (GetMuteFlag + 1) % 2;
end

--是否可显示坐骑宠物按钮
function HasAnyRideOrPet()
	local allpet = GetInst("HomeLandDataManager"):GetAllPetData()
	local hasAnyIdlePet = false;
	for _, v in ipairs(allpet) do
		local state = GetStateByServerId(v.pet_server_id, v)
		if state == 0 then
			hasAnyIdlePet = true;
			break;
		end
	end

	print("i have pet state: ", tostring(hasAnyIdlePet), #allpet)
	return AccountManager:getAccountData():getHorseNum() > 0 or hasAnyIdlePet;
end

--更新坐骑按钮显示(家园获取宠物信息会有延迟，导致首次进家园没获取到宠物信息，没有显示按钮)
function UpdateAccRideCallBtnShow()
	--强制新手引导过程中不显示坐骑按钮
	local guideControl = GetInst("ForceGuideStepControl")
	local isnotguide = true
	if guideControl and guideControl:IsGuiding() then
		isnotguide = false
	end
	
	-- 任务需要隐藏中，屏蔽按钮重新打开
	if UGCGetInst("GameTaskClientCtrl"):IsHideAllUI() then
		isnotguide = false
	end

	local _show = true 
	if isAbroadEvn() then
		_show = IsNeedShowAccRideCallBtn()
	end
	if _show and not isEducationalVersion and CurWorld and CurWorld:getOWID() ~= NewbieWorldId2 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) and isnotguide then--xyang自定义UI
		--getglobal("AccRideCallBtn"):Show();
	else
		getglobal("AccRideCallBtn"):Hide();
	end
	if CurWorld and CurWorld:getOWID() ~= NewbieWorldId and CurWorld:getOWID() ~= NewbieWorldId2 and GetClientInfo():isMobile() and isnotguide then
		if CurMainPlayer:isFlying() then
			if getglobal("AccRideCallBtn"):IsShown() then
				getglobal("AccRideCallBtn"):Hide();
			end
			--变形按钮
			if getglobal("AccRideChangeBtn"):IsShown() then
				SetAccRideChangeBtnVisible(false);
			end
			--召唤按钮
			if getglobal("AccSummonBtn"):IsShown() then
				SetAccSummonBtnVisible(false);
			end
		else
			if not getglobal("AccRideCallBtn"):IsShown() and CurMainPlayer:getMountType()~=MOUNT_DRIVE and  not MapEditManager:GetIsStartEdit() and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
				local _show = true
				if isAbroadEvn() then
					_show = IsNeedShowAccRideCallBtn()
				end
				if _show and not isEducationalVersion then
					--getglobal("AccRideCallBtn"):Show();
				end
			end
			--变形按钮
			-- local skinId = CurMainPlayer:getSkinID()
			local skinId = GetMyInMapCurSkinId()
			local skinDef = RoleSkinCsv:get(skinId)
			if not getglobal("AccRideChangeBtn"):IsShown() and skinDef and skinDef["ChangeType"] > 0 and UIEditorDef:isMainUICanShow(UIEditorDef.TREE_ITEM_TYPE.MOUNT) then--xyang自定义UI
				SetAccRideChangeBtnVisible(true);
			end
			if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" then
				SetAccSummonBtnVisible(true)
			else
				SetAccSummonBtnVisible(false)
			end
		end
	end

	--变形按钮
	if getglobal("AccRideChangeBtn"):IsShown() then
		if getglobal("AccRideCallBtn"):IsShown() then
			getglobal("AccRideChangeBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
		else
			getglobal("AccRideChangeBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
		end
	end

	if getglobal("AccRideChangeBtn"):IsShown() then--如果变形按钮显示，设置召唤按钮到变形按钮左边
		getglobal("AccSummonBtn"):SetPoint("right", "AccRideChangeBtn", "left", -3, 0)
	else
		if getglobal("AccRideCallBtn"):IsShown() then
			getglobal("AccSummonBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
		else
			getglobal("AccSummonBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
		end
	end
	SetSocUI()
end

-- 游戏内通用增加/消耗星星币接口：联机模式客机请求由主机回调
--[[
	op : 可拓展
	1：新版广告商人-消耗星星币刷新商品列表
	2：新版广告商人-消耗星星币购买商品

	result：1表示操作成功 0表示操作失败
]]
function AddExpResult(op, result)
	ShowLoadLoopFrame(false)
	if result and result == 1 then
		if op == 1 then
			-- 消耗星星币刷新商品列表
			GetInst("UIManager"):GetCtrl("ShopAdNpc"):RefreshGoodListLogicHandle()
		elseif op == 2 then
			-- 消耗星星币购买商品
			GetInst("UIManager"):GetCtrl("ShopAdNpc"):BuyGoodLogicHandle()
		end
	else
		ShowGameTipsWithoutFilter(GetS(555))
	end
end

-- 通知服务器devgoods获取
function DoSSTaskDevGoodsGet(uin)
	SandboxLuaMsg.sendToHost("get_dev_goods", {uin = uin});
end

-- 通知服务器npcshopgoods获取
function DoSSTaskNpcShopGoodsGet(_uin,_itemid)
	SandboxLuaMsg.sendToHost("get_npcshop_goods", {uin = _uin, itemid = _itemid});
end

-- 进入联机游戏，加载完成，收到服务器角色数据之后
function OnMpClientEnterGame()
	uploadSkinInfo()
	uploadHorseInfo()
end

-- 检测skin是否可以使用
function CheckSkinCanUse(skinId)
	if skinId == 0 then
		return true
	end
	local skinTime = AccountManager:getAccountData():getSkinTime(skinId)
	local id_costDefs = AccountManager:get_skincostdef()[skinId]
	local bVipSkin = false
	if id_costDefs then 
		local key, costDef = next(id_costDefs)
		if costDef.VipType == 1 then
			bVipSkin = true
		end
	end
	local bVip = GetInst('MembersSysMgr'):IsMember()
	if bVipSkin then 
		skinTime = bVip and -1 or 0
	end
	if skinTime == 0 then
		-- 变形金刚变身的皮肤
		if skinId == 91 then
			skinTime = AccountManager:getAccountData():getSkinTime(90)
		end
		if skinId == 97 then
			skinTime = AccountManager:getAccountData():getSkinTime(96)
		end
	end
	return skinTime ~= 0
end

-- 上报皮肤信息
function uploadSkinInfo()
	print("call uploadSkinInfo")
	local accountData = AccountManager:getAccountData()
	local Account = accountData.Account
	if not Account then
		return
	end
	local leveldb = accountData.leveldb
	local BillDataSvr = Account.BillDataSvr
    local RoleSkinNum = BillDataSvr.RoleSkinNum
    local RoleSkinInfo = BillDataSvr.RoleSkinInfo

    local tbSkin = {}
    local now = getServerTime()
    local skinCostDef = AccountManager:get_skincostdef()

    local isVipSkin = function(skinId)
    	local id_costDefs = skinCostDef[skinId]
    	if id_costDefs then 
			local key, costDef = next(id_costDefs)
			if costDef.VipType == 1 then
				return 1
			end
		end
		return 0
    end

    print("call uploadSkinInfo 1 now:", now)
	if RoleSkinInfo then
    	for key, tbInfo in pairs(RoleSkinInfo) do
        	local ExpireTime = tbInfo.ExpireTime
        	print("uploadSkinInfo skininfo1:", tbInfo)
        	if ExpireTime < 0 or ExpireTime > now then
            	table.insert(tbSkin, {id = tbInfo.SkinID, vip = isVipSkin(tbInfo.SkinID)})
        	end
    	end
	end

    local exRoleSkinInfo = leveldb.RoleSkinInfo -- leveldb里面的东西
    if exRoleSkinInfo then
        for key, tbInfo in pairs(exRoleSkinInfo) do
        	print("uploadSkinInfo skininfo2:", tbInfo)
            local ExpireTime = tbInfo.ExpireTime
            if ExpireTime < 0 or ExpireTime > now then
                table.insert(tbSkin, {id = tbInfo.SkinID, vip = isVipSkin(tbInfo.SkinID)})
            end 
        end
    end

    print("uploadSkinInfo:", tbSkin)
    local tbInfo = {skins = tbSkin, vip = GetInst('MembersSysMgr'):IsMember()}
    if CurMainPlayer then
		CurMainPlayer:UploadCheckInfo2Host(4, table2json(tbInfo))
    end
end

-- 上报坐骑信息
function uploadHorseInfo()
	if AccountManager and AccountManager.getAccountData then
		local horses = AccountManager:getAccountData():getHorse_all()
		local horse_ids = {}
		for i = 1, #horses do
			local horse_info = horses[i]
			if (isShapeShiftHorse and not isShapeShiftHorse(horse_info.RiderID)) then
				-- 召唤的坐骑ID为 基础坐骑ID + 坐骑等级
				for i=0, horse_info.RiderLevel do
					horse_ids[#horse_ids + 1] = horse_info.RiderID + i
				end
			end
		end
		if CurMainPlayer then
			CurMainPlayer:UploadCheckInfo2Host(5, table2json(horse_ids))
		end
	end
end

-- 游戏内通用交换接口：联机模式客机请求由主机回调
--[[
	type : 可拓展
	1：广告商人-消耗活动道具获得背包道具

	result：1表示操作成功 0表示操作失败
]]
function doExchangeItemResult(type, result)
	ShowLoadLoopFrame(false)
	-- 消耗道具购买商品
	if result == 1 then
		if type == 1 then
			GetInst("UIManager"):GetCtrl("ShopAdNpc"):ShowBuyGood()
		end
	else
		ShowGameTips(GetS(9286), 3)
	end
end

function OnBuyAdShopResult(tabId, goodId, result)
	ShowLoadLoopFrame(false)
	if result and result == 1 then
		GetInst("UIManager"):GetCtrl("ShopAdNpc"):ProcessBuyLogicSuccess(tabId, goodId)
	else
		ShowGameTipsWithoutFilter(GetS(555))
	end
end

-- local telContent = {
--	ret = retCode,
--	mapinfo = mapInfo
--}
-- 客户端发起实际的传送
function startMapTeleport(telContent)
	-- TODO 客户端发起实际的传送
	-- 注意此处传送最好做个延迟，否则房主立刻切换地图其他人可能收不到消息了
	print("call startMapTeleport", telContent)

	-- 社交大厅party game匹配需要先播放随机动画再进游戏
	if isAbroadEvn() and SocialRoomMatchService and SocialRoomMatchService:IsPartyGameMatching() then
		SocialRoomMatchService:OnReceiveMatchFinish(telContent);
		return;
	end

	-- 普通联机模式下且当前玩家为房主 需延迟传送
	local playerUin = AccountManager:getUin();
	if AccountManager:getMultiPlayer() > 0 and GetIsHost(playerUin) then
		ShowLoadLoopFrame2(true, "startMapTeleport", 3, GetS(111717));
		threadpool:delay(2, function()
			ShowLoadLoopFrame2(false, "startMapTeleport");
			AccountManager:sendToClientKickInfo(2);
			GetInst("CloudPortalInterface"):StartTransfer(telContent);
		end)
	else --其他情况下直接传送
		GetInst("CloudPortalInterface"):StartTransfer(telContent);
	end
end

function PlayMainActivityAwakenBtn_OnShow()
end

local tick = 0
--定时检测位置移动信息
function stopActorInviteAct()
	tick = tick + 1
	if tick < 9 or not CurMainPlayer or not CurMainPlayer.getLocoMotion or not CurMainPlayer.getBody then
		return;
	end
 	tick = 0
	local loc = CurMainPlayer:getLocoMotion();
	local body = CurMainPlayer:getBody();
	local x,y,z = loc.m_Position.x, loc.m_Position.y, loc.m_Position.z;
	local lastPos = {x = 0, y = 0, z = 0};
	lastPos.x = loc.m_TickPosition.m_LastTickPos.x;
	lastPos.y = loc.m_TickPosition.m_LastTickPos.y;
	lastPos.z = loc.m_TickPosition.m_LastTickPos.z;
	--联机模式下 位置移动 且正在播放互动动作
	if AccountManager and AccountManager:getMultiPlayer() > 0 and
		(x ~= lastPos.x or y ~= lastPos.y or z ~= lastPos.z) and body.clearAction 
			and body.isPlayingSkinAct and body:isPlayingSkinAct() then
		body:clearAction();
	end
end


function PlayMainRoomMatchBtn_OnClick()
	GetInst("MatchPartyInterface"):OpenReMatchUI()
end


--显示收到的好友请求
function Show_PlayMainAskAddFriend()
	if ClientCurGame and ClientCurGame:isInGame() and (not friendservice.showAskAddFriend) and friendservice.popMsg[1] then
		-- ShowGameTips("GetGameSetData_IgnoreFriendAdd()=" .. GetGameSetData_IgnoreFriendAdd())
		if GetGameSetData_IgnoreFriendAdd() == 2 or CurWorld:isGameMakerMode() then -- 关闭好友申请开关
			RemoveAskAddFriendPop()
			return
		end
		if getkv("noShowAddFriendFrameDate") == nil or CanShowAskAddFriendFrame() then
			friendservice.showAskAddFriend = true
			
			GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/common_comp"},"main_addfriendAutoGen")
			local data = friendservice.popMsg[1]
			data.disableOperateUI = true
			GetInst("MiniUIManager"):OpenUI("main_addfriend","miniui/miniworld/userInfoInteract","main_addfriendAutoGen",data)
		end
	end
end

--多重换装-获取变色皮肤ID
function GetChangeColorSkinID()
	local changeColorSkinID = {}
	if CurMainPlayer then
		-- local baseSkinId = CurMainPlayer:getSkinID()--进入地图内当前显示的皮肤ID
		local baseSkinId = GetMyInMapCurSkinId()
		local skinDef = RoleSkinCsv:get(baseSkinId)
		--需要是变色皮肤，并且需要解锁
		if skinDef and skinDef.ChangeSkinID and skinDef.ChangeSkinID ~= "" then--有变色的皮肤ID
			local changeSkinTab = split(skinDef.ChangeSkinID,',')
			for _, value in ipairs(changeSkinTab) do
				local splitData = split(value,'_')
				if next(splitData) and splitData[2] then
					table.insert(changeColorSkinID, tonumber(splitData[2]) or 0)
				end
			end	 
		end
	end
	return changeColorSkinID
end

function CanShowChangeColorBtn(skinid)
	if skinid == 323 or skinid == 324 then
		return false
	end
	return true
end

--多重换装-显示变色按钮
--根据ChangeSkinID字段判断是否是变色皮肤
function ShowChangeColorBtn()
	if ClientCurGame and ClientCurGame:isInGame() then
		local ishad = false --是否拥有
		local changeColorSkinID = GetChangeColorSkinID()
		for _, value in ipairs(changeColorSkinID) do
			local skinTime = AccountManager:getAccountData():getSkinTime(value)--变色皮肤
			if skinTime == -1 then--变色皮肤已解锁
				ishad = true
				break
			end
		end

		local IllusionEffect = ""
		if CurMainPlayer then
			-- local baseSkinId = CurMainPlayer:getSkinID()--进入地图内当前显示的皮肤ID
			local baseSkinId = GetMyInMapCurSkinId()
			local skinDef = RoleSkinCsv:get(baseSkinId)
			if skinDef then
				IllusionEffect = skinDef.IllusionEffect  --幻化皮肤
			end
			if ishad then
				ishad = CanShowChangeColorBtn(baseSkinId)
			end
		end

		if ishad and IllusionEffect == ""  then
			getglobal("AccChangeColorBtn"):Show()
			if getglobal("AccRideChangeBtn"):IsShown() then--如果变形按钮显示，设置变色按钮到变形按钮左边
				getglobal("AccChangeColorBtn"):SetPoint("right", "AccRideChangeBtn", "left", -3, 0)
			else
				if getglobal("AccRideCallBtn"):IsShown() then
					getglobal("AccChangeColorBtn"):SetPoint("right", "AccRideCallBtn", "left", -3, 0)
				else
					getglobal("AccChangeColorBtn"):SetPoint("center", "AccRideCallBtn", "center", 0, 0)
				end
			end
		
			local showEffect = true
			local noshowDate = getkv("first_change_color_skin_day")
			if noshowDate then
				local secondOfToday = os.time({day=noshowDate.day, month=noshowDate.month,year=noshowDate.year, hour=23, min=59, sec=59})--当天24点
				if AccountManager:getSvrTime() < secondOfToday then
					showEffect = false
				else
					showEffect = true
				end
			end
			if showEffect then
				getglobal("AccChangeColorBtnEffect"):SetUVAnimation(100, true)
				setkv("first_change_color_skin_day", os.date('*t',getServerTime()))
			else
				getglobal("AccChangeColorBtnEffect"):Hide()
			end
		else
			getglobal("AccChangeColorBtn"):Hide()	
		end
	end
end

--多重换装-点击变色按钮
function AccChangeColorBtn_OnClick()

	local seatSkinId = 0
	local seatID = AccountManager:avatar_seat_current()
	local seatInfo = GetInst("ShopDataManager"):GetSkinSeatInfo(seatID)
	if not seatInfo then
		seatInfo = GetInst("ShopDataManager"):GetPlayerUsingSeatInfo();
	end
	if seatInfo then
		seatSkinId = GetInst("AvatarBodyManager"):GetCurAvtarBodyModel(seatInfo.def)
	end

	if CurMainPlayer and ClientCurGame:isInGame() then
		-- local skinId = CurMainPlayer:getSkinID()--当前的皮肤
		local skinId = GetMyInMapCurSkinId()
		local skinDef = RoleSkinCsv:get(skinId)
		if skinDef then		
			local changeColorSkinID = 0
			--先判断变色皮肤是否已拥有
			changeColorSkinID = skinDef.ChangeOrder or 0 --变色的皮肤ID
			if changeColorSkinID > 0 then
				local skinTime = AccountManager:getAccountData():getSkinTime(changeColorSkinID)
				if skinTime ~= -1 then--没有解锁再查找关联的所有变色皮肤
					local allChangeColorSkin = GetChangeColorSkinID() 
					for _, value in ipairs(allChangeColorSkin) do
						local skinTime = AccountManager:getAccountData():getSkinTime(value)--变色皮肤
						if skinTime == -1 then--变色皮肤已解锁
							changeColorSkinID = value
							break
						end
					end
				end
				local model = AccountManager:getRoleModel()
				local index = CurMainPlayer:composePlayerIndex(model,AccountManager:getAccountData():getGenuisLv(model),changeColorSkinID)
				local player = ClientCurGame:getPlayerByUin(AccountManager:getUin())

				if player then
					if seatSkinId ~= 0 and seatSkinId == changeColorSkinID then
						local seatID = AccountManager:avatar_seat_current()
						if seatID and seatID > 0 then
							local code, uSeatInfo = AccountManager:avatar_seat_info(seatID)
							if code == ErrorCode.OK then
								CurMainPlayer:changePlayerModel(index, player:getBody():getMutateMob(), tostring(seatID or ""))
							else
								CurMainPlayer:changePlayerModel(index,player:getBody():getMutateMob())
							end
						else
							CurMainPlayer:changePlayerModel(index,player:getBody():getMutateMob())
						end
					else
						CurMainPlayer:changePlayerModel(index,player:getBody():getMutateMob())
					end
				end

				--重置快捷动作显示
				UpdataActionShow()	
				if getglobal("AccChangeColorBtnEffect"):IsShown() then
					getglobal("AccChangeColorBtnEffect"):Hide()
				end
			end
		end
	end
end

-----全民创造节引流-----
local activityVisualCfgMgrConfigBtnLink = nil
function ShowToActivityFrame()
	getglobal("ToActivityFrame"):Hide()
	local showTime = getkv("__EntryToActivityShow__")
	local isCanShow = (not showTime) or (getServerTime() > showTime)
	if isCanShow then
		local worldDesc = AccountManager:getCurWorldDesc();
		if worldDesc then
			--冒险模式：不展示
			--创造模式：进入创造模式时展示，冒险切换至创造模式时展示（切换回则不展示）
			--开发者模式：进入开发者编辑模式时展示，玩法模式切换至编辑模式时展示（切换回则不展示） （Worldtype =1 4）
			if worldDesc.worldtype==1 or worldDesc.worldtype==4 then
				GetEntryToActivityConfig()
			else
				getglobal("ToActivityFrame"):Hide()
			end
		end
	end
end
function GetEntryToActivityConfig()
	local callfun = function(config)
		if not config then
			return
		end
		if not (ClientCurGame and ClientCurGame:isInGame()) then
			return
		end

		if config.EntryToActivity then
			local isInTime = CheckActivityTime(config.EntryToActivity.rangeTime)
			local isInSetVer = CheckActivityVersion(config.EntryToActivity.minVersion, config.EntryToActivity.maxVersion)
			
			local toActivityFrame = getglobal("ToActivityFrame")
			if isInTime and isInSetVer then
				toActivityFrame:Show()
				local content = config.EntryToActivity.content or ""
				content = "#ccccccc"..content.."#n"
				local decTex = getglobal("ToActivityFrameDec")
				decTex:SetText(content)
				local btnTitle = config.EntryToActivity.btnTitle or ""
				getglobal("ToActivityFrameJumpBtnTitle"):SetText(btnTitle)
				local btnLink = config.EntryToActivity.btnLink or ""
				activityVisualCfgMgrConfigBtnLink = btnLink

				--重设宽高
				local h = decTex:GetTotalHeight()
				decTex:SetHeight(h)
				local frameH = toActivityFrame:GetHeight()
				if h > frameH + 10 then
					toActivityFrame:SetHeight(h+10)
				end
			else
				toActivityFrame:Hide()
			end
		end
	end
	local key = "EntryToActivity"
	local tb = GetInst("VisualCfgMgr"):GetCfg(key) or {}
	if not tb[key] then
		GetInst("VisualCfgMgr"):ReqCfg(key, function (code, ret)
			if code == 0 then
				tb = GetInst("VisualCfgMgr"):GetCfg(key) or {}

				if tb[key] and callfun then
					callfun(tb[key])
				end
			end
		end)
	else
		callfun(tb[key])
	end
end

function JumpToActivityBtn_OnClick()
	if activityVisualCfgMgrConfigBtnLink then
		local num = tonumber(activityVisualCfgMgrConfigBtnLink)
		if num and g_jump_ui_switch[num] then
			local param = {}
			if num == 76 then	--全民创造节
				param.comeFrom = 2
			end
			g_jump_ui_switch[num](param)
		else
			open_http_link(activityVisualCfgMgrConfigBtnLink)
		end
	end
end
function ToActivityClose_OnClick()
	getglobal("ToActivityFrame"):Hide()

	local nowNum = getServerTime()
	local t = os.date("*t", nowNum + 86400)
	local nextShow = os.time({year=t.year,month=t.month,day=t.day,hour=0,min=0,sec=0})
	setkv("__EntryToActivityShow__", nextShow)
end

function CheckActivityTime(configRangeTime)
	--判断时间
	if not configRangeTime or type(configRangeTime)~="table" or #configRangeTime~=2 then
		return
	end
	local startTime = tonumber(configRangeTime[1]/1000)
	local endTime = tonumber(configRangeTime[2]/1000)
	if startTime and endTime then
		local now =  getServerTime()
		---配置的数据时间戳是毫秒
		local isInTime = now >= startTime and now <= endTime
		return isInTime
	end
	return nil
end

function CheckActivityVersion(_minVersion, _maxVersion)
	--检测版本
	local curVersion = GetClientInfo():GetClientVersion();
	local _min = _minVersion
	local _max = _maxVersion
	local minVer = (_min and _min~="" and  GetClientInfo():clientVersionFromStr(_min)) or nil
	local maxVer = (_max and _max~="" and  GetClientInfo():clientVersionFromStr(_max)) or nil

	minVer = tonumber(minVer)
	maxVer = tonumber(maxVer)
	
	local isInSetVer = false
	if minVer and maxVer then
		isInSetVer = curVersion>=minVer and curVersion<=maxVer
	elseif minVer and (not maxVer) then
		isInSetVer = curVersion>=minVer
	elseif (not minVer) and maxVer then
		isInSetVer = curVersion<=maxVer
	elseif (not minVer) and (not maxVer) then
		isInSetVer = true
	end
	return isInSetVer
end
--------------------------------------------------------------------------------

function PlayMainHP_OnHide()
	if getglobal("PlayerArmorBar") then
		--getglobal("PlayerArmorBar"):Hide();
	end
	HpBarFrame_ShowArmorBar(false)
end

function PlayMainHP_OnShow()
	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
		--getglobal("PlayerArmorBar"):Show();
		HpBarFrame_ShowArmorBar(true)
	else
		--getglobal("PlayerArmorBar"):Hide();
		HpBarFrame_ShowArmorBar(false)
	end
end

function PlayMainStrength_OnHide()
	if getglobal("PlayerPerseveranceBar") then
		--getglobal("PlayerPerseveranceBar"):Hide();
	end
	HpBarFrame_ShowPerBar(false)
end

function PlayMainStrength_OnShow()
	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
		--getglobal("PlayerPerseveranceBar"):Show();
		HpBarFrame_ShowPerBar(true)
	else
		--getglobal("PlayerPerseveranceBar"):Hide();
		HpBarFrame_ShowPerBar(false)
	end
end
--------------------------------------------------------------------------------
function ShowEnterMapAnim()
	threadpool:work(function ()
		while true do
			if not getglobal("PlayMainFrame"):IsShown() or not CurMainPlayer or getglobal("LoadingFrame"):IsShown() or not CurMainPlayer:getBody():checkEntityRes() then
				threadpool:wait(0.01)
			else
				if isAbroadEvn() then	
					CurMainPlayer:playAnim(SEQ_ENTERWORLD)
					threadpool:delay(5.5, function()
						if CurMainPlayer and CurMainPlayer:getBody() and CurMainPlayer:getBody():hasAnimPlaying(SEQ_ENTERWORLD) then
							CurMainPlayer:playAnim(SEQ_STAND)
						end
					end)
					return
				else
					CurMainPlayer:playAnim(SEQ_ENTERWORLD)
					return
				end
			end
		end
	end)
end

-- 专门用于隐藏老UI Tips的鼠标离开事件
function OldUITipsHackItem_MouseLeave()
	--编辑模式
	if UGCModeMgr and UGCModeMgr:IsEditing() then
		-- 隐藏用于处理MouseLeave事件的老UI面板
		-- 尝试关闭Tips
		if getglobal("MItemTipsFrame"):GetClientID() > 0 then return end	--按下了alt
		CurSelectGridIndex = -1
		HideMTipsInfo();
		--新资源tips弹框
		GetInst('SceneEditorMsgHandler'):dispatcher(SceneEditorResourceDef.event.resource_item_tips_close)
	end
end
function DisabledExecute()
	if CurMainPlayer and CurMainPlayer:getLivingAttrib()then
		if CurMainPlayer:getLivingAttrib():hasBuff(1021) or CurMainPlayer:getLivingAttrib():getBuffEffectBankInfo(2034) then
			return true
		end
	end

	return false
end

-----------------皮肤召唤---------------------
function SetAccSummonBtnVisible( visible)
	MiniLog("SetAccSummonBtnVisible ", visible, debug.traceback())
	if visible then
		getglobal("AccSummonBtn"):Show()
	else
		getglobal("AccSummonBtn"):Hide()
	end
	if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
		GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(getglobal("AccSummonBtn"), visible)
	end
end

function AccSummonBtn_OnClick()
	if CurMainPlayer then
		if CurMainPlayer:isSleeping() or CurMainPlayer:isRestInBed() then
			CurMainPlayer:dismountActor();
			if CurMainPlayer:isShapeShift() then
				getglobal("AccRideAttackBtn"):Hide()
				getglobal("AccRideAttackLeftBtn"):Hide()
			end
		end

		-- local skinId = CurMainPlayer:getSkinID()
		local skinId = GetMyInMapCurSkinId()
		local skinDef = RoleSkinCsv:get(skinId)
		if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" then
			local summonList = split(skinDef.SummonID, ",")
			AvatarSummonIndex = AvatarSummonIndex + 1
			AvatarSummonIndex = AvatarSummonIndex > #summonList and 0 or AvatarSummonIndex
			if AvatarSummonIndex > 0 then
				--展示召唤装扮
				local summonID = summonList[AvatarSummonIndex]
				local def = SummonDefCsv:get(summonID);
				if def then
					local summonTip = def.SummonTip
					local summonAct = def.SummonAction
					local summoneffect = def.SummonEffect
					local summonSound = def.SummonSound
					CurMainPlayer:getBody():setAnimSwitchIsCall(true)
					ActorComponentCallModule(CurMainPlayer,"EffectComponent","playBodyEffectByName",summoneffect)
					if CurMainPlayer then
						CurMainPlayer:switchActView()
						threadpool:delay(0.01,function()
							CurMainPlayer:playAnim(SEQ_AVATAT_SUMMON)
						end)
					end 
				end
			else
				--隐藏召唤装扮
				ShowGameTips(GetS(30515))
				if CurWorld and CurWorld:isRemoteMode() then
					local params = {objid = CurMainPlayer:getObjId(),summonid = 0}
					SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.BUZZ.AVATAR_SUMMON_TOHOST, params)
				else
					CurMainPlayer:avatarSummon(0)
				end
			end
		end
	end
end

function AvatarSummonEvent()
	if not CurMainPlayer then return end
	-- local skinId = CurMainPlayer:getSkinID()
	local skinId = GetMyInMapCurSkinId()
	local skinDef = RoleSkinCsv:get(skinId)
	if skinDef and skinDef.SummonID and skinDef.SummonID ~= "" then
		local summonList = split(skinDef.SummonID, ",")
		if AvatarSummonIndex > 0 then
			CurMainPlayer:getBody():setAnimSwitchIsCall(false)
			local summonID = summonList[AvatarSummonIndex]
			local def = SummonDefCsv:get(summonID);
			if def then
				local summonTip = def.SummonTip
				local summoneffect = def.SummonEffect
				local summonSound = def.SummonSound
				if ActorComponentCallModule then
					ActorComponentCallModule(CurMainPlayer,"SoundComponent","playSound","summon."..summonSound, 1.0, 1.0)
				else
					CurMainPlayer:playSound("summon."..summonSound, 1.0, 1.0)
				end
				ActorComponentCallModule(CurMainPlayer,"EffectComponent","stopBodyEffectForTrigger",summoneffect)
				if CurWorld and CurWorld:isRemoteMode() then
					local params = {objid = CurMainPlayer:getObjId(),summonid = summonID}
					SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.BUZZ.AVATAR_SUMMON_TOHOST, params)
				else
					CurMainPlayer:avatarSummon(summonID)
				end
				ShowGameTips(summonTip)
			end
		end
	end
end

function AutoRoleEquip(worldid, uin)
	local cloud = "NotCloud"
	if ClientMgr and ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then
		cloud = "Cloud"
	end
	local ret = getkv("FristEnterWorldAutoRoleEquip"..worldid..uin..cloud) or false	
	if ret then
		return
	end		
	local success = false
	for i = 1, MAX_SHORTCUT do
		local grid_index = i + ClientBackpack:getShortcutStartIndex() - 1
		local itemid = ClientBackpack:getGridItem(grid_index)
		
		--装备类型(8->头, 9->胸甲, 10->腿, 11->鞋子, 16->背部)，对应格子(8000->头, 8001->胸甲, 8002->腿, 8003->鞋子, 16->背部)
		if itemid > 0 then
			local itemDef = ToolDefCsv:get(itemid)
			if itemDef then
				local itemType = itemDef.Type
				if itemType == 16 then
					itemType = 12
				end
				local index = -1
				if itemType >= 8 and itemType <= 12 then
					if itemType == 8 then
						index = 0
					elseif itemType == 9 then
						index = 1
					elseif itemType == 10 then
						index = 2
					elseif itemType == 11 then
						index = 3
					end
					if index >= 0 then
						-- CurMainPlayer:moveItem(grid_index, 7000, 1)
						-- CurMainPlayer:moveItem(7000, 8000+index, 1)
						CurMainPlayer:moveItem(grid_index, 8000+index, 1)--改为直接穿装备
						success = true
					end							
				end			
			end	
		end			
	end

	if success then
		setkv("FristEnterWorldAutoRoleEquip"..worldid..uin..cloud, true)
	end	
end

-----------------------------------------------------
local PtrRotation = {
    [1] = { angle = -90; };
    [2] = { angle = 0; };
    [3] = { angle = 90; };
    [4] = { angle = 150; };
}

local function isShowWaterPressureGuage()
    if MainPlayerAttrib == nil then return false end

	if WorldMgr and not WorldMgr:canWaterPressAffectPlayer() then
		return false
	end

    local itemid = MainPlayerAttrib:getEquipItem(EQUIP_SLOT_TYPE.EQUIP_HEAD)

    -- 潜水面罩，和高级潜水面罩显示水压仪表
    if itemid == 11642 or itemid == 11645 then
        return true
    end

    return false;
end

function CheckShowWaterPressureGuage()
    local showWaterPressureGauges = isShowWaterPressureGuage()
    if showWaterPressureGauges then
        getglobal("WaterPressureGauges"):Show()
    else
        getglobal("WaterPressureGauges"):Hide()
    end
end

function CheckShowThermometerFrame()

	--强制新手引导期间不显示温度计
	local guideControl = GetInst("ForceGuideStepControl")
	local isnotguide = true
	if guideControl and guideControl:IsGuiding() then
		isnotguide = false
	end	
    --显示温度计FGUI renjie
	local isShowTempMode = (WorldMgr and WorldMgr:isShowTempMode()) or false
	local isTemperatureActive=(WorldMgr and WorldMgr:GetWorldTemperatureActive()) or false
	-- MiniLog("isShowTempMode="..isShowTempMode)
	-- MiniLog("isTemperatureActive="..isTemperatureActive)
	if(isShowTempMode==true and isTemperatureActive==true and isnotguide)then--只有在冒险模式下才显示温度计
		if MainPlayerAttrib == nil then 
			GetInst("MiniUIManager"):CloseUI("thermometerFrameAutoGen")
			return 
		end
		if( not GetInst("MiniUIManager"):IsShown("thermometerFrameAutoGen")  )then
			GetInst("MiniUIManager"):OpenUI("thermometerFrame","miniui/miniworld/adventure","thermometerFrameAutoGen",{})
		else
			if GetInst("MiniUIManager"):GetCtrl("thermometerFrame") then
				GetInst("MiniUIManager"):GetCtrl("thermometerFrame"):onShow()
			end
		end
	else
		if(GetInst("MiniUIManager"):IsShown("thermometerFrameAutoGen"))then
			GetInst("MiniUIManager"):CloseUI("thermometerFrameAutoGen")
			return 
		end
	end
end

function CheckShowDigProFrame()--显示挖掘进度条FGUI renjie
	if(CurMainPlayer==nil)then
		return
	end
    --显示挖掘进度条FGUI renjie
	local playerControl =tolua.cast(CurMainPlayer,"PlayerControl")
	local isShow=false
	local Ctrl=nil
	if( CurMainPlayer:getPCControl() )then
		Ctrl = CurMainPlayer:getPCControl()
	elseif( CurMainPlayer:getTouchControl() )then
		Ctrl= CurMainPlayer:getTouchControl()
	end 
	if(Ctrl and Ctrl:isShowDigProgress())then
		isShow=true 
	end
	if(isShow==true and playerControl and playerControl:getDigProgress()>=0)then
		if(MainPlayerAttrib)then
			if( not GetInst("MiniUIManager"):IsShown("digproFrameAutoGen")  )then
				GetInst("MiniUIManager"):OpenUI("digproFrame","miniui/miniworld/adventure","digproFrameAutoGen",{})
			else
				if GetInst("MiniUIManager"):GetCtrl("digproFrame") then
					GetInst("MiniUIManager"):GetCtrl("digproFrame"):onShow()
				end
			end
		else
			if(GetInst("MiniUIManager"):IsShown("digproFrameAutoGen"))then
				GetInst("MiniUIManager"):CloseUI("digproFrameAutoGen")
				return 
			end
		end
	else
		if(GetInst("MiniUIManager"):IsShown("digproFrameAutoGen"))then
			GetInst("MiniUIManager"):CloseUI("digproFrameAutoGen")
			return 
		end
	end
end


--替换描述文字中的@item_xxxx#
function ReplaceItemIdToItemName(desc)
	if desc == nil or desc == ""  then
		return ""
	else
		local _,count = desc:gsub("%@item%_","")
		print(count)
		local resultDesc = desc
		if count > 0 then
			for i=1, count do
				local _,resultCount = resultDesc:gsub("%@item%_","")
				if resultCount > 0 then
					local itemIdString = resultDesc:match("%@item%_.-%#")  --提取包含头尾的字符串
					if itemIdString ~= nil and itemIdString ~= "" and string.len(itemIdString) > 7 then
						local itemId = itemIdString:sub(7, string.len(itemIdString)-1) --去除头尾
						local itemDef = ItemDefCsv:get(itemId)
						local repleaseName = itemDef and itemDef.Name or "";
						repleaseName = "%#c0aba1d"..repleaseName.."%#n"
						local repleaseDesc = string.gsub(resultDesc,itemIdString,repleaseName)
						resultDesc = repleaseDesc;
					end
				end
			end
		end
		return resultDesc
	end
end



function WaterPressureGauges_onTick()
    if MainPlayerAttrib == nil then return end

    local waterpressure = MainPlayerAttrib:getWaterPressure()
    if waterpressure > 4 then waterpressure = 4 end
    if waterpressure < 1 then waterpressure = 1 end

    local config = PtrRotation[waterpressure]
    local waterpressureptr = getglobal("WaterPressPtr")
    waterpressureptr:SetAngle(config.angle)
end

function MiscSwitchBtnTemplate_OnClick()

end

function MiscTip_OnLoad()

end

function MiscTip_OnShow()
	getglobal("MiscTipTipBg"):SetBlendAlpha(0.6);
	getglobal("MiscTipTipBgArrow"):SetBlendAlpha(0.6);
end
function MiscTip_OnUpdate()
	
end

function SpeakerTip_OnLoad()

end

function SpeakerTip_OnShow()
	getglobal("SpeakerTipTipBg"):SetBlendAlpha(0.6);
	getglobal("SpeakerTipTipBgArrow"):SetBlendAlpha(0.6);
end

function SpeakerTip_OnUpdate()
	
end

function MiscFrameTip_OnShow()
	local richText = getglobal("MiscFrameTipRichTxt")
	richText:SetText(GetS(111286))
end
function MiscFrameTip_OnUpdate()

end

function MiscTipMiscCtrl_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):MiscTipMiscCtrl_OnClick()
	end
end

function MiscTipMiscAll_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):MiscTipMiscAll_OnClick()
	end
end

function MiscTipMiscTeam_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):MiscTipMiscTeam_OnClick()
	end
end
function SpeakerTipSpeakerCtrl_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):SpeakerTipSpeakerCtrl_OnClick()
	end
end
function SpeakerTipSpeakerAll_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):SpeakerTipSpeakerAll_OnClick()
	end
end
function SpeakerTipSpeakerTeam_OnClick()
	if GetInst("GameVoiceManage") then
		GetInst("GameVoiceManage"):SpeakerTipSpeakerTeam_OnClick()
	end
end


--显示操作发射器UI
function ShowUseEmitterUI()
--	if CurMainPlayer:isPlayerControl() then
	--	if CurMainPlayer:openEmitterContainer() then
	if not GetInst("MiniUIManager"):IsShown("ManualEmitterMain")then
		GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common"}, "ManualEmitterMainAutoGen")
		GetInst("MiniUIManager"):OpenUI("ManualEmitterMain", 
		"miniui/miniworld/adventure", "ManualEmitterMainAutoGen", {disableOperateUI = true});
	else
		local ctrl = GetInst("MiniUIManager"):GetCtrl("ManualEmitterMain");
		if ctrl then
			ctrl:UpdateItemIcon(1, 1, 1);
		end
	end
	--	end
	--end
end

function HideUseEmitterUI()
	--if CurMainPlayer:isPlayerControl() then
		GetInst("MiniUIManager"):CloseUI("ManualEmitterMainAutoGen");
	--end
end

function PlayerMiniMapAddMarkData(x, z)
	if not CurWorld then
		return;
	end
	local ctrl1 = GetInst("MiniUIManager"):GetCtrl("main_minimap")
    if(ctrl1)then
        ctrl1:addCustomMarker("ui://c_ingame/icon_spawnMob",GetS(85668),x,z,4, false)
	else
		--没打开过打开一次,这个一般只会主机触发
		PixelMapInterface:ShowCompass();
		local ctrl1 = GetInst("MiniUIManager"):GetCtrl("main_minimap")
    	if(ctrl1)then
        	ctrl1:addCustomMarker("ui://c_ingame/icon_spawnMob",GetS(85668),x,z,4, false)
		end
		PixelMapInterface:HideCompass();
	end
end

function SurviveGameEnterSubscibeMsgHandle()
	local function playerCloseUINet(content)
		local ui = content.uiName;
		if ui == "MobInteractBackpack" then
			GetInst("UIManager"):Close("MobInteractBackpack");
		end
	end
	netHandle.playerCloseUIHandle = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.PLAYER_CLOSE_UI, playerCloseUINet)
	--------------------------------------------------------------------------------
	local function actorShowExchangeItem(content)
		local actorId = content.actorId
		if CurWorld and actorId then
			local mob = GetWorldActorMgr(CurWorld):findMobByWID(actorId);
			if mob then
				local exchangeItem = content.exchangeItem or 0;
				local exchangeNum = content.exchangeNum or 0;
				local saleItem = content.saleItem or 0;
				local saleNum = content.saleNum or 0;
				local tick = content.tick or -1;
				local isActor = content.isActor;
				if isActor == nil then
					isActor = false;
				end
				mob:getBody():setHeadExchangeDisplayIcon(exchangeItem, saleItem, isActor, exchangeNum, saleNum, tick);
			end
		end
	end
	netHandle.actorShowExchangeItemHandle = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.SHOW_EXCHANGE_ITEM_ICON, actorShowExchangeItem)
	--------------------------------------------------------------------------
	local function actorShowHeadIconByPath(content)
		local actorId = content.actorId
		if CurWorld and actorId then
			local mob = GetWorldActorMgr(CurWorld):findMobByWID(actorId);
			if mob then
				local imageResPath = content.imageResPath or 0;
				local imageResUVName = content.imageResUVName or 0;
				local imageWidth = content.imageWidth or 0;
				local imageHeight = content.imageHeight or 0;
				mob:getBody():setHeadIconByPath(imageResPath, imageResUVName, imageWidth, imageHeight);
			end
		end
	end
	netHandle.actorShowHeadIconByPathHandle = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.SHOW_HEAD_ICON_BY_PATH, actorShowHeadIconByPath)
	--------------------------------------------------------------------------------
	local playerExploreTask = function()
		if ClientCurGame and ClientCurGame:isInGame() then
			if not CurMainPlayer then
				return;
			end
			local x,y,z = CurMainPlayer:getPosition(0,0,0)
			--检测水平移动距离
			if not playerExploreTaskMgr.lastPos then      --上次检测的位置
				playerExploreTaskMgr.lastPos = {x=x, y=y, z=z};
			end
			local lastPos = playerExploreTaskMgr.lastPos;

			local moveDist =  math.floor(math.sqrt( (x-lastPos.x)*(x-lastPos.x)+(z-lastPos.z)*(z-lastPos.z) ) / 100);
			playerExploreTaskMgr.lastPos = {x=x, y=y, z=z};
			--不同地形的移动距离
			if CurWorld and CurWorld.getBiomeType then  
				local biometype = CurWorld:getBiomeType(math.floor(x / 100), math.floor(z / 100))
				userTaskReportedGlobal(-1, UserTaskReportType_EXPLORE, biometype, moveDist, 60);
			end
		end
	end
	local px,py,pz = CurMainPlayer:getPosition(0,0,0);
	playerExploreTaskMgr.lastPos = {x=px, y=py, z=pz};
	playerExploreTaskMgr.timer = threadpool:timer(2592000, 60, playerExploreTask, nil);

	netHandle.surviveReport = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.SURVIVE_REPORT, SurviveTaskReportInGame)
	--------------------------------------------------------------------------
	local function drfitBottleGet(content)
		local uin =  AccountManager:getUin();
		local _, str =  DriftBottleGetContent(nil,uin);
		local retContent = 
		{
			str = str,
			uin = uin,
			spawnType = content.spawnType;
			x = content.x;
			y = content.y;
			z = content.z;
		}
		SandboxLuaMsg.sendToHost(SANDBOX_LUAMSG_NAME.Survive.DRIFTBOTTLE_GETHOST, retContent);
	end
	netHandle.driftBottleGet = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.DRIFTBOTTLE_GET, drfitBottleGet);
	--------------------------------------------------------------------------
	local function DragonFlowerAttack(content)
		if not CurWorld then
			return;
		end
		if not WorldMgr then
			return;
		end
		if not content then
			return;
		end
		local x = content.x;
		local y = content.y;
		local z = content.z;
		local angle = content.angle;
		local pos = WCoord:new_local();
		pos.x = x;
		pos.y = y;
		pos.z = z;
		GetSandboxActorSubsystem():dragonFlowerAttack(angle, pos, CurWorld._cptr);
	end
	netHandle.voidDragonFlowerAttack = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.VOID_DRAGON_FLOWER_ATTACK, DragonFlowerAttack);
	---------------------------------------------------------------------------
	local function CitySearchCallBack(content)
		if not CurWorld then
			return;
		end
		if not content then
			return;
		end
		if content.data then
			SurviveGameModHandleMiniMap(content.data);
		end
	end
	netHandle.citySearchCallBack = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.CITY_SEARCH_CLIENT, CitySearchCallBack);
	---------------------------------------------------------------------------
	local function TreasureOpenCallBack(content)
		if not CurWorld then
			return;
		end
		if not content then
			return;
		end
		if content.x and content.y and content.z then
			if content.y < 0 then 
				ShowGameTips(GetS(85667))
				return;
			end
			--if player:isPlayerControl() then
				GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/c_ingame"}, "TreasureMainMapAutoGen")
				GetInst("MiniUIManager"):OpenUI("TreasureMainMap", 
				"miniui/miniworld/treasureMap", "TreasureMainMapAutoGen", {x = content.x, z = content.z, realPos = true});
			--end
		else
			ShowGameTips(GetS(85667));			
		end
	end
	netHandle.treasureOpenCallBack = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.TREASURE_USE_CLIENT, TreasureOpenCallBack);
	-------------------------------------------------------------------------------------
	local function SpecialBuildMarkDataCallBack(content)
		if not CurWorld then
			return;
		end
		if not content then
			return;
		end
		if (content.x and content.z) then
			PlayerMiniMapAddMarkData(content.x, content.z);
		end
	end
	netHandle.specialMarkDataCallBack = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.SPECIAL_BUILD_MARK, SpecialBuildMarkDataCallBack);
end

function SurviveGameLeaveUnSubscibeMsgHandle()
	if netHandle.playerCloseUIHandle then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.playerCloseUIHandle);
		netHandle.playerCloseUIHandle= nil;
	end
	if netHandle.actorShowExchangeItemHandle then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.actorShowExchangeItemHandle);
		netHandle.actorShowExchangeItemHandle= nil;
	end
	if netHandle.actorShowHeadIconByPathHandle then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.actorShowHeadIconByPathHandle);
		netHandle.actorShowHeadIconByPathHandle= nil;
	end
	if playerExploreTaskMgr.timer then
		playerExploreTaskMgr.timer = nil;
		playerExploreTaskMgr.lastPos = nil;
		threadpool:kick(playerExploreTaskMgr.timer)
	end
	if netHandle.surviveReport then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.surviveReport);
		netHandle.surviveReport= nil
	end

	if netHandle.driftBottleGet then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.driftBottleGet);
		netHandle.driftBottleGet= nil
	end
	if netHandle.manualEmitter then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.manualEmitter);
		netHandle.manualEmitter= nil
	end
	if netHandle.voidDragonFlowerAttack then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.voidDragonFlowerAttack);
		netHandle.voidDragonFlowerAttack= nil
	end
	if netHandle.citySearchCallBack then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.citySearchCallBack);
		netHandle.citySearchCallBack= nil
	end
	if netHandle.treasureOpenCallBack then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.treasureOpenCallBack);
		netHandle.treasureOpenCallBack= nil
	end
	if netHandle.specialMarkDataCallBack then
		SandboxLuaMsg:unSubscibeMsgHandle(netHandle.specialMarkDataCallBack);
		netHandle.specialMarkDataCallBack= nil
	end
end

--连击连段计时器 显示
function ComboAtkTimer_Show(param)
	if ClientCurGame and ClientCurGame.isInGame and ClientCurGame:isInGame() then
		local uiNode = GetInst("MiniUIManager"):GetUI("ComboAtkTimerAutoGen");
		if uiNode then
			uiNode.ctrl.model:SetIncomingParam({disableOperateUI = true, waitTime = param});
			if GetInst("MiniUIManager"):IsShown("ComboAtkTimerAutoGen") then
				uiNode:onRenew();
			else
				GetInst("MiniUIManager"):ShowUI("ComboAtkTimerAutoGen");
			end
		else
			GetInst("MiniUIManager"):OpenUI("ComboAtkTimerFramePage", "miniui/miniworld/adventure", "ComboAtkTimerAutoGen", {disableOperateUI = true, waitTime = param});
		end
    end
end

--连击连段计时器 关闭
function ComboAtkTimer_Close()
	GetInst("MiniUIManager"):HideUI("ComboAtkTimerAutoGen");
end

-- 显示瞄准镜
--@scopeType 瞄准镜标签：0=新瞄准镜功能， 1=老瞄准镜功能
function ShowTelescope(scopeType, bgName, vpX, vpY, rH, rV)
	if ClientCurGame and ClientCurGame.isInGame and ClientCurGame:isInGame() then
		local uiNode = GetInst("MiniUIManager"):GetUI("TelescopeAutoGen");
		if uiNode then
			if GetInst("MiniUIManager"):IsShown("TelescopeAutoGen") then
				uiNode.ctrl.model:SetIncomingParam({disableOperateUI = true, cus_viewportX = vpX, cus_viewportY = vpY, cus_scopeType = scopeType});
				uiNode:onRenew();
			else
				uiNode.ctrl.model:SetIncomingParam({disableOperateUI = true, cus_viewportX = vpX, cus_viewportY = vpY, cus_radiusH = rH, cus_radiusV = rV, cus_bgName = bgName, cus_scopeType = scopeType});
				GetInst("MiniUIManager"):ShowUI("TelescopeAutoGen");
			end
		else
			GetInst("MiniUIManager"):OpenUI("main", "miniui/miniworld/ugc_telescope", "TelescopeAutoGen", {disableOperateUI = true, cus_viewportX = vpX, cus_viewportY = vpY, cus_radiusH = rH, cus_radiusV = rV, cus_bgName = bgName, cus_scopeType = scopeType});
		end
    end
end

-- 关闭瞄准镜
function CloseTelescope()
	GetInst("MiniUIManager"):HideUI("TelescopeAutoGen");
end

--调整瞄准镜大小
function ResizeTelescope(rH, rV, debugBg)
	if ClientCurGame and ClientCurGame.isInGame and ClientCurGame:isInGame() then
		local uiNode = GetInst("MiniUIManager"):GetUI("TelescopeAutoGen");
		if uiNode and GetInst("MiniUIManager"):IsShown("TelescopeAutoGen") then
			uiNode.ctrl.model:SetIncomingParam({disableOperateUI = true, cus_radiusH = rH, cus_radiusV = rV, debug_bg = debugBg, cus_scopeType = 0});
			uiNode:onRenew();
		end
	end
end

-- 首次进入游戏延迟5s上报af
local isAlreadyReport = false;
function FirstEnterGameDelayReportEvent()
	-- print("延迟uploadEvent-start",os.date())
	threadpool:delay(5,function()
		if CurWorld and ReportPlayGameToFirebase then
			ReportPlayGameToFirebase(CurWorld:getOWID())
		end

		print("AppFlyerEvent Map5S map_open_5s",isAlreadyReport)
		if not isAlreadyReport then
			isAlreadyReport = true;
			StatisticsTools:appsFlyer("map_open_5s");
			print("AppFlyerEvent map_open_5s report_success");
		end
	end);
end


local view_order = { 0, 3, 2, 1, 4}
local view_model_to_id = { 
	[0] = 1,
	[1] = 3,
	[2] = 2,
	[3] = 4,
	[4] = 5,
}

local function getNextViewMode( viewmode )
	local nextindex = 1
	for i, mode in ipairs(view_order) do
		if mode == viewmode then
			nextindex = i + 1
			break
		end
	end

	if nextindex > #view_order then
		nextindex = 1
	end

	return view_order[nextindex]
end

--切换视角按钮
function ChangeViewBtn_OnClick()
	local oldmode = GetClientInfo():getGameData("view");
	local nextMode = getNextViewMode(oldmode - 1)
	local nextid = view_model_to_id[nextMode]

	TickBtnOnClick_ViewSet(nextid)
end
function AddItemHistoryToFile(id)
	local owid = CurWorld:getOWID();
    local mapId = CurWorld:getCurMapID();
    local path = "data/".."ItemGetHistory/w"..owid.."/".."m_"..mapId;
    local localData = GetInst("LocalDataManager"):CreatePlayerData("root",path):GetDataChunk();
	local historystr = localData.historystr
	local history = {}
	if historystr == nil or historystr == "" then
		historystr = ""
	end
	for value in string.gmatch(historystr, "[^,]+") do
		table.insert(history,tonumber(value))
	end
	for _, value in ipairs(history) do
		if value == id then
				return
		end
	end
	table.insert(history,id)
	local addStr = table.concat(history, ",")
	localData.historystr = addStr
end
function AddItemHistoryToClient(id,playerID)
	AddItemHistoryToFile(id)
end
function AddItemHistory(id,playerID)
	if CurWorld == nil then
		return
	end
	local param = { itemID = id, objID = playerID}
	if playerID == CurMainPlayer:getObjId() then
		AddItemHistoryToFile(id)
	else
		SandboxLuaMsg.sendToClient(playerID, SANDBOX_LUAMSG_NAME.Survive.BLOCK_TOOLBOX_HISTORY, param)
	end
end

function CheckItemHistory(id)
	if CurWorld == nil then
		return
	end
	local owid = CurWorld:getOWID();
    local mapId = CurWorld:getCurMapID();
    local path = "data/".."ItemGetHistory/w"..owid.."/".."m_"..mapId;
    local localData = GetInst("LocalDataManager"):CreatePlayerData("root",path):GetDataChunk();
	local historystr = localData.historystr
	local history = {}
	if historystr == nil or historystr == "" then
		return false
	end
	for value in string.gmatch(historystr, "[^,]+") do
		table.insert(history,tonumber(value))
	end
	for _, value in ipairs(history) do
		if value == id then
				return true
		end
	end
	return false
end
function Playmain_OnEasyModeGuideCheck(deathTimes)
	if not NewbieGuideManager or not CurWorld then
		return
	end

	local bNeverShowAgain = CurWorld:IsEasyGuideTipsNeverShow();
	if bNeverShowAgain then
		return
	end

	local str = GetS(86056)
	if deathTimes == 1 then
		str = GetS(86071)
	end

	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common_comp", "miniui/miniworld/common"}, "MessageBoxCheckBoxAutoGen")
	GetInst("MiniUIManager"):OpenUI("MessageBoxCheckBox", "miniui/miniworld/MessageBox", "MessageBoxCheckBoxAutoGen", {
		content = str,
		checkBoxFunc = function ()
			bNeverShowAgain = not bNeverShowAgain
			CurWorld:setEasyGuideTipsNeverShow(bNeverShowAgain);
		end,
		sureFunc = function ()
			if WorldMgr then
				if IsRoomOwner() then
					WorldMgr:surviveGameToggleDiffMode()
					
					RefreshStrengthConsumptionCfg()
					ShowGameTips(GetS(86058))
				else
					ShowGameTips("multiplayer mode can't toggle diff mode!!!")
				end
			end
		end,
		cancelFunc = function () 
			
		end,
		tipTxt = GetS(30135),
		ignoreCheckBoxKV = true,
		fullScreen = {Type = "Normal", bkgName = "n12"}
	})
end

function Playmain_OnShowInviteActionBtn(target_uin)
	GetInst("PlayerExpressionManager"):SetInvitePlayerUin(target_uin)
	-- 显示地图内玩家信息 code_by:huangfubin 2024.5.16
	local infoMgr = GetInst("PlayerInfoCardMgr") 
	if infoMgr and infoMgr.ShowPlayerInfoImMap then
		infoMgr:ShowPlayerInfoImMap(target_uin)
	end
end

---------------------- 一些海外独有函数 start


function UpdateMicSwitchBtnTexture()
	local icon = getglobal("MicSwitchBtnIcon");


	if ClientMgr:getGameData("micswitch") == 0 then
		icon:SetTexUV("icon_voice_no");
		icon:SetSize(38, 37);
	else
		icon:SetTexUV("icon_voice");
		icon:SetSize(22, 32);
	end
end

function UpdateSpeakerSwitchBtnTexture()
	local icon = getglobal("SpeakerSwitchBtnIcon");

	if ClientMgr:getGameData("speakerswitch") == 0 then
		icon:SetTexUV("icon_horn_no");
	else
		icon:SetTexUV("icon_horn");
	end
end

function GVoiceMicSwitch(isOpen)
	if ClientMgr:getGameData("micswitch") == 0 then	--如果前状态是关，点击打开
		--打开麦克风
		local rst = GYouMeVoiceMgr:setMicrophoneMute(false);
		if rst == -2 then		--国战语音 没有麦克风权限不能打开麦克风
			ShowGameTips(GetS(1207), 3);
		end
	else
		--关闭麦克风
		GYouMeVoiceMgr:setMicrophoneMute(true);
	end
	voiceManager.IsLocalOperateMacAndSpeaker = true
end

function GVoiceSpeakerSwitch(isOpen)
	local result = nil
	if GetClientInfo():getGameData("speakerswitch") == 0 then	--如果前状态是关，点击打开
		--打开扬声器
		print("GVoiceSpeakerSwitch 打开扬声器")
		result = 1
		YouMeVoiceMgr:setSpeakerMute(false);
	else
		--关闭扬声器
		print("GVoiceSpeakerSwitch 关闭扬声器")
		result = 0
		YouMeVoiceMgr:setSpeakerMute(true);
	end
	voiceManager.IsLocalOperateMacAndSpeaker = true

	return result
end

function SetVoiceTipsInfo(uvName)
	getglobal("VoiceTipsFrameIcon"):SetTexUV(uvName);

	if string.find(uvName,"icon_voice_no") ~= nil then
		getglobal("VoiceTipsFrame"):SetSize(82,80)
	elseif string.find(uvName,"icon_voice") ~= nil then
		getglobal("VoiceTipsFrame"):SetSize(60,82)
	else
		getglobal("VoiceTipsFrame"):SetSize(82,82)
	end

	getglobal("VoiceTipsFrameIcon"):SetBlendAlpha(1.0);
	if not getglobal("VoiceTipsFrame"):IsShown() then
		getglobal("VoiceTipsFrame"):Show();
	end
end

function ShowChangeViewBtn()
	local cvBtn = getglobal("ChangeViewBtn")
	if cvBtn and not cvBtn:IsShown() then
		cvBtn:Show()
		if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
			GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(cvBtn, true)
		end
	end
end

function HideChangeViewBtn()
	local cvBtn = getglobal("ChangeViewBtn")
	if cvBtn and cvBtn:IsShown() then
		cvBtn:Hide()
		if GetInst('MiniUIManager'):GetUI('DeveloperUIRoot') then
			GetInst('MiniUIManager'):GetUI('DeveloperUIRoot'):ChangeBasicOldVisible(cvBtn, false)
		end
	end
end

---------------------- 一些海外独有函数 end

---------------------- 血条相关全局函数
function HpBarFrame_Check()
	--if not (GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--	GetInst("MiniUIManager"):AddPackage(
	--		{
	--			"miniui/miniworld/common",
	--			"miniui/miniworld/c_ingame",
	--			"miniui/miniworld/c_login"
	--		}, 
	--		"HpBarFrameAutoGen"
	--	)
	--	GetInst("MiniUIManager"):OpenUI("HpBarFrame", "miniui/miniworld/adventure", "HpBarFrameAutoGen", {disableOperateUI = true});
	--end
end
function HpBarFrame_ShowHpBar(val)
	--if(val)then
	--	HpBarFrame_Check()
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):ShowHp(val)
	--	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
	--		--getglobal("PlayerArmorBar"):Show();
	--		HpBarFrame_ShowArmorBar(val)
	--	else
	--		--getglobal("PlayerArmorBar"):Hide();
	--		HpBarFrame_ShowArmorBar(false)
	--	end
	--end
end
function HpBarFrame_IsHpBarShow()
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):IsHpShow()
	--end
	return false;
end
function HpBarFrame_ShowHuBar(val)
	--if(val)then
	--	HpBarFrame_Check()
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):ShowHu(val)
	--end
end
function HpBarFrame_IsHuBarShow()
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):IsHuShow()
	--end
	return false;
end
function HpBarFrame_ShowStBar(val)
	--if(val)then
	--	HpBarFrame_Check()
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):ShowSt(val)
	--	if LuaInterface and LuaInterface:shouldUseNewHpRule() then
	--		--getglobal("PlayerPerseveranceBar"):Show();
	--		HpBarFrame_ShowPerBar(val)
	--	else
	--		--getglobal("PlayerPerseveranceBar"):Hide();
	--		HpBarFrame_ShowPerBar(false)
	--	end
	--end
end
function HpBarFrame_IsStBarShow()
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):IsStShow()
	--end
	return false;
end
function HpBarFrame_ShowArmorBar(val)
	--if(val)then
	--	HpBarFrame_Check()
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):ShowArmor(val)
	--end
end
function HpBarFrame_ShowPerBar(val)
	--if(val)then
	--	HpBarFrame_Check()
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):ShowPer(val)
	--end
end

function CheckHpBarShow()
	--if not (GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--	return false
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):GetHpShow()
	--end
	return false
end

function CheckHuBarShow()
	--if not (GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--	return false
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):GetHuShow()
	--end
	return false
end

function CheckStBarShow()
	--if not (GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen"))then
	--	return false
	--end
	--if GetInst("MiniUIManager"):GetCtrl("HpBarFrame") then
	--	return GetInst("MiniUIManager"):GetCtrl("HpBarFrame"):GetStShow()
	--end
	return false
end

function HpBarFrame_IsShown()
	local hpbarShownInfo = {}
	--local allShow = GetInst("MiniUIManager"):IsShown("HpBarFrameAutoGen")
	--local hpShow = CheckHpBarShow()
	--local huShow = CheckHuBarShow()
	--local stShow = CheckStBarShow()
	hpbarShownInfo.allShow = false
	hpbarShownInfo.hpShow = false
	hpbarShownInfo.huShow = false
	hpbarShownInfo.stShow = false
	return hpbarShownInfo
end

function HpBarFrame_Shown(hpbarShownInfo)
	--if(hpbarShownInfo.allShow)then
	--	GetInst("MiniUIManager"):AddPackage(
	--		{
	--			"miniui/miniworld/common",
	--			"miniui/miniworld/c_ingame",
	--			"miniui/miniworld/c_login"
	--		}, 
	--		"HpBarFrameAutoGen"
	--	)
	--	GetInst("MiniUIManager"):OpenUI("HpBarFrame", "miniui/miniworld/adventure", "HpBarFrameAutoGen", {disableOperateUI = true});
	--end
	--local ctrl = GetInst("MiniUIManager"):GetCtrl("HpBarFrame")
	--if(ctrl)then
	--	ctrl.view.widgets.Hp:setVisible(hpbarShownInfo.hpShow)
	--	--ctrl.view.widgets.Thirst:setVisible(hpbarShownInfo.hpShow)
	--	ctrl.view.widgets.Hu:setVisible(hpbarShownInfo.huShow)
	--	ctrl.view.widgets.St:setVisible(hpbarShownInfo.stShow)
	--end
end

---------------------------------- 提示相关全局函数
function SetNewTips(param)
	if GetInst("MiniUIManager"):IsShown("TeamupMainAutoGen") then
		return; --组队界面显示 时取消Hover效果
	end
	if GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen") then
		GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame"):onCloseByclick()
	end
	GetInst("MiniUIManager"):AddPackage(
		{
			"miniui/miniworld/common",
			"miniui/miniworld/c_shop"
		},
		"newItemTipsFrameAutoGen"
	)
	--param.isFullScreen=true
	--测试代码
	--[[if param.index > -1 then
		if ClientBackpack then
			local grid =	ClientBackpack:index2Grid(param.index);
			local strgrid = grid:getJsonStr();
			param.strGridinfo = strgrid;
			param.index = nil
			param.id = nil
		end
	end]]

	GetInst("MiniUIManager"):OpenUI("newItemTipsFrame","miniui/miniworld/adventure","newItemTipsFrameAutoGen",param)	
end


function TriggerShowNewTips(strparam)
	local Triggerparam = nil
	if type(strparam) == "string" then
		Triggerparam={
			isOutSideCall=true,
			strGridinfo = strparam,
			mousePx=0,
			mousePy=0
			}
	elseif type(strparam) == "number" then
		Triggerparam={
			id=strparam,
			index=-1,
			isOutSideCall=true,
			mousePx=0,
			mousePy=0
			}
	end
	if Triggerparam  then
		SetNewTips(Triggerparam)	
	end
end


local itemEditTypeTable = 
{
	[11] = GetS(100016),  --"地形"
	[12] = GetS(100009),  --"植物"
	[13] = GetS(292),  --"建筑"
	[14] = GetS(100014),  --"装饰"
	[15] = GetS(80317),  --"星能"
	[16] = GetS(111911),  --"颜色"
	[17] = GetS(6377),  --"功能"
	[18] = GetS(2141),  --"机械"
	[21] = GetS(3964),  --"动物"
	[22] = GetS(3965),  --"怪物"
	[23] = GetS(300254),  --"人物（NPC）"
	[24] = GetS(80318),  --"载具"
	[31] = GetS(3013),  --"装备"
	[32] = GetS(6377),  --"功能"
	[33] = GetS(80319),  --"食物"
	[34] = GetS(15358),  --"材料"
	[35] = GetS(80320),  --"武器"GetS(80320)
	[36] = GetS(295),  --"工具"
	[41] = GetS(6377),  --"功能"
	[42] = GetS(80321),  --"创造"
	[43] = GetS(300339),  --"功能对象"
}

-- 高级创造背包开放，11=地形，12=植物，13=建筑，14=装饰，15=星能，16=颜色，17=功能，18=机械，21=动物，22=怪物，23=人物（NPC），24=载具，31=装备，32=功能，33=食物，34=材料，35武器，36工具，41=功能，42=创造，43=功能对象
function GetItemEditTypeName(itemEditType)
	return itemEditTypeTable[itemEditType];
end

function tips_icon_click(obj,context,isOutside,px,py) --点击icon事件
	local text_id_obj=obj:getChild("text_id")
	local itemid=tonumber(text_id_obj:getText())
	if GetInst("MiniUIManager"):IsShown("newItemTipsFrameAutoGen") then
		GetInst("MiniUIManager"):GetCtrl("newItemTipsFrame"):onClose()
		--GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common","miniui/miniworld/adventure"})
		-- GetInst("MiniUIManager"):OpenUI("newItemTipsFrame","miniui/miniworld/adventure","newItemTipsFrameAutoGen",{itemid,-1,isOutside,px,py})	
		local param={
			id=itemid,
			index=-1,
			isOutSideCall=true,
			mousePx=px,
			mousePy=py
		}
		SetNewTips(param)
	end
end

function GetRuneItemDisplayName(runeid, runeval1, runeval2) --获取RuneDef
	if not runeid or not runeval1 or not runeval2 then 
		return nil
	end
	local runeDef = DefMgr:getRuneDef(runeid)--val0--val1--itemid
	if runeDef then 
		-- MiniLog("GetRuneItemDisplayName: runeid = ", runeid, runeval1, runeDef.DisplayType)
		local trueId = math.floor(runeid / 100);
		if trueId == 10 or trueId == 11 or trueId == 32 or trueId == 42 then
			runeval2 = math.floor(runeval2 + 0.5);
			runeval1 = math.floor(runeval1 + 0.5);
		end

		if runeDef.DisplayType == 0 then --使用数值1
			--print("GetRuneItemDisplayName: ret:",string.format("%s%d",runeDef.AttrDesc, runeval1))
			return string.format("%s%d",runeDef.AttrDesc, runeval1)
		elseif runeDef.DisplayType == 1 then --使用数值2
			--print("GetRuneItemDisplayName: ret:",string.format("%s%d",runeDef.AttrDesc, runeval2))
			return string.format("%s%d",runeDef.AttrDesc, runeval2)
		elseif runeDef.DisplayType == 2 then --使用数值1  * 100  %
			--print("GetRuneItemDisplayName: ret:",string.format("%s%d%%",runeDef.AttrDesc, runeval1*100))
			return string.format("%s%d%%",runeDef.AttrDesc, runeval1*100)
		elseif runeDef.DisplayType == 4 then -- 只使用描述字段
			return runeDef.AttrDesc
		elseif runeDef.DisplayType == 5 then -- 使用数值5  整数/100
			local num=runeval1/100
			local result = string.gsub(tostring(num), "%.?0*$", "")
			return string.format("%s%s",runeDef.AttrDesc, result)
		else --使用数值1   %
			--print("GetRuneItemDisplayName: ret:",string.format("%s%d%%",runeDef.AttrDesc, runeval1))
			return string.format("%s%d%%",runeDef.AttrDesc, runeval1)
		end
	end 
	return nil
end

------------------------------------------- 冒险其他全局函数
function newUpdatePlotFrame(title, content)
	GetInst("MiniUIManager"):AddPackage({"miniui/miniworld/common"}, "plotFrameAutoGen")
	GetInst("MiniUIManager"):OpenUI("plotFrame","miniui/miniworld/adventure","plotFrameAutoGen",{Title=title,Content=content})	
end

function ClipCraftData(data)--将string转为table，该string为C++侧传入，有固定的连接规则
	if(data == "")then
		return nil
	end
	local resultTable = {}
	for clipdata in data:gmatch("([^S]+)") do
		table.insert(resultTable, tonumber(clipdata))
	end
	return resultTable
end


function SetSelectInfo(def)
	local typeStr = { 3964, 3965, 4544 } --原local数据
	local CreateMonsterFramePageCtrl = GetInst("MiniUIManager"):GetCtrl("CreateMonsterFramePage")

	CreateMonsterFramePageCtrl.view.widgets.SelectName:setText(def.Name)
	CreateMonsterFramePageCtrl.view.widgets.SelectName:setVisible(true)

	CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:setVisible(true)
	CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectName"):setText(def.Name)
	CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectType"):setText(GetS(1107) .. GetS(8503) .. GetS(typeStr[CreateMonsterFramePageCtrl:getType(def.ID)]))
	CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectHp"):setText(GetS(4300) .. GetS(8503) .. def.Life)
	local atkpoint = 0
	if def.AttackType >= 0 and def.AttackType <= 2 then
		atkpoint = def.Attacks[def.AttackType]
	end
	CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectAct"):setText(GetS(4302) .. GetS(8503) .. atkpoint)
	
	if tostring(def.Desc) ~= "" then
		CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectDesInfo"):setText(def.Desc)
	else
		CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("lbl_SelectDesInfo"):setText(GetS(58))
	end

	local MainIcon = CreateMonsterFramePageCtrl.view.widgets.SelectBTN:getChild("Icon");
	local InfoIcon = CreateMonsterFramePageCtrl.view.widgets.SelectInfoMain:getChild("icon_SelectInfo");

	MiniuiSetActorIcon(MainIcon,def.ID)
	MiniuiSetActorIcon(InfoIcon,def.ID)
	MainIcon:setVisible(true)
	InfoIcon:setVisible(true)
	CreateMonsterFramePageCtrl.view.widgets.SelectBTN:setCustomData(def.ID)
end

function curtainSignFrame_OnShow(param)
	GetInst("MiniUIManager"):OpenUI("curtainSignFrame","miniui/miniworld/adventure","curtainSignFrameAutoGen",param)
end

function NewFurnaceOxyFrame_OnShow(param)
	GetInst("MiniUIManager"):AddPackage(
		{
			"miniui/miniworld/common",
			"miniui/miniworld/commonTexture"
		},
		"furnaceOxygenFrameAutoGen"
	)
	GetInst("MiniUIManager"):OpenUI("furnaceOxygenFrame","miniui/miniworld/adventure","furnaceOxygenFrameAutoGen",{param})	
end

function RemoveStarPunishLackStar()--移除星星惩罚星星不足
	local StarDebuffTime=MainPlayerAttrib:getStarDebuffTime()
	local tick2sec=20
	local unitSec=LuaConstants:get().revive_in_place_consume_buff_clear_unit*tick2sec
	local starNum = math.floor(MainPlayerAttrib:getExp()/EXP_STAR_RATIO);
	local Remove_Need_Star_New=math.ceil((LuaConstants:get().revive_in_place_consume_buff_clear_consume*(math.ceil(StarDebuffTime)))/(unitSec))
	if starNum < Remove_Need_Star_New then
		local needNum = math.ceil((Remove_Need_Star_New-starNum)/MiniCoin_Star_Ratio);
		local hasNum = AccountManager:getAccountData():getMiniCoin();
		if needNum <= hasNum then
			local callback = function(success)
				if not success then
					return
				end
				if CurWorld and CurWorld:isRemoteMode() then--客机
					local params = {
						type=0,--0为清空惩罚
						uin=CurMainPlayer:getObjId()
					};
					SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SET_STAR_PINISH, params)
				else
					MainPlayerAttrib:addExp(-Remove_Need_Star_New * EXP_STAR_RATIO);
				end
				MainPlayerAttrib:setStarDebuffTime(0)
				MainPlayerAttrib:setStarDebuffStage(0)
			end
			AccountManager:getAccountData():coinConvertStar(needNum, callback)
		else
			local lackNum = needNum - hasNum;
			local cost, buyNum = GetPayRealCost(lackNum);
			local text = GetS(453, cost, buyNum);
			StoreMsgBox(6, text, GetS(456), -1, lackNum, needNum, nil, NotEnoughMiniCoinCharge, cost);
		end
	else
		if CurWorld and CurWorld:isRemoteMode() then--客机
			local params = {
				type=0,--0为清空惩罚
				uin=CurMainPlayer:getObjId()
			};
			SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SET_STAR_PINISH, params)
		else
			MainPlayerAttrib:addExp(-Remove_Need_Star_New * EXP_STAR_RATIO);
		end
		MainPlayerAttrib:setStarDebuffTime(0)
		MainPlayerAttrib:setStarDebuffStage(0)
	end
end

function KeyBoardEventWithBox(num,Index)--数字键与箱子的交互
	toIndex=num.result-49
	local item_up_index=Index
	local item_down_index=SHORTCUT_START_INDEX+toIndex
	CurMainPlayer:swapItem(item_up_index, item_down_index)
end

