/**********************************************
*	FUNC:	��Ϊ��ģ�����
*	FILE:	BehaviorTreeObj
*	BY:		chenzh
*	TIME:	2020-6-16
*/
#pragma once
#ifndef __BEHAVIORTREE_OBJ_H__
#define __BEHAVIORTREE_OBJ_H__

#include "BehaviorTreeDef.h"
#include <functional>
#include "Allocator/MemoryMacros.h"

/*
*	��
*/
class BTObj//tolua_exports
{//tolua_exports
public:
	BTObj(): m_InstanceID(0)
	{
		BTObj(0);
	}

	BTObj(BTInstanceID id) : m_InstanceID(id), m_fRelease(NULL)
	{
		
	}

	virtual ~BTObj()
	{

	}

	// ������
	static const char* ClassType() { return "BTObj"; }

	// �����ͷŻص�
	void SetReleaseCallback(const std::function<void(BTObj*)>& frelease)
	{
		m_fRelease = frelease;
	}

	// �ͷ��Լ�
	virtual void ReleaseSelf()
	{
		if (m_fRelease)
			m_fRelease(this);
		else
		{
			delete this;
		}
	}

	// ���á���ȡʵ��ID
	void SetInstanceID(BTInstanceID id) { m_InstanceID = id; }
	int/*BTInstanceID*/ GetInstanceID() const { return m_InstanceID; }//tolua_exports

private:

	/* ���� */
	BTInstanceID m_InstanceID; // ʵ��ID

	// �ͷŻص�
	std::function<void(BTObj*)> m_fRelease;
};//tolua_exports

#endif