﻿#include "ActorVehicleAssemble.h"
#include <fstream>

#include "BlockMaterialMgr.h"
#include "special_blockid.h"

#include "PlayerLocoMotion.h"
#include "ActorChassis.h"

#include "VehicleMgr.h"
#include "VehicleControlInputs.h"
#include "PlayerControl.h"
#include "backpack.h"
#include "GameNetManager.h"
#include "MpActorManager.h"
#include "LuaInterfaceProxy.h"
#include "GameCamera.h"
#include "GunUseComponent.h"

#include "PhysicsLocoMotionComponent.h"
#include "VehicleContainerMgr.h"
#include "VehicleContainerActioner.h"
#include "Compress/LegacyCompress.h"
#include "BlockSlab.h"
#include "vehicleblock_tickmgr.h"
#include "WorldRender.h"
#include "CurveFace.h"
#include "container_mecha.h"
#include "container_emitter.h"
#include "container_radiounit.h"
#include "BlockDoor.h"
#include "BlockWindows.h"
#include "DynamicOverheat.h"
#include "DynamicTank.h"
#include "container_bookEditorTable.h"
#include "container_sensor.h"
#include "container_funnel.h"
#include "container_itemexpo.h"
#include "ClientActorProjectile.h"
#include "FurnaceContainer.h"
#include "IClientGameManagerInterface.h"
#include "GameMode.h"
#include "Components/BoxCollider.h"
#include "ClientInfoProxy.h"

#include "MechaMesh.h"
#include "BlockScene.h"
#include "Components/VehicleDriveCustom.h"
#include "EffectComponent.h"
#include "ClientActorHelper.h"
#include "RiddenComponent.h"
#include "ActorBindVehicle.h"
#include "ClientActorFuncWrapper.h"
#include "FireBurnComponent.h"
#include "container_driverseat_model.h"
#include "VehicleWorld.h"
#include "EffectParticle.h"
#include "ActorMechaUnit.h"
#include "minisystem/base/Plugin.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
extern int g_all_physxitem;
EXPORT_SANDBOXENGINE extern float GetBlockDensity(World *pworld, const WCoord &center, const CollideAABB &aabb);
EXPORT_SANDBOXENGINE extern  bool IsBlockCollideWithRay(BlockMaterial *blockmtl, World *pworld, const WCoord &blockpos, const Rainbow::Vector3f &origin, const Rainbow::Vector3f &dir, IntersectResult *presult);
extern flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ChunkContainer>>> CreateContainerVec(flatbuffers::FlatBufferBuilder &builder, std::vector<WorldContainer *>&chunkcontainers);
extern WorldContainer *CreateWorldVehicleContainerFromChunkContainer(const FBSave::ChunkContainer *pChunkContainer);

static const char *AttackTypeName[] = {
	"PUNCH",
	"RANGE",
	"EXPLODE",
	"FIRE",
	"POISON",
	"WITHER",
	"SUN",
	"FALLING",
	"ANVIL",
	"CACTUS",
	"WALL",
	"DROWN",
	"THORNS",
};

#define MAX_VEHICLE_KEYS 21
#define AttribUpdateInterval  2
IMPLEMENT_COMPONENTCLASS(ActorVehicleAttrib)

bool ActorVehicleAssemble::m_bEnterWorld = false;
ActorVehicleAttrib::ActorVehicleAttrib()
{
}

bool ActorVehicleAttrib::attackedFrom(OneAttackData &atkdata)
{
	if (m_Life <= 0) return false;
	if (atkdata.atktype == ATTACK_SUN)
	{
		bool hurt = true;
		int helmet = getEquipItem(EQUIP_HEAD);
		if (helmet > 0)
		{
			damageEquipItemWithType(EQUIP_HEAD, GenRandomInt(0, 1));
			hurt = false;
		}
		if (hurt && m_OwnerActor)
		{
			auto FireBurnComp = m_OwnerActor->sureFireBurnComponent();
			if (FireBurnComp)
			{
				FireBurnComp->setFire(100, 1);
			}

		}
		return true;
	}
	atkdata.knockback -= getKnockbackResistance();
	if (atkdata.knockback < 0) atkdata.knockback = 0;
	else if (GenRandomFloat() < getModAttrib(MODATTR_KNOCK_RESIST_PROB)) atkdata.knockback = 0;
	//else if (GenRandomFloat() < 0.5f) atkdata.knockback = 0;

	if (m_HurtResistantTime > 20 / 2)
	{
		if (!atkdata.ignore_resist && atkdata.atkpoints <= m_MaxHurtInResistant)
		{
			return false;
		}

		if (atkdata.atktype != ATTACK_FLASH)	// 闪电链是跟随普攻的，在保护期内不刷新这个阈值，不然会导致一直重置普攻
		{
			m_MaxHurtInResistant = atkdata.atkpoints;
		}
		atkdata.knockback = 0;
	}
	else
	{
		m_MaxHurtInResistant = atkdata.atkpoints;
		m_HurtResistantTime = 20;
		if (m_OwnerActor && m_OwnerActor->getBody())
		{
			auto effectComponent = m_OwnerActor->getEffectComponent();
			if (effectComponent)
			{
				effectComponent->playBodyEffect(BODYFX_HURT);
			}
		}
	}

	float hpdec = 0;
	//float enchant_protect = getEnchantArmorPoint(atkdata.atktype) * 0.04f * (GenRandomFloat()*0.5f + 0.5f);
	float enchant_protect = (getEnchantArmorPoint(atkdata.atktype) / (getEnchantArmorPoint(atkdata.atktype) + 20)) * (GenRandomFloat()*0.5f + 0.5f); // modify by null , 修改伤害计算公式 
	enchant_protect =Rainbow::Clamp(enchant_protect, 0.0f, 1.0f);
	float buff_add = atkdata.buff_atk + getModAttrib(MODATTR_DAMAGED_PUNCH + atkdata.atktype);
	if (buff_add < -1.0f) buff_add = -1.0f;

	ClientPlayer *thisplayer = dynamic_cast<ClientPlayer *>(m_OwnerActor);
	float hurtinc = 1.0f;
	float hurtdec = 1.0f;
	//float armor = getArmorPoint(atkdata.atktype) * 0.05f;
	float armor = getArmorPoint(atkdata.atktype);
	armor = armor / (armor + 20); // modify by null, 修改伤害计算公式 

	ClientPlayer* fromplayer = dynamic_cast<ClientPlayer*>(atkdata.fromplayer);
	if (atkdata.atktype < MAX_PHYSICS_ATTACK)
	{
		float criticalfactor = atkdata.critical ? 1.5f : 1.0f;
		float baseatk = 0.0f;

		int atktypeoffset = atkdata.atktype - ATTACK_PUNCH;
		if (fromplayer)
		{
			baseatk += 1.0f * GetLuaInterfaceProxy().get_lua_const()->kongshou_shanghai_beilv + fromplayer->getGeniusValue(GENIUS_BASEATK_INC); //modify by null  乘以空手伤害倍率
			hurtinc += fromplayer->getGeniusValue((PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_INC + atktypeoffset));
			hurtinc += fromplayer->getGeniusValue(GENIUS_PHYSICSHURT_INC);
		}

		if (thisplayer)
		{
			hurtdec -= thisplayer->getGeniusValue((PLAYER_GENIUS_TYPE)(GENIUS_PUNCHHURT_DEC + atktypeoffset));
			hurtdec -= thisplayer->getGeniusValue(GENIUS_PHYSICSHURT_DEC);
		}

		hpdec = (atkdata.atkpoints*(1.0f - armor) + baseatk + atkdata.enchant_atk) * (1.0f + buff_add) * criticalfactor * (1.0f - enchant_protect); //伤害公式

																																					//damageArmor(atkdata.atkpoints*(1.0f - armor)*2.0f);
	}
	else
	{
		int atktypeoffset = atkdata.atktype - ATTACK_FIRE;
		if (atkdata.atktype < MAX_MAGIC_ATTACK)
		{
			hpdec = atkdata.atkpoints * (1.0f - armor) * (1.0f + buff_add) * (1.0f - enchant_protect);
		}
		else hpdec = atkdata.atkpoints;

		if (fromplayer)
		{
			hurtinc += fromplayer->getGeniusValue((PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_INC + atktypeoffset));
			hurtinc += fromplayer->getGeniusValue(GENIUS_MAGICHURT_INC);
		}
		if (thisplayer)
		{
			hurtdec -= thisplayer->getGeniusValue((PLAYER_GENIUS_TYPE)(GENIUS_FIREHURT_DEC + atktypeoffset));
			hurtdec -= thisplayer->getGeniusValue(GENIUS_MAGICHURT_DEC);
		}

		//damageArmor(hpdec);
	}
	hpdec *= hurtinc * hurtdec;
	//addHP(-hpdec);
	atkdata.atkpoints = hpdec;
	if (atkdata.damage_armor) damageArmor(1);

	if (ActorAttrib::m_DisplayHurtNumber)
	{
		char msgbuf[256];
		const char *who = "I";
		MobAttrib *mobattr = dynamic_cast<MobAttrib *>(this);
		if (mobattr)
		{
			who = mobattr->getDef()->Name.c_str();
		}
		const char *atkname = AttackTypeName[atkdata.atktype];
		sprintf(msgbuf, "%s hurt: HP=%.2f/%.2f, atktype=%s, atkpoints=%.2f, knock=%.2f", who, hpdec, m_Life, atkname, atkdata.atkpoints, atkdata.knockback);

		GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(msgbuf, 0, 0, 1);
	}

	if (atkdata.buffId > 0)
	{
		addBuff(atkdata.buffId, atkdata.buffLevel);
	}
	return true;
}

ActorVehicleAssemble::ActorVehicleAssemble()
	: m_bIsUnlimitEnergy(false)
	, m_WaterMaskMat(nullptr)
	, m_EnableWaterMask(true)
	, m_bCanDig(false)
	, m_bIsRudder(false)
	, m_isAfterTick(false)
	, m_VehicleAABBDirty(true)
{
	createEvent();
	CreateComponent<VehicleAssembleLocoMotion>("VehicleAssembleLocoMotion");
	//getLocoMotion() = new VehicleAssembleLocoMotion(this);
	getLocoMotion()->setBound(70, 70);
	getLocoMotion()->setAttackBound(70, 70, 70);
	getLocoMotion()->m_yOffset = 35;

	m_WorkshopDirection = DIR_NEG_X;

	initAttrib();

	m_itemID = 0;
	m_pContainerWorkshop = NULL;

	m_FileName = "";
	m_ModelName = "";
	m_ModelDesc = "";
	for (int x = 0; x < MAX_DIM_X; x++)
	{
		for (int y = 0; y < MAX_DIM_Y; y++)
		{
			for (int z = 0; z < MAX_DIM_Z; z++)
			{
				m_Blocks[x][y][z] = ENG_NEW(VehicleBlock)();
			}
		}
	}
	const MonsterDef *def = GetDefManagerProxy()->getMonsterDef(VEHICLE_MONSTER, true);
	if (def == NULL)
	{
		CreateComponent<ActorAttrib>("ActorAttrib");
		return;
	}
	ActorVehicleAttrib *pAttrib = CreateComponent<ActorVehicleAttrib>("ActorVehicleAttrib");
	pAttrib->init(def);
	m_lastContactTick = 0;
	m_nCoreDecHp = 0;
	m_Def = def;
	if (m_Def)
	{
		m_SaySound = m_Def->SaySound;
	}
	m_AttribUpdateCount = 0;
	m_CreateModelCount = 0;

	m_vehicleSnd = VehicleSnd();
	m_CurActualSpeed = 0;
	m_CurSpeedShow = 0;
	m_LastSpeedShow = 0;
	m_EngineRotationSpeed = 0;

	m_MinVertBlock = WCoord(0, 0, 0);
	m_MaxVertBlock = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);

	m_isReCreate = false;

	m_pVehicleWorld = NULL;

	//需要将默认值修改成1，兼容老地图
	m_BlockGroupNum = 1;

	//m_ActionerInterval = 0;

	m_NumBlocks = 0;

	m_LineGraph = NULL;

	m_AddThruster = 0;

	m_bShowGraph = false;
	m_VehicleBlockLines.clear();
	m_VehicleBlockNodes.clear();
	m_SeatRelativePos = WCoord(0, 0, 0);
	m_ThrusterSnd = nullptr;
	m_OneLineStart = -1;
	m_FindBlockPosition = WCoord(0, 0, 0);

	m_ChassisPos = WCoord(0, 0, 0);
	m_bHaveChassisPos = false;
	m_StatisticData = 0;
	m_bMakeByItemUse = false;
	m_nOffsetYWorkshopAndWorld = 0;
	m_mustReCreatePhysics = false;
	m_createCount = 0;
	m_FatherVehilcID = 0;
	m_FatherVehilcPos = WCoord(0, 0, 0);
	m_nDynamicSThrusterCount = 0;
	m_nDynamicThrusterCount = 0;

	m_nDynamicBoatFloatbucketCount = 0;
	m_nDynamicBoatThrusterCount = 0;
	m_collideTick = 0;
	m_effectTick = 0;
	m_destoryDriverSeat = false;
	auto& dispatch = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr();
	if (!dispatch.HasEventDispatcher("AfterActorTick"))
	{
		dispatch.CreateEventDispatcher("AfterActorTick");
	}
	m_afterActorTick = dispatch.SubscribeEvent("AfterActorTick", nullptr,[&](SandboxContext context)->SandboxResult {
		this->afterActorTick();
		return SandboxResult(nullptr, true);
		});

	//m_RenderLine = new MINIW::RenderLines(false);
	m_floatBucketMgr.clear();
}

ActorVehicleAssemble::~ActorVehicleAssemble()
{
	deleteEvent();
	auto& dispatch = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr();
	dispatch.Unsubscribe("AfterActorTick", m_afterActorTick);
	for (int i = 0; i<(int)m_EntitiesChassis.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntitiesChassis[i]);
	}

	for (int i = 0; i<(int)m_EntitiesWheel.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntitiesWheel[i]);
	}

	for (int i = 0; i < (int)m_WaterMaskEntites.size(); i++) 
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_WaterMaskEntites[i]);
	}
	m_WaterMaskEntites.clear();

	ENG_RELEASE(m_WaterMaskMat);

	ENG_DELETE(m_pVehicleWorld);

	for (int x = 0; x < MAX_DIM_X; x++)
	{
		for (int y = 0; y < MAX_DIM_Y; y++)
		{
			for (int z = 0; z < MAX_DIM_Z; z++)
			{
				ENG_DELETE(m_Blocks[x][y][z]);
			}
		}
	}

	for (int i = 0; i<(int)m_pMechaSections.size(); i++)
	{
		if (m_pMechaSections[i] != nullptr)
		{
			m_pMechaSections[i]->destroy();
			ENG_DELETE(m_pMechaSections[i]);
		}
	}
	m_pMechaSections.clear();

	ENG_DELETE(m_LineGraph);

#ifndef IWORLD_SERVER_BUILD
    for (int i = 0; i < (int)m_ThrusterParticles.size(); i++) {
        EffectDeleteProxy::SafeDeleteEffect(m_ThrusterParticles[i]);
    }
    for (int i = 0; i < (int)m_ClawEffects.size(); i++) {
        EffectDeleteProxy::SafeDeleteEffect(m_ClawEffects[i]);
    }
	m_ClawBlocks.clear();
#endif
	auto iter = m_EntitiesChassisEx.begin();
	while (iter != m_EntitiesChassisEx.end())
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second);
		iter++;
	}
	m_EntitiesChassisEx.clear();

	for (int i = 0; i<(int)m_VehicleCreateMeshes.size(); i++)
	{
		for (int j = 0; j<(int)m_VehicleCreateMeshes[i].mRawMeshesWheel.size(); j++)
		{
			if (m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mVerts) {
				ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mVerts, kMemPhysics);
			}
			if (m_VehicleCreateMeshes[i].mRawMeshesWheelCollider[j].mVerts)
			{
				ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheelCollider[j].mVerts, kMemPhysics);
			}
		}
		m_VehicleCreateMeshes[i].clear();
	}

	m_Suspensions.clear();
	//deleteEvent();
	m_floatBucketMgr.clear();
}

void ActorVehicleAssemble::deleteEvent()
{
	Event().DestroyAllDispatchers();
	Event2().UnsubscribeAll();
}

void ActorVehicleAssemble::createEvent()
{
	MNSandbox::WeakRef<Ref> self = this;

	//Rainbow::Vector3f getRiddenBindPos(ClientActor *ridden)
	typedef ListenerFunctionRef<Rainbow::Vector3f&, ClientActor*> ListenerGetRiddenBindPos;
	ListenerGetRiddenBindPos* listenerGetRiddenBindPos = SANDBOX_NEW(ListenerGetRiddenBindPos, [self, this](Rainbow::Vector3f& ret, ClientActor* ridden) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiddenBindPos(ridden);
		});
	Event2().Subscribe("getRiddenBindPos", listenerGetRiddenBindPos);

	//int findEmptyRiddenIndex(int index)
	typedef ListenerFunctionRef<int&, int> ListenerFindEmptyRiddenIndex;
	ListenerFindEmptyRiddenIndex* listenerFindEmptyRiddenIndex = SANDBOX_NEW(ListenerFindEmptyRiddenIndex, [self, this](int& ret, int index) -> void {
		if (!self)
		{
			return;
		}
		ret = this->findEmptyRiddenIndex(index);
		});
	Event2().Subscribe("findEmptyRiddenIndex", listenerFindEmptyRiddenIndex);

	//WORLD_ID getRiddenByActorID(int i)
	typedef ListenerFunctionRef<WORLD_ID&, int> ListenerGetRiddenByActorID;
	ListenerGetRiddenByActorID* listenerGetRiddenByActorID = SANDBOX_NEW(ListenerGetRiddenByActorID, [self, this](WORLD_ID& ret, int i) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiddenByActorID(i);
		});
	Event2().Subscribe("getRiddenByActorID", listenerGetRiddenByActorID);

	//void setRiddenByActorObjId(WORLD_ID objId, int i /* = 0 */)
	typedef ListenerFunctionRef<WORLD_ID, int> ListenerSetRiddenByActorObjId;
	ListenerSetRiddenByActorObjId* listenerSetRiddenByActorObjId = SANDBOX_NEW(ListenerSetRiddenByActorObjId, [self, this](WORLD_ID objId, int i) -> void {
		if (!self)
		{
			return;
		}
		this->setRiddenByActorObjId(objId, i);
		});
	Event2().Subscribe("setRiddenByActorObjId", listenerSetRiddenByActorObjId);

	//void resetRiddenBindPos()
	typedef ListenerFunctionRef<> ListenerResetRiddenBindPos;
	ListenerResetRiddenBindPos* listenerResetRiddenBindPos = SANDBOX_NEW(ListenerResetRiddenBindPos, [self, this]() -> void {
		if (!self)
		{
			return;
		}
		this->resetRiddenBindPos();
		});
	Event2().Subscribe("resetRiddenBindPos", listenerResetRiddenBindPos);

	//bool getRiddenBindRot(ClientActor *ridden, Rainbow::Quaternionf &rot)
	typedef ListenerFunctionRef<bool&, ClientActor*, Rainbow::Quaternionf &> ListenerGetRiddenBindRot;
	ListenerGetRiddenBindRot* listenerGetRiddenBindRot = SANDBOX_NEW(ListenerGetRiddenBindRot, [self, this](bool& ret, ClientActor* ridden, Rainbow::Quaternionf & rot) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiddenBindRot(ridden, rot);
		});
	Event2().Subscribe("getRiddenBindRot", listenerGetRiddenBindRot);

	//int getNumRiddenPos()
	typedef ListenerFunctionRef<int&> ListenerGetNumRiddenPos;
	ListenerGetNumRiddenPos* m_listenerGetNumRiddenPos = SANDBOX_NEW(ListenerGetNumRiddenPos, [self, this](int& ret) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getNumRiddenPos();
		});
	Event2().Subscribe("getNumRiddenPos", m_listenerGetNumRiddenPos);

	//ClientActor* getRiddenByActor(int i = 0)
	typedef ListenerFunctionRef<ClientActor*&, int> ListenerGetRiddenByActor;
	ListenerGetRiddenByActor* listenerGetRiddenByActor = SANDBOX_NEW(ListenerGetRiddenByActor, [self, this](ClientActor*& ret, int i) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiddenByActor(i);
		});
	Event2().Subscribe("getRiddenByActor", listenerGetRiddenByActor);

	//bool getRiddenChangeFPSView()
	typedef ListenerFunctionRef<bool&> ListenerGetRiddenChangeFPSView;
	ListenerGetRiddenChangeFPSView* listenerGetRiddenChangeFPSView = SANDBOX_NEW(ListenerGetRiddenChangeFPSView, [self, this](bool& ret) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiddenChangeFPSView();
		});
	Event2().Subscribe("getRiddenChangeFPSView", listenerGetRiddenChangeFPSView);

	//int findRiddenIndex(ClientActor *ridden)
	typedef ListenerFunctionRef<int&, ClientActor*> ListenerFindRiddenIndex;
	ListenerFindRiddenIndex* listenerFindRiddenIndex = SANDBOX_NEW(ListenerFindRiddenIndex, [self, this](int& ret, ClientActor* ridden) -> void {
		if (!self)
		{
			return;
		}
		ret = this->findRiddenIndex(ridden);
		});
	Event2().Subscribe("findRiddenIndex", listenerFindRiddenIndex);

	//bool canBeRided(ClientPlayer *player)
	typedef ListenerFunctionRef<bool&, ClientPlayer*> ListenerCanBeRided;
	ListenerCanBeRided* listenerCanBeRided = SANDBOX_NEW(ListenerCanBeRided, [self, this](bool& ret, ClientPlayer *player) -> void {
		if (!self)
		{
			return;
		}
		ret = this->canBeRided(player);
		});
	Event2().Subscribe("canBeRided", listenerCanBeRided);

	//WCoord getRiderPosition(ClientActor *ridden)
	typedef ListenerFunctionRef<WCoord&, ClientActor*> ListenerGetRiderPosition;
	ListenerGetRiderPosition* listenerGetRiderPosition = SANDBOX_NEW(ListenerGetRiderPosition, [self, this](WCoord& ret, ClientActor *ridden) -> void {
		if (!self)
		{
			return;
		}
		ret = this->getRiderPosition(ridden);
		});
	Event2().Subscribe("getRiderPosition", listenerGetRiderPosition);

	typedef ListenerFunctionRef<WCoord&, const Rainbow::Vector3f&, bool, int> Listener1;
	m_listener1 = SANDBOX_NEW(Listener1, [&](WCoord& origin, const Rainbow::Vector3f& blockpos, bool realpos, int groupid) -> void {
			origin = this->convertWcoord(blockpos, realpos, groupid);
		});
	Event2().Subscribe("ConvertVector3f2WCoord", m_listener1);

	typedef ListenerFunctionRef<bool&, const CollideAABB&> Listener2;
	m_listener2 = SANDBOX_NEW(Listener2, [&](bool& check, const CollideAABB& box) -> void {
		check = this->checkIntersectBox(box);
		});
	Event2().Subscribe("Vehicle_CheckIntersectBox", m_listener2);

	typedef ListenerFunctionRef<bool&, const MINIW::WorldRay&, float&> Listener3;
	m_listener3 = SANDBOX_NEW(Listener3, [&](bool& returnval, const MINIW::WorldRay& worldray, float& t) -> void {
		int id = 0;
		int x, y, z;
		if (this->intersect(worldray, t, id, x, y, z) == false)
			returnval = true;
		else
			returnval = false;
		});
	Event2().Subscribe("Vehicle_Intersect", m_listener3);

}

void ActorVehicleAssemble::init(World *pworld, WCoord start, WCoord dim, int dir, ContainerWorkshop* pContainerWorkshop)
{
	m_pContainerWorkshop = pContainerWorkshop;
	m_WorkshopDirection = dir;
	if (m_pVehicleWorld == NULL)
	{
		m_pVehicleWorld = ENG_NEW(VehicleWorld)(GetWorldManagerPtr());
		m_pVehicleWorld->init(pworld, this);
	}
	//initParts(pworld, start, dim);
	for (int y = 0; y < MAX_DIM_Y; y++)
	{
		for (int z = 0; z < MAX_DIM_Z; z++)
		{
			for (int x = 0; x < MAX_DIM_X; x++)
			{
				m_Blocks[x][y][z]->m_iCurLife = -1;
				m_Blocks[x][y][z]->m_iCurFuel = -1;
				m_Blocks[x][y][z]->m_iCurHeat = 0;
			}
		}
	}
	//	LOG_INFO("ActorVehicleAssemble::init %d ",pContainerWorkshop->getVehicleBlockLine().size());
	if (pContainerWorkshop && pContainerWorkshop->getVehicleBlockLine().size() > 0)
	{
		m_VehicleBlockLines.clear();
		std::vector<VehicleBlockLine> lines = pContainerWorkshop->getVehicleBlockLine();
		m_VehicleBlockLines.swap(lines);
	}
	if (pContainerWorkshop)
		m_BlockGroupNum = pContainerWorkshop->getJointNum() + 1;

	initBlocks(pworld, start, dim);
	m_pWorld = pworld;
	create(pworld);
	resetAttib();

	// 通过车间把对应的动作序列器的container内容传递过去
	TransferActionerDataToVehicle(pworld, pContainerWorkshop);
}

bool ActorVehicleAssemble::CheckOnWaterMask(const Rainbow::BoxBound& worldPosBound)
{
	Rainbow::BoxBound testBound;
	testBound.setRange(m_WaterMaskWorldBound.CalculateMin(), m_WaterMaskWorldBound.CalculateMax());
	return  testBound.intersectBoxBound(worldPosBound);
}

void ActorVehicleAssemble::CheckWaterMaskOnWater()
{
	//calcualte aabb bound
	m_WaterMaskWorldBound.SetCenterAndExtent(Vector3f::zero, Vector3f::one);
	if (m_WaterMaskEntites.size() == 0) return;

	Rainbow::MinMaxAABB aabb = Rainbow::MinMaxAABB::invalid;
	for (size_t idx = 0; idx < m_WaterMaskEntites.size(); idx++)
	{
		if (m_WaterMaskEntites[idx] == NULL) continue;

		int bindCount = m_WaterMaskEntites[idx]->GetBindObjectCount();
		for (size_t bindIdx = 0; bindIdx < bindCount; bindIdx++)
		{
			MechaMeshObject* bindObj = static_cast<MechaMeshObject*>(m_WaterMaskEntites[idx]->GetBindObject(bindIdx));
			const AABB& bound = bindObj->GetMeshWorldBound();
			aabb.m_Min = Minimize(aabb.m_Min, bound.CalculateMin());
			aabb.m_Max = Maximize(aabb.m_Max, bound.CalculateMax());
		}
	}
	m_WaterMaskWorldBound.FromMinMaxAABB(aabb);

	Vector3f maxPos = m_WaterMaskWorldBound.CalculateMax();
	//check is water
	WCoord sectionPos = CoordDivSection(maxPos);
	Section* pSection = m_pWorld->getSectionBySCoord(sectionPos.x, sectionPos.y, sectionPos.z);
	if (pSection != nullptr && pSection->getWaterHeight() > 0) 
	{
		m_WaterMaskOnWater = maxPos.y <= pSection->getWaterHeight();
	}


	if (m_WaterMaskOnWater) 
	{
		//hide
		m_WaterMaskEntites[0]->Show(false);
	}
	else 
	{
		m_WaterMaskEntites[0]->Show(true);
	}





}

void ActorVehicleAssemble::initBlocks(World *pworld, WCoord start, WCoord dim)
{
	if (!pworld)
		return;

	if (start.x > start.x + dim.x)
		start.x = start.x + dim.x;

	if (start.y > start.y + dim.y)
		start.y = start.y + dim.y;

	if (start.z > start.z + dim.z)
		start.z = start.z + dim.z;

	dim.x = Rainbow::Abs(dim.x);
	dim.y = Rainbow::Abs(dim.y);
	dim.z = Rainbow::Abs(dim.z);

	//查找出处于旋转状态的方块，这部分方块需要额外处理
	std::vector< ActorMechaUnit*> actorMechaUnits;
	for (int y = 0; y <= dim.y; y++) {
		for (int z = 0; z <= dim.z; z++) {
			for (int x = 0; x <= dim.x; x++) {
				WCoord pos(start.x + x, start.y + y, start.z + z);

				bool inMecha = false;
				ContainerMecha *mechacontainer = dynamic_cast<ContainerMecha *>(pworld->getContainerMgr()->getContainer(pos));
				if (mechacontainer != NULL)
				{
					ActorMechaUnit *unit = mechacontainer->getBindUnit();
					if (unit != NULL)
					{
						if (unit->getMoveType() == MECHA_MOVE_ROTATE)
						{
							actorMechaUnits.push_back(unit);
						}
					}
				}
			}
		}
	}

	bool initBlock = false;
	int minPosY = -1;
	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				bool inMecha = false;
				WCoord pos(start.x + x, start.y + y, start.z + z);
				if (pworld->getChunk(pos) == NULL)
					pworld->syncLoadChunk(pos, 1);
				Block srcBlock = pworld->getBlock(pos);
				Block tmpBlock;
				if (srcBlock.getResID() == 0)
				{
					for (int i = 0; i < (int)actorMechaUnits.size(); i++)
					{
						std::vector< MechaBlock> mechaBlocks = actorMechaUnits[i]->getMechaBlocks();
						for (int j = 0; j < (int)mechaBlocks.size(); j++)
						{
							if (pos == mechaBlocks[j].pos)
							{
								tmpBlock = mechaBlocks[j].block;
								inMecha = true;
								break;
							}
						}
						if (inMecha) break;
					}
					if (!inMecha) continue;
				}



				if (m_pContainerWorkshop)
				{
					int blockid = inMecha ? tmpBlock.getResID() : srcBlock.getResID();
					int groupid = m_pContainerWorkshop->checkIfConnectCore(pos);
					if (groupid < 0 || groupid >= m_BlockGroupNum)
					{
						continue;
					}

					int oriGroupId = m_pContainerWorkshop->getOriGroupid(pos);
					if (oriGroupId > 0)
						m_Blocks[x][y][z]->m_GroupIdParent = oriGroupId;

					//LOG_INFO("%d %d %d   init blocks pos: %d %d %d blockid: %d groupid: %d",x, y, z, pos.x, pos.y, pos.z, inMecha ? tmpBlock.getResID() : srcBlock.getResID(), groupid);

					if (blockid > 0 && blockid != BLOCK_WORKSHOP) //1085, 是否是车间本身方块, 若是需要排除掉)
					{
						if (inMecha)
							m_Blocks[x][y][z]->m_Block = tmpBlock;
						else
							m_Blocks[x][y][z]->m_Block = srcBlock;
						m_Blocks[x][y][z]->m_GroupIdSelf = groupid;

						if (!initBlock) {
							m_nOffsetYWorkshopAndWorld = y;
							initBlock = true;
						}

						if (y < m_nOffsetYWorkshopAndWorld)
						{
							m_nOffsetYWorkshopAndWorld = y;
						}
					}
				}

			}
		}
	}

	//记录载具中绞绳信息
	if (m_pContainerWorkshop)
	{
		m_Ropes = m_pContainerWorkshop->getVehicleRope();
	}

	//排除掉不需要生成的方块
	excludeBlocks();//在生成时在车间对象中进行剔除了并把只与剔除块相连的方块也一同剔除
}

void ActorVehicleAssemble::clear()
{
	for (int i = 0; i<(int)m_EntitiesChassis.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntitiesChassis[i]);
	}

	for (int i = 0; i<(int)m_EntitiesWheel.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_EntitiesWheel[i]);
	}

	for (int i = 0; i < (int)m_WaterMaskEntites.size(); i++)
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(m_WaterMaskEntites[i]);
	}

	auto iter = m_EntitiesChassisEx.begin();
	while (iter != m_EntitiesChassisEx.end())
	{
		DESTORY_GAMEOBJECT_BY_COMPOENT(iter->second);
		iter++;
	}
	m_EntitiesChassis.clear();
	m_WaterMaskEntites.clear();
	m_EntitiesWheel.clear();
	m_EntitiesChassisEx.clear();
	m_WaterMaskBound.clear();

	for (int i = 0; i<(int)m_VehicleCreateMeshes.size(); i++)
	{
		for (int j = 0; j<(int)m_VehicleCreateMeshes[i].mRawMeshesWheel.size(); j++)
		{
			ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mVerts, kMemPhysics);
			ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheelCollider[j].mVerts, kMemPhysics);
		}
		m_VehicleCreateMeshes[i].clear();
	}
	m_VehicleCreateMeshes.clear();

	m_WheelCoord.clear();

	for (int i = 0; i<(int)m_VehicleCreateParams.size(); i++)
	{
		m_VehicleCreateParams[i].clear();
	}
	m_VehicleCreateParams.clear();

	m_JointCreateParams.clear();

	m_DynamicCreateParams.clear();

	for (int i = 0; i<(int)m_pMechaSections.size(); i++)
	{
		if (m_pMechaSections[i] != nullptr)
		{
			m_pMechaSections[i]->destroy();
		}
	}
	m_pMechaSections.clear();

	m_Suspensions.clear();

	m_BlockPos.clear();

	initAttrib();
}

void ActorVehicleAssemble::initAttrib()
{
	m_LifeCurrent = 0.0f;
	m_LifeLimit = 0.0f;

	m_PowerUse = 0;
	m_PowerAdd = 0;

	m_Mass = 0;
	m_ChassisMass = 0;
	m_ChassisMasses.clear();
	m_ChassisMassCenter = Vector3f(0.0f, 0.0f, 0.0f);
	m_ChassisMassCenters.clear();

	m_VehicleDims = Vector3f(0.0f, 0.0f, 0.0f);

	m_BlockNum = 0;
	m_ChassisBlockNum = 0;
	m_ChassisBlockNums.clear();
	m_WheelBlockNum = 0;

	m_isCreateFromLoad = false;

	m_SeatPos = WCoord(0, 0, 0);
	m_SeatRelativePos = WCoord(0, 0, 0);
	m_OtherSeatPos.clear();
	if (!m_isReCreate)
		m_OtherRiddens.clear();

	m_SeatDir = 0;
	m_OtherSeatDir.clear();

	m_EngineNum = 0;
	m_Engines.clear();
	m_hasSteeringSwitch = false;
	m_hasfuel = true;
	for (auto iter = m_CostParts.begin(); iter != m_CostParts.end(); iter++)
	{
		//delete *iter;
		ENG_DELETE(*iter);
	}
	m_CostParts.clear();
	for (auto iter = m_Overheats.begin(); iter != m_Overheats.end(); iter++)
	{
		//delete *iter;
		ENG_DELETE(*iter);
	}
	m_Overheats.clear();
	m_OneLineStart = -1;
	m_FindBlockPosition = WCoord(0, 0, 0);
	m_CurOperateKeys.clear();
}

inline void CalculateWhellMeshAABB(Rainbow::MinMaxAABB& aabb, const BlockVertPos& pos)
{
	aabb.m_Min.x = Rainbow::Min(aabb.m_Min.x, pos.x);
	aabb.m_Min.y = Rainbow::Min(aabb.m_Min.y, pos.y);
	aabb.m_Min.z = Rainbow::Min(aabb.m_Min.z, pos.z);

	aabb.m_Max.x = Rainbow::Max(aabb.m_Max.x, pos.x);
	aabb.m_Max.y = Rainbow::Max(aabb.m_Max.y, pos.y);
	aabb.m_Max.z = Rainbow::Max(aabb.m_Max.z, pos.z);
}

void ActorVehicleAssemble::createWheelMesh(MechaMeshObject *wheel_pmesh, Rainbow::RAWMesh& rawMesh, WCoord localpos, float angle, WCoord coord, int groupid)
{
	int submeshnum = wheel_pmesh->GetSubMeshCount();
	int phyvert = 0;
	for (int i = 0; i<submeshnum; i++)
	{
		SectionSubMesh *pSubMesh = wheel_pmesh->GetSubMeshAt(i);
		phyvert += pSubMesh->GetVertexCount();
	}


	Quaternionf quat = AngleEulerToQuaternionf(Vector3f(0, -angle, 0));
	rawMesh.mNbVerts = phyvert;
	rawMesh.mVerts = (Vector3f*)ENG_MALLOC_LABEL(rawMesh.mNbVerts * sizeof(Vector3f), kMemPhysics);
	memset(rawMesh.mVerts, 0, sizeof(Vector3f)*phyvert);
	rawMesh.mName = "wheelshape";
	rawMesh.mPos = localpos.toVector3();   //  - m_nOffsetYWorkshopAndWorld * BLOCK_SIZE
																								  // LOG_INFO("wheel mesh pos: %f %f %f coord: %d %d %d", rawMesh.mPos.p.x, rawMesh.mPos.p.y, rawMesh.mPos.p.z, coord.x, coord.y, coord.z);
	dynamic_array<Vector3f> colliderVert(kMemTempAlloc);
	int colliderMeshVertCount = 0;
	phyvert = 0;
	//取得的mesh需要将中心点移动到原点
	for (int i = 0; i < submeshnum; i++)
	{
		SectionSubMesh *pSubMesh = wheel_pmesh->GetSubMeshAt(i);
		SectionSubMesh::MeshInfo* meshInfo = pSubMesh->GetMeshInfo(0);
		Rainbow::MinMaxAABB minmaxAABB;
		for (int j = 0; j < (int)pSubMesh->GetVertexCount() / 3; j++)
		{
			BlockVertPos& v0 = pSubMesh->GetGeomVertex(j * 3).pos;
			BlockVertPos& v1 = pSubMesh->GetGeomVertex(j * 3 + 1).pos;
			BlockVertPos& v2 = pSubMesh->GetGeomVertex(j * 3 + 2).pos;

			v0.x = v0.x - localpos.x - BLOCK_SIZE / 2;
			v0.y = v0.y - localpos.y - BLOCK_SIZE / 2;
			v0.z = v0.z - localpos.z - BLOCK_SIZE / 2;

			v1.x = v1.x - localpos.x - BLOCK_SIZE / 2;
			v1.y = v1.y - localpos.y - BLOCK_SIZE / 2;
			v1.z = v1.z - localpos.z - BLOCK_SIZE / 2;

			v2.x = v2.x - localpos.x - BLOCK_SIZE / 2;
			v2.y = v2.y - localpos.y - BLOCK_SIZE / 2;
			v2.z = v2.z - localpos.z - BLOCK_SIZE / 2;

			rawMesh.mVerts[j * 3] = quat * Vector3f(v0);
			rawMesh.mVerts[j * 3 + 1] = quat * Vector3f(v1);
			rawMesh.mVerts[j * 3 + 2] = quat * Vector3f(v2);
			phyvert += 3;

			if (v0.y > 0 && v1.y > 0 && v2.y > 0)
			{
				colliderVert.push_back(rawMesh.mVerts[j * 3]);
				colliderVert.push_back(rawMesh.mVerts[j * 3 + 1]);
				colliderVert.push_back(rawMesh.mVerts[j * 3 + 2]);
				colliderMeshVertCount += 3;
			}
			CalculateWhellMeshAABB(minmaxAABB, v0);
			CalculateWhellMeshAABB(minmaxAABB, v1);
			CalculateWhellMeshAABB(minmaxAABB, v2);
		}
		meshInfo->m_AABB.Encapsulate(minmaxAABB);
	}
	Rainbow::RAWMesh colliderMesh;
	colliderMesh.mNbVerts = colliderMeshVertCount;
	colliderMesh.mVerts = (Vector3f*)ENG_MALLOC_LABEL(colliderMeshVertCount * sizeof(Vector3f), kMemPhysics);
	Memcpy(colliderMesh.mVerts, colliderVert.data(), sizeof(Vector3f) * colliderMeshVertCount);
	colliderMesh.mName = "wheelshape";
	colliderMesh.mPos = localpos.toVector3();   //  - m_nOffsetYWorkshopAndWorld * BLOCK_SIZE
	m_VehicleCreateMeshes[groupid].mRawMeshesWheelCollider.push_back(colliderMesh);
	m_VehicleCreateMeshes[groupid].mRawMeshesWheel.push_back(rawMesh);
	
	WheelPos temp;
	temp.wcoord = coord;
	temp.angle = angle;
	m_WheelCoord.push_back(temp);

#ifndef IWORLD_SERVER_BUILD
	wheel_pmesh->UploadMeshData();
	Entity* wheel_entity = Entity::Create();
	wheel_entity->AttachToScene(m_pWorld->getScene());
	wheel_pmesh->SetRotation(angle, 0, 0);
	wheel_entity->BindObject(0, wheel_pmesh);
	//wheel_pmesh->elease();
	m_EntitiesWheel.push_back(wheel_entity);
	//wheel_entity->ddRenderUsageBits(RU_SHADOWMAP);
	//wheel_pmesh->addRenderUsageBits(RU_SHADOWMAP);
#else
	//wheel_pmesh->release();
#endif

}

void ActorVehicleAssemble::createChassisEntity(MechaMeshObject *chassis_pmesh, const Rainbow::Vector3f& offset)
{
#ifndef IWORLD_SERVER_BUILD
	Rainbow::Entity* chassis_entity = Rainbow::Entity::Create();
	chassis_entity->AttachToScene(m_pWorld->getScene());
	chassis_pmesh->OnCreate();
	chassis_pmesh->SetPosition(Rainbow::Vector3f(-BLOCK_SIZE / 2 - offset.x, -offset.y, -BLOCK_SIZE / 2 - offset.z));  // -m_nOffsetYWorkshopAndWorld * BLOCK_SIZE
	chassis_entity->BindObject(0, chassis_pmesh);
	//chassis_pmesh->release();
	//chassis_entity->addRenderUsageBits(RU_SHADOWMAP);
	//chassis_pmesh->addRenderUsageBits(RU_SHADOWMAP);
	m_EntitiesChassis.push_back(chassis_entity);

	Vector4f lightparam(0, 0, 0, 0);
	WCoord center = getMassCenterWcoord();
	center.y += getLocoMotion()->m_BoundHeight / 2 + 100;
	if (m_pWorld)
	{
		Vector4f lightparams[3] = {};
		CollideAABB box;
		WCoord start, dims;
		getVehicleColliderBox(start, dims);
		box.dim = dims;
		box.pos = start;
		WCoord wcoord_ = CoordDivBlock(center - dims / 2);
		m_pWorld->getBlockLightValue2_Air(lightparams[0].x, lightparams[0].y, wcoord_);
		m_pWorld->getBlockLightValue2_Air(lightparams[1].x, lightparams[1].y, CoordDivBlock(center + dims / 2));
		m_pWorld->getBlockLightValue2_Air(lightparams[2].x, lightparams[2].y, CoordDivBlock(center));
		lightparam.x = max(max(lightparams[0].x, lightparams[1].x), lightparams[2].x);
		lightparam.y = max(max(lightparams[0].y, lightparams[1].y), lightparams[2].y);
	}

	MechaSectionMesh *mesh = dynamic_cast<MechaSectionMesh*>(chassis_pmesh);
	if (mesh)
	{
		mesh->setLight(lightparam.x, lightparam.y);
	}
#else
	//chassis_pmesh->release();
#endif	
}

void ActorVehicleAssemble::createChassisBoxMesh(int groupid, const Rainbow::Vector3f& offset)
{
	WCoord start = WCoord(0, 0, 0);
	WCoord centerPos = WCoord(start.x + MAX_DIM_X / 2, start.y, start.z + MAX_DIM_Z / 2);
	memset(&colbits[0][0][0], 0, MAX_DIM_X * MAX_DIM_Y * MAX_DIM_Z);
	memset(&colbits_4x4[0][0][0], 0, MAX_DIM_X * 4 * MAX_DIM_X * 4 * MAX_DIM_X * 4);

	memset(&colbits_4x4_check[0], 0, 4 * 4 * 4);
	bool havePhisicMeshBit = false;
	for (int y = 0; y<MAX_DIM_Y; y++)
	{
		for (int z = 0; z<MAX_DIM_Z; z++)
		{
			for (int x = 0; x<MAX_DIM_X; x++)
			{
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (m_Blocks[x][y][z]->m_iCurLife == 0 || srcBlock->isEmpty() || m_Blocks[x][y][z]->m_GroupIdSelf != groupid)
					continue;

				int blockid = srcBlock->getResID();
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
				if (!physicsPartsDef)
					continue;


				////测试绞绳的刚体创建
				//if (blockid == 1167)
				//{
				//	WCoord ropeBlockPos(x, y, z);
				//	WCoord blockpos = ropeBlockPos - centerPos;
				//	createRopeBoxMesh(groupid, blockpos, 1);
				//	continue;
				//}

				WCoord tmpBlockPos(x, y, z);
				WCoord blockpos = tmpBlockPos - centerPos;
				if (IsFlexibleBlock(blockid))
				{
					int dir = srcBlock->getData() & 7;
					bool power = srcBlock->getData() & 8;

					if (BLOCK_JOINT_ARM_PRISMATIC == blockid)
					{
						WorldContainer *container = m_pVehicleWorld->getContainerMgr()->getContainer(WCoord(x, y, z));
						int extendSize = 0;
						if (container)
						{
							ContainerArmPrismatic* arm = dynamic_cast<ContainerArmPrismatic*>(container);
							if (arm)
							{
								extendSize = arm->getExtendSize();
							}
						}
						createFlexibleBoxMesh(groupid, blockpos, dir, extendSize);
					}
					else
					{
						if (!power)
						{
							createFlexibleBoxMesh(groupid, blockpos, dir, 0);
						}
						else if (BLOCK_SPRING_IRON == blockid || BLOCK_SPRING_GOLD == blockid)
						{
							createFlexibleBoxMesh(groupid, blockpos, dir, 1);
						}
					}
					continue;
				}
				else if (IsDistortionBlock(blockid) || IsDoorBlock(blockid))
				{
					createDistortionMesh(blockid, groupid, tmpBlockPos, offset);
					continue;
				}
				else if (IsWindowsBlock(blockid))
				{
					bool isupper;
					bool isopen;
					bool ismirror;
					WindowsMaterial::ParseWindowDataInVehicle(m_pVehicleWorld, tmpBlockPos, isupper, isopen, ismirror);
					if (!isupper)
					{
						createDistortionMesh(blockid, groupid, tmpBlockPos, offset);
					}
					continue;
				}
				else if (blockid == BLOCK_CLAW)
				{
					//m_ClawBlocks.push_back(WCoord(x, y, z));
					if (m_BindVehilcIDs.find((x << 10) + (y << 5) + z) != m_BindVehilcIDs.end())
					{
						continue;
					}
				}

				if (isFunction(blockid, WHEELFUN))
					continue;
				auto moveCollide = -1;
				const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
				BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl)
				{//如果mtl 不为空 则不直接使用m_def->MoveCollide
					moveCollide = mtl->getBlockMoveCollide();
				}
				else {

					if (blockDef)
					{
						moveCollide = blockDef->MoveCollide;
					}
				}

				if (blockDef && (moveCollide == 0 || moveCollide == 2 || moveCollide == 5))
					continue;

				//const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(blockid);
				////自定义形状需要单独处理
				//if (physicsActorDef->ShapeID == 10)
				//{

				//	continue;
				//}
				//BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl && m_Blocks[x][y][z]->m_GroupIdSelf < (int)m_pMechaSections.size())
				{
					char * one_block_bit_4x4 = mtl->getPhisicMeshBit(m_pMechaSections[m_Blocks[x][y][z]->m_GroupIdSelf], WCoord(x, y, z) - centerPos);
					if (one_block_bit_4x4)
					{
						for (int m = 0; m<4; m++)
						{
							for (int n = 0; n<4; n++)
							{
								for (int j = 0; j<4; j++)
								{
									colbits_4x4[4 * y + n][4 * z + j][4 * x + m] = one_block_bit_4x4[m * 4 * 4 + n * 4 + j];
								}
							}
						}
						colbits_4x4_check[(y / 8) * 16 + (z / 8) * 4 + x / 8] = 1;
						havePhisicMeshBit = true;
					}
					else
						colbits[y][z][x] = 1;
				}
				else
					colbits[y][z][x] = 1;
			}
		}
	}


	int i = groupid;
	if (m_ChassisBlockNums[i] > 0)
	{
		//修正分组刚体偏移位置, 以造成分组的关节位置为基准
		//Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
		if (i > 0 && m_JointCreateParams.size() > 0)
		{
			//车轮位置需要根据关节位置进行修正
			for (int j = 0; j <(int)m_VehicleCreateMeshes[i].mRawMeshesWheel.size(); j++)
			{
				m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mPos = Vector3f(m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mPos.x - offset.x, m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mPos.y - offset.y, m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mPos.z - offset.z);
			}

			for (int j = 0; j < (int)m_VehicleCreateMeshes[i].mBoxMeshesRope.size(); j++)
			{
				for (int k = 0; k <(int)m_VehicleCreateMeshes[i].mBoxMeshesRope[j].mPos.size(); k++)
				{
					m_VehicleCreateMeshes[i].mBoxMeshesRope[j].mPos[k] = Vector3f(m_VehicleCreateMeshes[i].mBoxMeshesRope[j].mPos[k].x - offset.x, m_VehicleCreateMeshes[i].mBoxMeshesRope[j].mPos[k].y - offset.y, m_VehicleCreateMeshes[i].mBoxMeshesRope[j].mPos[k].z - offset.z);
				}
			}

			for (int j = 0; j < (int)m_VehicleCreateMeshes[i].mBoxMeshesFlexible.size(); j++)
			{
				for (int k = 0; k <(int)m_VehicleCreateMeshes[i].mBoxMeshesFlexible[j].mPos.size(); k++)
				{
					m_VehicleCreateMeshes[i].mBoxMeshesFlexible[j].mPos[k] = Vector3f(m_VehicleCreateMeshes[i].mBoxMeshesFlexible[j].mPos[k].x - offset.x, m_VehicleCreateMeshes[i].mBoxMeshesFlexible[j].mPos[k].y - offset.y, m_VehicleCreateMeshes[i].mBoxMeshesFlexible[j].mPos[k].z - offset.z);
				}
			}

			// 			for (int j = 0; j < (int)m_VehicleCreateMeshes[i].mBoxMeshesDistortion.size(); j++)
			// 			{
			// 				for (int k = 0; k < (int)m_VehicleCreateMeshes[i].mBoxMeshesDistortion[j].mPos.size(); k++)
			// 				{
			// 					m_VehicleCreateMeshes[i].mBoxMeshesDistortion[j].mPos[k] = Vector3f(m_VehicleCreateMeshes[i].mBoxMeshesDistortion[j].mPos[k].x - offset.x, m_VehicleCreateMeshes[i].mBoxMeshesDistortion[j].mPos[k].y - offset.y, m_VehicleCreateMeshes[i].mBoxMeshesDistortion[j].mPos[k].z - offset.z);
			// 				}
			// 			}

		}
	}

	int blockid = 0;
	Rainbow::BoxMesh boxMesh;
	for (int y = 0; y<MAX_DIM_Y; y++)
	{
		for (int z = 0; z<MAX_DIM_Z; z++)
		{
			for (int x = 0; x<MAX_DIM_X; x++)
			{
				if (colbits[y][z][x] == 0) continue;

				blockid = m_Blocks[x][y][z]->m_Block.getResID();
				if (930 == blockid)
				{
					continue;
				}


				// 				LOG_INFO("groupid: %d  pos: %d %d %d  blockid: %d", groupid, x, y, z, blockid );

				int xw = 1;
				int yw = 1;
				int zw = 1;

				//x方向延伸，有标志位的方块连在一起
				for (; x + xw<MAX_DIM_X && colbits[y][z][x + xw] == 1; xw++) {}

				//z和x方向延伸，有标志位的方块连在一起
				bool done = false;
				for (; z + zw<MAX_DIM_Z; zw++)
				{
					for (int k = 0; k<xw; k++)
					{
						if (colbits[y][z + zw][x + k] == 0) { done = true; break; }
					}
					if (done) break;
				}

				//y和z，x方向延伸，有标志位的方块连在一起
				done = false;
				for (; y + yw<MAX_DIM_Y; yw++)
				{
					for (int m = 0; m<zw; m++)
					{
						for (int k = 0; k<xw; k++)
						{
							if (colbits[y + yw][z + m][x + k] == 0) { done = true; break; }
						}
						if (done) break;

					}
					if (done) break;
				}

				WCoord pos(x*BLOCK_SIZE + BLOCK_SIZE * xw / 2, y*BLOCK_SIZE + BLOCK_SIZE * yw / 2, z*BLOCK_SIZE + BLOCK_SIZE * zw / 2);
				WCoord localpos = pos - BlockCenterCoord(centerPos);
				localpos.y += BLOCK_SIZE / 2;



				Vector3f tmpPos(localpos.x - offset.x, localpos.y - offset.y, localpos.z - offset.z);
				Vector3f exten(BLOCK_FSIZE*xw / 2, BLOCK_FSIZE*yw / 2, BLOCK_FSIZE*zw / 2);

				boxMesh.mPos.push_back(tmpPos);
				boxMesh.mExten.push_back(exten);


				//将已经处理的方块清除
				for (int n = 0; n < yw; n++)
				{
					for (int m = 0; m < zw; m++)
					{
						for (int k = 0; k < xw; k++)
						{
							colbits[y + n][z + m][x + k] = 0;
						}
					}
				}

			}
		}
	}

	if (havePhisicMeshBit)
	{
		for (int y0 = 0; y0<4; y0++)
		{
			for (int z0 = 0; z0<4; z0++)
			{
				for (int x0 = 0; x0<4; x0++)
				{
					if (!colbits_4x4_check[y0 * 4 * 4 + z0 * 4 + x0])
					{
						continue;
					}
					for (int y_ = 0; y_<MAX_DIM_Y; y_++)
					{
						for (int z_ = 0; z_<MAX_DIM_Z; z_++)
						{
							for (int x_ = 0; x_<MAX_DIM_X; x_++)
							{
								int y = y0*MAX_DIM_Y + y_;
								int z = z0*MAX_DIM_Z + z_;
								int x = x0*MAX_DIM_X + x_;
								if (colbits_4x4[y][z][x] == 0) continue;
								int xw = 1;
								int yw = 1;
								int zw = 1;

								//x方向延伸，有标志位的方块连在一起
								for (; x + xw<MAX_DIM_X * 4 && colbits_4x4[y][z][x + xw] != 0; xw++) {}

								//z和x方向延伸，有标志位的方块连在一起
								bool done = false;
								for (; z + zw<MAX_DIM_Z * 4; zw++)
								{
									for (int k = 0; k<xw; k++)
									{
										if (colbits_4x4[y][z + zw][x + k] == 0) { done = true; break; }
									}
									if (done) break;
								}

								//y和z，x方向延伸，有标志位的方块连在一起
								done = false;
								for (; y + yw<MAX_DIM_Y * 4; yw++)
								{
									for (int m = 0; m<zw; m++)
									{
										for (int k = 0; k<xw; k++)
										{
											if (colbits_4x4[y + yw][z + m][x + k] == 0) { done = true; break; }
										}
										if (done) break;

									}
									if (done) break;
								}

								WCoord pos(x*BLOCK_SIZE / 4 + BLOCK_SIZE / 4 * xw / 2, y*BLOCK_SIZE / 4 + BLOCK_SIZE / 4 * yw / 2, z*BLOCK_SIZE / 4 + BLOCK_SIZE / 4 * zw / 2);
								WCoord localpos = pos - BlockCenterCoord(centerPos);
								localpos.y += BLOCK_SIZE / 2;

								Vector3f tmpPos(localpos.x - offset.x, localpos.y - offset.y, localpos.z - offset.z);
								Vector3f exten(BLOCK_FSIZE / 4 * xw / 2, BLOCK_FSIZE / 4 * yw / 2, BLOCK_FSIZE / 4 * zw / 2);

								boxMesh.mPos.push_back(tmpPos);
								boxMesh.mExten.push_back(exten);


								//将已经处理的方块清除
								for (int n = 0; n < yw; n++)
								{
									for (int m = 0; m < zw; m++)
									{
										for (int k = 0; k < xw; k++)
										{
											colbits_4x4[y + n][z + m][x + k] = 0;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	m_VehicleCreateMeshes[groupid].mBoxMeshesChassis.push_back(boxMesh);
}

void ActorVehicleAssemble::createRopeBoxMesh(int groupid, WCoord blockpos, int dir, const Rainbow::Vector3f& offset)
{
	//先使用长方体来实现，后面是否需要优化成圆柱体
	WCoord pos(blockpos.x*BLOCK_SIZE, blockpos.y*BLOCK_SIZE, blockpos.z*BLOCK_SIZE);
	WCoord localpos = pos;
	//localpos.x = localpos.x + BLOCK_SIZE/2 - 5;
	localpos.y = localpos.y + BLOCK_SIZE / 2;
	Vector3f localexten;

	LOG_INFO("rope block pos: %d %d %d", blockpos.x, blockpos.y, blockpos.z);

	//先只考虑竖直的情况
	/*localpos.x = localpos.x - BLOCK_SIZE / 2;
	localpos.z = localpos.z - BLOCK_SIZE / 2;*/
	localexten = Vector3f(BLOCK_FSIZE*0.1f / 2, BLOCK_FSIZE / 2, BLOCK_FSIZE * 0.1f / 2);

	/*if (dir == DIR_NEG_X)
	{
	localpos.x = localpos.x - BLOCK_SIZE / 2 + 5;
	localexten = Vector3f(BLOCK_FSIZE*0.1 / 2, BLOCK_FSIZE * 2 / 2, BLOCK_FSIZE * 1 / 2);
	}
	else if (dir == DIR_POS_X)
	{
	localpos.x = localpos.x + BLOCK_SIZE / 2 - 5;
	localexten = Vector3f(BLOCK_FSIZE*0.1 / 2, BLOCK_FSIZE * 2 / 2, BLOCK_FSIZE * 1 / 2);
	}
	else if (dir == DIR_NEG_Z)
	{
	localpos.z = localpos.z - BLOCK_SIZE / 2 + 5;
	localexten = Vector3f(BLOCK_FSIZE * 1 / 2, BLOCK_FSIZE * 2 / 2, BLOCK_FSIZE*0.1 / 2);
	}
	else if (dir == DIR_POS_Z)
	{
	localpos.z = localpos.z + BLOCK_SIZE / 2 - 5;
	localexten = Vector3f(BLOCK_FSIZE * 1 / 2, BLOCK_FSIZE * 2 / 2, BLOCK_FSIZE*0.1 / 2);
	}*/

	Vector3f tmpPos(localpos.x - offset.x, localpos.y - offset.y, localpos.z - offset.z);
	Vector3f exten(localexten.x, localexten.y, localexten.z);

	Rainbow::BoxMesh boxMesh;

	boxMesh.mPos.push_back(tmpPos);
	boxMesh.mExten.push_back(exten);
	boxMesh.mBlockPos.push_back(Vector3f((float)(blockpos.x + MAX_DIM_X / 2), (float)blockpos.y, (float)(blockpos.z + MAX_DIM_Z / 2)));

	//---joker 修改车身
	//m_VehicleCreateMeshes[groupid].mBoxMeshesRope.push_back(boxMesh);

	m_VehicleCreateMeshes[groupid].mBoxMeshesChassis.push_back(boxMesh);
}


void ActorVehicleAssemble::createWaterMaskEntity(Rainbow::MechaMeshObject* mechaMeshObj, Rainbow::BoxBound& boxBound, const Rainbow::Vector3f& offset)
{
#ifndef IWORLD_SERVER_BUILD
	initWaterMaskMaterial();
	//MechaSectionMesh* mesh = dynamic_cast<MechaSectionMesh*>(sectionMesh);
	Rainbow::Entity* entity = Rainbow::Entity::Create();
	entity->AttachToScene(m_pWorld->getScene());
	mechaMeshObj->OnCreate();
	mechaMeshObj->SetCustomMaterial(m_WaterMaskMat);
	mechaMeshObj->SetPosition(Rainbow::Vector3f(-BLOCK_SIZE / 2 - offset.x, -offset.y, -BLOCK_SIZE / 2 - offset.z)); 
	entity->BindObject(0, mechaMeshObj);
	m_WaterMaskEntites.push_back(entity);




	Vector3f minPos = boxBound.getMinPos();
	Vector3f maxPos = boxBound.getMaxPos();
	minPos += offset;
	maxPos += offset;
	boxBound.setRange(minPos, maxPos);
#endif	
}



void ActorVehicleAssemble::createFlexibleBoxMesh(int groupid, WCoord blockpos, int dir, int nFlexibleSize, const Rainbow::Vector3f& offset)
{
	WCoord pos(blockpos.x*BLOCK_SIZE, blockpos.y*BLOCK_SIZE, blockpos.z*BLOCK_SIZE);
	WCoord localpos = pos;// +BLOCK_SIZE / 2;
	localpos.y += BLOCK_SIZE / 2;
	// 	localpos.y = localpos.y + BLOCK_SIZE / 1;
	Vector3f localexten = Vector3f(BLOCK_FSIZE / 2, BLOCK_FSIZE / 2, BLOCK_FSIZE / 2);
	if (0 < nFlexibleSize)
	{
		if (DIR_POS_X == dir)
		{
			localpos.x += (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.x = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
		else if (DIR_NEG_X == dir)
		{
			localpos.x -= (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.x = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
		else if (DIR_POS_Z == dir)
		{
			localpos.z += (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.z = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
		else if (DIR_NEG_Z == dir)
		{
			localpos.z -= (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.z = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
		else if (DIR_POS_Y == dir)
		{
			localpos.y += (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.y = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
		else if (DIR_NEG_Y == dir)
		{
			localpos.y -= (BLOCK_SIZE * nFlexibleSize) / 2;
			localexten.y = (BLOCK_FSIZE + BLOCK_FSIZE * nFlexibleSize) / 2;
		}
	}
	Vector3f tmpPos(localpos.x - offset.x, localpos.y - offset.y, localpos.z - offset.z);
	Vector3f exten(localexten.x, localexten.y, localexten.z);

	Rainbow::BoxMesh boxMesh;

	boxMesh.mPos.push_back(tmpPos);
	boxMesh.mExten.push_back(exten);
	boxMesh.mBlockPos.push_back(Vector3f((float)(blockpos.x + MAX_DIM_X / 2), (float)blockpos.y, (float)(blockpos.z + MAX_DIM_Z / 2)));

	m_VehicleCreateMeshes[groupid].mBoxMeshesFlexible.push_back(boxMesh);
}

void ActorVehicleAssemble::createDistortionMesh(int blockid, int groupid, WCoord blockpos, const Rainbow::Vector3f& offset)
{
	if (groupid < (int)m_pMechaSections.size())
	{
		Rainbow::BoxMesh boxMesh;
		if (getSingleModeMesh(boxMesh, blockid, groupid, blockpos, offset))
		{
			m_VehicleCreateMeshes[groupid].mBoxMeshesFlexible.push_back(boxMesh);
		}
	}
}

bool ActorVehicleAssemble::getSingleModeMesh(Rainbow::BoxMesh &mesh, int blockid, int groupid, WCoord pos, const Rainbow::Vector3f& offset)
{
	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
	if (mtl && groupid < (int)m_pMechaSections.size())
	{
		int precision = 0;
		int size = 0;
		char * one_block_bit = mtl->getPhisicMeshBitWithWorld(m_pVehicleWorld, pos, precision, size);
		if (one_block_bit && precision > 0 && size > 0)
		{
			WCoord start = WCoord(0, 0, 0);
			WCoord centerPos = WCoord(start.x + MAX_DIM_X / 2, start.y, start.z + MAX_DIM_Z / 2);

			int totalsize = precision * size;
			char ***colbits = new char**[totalsize];
			for (int i = 0; i < totalsize; i++)
			{
				colbits[i] = new char*[totalsize];
				for (int j = 0; j < totalsize; j++)
				{
					colbits[i][j] = new char[totalsize];
				}
			}

			for (int m = 0; m < totalsize; m++)
			{
				for (int n = 0; n < totalsize; n++)
				{
					for (int j = 0; j < totalsize; j++)
					{
						colbits[n][j][m] = one_block_bit[m * totalsize * totalsize + n * totalsize + j];
					}
				}
			}

			for (int y0 = 0; y0 < totalsize; y0++)
			{
				for (int z0 = 0; z0 < totalsize; z0++)
				{
					for (int x0 = 0; x0 < totalsize; x0++)
					{
						int y = y0;
						int z = z0;
						int x = x0;
						if (colbits[y0][z0][x0] == 0) continue;
						int xw = 1;
						int yw = 1;
						int zw = 1;
						//x方向延伸，有标志位的方块连在一起
						for (; x + xw < totalsize && colbits[y][z][x + xw] != 0; xw++) {}
						//z和x方向延伸，有标志位的方块连在一起
						bool done = false;
						for (; z + zw < totalsize; zw++)
						{
							for (int k = 0; k < xw; k++)
							{
								if (colbits[y][z + zw][x + k] == 0) { done = true; break; }
							}
							if (done) break;
						}
						//y和z，x方向延伸，有标志位的方块连在一起
						done = false;
						for (; y + yw < totalsize; yw++)
						{
							for (int m = 0; m < zw; m++)
							{
								for (int k = 0; k < xw; k++)
								{
									if (colbits[y + yw][z + m][x + k] == 0) { done = true; break; }
								}
								if (done) break;

							}
							if (done) break;
						}

						WCoord tpos(x*BLOCK_SIZE / precision + BLOCK_SIZE / precision * xw / 2, y*BLOCK_SIZE / precision + BLOCK_SIZE / precision * yw / 2, z*BLOCK_SIZE / precision + BLOCK_SIZE / precision * zw / 2);
						tpos += pos * BLOCK_SIZE;
						WCoord localpos = tpos - BlockCenterCoord(centerPos);
						localpos.y += BLOCK_SIZE / 2;

						Vector3f tmpPos(localpos.x - offset.x, localpos.y - offset.y, localpos.z - offset.z);
						Vector3f exten(BLOCK_FSIZE / precision * xw / 2, BLOCK_FSIZE / precision * yw / 2, BLOCK_FSIZE / precision * zw / 2);

						LOG_INFO("~!POS y = %f, z = %f, x = %f size x = %f, y = %f, z = %f", tmpPos.y, tmpPos.x, tmpPos.z, exten.x, exten.y, exten.z);
						// 					LOG_INFO("~!POS y = %f, z = %f, x = %f size x = %f, y = %f, z = %f", tpos.y, tpos.x, tpos.z, exten.x, exten.y, exten.z);
						mesh.mPos.push_back(tmpPos);
						mesh.mExten.push_back(exten);
						mesh.mBlockPos.push_back(Vector3f((float)(pos.x), (float)(pos.y), (float)(pos.z)));

						//将已经处理的方块清除
						for (int n = 0; n < yw; n++)
						{
							for (int m = 0; m < zw; m++)
							{
								for (int k = 0; k < xw; k++)
								{
									colbits[y + n][z + m][x + k] = 0;
								}
							}
						}
					}
				}
			}


			for (int i = 0; i < totalsize; i++)
			{
				for (int j = 0; j < totalsize; j++)
				{
					delete[] colbits[i][j];
				}
				delete[] colbits[i];
			}
			delete[] colbits;
			return true;
		}
		return false;
	}
	return false;
}

void ActorVehicleAssemble::changeDistortionmesh(WCoord blockpos)
{
	VehicleBlock* vehicleBlock = getVehicleBlock(blockpos);
	if (vehicleBlock  &&  vehicleBlock->m_Block.getResID() > 0)
	{
		int groupid = vehicleBlock->m_GroupIdSelf;
		VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());

		auto itera = locoVehicle->m_PhysGroupExData.find(groupid);
		if (itera == locoVehicle->m_PhysGroupExData.end() || locoVehicle->m_PhysActorVec[itera->second] == NULL)
		{
			return;
		}
		int nPartIdx = itera->second;
		Rainbow::RigidBaseActor* vehicleActor = locoVehicle->m_PhysActorVec[nPartIdx];

		if (locoVehicle->m_mapFlexibleShapes.find(blockpos) != locoVehicle->m_mapFlexibleShapes.end() && locoVehicle->m_mapFlexibleShapes[blockpos].size())
		{
			for (size_t i = 0; i < locoVehicle->m_mapFlexibleShapes[blockpos].size(); i++)
			{
				locoVehicle->m_pWorld->m_PhysScene->detachShapeFromRigidActor((*vehicleActor), (*locoVehicle->m_mapFlexibleShapes[blockpos][i]));
			}
			locoVehicle->m_mapFlexibleShapes[blockpos].clear();

			bool kinematic = locoVehicle->m_pWorld->isRemoteMode();
			float staticFriction = 0;
			float dynamicFriction = 0;
			float restitution = 0;
			void * userData = NULL;
			if (m_VehicleCreateMeshes[nPartIdx].mRawMeshesWheel.size() <= 0)
			{
				VehicleManagerParam vehicleManagerParam = locoVehicle->m_pWorld->m_PhysScene->GetVehicleManager()->getVehicleManagerParam();
				staticFriction = vehicleManagerParam.mStaticFrictionNowheel;
				dynamicFriction = vehicleManagerParam.mDynamicFrictionNowheel;
				restitution = vehicleManagerParam.mRestitutionNowheel;
			}
			else
			{
				if (kinematic)
				{
					staticFriction = 0.2f;
					dynamicFriction = 0.2f;
				}
				else
				{
					userData = this;
					staticFriction = 0.8f;
					dynamicFriction = 0.8f;
				}
				restitution = 0.3f;
			}
			SharePtr<PhysicMaterial> mtl = PhysXManager::GetInstance().GetOrCreatePhysicMaterial(staticFriction, dynamicFriction, restitution);

			Rainbow::BoxMesh boxMesh;
			Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
			if (groupid > 0 && m_JointCreateParams.size() > 1)
			{
				offset = m_JointCreateParams[groupid].mPos0;
			}
			if (getSingleModeMesh(boxMesh, vehicleBlock->m_Block.getResID(), groupid, blockpos, offset))
			{
				locoVehicle->m_pWorld->m_PhysScene->attachShapesToRigidActor((*vehicleActor), mtl, boxMesh, userData, kinematic, locoVehicle->m_mapFlexibleShapes[blockpos]);
			}
		}

	}
}



void ActorVehicleAssemble::create(World *pworld, bool havemesh)
{
	//之前已经创建过一次, 不需要重复创建
	if (m_BlockNum > 0)
		return;
	m_ClawBlocks.clear();
	m_bHasBoatThrushter = false;
#ifndef IWORLD_SERVER_BUILD
	for (int i = 0; i<(int)m_ClawEffects.size(); i++)
	{
        EffectDeleteProxy::SafeDeleteEffect(m_ClawEffects[i]);
	}
#endif
	m_vActorCollideLogicBlocks.clear();
	m_vWheelBlocksPos.clear();

	m_StatisticData = 0;
	m_nDynamicSThrusterCount = 0;
	m_nDynamicThrusterCount = 0;
	m_nDynamicBoatThrusterCount = 0;
	m_nDynamicBoatFloatbucketCount = 0;
	WCoord start = WCoord(0, 0, 0);
	WCoord dim = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);

	if (start.x > start.x + dim.x)
		start.x = start.x + dim.x;

	if (start.y > start.y + dim.y)
		start.y = start.y + dim.y;

	if (start.z > start.z + dim.z)
		start.z = start.z + dim.z;

	dim.x = Rainbow::Abs(dim.x);
	dim.y = Rainbow::Abs(dim.y);
	dim.z = Rainbow::Abs(dim.z);

	WCoord centerPos = WCoord(start.x + MAX_DIM_X / 2, start.y, start.z + MAX_DIM_Z / 2);

	int driveDirection = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.drive_direction;

	std::vector<WCoord>vehicleactioners;
	vehicleactioners.clear();

	//	char path[256];

	//方块场景模型相关
	std::vector<MechaMeshObject *> chassis_pmeshes(m_BlockGroupNum);
	std::vector<MechaMeshObject*> chassis_mask_pmeshes(m_BlockGroupNum);
	std::vector<VehicleWaterMaskData> water_mask_data(m_BlockGroupNum);
	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		chassis_pmeshes[i] = MechaMeshObject::Create(false);
		if (pworld->getScene())
			pworld->getScene()->AddGameObject(chassis_pmeshes[i]->GetGameObject());
		chassis_mask_pmeshes[i] = MechaMeshObject::Create(false);
		//pworld->getScene()->AddGameObject()
	}
	//m_RawMeshesWheel.resize(m_BlockGroupNum);
	//m_BoxMeshesChassis.resize(m_BlockGroupNum);
	//m_BoxMeshesDoor.resize(m_BlockGroupNum);
	m_VehicleCreateMeshes.resize(m_BlockGroupNum);
	m_VehicleCreateParams.resize(m_BlockGroupNum);
	std::vector<Rainbow::Vector3f> chassisMassVecs(m_BlockGroupNum, Rainbow::Vector3f(0, 0, 0));
	m_ChassisMasses.resize(m_BlockGroupNum, 0);
	m_ChassisMassCenters.resize(m_BlockGroupNum, Vector3f(0.0f, 0.0f, 0.0f));
	m_ChassisBlockNums.resize(m_BlockGroupNum, 0);
	std::map<int, MechaMeshObject*> m_EntitiesChassisExMesh;

	jointPos.resize(m_BlockGroupNum);

	m_pMechaSections.resize(m_BlockGroupNum, NULL);
	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		WCoord minPos = start - centerPos;
		WCoord maxPos = WCoord(start.x + dim.x, start.y + dim.x, start.z + dim.x) - centerPos;
		m_pMechaSections[i] = ENG_NEW(MechaSection)(minPos, maxPos);
		m_pMechaSections[i]->setVehicleWorld(m_pVehicleWorld);
		m_pMechaSections[i]->initialize();

		water_mask_data[i].mechaSection = ENG_NEW(MechaSection)(minPos, maxPos);
		water_mask_data[i].mechaSection->initialize();

	}

	m_JointCreateParams.resize(m_BlockGroupNum);
	m_DynamicCreateParams.resize(m_BlockGroupNum);
	m_floatBucketMgr.clear();
	float wheelRadiusMax = 0;

	int suspensionLevel = 0;
	int minArray[3] = { MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1 };
	int maxArray[3] = { 0,0,0 };

	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				WCoord pos(start.x + x, start.y + y, start.z + z);
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (!srcBlock || srcBlock->isEmpty())
					continue;
				if (m_Blocks[x][y][z]->m_iCurLife == 0)
				{
					continue;
				}
				if (srcBlock->getResID() == BLOCK_CLAW)
				{
					m_ClawBlocks.push_back(WCoord(x, y, z));
#ifndef IWORLD_SERVER_BUILD
					m_ClawEffects.push_back(NULL);
#endif
				}

				//如果有船舵方块 该载具可以添加和销毁方块
				if (srcBlock->getResID() == BLOCK_BOAT_RUDDER)
				{
					m_bCanDig = true;
					m_bIsRudder = true;
				}

				BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(srcBlock->getResID());
				if (mtl && mtl->hasActorCollidedWithBlockLogic())
				{
					m_vActorCollideLogicBlocks.push_back(WCoord(x, y, z));
				}
				WCoord blockpos = pos - centerPos;

				// m_GroupId ==  -1 说明没有连通核心
				int  groupid = m_Blocks[x][y][z]->m_GroupIdParent > 0 ? m_Blocks[x][y][z]->m_GroupIdParent : m_Blocks[x][y][z]->m_GroupIdSelf;
				if (groupid >= 0 && groupid < (int)m_pMechaSections.size())
				{
					water_mask_data[groupid].UpdateRange(x, y, z);
					if (m_pMechaSections[groupid]) {
						m_pMechaSections[groupid]->addBlock(blockpos, *srcBlock, 0xff);
						auto iter = m_mJointSideBlocksPos.find(groupid);
						if (iter == m_mJointSideBlocksPos.end())
						{
							for (int i = 0; i < 6; i++)
							{
								m_mJointSideBlocksPos[groupid][i] = pos;
							}
						}
						else
						{
							if (m_mJointSideBlocksPos[groupid][0].x > pos.x)
							{
								m_mJointSideBlocksPos[groupid][0] = pos;
							}
							if (m_mJointSideBlocksPos[groupid][1].x < pos.x)
							{
								m_mJointSideBlocksPos[groupid][1] = pos;
							}
							if (m_mJointSideBlocksPos[groupid][2].z > pos.z)
							{
								m_mJointSideBlocksPos[groupid][2] = pos;
							}
							if (m_mJointSideBlocksPos[groupid][3].z < pos.z)
							{
								m_mJointSideBlocksPos[groupid][3] = pos;
							}
							if (m_mJointSideBlocksPos[groupid][4].y > pos.y)
							{
								m_mJointSideBlocksPos[groupid][4] = pos;
							}
							if (m_mJointSideBlocksPos[groupid][5].y < pos.y)
							{
								m_mJointSideBlocksPos[groupid][5] = pos;
							}
						}
					}
				}
			}
		}
	}

	//find mas point fill boat
	for (size_t idx = 0; idx < m_BlockGroupNum; idx++) 
	{
		fillWaterMaskBlock(water_mask_data[idx], centerPos);
	}

	std::vector<WCoord> meshexs;
	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				WCoord pos(start.x + x, start.y + y, start.z + z);

				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;

				if (!srcBlock || srcBlock->isEmpty())
					continue;
#ifdef _DEBUG
				LOG_INFO("Create vehicle  pos: %d %d %d  blockid: %d groupid: %d", x, y, z, srcBlock->getResID(), m_Blocks[x][y][z]->m_GroupIdSelf);
#endif

				if (x <= minArray[0] || y <= minArray[1] || z <= minArray[2])
				{
					minArray[0] = x <= minArray[0] ? x : minArray[0];
					minArray[1] = y <= minArray[1] ? y : minArray[1];
					minArray[2] = z <= minArray[2] ? z : minArray[2];
				}
				if (x >= maxArray[0] || y >= maxArray[1] || z >= maxArray[2])
				{
					maxArray[0] = x >= maxArray[0] ? x : maxArray[0];
					maxArray[1] = y >= maxArray[1] ? y : maxArray[1];
					maxArray[2] = z >= maxArray[2] ? z : maxArray[2];
				}
				int blockid = srcBlock->getResID();
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
				if (!physicsPartsDef)
					continue;

				//记录载具方块的全部位置
				bool blockExits = false;
				for (int i = 0; i < (int)m_BlockPos.size(); i++)
				{
					if (m_BlockPos[i] == WCoord(x, y, z))
						blockExits = true;
				}
				if (!blockExits)
					m_BlockPos.push_back(WCoord(x, y, z));

				WCoord blockpos = pos - centerPos;

				if (blockid > 0 && blockid != BLOCK_WORKSHOP) //1085, 是否是车间本身方块, 若是需要排除掉)
				{
					//统计埋点用(转轴方块，滑动方块，开关，动作序列器，推进器，发射装置，红外感应方块、感应器、液压臂）
					UpdateStaticDate(blockid);

					//半砖方块处理(两个半砖叠在一起时，生命值、质量翻倍)
					int slabNum = 1;
					SlabMaterial *pmtl = dynamic_cast<SlabMaterial *>(g_BlockMtlMgr.getMaterial(blockid));
					if (pmtl&&srcBlock->getData() >= 2)
					{
						slabNum = 2;
					}

					if (m_Blocks[x][y][z]->m_iCurLife < 0)
					{
						if (physicsPartsDef->Life > 0)
						{
							m_Blocks[x][y][z]->m_iCurLife = (physicsPartsDef->Life*slabNum);
						}
						else
						{
							m_Blocks[x][y][z]->m_iCurLife = 0;
						}
					}

					if (physicsPartsDef->IsCore)
					{
						m_LifeLimit += (physicsPartsDef->Life*slabNum);
						m_LifeCurrent += m_Blocks[x][y][z]->m_iCurLife;
						if (m_Blocks[x][y][z]->m_iCurLife == 0)
						{
							std::stringstream stringstream;
							stringstream << x << "_" << y << "_" << z;
							std::string strPos;
							stringstream >> strPos;
							ContactDesc contactDesc;
							contactDesc.m_BlockPos = WCoord(x, y, z);
							contactDesc.m_BlockID = blockid;
							m_ContactDescs[strPos] = contactDesc;
						}
					}
					else if (physicsPartsDef->IsCoreArmor)
					{
						m_LifeLimit += (physicsPartsDef->Life*slabNum);
						if (m_Blocks[x][y][z]->m_iCurLife)
						{
							m_LifeCurrent += (physicsPartsDef->Life*slabNum);
						}
					}
					if (m_Blocks[x][y][z]->m_iCurLife == 0)
					{
						continue;
					}

					if (physicsPartsDef->UsePower > 0)
						m_PowerUse += physicsPartsDef->UsePower;

					if (physicsPartsDef->AddPower > 0)
						m_PowerAdd += physicsPartsDef->AddPower;

					const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(blockid);
					if (physicsActorDef && physicsActorDef->Mass > 0)
					{
						m_Mass += (physicsActorDef->Mass * slabNum);
					}

					m_BlockNum += 1;
					int blockGroupId = m_Blocks[x][y][z]->m_GroupIdSelf;
					if (blockGroupId < 0 || blockGroupId >= m_BlockGroupNum)
						continue;
					//记录悬挂和groupid的对应信息
					if (blockid == BLOCK_SUSPENSION_JOINT)
						m_Suspensions[blockGroupId] = WCoord(x, y, z);

					//获取配表信息
					PhysicsPartsDef::EffectFunctionsDef* wheelFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* engineFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* seatFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* costFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* thrusterFunc = NULL;
					PhysicsPartsDef::EffectFunctionsDef* overheatFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* SThruster = NULL; //航天推进器
					PhysicsPartsDef::EffectFunctionsDef* boatThrusterFun = NULL;//船只推进器
					PhysicsPartsDef::EffectFunctionsDef* boatFloatbucketFun = NULL;//船只浮桶

					for (int i = 0; i < (int)physicsPartsDef->EffectFunctions.size(); i++)
					{
						PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
						if (functiondef->func_id == 1) //车轮
						{
							wheelFun = functiondef;
						}
						else if (functiondef->func_id == 2) //引擎
						{
							engineFun = functiondef;
						}
						else if (functiondef->func_id == 3) //座位
						{
							if (srcBlock->getResID() == BLOCK_BOAT_RUDDER)
							{
								if (srcBlock->getData() & 8)
								{
									seatFun = functiondef;
								}
							}
							else {
								seatFun = functiondef;
							}
							
						}
						else if (functiondef->func_id == 5)	//消耗
						{
							costFun = functiondef;
						}
						else if (functiondef->func_id == 6)	//过热
						{
							overheatFun = functiondef;
						}
						else if (functiondef->func_id == 9) //推进器
						{
							thrusterFunc = functiondef;
						}
						else if (functiondef->func_id == 11) //航天推进器
						{
							SThruster = functiondef;
						}
						else if (functiondef->func_id == 13) //船只推进器（螺旋推进器）
						{
							if (srcBlock->getData() & 8)
							{
								boatThrusterFun = functiondef;
								m_bHasBoatThrushter = true;
							}
						}
						else if (functiondef->func_id == 14) //船只浮桶
						{
							boatFloatbucketFun = functiondef;
						}
					}

					BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
					if (mtl && (!mtl->hasGeom() || mtl->getGeom() == g_BlockMtlMgr.getGeomTemplate("block")))
					{
						m_sBlockID.insert(blockid);
					}
					if (wheelFun == NULL)
					{
						if (mtl && mtl->hasContainer())
						{
							WorldContainer *container = m_pVehicleWorld->getContainerMgr()->getContainer(pos);
							if (container == NULL)
							{
								container = mtl->createContainer(m_pVehicleWorld, pos);
								if (container)
								{
									if (m_pContainerWorkshop != NULL && blockid != BLOCK_TRANSFERCORE && blockid != BLOCK_TRANSFER && blockid != BLOCK_ACTIONER)
									{
										WCoord shopStartPos = m_pContainerWorkshop->getStartPos();
										WCoord shopCenterPos = m_pContainerWorkshop->getCenterPos(shopStartPos);
										int shopDir = m_pContainerWorkshop->getDirection();
										WCoord localpos = shopCenterPos / BLOCK_SIZE + WCoord(blockpos.x, blockpos.y - MAX_DIM_Y / 2, blockpos.z);

										WorldContainer *srcContainer = dynamic_cast<WorldContainer *>(pworld->getContainerMgr()->getContainer(localpos));
										if (srcContainer)
										{
											flatbuffers::FlatBufferBuilder builder;
											flatbuffers::Offset<FBSave::ChunkContainer> save = srcContainer->save(builder);
											builder.Finish(save);
											const FBSave::ChunkContainer *src = flatbuffers::GetRoot<FBSave::ChunkContainer>(builder.GetBufferPointer());
											if (src)
												container->load(src->container());
											container->m_BlockPos = pos;

											//从胶囊中生成载具，container中保存的物品全部清理掉
											container->clear();

											//书架清理
											WorldBookCabinet* bookConatiner = dynamic_cast<WorldBookCabinet*>(container);
											if (bookConatiner)
											{
												for (int bindex = 0; bindex < bookConatiner->getGridNum(); bindex++)
												{
													BackPackGrid *pback = bookConatiner->index2Grid(bindex + BOOKCABINET_START_INDEX);
													if (pback)
													{
														if (pback->getNum() > 0)
														{
															pback->clear();
															int data = m_pVehicleWorld->getBlockData(bookConatiner->m_BlockPos);
															m_pVehicleWorld->setBlockData(bookConatiner->m_BlockPos, data - 1, 2);
														}
													}
												}
											}

											//编书台清理
											WorldBookEditorTableContainer* bookEditorContainer = dynamic_cast<WorldBookEditorTableContainer*>(container);
											if (bookEditorContainer)
											{
												for (int bindex = 0; bindex < bookEditorContainer->getGridNum(); bindex++)
												{
													BackPackGrid *pback = bookEditorContainer->index2Grid(bindex + EDITBOOK_START_INDEX);
													if (pback)
													{
														if (pback->getNum() > 0)
														{
															pback->clear();
															int data = m_pVehicleWorld->getBlockData(bookEditorContainer->m_BlockPos);
															m_pVehicleWorld->setBlockData(bookEditorContainer->m_BlockPos, data - 1, 2);
														}
													}
												}
											}

											// 非GodMod模式下，柜子的物品直接放入生成的机械的柜子里，不掉落
											if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isGodMode())
											{
												WorldStorageBox* storageBoxContainer = dynamic_cast<WorldStorageBox*>(srcContainer);
												if (storageBoxContainer)
												{
													for (int bindex = 0; bindex < storageBoxContainer->getGridNum(); bindex++)
													{
														BackPackGrid* pback = storageBoxContainer->index2Grid(bindex + STORAGE_START_INDEX);
														if (pback)
														{
															if (pback->getNum() > 0)
															{
																container->addItem_byGrid(pback);
															}
														}
													}

													storageBoxContainer->clear();
												}
											}
										}
									}

									m_pVehicleWorld->getContainerMgr()->spawnContainer(container);

									if (m_pContainerWorkshop != NULL)
									{
										WCoord shopStartPos = m_pContainerWorkshop->getStartPos();
										WCoord shopCenterPos = m_pContainerWorkshop->getCenterPos(shopStartPos);
										int shopDir = m_pContainerWorkshop->getDirection();
										WCoord localpos = shopCenterPos / BLOCK_SIZE + WCoord(blockpos.x, blockpos.y - MAX_DIM_Y / 2, blockpos.z);
										ContainerMecha *mechacontainer = dynamic_cast<ContainerMecha *>(pworld->getContainerMgr()->getContainer(localpos));
										VehicleContainerMecha* vehicleMechaContainer = dynamic_cast<VehicleContainerMecha *>(m_pVehicleWorld->getContainerMgr()->getContainer(pos));

										if (mechacontainer && vehicleMechaContainer)
										{
											if (BLOCK_SUSPENSION_JOINT == blockid)
											{
												m_Blocks[x][y][z]->m_iSuspensionLevel = mechacontainer->getSuspensionLevel();
												vehicleMechaContainer->setSuspensionLevel(mechacontainer->getSuspensionLevel());
											}
											else if (BLOCK_JOINT_T_REVOLUTE == blockid)
											{
												vehicleMechaContainer->setAxisType(mechacontainer->getAxisType());
												vehicleMechaContainer->setLoopType(mechacontainer->getLoopType());
												/*vehicleMechaContainer->setAngle(mechacontainer->getAngle());
												vehicleMechaContainer->setCurAngle(mechacontainer->getCurAngle());
												vehicleMechaContainer->SetActionMode(mechacontainer->GetActionMode());*/
											}
											else if (BLOCK_FUEL_TANK == blockid)
											{
												m_Blocks[x][y][z]->m_iCurFuel = mechacontainer->getFuel();
												vehicleMechaContainer->setFuel(mechacontainer->getFuel());
											}
											else if (BLOCK_VEHICLEENGINE == blockid)
											{
												m_Blocks[x][y][z]->m_iCurFuel = mechacontainer->getFuel();
												vehicleMechaContainer->setFuel(mechacontainer->getFuel());
											}
										}
									}

									if (m_pContainerWorkshop && blockid == BLOCK_TRANSFERCORE)
									{
										//mtl->onBlockPlacedBy(m_pVehicleWorld, WCoord(x, y, z), NULL);
										mtl->DoOnBlockPlacedBy(m_pVehicleWorld, WCoord(x, y, z), NULL, 0, Rainbow::Vector3f::zero, false, 0);
										/*mtl->Event().Emit("OnBlockPlaceBy", SandboxContext()
											.SetData_Usertype<World>("world", m_pVehicleWorld)
											.SetData_UserObject<WCoord>("pos", WCoord(x, y, z))
											.SetData_Usertype<ClientPlayer>("player", nullptr)
										);*/
										mtl->DoOnBlockAdded(m_pVehicleWorld, WCoord(x, y, z));
										WorksShopBlockPos workpos;
										workpos.vehicle_pos = WCoord(x, y, z);
										workpos.world_pos = pos;
										m_vTransferPos.push_back(workpos);
									}
								}
							}
							else
							{
								container->leaveWorld();
								container->enterWorld(m_pVehicleWorld);
							}
						}



						//座位
						if (seatFun)
						{
							//驾驶座
							if (seatFun->func.seatfun.IsControl == 1)
							{
// 								LOG_INFO("seat control: %d %d %d", pos.x, pos.y, pos.z);
								m_SeatDir = srcBlock->getData() % 4;
								m_SeatRelativePos = WCoord(x, y, z);
								if (srcBlock->getResID() == BLOCK_BOAT_RUDDER)
								{
									WCoord offset(0, 0, 0);
									if (m_SeatDir == DIR_POS_Z)
									{
										offset.z = -1;
									}
									else if (m_SeatDir == DIR_NEG_Z)
									{
										offset.z = 1;
									}
									else if (m_SeatDir == DIR_POS_X)
									{
										offset.x = -1;
									}else if (m_SeatDir == DIR_NEG_X)
									{
										offset.x = 1;
									}
									m_SeatPos = (blockpos + offset) * BLOCK_SIZE;
								}
								else {
									m_SeatPos = blockpos * BLOCK_SIZE;
								}
								
							}
							//乘客座
							else if (seatFun->func.seatfun.IsControl == 0)
							{
								// 								LOG_INFO("seat follow: %d %d %d", pos.x, pos.y, pos.z);
								WCoord playerpos = blockpos * BLOCK_SIZE /*+ WCoord(0, 60, 0)seatFun->func.seatfun.AddHeight*/;
								m_OtherSeatPos.push_back(playerpos);
								m_OtherSeatDir.push_back(srcBlock->getData() % 4);
								if (!m_isCreateFromLoad && !m_isReCreate || (m_OtherRiddens.size() < m_OtherSeatPos.size()))
									m_OtherRiddens.push_back(0);
							}
						}

						//引擎
						if (engineFun)
						{
							m_EngineNum++;
							PhysicsPartsDef::EffectFunctionsDef* engineFunc = NULL;
							for (int i = 0; i < (int)physicsPartsDef->EffectFunctions.size(); i++)
							{
								PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
								if (functiondef->func_id == 2)
								{
									engineFunc = functiondef;
								}
							}
							bool engineExists = false;
							std::vector<EnginePart>::iterator it = m_Engines.begin();
							for (; it != m_Engines.end(); it++)
							{
								if (blockid == it->blockid) {
									// 									EnginePart enginePart = *it;
									// 									enginePart.num += 1;
									it->num += 1;
									it->m_mPosGroup.insert(make_pair(Rainbow::Vector3f((float)x, (float)y, (float)z), blockGroupId));
									engineExists = true;
									break;
								}
							}
							if (!engineExists)
								m_Engines.push_back(EnginePart(WCoord(x, y, z), engineFunc, m_Blocks[x][y][z]->m_GroupIdSelf, blockid, physicsPartsDef->CostValue, physicsPartsDef->CostInterval));

							// GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.max_speed;
						}
						//消耗
						if (costFun)
						{
							if (m_Blocks[x][y][z]->m_iCurFuel == -1) m_Blocks[x][y][z]->m_iCurFuel = 0;

							m_CostParts.push_back(ENG_NEW(DynamicCost)(this, WCoord(x, y, z), costFun, (float)m_Blocks[x][y][z]->m_iCurFuel));
						}
						//过热
						if (overheatFun)
						{
							if (m_Blocks[x][y][z]->m_iCurHeat <= 0) m_Blocks[x][y][z]->m_iCurHeat = 0;

							if (IsThrusterBlockID(blockid))
								m_Overheats.push_back(ENG_NEW(DynamicOverheat)(this, WCoord(x, y, z), overheatFun, m_Blocks[x][y][z]->m_iCurFuel));
						}
/*
#ifndef IWORLD_SERVER_BUILD
						//WCoord meshPos(blockpos.x, blockpos.y - m_nOffsetYWorkshopAndWorld, blockpos.z);
						if (havemesh)
						{
							//mtl->createBlockMesh(m_pMechaSections[blockGroupId], blockpos, chassis_pmeshes[blockGroupId]);
							if (blockid != BLOCK_SUSPENSION_JOINT && blockid != BLOCK_JOINT_ARM_PRISMATIC)
							{
								mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[blockGroupId]->GetSectionMesh());
							}
							else
							{
								m_EntitiesChassisExMesh[(x << 10) + (y << 5) + z] = MechaMeshObject::Create(false);
								mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), blockpos, m_EntitiesChassisExMesh[(x << 10) + (y << 5) + z]->GetSectionMesh());
							}
						}
#endif
*/
						m_ChassisBlockNums[blockGroupId]++;
						if (IsDynamicBlockID(blockid))
						{
							if (blockid == BLOCK_WING)
							{
								MINIW::WingParam param;
								param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
								param.mBlockDir = srcBlock->getData() & 7;
								param.mGroupId = blockGroupId;
								m_DynamicCreateParams[blockGroupId].mWingParams.push_back(param);
							}
							else if (blockid == BLOCK_EMPENNAGE)
							{
								MINIW::EmpParam param;
								param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
								param.mBlockDir = srcBlock->getData() & 15;
								param.mGroupId = blockGroupId;
								m_DynamicCreateParams[blockGroupId].mEmpParams.push_back(param);
							}
							else if (blockid == BLOCK_STHRUSTER)
							{
								MINIW::SThrusterParam param;
								param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
								param.mBlockDir = srcBlock->getData() & 7;
								param.mGroupId = blockGroupId;
								if (SThruster)
								{
									param.m_fPowerMax = SThruster->func.sthrusterfun.PowerMax;
									param.m_fPowerChangeVal = SThruster->func.sthrusterfun.PowerChangeValue;
									param.m_nPowerChangeInterval = SThruster->func.sthrusterfun.PowerChangeInterval;
									param.m_nCostInterval = SThruster->func.sthrusterfun.CostInterval;
									param.m_vTopLimitList[0] = SThruster->func.sthrusterfun.Level1TopLimit;
									param.m_vTopLimitList[1] = SThruster->func.sthrusterfun.Level2TopLimit;
									param.m_vTopLimitList[2] = SThruster->func.sthrusterfun.Level3TopLimit;
									param.m_vCostValList[0] = SThruster->func.sthrusterfun.Level1CostValue;
									param.m_vCostValList[1] = SThruster->func.sthrusterfun.Level2CostValue;
									param.m_vCostValList[2] = SThruster->func.sthrusterfun.Level3CostValue;
									param.m_fThrusterForce2 = SThruster->func.sthrusterfun.ThrusterForce2;
									param.m_fThrusterForce1 = SThruster->func.sthrusterfun.ThrusterForce1;
								}
								m_DynamicCreateParams[blockGroupId].mSThrusterParams.push_back(param);
								m_nDynamicSThrusterCount++;
							}
							else if (blockid == BLOCK_THRUSTER)
							{
								MINIW::ThrusterParam param;
								param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
								param.mBlockDir = srcBlock->getData() & 7;
								param.mGroupId = blockGroupId;
								if (thrusterFunc)
								{
									param.m_fThrusterForce = (float)thrusterFunc->func.propellerfun.ThrusterForce;
									param.m_fRigidForce = (float)thrusterFunc->func.propellerfun.RigidForce;
									param.m_nThrusterType = thrusterFunc->func.propellerfun.ThrusterType;
								}
								m_DynamicCreateParams[blockGroupId].mThrusterParams.push_back(param);
								m_nDynamicThrusterCount++;
							}
							else if (blockid == BLOCK_BOAT_FLOATBUCKET)
							{
								//浮桶
								MINIW::FloatbucketParam param;
								param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
								param.mBlockDir = srcBlock->getData() & 7;
								param.mGroupId = blockGroupId;
								if (boatFloatbucketFun)
								{
									param.m_fFloatForce = boatFloatbucketFun->func.boatBucketfun.floatForce;
								}
								++m_nDynamicBoatFloatbucketCount;
								m_DynamicCreateParams[blockGroupId].mFloatbucketParams.push_back(param);
								//记录浮桶位置
								m_floatBucketMgr.addInfo(pos);
							}
							else if (blockid == BLOCK_BOAT_THRUSTER)
							{
								if (srcBlock->getData() & 8)
								{
									//船只推进器
									MINIW::BoatThrusterParam param;
									param.mBlockPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
									param.mBlockDir = srcBlock->getData() & 7;
									param.mGroupId = blockGroupId;
									if (boatThrusterFun)
									{
										param.m_fThrusterForce = boatThrusterFun->func.boatPropellerfun.Thruster;
										param.m_fUpAngle = boatThrusterFun->func.boatPropellerfun.maxUpAngle;
										param.m_fDownAngle = boatThrusterFun->func.boatPropellerfun.maxDownAngle;
									}
									++m_nDynamicBoatThrusterCount;
									m_DynamicCreateParams[blockGroupId].mBoatThrusterParams.push_back(param);
									m_hasSteeringSwitch = true;
								}	
							}

						}
#if 1
						//关节方块
						if (IsJointBlockID(blockid))
						{
							int dir = srcBlock->getData() & 7;
							WCoord localpos = BlockCenterCoord(pos) - BlockCenterCoord(centerPos);
							MINIW::JointCreateParam jointParam;

							if (blockid == BLOCK_REVOLUTE_JOINT)
							{
								jointParam.mType = REVOLUTE;
							}
							else if (blockid == BLOCK_PRISMATIC_JOINT)
							{
								jointParam.mType = PRISMATIC;
							}
							else if (BLOCK_JOINT_ARM_PRISMATIC == blockid)
							{
								jointParam.mType = ARM_PRISMATIC;
							}
							else if (BLOCK_JOINT_T_REVOLUTE == blockid)
							{
								jointParam.mType = T_REVOLUTE;
							}
							else if (BLOCK_JOINT_SPHERICAL == blockid)
							{
								jointParam.mType = SPHERICAL;
							}
							else if (blockid == BLOCK_SUSPENSION_JOINT)
							{
								jointParam.mType = SUSPENSION;
							}
							else if (blockid == BLOCK_ROPE_HEAD) //绞绳头部
							{
								jointParam.mType = ROPE;
							}
							else if (blockid == BLOCK_ROPE)  //绞绳节点
							{
								jointParam.mType = ROPENODE;
							}
							else if (blockid == BLOCK_ROPE_TAIL) //绞绳尾部
							{
								jointParam.mType = ROPETAIL;
							}

							jointParam.mPos0 = Rainbow::Vector3f((float)localpos.x, (float)localpos.y, (float)localpos.z);

							if (blockid == BLOCK_REVOLUTE_JOINT || blockid == BLOCK_SUSPENSION_JOINT)
							{
								if (dir == DIR_NEG_X)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisZ(), kHalfPI) * jointParam.mQuat0;
									//jointParam.mQuat0.rotate(jointParam.mQuat0.getAxisY(), reverseangle);
									jointParam.mPos0.y += BLOCK_SIZE / 2;
									jointParam.mPosBase.y = BLOCK_SIZE / 2;
									//jointParam.mPos1.y -= BLOCK_SIZE/2;
								}
								else if (dir == DIR_POS_X)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisZ(), -kHalfPI) * jointParam.mQuat0;
									//jointParam.mQuat0.rotate(jointParam.mQuat0.getAxisY(), reverseangle);
									jointParam.mPos0.y += BLOCK_SIZE / 2;
									jointParam.mPosBase.y = BLOCK_SIZE / 2;
								}
								else if (dir == DIR_NEG_Z)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisX(), -kHalfPI) * jointParam.mQuat0;
									//jointParam.mQuat0.rotate(jointParam.mQuat0.getAxisY(), reverseangle);
									jointParam.mPos0.y += BLOCK_SIZE / 2;
									jointParam.mPosBase.y = BLOCK_SIZE / 2;
								}
								else if (dir == DIR_POS_Z)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisX(), kHalfPI) * jointParam.mQuat0;
									//jointParam.mQuat0.rotate(jointParam.mQuat0.getAxisY(), reverseangle);
									jointParam.mPos0.y += BLOCK_SIZE / 2;
									jointParam.mPosBase.y = BLOCK_SIZE / 2;
								}
								else if (dir == DIR_NEG_Y)
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisZ(), kOnePI) * jointParam.mQuat0;
							}
							else if (blockid == BLOCK_PRISMATIC_JOINT)
							{
								WCoord slideFromPos(0, 0, 0);
								WCoord slideToPos(0, 0, 0);
								int mindist = MAX_INT;
								Block *slideBlock = NULL;
								WCoord slidePos;
								bool slideReverse = false;
								if ((dir == DIR_NEG_X && x > 0) || (dir == DIR_POS_X && x < dim.x))
								{
									if (dir == DIR_NEG_X && x > 0)
										slidePos = WCoord(x - 1, y, z);
									else if (dir == DIR_POS_X && x < dim.x)
										slidePos = WCoord(x + 1, y, z);
									slideBlock = &m_Blocks[slidePos.x][slidePos.y][slidePos.z]->m_Block;
									if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_START)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisZ(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisY(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										slideReverse = true;
									}
									else if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_END)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisZ(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisY(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										slideReverse = false;
									}
								}

								else if ((dir == DIR_NEG_Z && z > 0) || (dir == DIR_POS_Z && z < dim.z))
								{
									if (dir == DIR_NEG_Z && z > 0)
										slidePos = WCoord(x, y, z - 1);
									else if (dir == DIR_POS_Z && z < dim.z)
										slidePos = WCoord(x, y, z + 1);
									slideBlock = &m_Blocks[slidePos.x][slidePos.y][slidePos.z]->m_Block;
									if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_START)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisX(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisY(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										slideReverse = true;
									}
									else if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_END)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisX(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisY(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										slideReverse = false;
									}
								}

								else if ((dir == DIR_NEG_Y && y > 0) || (dir == DIR_POS_Y && y < dim.y))
								{
									if (dir == DIR_NEG_Y && y > 0)
										slidePos = WCoord(x, y - 1, z);
									else if (dir == DIR_POS_Y && y < dim.y)
										slidePos = WCoord(x, y + 1, z);
									slideBlock = &m_Blocks[slidePos.x][slidePos.y][slidePos.z]->m_Block;
									if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_START)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisX(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisZ(BLOCK_PRISMATIC_JOINT_END, slideFromPos, dim, mindist, slideToPos);
										slideReverse = true;
									}
									else if (slideBlock->getResID() == BLOCK_PRISMATIC_JOINT_END)
									{
										slideFromPos = slidePos;
										findSlideToPosAxisX(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										findSlideToPosAxisZ(BLOCK_PRISMATIC_JOINT_START, slideFromPos, dim, mindist, slideToPos);
										slideReverse = false;
									}
								}

								if (mindist < MAX_INT)
								{
									WCoord slideDist;
									if (slideReverse)
										slideDist = slideFromPos - slideToPos;
									else
										slideDist = slideToPos - slideFromPos;
									jointParam.mPos1 += (slideDist*BLOCK_SIZE / 2).toVector3();
									jointParam.mSlideLen = (int)slideDist.length()*BLOCK_SIZE / 2;
									if (slideDist.x < 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_POS_X;
										else
											jointParam.mSlideAxis = AXIS_NEG_X;
									}
									else if (slideDist.x > 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_NEG_X;
										else
											jointParam.mSlideAxis = AXIS_POS_X;
									}
									else if (slideDist.y < 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_POS_Y;
										else
											jointParam.mSlideAxis = AXIS_NEG_Y;
									}
									else if (slideDist.y > 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_NEG_Y;
										else
											jointParam.mSlideAxis = AXIS_POS_Y;
									}
									else if (slideDist.z < 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_POS_Z;
										else
											jointParam.mSlideAxis = AXIS_NEG_Z;
									}
									else if (slideDist.z > 0)
									{
										if (slideReverse)
											jointParam.mSlideAxis = AXIS_NEG_Z;
										else
											jointParam.mSlideAxis = AXIS_POS_Z;
									}
									jointParam.mSlideReverse = slideReverse;
								}
							}
							else if (BLOCK_JOINT_ARM_PRISMATIC == blockid)
							{
								jointParam.mSlideLen = 0;
								if (m_pVehicleWorld)
								{
									ContainerArmPrismatic *mechacontainer = dynamic_cast<ContainerArmPrismatic *>(m_pVehicleWorld->getContainerMgr()->getContainer(pos));
									if (mechacontainer)
									{
										jointParam.mSlideLen = mechacontainer->getExtendSize();
									}
								}

								WCoord dirPos(0, 0, 0);
								if (dir == DIR_NEG_X && x >= 0)
								{
									jointParam.mSlideAxis = AXIS_NEG_X;
									dirPos.x = -1;
								}
								else if (dir == DIR_POS_X && x < dim.x)
								{
									jointParam.mSlideAxis = AXIS_POS_X;
									dirPos.x = 1;
								}
								else if (dir == DIR_NEG_Y && y >= 0)
								{
									jointParam.mSlideAxis = AXIS_NEG_Y;
									dirPos.y = -1;
								}
								else if (dir == DIR_POS_Y && y < dim.y)
								{
									jointParam.mSlideAxis = AXIS_POS_Y;
									dirPos.y = 1;
								}
								else if (dir == DIR_NEG_Z && z >= 0)
								{
									jointParam.mSlideAxis = AXIS_NEG_Z;
									dirPos.z = -1;
								}
								else if (dir == DIR_POS_Z && z < dim.z)
								{
									jointParam.mSlideAxis = AXIS_POS_Z;
									dirPos.z = 1;
								}
								jointParam.mPos1 += (dirPos*BLOCK_SIZE / 2).toVector3();
							}
							else if (BLOCK_JOINT_T_REVOLUTE == blockid)
							{
								jointParam.mPos0.y += BLOCK_SIZE / 2;

								if (dir == DIR_NEG_X)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), kOnePI) * jointParam.mQuat0;
								}
								else if (dir == DIR_POS_X)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), 0) * jointParam.mQuat0;
								}
								else if (dir == DIR_NEG_Z)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), kHalfPI) * jointParam.mQuat0;
								}
								else if (dir == DIR_POS_Z)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), -kHalfPI) * jointParam.mQuat0;
								}
								else if (dir == DIR_NEG_Y)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), kHalfPI) * jointParam.mQuat0;
								}
								else if (dir == DIR_POS_Y)
								{
									jointParam.mQuat0 = AxisAngleToQuaternionf(jointParam.mQuat0.GetAxisY(), -kHalfPI) * jointParam.mQuat0;
								}
							}
							else if (BLOCK_JOINT_SPHERICAL == blockid || BLOCK_ROPE_HEAD == blockid || BLOCK_ROPE_TAIL == blockid || BLOCK_ROPE == blockid)
							{
								jointParam.mPos0.y += BLOCK_SIZE / 2;
								jointParam.mPosBase.y = BLOCK_SIZE / 2;
							}

							jointParam.mQuat1 = jointParam.mQuat0;

							if (m_pContainerWorkshop)
							{
								WCoord shopStartPos = m_pContainerWorkshop->getStartPos();
								WCoord shopCenterPos = m_pContainerWorkshop->getCenterPos(shopStartPos);
								int shopDir = m_pContainerWorkshop->getDirection();
								WCoord worldpos = shopCenterPos / BLOCK_SIZE + WCoord(blockpos.x, blockpos.y - MAX_DIM_Y / 2, blockpos.z);
								ContainerMecha *mechacontainer = dynamic_cast<ContainerMecha *>(pworld->getContainerMgr()->getContainer(worldpos));
								if (mechacontainer != NULL)
								{
									if (blockid == BLOCK_REVOLUTE_JOINT)
									{
										int reverseangle = 0;
										if (pworld->isBlockIndirectlyGettingPowered(worldpos))
											reverseangle = mechacontainer->getAngle();

										jointParam.mQuat1 = AxisAngleToQuaternionf(jointParam.mQuat1.GetAxisY(), (float)reverseangle * kDeg2Rad) * jointParam.mQuat1;
									}
									else if (blockid == BLOCK_SUSPENSION_JOINT)
									{
										jointParam.mSuspensionLevel = mechacontainer->getSuspensionLevel();
									}
								}
							}
							jointParam.mSuspensionLevel = m_Blocks[x][y][z]->m_iSuspensionLevel;
							jointParam.mContainerPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
							jointParam.mDir = dir;
							jointParam.mInvInertiaScale = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.inv_inertia_scale;
							jointParam.mInvMassScale = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.inv_mass_scale;
							jointParam.x = x;
							jointParam.y = y;
							jointParam.z = z;
							if (m_pContainerWorkshop)
							{
								std::map<WCoord, JointInfo>& jointInfoMap = m_pContainerWorkshop->getJointInfoMap();
								if (jointInfoMap.find(pos) != jointInfoMap.end())
								{
									jointParam.mGroupId0 = jointInfoMap[pos].m_BlockGroupId0;
									jointParam.mGroupId1 = jointInfoMap[pos].m_BlockGroupId1;
									if (jointParam.mGroupId0 == m_Blocks[x][y][z]->m_GroupIdSelf || jointParam.mType == SUSPENSION) //悬挂比较特殊，划归到子关节部分
										m_Blocks[x][y][z]->m_GroupIdChild = jointParam.mGroupId1 + 1;
								}
								//m_JointCreateParams.push_back(jointParam);
								m_JointCreateParams[jointParam.mGroupId1] = jointParam;
							}
							else if (m_Blocks[x][y][z]->m_GroupIdChild > 0)
							{
								//jointParam.mGroupId0 = m_Blocks[x][y][z]->m_GroupId;
								//悬挂划归到子关节部分
								if (jointParam.mType != SUSPENSION)
									jointParam.mGroupId0 = m_Blocks[x][y][z]->m_GroupIdSelf;
								else
									jointParam.mGroupId0 = m_Blocks[x][y][z]->m_GroupIdParent;

								jointParam.mGroupId1 = m_Blocks[x][y][z]->m_GroupIdChild - 1;
								if (m_Blocks[x][y][z]->m_GroupIdChild <= (int)m_JointCreateParams.size())
									m_JointCreateParams[m_Blocks[x][y][z]->m_GroupIdChild - 1] = jointParam;
							}
							else if (m_GroupJointFromServer.find((unsigned int)(x << 16) | (z << 8) | y) != m_GroupJointFromServer.end())
							{
								jointParam.mGroupId0 = m_Blocks[x][y][z]->m_GroupIdSelf;
								jointParam.mGroupId1 = m_GroupJointFromServer[(unsigned int)(x << 16) | (z << 8) | y];
								if (jointParam.mGroupId1 < (int)m_JointCreateParams.size())
									m_JointCreateParams[jointParam.mGroupId1] = jointParam;
								m_Blocks[x][y][z]->m_GroupIdChild = jointParam.mGroupId1 + 1;
							}
							if (blockid == BLOCK_JOINT_SPHERICAL || blockid == BLOCK_JOINT_T_REVOLUTE)//需要渲染两个地方
							{
#ifndef IWORLD_SERVER_BUILD
								if (havemesh)
									meshexs.push_back(WCoord(x, y, z));
#endif
							}
						}

#endif

#ifndef IWORLD_SERVER_BUILD
						//WCoord meshPos(blockpos.x, blockpos.y - m_nOffsetYWorkshopAndWorld, blockpos.z);
						if (havemesh)
						{
							//mtl->EXEC_USEMODULE(CreateBlockMesh,m_pMechaSections[blockGroupId], blockpos, chassis_pmeshes[blockGroupId]);
							if (blockid != BLOCK_SUSPENSION_JOINT && blockid != BLOCK_JOINT_ARM_PRISMATIC)
							{
								//Block* upBlock = m_pMechaSections[blockGroupId]->getNeighborBlock(blockpos, DirectionType::DIR_POS_Y);
								mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[blockGroupId]->GetSectionMesh());
							}
							else
							{
								m_EntitiesChassisExMesh[(x << 10) + (y << 5) + z] = MechaMeshObject::Create(false);
								mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), blockpos, m_EntitiesChassisExMesh[(x << 10) + (y << 5) + z]->GetSectionMesh());
							}
						}
#endif
						if (blockid == BLOCK_ACTIONER)
							vehicleactioners.push_back(WCoord(x, y, z));

						if (physicsActorDef && physicsActorDef->Mass > 0)
						{
							m_ChassisMasses[blockGroupId] += (physicsActorDef->Mass*slabNum);
							WCoord localpos = BlockCenterCoord(pos) - BlockCenterCoord(centerPos);
							chassisMassVecs[blockGroupId] += Rainbow::Vector3f(
								(float)(localpos.x*physicsActorDef->Mass*slabNum),
								(float)(localpos.y*physicsActorDef->Mass*slabNum),
								(float)(localpos.z*physicsActorDef->Mass*slabNum));
						}


					}
					else
					{
						m_vWheelBlocksPos.push_back(WCoord(x, y, z));
						//mcsect.addBlock(blockpos, *srcBlock, 0xff);
						MechaMeshObject *wheel_pmesh = MechaMeshObject::Create(false);
						mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), blockpos, wheel_pmesh->GetSectionMesh());
						m_WheelBlockNum++;

						int dir = srcBlock->getData() % 4;
						float angle = 0.0f;
						float radian = 0.0f;

						// 对应的为 正向设置和反向设置值
						float angles[2][4] = {
							{ 0.0f, 90.0f, 180.0f, 270.0f },
							{ 180.0f,270.0f, 0.0f, 90.0f }
						};
						int angle_index = 0;
						/*
						小地图正北朝向：
						工作台朝向 m_WorkshopDirection == DIR_NEG_Z 其余情况朝向对应换算

						车轮的轴方向 为dir对应的方向 滚动的方向与dir互相垂直
						工作台区域对应的左向和前向 为车轮生成正方向
						*/
						if (DIR_POS_Z == m_WorkshopDirection)
						{
							if (DIR_NEG_Z == dir || DIR_POS_Z == dir)
								angle_index = 1;
							else if (DIR_NEG_X == dir || DIR_POS_X == dir)
								angle_index = 2;
						}
						else if (DIR_NEG_Z == m_WorkshopDirection)
						{
							if (DIR_NEG_Z == dir || DIR_POS_Z == dir)
								angle_index = 3;
							else if (DIR_NEG_X == dir || DIR_POS_X == dir)
								angle_index = 0;
						}
						else if (DIR_NEG_X == m_WorkshopDirection)
						{
							if (DIR_NEG_X == dir || DIR_POS_X == dir)
								angle_index = 0;
							else if (DIR_NEG_Z == dir || DIR_POS_Z == dir)
								angle_index = 1;
						}
						else if (DIR_POS_X == m_WorkshopDirection)
						{
							if (DIR_NEG_X == dir || DIR_POS_X == dir)
								angle_index = 2;
							else if (DIR_NEG_Z == dir || DIR_POS_Z == dir)
								angle_index = 3;
						}
						// radian值用对应的angle值换算即可:3.1415926对应180.0f
						if (driveDirection == 0)
							angle = angles[0][angle_index];
						else
							angle = angles[1][angle_index];
						radian = angle / 90.0f * kHalfPI;


						WCoord localpos = BlockCenterCoord(pos) - BlockCenterCoord(centerPos);
#ifdef _DEBUG
						LOG_INFO("wheel localpos:%d %d %d  pos: %d %d %d  centerpos: %d %d %d", localpos.x, localpos.y, localpos.z,
							BlockCenterCoord(pos).x, BlockCenterCoord(pos).y, BlockCenterCoord(pos).z,
							BlockCenterCoord(centerPos).x, BlockCenterCoord(centerPos).y, BlockCenterCoord(centerPos).z);
#endif
						Rainbow::RAWMesh rawMesh;
						createWheelMesh(wheel_pmesh, rawMesh, localpos, angle, WCoord(x, y, z), blockGroupId);

						float wheelWidth = 0.0f;
						float wheelRadius = 0.0f;
						computeWheelWidthAndRadius(rawMesh, dir, wheelWidth, wheelRadius);

						wheelRadiusMax = Max(wheelRadiusMax, wheelRadius);

						Rainbow::WheelParam wheelParam;
						wheelParam.mWidth = wheelWidth;
						wheelParam.mRadius = wheelRadius;

						if (wheelFun->func.wheelfun.DriveSwitch == 1)
						{
							wheelParam.mDriveEnable = 1;
							wheelParam.mMaxHandBrakeTorque = (float)wheelFun->func.wheelfun.BrakeTorque;
							wheelParam.mType = Rainbow::ETireType::TIRE_TYPE_WETS;
						}
						else
						{
							wheelParam.mDriveEnable = 0;
							wheelParam.mMaxHandBrakeTorque = (float)wheelFun->func.wheelfun.BrakeTorque;
							wheelParam.mType = Rainbow::ETireType::TIRE_TYPE_WETS;
						}

						if (wheelFun->func.wheelfun.SteeringSwitch == 1)
						{
							wheelParam.mMaxSteer = wheelFun->func.wheelfun.SteeringRange * PxPiPerAngle;
							m_hasSteeringSwitch = true;
						}
						else
						{
							wheelParam.mMaxSteer = 0.0f;
						}

						wheelParam.mDefaultSteer = radian;
						wheelParam.mMOI = wheelFun->func.wheelfun.MOI;
						wheelParam.mDampingRate = wheelFun->func.wheelfun.DampingRate;
						if (physicsActorDef && physicsActorDef->Mass > 0)
							wheelParam.mMass = physicsActorDef->Mass * slabNum * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.mass_scale;
						m_VehicleCreateParams[blockGroupId].mWheelParams.push_back(wheelParam);

						Rainbow::SuspensionParam suspensionParam;
						suspensionParam.mMaxCompression = wheelFun->func.wheelfun.MaxCompression;
						suspensionParam.mMaxDroop = wheelFun->func.wheelfun.MaxDroop;
						suspensionParam.mSpringStrength = wheelFun->func.wheelfun.SpringStrength;
						suspensionParam.mSpringDamperRate = wheelFun->func.wheelfun.SpringDamperRate;
						m_VehicleCreateParams[blockGroupId].mSuspensionParams.push_back(suspensionParam);
					}

				}
				else
				{
					m_Blocks[x][y][z]->m_iCurLife = 0;
				}
			}
		}
	}

	m_WaterMaskBound.resize(0);

	//create mask mesh
	for (size_t idx = 0; idx < m_BlockGroupNum; idx++) 
	{
		if (water_mask_data[idx].fillCount <= 0) 
		{
			Rainbow::BoxBound boxBound = Rainbow::BoxBound(Vector3f::zero, Vector3f::one);
			m_WaterMaskBound.push_back(boxBound);
			continue;
		}

		Vector3f minPos = Vector3f(dim.x * BLOCK_SIZE, dim.y * BLOCK_SIZE, dim.z * BLOCK_SIZE);
		Vector3f maxPos = Vector3f(-minPos.x, -minPos.y, -minPos.z);
		for (int y = 0; y <= dim.y; y++)
		{
			for (int z = 0; z <= dim.z; z++)
			{
				for (int x = 0; x <= dim.x; x++)
				{
					WCoord pos(start.x + x, start.y + y, start.z + z);
					WCoord blockpos = pos - centerPos;
					VehicleWaterMaskData& maskData = water_mask_data[idx];


					Block block = maskData.mechaSection->getBlock(blockpos.x, blockpos.y, blockpos.z);
					if (block.isEmpty()) continue;
					int blockid = block.getResID();
					BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
					mtl->createBlockMesh(maskData.mechaSection->GetBuildSectionMeshData(), blockpos, chassis_mask_pmeshes[idx]->GetSectionMesh());
					Vector3f localPos = (blockpos.toVector3()) * (float)BLOCK_SIZE;

					if (localPos.x < minPos.x) minPos.x = localPos.x;
					if (localPos.x > maxPos.x) maxPos.x = localPos.x;

					if (localPos.y < minPos.y) minPos.y = localPos.y;
					if (localPos.y > maxPos.y) maxPos.y = localPos.y;

					if (localPos.z < minPos.z) minPos.z = localPos.z;
					if (localPos.z > maxPos.z) maxPos.z = localPos.z;
				}
			}
		}
		//fill edge
		maxPos += Vector3f(BLOCK_SIZE / 2, BLOCK_SIZE, BLOCK_SIZE / 2);
		minPos += Vector3f(-BLOCK_SIZE / 2, 0, -BLOCK_SIZE / 2);
		Rainbow::BoxBound boxBound = Rainbow::BoxBound(minPos, maxPos);
		m_WaterMaskBound.push_back(boxBound);
	}


	//destroy mask data
	for (size_t idx = 0; idx < m_BlockGroupNum; idx++) 
	{
		ENG_DELETE(water_mask_data[idx].mechaSection);
	}


#ifndef IWORLD_SERVER_BUILD
	for (int i = 0; i<(int)meshexs.size(); i++)
	{
		int x = meshexs[i].x;
		int y = meshexs[i].y;
		int z = meshexs[i].z;
		WCoord blockpos = meshexs[i] - centerPos;
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(m_Blocks[x][y][z]->m_Block.getResID());
		if (mtl)
		{
			if (m_Blocks[x][y][z]->m_GroupIdChild > 0 && m_Blocks[x][y][z]->m_GroupIdChild <= m_BlockGroupNum && m_ChassisBlockNums[m_Blocks[x][y][z]->m_GroupIdChild - 1]>0)
				mtl->createBlockMeshEx(m_pMechaSections[m_Blocks[x][y][z]->m_GroupIdSelf]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[m_Blocks[x][y][z]->m_GroupIdChild - 1]->GetSectionMesh());
			else
				mtl->createBlockMeshEx(m_pMechaSections[m_Blocks[x][y][z]->m_GroupIdSelf]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[m_Blocks[x][y][z]->m_GroupIdSelf]->GetSectionMesh());
		}
	}
#endif

	vector<WorldContainer *> tmpList;
	VehicleContainerMgr* pVehicleContainerMgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
	if (pVehicleContainerMgr)
	{
		pVehicleContainerMgr->getContainersVec(tmpList);
	}
	for (int i = 0; i < (int)tmpList.size(); i++)
	{
		RadioUnitContainer * t = dynamic_cast<RadioUnitContainer*>(tmpList[i]);
		if (t)
		{
			for (int j = 0; j < (int)tmpList.size(); j++)
			{
				if (i != j)
				{
					RadioUnitContainer * k = dynamic_cast<RadioUnitContainer*>(tmpList[j]);
					if (!t->connectVehicleWithContainer(k))//返回0则连接数满了
					{
						break;
					}
				}
			}
		}
	}

	m_MinVertBlock = WCoord(minArray[0], minArray[1], minArray[2]);
	m_MaxVertBlock = WCoord(maxArray[0], maxArray[1], maxArray[2]);

	int iWheelNum = 0;
	Rainbow::Vector3f chassisMassVec = Rainbow::Vector3f(0, 0, 0);
	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		//修正分组刚体偏移位置, 以造成分组的关节位置为基准
		Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
		if (i>0 && m_JointCreateParams.size()>1)
		{
			offset = m_JointCreateParams[i].mPos0;
		}

		if (m_ChassisBlockNums[i]>0)
		{
			if (havemesh)
			{
				createChassisBoxMesh(i, offset);
				const Vector3f chassisDim = computeChassisAABBDimensions(i);
				m_VehicleCreateParams[i].mChassisParam.mDim = chassisDim;
			}
			if (chassis_pmeshes[i])
			{
				if (havemesh) 
				{
					createChassisEntity(chassis_pmeshes[i], offset);
					createWaterMaskEntity(chassis_mask_pmeshes[i], m_WaterMaskBound[i], offset);
				}	
				//else
					//chassis_pmeshes[i]->release();

			}
		}
		else
		{
			//无车身只有车轮，则需要删除车轮方块
			if (m_VehicleCreateMeshes[i].mRawMeshesWheel.size() > 0 && m_EntitiesWheel.size() > 0)
			{
				m_EntitiesWheel.erase(m_EntitiesWheel.begin() + iWheelNum, m_EntitiesWheel.begin() + iWheelNum + m_VehicleCreateMeshes[i].mRawMeshesWheel.size());
				m_WheelCoord.erase(m_WheelCoord.begin() + iWheelNum, m_WheelCoord.begin() + iWheelNum + m_VehicleCreateMeshes[i].mRawMeshesWheel.size());
				m_WheelBlockNum -= m_VehicleCreateMeshes[i].mRawMeshesWheel.size();
				m_VehicleCreateMeshes[i].mRawMeshesWheel.clear_dealloc();
				m_VehicleCreateMeshes[i].mRawMeshesWheelCollider.clear_dealloc();
			}
		}

		m_ChassisMassCenters[i] = Vector3f(chassisMassVecs[i].x / m_ChassisMasses[i], chassisMassVecs[i].y / m_ChassisMasses[i], chassisMassVecs[i].z / m_ChassisMasses[i]);
		m_VehicleCreateParams[i].mChassisParam.mMass = m_ChassisMasses[i] * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.mass_scale;
		m_VehicleCreateParams[i].mChassisParam.mMassCenter = Vector3f(m_ChassisMassCenters[i].x - offset.x, m_ChassisMassCenters[i].y - offset.y, m_ChassisMassCenters[i].z - offset.z);
		m_VehicleCreateParams[i].mChassisParam.mHigh = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.chassis_high;

		m_ChassisBlockNum += m_ChassisBlockNums[i];
		chassisMassVec += chassisMassVecs[i];
		m_ChassisMass += m_ChassisMasses[i];

		iWheelNum += m_VehicleCreateMeshes[i].mRawMeshesWheel.size();
	}

	if (havemesh)
	{
		auto iterblock = m_EntitiesChassisExMesh.begin();
		while (iterblock != m_EntitiesChassisExMesh.end())
		{
			Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
			//fromvalue>>10, (fromvalue>>5)&0x1f, (fromvalue & 0x1f)
			VehicleBlock *srcBlock = m_Blocks[iterblock->first >> 10][(iterblock->first >> 5) & 0x1f][iterblock->first & 0x1f];
			if (srcBlock->m_GroupIdSelf>0 && m_JointCreateParams.size()>0)
			{
				offset = m_JointCreateParams[srcBlock->m_GroupIdSelf].mPos0;
			}
			Rainbow::Entity* chassis_entity = Rainbow::Entity::Create();
			chassis_entity->AttachToScene(m_pWorld->getScene());
			iterblock->second->OnCreate();
			iterblock->second->SetPosition(Rainbow::Vector3f(-BLOCK_SIZE / 2 - offset.x, -offset.y, -BLOCK_SIZE / 2 - offset.z));  // -m_nOffsetYWorkshopAndWorld * BLOCK_SIZE
			chassis_entity->BindObject(0, iterblock->second);
			//iterblock->second->release();
			//chassis_entity->addRenderUsageBits(RU_SHADOWMAP);
			//iterblock->second->addRenderUsageBits(RU_SHADOWMAP);
			auto iter_entity = m_EntitiesChassisEx.find(iterblock->first);
			if (iter_entity != m_EntitiesChassisEx.end())
			{
				//iter_entity->second->release();
				DESTORY_GAMEOBJECT_BY_COMPOENT(iter_entity->second);
				m_EntitiesChassisEx.erase(iter_entity);
				
			}
			m_EntitiesChassisEx[iterblock->first] = chassis_entity;
			chassis_entity->AttachToScene(m_pWorld->getScene());
			iterblock++;
		}
	}

	if (m_ChassisBlockNum>0)
	{
		const Vector3f chassisDims = computeChassisAABBDimensions();
		getLocoMotion()->setBound((int)chassisDims.y, (int)chassisDims.x);
		getLocoMotion()->setAttackBound((int)chassisDims.y, (int)chassisDims.x, (int)chassisDims.z);
		m_VehicleDims = chassisDims;
		m_VehicleDims.y += wheelRadiusMax * 2;
	}

	m_ChassisMassCenter = Vector3f(chassisMassVec.x / m_ChassisMass, chassisMassVec.y / m_ChassisMass, chassisMassVec.z / m_ChassisMass);
	if (m_JointCreateParams.size())
	{
		MINIW::JointCreateParam jointParam;
		if (m_FatherVehilcID)
		{
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(m_pWorld->getActorMgr()->iFindActorByWID(m_FatherVehilcID));
			if (vehicle)
			{
				VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
				VehicleAssembleLocoMotion *locoVehicle_me = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
				if (locoVehicle->m_PhysActorVec.size() && locoVehicle->m_ChassisUpdatePos.size())
				{
					//Rainbow::Vector3f pos;
					//Rainbow::Quaternionf quat;
					//locoVehicle->m_PhysActorVec[0]->GetPos(pos, quat);
					//locoVehicle->m_PhysActorVec[0]->GetPxRigidActor()->setGlobalPose(PxTransform(Vector3f(pos.x, pos.y+10, pos.z), PxQuat(quat.x, quat.y, quat.z , quat.w)));

					WCoord start = WCoord(0, 0, 0);
					WCoord dim = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);
					if (start.x > start.x + dim.x)
						start.x = start.x + dim.x;
					if (start.y > start.y + dim.y)
						start.y = start.y + dim.y;
					if (start.z > start.z + dim.z)
						start.z = start.z + dim.z;
					//WCoord centerPos = WCoord(start.x+MAX_DIM_X/2, start.y, start.z+MAX_DIM_Z/2);
					WCoord pos(start.x + m_FatherVehilcPos.x, start.y + m_FatherVehilcPos.y, start.z + m_FatherVehilcPos.z);
					Block srcBlock = vehicle->getBlock(m_FatherVehilcPos);
					int group = vehicle->getPartGroupIdWithPos(m_FatherVehilcPos);
					int dir = srcBlock.getData() & 7;
					MINIW::JointCreateParam jointParam;
					jointParam.mType = JointType::HFIXED;
					Matrix4x4f tm;
					group = locoVehicle->getPhysIndexWithGroupId(group);
					if (group >= (int)locoVehicle->m_ChassisUpdatePos.size())
						group = 0;
					Rainbow::Vector3f chassisposfather = locoVehicle->m_ChassisUpdatePos[group].m_PPosition.toVector3();
					chassisposfather.y += 0.0f;
					tm.SetTRS(chassisposfather, locoVehicle->m_ChassisUpdatePos[group].m_RotateQuat, Vector3f::one);
					tm.Invert_Full();
					Matrix4x4f tm1;
					Rainbow::Vector3f chassispos = locoVehicle_me->m_Position.toVector3();
					tm1.SetTRS(chassispos, locoVehicle_me->m_RotateQuat,
						Vector3f::one);
					Matrix4x4f tm2 = Matrix4x4Mul(tm1, tm);
					jointParam.mPos0 = tm2.GetPosition();
					MatrixToQuaternionf(tm2, jointParam.mQuat0);
					jointParam.mQuat1 = jointParam.mQuat0;
					jointParam.mContainerPos = Rainbow::Vector3f((float)pos.x, (float)pos.y, (float)pos.z);
					jointParam.mDir = dir;
					jointParam.mInvInertiaScale = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.inv_inertia_scale;
					jointParam.mInvMassScale = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.inv_mass_scale;
					jointParam.x = m_FatherVehilcPos.x;
					jointParam.y = m_FatherVehilcPos.y;
					jointParam.z = m_FatherVehilcPos.z;
					m_JointCreateParams[0] = jointParam;
					for (int i = 1; i<(int)m_JointCreateParams.size(); i++)
					{
						m_JointCreateParams[i].mPos1 = m_JointCreateParams[i].mPos0;
						m_JointCreateParams[i].mQuat1 = m_JointCreateParams[i].mQuat0;
					}
				}
			}
		}
	}

	resetAttib();

	if (m_LifeCurrent == 0)
	{
		check_link_core();
	}
	if (getBlockNum() > 0)
	{
		int num = getBlockNum() / 10;
		if (num < 1)num = 1;
		g_all_physxitem += num;
	}

	std::vector<VehicleNodeStruct> lineNodes;
	m_VehicleBlockNodes.clear();
	std::vector<WCoord> nodesPos;

	VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (!srcBlock)
					continue;

				int blockid = srcBlock->getResID();
				int blockdata = srcBlock->getData();

				if (blockid > 0 && blockid != BLOCK_WORKSHOP) //1085, 是否是车间本身方块, 若是需要排除掉
				{
					// 设置物理机械所有的节点
					int porttype = 0;
					if (vehicleModule)
					{
						porttype = vehicleModule->getBlockPhysicsPortType(blockid);
					}
					if (blockid > 0 && porttype > -1)
					{
						VehicleNodeStruct nodedata;
						nodedata.data = (x << 10) + (y << 5) + z;
						nodedata.porttype = porttype;

						const PhysicsPartsDef* partsdef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
						int nodeVal = (partsdef->PartsType << 15) + partsdef->SubType;
						if (vehicleModule && vehicleModule->isNodeMatch(nodeVal))
							nodedata.canedit = '1';
						else
							nodedata.canedit = '0';
						nodedata.groupid = m_Blocks[x][y][z]->m_GroupIdSelf;
						nodedata.dir = (srcBlock->getData()) & 7;

						if (IsDoorBlock(blockid))
						{
							WCoord doorBlockPos(x, y, z);
							bool isupper, isopen, mirror;
							int dir = DoorMaterial::ParseDoorDataInVehicle(m_pVehicleWorld, doorBlockPos, isupper, isopen, mirror);

							//判断是否是门下面的方块，只需要显示一个连接点（创建下面的连接点，涉及开关触发开门的逻辑）
							if (isupper)
								continue;
						}
						else if (blockid == BLOCK_CATAPULT)
						{
							//如果是投掷装置，只需要显示尾部一个连接点
							int ishead = blockdata & 4;
							if (ishead)
								continue;
						}
						else if (IsWindowsBlock(blockid))
						{
							bool isupper, isopen, mirror;
							WCoord windowBlockPos(x, y, z);
							WindowsMaterial::ParseWindowDataInVehicle(m_pVehicleWorld, windowBlockPos, isupper, isopen, mirror);
							if (isupper)
								continue;
						}
						else if (blockid == BLOCK_BOAT_RUDDER)
						{
							if ((blockdata & 8) == 0) 
								continue;
						}

						lineNodes.push_back(nodedata);
						nodesPos.push_back(WCoord(x, y, z));
					}
				}
			}
		}
	}
	if (m_LineGraph)
	{
		ENG_DELETE(m_LineGraph);
	}


	m_LineGraph = ENG_NEW(MGraph)(lineNodes.size());
	for (int i = 0; i < (int)lineNodes.size(); i++)
	{
		VehicleNodeStruct nodedata;
		// 这个data是第几个节点 索引用的
		nodedata.data = i;
		nodedata.porttype = lineNodes[i].porttype;
		nodedata.canedit = lineNodes[i].canedit;
		nodedata.groupid = lineNodes[i].groupid;
		nodedata.dir = lineNodes[i].dir;
		m_VehicleBlockNodes[lineNodes[i].data] = nodedata;
		// 这里的data是 相对位置position
		m_LineGraph->AddMGraph(lineNodes[i].data);
	}

	if (m_bMakeByItemUse)
	{
		autoMakeLines(nodesPos);
	}
	resetVehicleLine();
	ChangeDataToGraph();

	refreshAllSeatBindKeys();

	UpdateActionerInfo(vehicleactioners, pworld);
	//更新驾驶座和乘客座控制的键位数据
	vector<WCoord>temp_keyinfo;
	GetFromPartBindLineInfo(m_SeatRelativePos, temp_keyinfo);

	m_pContainerWorkshop = NULL;
}

bool ActorVehicleAssemble::isFunction(int blockid, int funid)
{
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
	if (!physicsPartsDef)
		return false;

	//获取配表信息
	//PhysicsPartsDef::EffectFunctionsDef* fun = NULL;
	for (int i = 0; i<(int)physicsPartsDef->EffectFunctions.size(); i++)
	{
		PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
		if (functiondef->func_id == funid)
		{
			//fun = functiondef;
			return true;
		}
	}

	return false;
}

void ActorVehicleAssemble::findSlideToPosAxisX(int targetid, const WCoord& slideFromPos, const WCoord& dim, int& mindist, WCoord& slideToPos)
{
	for (int ix = slideFromPos.x; ix <= dim.x; ix++)
	{
		Block blockTemp = m_Blocks[ix][slideFromPos.y][slideFromPos.z]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = ix - slideFromPos.x;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(ix, slideFromPos.y, slideFromPos.z);
			}
		}
		else if (blockTemp.isEmpty())
		{
			break;
		}
	}

	for (int ix = slideFromPos.x; ix >= 0; ix--)
	{
		Block blockTemp = m_Blocks[ix][slideFromPos.y][slideFromPos.z]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = slideFromPos.x - ix;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(ix, slideFromPos.y, slideFromPos.z);
			}
		}
		else if (blockTemp.isEmpty())
		{
			return;
		}
	}
}

void ActorVehicleAssemble::findSlideToPosAxisY(int targetid, const WCoord& slideFromPos, const WCoord& dim, int& mindist, WCoord& slideToPos)
{
	for (int iy = slideFromPos.y; iy <= dim.y; iy++)
	{
		Block blockTemp = m_Blocks[slideFromPos.x][iy][slideFromPos.z]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = iy - slideFromPos.y;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(slideFromPos.x, iy, slideFromPos.z);
			}
		}
		else if (blockTemp.isEmpty())
		{
			break;
		}
	}

	for (int iy = slideFromPos.y; iy >= 0; iy--)
	{
		Block blockTemp = m_Blocks[slideFromPos.x][iy][slideFromPos.z]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = slideFromPos.y - iy;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(slideFromPos.x, iy, slideFromPos.z);
			}
		}
		else if (blockTemp.isEmpty())
		{
			return;
		}
	}
}

void ActorVehicleAssemble::findSlideToPosAxisZ(int targetid, const WCoord& slideFromPos, const WCoord& dim, int& mindist, WCoord& slideToPos)
{
	for (int iz = slideFromPos.z; iz <= dim.z; iz++)
	{
		Block blockTemp = m_Blocks[slideFromPos.x][slideFromPos.y][iz]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = iz - slideFromPos.z;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(slideFromPos.x, slideFromPos.y, iz);
			}
		}
		else if (blockTemp.isEmpty())
		{
			break;
		}
	}

	for (int iz = slideFromPos.z; iz >= 0; iz--)
	{
		Block blockTemp = m_Blocks[slideFromPos.x][slideFromPos.y][iz]->m_Block;
		if (blockTemp.getResID() == targetid)
		{
			int d = slideFromPos.z - iz;
			if (d < mindist)
			{
				mindist = d;
				slideToPos = WCoord(slideFromPos.x, slideFromPos.y, iz);
			}
		}
		else if (blockTemp.isEmpty())
		{
			return;
		}
	}
}

WPreciseCoord ActorVehicleAssemble::getRealWorldPosWithPos(WCoord blockpos, bool realpos, int groupid)
{
	VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	float EmitterPlus = 0.f;
	if (!realpos)
	{
		if (locoVehicle && (blockpos.x < MAX_DIM_X && blockpos.y < MAX_DIM_Y && blockpos.z < MAX_DIM_Z)
			&& (blockpos.x >= 0 && blockpos.y >= 0 && blockpos.z >= 0))
		{
			if (locoVehicle->m_ChassisUpdatePos.size() == 0) return blockpos;

			VehicleBlock* pBlockData = m_Blocks[blockpos.x][blockpos.y][blockpos.z];
			int blockId = pBlockData->m_Block.getResID();
			int groupId = pBlockData->m_GroupIdSelf;
			if (blockId == 717 && groupId == 0)   //BLOCK_EMITTER_ID == 717
			{
				EmitterPlus = 0.5f;
			}

			if (pBlockData->GetUpdateCount() >= locoVehicle->m_UpdateCountResetPos)
			{
				return pBlockData->getRealWorldPos();
			}
			else
			{
				int vdir = getHeadDir();
				int groupId = pBlockData->m_GroupIdSelf;
				int indexPhy = locoVehicle->getPhysIndexWithGroupId(groupId);
				// 			if (groupId <= -1 ||/*|| groupId >= locoVehicle->m_ChassisUpdatePos.size()*/) groupId = 0;
				if (indexPhy != 0)
				{
					//滑动方块的方向，会有一定的偏移
					int xPlus = 0;
					int yPlus = 0;
					int zPlus = 0;
					if (m_JointCreateParams[groupId].mType == PRISMATIC)
					{
						int dir = getVehicleWorld()->getBlockData(m_JointCreateParams[groupId].mContainerPos) & 7;
						if (dir != vdir)
						{
							yPlus = (int)(0.5 * BLOCK_FSIZE);
						}
					}
					else if (m_JointCreateParams[groupId].mType == SUSPENSION)
					{
						yPlus = (int)(-0.5 * BLOCK_FSIZE);
					}

					Rainbow::Vector3f origin;

					origin.x = blockpos.x*BLOCK_FSIZE - m_JointCreateParams[groupId].mContainerPos.x * BLOCK_FSIZE;
					origin.z = blockpos.z*BLOCK_FSIZE - m_JointCreateParams[groupId].mContainerPos.z * BLOCK_FSIZE;
					origin.y = blockpos.y*BLOCK_FSIZE - m_JointCreateParams[groupId].mContainerPos.y * BLOCK_FSIZE + yPlus;

					Rainbow::Vector3f origin_world;

					locoVehicle->m_ChassisUpdatePosTM[indexPhy].MultiplyPoint3(origin, origin_world);
					pBlockData->setRealWorldPos(origin_world, locoVehicle->m_UpdateCountResetPos);
				}
				else
				{
					Rainbow::Vector3f origin;
					origin.x = blockpos.x*BLOCK_FSIZE - (MAX_DIM_X / 2)*BLOCK_FSIZE;
					origin.z = blockpos.z*BLOCK_FSIZE - (MAX_DIM_Z / 2)*BLOCK_FSIZE;
					origin.y = blockpos.y*BLOCK_FSIZE + EmitterPlus * BLOCK_FSIZE;
					Rainbow::Vector3f origin_world;

					locoVehicle->m_ChassisUpdatePosTM[0].MultiplyPoint3(origin, origin_world);
					pBlockData->setRealWorldPos(origin_world, locoVehicle->m_UpdateCountResetPos);
				}
				m_VehicleAABBDirty |= true;
				return pBlockData->getRealWorldPos();
			}
		}
		else
		{
			return blockpos;
		}
	}
	else
	{
		int indexPhy = locoVehicle->getPhysIndexWithGroupId(groupid);
		if (locoVehicle->m_ChassisUpdatePos.size() == 0) return blockpos;
		Rainbow::Vector3f origin_world;
		if (indexPhy != 0)
		{
			Rainbow::Vector3f origin;
			origin.x = blockpos.x - m_JointCreateParams[groupid].mContainerPos.x * BLOCK_FSIZE;
			origin.z = blockpos.z - m_JointCreateParams[groupid].mContainerPos.z * BLOCK_FSIZE;
			origin.y = blockpos.y - m_JointCreateParams[groupid].mContainerPos.y * BLOCK_FSIZE;
			locoVehicle->m_ChassisUpdatePosTM[indexPhy].MultiplyPoint3(origin, origin_world);
		}
		else
		{
			Rainbow::Vector3f origin;
			origin.x = blockpos.x - (MAX_DIM_X / 2)*BLOCK_FSIZE;
			origin.z = blockpos.z - (MAX_DIM_Z / 2)*BLOCK_FSIZE;
			origin.y = (float)blockpos.y;
			locoVehicle->m_ChassisUpdatePosTM[0].MultiplyPoint3(origin, origin_world);
		}
		return origin_world;
	}
	return WPreciseCoord();
}

WCoord ActorVehicleAssemble::convertWcoord(WCoord blockpos, bool realpos)
{
	WCoord bpos = blockpos;
	if (realpos)
	{
		bpos = bpos / BLOCK_SIZE;
	}
	return getRealWorldPosWithPos(bpos);
}

WCoord ActorVehicleAssemble::convertWcoord(Rainbow::Vector3f blockpos, bool realpos, int groupid)
{
	WCoord bpos = blockpos;
	if (realpos && groupid == -1)
	{
		WCoord bpos_ = bpos / BLOCK_SIZE;
		if ((bpos_.x < MAX_DIM_X && bpos_.y < MAX_DIM_Y && bpos_.z < MAX_DIM_Z)
			&& (bpos_.x >= 0 && bpos_.y >= 0 && bpos_.z >= 0))
		{
			VehicleBlock* pBlockData = m_Blocks[bpos_.x][bpos_.y][bpos_.z];
			groupid = pBlockData->m_GroupIdSelf;
		}
		else
		{
			groupid = 0;
		}
	}
	return getRealWorldPosWithPos(bpos, realpos, groupid);
}

WPreciseCoord ActorVehicleAssemble::convertWPrecisecoord(WCoord blockpos, bool realpos)
{
	WCoord bpos = blockpos;
	if (realpos)
	{
		bpos = bpos / BLOCK_SIZE;
	}
	return getRealWorldPosWithPos(bpos);
}

WPreciseCoord ActorVehicleAssemble::convertWPrecisecoord(Rainbow::Vector3f blockpos, bool realpos)
{
	WCoord bpos = WCoord(blockpos.x, blockpos.y, blockpos.z);
	return convertWcoord(bpos, realpos);
}

Rainbow::Quaternionf ActorVehicleAssemble::convertRot(const WCoord &blockpos)
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc->m_ChassisUpdatePos.size() == 0)
	{
		return Rainbow::Quaternionf(0.0f, 0.0f, 0.0f, 1.0f);
	}
	int groupId = 0;
	if ((blockpos.x < MAX_DIM_X && blockpos.y < MAX_DIM_Y && blockpos.z < MAX_DIM_Z)
		&& (blockpos.x >= 0 && blockpos.y >= 0 && blockpos.z >= 0))
	{
		VehicleBlock* vehicleBlock = m_Blocks[blockpos.x][blockpos.y][blockpos.z];
		groupId = vehicleBlock->m_GroupIdSelf;
	}

	groupId = loc->getPhysIndexWithGroupId(groupId);
	// 	if (groupId <= -1/*|| groupId >= locoVehicle->m_ChassisUpdatePos.size()*/)
	// 	{
	// 		groupId = 0;
	// 	}
	return loc->m_ChassisUpdatePos[groupId].m_RotateQuat;
}

Rainbow::Vector3f ActorVehicleAssemble::convertRealDir(WCoord blockpos, int facedir, int &groupId)
{
	Rainbow::Quaternionf quatTarget;
	if (DIR_POS_Z == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(0, 0, 0));
	else if (DIR_NEG_Z == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(0, kOnePI, 0));
	else if (DIR_NEG_X == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(0, -kHalfPI, 0));
	else if (DIR_POS_X == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(0, kHalfPI, 0));
	else if (DIR_POS_Y == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(-kHalfPI, 0, 0));
	else if (DIR_NEG_Y == facedir)
		quatTarget = EulerToQuaternionf(Vector3f(kHalfPI, 0, 0));
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc->m_ChassisUpdatePos.size() == 0)
	{
		return quatTarget.GetAxisZ();
	}
	int group = 0;
	if ((blockpos.x < MAX_DIM_X && blockpos.y < MAX_DIM_Y && blockpos.z < MAX_DIM_Z)
		&& (blockpos.x >= 0 && blockpos.y >= 0 && blockpos.z >= 0))
	{
		VehicleBlock* vehicleBlock = m_Blocks[blockpos.x][blockpos.y][blockpos.z];
		group = vehicleBlock->m_GroupIdSelf;
	}
	groupId = group == -1 ? 0 : group;
	group = loc->getPhysIndexWithGroupId(group);
	// 	if (group == -1 || group >= loc->m_ChassisUpdatePos.size())
	// 		group = 0;
	// 新引擎的四元数乘法需要交换顺序
	quatTarget = loc->m_ChassisUpdatePos[group].m_RotateQuat * quatTarget;
	return quatTarget.GetAxisZ();
	//return Vector3f::zero;
}

bool ActorVehicleAssemble::getRealWorldPosAndRotate(const int &dir, const WCoord &blockpos, WCoord &realPos, const Quaternionf &tagetQuaternion, Quaternionf &realRotate, const WCoord &offsetPos)
{
	if ((blockpos.x < MAX_DIM_X && blockpos.y < MAX_DIM_Y && blockpos.z < MAX_DIM_Z)
		&& (blockpos.x >= 0 && blockpos.y >= 0 && blockpos.z >= 0))
	{
		realPos = convertWcoord(blockpos);

		int groupId = m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_GroupIdSelf;
		VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
		Rainbow::Quaternionf quatRotate;
		if (loc->m_ChassisUpdatePos.size() > 0)
		{
			// 			if (groupId == -1) groupId = 0;
			int index = loc->getPhysIndexWithGroupId(groupId);
			quatRotate = loc->m_ChassisUpdatePos[index].m_UpdateRot;
		}
		else
		{
			quatRotate = Rainbow::Quaternionf(0.0f, 0.0f, 0.0f, 1.0f);
		}
		float transformX = 0.5f;
		float transformY = 0.5f;
		float transformZ = 0.5f;
		bool hasJoint = false;
		JointType nJointType = JointType::HFIXED;
		if (m_JointCreateParams.size())
		{
			hasJoint = true;
			nJointType = m_JointCreateParams[groupId].mType;
		}
		if (groupId != 0 && hasJoint)
		{
			int jointDir = m_JointCreateParams[groupId].mDir;
			if (DIR_POS_X == jointDir || DIR_NEG_X == jointDir)
			{
				if (nJointType == JointType::REVOLUTE)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						transformX = 0.4f;
						transformZ = 0;
						transformY = 0;
					}
					else if (DIR_NEG_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = -0.5f;
					}
					else if (DIR_POS_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0.5f;
					}
					else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0;
					}
				}
				else if (nJointType == JointType::PRISMATIC)
				{
					if (DIR_NEG_X == jointDir)
					{
						if (DIR_POS_X == dir || DIR_NEG_X == dir)
						{
							transformX = 0.4f;
							transformZ = 0;
							// 							transformY = 0;
						}
						else if (DIR_NEG_Y == dir || DIR_POS_Y == dir)
						{
							transformX = 0;
							transformZ = 0;
							transformY = 0;
						}
						else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
						{
							transformX = 0;
							transformZ = 0;
							// 							transformY = 0;
						}
					}
					else
					{
						if (DIR_POS_X == dir || DIR_NEG_X == dir)
						{
							transformX = 0.4f;
							transformZ = 0;
							transformY = 0;
						}
						else if (DIR_NEG_Y == dir)
						{
							transformX = 0;
							transformZ = 0;
							transformY = -0.5f;
						}
						else if (DIR_POS_Y == dir)
						{
							transformX = 0;
							transformZ = 0;
							transformY = 0.5f;
						}
						else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
						{
							transformX = 0;
							transformZ = 0;
							transformY = 0;
						}
					}
				}
				else if (nJointType == JointType::ARM_PRISMATIC)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
				else if (nJointType == JointType::T_REVOLUTE)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
			}
			else if (DIR_POS_Y == jointDir || DIR_NEG_Y == jointDir)
			{
				if (nJointType == JointType::REVOLUTE)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						transformX = 0.4f;
						transformZ = 0;
					}
					else if (DIR_POS_Y == dir || DIR_NEG_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0;
					}
					else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
					{
						transformX = 0;
						transformZ = 0;
					}
				}
				else if (nJointType == JointType::PRISMATIC)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						transformX = 0.4f;
						transformZ = 0;
						transformY = 0;
					}
					else if (DIR_NEG_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = -0.5f;
					}
					else if (DIR_POS_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0.5;
					}
					else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0;
					}
				}
				else if (nJointType == JointType::ARM_PRISMATIC)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
				else if (nJointType == JointType::T_REVOLUTE)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
			}
			else if (DIR_POS_Z == jointDir || DIR_NEG_Z == jointDir)
			{
				if (nJointType == JointType::REVOLUTE)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						transformX = 0.4f;
						transformZ = 0;
						transformY = 0;
					}
					else if (DIR_NEG_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = -0.5f;
					}
					else if (DIR_POS_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0.5f;
					}
					else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0;
					}
				}
				else if (nJointType == JointType::PRISMATIC)
				{
					if (DIR_POS_X == dir || DIR_NEG_X == dir)
					{
						transformX = 0.4f;
						transformZ = 0;
						transformY = 0;
					}
					else if (DIR_NEG_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = -0.5;
					}
					else if (DIR_POS_Y == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0.5;
					}
					else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
					{
						transformX = 0;
						transformZ = 0;
						transformY = 0;
					}
				}
				else if (nJointType == JointType::ARM_PRISMATIC)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
				else if (nJointType == JointType::T_REVOLUTE)
				{
					if (DIR_POS_X == dir)
					{
					}
					else if (DIR_NEG_X == dir)
					{
					}
					else if (DIR_NEG_Y == dir)
					{
					}
					else if (DIR_POS_Y == dir)
					{
					}
					else if (DIR_POS_Z == dir)
					{
					}
					else if (DIR_NEG_Z == dir)
					{
					}
				}
			}
		}
		else
		{
			if (DIR_POS_X == dir || DIR_NEG_X == dir)
			{
				transformX = 0.4f;
				transformZ = 0;

			}
			else if (DIR_POS_Y == dir || DIR_NEG_Y == dir)
			{
				transformX = 0;
				transformZ = 0;
				transformY = 0;
			}
			else if (DIR_POS_Z == dir || DIR_NEG_Z == dir)
			{
				transformX = 0;
				transformZ = 0;
			}
		}

		// 新引擎的四元数乘法需要交换顺序
		realRotate = quatRotate * tagetQuaternion;

		Matrix4x4f tm_;
		tm_.SetTRS(Rainbow::Vector3f(0.0f, 0.0f, 0.0f), realRotate, Rainbow::Vector3f(1.0f, 1.0f, 1.0f));
		Matrix4x4f tm;
		tm.SetTRS(Rainbow::Vector3f(transformX* BLOCK_SIZE + offsetPos.x, transformY* BLOCK_SIZE + offsetPos.y, transformZ* BLOCK_SIZE + offsetPos.z), Rainbow::Quaternionf(0.0f, 0.0f, 0.0f, 1.0f), Rainbow::Vector3f(1.0f, 1.0f, 1.0f));
		tm = Matrix4x4Mul(tm, tm_);
		tm.GetPosition();
#ifdef _DEBUG
		// 	LOG_INFO("~!~!!!!!!! realPos x = %d, y = %d, z = %d;;;tm x = %d, y = %d, z = %d", realPos.x, realPos.y, realPos.z, tm.getTranslate().x, tm.getTranslate().y, tm.getTranslate().z);
#endif
		realPos = realPos + tm.GetPosition();

		return true;
	}

	return false;
}

int ActorVehicleAssemble::getJointTypeWithGroupId(int groupid)
{
	if (groupid > 0 && groupid < (int)m_JointCreateParams.size())
	{
		return m_JointCreateParams[groupid].mType;
	}
	return 0;
}

WCoord ActorVehicleAssemble::getSuspensionWithGroupId(int groupid)
{
	if (m_Suspensions.find(groupid) != m_Suspensions.end())
	{
		return m_Suspensions[groupid];
	}
	return WCoord(0, -1, 0);
}

int ActorVehicleAssemble::getJointTypeWithPos(WCoord &pos)
{
	return getJointTypeWithGroupId(m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdSelf);
}

int ActorVehicleAssemble::getPartGroupIdWithPos(const WCoord &blockpos)
{
	int group = 0;
	if ((blockpos.x < MAX_DIM_X && blockpos.y < MAX_DIM_Y && blockpos.z < MAX_DIM_Z)
		&& (blockpos.x >= 0 && blockpos.y >= 0 && blockpos.z >= 0))
	{
		VehicleBlock* vehicleBlock = m_Blocks[blockpos.x][blockpos.y][blockpos.z];
		group = vehicleBlock->m_GroupIdSelf;
	}
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc->getPhysIndexWithGroupId(group))
	{
		return group;
	}
	return 0;
	// 	if (group == -1 || group >= loc->m_ChassisUpdatePos.size())
	// 	{
	// 		group = 0;
	// 	}

	// 	return group;
}

bool ActorVehicleAssemble::inBlock(WCoord &pos, int &blockID, int &_x, int &_y, int &_z)
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		if (loc->m_ChassisUpdatePos.size() == 0)
		{
			return false;
		}
		for (int m = 0; m<(int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1.0f, loc->m_ChassisUpdatePos[m].m_RotateQuat, 
			// 				Rainbow::Vector3f(loc->m_ChassisUpdatePos[m].m_PPosition.x,
			// 				loc->m_ChassisUpdatePos[m].m_PPosition.y,
			// 				loc->m_ChassisUpdatePos[m].m_PPosition.z));
			tm.Invert_Full();
			Rainbow::Vector3f loaclPos;
			tm.MultiplyPoint3(pos.toVector3(), loaclPos);
			loaclPos.x += (MAX_DIM_X / 2 + 0.5f)*BLOCK_FSIZE;
			loaclPos.z += (MAX_DIM_Z / 2 + 0.5f)*BLOCK_FSIZE;
			loaclPos.y += BLOCK_HALFSIZE;//

			int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
			if (groupIdPhy > 0 && (int)m_JointCreateParams.size() > groupIdPhy)
			{
				Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
				loaclPos += offset;
			}
			loaclPos.x /= 100.0f;
			loaclPos.y /= 100.0f;
			loaclPos.z /= 100.0f;
			//cube containing origin point
			int x = (int)(loaclPos.x);
			int y = (int)(loaclPos.y);
			int z = (int)(loaclPos.z);
			if (x >= 0 && y >= 0 && z >= 0 && x<MAX_DIM_X && y<MAX_DIM_Y && z<MAX_DIM_Z)
			{
				VehicleBlock *srcBlock = m_Blocks[x][y][z];
				blockID = srcBlock->m_Block.getResID();
				if (srcBlock->m_GroupIdSelf != groupIdPhy)
				{
					continue;
				}
				if (blockID == 0 || srcBlock->m_iCurLife <= 0)
				{
					break;
				}
				_x = x;
				_y = y;
				_z = z;
				return true;
			}
		}
	}
	return false;
}

bool ActorVehicleAssemble::intersect(const MINIW::WorldRay &ray, float &t, int &blockID, int &_x, int &_y, int &_z, bool checkBlockCollide)
{
	t = ray.m_Range;
	IntersectResult result;

	float scale = 1.0f / float(BLOCK_SIZE);
	Rainbow::Vector3f origin = ray.m_Origin.toVector3()*scale;
	float range = ray.m_Range*scale;
	//Rainbow::Vector3f dir = ray.m_Dir;
	float radius = 7 * BLOCK_FSIZE;
	bool find_block = false;


	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		if (loc->m_ChassisUpdatePos.size() == 0)
		{
			return false;
		}
		for (int m = 0; m<(int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1.0f, loc->m_ChassisUpdatePos[m].m_RotateQuat, 
			// 				Rainbow::Vector3f(WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().x/* + MAX_DIM_X/2*1000*/),
			// 				WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().y),
			// 				WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().z /*+ MAX_DIM_X/2*1000*/)));
			tm.Invert_Full();

			MINIW::Ray localray;
			localray.m_Range = ray.m_Range;
			tm.MultiplyPoint3(ray.m_Origin.toVector3(), localray.m_Origin);
			tm.MultiplyVector3(ray.m_Dir, localray.m_Dir);
			float len = localray.m_Dir.Length();
			localray.m_Dir /= len;

			origin = localray.m_Origin;
			origin.x += (MAX_DIM_X / 2 + 0.5f)*BLOCK_FSIZE;
			origin.z += (MAX_DIM_Z / 2 + 0.5f)*BLOCK_FSIZE;
			origin.y += 0;
			int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
			if (m > 0 && m_JointCreateParams.size()>0)
			{
				Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
				origin += offset;
			}

			origin.x /= 100.0f;
			origin.y /= 100.0f;
			origin.z /= 100.0f;

			Rainbow::Vector3f &dir = localray.m_Dir;
			Rainbow::Vector3f origin_c2 = origin * float(BLOCK_SIZE);
			//cube containing origin point
			int x = (int)floor(origin.x);
			int y = (int)floor(origin.y);
			int z = (int)floor(origin.z);

			//怪物和载具的射线检测调试
			/*if (!checkBlockCollide) y -= 1;*/

			//break out direction vector
			float dx = dir.x;
			float dy = dir.y;
			float dz = dir.z;

			//direction to increment x, y, z when stepping
			int stepX = dx>0 ? 1 : (dx<0 ? -1 : 0);
			int stepY = dy>0 ? 1 : (dy<0 ? -1 : 0);
			int stepZ = dz>0 ? 1 : (dz<0 ? -1 : 0);

			float tMaxX = intbound(origin.x, dx);
			float tMaxY = intbound(origin.y, dy);
			float tMaxZ = intbound(origin.z, dz);

			float tDeltaX = stepX / dx;
			float tDeltaY = stepY / dy;
			float tDeltaZ = stepZ / dz;

			DirectionType face = DIR_NOT_INIT;
			bool inter_ret = false;

			WCoord blockorigin = WCoord(WorldPos::GetOriginPos()) / BLOCK_SIZE;

			while (true)
			{
				do
				{
					if (x >= 0 && y >= 0 && z >= 0 && x<MAX_DIM_X && y<MAX_DIM_Y && z<MAX_DIM_Z)
					{
						WCoord blockpos = blockorigin + WCoord(x, y, z);
						VehicleBlock *srcBlock = m_Blocks[x][y][z];
						blockID = srcBlock->m_Block.getResID();
						if (srcBlock->m_GroupIdSelf != groupIdPhy)
						{
							break;
						}
						if (blockID == 0 || srcBlock->m_iCurLife <= 0)
						{
							break;
						}
						BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockID);
						IntersectResult inter_result;
						if (blockmtl && blockmtl->m_Def && blockmtl->m_Def->ClickCollide && (!checkBlockCollide || IsBlockCollideWithRay(blockmtl, m_pVehicleWorld, blockpos, origin_c2, dir, &inter_result)))
						{
							WCoord coord = convertWcoord(WCoord(x, y, z));
							Rainbow::Vector3f pos = ray.m_Origin.toVector3();
							float t_ = (float)Sqrt(((float)coord.x - pos.x)*((float)coord.x - pos.x) + ((float)coord.y - pos.y)*((float)coord.y - pos.y) + ((float)coord.z - pos.z)*((float)coord.z - pos.z));
							//t =  (float)Sqrt(((float)x + 0.5 - origin.x)*((float)x + 0.5 - origin.x)+((float)y + 0.5- origin.y)*((float)y + 0.5- origin.y)+((float)z + 0.5 - origin.z)*((float)z + 0.5 - origin.z))*BLOCK_SIZE;
							if (t_<t && t_< localray.m_Range)
							{
								_x = x;
								_y = y;
								_z = z;
								t = t_;
								find_block = true;
								goto over_while;
							}
							else
								goto over_while;
						}
						else
						{
							break;
						}
					}
				} while (0);
				// tMaxX stores the t-value at which we cross a cube boundary along the
				// X axis, and similarly for Y and Z. Therefore, choosing the least tMax
				// chooses the closest cube boundary. Only the first case of the four
				// has been commented in detail.
				if (tMaxX < tMaxY)
				{
					if (tMaxX < tMaxZ)
					{
						if (tMaxX > radius) break;
						// Update which cube we are now in.
						x += stepX;
						// Adjust tMaxX to the next X-oriented boundary crossing.
						tMaxX += tDeltaX;
						// Record the normal vector of the cube face we entered.
						face = DIR_NEG_X;
					}
					else
					{
						if (tMaxZ > radius) break;
						z += stepZ;
						tMaxZ += tDeltaZ;
						face = DIR_NEG_Z;
					}
				}
				else
				{
					if (tMaxY < tMaxZ)
					{
						if (tMaxY > radius) break;
						y += stepY;
						tMaxY += tDeltaY;
						face = DIR_NEG_Y;
					}
					else
					{
						// Identical to the second case, repeated for simplicity in
						// the conditionals.
						if (tMaxZ > radius) break;
						z += stepZ;
						tMaxZ += tDeltaZ;
						face = DIR_NEG_Z;
					}
				}
			}
		over_while:;
		}
	}

	return find_block;
}

bool ActorVehicleAssemble::intersect(ClientActor *actor, int &x, int &y, int &z)
{
	MINIW::WorldRay ray;
	if (!actor)
		return false;
	/*
	WCoord center;
	if (actor->getObjType() <= OBJ_TYPE_ROLE)
	{
	center = actor->getEyePosition();
	}
	else
	{
	center = actor->getPosition();
	}
	ray.m_Origin = center.toWorldPos();
	ray.m_Dir = Yaw2FowardDir(actor->getLocoMotion()->m_RotateYaw);
	ray.m_Range = 7*BLOCK_FSIZE;
	*/
	//获取更精确的射线
	WCoord pos;
	if (actor->getObjType() == OBJ_TYPE_THROWABLE)
		pos = WCoord(actor->getPosition());
	else
		pos = WCoord(actor->getEyePosition());
	float yaw;
	float pitch;
	yaw = actor->getLocoMotion()->m_RotateYaw;
	pitch = actor->getLocoMotion()->m_RotationPitch;
	float speedadd = 0.0f;

	ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
	PlayerControl *player_control = dynamic_cast<PlayerControl *>(actor);
	// 客机
	if (player && ((player_control != g_pPlayerCtrl) || (player_control == NULL)))
	{
		// 如果是客机，从GunLogic获取客机同步过来的精确位置和朝向
		Rainbow::Vector3f tmp;
		if (player->getGunLogical()) player->getGunLogical()->getDir(yaw, pitch, tmp);
		pos = tmp;
		Rainbow::Vector3f dir;
		PitchYaw2Direction(dir, yaw, pitch);
		ray.m_Origin = pos.toWorldPos();
		ray.m_Dir = dir;
		ray.m_Range = 10 * BLOCK_FSIZE;
	}
	// 主机
	else if (player && g_pPlayerCtrl)
	{
		if (g_pPlayerCtrl->getCamera()->getMode() == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = player_control->m_pCamera->getLookDir();
		}
		else if (g_pPlayerCtrl->getCamera()->getMode() == CAMERA_CUSTOM_VIEW
			&& g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else g_pPlayerCtrl->getCamera()->getViewRayByScreenPt(&ray, player_control->m_CurMouseX, player_control->m_CurMouseY);
		ray.m_Range = 10 * BLOCK_FSIZE;
	}
	else
	{
		//载具和怪物碰撞，如果使用怪物的方向作为射线的方向，可能出现检测不到的问题。例如：怪物在向前跑，载具从后面撞上怪物
		Rainbow::Vector3f dir;
		PitchYaw2Direction(dir, yaw, pitch);
		ray.m_Origin = pos.toWorldPos();
		ray.m_Dir = dir;
		ray.m_Range = 10 * BLOCK_FSIZE;
		int interactBlockID;
		float t = 0;
		if (intersect(ray, t, interactBlockID, x, y, z))
		{
			return true;
		}
		CollideAABB box1;
		actor->getCollideBox(box1);
		box1.expand(15, 0, 15);

		return intersectBox(box1, interactBlockID, x, y, z);
	}
	int blockID = 0;
	float t = 0;

	return intersect(ray, t, blockID, x, y, z);
}

Vector3f ActorVehicleAssemble::computeChassisAABBDimensions(int i)
{
	Vector3f chassisMin(3000, 3000, 3000);
	Vector3f chassisMax(-3000, -3000, -3000);

	for (UInt32 j = 0; j<m_VehicleCreateMeshes[i].mBoxMeshesChassis.size(); j++)
	{
		const Rainbow::BoxMesh& boxMesh = m_VehicleCreateMeshes[i].mBoxMeshesChassis[j];
		for (UInt32 k = 0; k<boxMesh.mPos.size(); k++)
		{
			chassisMin.x = Min(chassisMin.x, boxMesh.mPos[k].x - boxMesh.mExten[k].x);
			chassisMin.y = Min(chassisMin.y, boxMesh.mPos[k].y - boxMesh.mExten[k].y);
			chassisMin.z = Min(chassisMin.z, boxMesh.mPos[k].z - boxMesh.mExten[k].z);

			chassisMax.x = Max(chassisMax.x, boxMesh.mPos[k].x + boxMesh.mExten[k].x);
			chassisMax.y = Max(chassisMax.y, boxMesh.mPos[k].y + boxMesh.mExten[k].y);
			chassisMax.z = Max(chassisMax.z, boxMesh.mPos[k].z + boxMesh.mExten[k].z);
		}
	}

	Vector3f chassisDims = chassisMax - chassisMin;
	if (chassisDims.x < 0)
	{
		chassisDims = Vector3f::one;
	}
	return chassisDims;
}

bool ActorVehicleAssemble::intersectWithResult(ClientActor* actor, int& x, int& y, int& z, IntersectResult& result)
{
	MINIW::WorldRay ray;
	if (!actor)
		return false;
	/*
	WCoord center;
	if (actor->getObjType() <= OBJ_TYPE_ROLE)
	{
	center = actor->getEyePosition();
	}
	else
	{
	center = actor->getPosition();
	}
	ray.m_Origin = center.toWorldPos();
	ray.m_Dir = Yaw2FowardDir(actor->getLocoMotion()->m_RotateYaw);
	ray.m_Range = 7*BLOCK_FSIZE;
	*/
	//获取更精确的射线
	WCoord pos;
	if (actor->getObjType() == OBJ_TYPE_THROWABLE)
		pos = WCoord(actor->getPosition());
	else
		pos = WCoord(actor->getEyePosition());
	float yaw;
	float pitch;
	yaw = actor->getLocoMotion()->m_RotateYaw;
	pitch = actor->getLocoMotion()->m_RotationPitch;
	float speedadd = 0.0f;

	ClientPlayer* player = dynamic_cast<ClientPlayer*>(actor);
	PlayerControl* player_control = dynamic_cast<PlayerControl*>(actor);
	// 客机
	if (player && ((player_control != g_pPlayerCtrl) || (player_control == NULL)))
	{
		// 如果是客机，从GunLogic获取客机同步过来的精确位置和朝向
		Vector3f tmp;
		if (player->getGunLogical()) player->getGunLogical()->getDir(yaw, pitch, tmp);
		pos = tmp;
		Vector3f dir;
		PitchYaw2Direction(dir, yaw, pitch);
		ray.m_Origin = pos.toWorldPos();
		ray.m_Dir = dir;
		ray.m_Range = 10 * BLOCK_FSIZE;
	}
	// 主机
	else if (player && g_pPlayerCtrl)
	{
		if (g_pPlayerCtrl->getCamera()->getMode() == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = player_control->m_pCamera->getLookDir();
		}
		else if (g_pPlayerCtrl->getCamera()->getMode() == CAMERA_CUSTOM_VIEW
			&& g_pPlayerCtrl->getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else g_pPlayerCtrl->getCamera()->getViewRayByScreenPt(&ray, player_control->m_CurMouseX, player_control->m_CurMouseY);
		ray.m_Range = 10 * BLOCK_FSIZE;
	}
	else
	{
		//载具和怪物碰撞，如果使用怪物的方向作为射线的方向，可能出现检测不到的问题。例如：怪物在向前跑，载具从后面撞上怪物
		Vector3f dir;
		PitchYaw2Direction(dir, yaw, pitch);
		ray.m_Origin = pos.toWorldPos();
		ray.m_Dir = dir;
		ray.m_Range = 10 * BLOCK_FSIZE;
		int interactBlockID;
		float t = 0;
		if (intersectWithResult(ray, t, interactBlockID, x, y, z,result))
		{
			return true;
		}
		CollideAABB box1;
		actor->getCollideBox(box1);
		box1.expand(15, 0, 15);

		return intersectBox(box1, interactBlockID, x, y, z);
	}
	int blockID = 0;
	float t = 0;
	return intersectWithResult(ray, t, blockID, x, y, z,result);
}
bool ActorVehicleAssemble::intersectWithResult(const MINIW::WorldRay& ray, float& t, int& blockID, int& _x, int& _y, int& _z, IntersectResult& result, bool checkBlockCollide )
{

	t = ray.m_Range;
	//IntersectResult result;

	float scale = 1.0f / float(BLOCK_SIZE);
	Vector3f origin = ray.m_Origin.toVector3() * scale;
	float range = ray.m_Range * scale;
	//MINIW::Vector3 dir = ray.m_Dir;
	float radius = 7 * BLOCK_FSIZE;
	bool find_block = false;


	VehicleAssembleLocoMotion* loc = static_cast<VehicleAssembleLocoMotion*>(getLocoMotion());
	if (loc)
	{
		if (loc->m_ChassisUpdatePos.size() == 0)
		{
			return false;
		}
		for (int m = 0; m < (int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1.0f, loc->m_ChassisUpdatePos[m].m_RotateQuat, 
			// 				Vector3(WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().x/* + MAX_DIM_X/2*1000*/),
			// 				WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().y),
			// 				WorldPos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PPosition.toWorldPos().z /*+ MAX_DIM_X/2*1000*/)));
			tm.Invert_Full();
			MINIW::Ray localray;
			localray.m_Range = ray.m_Range;
			tm.MultiplyPoint3(ray.m_Origin.toVector3(),localray.m_Origin);
			tm.MultiplyVector3(ray.m_Dir,localray.m_Dir);
			float len = localray.m_Dir.Length();
			localray.m_Dir /= len;

			origin = localray.m_Origin;
			origin.x += (MAX_DIM_X / 2 + 0.5f) * BLOCK_FSIZE;
			origin.z += (MAX_DIM_Z / 2 + 0.5f) * BLOCK_FSIZE;
			origin.y += 0;
			int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
			if (m > 0 && m_JointCreateParams.size() > 0)
			{
				Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
				origin += offset;
			}

			origin.x /= 100.0f;
			origin.y /= 100.0f;
			origin.z /= 100.0f;

			Vector3f& dir = localray.m_Dir;
			Vector3f origin_c2 = origin * float(BLOCK_SIZE);
			//cube containing origin point
			int x = (int)floor(origin.x);
			int y = (int)floor(origin.y);
			int z = (int)floor(origin.z);

			//怪物和载具的射线检测调试
			/*if (!checkBlockCollide) y -= 1;*/

			//break out direction vector
			float dx = dir.x;
			float dy = dir.y;
			float dz = dir.z;

			//direction to increment x, y, z when stepping
			int stepX = dx > 0 ? 1 : (dx < 0 ? -1 : 0);
			int stepY = dy > 0 ? 1 : (dy < 0 ? -1 : 0);
			int stepZ = dz > 0 ? 1 : (dz < 0 ? -1 : 0);

			float tMaxX = intbound(origin.x, dx);
			float tMaxY = intbound(origin.y, dy);
			float tMaxZ = intbound(origin.z, dz);

			float tDeltaX = stepX / dx;
			float tDeltaY = stepY / dy;
			float tDeltaZ = stepZ / dz;

			DirectionType face = DIR_NOT_INIT;
			bool inter_ret = false;

			WCoord blockorigin = WCoord(WorldPos::GetOriginPos()) / BLOCK_SIZE;

			while (true)
			{
				do
				{
					if (x >= 0 && y >= 0 && z >= 0 && x < MAX_DIM_X && y < MAX_DIM_Y && z < MAX_DIM_Z)
					{
						WCoord blockpos = blockorigin + WCoord(x, y, z);
						VehicleBlock* srcBlock = m_Blocks[x][y][z];
						blockID = srcBlock->m_Block.getResID();
						if (srcBlock->m_GroupIdSelf != groupIdPhy)
						{
							break;
						}
						if (blockID == 0 || srcBlock->m_iCurLife <= 0)
						{
							break;
						}
						BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(blockID);
						IntersectResult inter_result;
						if (blockmtl && blockmtl->m_Def && blockmtl->m_Def->ClickCollide && (!checkBlockCollide || IsBlockCollideWithRay(blockmtl, m_pVehicleWorld, blockpos, origin_c2, dir, &inter_result)))
						{
							WCoord coord = convertWcoord(WCoord(x, y, z));
							Vector3f pos = ray.m_Origin.toVector3();
							float t_ = (float)Sqrt(((float)coord.x - pos.x) * ((float)coord.x - pos.x) + ((float)coord.y - pos.y) * ((float)coord.y - pos.y) + ((float)coord.z - pos.z) * ((float)coord.z - pos.z));
							//t =  (float)Sqrt(((float)x + 0.5 - origin.x)*((float)x + 0.5 - origin.x)+((float)y + 0.5- origin.y)*((float)y + 0.5- origin.y)+((float)z + 0.5 - origin.z)*((float)z + 0.5 - origin.z))*BLOCK_SIZE;
							if (t_ < t && t_ < localray.m_Range)
							{
								_x = x;
								_y = y;
								_z = z;
								t = t_;
								find_block = true;
								result = inter_result;
								goto over_while;
							}
							else
								goto over_while;
						}
						else
						{
							break;
						}
					}
				} while (0);
				// tMaxX stores the t-value at which we cross a cube boundary along the
				// X axis, and similarly for Y and Z. Therefore, choosing the least tMax
				// chooses the closest cube boundary. Only the first case of the four
				// has been commented in detail.
				if (tMaxX < tMaxY)
				{
					if (tMaxX < tMaxZ)
					{
						if (tMaxX > radius) break;
						// Update which cube we are now in.
						x += stepX;
						// Adjust tMaxX to the next X-oriented boundary crossing.
						tMaxX += tDeltaX;
						// Record the normal vector of the cube face we entered.
						face = DIR_NEG_X;
					}
					else
					{
						if (tMaxZ > radius) break;
						z += stepZ;
						tMaxZ += tDeltaZ;
						face = DIR_NEG_Z;
					}
				}
				else
				{
					if (tMaxY < tMaxZ)
					{
						if (tMaxY > radius) break;
						y += stepY;
						tMaxY += tDeltaY;
						face = DIR_NEG_Y;
					}
					else
					{
						// Identical to the second case, repeated for simplicity in
						// the conditionals.
						if (tMaxZ > radius) break;
						z += stepZ;
						tMaxZ += tDeltaZ;
						face = DIR_NEG_Z;
					}
				}
			}
		over_while:;
		}
	}

	return find_block;
}
Vector3f ActorVehicleAssemble::computeChassisAABBDimensions()
{
	Vector3f chassisMin(3000, 3000, 3000);
	Vector3f chassisMax(-3000, -3000, -3000);

	for (UInt32 i = 0; i<m_VehicleCreateMeshes.size(); i++)
	{
		for (UInt32 j = 0; j<m_VehicleCreateMeshes[i].mBoxMeshesChassis.size(); j++)
		{
			const Rainbow::BoxMesh& boxMesh = m_VehicleCreateMeshes[i].mBoxMeshesChassis[j];
			for (UInt32 k = 0; k<boxMesh.mPos.size(); k++)
			{
				chassisMin.x = Min(chassisMin.x, boxMesh.mPos[k].x - boxMesh.mExten[k].x);
				chassisMin.y = Min(chassisMin.y, boxMesh.mPos[k].y - boxMesh.mExten[k].y);
				chassisMin.z = Min(chassisMin.z, boxMesh.mPos[k].z - boxMesh.mExten[k].z);

				chassisMax.x = Max(chassisMax.x, boxMesh.mPos[k].x + boxMesh.mExten[k].x);
				chassisMax.y = Max(chassisMax.y, boxMesh.mPos[k].y + boxMesh.mExten[k].y);
				chassisMax.z = Max(chassisMax.z, boxMesh.mPos[k].z + boxMesh.mExten[k].z);
			}
		}
	}

	Vector3f chassisDims = chassisMax - chassisMin;
	if (chassisDims.x < 0)
	{
		chassisDims = Vector3f::one;

	}
	return chassisDims;
}

void ActorVehicleAssemble::computeWheelWidthAndRadius(const Rainbow::RAWMesh& rawMesh, int dir, float& wheelWidth, float& wheelRadius)
{
	//由车轮mesh计算出半径和宽度
	Vector3f wheelMin(Rainbow::MAX_FLOAT, Rainbow::MAX_FLOAT, Rainbow::MAX_FLOAT);
	Vector3f wheelMax(-Rainbow::MAX_FLOAT, -Rainbow::MAX_FLOAT, -Rainbow::MAX_FLOAT);

	int phyvert = rawMesh.mNbVerts;
	for (UInt32 i = 0; i<(UInt32)phyvert; i++)
	{
		wheelMin.x = Min(wheelMin.x, rawMesh.mVerts[i].x);
		wheelMin.y = Min(wheelMin.y, rawMesh.mVerts[i].y);
		wheelMin.z = Min(wheelMin.z, rawMesh.mVerts[i].z);
		wheelMax.x = Max(wheelMax.x, rawMesh.mVerts[i].x);
		wheelMax.y = Max(wheelMax.y, rawMesh.mVerts[i].y);
		wheelMax.z = Max(wheelMax.z, rawMesh.mVerts[i].z);
	}

	if (dir == DIR_NEG_X || dir == DIR_POS_X)
	{
		wheelWidth = wheelMax.x - wheelMin.x;
	}
	else
	{
		wheelWidth = wheelMax.z - wheelMin.z;
	}

	wheelRadius = (wheelMax.y - wheelMin.y) / 2;
}

int ActorVehicleAssemble::getObjType() const
{
	return OBJ_TYPE_VEHICLE;
}

void ActorVehicleAssemble::tick()
{
	if (!m_pWorld) return;
    OPTICK_EVENT();
	m_pVehicleWorld->tick();
	ClientActor::tick();

	if (m_pWorld && g_pPlayerCtrl && m_EnableWaterMask && m_WaterMaskMat != nullptr)
	{
		CheckWaterMaskOnWater();
		//当前角色在水面高度下边,而且包围盒在船内,water mask的深度测试去掉,不然会有一些显示异常




		ActorLocoMotion* locoMotion = g_pPlayerCtrl->getLocoMotion();
		bool onWaterMask = false;
		if (locoMotion != nullptr)
		{
			//是否为第一人称
			CameraControlMode  cameraMode = g_pPlayerCtrl->getCamera()->getMode();
			WCoord pos(0, 0, 0);// locoMotion->getPosition();
			if (cameraMode == CAMERA_FPS)
			{
				pos = g_pPlayerCtrl->getCamera()->getEyePos();
			}
			else 
			{
				pos = locoMotion->getPosition();
			}
			Rainbow::BoxBound boxBound;
			boxBound.setCenterExtension(pos.toVector3(), Vector3f::one);
			onWaterMask = this->CheckOnWaterMask(boxBound);
			//WCoord sectionPos = CoordDivSection(pos);
			//Section* pSection = m_pWorld->getSectionBySCoord(sectionPos.x, sectionPos.y, sectionPos.z);
			//if (pSection != nullptr && pSection->getWaterHeight() > 0) 
			//{
			//	if (pos.y < pSection->getWaterHeight()) 
			//	{
			//		CollideAABB collideAABB;
			//		g_pPlayerCtrl->getCollideBox(collideAABB);
			//		Vector3f max = collideAABB.maxPos().toVector3();
			//		Vector3f min = collideAABB.minPos().toVector3();
			//		Rainbow::BoxBound boxBound = Rainbow::BoxBound(min, max);
			//		
			//	}
			//}
		}
		m_WaterMaskMat->SetDepthFunc(onWaterMask ? DepthFunc::kDepthFuncAlways : DepthFunc::kDepthFuncNear);
	}
	if (getActorMgr())
	{
		std::vector<ClientPlayer*> players;
		getActorMgr()->selectNearAllPlayers(players, getPosition(), 30 * BLOCK_SIZE);
		for (size_t i = 0; i < players.size(); i++)
		{
			ClientPlayer* player = players[i];
			if (!player) continue;
			ActorLocoMotion* locoMotion = player->getLocoMotion();
			if (locoMotion)
			{
				CollideAABB box;
				player->getCollideBox(box);
				Vector3f max = box.maxPos().toVector3();
				Vector3f min = box.minPos().toVector3();
				Rainbow::BoxBound bound(min, max);
				if (CheckOnWaterMask(bound))
					locoMotion->m_IsInBoatWaterMask = true;
				else
					locoMotion->m_IsInBoatWaterMask = false;
			}
		}
	}

	if (m_isAfterTick == false)
		afterActorTick();

}

void ActorVehicleAssemble::afterActorTick()
{
	if (!m_pWorld) return;
	m_AttribUpdateCount++;
	m_CreateModelCount++;
	m_isAfterTick = true;
	if (getVehicleWorld()) {
		VehicleBlockTickMgr* pblocktickmgr = getVehicleWorld()->getVehicleBlockTickMgr();
		if (pblocktickmgr) pblocktickmgr->tick();

		VehicleContainerMgr* pcontainermgr = dynamic_cast<VehicleContainerMgr*>(getVehicleWorld()->getContainerMgr());
		if (pcontainermgr)pcontainermgr->updateTick();

	}

	//过热消耗组件tick函数执行
	vehicleDecoratorTick();

	int count = 0;
	if (m_CreateModelCount % 20 == 0)
	{
		auto iter = m_sBlockID.begin();
		while (iter != m_sBlockID.end())
		{
			BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(*iter);
			if (mtl && (mtl->hasGeom() && mtl->getGeom() != g_BlockMtlMgr.getGeomTemplate("block")))
			{
				iter = m_sBlockID.erase(iter);
				count++;
			}
			else
				iter++;
		}
		m_CreateModelCount = 0;
	}

	//bool must_re_create = false;
	//如果碰撞点不为空（处理载具和非生物之间的碰撞）
	if (!m_ContactDescs.empty())
	{
		if (!m_pWorld->isRemoteMode())
		{
			std::vector<WCoord> blockpos;
			//创建掉落刚体
			std::map<std::string, ContactDesc>::iterator i = m_ContactDescs.begin();
			for (; i != m_ContactDescs.end(); i++)
			{
				ContactDesc contactDesc = i->second;
				blockpos.push_back(contactDesc.m_BlockPos);
				WCoord blockpos = contactDesc.m_BlockPos;
				contactDesc.m_BlockPos = convertWcoord(contactDesc.m_BlockPos);
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(contactDesc.m_BlockID);
				if (physicsPartsDef && physicsPartsDef->IsCore)
				{
					setNeedClear(true);
					/*爆炸效果*/
					//驾驶座的玩家位置和爆炸点位置相同，代码逻辑上判断玩家不会受到伤害，所以这里先把爆炸位置偏移1
					m_pWorld->createExplosion(NULL, WCoord(contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y - 1, contactDesc.m_BlockPos.z), 3);
					if (physicsPartsDef->EffectFunctions.size())
					{
						for (int j = 0; j<(int)physicsPartsDef->EffectFunctions.size(); j++)
						{
							if (physicsPartsDef->EffectFunctions[j].func_id == 3 && physicsPartsDef->EffectFunctions[j].func.seatfun.SeatType == 1)
							{
								//强制死亡
								for (int c = 0; c<(int)m_OtherRiddens.size(); c++)
								{
									if (m_OtherRiddens[c])
									{
										ClientActor* actor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OtherRiddens[c]);
										if (actor)
										{
											ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
											if (player)
											{
												player->kill();
											}
										}
									}
								}
							}
						}
					}
				}

				if (contactDesc.m_BlockID)
				{
					int t = 0;
					for (int t = 0; t<(int)m_WheelCoord.size(); t++)
					{
						if (m_WheelCoord[t].wcoord == blockpos)
						{
							VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
							if (loc && t < (int)loc->m_WheelUpdatePos.size())
							{
								Rainbow::Quaternionf rot_ = EulerToQuaternionf(Vector3f(0, kHalfPI, 0));
								Rainbow::Quaternionf rot = loc->m_WheelUpdatePos[t].m_RotateQuat;
								// 新引擎的四元数乘法需要交换顺序
								rot_ = rot * rot_;
								ActorChassis* chassis = ActorChassis::create(this->m_pWorld, contactDesc.m_BlockID, contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y, contactDesc.m_BlockPos.z, 0, 0);
								if (chassis)
								{
									PhysicsLocoMotionComponent* loc = static_cast<PhysicsLocoMotionComponent *>(chassis->getLocoMotion());
									if (loc)
									{
										loc->setRotation(rot_);
									}
								}
								break;
							}
						}
					}

					if (t == m_WheelCoord.size() && contactDesc.m_bIsDrop)
						ActorChassis::create(this->m_pWorld, contactDesc.m_BlockID, contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y, contactDesc.m_BlockPos.z, 0, 0);
				}

				//播放特效
				if (physicsPartsDef && physicsPartsDef->IsEnergy)
				{
					Quaternionf quat = AxisAngleToQuaternionf(Vector3f::zAxis, kHalfPI);
					Vector3f euler = QuaternionToEulerAngle(quat);
					m_pWorld->getEffectMgr()->playParticleEffectAsync(GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.energy_destroy_effect.c_str(), WCoord(contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y, contactDesc.m_BlockPos.z), 40, euler.x, euler.y, true, 0);
					m_pWorld->getEffectMgr()->playSoundAtActor(this, GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.energy_destroy_sound.c_str(), 1.0f, 1.0f);
				}
				else
				{
					if (contactDesc.m_bdestroy)
					{
						m_pWorld->getEffectMgr()->playBlockDestroyEffect(0, WCoord(contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y, contactDesc.m_BlockPos.z), DIR_NEG_X, 40, contactDesc.m_BlockID);
						//播放音效
						int blockid = contactDesc.m_BlockID;
						if (blockid >= SOC_BLOCKID_MAX)
							blockid = 2000;
						const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);
						if (blockdef && !blockdef->DigSound.empty()) m_pWorld->getEffectMgr()->playSound(WCoord(contactDesc.m_BlockPos.x, contactDesc.m_BlockPos.y, contactDesc.m_BlockPos.z), blockdef->DigSound.c_str(), GSOUND_DESTROY);
					}
				}

				//破坏座位逻辑处理
				if (physicsPartsDef)
				{
					//获取配表信息
					PhysicsPartsDef::EffectFunctionsDef* seatFun = NULL;
					PhysicsPartsDef::EffectFunctionsDef* thrusterFun = NULL;
					for (int i = 0; i<(int)physicsPartsDef->EffectFunctions.size(); i++)
					{
						PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
						if (functiondef->func_id == 3) //座位
						{
							seatFun = functiondef;
						}
						else if (functiondef->func_id == 9) //推进器
						{
							thrusterFun = functiondef;
						}
					}

					//座位
					if (seatFun)
					{
						//驾驶座
						if (seatFun->func.seatfun.IsControl == 1)
						{
							//驾驶座都不存在，不能再重建载具
							m_destoryDriverSeat = true;
						}
						//乘客座
						else if (seatFun->func.seatfun.IsControl == 0)
						{
							WCoord start = WCoord(0, 0, 0);
							WCoord centerPos = WCoord(start.x + MAX_DIM_X / 2, start.y, start.z + MAX_DIM_Z / 2);
							std::vector<WCoord>::iterator iterOtherSeatPos = std::find(m_OtherSeatPos.begin(), m_OtherSeatPos.end(), (blockpos - centerPos)*BLOCK_SIZE);

							if (iterOtherSeatPos != m_OtherSeatPos.end())
							{
								int i = iterOtherSeatPos - m_OtherSeatPos.begin();
								std::vector<int>::iterator iterOtherSeatDir = m_OtherSeatDir.begin() + i;
								std::vector<WORLD_ID>::iterator iterOtherRiddens = m_OtherRiddens.begin() + i;

								m_OtherSeatPos.erase(iterOtherSeatPos);
								m_OtherSeatDir.erase(iterOtherSeatDir);

								if (m_OtherRiddens[i])
								{
									ClientActor* actorride = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OtherRiddens[i]);
									if (actorride)
									{
										ClientPlayer *player = dynamic_cast<ClientPlayer *>(actorride);
										if (player)
										{
											player->mountActor(NULL);
										}
									}
								}

								m_OtherRiddens.erase(iterOtherRiddens);
							}
						}
					}
				}
			}
			syncBlockDestroy(blockpos);
			//if (blockpos.size())
			//must_re_create = true;
			resetEntityPos(0);
			count = 0;
		}
		m_ContactDescs.clear();
	}
	//发送数据改变到客机
	sendChunkUpdateToPlayers();

	if ((count > 0) && (m_mustReCreatePhysics == false))
	{
		//重新创建载具刚体
		m_createCount = 10;
		m_mustReCreatePhysics = true;
	}

	if (m_mustReCreatePhysics && (m_createCount-- <= 0))
	{
		reCreateVehicleAssemble();
	}

	//速度同步、载具乘客信息修正,爪子抓取的判断
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		if (m_AttribUpdateCount%AttribUpdateInterval == 0)
		{
			float speed = getCurActualSpeed();
			m_CurSpeedShow = getCurSpeedShow();
			m_CurActualSpeed = speed > 0.0f ? floor(speed / 100.0f + 0.5f) : ceil(speed / 100.0f - 0.5f);
			//m_EngineRotationSpeed = getEngineRotationSpeed();
			if (m_CurSpeedShow != m_LastSpeedShow)
			{
				syncVehicleSpeed((int)m_CurActualSpeed, (int)m_CurSpeedShow);
			}
			//引擎转速现在没用到，先注释了
			//syncEngintRotationSpeed(m_EngineRotationSpeed);

			for (int i = 0; i < getNumRiddenPos(); i++)
			{
				ClientActor *ridden = getRiddenByActor(i);
				if (ridden)
				{
					auto riddenComp = ridden->sureRiddenComponent();
					if (riddenComp && !riddenComp->checkRidingByActorObjId(getObjId()))
					{
						if (riddenComp->getRidingActorObjId() == 0)
						{
							setRiddenByActor(NULL, i);
						}
						else {
							auto RidComp = sureRiddenComponent();
							if (RidComp)
								RidComp->setRiddenByActor_Base(NULL, i);
						}
					}
				}
			}

		}

		for (int i = 0; i<(int)m_ClawBlocks.size(); i++)
		{
			if (m_BindVehilcIDs.find((m_ClawBlocks[i].x << 10) + (m_ClawBlocks[i].y << 5) + m_ClawBlocks[i].z) != m_BindVehilcIDs.end())
			{
				continue;
			}

			Block block = getBlock(m_ClawBlocks[i]);
			if (block.getResID() == BLOCK_CLAW && (block.getData() & 8))
			{
				WCoord pos = convertWcoord(m_ClawBlocks[i]);
				Rainbow::Vector3f dir;
				int groupId = 0;
				dir = convertRealDir(m_ClawBlocks[i], block.getData() & 7, groupId);
				MINIW::WorldRay ray;
				dir  = MINIW::Normalize(dir);
				ray.m_Origin = pos.toWorldPos();
				ray.m_Dir = dir;
				ray.m_Range = BLOCK_FSIZE * 8;
				//不要射到自己
				ActorExcludes excludes;
				excludes.addActor(this);

				IntersectResult inter_result1, inter_result2;
				IntersectResult *presult;
				//后面应该加入team的判断
				int excludeTeam = 0;
				ActorLiving*living = dynamic_cast<ActorLiving*>(getShootingActor());
				std::vector<IClientActor*> actors;
				inter_result2.actors = actors;
				WorldPickResult intertype = m_pWorld->pickAll(ray, &inter_result2, excludes, PICK_METHOD_SOLID, excludeTeam);
				presult = &inter_result2;
				auto presultactor = static_cast<ClientActor*>(presult->actor);
				if (presultactor && presult->collide_t <= (2 * BLOCK_FSIZE)) //actor
				{
					if (presultactor->getObjType() != OBJ_TYPE_VEHICLE && presultactor->getObjType() != OBJ_TYPE_ROLE)
					{
						m_BindVehilcIDs[(m_ClawBlocks[i].x << 10) + (m_ClawBlocks[i].y << 5) + m_ClawBlocks[i].z] = presult->actor->getObjId();
						auto functionWrapper = presultactor->getFuncWrapper();
						if (functionWrapper)
						{
							functionWrapper->setCanMove(false);
						}
						auto vehicleComponent = presultactor->getActorBindVehicle();
						if (vehicleComponent)
						{
							vehicleComponent->Bind(getObjId(), m_ClawBlocks[i], true);
						}
						break;
					}
					else if (presultactor->getObjType() == OBJ_TYPE_VEHICLE)
					{
						//checkClawAssemble(ActorVehicleAssemble* vehicle);
						ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(presultactor);
						if (vehicle && vehicle->m_FatherVehilcID == 0 && getObjId() != vehicle->getObjId() && !vehicle->isSonVehicle(getObjId()) && !isSonVehicle(vehicle->getObjId()))
						{
							vehicle->m_FatherVehilcID = getObjId();
							vehicle->m_FatherVehilcPos = WCoord(m_ClawBlocks[i].x, m_ClawBlocks[i].y, m_ClawBlocks[i].z);
							vehicle->detachAllPhysActor();
							m_BindVehilcIDs[(m_ClawBlocks[i].x << 10) + (m_ClawBlocks[i].y << 5) + m_ClawBlocks[i].z] = vehicle->getObjId();

							VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
							if (loc && loc->m_PhysActor)
								loc->m_PhysActor->SetLinearVelocity(0.0f);
							//detachAllPhysActor();
							break;
						}
					}
				}
			}
		}
		auto vehicle_ = m_BindVehilcIDs.begin();
		while (vehicle_ != m_BindVehilcIDs.end())
		{		
			ClientActor* actor = m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(vehicle_->second);
			if (!actor)
			{
				vehicle_ = m_BindVehilcIDs.erase(vehicle_);
			}
			else
				vehicle_++;
		}
	}
	//LOG_INFO("ACTUAL SPEED: %d", m_CurActualSpeed);

	int iEffectLevel = 0;
	//float soundVolum = 0.0f;
	if (m_CurSpeedShow > GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.speed4 / 100) {
		iEffectLevel = 3;
		//soundVolum = 1.0f;
	}
	else if (m_CurSpeedShow > GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.speed3 / 100) {
		iEffectLevel = 2;
		//soundVolum = 0.6f;
	}
	else if (m_CurSpeedShow > GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.speed2 / 100) {
		iEffectLevel = 1;
		//soundVolum = 0.2f;
	}

	if (iEffectLevel > 0 && !m_pWorld->isRemoteMode() && !isInAir())
	{
		char effectPath[128] = { 0 };
		sprintf(effectPath, "particles/phy_WheelTravel%d.ent", iEffectLevel);
		if (m_VehicleCreateParams[0].mWheelParams.size())
		{
			Rainbow::WheelParam wheelParam = m_VehicleCreateParams[0].mWheelParams[0];
			for (int i = 0; i < (int)m_EntitiesWheel.size(); i++)
			{
				WorldPos effectPos = m_EntitiesWheel[i]->GetPosition();
				effectPos = effectPos + WorldPos(0, (Rainbow::WPOS_T)(-10 * wheelParam.mRadius + 10 * BLOCK_SIZE*0.1f), 0);
				if (m_effectTick <= 0)
					m_pWorld->getEffectMgr()->playParticleEffectAsync(effectPath, effectPos, 20);
			}
		}
	}
	//车轮音效播放
	//用于音效播放控制的速度是换算过的，和界面上的速度显示同步，不是实际速度值
	//float WheelSound_threshold = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.wheeltravel_sound_threshold / 100.0f;
	if (m_CurSpeedShow > 0)
	{
		playWheelLoopSnd(MOVE_START);
	}
	else if (m_CurSpeedShow <= 0)
	{
		playWheelLoopSnd(MOVE_END);
	}
	RiddenComponent *RidComp = NULL;
	if (g_pPlayerCtrl)
		RidComp = g_pPlayerCtrl->getRiddenComponent();
	//引擎音效播放
	if (/*m_RiddenByActor&&m_RiddenByActor > 0*/getRiddenByActorID() > 0 && getEngineNum()>0)
	{
		if (g_pPlayerCtrl&&RidComp && RidComp->isVehicleDriver())
		{
			VEHICLE_ENGINESOUND_STATE tempEngineState = m_vehicleSnd.engineState;
			VEHICLE_ENGINESOUND_STATE curEngineState = m_vehicleSnd.engineState;
			bool accel = true;
			bool brake = false;
			if (GetClientInfoProxy()->isPC())
			{
				accel = g_pPlayerCtrl->m_VehicleControlInputs->getAccelKeyPressed();
				brake = g_pPlayerCtrl->m_VehicleControlInputs->getBrakeKeyPressed();
			}
			else if (GetClientInfoProxy()->isMobile())
			{
				accel = g_pPlayerCtrl->m_VehicleControlInputs->getAccel();
				brake = g_pPlayerCtrl->m_VehicleControlInputs->getBrake();
			}

			if ((accel || brake) && m_hasfuel)
			{
				int dir = 1;
				m_vehicleSnd.workdelayCount = (int)GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig.engine_sound_delaytime * 20;
				//车头方向为pos时，前进键使速度为正；neg时，为负
				int vehicleDir = getHeadDir();
				if (vehicleDir == DIR_NEG_X || vehicleDir == DIR_NEG_Z)
					dir = -1;
				else if (vehicleDir == DIR_POS_X || vehicleDir == DIR_POS_Z)
					dir = 1;

				if (accel && (m_CurActualSpeed*dir < 0) && curEngineState != BRAKE_PROCESS)
				{
					tempEngineState = BRAKE_START;
				}
				else if (brake && (m_CurActualSpeed*dir) > 0 && curEngineState != BRAKE_PROCESS)
				{
					//LOG_INFO("brake%d", m_CurActualSpeed);
					tempEngineState = BRAKE_START;
				}
				else if (accel && (m_CurActualSpeed*dir >= 0) || brake && (m_CurActualSpeed*dir <= 0))
				{
					if (curEngineState != WORK_START)
						tempEngineState = WORK_START;
				}
			}
			else
			{
				if (curEngineState == WORK_START && m_vehicleSnd.workdelayCount > 0 && getCurSpeedShow()>(GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig.engine_sound_delayspeed / 100))
				{
					m_vehicleSnd.workdelayCount--;
				}
				else if (curEngineState != IGNITION_START)
				{
					tempEngineState = IGNITION_START;
					m_vehicleSnd.workdelayCount = (int)GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig.engine_sound_delaytime * 20;
				}
			}
			if (tempEngineState != curEngineState)
			{
				syncEngineState(tempEngineState);
			}

		}
		playEngineLoopSnd(m_vehicleSnd.engineState);
	}
	else
	{
		playEngineLoopSnd(ENGINE_NONE);
	}

	//消耗统计和引擎消耗
	m_bIsUnlimitEnergy = false;  //如果是无限引擎，不需要耗油
	updateEnergyCost();

	//设置载具的最大速度（避免推进器导致载具速度太快）
	if (!m_pWorld->isRemoteMode())
	{
		VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());

		if (loc->m_PhysActor && loc->m_PhysActor)
		{
			RigidBaseActor* body = loc->m_PhysActor;
			if (m_nDynamicThrusterCount || m_nDynamicSThrusterCount || m_nDynamicBoatThrusterCount )
			{
				body->SetMaxLinearVelocity((float)(GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.vehicle_liner_max_speed + m_nDynamicThrusterCount * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.vehicle_liner_add_speed + m_nDynamicSThrusterCount * GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.vehicle_s_liner_add_speed));
			}
		}
	}

	Vector4f lightparam(0, 0, 0, 0);
	WCoord center = getMassCenterWcoord();
	center.y += getLocoMotion()->m_BoundHeight / 2 + 100;
	if (m_pWorld)
	{
		Vector4f lightparams[3] = {};
		CollideAABB box;
		WCoord start, dims;
		getVehicleColliderBox(start, dims);
		box.dim = dims;
		box.pos = start;
		WCoord wcoord_ = CoordDivBlock(center - dims / 2);
		m_pWorld->getBlockLightValue2_Air(lightparams[0].x, lightparams[0].y, wcoord_);
		m_pWorld->getBlockLightValue2_Air(lightparams[1].x, lightparams[1].y, CoordDivBlock(center + dims / 2));
		m_pWorld->getBlockLightValue2_Air(lightparams[2].x, lightparams[2].y, CoordDivBlock(center));
		lightparam.x = max(max(lightparams[0].x, lightparams[1].x), lightparams[2].x);
		lightparam.y = max(max(lightparams[0].y, lightparams[1].y), lightparams[2].y);
	}

	for (size_t i = 0; i<m_EntitiesChassis.size(); i++)
	{
		MechaMeshObject*meshObj = dynamic_cast<MechaMeshObject*>(m_EntitiesChassis[i]->GetBindObject(0));
		if (meshObj)
		{
			MechaSectionMesh* mesh = dynamic_cast<MechaSectionMesh*>(meshObj->GetSectionMesh());
			mesh->setLight(lightparam.x, lightparam.y);
		}
	}

	for (int i = 0; i<(int)m_EntitiesWheel.size(); i++)
	{
		MechaMeshObject* meshObj = dynamic_cast<MechaMeshObject*>(m_EntitiesWheel[i]->GetBindObject(0));
		if (meshObj)
		{
			MechaSectionMesh* mesh = dynamic_cast<MechaSectionMesh*>(meshObj->GetSectionMesh());

			mesh->setLight(lightparam.x, lightparam.y);
		}
	}

	auto iter = m_EntitiesChassisEx.begin();
	while (iter != m_EntitiesChassisEx.end())
	{
		MechaMeshObject* meshObj = dynamic_cast<MechaMeshObject*>(iter->second->GetBindObject(0));
		if (meshObj)
		{
			MechaSectionMesh* mesh = dynamic_cast<MechaSectionMesh*>(meshObj->GetSectionMesh());
			mesh->setLight(lightparam.x, lightparam.y);
		}
		iter++;
	}

	//重置tickCount
	if (m_AttribUpdateCount % AttribUpdateInterval == 0)m_AttribUpdateCount = 0;
	m_LastSpeedShow = m_CurSpeedShow;

	// 查看主角是否手持连线钳 在16格范围内
	if (!checkPlayerIsInRange(1600))
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_clearVehicleEndLinkTick",
			SandboxContext(nullptr)
			.SetData_String("objId", to_string(getObjId())));
	}

	//碰撞优化，tick清零允许处理碰撞逻辑
	m_collideTick = 0;
	//特效播放优化，tick清零允许播放特效
	if (m_effectTick > 0)
		m_effectTick--;
	else
		m_effectTick = 20;

	livingHPtick();
	mobHPTick();
}

void ActorVehicleAssemble::resetEntityPos(float dtime)
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc->m_hasPhysCar)
	{
		for (int i = 0; i < (int)m_EntitiesChassis.size(); i++)
		{
			if (loc && i < (int)loc->m_ChassisUpdatePos.size()) {
				//Rainbow::WorldPos updatePos = Rainbow::WorldPos(loc->m_UpdatePos.x+m_RawMeshesChassis[i].mPos.p.x*10, loc->m_UpdatePos.y+m_RawMeshesChassis[i].mPos.p.y*10, loc->m_UpdatePos.z+m_RawMeshesChassis[i].mPos.p.z*10);
				m_EntitiesChassis[i]->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.z));
				m_EntitiesChassis[i]->SetRotation(loc->m_ChassisUpdatePos[i].m_UpdateRot);
				m_EntitiesChassis[i]->Tick(dtime);

			}
		}
		for (int i = 0; i<(int)m_EntitiesWheel.size(); i++)
		{
			if (loc && i < (int)loc->m_WheelUpdatePos.size())
			{
				m_EntitiesWheel[i]->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_WheelUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_WheelUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_WheelUpdatePos[i].m_PUpdatePos.z));
				m_EntitiesWheel[i]->SetRotation(loc->m_WheelUpdatePos[i].m_UpdateRot);
			}
			m_EntitiesWheel[i]->Tick(dtime);
		}

		if (m_EnableWaterMask) 
		{
			for (int i = 0; i < (int)m_WaterMaskEntites.size(); i++)
			{
				if (loc && i < (int)loc->m_ChassisUpdatePos.size()) {
					m_WaterMaskEntites[i]->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.z));
					m_WaterMaskEntites[i]->SetRotation(loc->m_ChassisUpdatePos[i].m_UpdateRot);
					m_WaterMaskEntites[i]->Tick(dtime);
				}
			}
		}
	}
	else if (loc->m_ChassisUpdatePos.size() == m_EntitiesChassis.size())
	{
		for (int i = 0; i<(int)m_EntitiesChassis.size(); i++)
		{
			if (loc && i < (int)loc->m_ChassisUpdatePos.size())
			{
				m_EntitiesChassis[i]->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.z));
				m_EntitiesChassis[i]->SetRotation(loc->m_ChassisUpdatePos[i].m_UpdateRot);
				m_EntitiesChassis[i]->Tick(dtime);
			}
		}
		if (this->m_EnableWaterMask)
		{
			for (int i = 0; i < (int)m_WaterMaskEntites.size(); i++)
			{
				if (loc && i < (int)loc->m_ChassisUpdatePos.size())
				{
					m_WaterMaskEntites[i]->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.z));
					m_WaterMaskEntites[i]->SetRotation(loc->m_ChassisUpdatePos[i].m_UpdateRot);
					m_WaterMaskEntites[i]->Tick(dtime);
				}
			}
		}

	}
	auto iter = m_EntitiesChassisEx.begin();
	while (iter != m_EntitiesChassisEx.end())
	{
		VehicleBlock *srcBlock = m_Blocks[iter->first >> 10][(iter->first >> 5) & 0x1f][iter->first & 0x1f];
		int i = loc->getPhysIndexWithGroupId(srcBlock->m_GroupIdSelf);
		if (loc && i < (int)loc->m_ChassisUpdatePos.size()) {
			//Rainbow::WorldPos updatePos = Rainbow::WorldPos(loc->m_UpdatePos.x+m_RawMeshesChassis[i].mPos.p.x*10, loc->m_UpdatePos.y+m_RawMeshesChassis[i].mPos.p.y*10, loc->m_UpdatePos.z+m_RawMeshesChassis[i].mPos.p.z*10);
			iter->second->SetPosition(WorldPos((Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.x, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.y, (Rainbow::WPOS_T)loc->m_ChassisUpdatePos[i].m_PUpdatePos.z));
			iter->second->SetRotation(loc->m_ChassisUpdatePos[i].m_UpdateRot);
			iter->second->Tick(dtime);
		}
		iter++;
	}
}

bool ActorVehicleAssemble::hasThrusterActive(int dir)
{
	for (int i = 0; i < (int)m_Overheats.size(); i++)
	{
		DynamicOverheat* dynamicOverheat = dynamic_cast<DynamicOverheat*>(m_Overheats[i]);

		if (dynamicOverheat)
		{
			PhysicsPartsDef::EffectFunctionsDef* funcDef = dynamicOverheat->getFuncDef();
			if (funcDef && funcDef->func_id == 6)
			{
				WCoord blockpos = dynamicOverheat->getPos();
				VehicleBlock* vehicleBlock = getVehicleBlock(blockpos);
				if (vehicleBlock && IsThrusterBlockID(vehicleBlock->m_Block.getResID()))
				{
					int thrusterData = vehicleBlock->m_Block.getData();
					bool isCanWork = checkifOverheatPartCanWork(blockpos);
					bool active = thrusterData & 8;
					if (active && isCanWork)
						return true;
				}
			}
		}
	}
	return false;
}

void ActorVehicleAssemble::update(float dtime)
{
	ClientActor::update(dtime);
	if (getVehicleWorld())
	{
		VehicleContainerMgr* pcontainermgr = dynamic_cast<VehicleContainerMgr*>(getVehicleWorld()->getContainerMgr());
		if (pcontainermgr)pcontainermgr->updateDisplay(dtime);
	}
	resetEntityPos(dtime);

	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());

	//显示爪子的特效
#ifndef IWORLD_SERVER_BUILD
	for (int i = 0; i < (int)m_ClawBlocks.size(); i++)
	{
		Block block = getBlock(m_ClawBlocks[i]);
		if (block.getResID() == BLOCK_CLAW && (block.getData() & 8))
		{
			if (i < (int)m_ClawEffects.size())
			{
				WCoord clawPos(0, 0, 0);
				int clawdir = ((block.getData() & 7) % 6);
				clawPos = convertWcoord(Rainbow::Vector3f(
					(float)(m_ClawBlocks[i].x*BLOCK_SIZE),
					(float)(m_ClawBlocks[i].y*BLOCK_SIZE + BLOCK_SIZE / 2),
					(float)(m_ClawBlocks[i].z*BLOCK_SIZE)), true);
				Rainbow::Quaternionf quatTarget;
				Rainbow::Quaternionf quatRotate;
				if (loc->m_ChassisUpdatePos.size() > 0)
				{
					VehicleBlock* vehicleBlock = m_Blocks[m_ClawBlocks[i].x][m_ClawBlocks[i].y][m_ClawBlocks[i].z];
					if (vehicleBlock->m_GroupIdSelf < (int)loc->m_ChassisUpdatePos.size())
						quatRotate = loc->m_ChassisUpdatePos[vehicleBlock->m_GroupIdSelf].m_UpdateRot;
					else
						quatRotate = loc->m_ChassisUpdatePos[0].m_UpdateRot;
				}
				Rainbow::Vector3f vecRotate = QuaternionToEuler(quatRotate);
				if (DIR_POS_Z == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(0, 0, 0));
				else if (DIR_NEG_Z == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(0, kOnePI, 0));
				else if (DIR_NEG_X == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(0, -kHalfPI, 0));
				else if (DIR_POS_X == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(0, kHalfPI, 0));
				else if (DIR_POS_Y == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(-kHalfPI, 0, 0));
				else if (DIR_NEG_Y == clawdir)
					quatTarget = EulerToQuaternionf(Vector3f(kHalfPI, 0, 0));
				// 新引擎的四元数乘法需要交换顺序
				Rainbow::Quaternionf quatRotate_ = quatRotate * quatTarget;

				if (m_ClawEffects[i] == NULL)
				{
					char effectPath[128] = { 0 };
					sprintf(effectPath, "particles/absorb_claws.ent");
					EffectParticle *pt = ENG_NEW(EffectParticle)(m_pWorld, effectPath, clawPos, 0x7fffffff);
					m_ClawEffects[i] = pt;
				}
				// 播放特效
				m_ClawEffects[i]->setPosition(
					(float)clawPos.x,
					(float)clawPos.y,
					(float)clawPos.z);
				m_ClawEffects[i]->setRotation(quatRotate_);
				m_ClawEffects[i]->update(dtime);
				m_ClawEffects[i]->show(true);
				m_ClawEffects[i]->setLoop(true);
			}
		}
		else
		{
			if (i < (int)m_ClawEffects.size() && m_ClawEffects[i])
			{
				m_ClawEffects[i]->show(false);
			}
		}
	}
	RenderCurve();
	if (isRudder())//判断人物是否还能够在船舵上
	{
		auto actor = getRiddenByActor();
		if (actor)
		{
			auto seatPos = getRiddenBindSeatPos(actor);
			Block* srcBlock = &m_Blocks[seatPos.x][seatPos.y][seatPos.z]->m_Block;
			if (isFunction(srcBlock->getResID(), SEATFUN))
			{
				int dir = srcBlock->getData() % 4;
				WCoord standOffset(0, 0, 0);

				if (dir == DIR_POS_Z)
				{
					standOffset.z = -1;
				}
				else if (dir == DIR_NEG_Z)
				{
					standOffset.z = 1;
				}
				else if (dir == DIR_POS_X)
				{
					standOffset.x = -1;
				}
				else if (dir == DIR_NEG_X)
				{
					standOffset.x = 1;
				}
				int x1 = seatPos.x + standOffset.x;
				int y1 = seatPos.y + standOffset.y;
				int z1 = seatPos.z + standOffset.z;
				if (x1 < MAX_DIM_X && (y1+1) < MAX_DIM_Y && z1 < MAX_DIM_Z)
				{
					Block* standBlock = &m_Blocks[x1][y1][z1]->m_Block;
					Block* standLandBlock = &m_Blocks[x1][y1 - 1][z1]->m_Block;
					Block* standHeadBlock = &m_Blocks[x1][y1 + 1][z1]->m_Block;
					Block* rudderLandBlock = &m_Blocks[seatPos.x][seatPos.y - 1][seatPos.z]->m_Block;
					if (standBlock->isEmpty() && standHeadBlock->isEmpty() && !standLandBlock->isEmpty() && !rudderLandBlock->isEmpty())
					{
					}
					else
					{
						auto player = dynamic_cast<PlayerControl*>(actor);
						if (player)
						{
							player->dismountActor();
						}
					}
				}
			}

		}
	}

#endif

	/*	if (getVehicleWorld())
	{
		VehicleContainerMgr* pcontainermgr = dynamic_cast<VehicleContainerMgr*>(getVehicleWorld()->getContainerMgr());
		if (pcontainermgr)pcontainermgr->updateDisplay(dtime);
	}*/
	//#endif
}

void ActorVehicleAssemble::enterWorld(World *pworld)
{
	pworld->setVehicleAssembleNum(pworld->getVehicleAssembleNum() + 1);
	ClientActor::enterWorld(pworld);
	LOG_INFO("ActorVehicleAssemble::enterWorld:%p", this);

	if (m_pVehicleWorld == NULL)
	{
		m_pVehicleWorld = ENG_NEW(VehicleWorld)(GetWorldManagerPtr());
		m_pVehicleWorld->init(pworld, this);
	}
	if (m_pVehicleWorld && !m_pVehicleWorld->getInit())
	{
		VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		if (containermgr)
		{
			for (int i = 0; i < (int)m_WorldContainer.size(); i++)
			{
				containermgr->spawnContainer(m_WorldContainer[i]);
			}
		}
	}
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	loc->checkPhysWorld();
	if (m_bHaveChassisPos)
	{
		loc->m_Position = m_ChassisPos;
	}
	if (getRiddenByActorID() > 0 && getRiddenByActor() == g_pPlayerCtrl)
		loc->setFocus();
	pworld->addSpecialUnit(this);
	m_WorldContainer.clear();

	if (!m_pWorld->isRemoteMode())
	{
		m_mustReCreatePhysics = true;
		m_createCount = 3;
		create(pworld, false);
	}
	else
	{
		reCreateVehicleAssemble();
	}
	onEnterOrLeaveWorld(true);
	if (m_pVehicleWorld)
		m_pVehicleWorld->setInit(true);
}

std::vector<WorldContainer*> ActorVehicleAssemble::getContainers(bool isCopy)
{
	if (isCopy)
	{
		return m_WorldContainer;
	}
	std::vector<WorldContainer *> chunkcontainers;
	if (m_pVehicleWorld)
	{
		VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		if (containermgr)
		{
			containermgr->getContainersVec(chunkcontainers);
		}
	}
	return chunkcontainers;
}

void ActorVehicleAssemble::leaveWorld(bool keep_inchunk)
{
	if (m_pWorld&& m_pWorld->getVehicleAssembleNum() > 0)
		m_pWorld->setVehicleAssembleNum(m_pWorld->getVehicleAssembleNum() - 1);
	detachAllPhysActor();
	onEnterOrLeaveWorld(false);
	LOG_INFO("ActorVehicleAssemble::leaveWorld:%p", this);
	m_pWorld->removeSpecialUnit(this);
	static_cast<VehicleAssembleLocoMotion *>(getLocoMotion())->detachPhysActor();

	WORLD_ID rootfatherid = getRootFatherVehilcID();;
	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_FatherVehilcID));
	if (vehicle)
	{
		auto son = vehicle->m_BindVehilcIDs.begin();
		while (son != vehicle->m_BindVehilcIDs.end())
		{
			if (son->second == getObjId())
			{
				son = vehicle->m_BindVehilcIDs.erase(son);
			}
			else
				son++;
		}
		m_FatherVehilcID = 0;
		m_FatherVehilcPos = WCoord(0, 0, 0);
	}


	auto bindvehicle = m_BindVehilcIDs.begin();
	while (bindvehicle != m_BindVehilcIDs.end())
	{
		ClientActor *actor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(bindvehicle->second);

		if (actor && actor->getObjType() != OBJ_TYPE_VEHICLE)
		{
			auto functionWrapper = actor->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setCanMove(true);
			}
			auto vehicleComponent = actor->getActorBindVehicle();
			if (vehicleComponent)
			{
				vehicleComponent->UnBind(!m_pWorld->isRemoteMode());
			}
		}
		else
		{
			ActorVehicleAssemble* vehicle_ = dynamic_cast<ActorVehicleAssemble*>(actor);
			if (vehicle)
			{
				vehicle->m_FatherVehilcID = 0;
				vehicle->m_FatherVehilcPos = WCoord(0, 0, 0);
			}
		}
		bindvehicle++;
	}

	if (rootfatherid)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(rootfatherid));
		if (vehicle)
		{
			vehicle->detachAllPhysActor();
		}
	}
	ClientActor::leaveWorld(keep_inchunk);

	OGRE_RELEASE(m_vehicleSnd.engineWorkSnd);
	OGRE_RELEASE(m_vehicleSnd.engineIgnitionSnd);
	OGRE_RELEASE(m_vehicleSnd.wheelPlaySnd);
	OGRE_RELEASE(m_vehicleSnd.thrusterSnd);
	OGRE_RELEASE(m_vehicleSnd.sthrusterSnd);

	if (getBlockNum() > 0)
	{
		int num = getBlockNum() / 10;
		if (num < 1)num = 1;
		g_all_physxitem -= num;
	}

	if (m_LineGraph)
	{
		ENG_DELETE(m_LineGraph);
	}
	m_VehicleBlockLines.clear();
	m_VehicleBlockNodes.clear();
	clear();
}

bool ActorVehicleAssemble::canBeCollidedWith()
{
	return true;
}

bool ActorVehicleAssemble::canBeRidedWithStandPos(ClientPlayer* player)
{
	for (int y = 0; y < MAX_DIM_Y; y++)
	{
		for (int z = 0; z < MAX_DIM_Z; z++)
		{
			for (int x = 0; x < MAX_DIM_X; x++)
			{
				Block* srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (srcBlock->isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
				{
					continue;
				}
				if (isFunction(srcBlock->getResID(), SEATFUN))
				{
					if (isRudder())
					{
						if (!(srcBlock->getData() & 8))
						{
							continue;
						}
						int dir = srcBlock->getData() % 4;
						WCoord standOffset(0, 0, 0);

						if (dir == DIR_POS_Z)
						{
							standOffset.z = -1;
						}
						else if (dir == DIR_NEG_Z)
						{
							standOffset.z = 1;
						}
						else if (dir == DIR_POS_X)
						{
							standOffset.x = -1;
						}
						else if (dir == DIR_NEG_X)
						{
							standOffset.x = 1;
						}
						if (x + standOffset.x >= MAX_DIM_X || x + standOffset.x < 0) continue;
						if (y + standOffset.y >= MAX_DIM_Y || y + standOffset.y < 0) continue;
						if (y + standOffset.y + 1 >= MAX_DIM_Y || y + standOffset.y - 1 < 0) continue;
						if (z + standOffset.z >= MAX_DIM_Z || z + standOffset.z < 0) continue;
						Block* standBlock = &m_Blocks[x + standOffset.x][y + standOffset.y][z + standOffset.z]->m_Block;
						Block* standLandBlock = &m_Blocks[x + standOffset.x][y + standOffset.y - 1][z + standOffset.z]->m_Block;
						Block* standHeadBlock = &m_Blocks[x + standOffset.x][y + standOffset.y + 1][z + standOffset.z]->m_Block;
						if (standBlock->isEmpty() && standHeadBlock->isEmpty() && !standLandBlock->isEmpty())
						{
							return true;
						}
						else {
							return false;
						}
					}
				}
			}
		}
	}
	return true;
}

bool ActorVehicleAssemble::canBeRided(ClientPlayer *player)
{
	int curItemID = player->getCurToolID();
	if (curItemID > 0)
	{
		// 手上是扳手 不能上机械
		if (player->getCurToolID() == ITEM_WRENCH)
		{
			return false;
		}
		// 手上是连线钳 不能上机械
		if (player->getCurToolID() == ITEM_VEHICLE_LINK_TOOL)
		{
			return false;
		}
	}

	bool m_bHaveSeat = false;
	for (int y = 0; y<MAX_DIM_Y; y++)
	{
		for (int z = 0; z<MAX_DIM_Z; z++)
		{
			for (int x = 0; x<MAX_DIM_X; x++)
			{
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				if (srcBlock.isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
				{
					continue;
				}
				if (isFunction(srcBlock.getResID(), SEATFUN))
				{
					if (isRudder())
					{
						if (!(srcBlock.getData() & 8))
						{
							continue;
						}
						int dir = srcBlock.getData() % 4;
						WCoord standOffset(0, 0, 0);

						if (dir == DIR_POS_Z)
						{
							standOffset.z = -1;
						}
						else if (dir == DIR_NEG_Z)
						{
							standOffset.z = 1;
						}
						else if (dir == DIR_POS_X)
						{
							standOffset.x = -1;
						}
						else if (dir == DIR_NEG_X)
						{
							standOffset.x = 1;
						}
						if (x + standOffset.x >= MAX_DIM_X || x + standOffset.x < 0) continue;
						if (y + standOffset.y >= MAX_DIM_Y || y + standOffset.y < 0) continue;
						if (y + standOffset.y + 1>= MAX_DIM_Y || y + standOffset.y - 1 < 0) continue;
						if (z + standOffset.z >= MAX_DIM_Z || z + standOffset.z < 0) continue;
						Block* standBlock = &m_Blocks[x + standOffset.x][y + standOffset.y][z + standOffset.z]->m_Block;
						Block* standLandBlock = &m_Blocks[x + standOffset.x][y + standOffset.y - 1][z + standOffset.z]->m_Block;
						Block* standHeadBlock = &m_Blocks[x + standOffset.x][y + standOffset.y + 1][z + standOffset.z]->m_Block;
						if (standBlock->isEmpty() && standHeadBlock->isEmpty() && !standLandBlock->isEmpty())
						{
							//判断船舵前面可以站人
							m_bHaveSeat = true;
						}
					}
					else {
						m_bHaveSeat = true;
					}
				}
			}
		}
	}

	if (!m_bHaveSeat)
		return false;

	bool isRemote = m_pWorld->isRemoteMode();
	m_pWorld->setRemoteMode(true);
	int currItemID = player->getCurToolID();
	int x = -1, y = -1, z = -1;
	intersect(player, x, y, z);
	bool needride = false;
	if (x >= 0 && y >= 0 && z >= 0)
	{
		WCoord blockpos(x, y, z);
		int blockid = getBlockID(blockpos);
		if (blockid == BLOCK_VEHICLEENGINE)
		{
			m_pWorld->setRemoteMode(isRemote);
			return false;
		}
		else if (blockid == BLOCK_CATAPULT)   //现在点击投射装置，也不上车
		{
			m_pWorld->setRemoteMode(isRemote);
			return false;
		}

		BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
		if (pmtl && pmtl->getBlockSettingAttState(ENABLE_BEOPERATED) > 0)
		{
			DirectionType targetface = DIR_NOT_INIT;
			if (isFunction(blockid, SEATFUN))
			{
				needride = true;
			}
			//这里要调用lua里面的代码，现在调不过去，先跳过吧
			else if(!pmtl->DoOnTrigger(m_pVehicleWorld, blockpos, targetface, player, Vector3f(0, 0, 0)))
			{
				for (int y = 0; y<MAX_DIM_Y; y++)
				{
					for (int z = 0; z<MAX_DIM_Z; z++)
					{
						for (int x = 0; x<MAX_DIM_X; x++)
						{
							Block srcBlock = m_Blocks[x][y][z]->m_Block;
							if (srcBlock.isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
							{
								continue;
							}
							if (isFunction(srcBlock.getResID(), SEATFUN))
							{
								needride = true;
							}
						}
					}
				}
			}
		}
	}
	m_pWorld->setRemoteMode(isRemote);
	if (needride && currItemID != ITEM_WRENCH)
	{
		return true;
	}
	return false;
}

//得到的坐标是(MAX_DIM_X, MAX_DIM_Y, MAX_DIM_Z)范围内的相对坐标
WCoord ActorVehicleAssemble::getRiddenBindSeatPos(ClientActor *ridden)
{
	WCoord seatPos;
	int i = findRiddenIndex(ridden);
	if (getNumRiddenPos() <= 1 || i <= 0)
	{
		seatPos = m_SeatRelativePos;
	}
	else {
		seatPos = m_OtherSeatPos[i - 1];
		WCoord centerPos = WCoord(MAX_DIM_X / 2, 0, MAX_DIM_Z / 2);
		seatPos = seatPos / BLOCK_SIZE + centerPos;
	}
	return seatPos;
}

int ActorVehicleAssemble::getRiddenBindSeatDir(ClientActor *ridden)
{
	int seatDir;
	if (getNumRiddenPos() > 1)
	{
		int i = findRiddenIndex(ridden);
		if (i <= 0)
			seatDir = m_SeatDir;
		else
			seatDir = m_OtherSeatDir[i - 1];
	}
	else
	{
		seatDir = m_SeatDir;
	}

	return seatDir;
}

void ActorVehicleAssemble::resetRiddenBindPos()
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		loc->resetRiddenBindPos();
	}
}

Rainbow::Vector3f ActorVehicleAssemble::getRiddenBindPos(ClientActor *ridden)
{
	Rainbow::Vector3f seatPos;
	if (getNumRiddenPos() > 1)
	{
		int i = findRiddenIndex(ridden);
		if (i <= 0)
			seatPos = m_SeatPos.toVector3();
		else
			seatPos = m_OtherSeatPos[i - 1].toVector3();
	}
	else
	{
		seatPos = m_SeatPos.toVector3();
	}

	WCoord blockpos = getRiddenBindSeatPos(ridden);

	int h = 0;
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(blockpos));
	if (physicsPartsDef)
	{
		PhysicsPartsDef::EffectFunctionsDef* seatFun = NULL;
		for (int i = 0; i<(int)physicsPartsDef->EffectFunctions.size(); i++)
		{
			PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
			if (functiondef->func_id == 3) //座位
			{
				seatFun = functiondef;
			}
		}
		if (seatFun)
		{
			h = seatFun->func.seatfun.AddHeight;
		}
	}

	int groupid = m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_GroupIdSelf;
	int dir = getBlockData(blockpos) & 7;
	int realGroupId = calculateRealChassisIndex(groupid);
	if (realGroupId > 0)
	{
		blockpos = blockpos * BLOCK_FSIZE;
		blockpos.y += h;
		if (realGroupId < (int)m_JointCreateParams.size() && m_JointCreateParams[realGroupId].mType == REVOLUTE)
		{
			blockpos.y -= (int)m_JointCreateParams[realGroupId].mPosBase.y;
		}

		seatPos = convertWcoord(Rainbow::Vector3f((float)blockpos.x, (float)blockpos.y, (float)blockpos.z), true, realGroupId).toVector3();
		return seatPos;
	}
	else
	{
		seatPos.y += h;
		VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
		Rainbow::Vector3f pos;
		Rainbow::Quaternionf updateRot;
		if (loc->m_ChassisUpdatePos.size())
		{
			pos = loc->m_ChassisUpdatePos[0].m_PUpdatePos.toVector3();
			updateRot = loc->m_ChassisUpdatePos[0].m_UpdateRot;
		}
		else
		{
			pos = loc->m_Position.toVector3();
			updateRot = loc->m_UpdateRot;
		}
		Rainbow::Vector3f addPos;
		addPos = updateRot* seatPos;
		//updateRot.rotate(addPos, seatPos);
		return pos + addPos;
	}
	return Vector3f::zero;
}

bool ActorVehicleAssemble::getRiddenBindRot(ClientActor *ridden, Rainbow::Quaternionf &rot)
{
	if (!ridden)	return false;
	WCoord blockpos = getRiddenBindSeatPos(ridden);
	int groupid = m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_GroupIdSelf;
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		if (loc->m_ChassisUpdatePos.size())
		{
			int index = loc->getPhysIndexWithGroupId(groupid);
			rot = loc->m_ChassisUpdatePos[index].m_UpdateRot;
		}
		else
		{
			rot = loc->m_UpdateRot;
		}
	}
	return true;
}

int ActorVehicleAssemble::getNumRiddenPos()
{
	return m_OtherRiddens.size() + 1;
}

WORLD_ID ActorVehicleAssemble::getRiddenByActorID(int i)
{
	if (i == 0)
	{
		auto RidComp = getRiddenComponent();
		if (RidComp)
			return RidComp->getRiddenByActorID_Base();
		return 0;
	}
	else
	{
		assert(i < getNumRiddenPos());
		return m_OtherRiddens[i - 1];
	}
}
ClientActor* ActorVehicleAssemble::getRiddenByActor(int i)
{
	WORLD_ID id = getRiddenByActorID(i);
	if (id != 0 && m_pWorld)
	{
		return static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(id);
	}
	return nullptr;
}

int ActorVehicleAssemble::getSeatIndex(WCoord pos) {

	WCoord blockpos = CoordDivBlock(pos);
	int blockid = getBlockID(blockpos);
	if (blockid == BLOCK_BOAT_RUDDER)
	{
		auto tmpCt = m_pVehicleWorld->getContainerMgr()->getContainer(blockpos);
		if (tmpCt)
		{
			ContainerDriverSeatModel* container = dynamic_cast<ContainerDriverSeatModel*>(tmpCt);
			if (container)
			{
				WCoord offset(0, 0, 0);
				if (m_SeatDir == DIR_POS_Z)
				{
					offset.z = -1;
				}
				else if (m_SeatDir == DIR_NEG_Z)
				{
					offset.z = 1;
				}
				else if (m_SeatDir == DIR_POS_X)
				{
					offset.x = -1;
				}
				else if (m_SeatDir == DIR_NEG_X)
				{
					offset.x = 1;
				}
				pos = (container->getCoreBlockPos() + offset) * BLOCK_SIZE;
			}
		}	
	}

	pos = pos - WCoord(MAX_DIM_X / 2, 0, MAX_DIM_Z / 2) * BLOCK_SIZE;
	if (pos == m_SeatPos) {
		return 0;
	}
	else {
		for (int i = 0; i < (int)m_OtherSeatPos.size(); i++) {
			WCoord pos1 = m_OtherSeatPos[i];
			LOG_INFO("pos1: %d %d %d", pos1.x, pos1.y, pos1.z);
			if (pos.x == pos1.x &&  pos.y == pos1.y && pos.z == pos1.z) {
				return i + 1;
			}
		}
	}

	return -1;
}
//
int ActorVehicleAssemble::findEmptyRiddenIndex(int seatIndex)
{
	if (seatIndex < 0) return -1;
	if (getRiddenByActor(seatIndex) == NULL)
		return seatIndex;
	else
		return -1;
}
//
WCoord ActorVehicleAssemble::getGroupSideBlockPosWithGroupId(int groupId, int dir)
{
	if (0 <= dir && 6 > dir)
	{
		auto iter = m_mJointSideBlocksPos.find(groupId);
		if (iter != m_mJointSideBlocksPos.end())
		{
			return m_mJointSideBlocksPos[groupId][dir];
		}
	}
	return WCoord(0, 0, 0);
}
//
int ActorVehicleAssemble::findEmptyRiddenIndexInOrder()
{
	int n = getNumRiddenPos();
	for (int i = 0; i < n; i++)
	{
		if (getRiddenByActor(i) == NULL) return i;
	}
	return -1;
}

int ActorVehicleAssemble::calculateRealChassisIndex(int groupid)
{
	int res = groupid;
	if (groupid > m_ChassisBlockNums.size())
	{
		// 防止下面的 m_ChassisBlockNums[i] 越界
		// https://console.firebase.google.com/u/0/project/mini-world-171311/crashlytics/app/android:com.playmini.miniworld/issues/e8a1be69a6e0e9ad966beacefde395fd?time=last-seven-days&versions=1.0.45%20(65581)&sessionEventKey=630fe91e02fe00016d641c1b977485d6_1716635763922466260
		groupid = m_ChassisBlockNums.size();
	}

	for (int i = 0; i < groupid; i++)
	{
		if (m_ChassisBlockNums[i] <= 0)
		{
			res -= 1;
		}
	}
	return res;
}

void ActorVehicleAssemble::setRiddenByActor(ClientActor *p, int i)
{
	//客机上车时，主机同步一下油耗
	if (m_CostParts.size() > 0)
	{
		if (p && g_pPlayerCtrl && g_pPlayerCtrl->getUin() != p->getObjId() && !m_pWorld->isRemoteMode())
		{
			for (int i = 0; i < (int)m_CostParts.size(); i++)
			{
				DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[i]);
				if (dynamicCost)
					setFuelWithIndex(dynamicCost->getCurValue(), i);
			}
		}
	}
	//操作载具数据统计上报
	if (p&&i >= 0)
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(p);
		if (player && g_pPlayerCtrl)
		{
			unsigned short data = getStatisticData();
			MINIW::ScriptVM::game()->callFunction("SendVehicleStatisticData", "iii", 52004, i, data);
			//const char * flag = (i == 0) ? "0" : "1";
			//g_pPlayerCtrl->statisticToWorld(player->getUin(), 52004, "", g_pPlayerCtrl->getCurWorldType(), "param_to_str", flag);
			//拆分成两个id上报
			MINIW::ScriptVM::game()->callFunction("SendVehicleStatisticData", "iii", 52024, i, data);
			MINIW::ScriptVM::game()->callFunction("SendVehicleStatisticData", "iii", 52022, i, data);
		}

	}
	if (p)
	{
		ClientPlayer *player = dynamic_cast<ClientPlayer *>(p);
		if (player)
		{
			PlayerLocoMotion* loco = dynamic_cast<PlayerLocoMotion *>(player->getLocoMotion());
			loco->detachPhysActor();
			if (!m_pWorld->isRemoteMode() && player == g_pPlayerCtrl)
			{
				VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
				loc->setFocus();
			}
		}
	}
	else
	{
		ClientActor* actor = NULL;
		if (i == 0)
			//actor = m_pWorld->getActorMgr()->findActorByWID(m_RiddenByActor);
			actor = getRiddenByActor();
		else
			actor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OtherRiddens[i - 1]);
		if (actor)
		{
			ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
			if (player)
			{
				PlayerLocoMotion* locoPlayer = dynamic_cast<PlayerLocoMotion *>(player->getLocoMotion());
				locoPlayer->attachPhysActor();
				if (!m_pWorld->isRemoteMode())
				{
					VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
					if (player == g_pPlayerCtrl)
						locoVehicle->unsetFocus();

					if (i == 0 && player == g_pPlayerCtrl)
						g_pPlayerCtrl->m_VehicleControlInputs->reset();

					if (i == 0)
						locoVehicle->stop();
				}
			}
			//按键状态做一个清除操作
			WCoord seatpos = getRiddenBindSeatPos(player);
			ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
			if (container)
			{
				auto keyiter = container->m_BindKeyData.begin();
				while (keyiter != container->m_BindKeyData.end())
				{
					if(player != nullptr)
						TriggerControlKeys(player->getUin(), keyiter->first, 0, false);
					keyiter++;
				}
			}
		}
	}

	if (i == 0)
	{
		WORLD_ID tempActor = getRiddenByActorID();
		auto RidComp = sureRiddenComponent();
		if (RidComp)
			RidComp->setRiddenByActor_Base(p, 0);

		if (p)
		{
			if (p->getObjId() > 0)
			{
				ClientPlayer *player = dynamic_cast<ClientPlayer *>(p);
				if (player)
				{
					PlayerControl *player_control = dynamic_cast<PlayerControl *>(player);
					if (player_control)
					{
						player_control->setCurShortcut(player_control->getCurShortcut());
						//g_pPlayerCtrl->getBody()->setEquipItem(EQUIP_WEAPON, 0);
					}

				}
			}
		}
		else
		{
			if (tempActor&&tempActor > 0)
			{
				ClientActor* actor = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(tempActor);
				if (actor)
				{
					ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
					if (player)
					{
						PlayerControl *player_control = dynamic_cast<PlayerControl *>(player);
						if (player_control)
							player_control->setCurShortcut(player_control->getCurShortcut());
					}
				}
			}
		}

	}
	else
	{
		assert(i < getNumRiddenPos());
		if (p)
			m_OtherRiddens[i - 1] = p->getObjId();
		else
			m_OtherRiddens[i - 1] = 0;
	}
}

void ActorVehicleAssemble::setRiddenByActorObjId(WORLD_ID objId, int i /* = 0 */)
{
	if (i == 0)
	{
		auto RidComp = sureRiddenComponent();
		if (RidComp)
		{
			RidComp->setRiddenByActorObjId_Base(objId, i);
		}
	}
	else
	{
		assert(i < getNumRiddenPos());
		m_OtherRiddens[i - 1] = objId;
	}
}

flatbuffers::Offset<FBSave::SectionActor> ActorVehicleAssemble::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VABlockData>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::VABlockData>> blockdatas;

	WCoord dim = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);

	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				if (srcBlock.isEmpty())
					continue;

				if (srcBlock.getResID() > 0)
				{
					int life = m_Blocks[x][y][z]->m_iCurLife;
					if (life < 0)
						life = 0;
					int fuel = m_Blocks[x][y][z]->m_iCurFuel;
					if (fuel < 0)
						fuel = 0;
					int heat = m_Blocks[x][y][z]->m_iCurHeat;
					if (heat < 0)
						heat = 0;
					blockdatas.push_back(FBSave::CreateVABlockData(builder, srcBlock.getOriginData(), (x << 10) | (y << 5) | z, life, fuel, m_Blocks[x][y][z]->m_GroupIdSelf, heat, m_Blocks[x][y][z]->m_GroupIdChild, m_Blocks[x][y][z]->m_GroupIdParent, srcBlock.getDataEx()));
				}
			}
		}
	}
	blockdatasoffset = builder.CreateVector(blockdatas);

	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	if (getNumRiddenPos() > 1)
	{
		std::vector<uint64_t> riddens;
		for (int i = 1; i<getNumRiddenPos(); i++)
		{
			riddens.push_back(m_OtherRiddens[i - 1]);
		}
		otherriddens = builder.CreateVector(riddens);
	}
	VehicleAssembleLocoMotion *locmove = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	auto quat = QuaternionToSave(locmove->m_RotateQuat);

	std::vector<WorldContainer *> chunkcontainers;
	if (m_pVehicleWorld)
	{
		VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		if (containermgr)
		{
			containermgr->getContainersVec(chunkcontainers);
		}
	}
	auto containervec = CreateContainerVec(builder, chunkcontainers);

	std::vector<flatbuffers::Offset<FBSave::VehicleBlockLine>> vehicleLines;
	for (auto it = m_VehicleBlockLines.begin(); it != m_VehicleBlockLines.end(); it++)
	{
		vehicleLines.push_back(FBSave::CreateVehicleBlockLine(builder, it->from, it->to, it->canedit));
	}

	FBSave::Coord3 *coord_pos = NULL;
	FBSave::Coord3 temp_pos(0, 0, 0);
	if (locmove->m_ChassisUpdatePos.size())
	{
		WCoord saveData = WCoord(locmove->m_ChassisUpdatePos[0].m_PPosition.x, locmove->m_ChassisUpdatePos[0].m_PPosition.y, locmove->m_ChassisUpdatePos[0].m_PPosition.z);
		temp_pos = WCoordToCoord3(saveData);
		coord_pos = &temp_pos;
	}

	auto actor = FBSave::CreateActorVehicleAssemble(builder,
		basedata,
		blockdatasoffset,
		0,				// 此字段废弃 改用后面的filename
		getRiddenByActorID(),
		otherriddens,
		m_WorkshopDirection,
		m_LifeCurrent,
		builder.CreateString(m_FileName),
		builder.CreateString(m_ModelName),
		builder.CreateString(m_ModelDesc),
		0,				//已废弃
		m_itemID,
		m_nCoreDecHp,
		&quat,
		containervec,
		builder.CreateVector(vehicleLines)
		, coord_pos,
		m_BlockGroupNum);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorVehicleAssemble, actor.Union());
}

flatbuffers::Offset<FBSave::SectionActor> ActorVehicleAssemble::saveToNet(SAVE_BUFFER_BUILDER &builder)
{
	flatbuffers::FlatBufferBuilder builder_;
	auto basedata = saveActorCommon(builder_);

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VABlockEnterData>>> blockdatasoffset = 0;
	std::vector<flatbuffers::Offset<FBSave::VABlockEnterData>> blockdatas;

	WCoord dim = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);

	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				int life = m_Blocks[x][y][z]->m_iCurLife;
				if (life < 0)
					life = 0;
				blockdatas.push_back(FBSave::CreateVABlockEnterData(builder_, m_Blocks[x][y][z]->m_Block.getOriginData(), (life << 16) | m_Blocks[x][y][z]->m_GroupIdSelf, m_Blocks[x][y][z]->m_Block.getDataEx()));
			}
		}
	}
	blockdatasoffset = builder_.CreateVector(blockdatas);

	flatbuffers::Offset<flatbuffers::Vector<uint64_t>> otherriddens = 0;
	if (getNumRiddenPos() > 1)
	{
		std::vector<uint64_t> riddens;
		for (int i = 1; i<getNumRiddenPos(); i++)
		{
			riddens.push_back(m_OtherRiddens[i - 1]);
		}
		otherriddens = builder_.CreateVector(riddens);
	}

	std::vector<WorldContainer *> chunkcontainers;
	if (m_pVehicleWorld)
	{
		VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		if (containermgr)
		{
			containermgr->getContainersVec(chunkcontainers);
		}
	}
	auto containervec = CreateContainerVec(builder_, chunkcontainers);

	std::vector<flatbuffers::Offset<FBSave::VehicleBlockLine>> vehicleLines;
	for (auto it = m_VehicleBlockLines.begin(); it != m_VehicleBlockLines.end(); it++)
	{
		vehicleLines.push_back(FBSave::CreateVehicleBlockLine(builder_, it->from, it->to, it->canedit));
	}

	flatbuffers::Offset<flatbuffers::Vector<uint32_t>> groupids = 0;
	std::vector<uint32_t> groupidsv;
	for (int i = 0; i<(int)m_JointCreateParams.size(); i++)
	{
		if (m_JointCreateParams[i].mGroupId0 != m_JointCreateParams[i].mGroupId1)
		{
			groupidsv.push_back((unsigned int)((m_JointCreateParams[i].x << 16) | (m_JointCreateParams[i].z << 8) | m_JointCreateParams[i].y) | (m_JointCreateParams[i].mGroupId1 << 24));
		}
	}
	groupids = builder_.CreateVector(groupidsv);

	VehicleAssembleLocoMotion *locmove = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	auto quat = QuaternionToSave(locmove->m_RotateQuat);
	auto actor = FBSave::CreateActorVehicleAssembleEnterData(builder_,
		basedata,
		blockdatasoffset,
		0,				// 此字段废弃 改用后面的filename
		getRiddenByActorID(),
		otherriddens,
		m_WorkshopDirection,
		m_LifeCurrent,
		builder_.CreateString(m_FileName),
		builder_.CreateString(m_ModelName),
		builder_.CreateString(m_ModelDesc),
		0,				//已废弃
		m_itemID,
		&quat, containervec, builder_.CreateVector(vehicleLines), groupids, m_BlockGroupNum);

	std::vector<uint8_t> blockdatas_enter;
	builder_.Finish(actor);
	unsigned char *src = builder_.GetBufferPointer();
	size_t srclen = builder_.GetSize();

	int compresstype = CompressTool::COMPRESS_LZMA;
	CompressTool ctool(compresstype);
	size_t destlen = ctool.compressBound(srclen);

	char *blobDetail = new char[destlen];
	if (!ctool.compress(blobDetail, destlen, src, srclen, 0))
	{
		assert(0);
		OGRE_DELETE_ARRAY(blobDetail);
		return NULL;
	}
	blockdatas_enter.resize(destlen);
	memcpy(&blockdatas_enter[0], blobDetail, destlen);
	OGRE_DELETE_ARRAY(blobDetail);

	flatbuffers::Offset<flatbuffers::Vector<uint8_t>> blockdatasoffset_enter = 0;
	blockdatasoffset_enter = builder.CreateVector(blockdatas_enter);

	auto actor_enter = FBSave::CreateActorVehicleAssembleEnter(builder, blockdatasoffset_enter, srclen);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorVehicleAssemble, actor_enter.Union());
}

bool ActorVehicleAssemble::load(const void *srcdata, int version)
{
	auto actor = reinterpret_cast<const FBSave::ActorVehicleAssemble*>(srcdata);
	loadActorCommon(actor->basedata());

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::VABlockData>>> bpblocksoffset0 = 0;
	std::vector<flatbuffers::Offset<FBSave::VABlockData>> bpblocks0;

	if (actor->blockGroupNum() == 0)
	{
		if (m_pContainerWorkshop)
			m_BlockGroupNum = m_pContainerWorkshop->getJointNum() + 1;
		else
			m_BlockGroupNum = 1;
	}
	else
	{
		m_BlockGroupNum = actor->blockGroupNum();
	}
	if (actor->blocks())
	{
		for (int i = 0; i < (int)actor->blocks()->size(); i++)
		{
			auto src = actor->blocks()->Get(i);
			int possrc = src->relativepos();
			WCoord pos((possrc & (0x1f << 10)) >> 10, (possrc & (0x1f << 5)) >> 5, possrc & 0x1f);

			m_Blocks[pos.x][pos.y][pos.z]->m_Block = Block::changeBlockNewFormat(src->block(), src->blockex());
			m_Blocks[pos.x][pos.y][pos.z]->m_iCurLife = src->blockhp();
			m_Blocks[pos.x][pos.y][pos.z]->m_iCurFuel = src->blockCost();
			m_Blocks[pos.x][pos.y][pos.z]->m_iCurHeat = src->blockHeat();
			m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdSelf = src->blockGroup() == 65535 ? -1 : src->blockGroup();
			m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdChild = src->blockGroupJoint();
			m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdParent = src->oriGroupid();

			m_BlockPos.push_back(WCoord(pos.x, pos.y, pos.z));

			if (m_CostParts.size() > 0)
			{
				for (int j = 0; j < (int)m_CostParts.size(); j++)
				{
					WCoord costBlockPos = m_CostParts[j]->getPos();
					DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[j]);
					if (dynamicCost && costBlockPos == pos)
						dynamicCost->setCurValue(m_Blocks[pos.x][pos.y][pos.z]->m_iCurFuel);
				}
			}

			if (m_Overheats.size() > 0)
			{
				for (int j = 0; j < (int)m_Overheats.size(); j++)
				{
					DynamicOverheat* dynamicOverheat = dynamic_cast<DynamicOverheat*>(m_Overheats[j]);
					WCoord overheatBlockPos = m_Overheats[j]->getPos();
					if (dynamicOverheat && overheatBlockPos == pos)
						dynamicOverheat->setCurHeat(m_Blocks[pos.x][pos.y][pos.z]->m_iCurHeat);
				}
			}
		}
	}

	if (actor->filename())
		m_FileName = actor->filename()->c_str();

	//m_RiddenByActor = actor->ridden();
	setRiddenByActorObjId(actor->ridden());

	auto otherriddens = actor->otherriddens();
	if (otherriddens)
	{
		m_OtherRiddens.resize(otherriddens->size());
		for (size_t i = 0; i < otherriddens->size(); i++)
		{
			m_OtherRiddens[i] = otherriddens->Get(i);
		}
	}

	m_WorkshopDirection = actor->direction();

	m_LifeCurrent = actor->lifecurrent();
	if (actor->name())
	{
		m_ModelName = actor->name()->str();
	}
	if (actor->desc())
	{
		m_ModelDesc = actor->desc()->str();
	}
	if (actor->itemid())
	{
		m_itemID = actor->itemid();
	}
	if (actor->coredechp())
	{
		m_nCoreDecHp = actor->coredechp();
	}
	VehicleAssembleLocoMotion* locmove = static_cast<VehicleAssembleLocoMotion*>(getLocoMotion());

	if (actor->mainchassispos())
	{
		m_ChassisPos = Coord3ToWCoord(actor->mainchassispos());
		m_bHaveChassisPos = true;
	}

	if (actor->rotatequat())
	{
		locmove->m_RotateQuat = QuaternionFromSave(actor->rotatequat());
	}
	else
	{

		locmove->m_RotateQuat = AngleEulerToQuaternionf(Vector3f(locmove->m_RotationPitch, locmove->m_RotateYaw, 0));
		m_bHaveChassisPos = false;
	}
	if (actor->containers())
	{
		for (size_t i = 0; i < actor->containers()->size(); i++)
		{
			auto con = actor->containers()->Get(i);
			WorldContainer* obj = CreateWorldVehicleContainerFromChunkContainer(con);
			if (obj)
			{
				if (obj->load(con->container()))
				{
					//m_pVehicleWorld->getContainerMgr()->spawnContainer(container);
					m_WorldContainer.push_back(obj);
				}
				else
				{
					OGRE_DELETE(obj);
				}
			}
		}
	}
	m_VehicleBlockLines.clear();
	//LOG_INFO("m_VehicleBlockLines.clear()11111 %d ",actor->vehiclelines()->size());
	if (actor->vehiclelines())
	{
		for (size_t i = 0; i < actor->vehiclelines()->size(); i++)
		{
			auto* oneline = actor->vehiclelines()->Get(i);
			VehicleBlockLine lineinfo;
			lineinfo.from = oneline->from();
			lineinfo.to = oneline->to();
			lineinfo.canedit = oneline->canedit();

			m_VehicleBlockLines.push_back(lineinfo);
		}
	}
	m_isCreateFromLoad = true;

	resetAttib();
	return true;
}

bool ActorVehicleAssemble::loadFromNet(const void *srcdata, int version)
{
	
	auto actor_enter = reinterpret_cast<const FBSave::ActorVehicleAssembleEnter *>(srcdata);

	const flatbuffers::Vector<uint8_t> *bpblocks0 = actor_enter->data();
	const char* chunkBuffer = (const char*)bpblocks0->Data();
	size_t srcLen = bpblocks0->size();
	size_t destlen = actor_enter->unziplen();
	if (destlen > 4 * 1024 * 1024)
	{
		//assert(0);
		return false; //解压缩数据不能大于2M, 否则肯定是数据错误
	}
	unsigned char *rawdata = new (std::nothrow) unsigned char[destlen];
	if (rawdata == NULL)  return false;
	int compresstype = CompressTool::COMPRESS_LZMA;
	Rainbow::CompressTool ctool(compresstype);
	if (!ctool.decompress(rawdata, destlen, chunkBuffer, srcLen))
	{
		//assert(0);
		LOG_WARNING("uncompress chunk save blob failed");
		OGRE_DELETE_ARRAY(rawdata);
		return false;
	}

	const FBSave::ActorVehicleAssembleEnterData *actor = flatbuffers::GetRoot<FBSave::ActorVehicleAssembleEnterData>(rawdata);

	loadActorCommon(actor->basedata());

	if (actor->blockGroupNum() == 0)
	{
		m_BlockGroupNum = 1;
	}
	else
	{
		m_BlockGroupNum = actor->blockGroupNum();
	}
	if (actor->blocks())
	{
		if (actor->blocks()->size() == (MAX_DIM_X*MAX_DIM_Y*MAX_DIM_Z))
		{
			int count = 0;
			for (int y = 0; y < MAX_DIM_Y; y++)
			{
				for (int z = 0; z < MAX_DIM_Z; z++)
				{
					for (int x = 0; x < MAX_DIM_X; x++)
					{
						auto src = actor->blocks()->Get(count);
						m_Blocks[x][y][z]->m_Block = Block::changeBlockNewFormat(src->block(), src->blockex());
						m_Blocks[x][y][z]->m_iCurLife = src->info() >> 16;
						m_Blocks[x][y][z]->m_GroupIdSelf = (src->info() & 0xff) == 65535 ? -1 : (src->info() & 0xff);
// 						if (m_BlockGroupNum <= m_Blocks[x][y][z]->m_GroupId)
// 						{
// 							m_BlockGroupNum = m_Blocks[x][y][z]->m_GroupId + 1;
// 						}
						count++;
					}
				}
			}
		}
	}

	if (actor->filename())
		m_FileName = actor->filename()->c_str();


	setRiddenByActorObjId(actor->ridden());
	auto otherriddens = actor->otherriddens();
	if (otherriddens)
	{
		m_OtherRiddens.resize(otherriddens->size());
		for (size_t i = 0; i<otherriddens->size(); i++)
		{
			m_OtherRiddens[i] = otherriddens->Get(i);
		}
	}

	m_WorkshopDirection = actor->direction();

	m_LifeCurrent = actor->lifecurrent();
	if (actor->name())
	{
		m_ModelName = actor->name()->str();
	}
	if (actor->desc())
	{
		m_ModelDesc = actor->desc()->str();
	}
	if (actor->itemid())
	{
		m_itemID = actor->itemid();
	}
	m_nCoreDecHp = 0;

	VehicleAssembleLocoMotion *locmove = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (actor->rotatequat())
		locmove->m_RotateQuat = QuaternionFromSave(actor->rotatequat());
	else
		locmove->m_RotateQuat = AngleEulerToQuaternionf(Vector3f(locmove->m_RotationPitch, locmove->m_RotateYaw, 0));

	if (actor->containers())
	{
		for (size_t i = 0; i<actor->containers()->size(); i++)
		{
			auto con = actor->containers()->Get(i);
			WorldContainer *obj = CreateWorldVehicleContainerFromChunkContainer(con);
			if (obj)
			{
				if (obj->load(con->container()))
				{
					//m_pVehicleWorld->getContainerMgr()->spawnContainer(container);
					m_WorldContainer.push_back(obj);
				}
				else
				{
					ENG_DELETE(obj);
				}
			}
		}
	}
	m_VehicleBlockLines.clear();
	//LOG_INFO("m_VehicleBlockLines.clear()22222 %d ",actor->vehiclelines()->size());
	if (actor->vehiclelines())
	{
		for (size_t i = 0; i < actor->vehiclelines()->size(); i++)
		{
			auto* oneline = actor->vehiclelines()->Get(i);
			VehicleBlockLine lineinfo;
			lineinfo.from = oneline->from();
			lineinfo.to = oneline->to();
			lineinfo.canedit = oneline->canedit();
			m_VehicleBlockLines.push_back(lineinfo);
		}
	}

	auto jointgroups = actor->jointgroup();
	if (jointgroups)
	{
		for (size_t i = 0; i<jointgroups->size(); i++)
		{
			m_GroupJointFromServer[jointgroups->Get(i) & 0xffffff] = jointgroups->Get(i) >> 24;
		}
	}

	m_isCreateFromLoad = true;
	resetAttib();
	OGRE_DELETE_ARRAY(rawdata);
	return true;
}

void ActorVehicleAssemble::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
	VehicleAssembleLocoMotion *locmove = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (!(locmove->m_ServerRot == rot && pos == locmove->m_ServerPos))
	{
		locmove->m_PosRotationIncrements = interpol_ticks;
		locmove->m_ServerPos = pos;
		locmove->m_ServerRot = rot;
	}
}

void ActorVehicleAssemble::syncServerPos(const std::vector<VehicleSyncPosDesc> &chassisServerPos, const std::vector<VehicleSyncPosDesc> &wheelServerPos, int interpol_ticks, int wheelRot_ticks)
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());

	if (loc->m_VehiclePosRotationIncrements < interpol_ticks)
		loc->m_VehiclePosRotationIncrements = interpol_ticks;
	if (loc->m_VehicleWheelRotationIncrements < wheelRot_ticks)
		loc->m_VehicleWheelRotationIncrements = wheelRot_ticks;
	loc->m_ChassisServerPos = chassisServerPos;
	loc->m_WheelServerPos = wheelServerPos;
}

bool ActorVehicleAssemble::attackedFrom(OneAttackData &atkdata, ClientActor *actor)
{
	CollideAABB box;
	box.pos = WCoord(0, 0, 0);
	box.dim = WCoord(0, 0, 0);
	return attackedVehicleFrom(atkdata, actor, box);
}

bool ActorVehicleAssemble::attackedVehicleFrom(OneAttackData &atkdata, ClientActor *actor, CollideAABB &box)
{
	if (!canBeHurt())
	{
		return false;
	}
	if (dynamic_cast<ClientActorProjectile*>(this))
	{
		return false;
	}
	//计算实际攻击力
	getAttrib()->attackedFrom(atkdata);
	//计算攻击部位
	if (atkdata.atktype == ATTACK_EXPLODE)
	{
		for (int i = 0; i < (int)m_BlockPos.size(); i++)
		{
			int x = m_BlockPos[i].x;
			int y = m_BlockPos[i].y;
			int z = m_BlockPos[i].z;
			{
				if (m_Blocks[x][y][z]->m_iCurLife == 0)
					continue;

				Rainbow::Vector3f dp = (convertWcoord(WCoord(x, y, z)) - WCoord(atkdata.explodePos_x, atkdata.explodePos_y, atkdata.explodePos_z)).toVector3();
				float dist = dp.Length();
				if (atkdata.explodeSize < BLOCK_SIZE)
					atkdata.explodeSize = BLOCK_SIZE;
				float t = dist / atkdata.explodeSize;
				const int MAX_COLBOX_DIM = BLOCK_SIZE * 2;

				if (t <= 1.0f)
				{
					if (dist != 0)
					{
						const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(m_Blocks[x][y][z]->m_Block.getResID());
						if (!physicsPartsDef)
						{
							continue;
						}
						//载具的每个方块单独计算伤害
						CollideAABB box;
						box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
						box.pos = convertWcoord(WCoord(x, y, z)) - box.dim / 2;
						float density = GetBlockDensity(m_pWorld, WCoord(atkdata.explodePos_x, atkdata.explodePos_y, atkdata.explodePos_z), box);
						float explodestr = (1.0f - t) * density;
						float explodesize = atkdata.explodeSize / (BLOCK_FSIZE*2.0f);
						int atkpoints = int((explodestr*explodestr + explodestr) * 8.0f * explodesize + 1.0f) * 5;
						atkdata.atkpoints = (float)atkpoints;
						if (physicsPartsDef->IsCore)
						{
							m_nCoreDecHp += (int)atkdata.atkpoints;

						}
						else
						{
							if (m_Blocks[x][y][z]->m_iCurLife)
							{
								m_Blocks[x][y][z]->m_iCurLife -= (int)atkdata.atkpoints;
							}

							if (m_Blocks[x][y][z]->m_iCurLife <= 0)
							{
								m_Blocks[x][y][z]->m_iCurLife = 0;
								if (m_Blocks[x][y][z]->m_Block.getResID() > 0)
								{
									std::stringstream stringstream;
									stringstream << x << "_" << "" << y << "_" << z;
									std::string strPos;
									stringstream >> strPos;
									ContactDesc contactDesc;
									contactDesc.m_BlockPos = WCoord(x, y, z);
									contactDesc.m_BlockID = m_Blocks[x][y][z]->m_Block.getResID();
									contactDesc.m_bdestroy = true;
									m_ContactDescs[strPos] = contactDesc;
								}
							}
						}
					}
				}
			}
		}

		check_link_core();
	}
	//岩浆攻击
	else if (atkdata.atktype == MAX_PHYSICS_ATTACK && actor == NULL)
	{
		for (int i = 0; i < (int)m_BlockPos.size(); i++)
		{
			int x = m_BlockPos[i].x;
			int y = m_BlockPos[i].y;
			int z = m_BlockPos[i].z;
			if (m_Blocks[x][y][z]->m_iCurLife == 0)
				continue;
			const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(m_Blocks[x][y][z]->m_Block.getResID());
			if (!physicsPartsDef)
			{
				continue;
			}
			bool isWheelPart = false;
			//if (m_pWorld && !m_pWorld->isRemoteMode())
			{
				for (int i = 0; i < (int)physicsPartsDef->EffectFunctions.size(); i++)
				{
					PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
					if (functiondef->func_id == 1) //车轮
					{
						isWheelPart = true;
						break;
					}
				}
			}
			WCoord block_wpos = convertWcoord(WCoord(x, y, z));
			WCoord minpos = isWheelPart ? CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)) : CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2));
			WCoord maxpos = isWheelPart ? CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE, 0, BLOCK_SIZE)) : CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE, BLOCK_SIZE / 2));
			bool isHurt = false;
			for (int curz = minpos.z; curz <= maxpos.z; curz++)
			{
				for (int curx = minpos.x; curx <= maxpos.x; curx++)
				{
					for (int cury = minpos.y; cury <= maxpos.y; cury++)
					{
						int id = m_pWorld->getBlockID(curx, cury, curz);
						if (id == BLOCK_FIRE || BlockMaterialMgr::isLava(id)/*id == BLOCK_STILL_LAVA || id == BLOCK_FLOW_LAVA*/)
						{
							isHurt = true;
							curx = maxpos.x + 1;
							cury = maxpos.y + 1;
							curz = maxpos.z + 1;
						}

					}
				}
			}
			if (isHurt)
			{
				if (physicsPartsDef->IsCore)
				{
					m_nCoreDecHp += (int)atkdata.atkpoints;

				}
				else
				{
					if (m_Blocks[x][y][z]->m_iCurLife)
					{
						m_Blocks[x][y][z]->m_iCurLife -= (int)atkdata.atkpoints;
					}

					if (m_Blocks[x][y][z]->m_iCurLife <= 0)
					{
						m_Blocks[x][y][z]->m_iCurLife = 0;
						if (m_Blocks[x][y][z]->m_Block.getResID() > 0)
						{
							std::stringstream stringstream;
							stringstream << x << "_" << "" << y << "_" << z;
							std::string strPos;
							stringstream >> strPos;
							ContactDesc contactDesc;
							contactDesc.m_BlockPos = WCoord(x, y, z);
							contactDesc.m_BlockID = m_Blocks[x][y][z]->m_Block.getResID();
							contactDesc.m_bdestroy = true;
							m_ContactDescs[strPos] = contactDesc;
						}
					}
				}
			}
		}

		check_link_core();
	}
	else
	{
		if (actor)
		{
			int x = -1, y = -1, z = -1;
			intersect(actor, x, y, z);
			if (x >= 0 && y >= 0 && z >= 0)
			{
				std::vector<WCoord> destroyPos;
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				if (srcBlock.isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
				{
					return false;
				}

				destroyPos.push_back(WCoord(x, y, z));

				//如果击打的是门，需要将两个方块全部删除掉
				WCoord ng;
				int blockid = srcBlock.getResID();
				if (blockid == 812)
				{
					auto pmtl = dynamic_cast<DoorMaterial*>(g_BlockMtlMgr.getMaterial(blockid));
					if (pmtl)
					{
						bool isupper, isopen, mirror;
						int placedir = pmtl->ParseDoorDataInVehicle(m_pVehicleWorld, WCoord(x, y, z), isupper, isopen, mirror);
						ng = isupper ? WCoord(x, y - 1, z) : WCoord(x, y + 1, z);

						destroyPos.push_back(ng);
						LOG_INFO("attack door");
					}
				}

				ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
				if (!player)
				{
					player = static_cast<ClientPlayer*>(atkdata.fromplayer);
				}
				if (player)
				{
					if (player->canShowShotTip())
					{
						if (g_pPlayerCtrl)
							g_pPlayerCtrl->triggerVehicleshotTip();
					}
					else
					{
						auto effectComponent = player->getEffectComponent();
						if (effectComponent)
						{
							effectComponent->playBodyEffect(HUDFX_VEHICLESHOT);
						}
					}
				}
				playHurtSound();

				//if (blockid > 2000)
				//	blockid = 2000;
				for (int i = 0; i < (int)destroyPos.size(); i++)
				{
					//像门这样的方块，一次性需要清除2个，2个方块的blockid是一样的
					const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
					if (!physicsPartsDef)
					{
						return false;
					}

					if (physicsPartsDef->IsCore)
					{
						m_nCoreDecHp += (int)atkdata.atkpoints;
						check_link_core();
					}
					else
					{
						int xw = destroyPos[i].x;
						int yw = destroyPos[i].y;
						int zw = destroyPos[i].z;

						if (m_Blocks[xw][yw][zw]->m_iCurLife)
						{
							m_Blocks[xw][yw][zw]->m_iCurLife -= (int)atkdata.atkpoints;
						}

						if (m_Blocks[xw][yw][zw]->m_iCurLife <= 0)
						{
							m_Blocks[xw][yw][zw]->m_iCurLife = 0;
							if (blockid > 0)
							{
								std::stringstream stringstream;
								stringstream << xw << "_" << "" << yw << "_" << zw;
								std::string strPos;
								stringstream >> strPos;
								ContactDesc contactDesc;
								contactDesc.m_BlockPos = WCoord(xw, yw, zw);
								contactDesc.m_BlockID = blockid;
								contactDesc.m_bdestroy = true;
								m_ContactDescs[strPos] = contactDesc;
							}
							//判断是否联通
							check_link_core();
						}
					}
				}
			}
		}
		else if (!box.dim.isZero())
		{
			vector<WCoord> intersectPosList;
			getIntersectPosList(box, intersectPosList);
			for (int i = 0; i < (int)intersectPosList.size(); i++)
			{
				Block srcBlock = m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_Block;
				if (srcBlock.isEmpty() || m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_iCurLife == 0)
				{
					continue;
				}

				playHurtSound();

				int blockid = srcBlock.getResID();
				//if (blockid > 2000)
				//	blockid = 2000;
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
				if (!physicsPartsDef)
				{
					continue;
				}

				if (physicsPartsDef->IsCore)
				{
					m_nCoreDecHp += (int)atkdata.atkpoints;
					check_link_core();
				}
				else
				{
					if (m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_iCurLife)
					{
						m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_iCurLife -= (int)atkdata.atkpoints;
					}

					if (m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_iCurLife <= 0)
					{
						m_Blocks[intersectPosList[i].x][intersectPosList[i].y][intersectPosList[i].z]->m_iCurLife = 0;
						if (blockid > 0)
						{
							std::stringstream stringstream;
							stringstream << intersectPosList[i].x << "_" << "" << intersectPosList[i].y << "_" << intersectPosList[i].z;
							std::string strPos;
							stringstream >> strPos;
							ContactDesc contactDesc;
							contactDesc.m_BlockPos = WCoord(intersectPosList[i].x, intersectPosList[i].y, intersectPosList[i].z);
							contactDesc.m_BlockID = blockid;
							contactDesc.m_bdestroy = true;
							m_ContactDescs[strPos] = contactDesc;
						}
						//判断是否联通
						check_link_core();
					}
				}
			}
		}
	}
	resetAttib();
	for (int c = 0; c<(int)m_OtherRiddens.size(); c++)
	{
		if (m_OtherRiddens[c])
		{
			ClientActor* actorride = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OtherRiddens[c]);
			if (actorride)
			{
				ClientPlayer *player = dynamic_cast<ClientPlayer *>(actorride);
				if (player && actor)
				{
					player->setBeHurtTarget(actor);
				}
			}
		}
	}
	setBeHurtTarget(actor);

	return false;
}

void ActorVehicleAssemble::syncBlockDestroy(std::vector<WCoord> &blockpos)
{
	VehicleContainerMgr* containermgr = NULL;
	if (m_pVehicleWorld)
	{
		containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
	}
	int blocksize = blockpos.size();
	for (int i = 0; i<blocksize; i++)
	{
		// 		LOG_INFO("block destroy pos: %d %d %d", blockpos[i].x, blockpos[i].y, blockpos[i].z);
		if (blockpos[i].x < MAX_DIM_X && blockpos[i].y < MAX_DIM_Y && blockpos[i].z < MAX_DIM_Z)
		{
			//m_Blocks[blockpos[i].x][blockpos[i].y][blockpos[i].z]->m_iCurLife = 0;
			//m_Blocks[blockpos[i].x][blockpos[i].y][blockpos[i].z]->m_Block.setAll(0, 0);
			if (m_pVehicleWorld)
				m_pVehicleWorld->setBlockAll(blockpos[i], 0, 0);
			m_Blocks[blockpos[i].x][blockpos[i].y][blockpos[i].z]->m_iCurLife = 0;
			/*if (containermgr)
			{
			containermgr->destroyContainer(blockpos[i]);
			}*/
		}
	}
	/*if (blocksize)
	{
	reCreateVehicleAssemble();
	}*/
}

void ActorVehicleAssemble::syncBlock(std::vector<unsigned int> &blockpos, std::vector<BLOCK_DATA_TYPE> &blockdata, std::vector<unsigned int> &blockinfo, const FBSave::ChunkContainers *s)
{
	std::set<WCoord> oldcontainers; //清掉服务器不存在的container
	std::vector<WCoord> blockWcoord;
	int blocksize = blockpos.size();
	int lifezero = 0;
	VehicleContainerMgr* containermgr = NULL;
	if (m_pVehicleWorld)
	{
		containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
	}
	for (int i = 0; i<blocksize; i++)
	{
		unsigned int n = blockpos[i];
		WCoord pos = WCoord((int)(n >> 16), (int)(n & 255), (int)((n >> 8) & 255));
		blockWcoord.push_back(pos);
		unsigned int blockall = blockdata[i];
		if (pos.x < MAX_DIM_X && pos.y < MAX_DIM_Y && pos.z < MAX_DIM_Z)
		{
			int oldid = m_Blocks[pos.x][pos.y][pos.z]->m_Block.getResID();
			setBlockAll(pos, blockall & Block::SOC_BLOCKID_MASK, (blockall >> 12) & 15);
			m_Blocks[pos.x][pos.y][pos.z]->m_iCurLife = blockinfo[i] >> 16;
			m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdSelf = blockinfo[i] & 0xff;
			m_Blocks[pos.x][pos.y][pos.z]->m_Block.setAllData(blockall);
			if (m_Blocks[pos.x][pos.y][pos.z]->m_iCurLife <= 0 || oldid != m_Blocks[pos.x][pos.y][pos.z]->m_Block.getResID())
			{
				lifezero++;
			}
			if (containermgr && containermgr->getContainer(pos)) oldcontainers.insert(pos);
		}
	}

	if (s)
	{
		for (int i = 0; i < (int)s->containers()->size(); i++)
		{
			const FBSave::ChunkContainer *src = s->containers()->Get(i);
			WorldContainer *obj = CreateWorldVehicleContainerFromChunkContainer(src);
			if (obj)
			{
				if (obj->load(src->container()) && containermgr && m_pVehicleWorld)
				{
					containermgr->addContainerByServer(obj);
					if (obj->remoteMarkBlockForUpdate())
					{
						m_pVehicleWorld->markBlockForUpdate(obj->m_BlockPos, false);
					}
					std::set<WCoord>::iterator iter = oldcontainers.find(obj->m_BlockPos);
					if (iter != oldcontainers.end()) oldcontainers.erase(iter);
				}
				else ENG_DELETE(obj);
			}
		}
	}
	std::set<WCoord>::iterator iter = oldcontainers.begin();
	for (; iter != oldcontainers.end(); iter++)
	{
		if (containermgr)
			containermgr->destroyContainer(*iter);
	}

	if (blocksize && lifezero)
	{
		//@tangjie 延迟创建物理
		//m_mustReCreatePhysics = true;
		//reCreateVehicleAssemble();
	}
	else
	{
		updateChassisEntity(blockWcoord);
	}
}

void ActorVehicleAssemble::syncAllBlock(unsigned char *blockdata, int datalen, int blockidVersion)
{
	SyncVehicleBlock sync_blocks[MAX_DIM_X][MAX_DIM_Y][MAX_DIM_Z];

	if (datalen >= sizeof(sync_blocks))
	{
		memcpy(sync_blocks, blockdata, sizeof(sync_blocks));
	}
	else
	{
		memset(sync_blocks, 0, sizeof(sync_blocks));
		memcpy(sync_blocks, blockdata, datalen);
	}

	for (int x = 0; x < MAX_DIM_X; x++)
	{
		for (int y = 0; y < MAX_DIM_Y; y++)
		{
			for (int z = 0; z < MAX_DIM_Z; z++)
			{
				m_Blocks[x][y][z]->m_iCurLife = sync_blocks[x][y][z].m_Info >> 16;
				m_Blocks[x][y][z]->m_GroupIdSelf = sync_blocks[x][y][z].m_Info & 0xff;
				if (1 == blockidVersion)
				{
					m_Blocks[x][y][z]->m_Block.setAllData(sync_blocks[x][y][z].m_Block.getAll());
				}
				else if (0 == blockidVersion)
				{
					m_Blocks[x][y][z]->m_Block.setAllDataLoad(sync_blocks[x][y][z].m_Block.getAll(), 0);
				}
			}
		}
	}

	if (datalen > sizeof(sync_blocks))
	{
		VehicleContainerMgr* containermgr = NULL;
		if (m_pVehicleWorld)
		{
			containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		}
		std::set<WCoord> oldcontainers; //清掉服务器不存在的container
		const FBSave::ChunkContainers* s = NULL;
		s = flatbuffers::GetRoot<FBSave::ChunkContainers>(blockdata + sizeof(sync_blocks));
		if (s)
		{
			for (int i = 0; i < (int)s->containers()->size(); i++)
			{
				const FBSave::ChunkContainer* src = s->containers()->Get(i);
				WorldContainer* obj = CreateWorldVehicleContainerFromChunkContainer(src);
				if (obj)
				{
					if (obj->load(src->container()) && containermgr && m_pVehicleWorld)
					{
						containermgr->addContainerByServer(obj);
						if (obj->remoteMarkBlockForUpdate())
						{
							m_pVehicleWorld->markBlockForUpdate(obj->m_BlockPos, false);
						}
						std::set<WCoord>::iterator iter = oldcontainers.find(obj->m_BlockPos);
						if (iter != oldcontainers.end()) oldcontainers.erase(iter);
					}
					else ENG_DELETE(obj);
				}
			}
		}
		std::set<WCoord>::iterator iter = oldcontainers.begin();
		for (; iter != oldcontainers.end(); iter++)
		{
			if (containermgr)
				containermgr->destroyContainer(*iter);
		}
	}
	reCreateVehicleAssemble();
}

void ActorVehicleAssemble::detachAllPhysActor()
{
	VehicleAssembleLocoMotion *locoVehicle_me = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (m_BindVehilcIDs.size())
	{
		auto son = m_BindVehilcIDs.begin();
		while (son != m_BindVehilcIDs.end())
		{
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(son->second));
			if (vehicle)
			{
				vehicle->m_mustReCreatePhysics = true;
				if (m_isReCreate)
					vehicle->m_createCount = m_createCount + 1;
				else
					vehicle->m_createCount = m_createCount + 2;
				vehicle->detachAllPhysActor();
			}
			son++;
		}
	}
	if (locoVehicle_me)
		locoVehicle_me->detachPhysActor();
	m_mustReCreatePhysics = true;
	m_createCount = 0;
}

long long ActorVehicleAssemble::getRootFatherVehilcID()
{
	long long  father = m_FatherVehilcID;
	while (father)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(father));
		if (vehicle)
		{
			if (vehicle->m_FatherVehilcID)
			{
				father = vehicle->m_FatherVehilcID;
			}
			else
			{
				break;
			}
		}
		else
		{
			break;
		}
	}
	return father;
}

WCoord ActorVehicleAssemble::getMaxBlockCountSingleCoord()
{
	return m_MaxVertBlock - m_MinVertBlock;
}

void ActorVehicleAssemble::SetEnableWaterMask(bool value) 
{
	m_EnableWaterMask = value;

}

void ActorVehicleAssemble::reCreateVehicleAssemble(bool checkfather)
{
	//如果破坏了驾驶座，不应该重新生成载具，直接在下一个tick销毁
	if (m_destoryDriverSeat)
	{
		setNeedClear(0);
		return;
	}

	VehicleAssembleLocoMotion* loco = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (m_FatherVehilcID && checkfather)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_FatherVehilcID));
		if (vehicle)
		{
			VehicleAssembleLocoMotion* loco_ = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
			if (loco->m_bHaveParent || (loco_ && loco_->m_PhysActor == NULL))
			{
				vehicle->reCreateVehicleAssemble();
				return;
			}
		}
	}
	if (m_pVehicleWorld)
		m_pVehicleWorld->setInit(false);
	//地图物理稳定度计算
	if (getBlockNum() > 0)
	{
		int num = getBlockNum() / 10;
		if (num < 1)num = 1;
		g_all_physxitem -= num;
	}
	int preChassisUpdatePosSize = loco->m_ChassisUpdatePos.size();
	WPreciseCoord *pChassisUpdatePos = NULL;
	Rainbow::Quaternionf *pChassisUpdateRot = NULL;
	if (preChassisUpdatePosSize)
	{
		pChassisUpdatePos = new WPreciseCoord[preChassisUpdatePosSize];
		pChassisUpdateRot = new Rainbow::Quaternionf[preChassisUpdatePosSize];
	}
	for (int i = 0; i<preChassisUpdatePosSize; i++)
	{
		pChassisUpdatePos[i] = loco->m_ChassisUpdatePos[i].m_PPosition;
		pChassisUpdateRot[i] = loco->m_ChassisUpdatePos[i].m_RotateQuat;
	}
	int preWheelUpdatePosSize = loco->m_WheelUpdatePos.size();
	WPreciseCoord *pWheelUpdatePos = NULL;
	Rainbow::Quaternionf *pWheelUpdateRot = NULL;
	if (preWheelUpdatePosSize)
	{
		pWheelUpdatePos = new WPreciseCoord[preWheelUpdatePosSize];
		pWheelUpdateRot = new Rainbow::Quaternionf[preWheelUpdatePosSize];
	}
	for (int i = 0; i<preWheelUpdatePosSize; i++)
	{
		pWheelUpdatePos[i] = loco->m_WheelUpdatePos[i].m_PPosition;
		pWheelUpdateRot[i] = loco->m_WheelUpdatePos[i].m_RotateQuat;
	}

	Rainbow::Vector3f lastV = 0.f;
	float acce = 0.f;
	float brake = 0.f;
	float left = 0.f;
	float right = 0.f;
	EGearType currentGear = EGearType::eFIRST;
	if (loco->m_PhysActor && m_BindVehilcIDs.size() == 0 && m_FatherVehilcID == 0)
	{
		lastV = loco->m_PhysActor->GetLinearVelocity();
		acce = loco->getAccelAnalog();
		brake = loco->getBrakeAnalog();
		left = loco->getSteerLeftAnalog();
		right = loco->getSteerRightAnalog();
		currentGear = loco->getCurrentGear();
	}
	for (int i = 0; i<(int)m_VehicleCreateMeshes.size(); i++)
	{
		for (int j = 0; j<(int)m_VehicleCreateMeshes[i].mRawMeshesWheel.size(); j++)
		{
			ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheel[j].mVerts, kMemPhysics);
			ENG_FREE_LABEL(m_VehicleCreateMeshes[i].mRawMeshesWheelCollider[j].mVerts, kMemPhysics);
		}
		m_VehicleCreateMeshes[i].mRawMeshesWheel.clear_dealloc();
		m_VehicleCreateMeshes[i].mRawMeshesWheelCollider.clear_dealloc();
	}
	m_VehicleCreateMeshes.clear();
	//对抓取的载具进行重新生成
	if (m_BindVehilcIDs.size())
	{
		detachAllPhysActor();
	}
	clear();
	create(m_pWorld);
	m_isReCreate = true;
	loco->detachPhysActor();
	loco->checkPhysWorld();
	//loco->attachPhysActors(m_BoxMeshesChassis, m_RawMeshesWheel, m_VehicleCreateParams, m_JointCreateParams, m_AddThruster);
	std::vector<WorldContainer *> chunkcontainers;
	if (m_pVehicleWorld)
	{
		VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		if (containermgr)
		{
			containermgr->getContainersVec(chunkcontainers);
		}
		for (int i = 0; i < (int)chunkcontainers.size(); i++)
		{
			chunkcontainers[i]->leaveWorld();
		}
		loco->attachPhysVehicleArticulations(m_VehicleCreateMeshes, m_VehicleCreateParams, m_JointCreateParams, m_DynamicCreateParams, m_Ropes, getFatherVehicleDynamicActor(), getFatherVehicleArticulation());
		if (getRiddenByActorID() > 0 && getRiddenByActor() == g_pPlayerCtrl)
			loco->setFocus();
		loco->calculateCarPos();
		for (int i = 0; i<(int)chunkcontainers.size(); i++)
		{
			chunkcontainers[i]->enterWorld(m_pVehicleWorld);

			//T铰链不需要初始化的时候，自动转向
			int tBlockId = getBlockID(chunkcontainers[i]->m_BlockPos);
			if (tBlockId != BLOCK_JOINT_T_REVOLUTE)
				chunkcontainers[i]->startVehicle();
		}
	}
	else
	{
		loco->attachPhysVehicleArticulations(m_VehicleCreateMeshes, m_VehicleCreateParams, m_JointCreateParams, m_DynamicCreateParams, m_Ropes, getFatherVehicleDynamicActor(), getFatherVehicleArticulation());
		if (getRiddenByActorID() > 0 && getRiddenByActor() == g_pPlayerCtrl)
			loco->setFocus();
		loco->calculateCarPos();
	}
	if (loco->m_PhysActor)
	{
		Rainbow::RigidDynamicActor* dynamicActor = dynamic_cast<Rainbow::RigidDynamicActor*>(loco->m_PhysActor);
		if (!dynamicActor->GetKinematicMode())
			loco->m_PhysActor->SetLinearVelocity(lastV);

		loco->setAccelAnalog(acce);
		loco->setBrakeAnalog(brake);
		loco->setSteerLeftAnalog(left);
		loco->setSteerRightAnalog(right);
		loco->forceGearChange(currentGear);
	}
	if (m_pVehicleWorld)
		m_pVehicleWorld->setInit(true);
	for (int i = 0; i <(int)loco->m_ChassisUpdatePos.size(); i++)
	{
		if ((int)i < preChassisUpdatePosSize)
		{
			loco->m_ChassisUpdatePos[i].m_PUpdatePos = pChassisUpdatePos[i].toWorldPrecisePos();
			loco->m_ChassisUpdatePos[i].m_UpdateRot = pChassisUpdateRot[i];
			loco->m_ChassisUpdatePos[i].m_PPosition = pChassisUpdatePos[i];
			loco->m_ChassisUpdatePos[i].m_RotateQuat = pChassisUpdateRot[i];
			loco->m_ChassisUpdatePos[i].m_PPrevPosition = pChassisUpdatePos[i];
			loco->m_ChassisUpdatePos[i].m_PrevRotateQuat = pChassisUpdateRot[i];
		}
	}
	for (int i = 0; i<(int)loco->m_WheelUpdatePos.size(); i++)
	{
		if (i < preWheelUpdatePosSize)
		{
			loco->m_WheelUpdatePos[i].m_PUpdatePos = pWheelUpdatePos[i].toWorldPrecisePos();
			loco->m_WheelUpdatePos[i].m_UpdateRot = pWheelUpdateRot[i];
			loco->m_WheelUpdatePos[i].m_PPosition = pWheelUpdatePos[i];
			loco->m_WheelUpdatePos[i].m_RotateQuat = pWheelUpdateRot[i];
			loco->m_WheelUpdatePos[i].m_PPrevPosition = pWheelUpdatePos[i];
			loco->m_WheelUpdatePos[i].m_PrevRotateQuat = pWheelUpdateRot[i];
		}
	}
	OGRE_DELETE_ARRAY(pWheelUpdatePos);
	OGRE_DELETE_ARRAY(pWheelUpdateRot);
	OGRE_DELETE_ARRAY(pChassisUpdatePos);
	OGRE_DELETE_ARRAY(pChassisUpdateRot);
	resetEntityPos(0);
	loco->m_hasPhysActor = true;
	//对抓取的载具进行重新生成
	if (m_BindVehilcIDs.size())
	{
		auto son = m_BindVehilcIDs.begin();
		while (son != m_BindVehilcIDs.end())
		{
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(son->second));
			if (vehicle)
			{
				vehicle->reCreateVehicleAssemble(false);
			}
			son++;
		}
	}
	else
	{
		long long  father = getRootFatherVehilcID();
		if (father)
		{
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(father));
			if (vehicle)
			{
				VehicleAssembleLocoMotion* loco_ = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
				if (loco_ && loco_->m_PhysActorVec.size())
				{
					Matrix4x4f m;
					m.SetTR(loco_->m_Position.toVector3(), loco_->m_RotateQuat);
					loco_->m_VehicleArticulation->setPose(m);
				}
			}
		}
	}

	m_mustReCreatePhysics = false;
	m_createCount = 0;
}

bool ActorVehicleAssemble::check_sepcial_block_destroyed(int blockID, const bool& isdrop)
{
	/*int check_num = 1;
	if (blockID == BLOCK_DRIVERS_SEAT)
	{
	check_num = 2;
	}*/
	int maxlifeAll = 0;
	int maxlife = 0;
	int declife = 0;
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockID);
	if (physicsPartsDef)
	{
		maxlife = physicsPartsDef->Life;
		maxlifeAll = physicsPartsDef->Life;
		if (physicsPartsDef->IsCore)
		{
			declife = m_nCoreDecHp;
		}
	}

	bool isDestroyed = false;
	WCoord pos_core(-1, -1, -1);
	for (int i = 0; i<MAX_DIM_X; i++)
	{
		for (int j = 0; j<MAX_DIM_Y; j++)
		{
			for (int m = 0; m<MAX_DIM_Z; m++)
			{
				//半砖特殊判断
				int slabNum = 1;
				Block srcBlock = m_Blocks[i][j][m]->m_Block;
				SlabMaterial *pmtl = dynamic_cast<SlabMaterial *>(g_BlockMtlMgr.getMaterial(srcBlock.getResID()));
				if (pmtl&&(srcBlock.getData() & 15) >= 2)
				{
					slabNum = 2;
				}

				if (m_Blocks[i][j][m]->m_Block.getResID() == blockID)
				{
					if (BLOCK_DRIVERS_SEAT != blockID && BLOCK_BOAT_RUDDER != blockID)
					{
						if (m_Blocks[i][j][m]->m_iCurLife > 0)
						{
							declife += (maxlife - m_Blocks[i][j][m]->m_iCurLife);
						}
						else
						{
							isDestroyed = true;
							goto breakfor;
						}
					}
				}
				else if ((blockID == BLOCK_DRIVERS_SEAT  || BLOCK_BOAT_RUDDER == blockID) && m_Blocks[i][j][m]->m_Block.getResID())
				{
					const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(m_Blocks[i][j][m]->m_Block.getResID());
					if (physicsPartsDef && physicsPartsDef->IsCoreArmor && m_Blocks[i][j][m]->m_iCurLife > 0)
					{
						maxlifeAll += (physicsPartsDef->Life*slabNum);
					}
				}
			}
		}
	}

breakfor:
	if (declife >= maxlifeAll)
	{
		isDestroyed = true;
	}

	if (isDestroyed)
	{
		for (int i = 0; i<MAX_DIM_X; i++)
		{
			for (int j = 0; j<MAX_DIM_Y; j++)
			{
				for (int m = 0; m<MAX_DIM_Z; m++)
				{
					if (m_Blocks[i][j][m]->m_Block.getResID() == blockID && m_Blocks[i][j][m]->m_iCurLife > 0)
					{
						m_Blocks[i][j][m]->m_iCurLife = 0;
						std::stringstream stringstream;
						stringstream << i << "_" << j << "_" << m;
						std::string strPos;
						stringstream >> strPos;
						ContactDesc contactDesc;
						contactDesc.m_BlockPos = WCoord(i, j, m);
						contactDesc.m_BlockID = blockID;
						contactDesc.m_bdestroy = true;
						if ((blockID == BLOCK_BOAT_RUDDER || blockID == BLOCK_BOAT_THRUSTER) && (m_Blocks[i][j][m]->m_Block.getData() & 8) == 0)
						{
							contactDesc.m_bIsDrop = false;
						}
						else {
							contactDesc.m_bIsDrop = isdrop;
						}
						
						m_ContactDescs[strPos] = contactDesc;
					}
				}
			}
		}
		return true;
	}
	return false;
}
void ActorVehicleAssemble::check_dismount(const WCoord& blockpos)
{	
	if ( isRudder() )
	{
		auto actor = getRiddenByActor();
		if (actor)
		{
			auto seatPos = getRiddenBindSeatPos(actor);
			Block* srcBlock = &m_Blocks[seatPos.x][seatPos.y][seatPos.z]->m_Block;
			if (srcBlock->isEmpty() || m_Blocks[seatPos.x][seatPos.y][seatPos.z]->m_iCurLife == 0)
			{
				return;
			}
			if (!(srcBlock->getData() & 8))
			{
				return;
			}
			int dir = srcBlock->getData() % 4;
			WCoord standOffset(0, 0, 0);

			if (dir == DIR_POS_Z)
			{
				standOffset.z = -1;
			}
			else if (dir == DIR_NEG_Z)
			{
				standOffset.z = 1;
			}
			else if (dir == DIR_POS_X)
			{
				standOffset.x = -1;
			}
			else if (dir == DIR_NEG_X)
			{
				standOffset.x = 1;
			}
			seatPos = seatPos + standOffset;
			if (seatPos.x >= MAX_DIM_X || seatPos.x < 0) return;
			if (seatPos.y >= MAX_DIM_Y || seatPos.y < 0) return;
			if (seatPos.z >= MAX_DIM_Z || seatPos.z < 0) return;
			
			seatPos.y -= 1;
			if (seatPos == blockpos)
			{
				auto player = dynamic_cast<PlayerControl*>(actor);
				if (player)
				{
					player->dismountActor();
				}
			}
		}
	}
}



void ActorVehicleAssemble::check_link_core(const bool& isdrop)
{
	if (isRudder())
	{
		//船舵模式
		check_sepcial_block_destroyed(BLOCK_BOAT_RUDDER, isdrop);
	}
	else {
		//座位模式
		check_sepcial_block_destroyed(BLOCK_DRIVERS_SEAT, isdrop);
	}
	//判断是否联通
	bool check_block[MAX_DIM_X][MAX_DIM_Y][MAX_DIM_Z];
	WCoord pos_core(-1, -1, -1);
	std::list<WCoord> check_pos;
	for (int i = 0; i<MAX_DIM_X; i++)
	{
		for (int j = 0; j<MAX_DIM_Y; j++)
		{
			for (int m = 0; m<MAX_DIM_Z; m++)
			{
				if ((m_Blocks[i][j][m]->m_Block.getResID() == BLOCK_DRIVERS_SEAT || m_Blocks[i][j][m]->m_Block.getResID() == BLOCK_BOAT_RUDDER) && m_Blocks[i][j][m]->m_iCurLife > 0)
				{
					pos_core.x = i;
					pos_core.y = j;
					pos_core.z = m;
					check_block[i][j][m] = true;
					check_pos.push_back(pos_core);
				}
				else
				{
					check_block[i][j][m] = false;
				}
			}
		}
	}
	//检查和机械核心分离的部件要断开

	//用list来代替递归
	while (check_pos.size())
	{
		WCoord pos = *check_pos.begin();
		check_pos.pop_front();
		for (int dir = 0; dir<6; dir++)
		{
			int x2 = pos.x + g_DirectionCoord[dir].x;
			int y2 = pos.y + g_DirectionCoord[dir].y;
			int z2 = pos.z + g_DirectionCoord[dir].z;
			if ((x2 >= 0 && x2 <MAX_DIM_X)
				&& (y2 >= 0 && y2 <MAX_DIM_Y)
				&& (z2 >= 0 && z2 <MAX_DIM_Z)
				&& check_block[x2][y2][z2] == false
				&& m_Blocks[x2][y2][z2]->m_iCurLife > 0)
			{
				WCoord pos1;
				check_block[x2][y2][z2] = true;
				pos1.x = x2;
				pos1.y = y2;
				pos1.z = z2;
				check_pos.push_back(pos1);
			}
		}
	}
	for (int i = 0; i<MAX_DIM_X; i++)
	{
		for (int j = 0; j<MAX_DIM_Y; j++)
		{
			for (int m = 0; m<MAX_DIM_Z; m++)
			{
				if (check_block[i][j][m] == false && m_Blocks[i][j][m]->m_Block.getAll() && m_Blocks[i][j][m]->m_iCurLife > 0)
				{
					m_Blocks[i][j][m]->m_iCurLife = 0;
					Block srcBlock = m_Blocks[i][j][m]->m_Block;
					int blockid = srcBlock.getResID();
					if (blockid > 0)
					{
						std::stringstream stringstream;
						stringstream << i << "_" << j << "_" << m;
						std::string strPos;
						stringstream >> strPos;
						ContactDesc contactDesc;
						contactDesc.m_BlockPos = WCoord(i, j, m);
						contactDesc.m_BlockID = blockid;
						if ((blockid == BLOCK_BOAT_RUDDER || blockid == BLOCK_BOAT_THRUSTER) && (m_Blocks[i][j][m]->m_Block.getData() & 8) == 0)
						{
							contactDesc.m_bIsDrop = false;
						}
						else {
							contactDesc.m_bIsDrop = isdrop;
						}
						m_ContactDescs[strPos] = contactDesc;
					}
				}
			}
		}
	}
}

void ActorVehicleAssemble::resetAttib()
{
	if (getAttrib())
	{
		getAttrib()->initHP(m_LifeCurrent);
		getAttrib()->initMaxHP(m_LifeLimit);
	}
}

void ActorVehicleAssemble::onEnterOrLeaveWorld(bool enter)
{
	if (enter)
		ActorVehicleAssemble::m_bEnterWorld = true;
	WCoord start = WCoord(0, 0, 0);
	WCoord dim = WCoord(MAX_DIM_X - 1, MAX_DIM_Y - 1, MAX_DIM_Z - 1);

	if (start.x > start.x + dim.x)
		start.x = start.x + dim.x;

	if (start.y > start.y + dim.y)
		start.y = start.y + dim.y;

	if (start.z > start.z + dim.z)
		start.z = start.z + dim.z;

	dim.x = Rainbow::Abs(dim.x);
	dim.y = Rainbow::Abs(dim.y);
	dim.z = Rainbow::Abs(dim.z);

	bool have_transfercore = false;
	for (int y = 0; y <= dim.y; y++)
	{
		for (int z = 0; z <= dim.z; z++)
		{
			for (int x = 0; x <= dim.x; x++)
			{
				WCoord pos(start.x + x, start.y + y, start.z + z);
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				int blockid = srcBlock->getResID();

				/*if(blockid == 701 ||  blockid == 700){
				int a = 0;
				int b = a;
				}*/

				BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl)
				{
					if (!m_pWorld->isRemoteMode())
					{
						if (m_NeedClearTicks == 0 || BLOCK_TRANSFERCORE != blockid)
						{
							if (BLOCK_TRANSFERCORE == blockid)
								have_transfercore = true;
							if (enter) {
								mtl->DoOnBlockAdded(m_pVehicleWorld, pos);
							}
							else {
								mtl->DoOnBlockRemoved(m_pVehicleWorld, pos, blockid, srcBlock->getData());
							}
						}
					}
				}
			}
		}
	}

	if (have_transfercore)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_writeToJsonFile",
			SandboxContext(nullptr)
			.SetData_Number("srcId", -1)
			.SetData_Bool("isLeaveWorld", true));
	}

	ActorVehicleAssemble::m_bEnterWorld = false;
}

bool ActorVehicleAssemble::canBeHurt()
{
	if (GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_VEHICLE_HURT) == 0)
	{
		return false;
	}
	return true;
}

bool ActorVehicleAssemble::isSonVehicle(WORLD_ID vehicle)
{
	auto son = m_BindVehilcIDs.begin();
	while (son != m_BindVehilcIDs.end())
	{
		if (son->second == vehicle)
		{
			return true;
		}
		else
		{
			ActorVehicleAssemble* pvehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(son->second));
			if (pvehicle && pvehicle->isSonVehicle(vehicle))
			{
				return true;
			}
			son++;
		}
	}
	return false;
}

void ActorVehicleAssemble::onContact(Vector3f point, Vector3f impulse, const Collider *contactShape)
{
	//碰撞优化，tick清零允许处理碰撞逻辑
	if (m_collideTick <= 0)
		m_collideTick = 1;
	else
		return;

	if (!canBeHurt())
	{
		return;
	}
	bool isDynamic = false;
	ClientActor *actor = NULL;
	if (contactShape)
	{
		ActorBodyComponentBase* body = contactShape->GetBody();
		isDynamic = body != nullptr;
		actor = reinterpret_cast<ClientActor*>(contactShape->GetUserData());
	}
	if (m_pWorld->isRemoteMode() || isDead())
	{
		return;
	}

	if (Rainbow::Abs(impulse.x) > 10.0 || Rainbow::Abs(impulse.y) > 10.0 || Rainbow::Abs(impulse.z) > 10.0)
	{
		VehicleAssembleLocoMotion *pLoco = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
		float attack_point = 0;
		if (pLoco && pLoco->m_Car)
		{
			float m1 = pLoco->m_Car->GetRigidbody()->GetMass();
			float v1 = pLoco->m_Car->GetRigidbody()->GetVelocity().Length();
			float m2 = 0;
			float v2 = 0;
			if (isDynamic)
			{
				//m2 =  dynamic_cast<PxRigidDynamic*>(pxActor)->getMass();
				//v2 =  dynamic_cast<PxRigidDynamic*>(pxActor)->getLinearVelocity().magnitude();
				if (actor)
				{
					if (actor->getObjType() == OBJ_TYPE_ROLE)
					{
						ClientPlayer *player = dynamic_cast<ClientPlayer*>(actor);
						if (player)
						{
							float mass = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.mass_role*GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.mass_scale;;
							//m2 =  dynamic_cast<PxRigidDynamic*>(pxActor)->getMass();
							//v2 =  dynamic_cast<PxRigidDynamic*>(pxActor)->getLinearVelocity().magnitude();
							PlayerLocoMotion* playerlocomote = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
							if (playerlocomote)
							{
								m2 = mass;//playerlocomote->getRoleController()->getPxController()->getActor()->getMass();
								auto physType = playerlocomote->getPhysType();
								if(physType == RolePhysType::PHYS_ROLECONTROLLER && playerlocomote->getRoleController())
									v2 = playerlocomote->getRoleController()->GetVelocity().Length();
								else if (physType == RolePhysType::PHYS_RIGIDBODY && playerlocomote->getRigidDynamicActor())
								{
									v2 = playerlocomote->getRigidDynamicActor()->GetLinearVelocity().Length();
								}
							}
						}
					}
					else if (actor->getObjType() == OBJ_TYPE_VEHICLE)
					{
						ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
						if (vehicle)
						{
							VehicleAssembleLocoMotion* vehicle_mot = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
							m2 = vehicle_mot->m_Car->GetRigidbody()->GetMass();
							v2 = vehicle_mot->m_Car->GetRigidbody()->GetVelocity().Length();
						}
					}

					// 如果是角色或者物理生物
					PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
					ClientMob* mob = dynamic_cast<ClientMob*>(actor);
					if (mob || (loc && loc->m_hasPhysActor))
					{
						if (actor->canBePushed())
						{
							collideWithActor(actor);
						}
					}
				}
			}

			float x = (m1*v1*v1 + m2*v2*v2)* GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.attack_rate;
			if (m2 > 0)
				attack_point = x*m2 / (m1 + m2);
			else
				attack_point = 0.5f * x;
			if (ActorAttrib::m_DisplayHurtNumber)
			{
				char msgbuf[256];
				sprintf(msgbuf, "onContact hurt: m1=%f, m2=%f, v1=%f, v2=%f, attack_point=%d", m1, m2, v1, v2, (int)attack_point);
				GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->sendChat(msgbuf);
			}

			if (v1 >= GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_effect_speed
				|| v2 >= GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_effect_speed)
			{
				m_pWorld->getEffectMgr()->playParticleEffectAsync(GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_effect.c_str(), WCoord((int)point.x, (int)point.y, (int)point.z), 40, 0, 0, true, 0);
				m_pWorld->getEffectMgr()->playSoundAtActor(this, GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.collide_sound.c_str(), 1.0f, 1.0f);
			}
		}

		MINIW::WorldRay ray;
		ray.m_Origin.x = (Rainbow::WPOS_T)point.x * 10;
		ray.m_Origin.y = (Rainbow::WPOS_T)point.y * 10;
		ray.m_Origin.z = (Rainbow::WPOS_T)point.z * 10;

		//ray.m_Origin = point;
		ray.m_Dir.x = impulse.x;
		ray.m_Dir.y = impulse.y;
		ray.m_Dir.z = impulse.z;

		ray.m_Range = 7 * BLOCK_FSIZE;
		int blockID = 0;
		float t = 0;
		int x = -1, y = -1, z = -1;
		bool ret = intersect(ray, t, blockID, x, y, z);
		if ((0 <= x && x < MAX_DIM_X) && (0 <= y  && y < MAX_DIM_Y) && (0 <= z  &&  z < MAX_DIM_Z))
		{
			//Rainbow::HashTable<WCoord, int, WCoordHashCoder> check_woord(16);
			Block* srcBlock = &m_Blocks[x][y][z]->m_Block;
			int blockid = srcBlock->getResID();
			if (blockid == BLOCK_CLAW)
			{
				/*if (srcBlock->getData() & 8)
				{
				//抓住生物或者载具
				ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
				if (vehicle && vehicle->m_FatherVehilcID == 0 && getObjId() != vehicle->getObjId() && !vehicle->isSonVehicle(getObjId()) && !isSonVehicle(vehicle->getObjId()))
				{
				vehicle->m_FatherVehilcID = getObjId();
				vehicle->m_FatherVehilcPos = WCoord(x, y, z);
				vehicle->m_mustReCreatePhysics = true;
				m_BindVehilcIDs[(x<<10) + (y<<5) + z] = vehicle->getObjId();
				}
				}*/
			}
			else if (blockid > 0 && m_Blocks[x][y][z]->m_iCurLife > 0)
			{
				//return;
				//碰撞回调函数，车身的方块触发碰撞机制
				WCoord wcoordcollide(x, y, z);
				auto mtl = g_BlockMtlMgr.getMaterial(blockid);
				if (mtl)
				{
					mtl->DoOnActorCollidedWithBlock(m_pVehicleWorld, wcoordcollide, actor);
				}

				if (getLocoMotion()->m_OnGround && !getFlying())
				{
					WCoord down_pos = CoordDivBlock(wcoordcollide) + WCoord(0, -1, 0);
					BlockMaterial *mtl = m_pWorld->getBlockMaterial(down_pos);
					if (mtl)
					{
						mtl->DoOnActorWalking(m_pWorld, down_pos, this);
					}

					ActorLiving* living = dynamic_cast<ActorLiving*>(this);
					if (living != NULL)
					{ // 非投射物类型
						WCoord curPos = CoordDivBlock(wcoordcollide);
						BlockMaterial *curMtl = m_pWorld->getBlockMaterial(curPos);
						if (curMtl != NULL) { // 观察者事件接口/碰撞事件
											  //ObserverEvent_Block obevent(curPos.x, curPos.y, curPos.z, curMtl->getBlockResID(), m_OwnerActor->getObjId());
											  //GetObserverEventManager().OnTriggerEvent("Block.ActorCollide", &obevent);
						}
					}
				}

				if (Rainbow::Timer::getSystemTick() - m_lastContactTick > 20)
				{
					if (blockid == BLOCK_DRIVERS_SEAT || blockid == BLOCK_BOAT_RUDDER)
					{
						m_nCoreDecHp += (int)attack_point;
						check_link_core();
					}
					else
					{
						m_Blocks[x][y][z]->m_iCurLife -= (int)attack_point;
						m_lastContactTick = Rainbow::Timer::getSystemTick();
						if (m_Blocks[x][y][z]->m_iCurLife <= 0)
						{
							m_Blocks[x][y][z]->m_iCurLife = 0;
							std::stringstream stringstream;
							stringstream << x << "_" << "" << y << "_" << z;
							std::string strPos;
							stringstream >> strPos;
							ContactDesc contactDesc;
							contactDesc.m_BlockPos = WCoord(x, y, z);
							contactDesc.m_BlockID = blockid;
							contactDesc.m_bdestroy = true;
							m_ContactDescs[strPos] = contactDesc;
							check_link_core();
						}
					}
					for (int c = 0; c<(int)m_OtherRiddens.size(); c++)
					{
						if (m_OtherRiddens[c])
						{
							ClientActor* actorride = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_OtherRiddens[c]);
							if (actor)
							{
								ClientPlayer *player = dynamic_cast<ClientPlayer *>(actorride);
								if (player)
								{
									player->setBeHurtTarget(NULL);
								}
							}
						}
					}
				}
			}
		}
	}
}

ActorVehicleAssemble *ActorVehicleAssemble::create(World *pworld, WCoord start, WCoord dim, int dir, ContainerWorkshop* pContainerWorkshop)
{
	ActorVehicleAssemble* vehicle = SANDBOX_NEW(ActorVehicleAssemble);
	vehicle->init(pworld, start, dim, dir, pContainerWorkshop);
	if (vehicle->getBlockNum() <= 0 || vehicle->getChassisBlockNum() <= 0 || vehicle->getWheelBlockNum() >= MAX_NB_WHEELS)
	{
		OGRE_DELETE(vehicle);
		return NULL;
	}
	WCoord pos = start;

	//WCoord center = WCoord(start.x + MAX_DIM_X / 2, start.y + MAX_DIM_Y, start.z + MAX_DIM_Z / 2);
	// 玩法或创造模式  机械生成位置为工作台右前方
	// 其他模式 生成位置为block位置
	float offsetX = dim.x < 0 ? -2.f : 2.f;
	float offsetZ = dim.z < 0 ? -2.f : 2.f;
	if (GetWorldManagerPtr()->isGodMode())
	{
		if (DIR_NEG_X == dir || DIR_POS_X == dir)
		{
			pos.x = pos.x + dim.x / 2;
			pos.z = pos.z - dim.z / 2 - (int)offsetZ;
		}
		else if (DIR_NEG_Z == dir || DIR_POS_Z == dir)
		{
			pos.x = pos.x - dim.x / 2 - (int)offsetX;
			pos.z = pos.z + dim.z / 2;
		}

		pos.y += vehicle->getOffsetYWorkshopAndWorld();

		//pos = pos * BLOCK_SIZE;
	}
	else {
		pos.x = pos.x + dim.x / 2;
		pos.z = pos.z + dim.z / 2;

		pos.y += vehicle->getOffsetYWorkshopAndWorld();

		//pos = pos * BLOCK_SIZE;
	}
	pos = BlockCenterCoord(pos);
	static_cast<ClientActorMgr*>(pworld->getActorMgr())->spawnActor(vehicle, pos.x, pos.y, pos.z, 0, 0);
	vehicle->refreshVehicleExtentForInit();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_updateTransferVehicle",
		SandboxContext(nullptr)
		.SetData_Number("curMapID", pworld->getCurMapID())
		.SetData_UserObject("vehicle", vehicle));

	auto centerPos = vehicle->getChassisMassCenter();
	WCoord centroid = pos + WCoord(centerPos.x, centerPos.y, centerPos.z);
	VehicleConfig config = GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig;
	pworld->getEffectMgr()->playParticleEffectAsync(config.create_effect.c_str(), centroid, 40, 0, 0, true, config.create_effect_range);
	pworld->getEffectMgr()->playSound(centroid, config.create_sound.c_str(), config.create_sound_volume, 1.0f);

	return vehicle;
}

ActorVehicleAssemble* ActorVehicleAssemble::createWithItem(ClientPlayer* player, int x, int y, int z, int dir, int iShortcutIdx)
{
	if (!player) { return NULL; }

	ActorVehicleAssemble* vehicle = SANDBOX_NEW(ActorVehicleAssemble);
	if (!vehicle->initWithItem(player, dir, iShortcutIdx) || vehicle->getBlockNum() <= 0 ||
		vehicle->getChassisBlockNum() <= 0 || vehicle->getWheelBlockNum() >= MAX_NB_WHEELS) {
		ENG_DELETE(vehicle);
		return NULL;
	}

	//消耗掉该物品
	//player->getBackPack()->removeItem(iShortcutIdx, 1);
	player->shortcutItemUsed();

	WCoord pos(x, y + 1, z);
	VehicleAssembleLocoMotion *pLoco = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
	//pLoco->m_RotateQuat = Rainbow::Quaternionf(0.0f, 0.0f, 0.0f, 1.0f);

	CollideAABB box;
	pLoco->getCollideBox(box);
	int dim_x = box.dim.x / (2 * BLOCK_SIZE);
	int dim_z = box.dim.z / (2 * BLOCK_SIZE);

	dir = ReverseDirection(dir);
	if (dir == DIR_NEG_X)
	{
		pos.x -= dim_x;
	}
	else if (dir == DIR_POS_X)
	{
		pos.x += dim_x;
	}
	else if (dir == DIR_NEG_Z)
	{
		pos.z -= dim_z;
	}
	else if (dir == DIR_POS_Z)
	{
		pos.z += dim_z;
	}
	pos = BlockCenterCoord(pos);

	vehicle->m_bHaveChassisPos = false;
	vehicle->setVehicleDirection(dir);
	static_cast<ActorManager*>(player->getWorld()->getActorMgr())->spawnActor(vehicle, pos.x, pos.y, pos.z, 0, 0);

	//特效音效播放
	auto centerPos = vehicle->getChassisMassCenter();
	WCoord centroid = pos + WCoord(centerPos.x, centerPos.y, centerPos.z);
	VehicleConfig config = GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig;
	player->getWorld()->getEffectMgr()->playParticleEffectAsync(config.create_effect.c_str(), centroid, 40, 0, 0, true, config.create_effect_range);
	player->getWorld()->getEffectMgr()->playSound(centroid, config.create_sound.c_str(), config.create_sound_volume, 1.0f);

	return vehicle;
}

bool ActorVehicleAssemble::initWithItem(ClientPlayer* player, int dir, int iShortcutIdx)
{
	if (!player) { return false; }

	auto grid = player->getBackPack()->index2Grid(iShortcutIdx);
	if (!grid) { return false; }
	std::vector<BlockData> blocks;
	blocks.clear();
	std::vector<int> vBlockhp;
	std::vector<int> vBlockfuel;
	std::vector<int> vBlockheat;
	std::map<WCoord, char*> m_ContainerData;
	bool isOldSave = false;
	int result = 0;

	SandboxResult sbResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_parseUserdatastr",
		SandboxContext(nullptr)
		.SetData_String("userdata", grid->getUserdataStr())
		.SetData_UserObject("m_AllBlocks", blocks)
		.SetData_UserObject("vBlockhp", vBlockhp)
		.SetData_UserObject("vBlockfuel", vBlockfuel)
		.SetData_UserObject("vBlockheat", vBlockheat)
		.SetData_Number("dir", m_WorkshopDirection)
		.SetData_UserObject("m_ContainerData", m_ContainerData)
		.SetData_UserObject("pVehicle", this)
		.SetData_Bool("isCopy", false));
	if (sbResult.IsExecSuccessed())
	{
		blocks = sbResult.GetData_UserObject<std::vector<BlockData>>("m_AllBlocks");
		vBlockhp = sbResult.GetData_UserObject<std::vector<int>>("vBlockhp");
		vBlockfuel = sbResult.GetData_UserObject<std::vector<int>>("vBlockfuel");
		vBlockheat = sbResult.GetData_UserObject<std::vector<int>>("vBlockheat");
		dir = sbResult.GetData_Number("dir");
		m_ContainerData = sbResult.GetData_UserObject<std::map<WCoord, char*>>("m_ContainerData");
		result = sbResult.GetData_Number("ret");
	}
	// m_ContainerData 这里可能有内存泄漏
	auto iter = m_ContainerData.begin();
	while (iter != m_ContainerData.end())
	{
		ENG_DELETE(iter->second);
		iter++;
	}
	m_ContainerData.clear();

	if (0 == result) { return false; }

	for (int i = 0; i < (int)blocks.size(); i++) {
		m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_Block = blocks[i].data;
		if (i < (int)vBlockhp.size()) {
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurLife = vBlockhp[i];
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurFuel = vBlockfuel[i];
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurHeat = vBlockheat[i];
			if (1 == result)
			{
				m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_GroupIdSelf = 0;
				m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_GroupIdChild = 0;
			}
		}
	}

	if (m_pVehicleWorld == NULL)
	{
		m_pVehicleWorld = ENG_NEW(VehicleWorld)(GetWorldManagerPtr());
		m_pVehicleWorld->init(player->getWorld(), this);
	}
	m_pWorld = m_pVehicleWorld;
	m_bMakeByItemUse = true;

	create(player->getWorld());

	m_BlockNum = blocks.size();

	m_LifeCurrent = m_LifeLimit;


	return true;
}
std::string readVehicDate(World *pworld, int itemid)
{
	std::string path = Rainbow::GetFileManager().ToFullPath("veh")+"/" + std::to_string(itemid)+".ud";
	std::ifstream file(path);
	if (!file.is_open()) {
		LOG_INFO(" readVehicDate err: not find file: %s", path.c_str());
		return "";
	}
	// userdata数据文件
	std::string userdata((std::istreambuf_iterator<char>(file)),
		std::istreambuf_iterator<char>());

	return userdata;
}

ActorVehicleAssemble *ActorVehicleAssemble::createWithUserdataFile(World *pworld, int itemid, WCoord cpos, int dir)
{
	std::string userdata = readVehicDate(pworld, itemid);
	return createWithUserdata(pworld, userdata.c_str(), cpos, dir);
}

ActorVehicleAssemble* ActorVehicleAssemble::createWithUserdata(World *pworld, const char* userdata, WCoord cpos, int dir)
{
	if (!pworld) { return NULL; }

	ActorVehicleAssemble* vehicle = SANDBOX_NEW(ActorVehicleAssemble);
	if (!vehicle->initWithUserdata(pworld, userdata) || vehicle->getBlockNum() <= 0 ||
		vehicle->getChassisBlockNum() <= 0 || vehicle->getWheelBlockNum() >= MAX_NB_WHEELS) {
		ENG_DELETE(vehicle);
		return NULL;
	}

	WCoord pos(cpos.x, cpos.y + 1, cpos.z);
	VehicleAssembleLocoMotion *pLoco = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
	//pLoco->m_RotateQuat = Rainbow::Quaternionf(0.0f, 0.0f, 0.0f, 1.0f);

	CollideAABB box;
	pLoco->getCollideBox(box);
	int dim_x = box.dim.x / (2 * BLOCK_SIZE);
	int dim_z = box.dim.z / (2 * BLOCK_SIZE);

	dir = ReverseDirection(dir);
	if (dir == DIR_NEG_X)
	{
		pos.x -= dim_x;
	}
	else if (dir == DIR_POS_X)
	{
		pos.x += dim_x;
	}
	else if (dir == DIR_NEG_Z)
	{
		pos.z -= dim_z;
	}
	else if (dir == DIR_POS_Z)
	{
		pos.z += dim_z;
	}
	pos = BlockCenterCoord(pos);

	vehicle->m_bHaveChassisPos = false;
	vehicle->setVehicleDirection(dir);
	static_cast<ActorManager*>(pworld->getActorMgr())->spawnActor(vehicle, pos.x, pos.y, pos.z, 0, 0);

	//特效音效播放
	auto centerPos = vehicle->getChassisMassCenter();
	WCoord centroid = pos + WCoord(centerPos.x, centerPos.y, centerPos.z);
	VehicleConfig config = GetWorldManagerPtr()->getSurviveGameConfig()->vehicleconfig;
	pworld->getEffectMgr()->playParticleEffectAsync(config.create_effect.c_str(), centroid, 40, 0, 0, true, config.create_effect_range);
	pworld->getEffectMgr()->playSound(centroid, config.create_sound.c_str(), config.create_sound_volume, 1.0f);

	return vehicle;
}

bool ActorVehicleAssemble::initWithUserdata(World *pworld, const char* userdata)
{
	if (!pworld) { return false; }
	std::string userdatastr = userdata;
	if (userdatastr.empty()) { return false; }

	std::vector<BlockData> blocks;
	blocks.clear();
	std::vector<int> vBlockhp;
	std::vector<int> vBlockfuel;
	std::vector<int> vBlockheat;
	std::map<WCoord, char*> m_ContainerData;
	bool isOldSave = false;
	int result = 0;

	SandboxResult sbResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_parseUserdatastr",
		SandboxContext(nullptr)
		.SetData_String("userdata", userdatastr)
		.SetData_UserObject("m_AllBlocks", blocks)
		.SetData_UserObject("vBlockhp", vBlockhp)
		.SetData_UserObject("vBlockfuel", vBlockfuel)
		.SetData_UserObject("vBlockheat", vBlockheat)
		.SetData_Number("dir", m_WorkshopDirection)
		.SetData_UserObject("m_ContainerData", m_ContainerData)
		.SetData_UserObject("pVehicle", this)
		.SetData_Bool("isCopy", false));
	if (sbResult.IsExecSuccessed())
	{
		blocks = sbResult.GetData_UserObject<std::vector<BlockData>>("m_AllBlocks");
		vBlockhp = sbResult.GetData_UserObject<std::vector<int>>("vBlockhp");
		vBlockfuel = sbResult.GetData_UserObject<std::vector<int>>("vBlockfuel");
		vBlockheat = sbResult.GetData_UserObject<std::vector<int>>("vBlockheat");
		m_WorkshopDirection = sbResult.GetData_Number("dir");
		m_ContainerData = sbResult.GetData_UserObject<std::map<WCoord, char*>>("m_ContainerData");
		result = sbResult.GetData_Number("ret");
	}
	// m_ContainerData 这里可能有内存泄漏
	auto iter = m_ContainerData.begin();
	while (iter != m_ContainerData.end())
	{
		ENG_DELETE(iter->second);
		iter++;
	}
	m_ContainerData.clear();

	if (0 == result) { return false; }

	for (int i = 0; i < (int)blocks.size(); i++) {
		m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_Block = blocks[i].data;
		if (i < (int)vBlockhp.size()) {
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurLife = vBlockhp[i];
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurFuel = vBlockfuel[i];
			m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_iCurHeat = vBlockheat[i];
			if (1 == result)
			{
				m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_GroupIdSelf = 0;
				m_Blocks[blocks[i].relativepos.x][blocks[i].relativepos.y][blocks[i].relativepos.z]->m_GroupIdChild = 0;
			}
		}
	}

	if (m_pVehicleWorld == NULL)
	{
		m_pVehicleWorld = ENG_NEW(VehicleWorld)(GetWorldManagerPtr());
		m_pVehicleWorld->init(pworld, this);
	}
	m_pWorld = m_pVehicleWorld;
	m_bMakeByItemUse = true;

	create(pworld);

	m_BlockNum = blocks.size();

	m_LifeCurrent = m_LifeLimit;


	return true;
}

void ActorVehicleAssemble::setVehicleDirection(int iDestDir)
{
	VehicleAssembleLocoMotion *pLoco = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (!pLoco) { return; }

	Rainbow::Vector3f srcVec3 = getDirVec3(getHeadDir());
	Rainbow::Vector3f destVec3 = getDirVec3(iDestDir);
	float angle = acos(DotProduct(srcVec3, destVec3) / srcVec3.Length() / destVec3.Length()) * 180 / kOnePI;

	float crossValue = srcVec3.x*destVec3.z - srcVec3.z*destVec3.x;
	if (crossValue > 0) {
		angle = -angle;
	}

	pLoco->rotate(0, angle, 0);
	pLoco->m_RotateQuat = AngleEulerToQuaternionf(Vector3f(0, angle, 0));
}

Rainbow::Vector3f ActorVehicleAssemble::getDirVec3(int dir)
{
	switch (dir)
	{
	case DIR_NEG_X:
		return Rainbow::Vector3f::neg_xAxis;
	case DIR_NEG_Z:
		return Rainbow::Vector3f::neg_zAxis;
	case DIR_POS_Z:
		return Rainbow::Vector3f::zAxis;
	}

	return Rainbow::Vector3f::xAxis;
}

//给客机使用判断是否点击的是驾驶座
bool ActorVehicleAssemble::interactDriverSeat(ClientPlayer* player, bool onshift , bool isMobile)
{
	bool ret = false;
	int currItemID = player->getCurToolID();
	// 手持连线钳 先返回
	if (currItemID == ITEM_VEHICLE_LINK_TOOL)
	{
		return false;
	}

	do
	{
		int x = -1, y = -1, z = -1;
		IntersectResult result;
		intersectWithResult(player, x, y, z, result);
		if (x >= 0 && y >= 0 && z >= 0)
		{
			Block* srcBlock = &m_Blocks[x][y][z]->m_Block;
			if (srcBlock->isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
			{
				return false;
			}

			WCoord blockpos(x, y, z);
			DirectionType targetface = DIR_NOT_INIT;
			int blockid = getBlockID(blockpos);
			int blockdata = getBlockData(blockpos);
			if ((blockid == BLOCK_DRIVERS_SEAT || blockid == BLOCK_PASSENGER_SEAT || blockid == BLOCK_BOAT_RUDDER))
			{
				// 驾驶座 乘客座
				auto ctl = dynamic_cast<PlayerControl*>(player);
				if (ctl)
				{
					ctl->tryMountActor(this);
					ret = true;
				}
				break;
			}
		}
	} while (0);

	return ret;
}

bool ActorVehicleAssemble::interact(ClientActor *player, bool onshift/* =false */, bool isMobile)
{
	bool ret = false;
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		int currItemID = pTempPlayer->getCurToolID();
		// 手持连线钳 先返回
		if (currItemID == ITEM_VEHICLE_LINK_TOOL)
		{
			return false;
		}

		do
		{
			//方块行为
			//else
			{
				int x = -1, y = -1, z = -1;
				IntersectResult result;
				intersectWithResult(pTempPlayer, x, y, z,result);
				if (x >= 0 && y >= 0 && z >= 0)
				{
					Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
					if (srcBlock->isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
					{
						return false;
					}

					WCoord blockpos(x, y, z);
					DirectionType targetface = DIR_NOT_INIT;
					int blockid = getBlockID(blockpos);
					int blockdata = getBlockData(blockpos);

					//加油
					if (blockid == BLOCK_VEHICLEENGINE || BLOCK_FUEL_TANK == blockid)
					{
						// 					BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
						// 					if (pmtl)
						// 					{
						// 						pmtl->EXEC_USEMODULE(OnNotify, m_pVehicleWorld, blockpos, blockid);
						// 					}
						int index = checkFuel(pTempPlayer, blockpos);
						if (index != -1)
						{
							DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[index]);
							if (dynamicCost && dynamicCost->getFuncDef())
							{
								int supplyVal = dynamicCost->getCurValue() + dynamicCost->getFuncDef()->func.costfun.SupplyVal;
								//pTempPlayer->playAnim(SEQ_ATTACK, true);
								setFuelWithIndex(supplyVal, index);
								pTempPlayer->shortcutItemUsed();
								ret = true;
							}
							break;
						}
						m_pVehicleWorld->markBlockForUpdate(blockpos, true);
					}
					else if ((blockid == BLOCK_CATAPULT) && ((blockdata & 4) != 0))
					{
						LOG_INFO("interact catapult block pos: %d %d %d ", x, y, z);

						//不可以放置门窗
						if (!IsDoorBlock(currItemID) && !IsWindowsBlock(currItemID))
						{
							WCoord addPos(x, y + 1, z);
							VehicleBlock *vehicleBLock = getVehicleBlock(blockpos);
							updateVehicleBlock(addPos, currItemID, 0, vehicleBLock->m_GroupIdSelf);
							ret = true;
						}

						break;
					}
					else if ((blockid == BLOCK_DRIVERS_SEAT || blockid == BLOCK_PASSENGER_SEAT || blockid == BLOCK_BOAT_RUDDER))
					{
						// 驾驶座 乘客座
						auto ctl = dynamic_cast<PlayerControl*>(player);
						if (ctl)
						{
							ctl->tryMountActor(this);
							ret = true;
						}
						break;
					}
					/*else {
						//检测选中方块是否是可用的
						BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
						if (pmtl)
						{
							ret = pmtl->EXEC_USEMODULE(OnTrigger, m_pVehicleWorld, blockpos, result.face, player, result.facepoint);
						}
						if (!ret)
						{
							//添加方块
							int dir = 0;
							switch (result.face)
							{
							case DIR_NEG_X:
							{
								x -= 1;
								dir = DIR_POS_X;
							}break;
							case DIR_POS_X:
							{
								x += 1;
								dir = DIR_NEG_X;
							}break;
							case DIR_NEG_Y:
							{
								y -= 1;
								dir = DIR_POS_Y;
							}break;
							case DIR_POS_Y:
							{
								y += 1;
								dir = DIR_NEG_Y;
							}break;
							case DIR_NEG_Z:
							{
								z -= 1;
								dir = DIR_POS_Z;
							}break;
							case DIR_POS_Z:
							{
								z += 1;
								dir = DIR_NEG_Z;
							}break;
							default:
								break;
							}
							ret = addBlock(x, y, z, dir, player->getCurToolID(), player, &result);
						}
					
					}*/
				
					//else if ((currItemID == ITEM_WRENCH) && (blockid == BLOCK_COLLIDER || blockid == BLOCK_BALLCOLLIDER || blockid == BLOCK_MOBCOLLIDER || blockid == BLOCK_PHYSXCOLLIDER))
					//{
					//	//如果是载具上触碰方块，扳手点击，需要进行触发
					//	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
					//	if (pmtl)
					//	{
					//		pmtl->EXEC_USEMODULE(OnTrigger, m_pVehicleWorld, blockpos, targetface,  player, MINIW::Vector3(0, 0, 0));
					//	}
					//	ret = true;
					//}

					const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);


					if (physicsPartsDef && !physicsPartsDef->CannotTrigger)
					{
						BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
						if (pmtl && pmtl->getBlockSettingAttState(ENABLE_BEOPERATED) > 0)
						{
							// 触发开始 先重置所有的连线block遍历状态
							m_LineGraph->ResetNode();

							//增加判断，玩家是否允许使用方块
							if (GetWorldManagerPtr() && GetWorldManagerPtr()->getPlayerPermit(ENABLE_OPERATEBLOCK) && pmtl->DoOnTrigger(m_pVehicleWorld, blockpos, targetface, pTempPlayer, Rainbow::Vector3f(0, 0, 0)))
							{
								ret = true;
							}
						}
					}
					if (!ret)
					{
						//添加方块
						int dir = 0;
						switch (result.face)
						{
						case DIR_NEG_X:
						{
							x -= 1;
							dir = DIR_POS_X;
						}break;
						case DIR_POS_X:
						{
							x += 1;
							dir = DIR_NEG_X;
						}break;
						case DIR_NEG_Y:
						{
							y -= 1;
							dir = DIR_POS_Y;
						}break;
						case DIR_POS_Y:
						{
							y += 1;
							dir = DIR_NEG_Y;
						}break;
						case DIR_NEG_Z:
						{
							z -= 1;
							dir = DIR_POS_Z;
						}break;
						case DIR_POS_Z:
						{
							z += 1;
							dir = DIR_NEG_Z;
						}break;
						default:
							break;
						}
						ret = addBlock(x, y, z, dir, pTempPlayer->getCurToolID(), pTempPlayer, &result);
					}
				}
			}
		} while (0);

		if (currItemID == ITEM_WRENCH && ret == false)
		{
			if (needClear())
			{
				return ret;
			}
			// 机械上坐人时，不能将其变为道具
			// 使用扳手会提示：“机械上还有乘客，不能收起”
			int num = this->getNumRiddenPos();
			for (int i = 0; i < num; i++)
			{
				WORLD_ID riddenid = getRiddenByActorID(i);
				if (riddenid&&riddenid > 0)
				{
					pTempPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 12013);
					return ret;
				}
			}

			//变成胶囊的时候容器里面的东西都调掉出来
			std::vector<WorldContainer *> chunkcontainers;
			if (m_pVehicleWorld)
			{
				VehicleContainerMgr* containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
				if (containermgr)
				{
					containermgr->getContainersVec(chunkcontainers);
				}
				for (int i = 0; i < (int)chunkcontainers.size(); i++)
				{
					if (g_WorldMgr->isGameMakerRunMode() || g_WorldMgr->isSurviveMode() || g_WorldMgr->isFreeMode() || g_WorldMgr->isCreateRunMode() || g_WorldMgr->isExtremityMode())
					{
						//载具的储物箱掉落物品以后，清理储物箱
						chunkcontainers[i]->dropItems();
						if (chunkcontainers[i]->getObjType() == OBJ_TYPE_BOX)
							chunkcontainers[i]->clear();

						//清理信号解析器
						if (chunkcontainers[i]->getObjType() == OBJ_TYPE_INTERPRETERUNIT)
							chunkcontainers[i]->clear();
						//发射器清理
						WorldEmitterContainer* emitterContainer = dynamic_cast<WorldEmitterContainer*>(chunkcontainers[i]);
						if (emitterContainer) emitterContainer->clear();

						//书架清理
						WorldBookCabinet* bookConatiner = dynamic_cast<WorldBookCabinet*>(chunkcontainers[i]);
						if (bookConatiner)
						{
							for (int bindex = 0; bindex < bookConatiner->getGridNum(); bindex++)
							{
								BackPackGrid *pback = bookConatiner->index2Grid(bindex + BOOKCABINET_START_INDEX);
								if (pback)
								{
									if (pback->getNum() > 0)
									{
										pback->clear();
										int data = m_pVehicleWorld->getBlockData(bookConatiner->m_BlockPos);
										m_pVehicleWorld->setBlockData(bookConatiner->m_BlockPos, data - 1, 2);
									}
								}
							}
						}

						//编书台清理
						WorldBookEditorTableContainer* bookEditorContainer = dynamic_cast<WorldBookEditorTableContainer*>(chunkcontainers[i]);
						if (bookEditorContainer)
						{
							for (int bindex = 0; bindex < bookEditorContainer->getGridNum(); bindex++)
							{
								BackPackGrid *pback = bookEditorContainer->index2Grid(bindex + EDITBOOK_START_INDEX);
								if (pback)
								{
									if (pback->getNum() > 0)
									{
										pback->clear();
										int data = m_pVehicleWorld->getBlockData(bookEditorContainer->m_BlockPos);
										m_pVehicleWorld->setBlockData(bookEditorContainer->m_BlockPos, data - 1, 2);
									}
								}
							}
						}

						//感应方块进行清理
						WorldSensorContainer* sensorConatiner = dynamic_cast<WorldSensorContainer*>(chunkcontainers[i]);
						if (sensorConatiner) sensorConatiner->clear();

						//漏斗方块进行清理
						WorldFunnelContainer* funnelConatiner = dynamic_cast<WorldFunnelContainer*>(chunkcontainers[i]);
						if (funnelConatiner) funnelConatiner->clear();

						//熔炉方块进行清理
						FurnaceContainer* furnaceConatiner = dynamic_cast<FurnaceContainer*>(chunkcontainers[i]);
						if (furnaceConatiner) furnaceConatiner->clear();

						//相框进行清理
						ContainerItemExpo* expoContainer = dynamic_cast<ContainerItemExpo*>(chunkcontainers[i]);
						if (expoContainer) expoContainer->clear();
					}
				}
			}
			VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
			if (vehicleModule)
			{
				char filename[256];
#if defined(_WIN32)
				sprintf(filename, "%d%I64d", pTempPlayer->getUin(), time(NULL));
#else
				sprintf(filename, "%d%ld", pTempPlayer->getUin(), time(NULL));
#endif
				m_FileName = filename;

				m_itemID = vehicleModule->dropItem(pTempPlayer, this, m_itemID, m_FileName, m_ModelName, m_ModelDesc);
				setNeedClear();
				ret = true;
				// 扳手操作 itemid和原始数据一起保存
				vehicleModule->saveOriginalFB(pTempPlayer, m_Blocks, m_itemID, m_FileName, m_ModelName, m_ModelDesc, VEHICLE_MODEL, m_WorkshopDirection);
			}
			else
				ret = false;
		}
	}
	return ret;
}

std::string ActorVehicleAssemble::GetUserdataStr(ClientPlayer *player)
{
	char filename[256];
	sprintf(filename, "%d%ld", player->getUin(), static_cast<long int>(time(NULL)));

	jsonxx::Object obj;
	obj << "id" << m_itemID;
	obj << "itemtype" << 5;
	obj << "filename" << filename;
	obj << "itemname" << m_ModelName;
	obj << "itemdesc" << m_ModelDesc;
	flatbuffers::FlatBufferBuilder builder;
	auto s = this->save(builder);
	builder.Finish(s);
	unsigned char *src = builder.GetBufferPointer();
	size_t srclen = builder.GetSize();
	unsigned char *buffer = (unsigned char *)malloc(srclen);
	memcpy(buffer, src, srclen);
	char* savedata = (char*)b64_encode(buffer, srclen);
	if (savedata)
	{
		std::string savestring = savedata;
		obj << "savedata" << savestring;
		free(savedata);
	}
	free(buffer);
	return obj.json();
}

void ActorVehicleAssemble::reset()
{
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
		loc->reset();
}

int ActorVehicleAssemble::getHeadDir()
{
	int driveDirection = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig.drive_direction;

	if (driveDirection == 0)
	{
		if (m_WorkshopDirection == DIR_NEG_X)
			return DIR_POS_X;
		else if (m_WorkshopDirection == DIR_POS_X)
			return DIR_NEG_X;
		else if (m_WorkshopDirection == DIR_NEG_Z)
			return DIR_POS_Z;
		else if (m_WorkshopDirection == DIR_POS_Z)
			return DIR_NEG_Z;
	}
	else
	{
		if (m_WorkshopDirection == DIR_NEG_X)
			return DIR_NEG_X;
		else if (m_WorkshopDirection == DIR_POS_X)
			return DIR_POS_X;
		else if (m_WorkshopDirection == DIR_NEG_Z)
			return DIR_NEG_Z;
		else if (m_WorkshopDirection == DIR_POS_Z)
			return DIR_POS_Z;
	}
	return DIR_POS_X;
}

std::string ActorVehicleAssemble::getFileName()
{
	return m_FileName;
}

void ActorVehicleAssemble::setFileName(std::string& name)
{
	m_FileName = name;
}

void ActorVehicleAssemble::setData(std::string modelname, std::string modeldesc, int itemid, std::string filename/*""*/)
{
	m_FileName = filename;
	m_ModelName = modelname;
	m_ModelDesc = modeldesc;
	m_itemID = itemid;
}

float ActorVehicleAssemble::getMinPartsCost()
{
	float rate = -1;
	if (m_CostParts.size() <= 0 /*|| m_Engines.size() <= 0*/) return rate;
	bool isUnlimit = true;
	for (int i = 0; i < (int)m_Engines.size(); i++)
	{
		if (BLOCK_INFINITEENGINE == m_Engines[i].blockid)
		{
			return rate;
		}
	}
	float curValue = 0;
	float maxCost = 0;
	for (int i = 0; i < (int)m_CostParts.size(); i++)
	{
		DynamicCost* dynamicCost = dynamic_cast<DynamicCost *>(m_CostParts[i]);
		if (dynamicCost)
		{
			PhysicsPartsDef::EffectFunctionsDef* def = m_CostParts[i]->getFuncDef();
			if (def && def->func.costfun.UIDisplay == 1)
			{
				curValue += dynamicCost->getCurValue();
				maxCost += def->func.costfun.MaxCost;
			}
		}
	}
	if (maxCost)
	{
		rate = curValue / maxCost;
	}
	return rate;
}

void ActorVehicleAssemble::setFuelWithBlockPos(int fuel, const WCoord &blockpos, bool enterWorld, bool canOperate)
{
	for (int i = 0; m_CostParts.size(); i++)
	{
		DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[i]);
		if (dynamicCost && dynamicCost->getPos() == blockpos && dynamicCost->getFuncDef()->func_id == 5)
		{
			setFuelWithIndex(fuel, i, enterWorld, canOperate);
			break;
		}
	}
}

void ActorVehicleAssemble::setFuelWithIndex(int fuel, int index, bool enterWorld, bool canOperate)
{
	if (0 <= index && index < (int)m_CostParts.size())
	{
		DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[index]);
		if (dynamicCost)
		{
			WCoord blockpos = m_CostParts[index]->getPos();
			int posData = (blockpos.x << 10) + (blockpos.y << 5) + blockpos.z;
			//客机向主机发送协议，请求消耗
			if (m_pWorld->isRemoteMode() && canOperate == false && enterWorld == false)
			{
				PB_VehicleAttribChangeCH vehicleAttrChangeCH;
				vehicleAttrChangeCH.set_objid(getObjId());
				vehicleAttrChangeCH.set_fuel(fuel);
				vehicleAttrChangeCH.set_partindex(posData);

				GetGameNetManagerPtr()->sendToHost(PB_VEHICLE_ATTRIB_CHANGE_CH, vehicleAttrChangeCH);
			}
			//主机执行消耗，并传播给客机
			if (!enterWorld && !m_pWorld->isRemoteMode())
			{
				PB_VehicleAttribChangeHC vehicleAttribChangeHC;
				vehicleAttribChangeHC.set_objid(getObjId());
				vehicleAttribChangeHC.set_fuel(fuel);
				vehicleAttribChangeHC.set_partindex(posData);

				m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLE_ATTRIB_CHANGE_HC, vehicleAttribChangeHC, this, false);
			}

			//客机 或 主机 或 刚进入世界时，执行消耗
			if (enterWorld || !m_pWorld->isRemoteMode() || canOperate)
			{
				PhysicsPartsDef::EffectFunctionsDef* def = dynamicCost->getFuncDef();
				if (def)
				{
					if (fuel < 0)
						fuel = 0;
					else if (fuel > def->func.costfun.MaxCost)
						fuel = def->func.costfun.MaxCost;

					dynamicCost->setCurValue(fuel);
				}
				WCoord blockPos = dynamicCost->getPos();
				int x = blockPos.x;
				int y = blockPos.y;
				int z = blockPos.z;
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				if (srcBlock.getResID())
				{
					if (srcBlock.getResID() > 0)
						m_Blocks[x][y][z]->m_iCurFuel = dynamicCost->getCurValue();

					VehicleContainerMecha* vehicleMechaContainer = dynamic_cast<VehicleContainerMecha *>(m_pVehicleWorld->getContainerMgr()->getContainer(WCoord(x, y, z)));
					if (vehicleMechaContainer)
						vehicleMechaContainer->setFuel(dynamicCost->getCurValue());
				}
				m_pVehicleWorld->markBlockForUpdate(blockpos, false);
			}
		}
	}
}

int ActorVehicleAssemble::checkFuel(ClientActor *actor, const WCoord &blockpos)
{
	ClientPlayer *player = dynamic_cast<ClientPlayer *>(actor);
	if (player == NULL) return false;

	for (int i = 0; i < (int)m_CostParts.size(); i++)
	{
		WCoord tpos = m_CostParts[i]->getPos();
		LOG_INFO("tpos: %d %d %d   func_id: %d", tpos.x, tpos.y, tpos.z, m_CostParts[i]->getFuncDef()->func_id);
		if (m_CostParts[i]->getPos() == blockpos && m_CostParts[i]->getFuncDef()->func_id == 5)
		{
			DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[i]);
			PhysicsPartsDef::EffectFunctionsDef* def = m_CostParts[i]->getFuncDef();
			if (def->func.costfun.SupplyWay == 0 && player->getCurToolID() == def->func.costfun.SupplyItem)
			{
				int maxCost = def->func.costfun.MaxCost;
				if (dynamicCost->getCurValue() < maxCost)
				{
					return i;
				}
				else
				{
					PlayerControl* playercontrol = dynamic_cast<PlayerControl*>(player);
					if (playercontrol)
						playercontrol->postInfoTips(12313);
					break;
				}
			}
			else
			{
				PlayerControl* playercontrol = dynamic_cast<PlayerControl*>(player);
				if (playercontrol)
					playercontrol->postInfoTips(12314);
				break;
			}
		}
	}
	return -1;
}

void ActorVehicleAssemble::onClear()
{
	int num = getNumRiddenPos();
	if (g_WorldMgr)
	{
	for (int i = 0; i < num; i++)
	{
		WORLD_ID playerid = getRiddenByActorID(i);
		if (playerid > 0)
		{
			ClientPlayer* player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin((int)playerid));
			if (player)player->mountActor(NULL);
		}
	}
	}

	PhysicsLocoMotion* pLoco = dynamic_cast<PhysicsLocoMotion*>(this->getLocoMotion());
	bool bNeedClear = pLoco ? pLoco->m_hasPhysActor : false;
	if (bNeedClear)
		m_pWorld->getEffectMgr()->playParticleEffectAsync("particles/10021.ent", getMassCenterWcoord(), 60);
	//playParticles("10021.ent");
}

bool ActorVehicleAssemble::canBePushed()
{
	return false;
}

void ActorVehicleAssemble::syncVehicleSpeed(int actualSpeed, int showSpeed)
{
	if (!m_pWorld->isRemoteMode())
	{
		PB_VehicleAttribChangeHC vehicleAttribChangeHC;
		vehicleAttribChangeHC.set_objid(getObjId());
		vehicleAttribChangeHC.set_actualspeed(actualSpeed);
		vehicleAttribChangeHC.set_showspeed(showSpeed);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLE_ATTRIB_CHANGE_HC, vehicleAttribChangeHC, this, false);
	}
	else
	{
		m_CurActualSpeed = (float)actualSpeed;
		m_CurSpeedShow = (float)showSpeed;
	}
}

void ActorVehicleAssemble::syncEngintRotationSpeed(float speed)
{
	if (!m_pWorld->isRemoteMode())
	{
		PB_VehicleAttribChangeHC vehicleAttribChangeHC;
		vehicleAttribChangeHC.set_objid(getObjId());
		vehicleAttribChangeHC.set_enginerotationspeed(speed);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLE_ATTRIB_CHANGE_HC, vehicleAttribChangeHC, this, false);
	}
	else
	{
		m_EngineRotationSpeed = speed;
	}
}

float ActorVehicleAssemble::getCurActualSpeed()
{
	float vz = 0.0f;
	VehicleAssembleLocoMotion *loc = dynamic_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		vz = loc->getActualSpeed();
	}
	return vz;
}
Rainbow::AABB ActorVehicleAssemble::GetAABB() {
	OPTICK_EVENT();

	if (m_VehicleAABBDirty) {
		WCoord start, dim;
		getVehicleColliderBox(start, dim);
		auto ext = (dim / 2).toWorldPos().toVector3();
		//我也不知道为什么要+ext, 反正就是偏了=.=, 需要深入的分析下代码
		m_VehicleAABB = Rainbow::AABB(start.toWorldPos().toVector3() + ext, ext+BLOCK_FSIZE);
		m_VehicleAABBDirty = false;
	}
	
	return m_VehicleAABB;
}
void ActorVehicleAssemble::getViewBox(CollideAABB &box)
{
	//修复载具超大时视角点超过可视范围而消失的问题，但是出现能看到载具，可地面及其他物品不可见造成不可知的逻辑

	// 	int maxX = MAX_DIM_X * 100;
	// 	int maxY = MAX_DIM_Y * 100;
	// 	int maxZ = MAX_DIM_Z * 100;
	// 	std::map<WCoord, int> checkPosList;
	// 	for (auto iter = m_mJointSideBlocksPos.begin(); iter != m_mJointSideBlocksPos.end(); iter++)
	// 	{
	// 		for (auto iterSec = iter->second.begin(); iterSec != iter->second.end(); iterSec++)
	// 		{
	// 			if (checkPosList.find(iterSec->second) == checkPosList.end())
	// 			{
	// 				checkPosList.insert(make_pair(iterSec->second, iter->first));
	// 			}
	// 		}
	// 	}
	// 	WCoord pos = getPosition();
	// 	for (auto iter = checkPosList.begin(); iter != checkPosList.end(); iter++)
	// 	{
	// 		WCoord tmpPos = this->getRealWorldPosWithPos(iter->first);
	// 		int x = abs(tmpPos.x - pos.x);
	// 		maxX = x > maxX ? x : maxX;
	// 
	// 		int y = abs(tmpPos.y - pos.y);
	// 		maxY = y > maxY ? y : maxY;
	// 
	// 		int z = abs(tmpPos.z - pos.z);
	// 		maxZ = z > maxZ ? z : maxZ;
	// 	}
	// 
	// 	maxX += 16 * 100;
	// 	maxY += 16 * 100;
	// 	maxZ += 16 * 100;
	// 	box.dim = WCoord(maxX, maxY, maxZ);
	box.dim = WCoord(MAX_DIM_X * 200, MAX_DIM_Y * 200, MAX_DIM_Z * 200);
	box.pos = getPosition() - WCoord(box.dim.x / 2, 0, box.dim.z / 2);
}

bool ActorVehicleAssemble::getRiddenChangeFPSView()
{
	if (!GetWorldManagerPtr()->isGameMakerRunMode() || GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA) < 3)
		return true;
	return false;
}

void ActorVehicleAssemble::syncEngineState(VEHICLE_ENGINESOUND_STATE state, bool operate/*=false*/)
{
	//主机广播
	if (!m_pWorld->isRemoteMode())
	{
		PB_VehicleAttribChangeHC vehicleAttribChangeHC;
		vehicleAttribChangeHC.set_objid(getObjId());
		vehicleAttribChangeHC.set_enginestate((int)state);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLE_ATTRIB_CHANGE_HC, vehicleAttribChangeHC, this, false);
	}

	//客机发送协议
	if (m_pWorld->isRemoteMode() && operate == false)
	{
		PB_VehicleAttribChangeCH vehicleAttrChangeCH;
		vehicleAttrChangeCH.set_objid(getObjId());
		vehicleAttrChangeCH.set_enginestate((int)state);

		GetGameNetManagerPtr()->sendToHost(PB_VEHICLE_ATTRIB_CHANGE_CH, vehicleAttrChangeCH);
	}

	//执行状态变更
	if (!m_pWorld->isRemoteMode() || operate)
	{
		m_vehicleSnd.engineState = state;
	}
}

float ActorVehicleAssemble::getCurSpeedShow()
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		float vz = 0.0f;
		VehicleAssembleLocoMotion *loc = dynamic_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
		if (loc)
		{
			vz = loc->getSpeed();
		}
		return floor(vz / 100.0f + 0.5f);
	}
	else
	{
		return m_CurSpeedShow;
	}
}

float ActorVehicleAssemble::getEngineRotationSpeed()
{
	if (!m_pWorld->isRemoteMode())
	{
		float vz = 0.0f;
		VehicleAssembleLocoMotion *loc = dynamic_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
		if (loc)
		{
			vz = loc->getEngineRotationSpeed();
		}
		return vz;
	}
	else
		return m_EngineRotationSpeed;
}

void ActorVehicleAssemble::getVehicleColliderBox(WCoord& start, WCoord& dim)
{
	WCoord VertBlock[8];
	VertBlock[0] = convertWcoord(m_MinVertBlock);
	VertBlock[7] = convertWcoord(m_MaxVertBlock);
	VertBlock[1] = convertWcoord(WCoord(m_MaxVertBlock.x, m_MinVertBlock.y, m_MinVertBlock.z));
	VertBlock[2] = convertWcoord(WCoord(m_MinVertBlock.x, m_MaxVertBlock.y, m_MinVertBlock.z));
	VertBlock[3] = convertWcoord(WCoord(m_MinVertBlock.x, m_MinVertBlock.y, m_MaxVertBlock.z));

	VertBlock[4] = convertWcoord(WCoord(m_MaxVertBlock.x, m_MaxVertBlock.y, m_MinVertBlock.z));
	VertBlock[5] = convertWcoord(WCoord(m_MaxVertBlock.x, m_MinVertBlock.y, m_MaxVertBlock.z));
	VertBlock[6] = convertWcoord(WCoord(m_MinVertBlock.x, m_MaxVertBlock.y, m_MaxVertBlock.z));

	int minValue[3] = { VertBlock[0].x,VertBlock[0].y,VertBlock[0].z };
	int maxValue[3] = { VertBlock[0].x,VertBlock[0].y,VertBlock[0].z };
	for (int i = 1; i < 8; i++)
	{
		maxValue[0] = maxValue[0] <= VertBlock[i].x ? VertBlock[i].x : maxValue[0];
		maxValue[1] = maxValue[1] <= VertBlock[i].y ? VertBlock[i].y : maxValue[1];
		maxValue[2] = maxValue[2] <= VertBlock[i].z ? VertBlock[i].z : maxValue[2];

		minValue[0] = minValue[0] >= VertBlock[i].x ? VertBlock[i].x : minValue[0];
		minValue[1] = minValue[1] >= VertBlock[i].y ? VertBlock[i].y : minValue[1];
		minValue[2] = minValue[2] >= VertBlock[i].z ? VertBlock[i].z : minValue[2];
	}
	dim = WCoord(maxValue[0] - minValue[0], maxValue[1] - minValue[1], maxValue[2] - minValue[2]) + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	start = WCoord(minValue[0], minValue[1], minValue[2]) - WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2);
}

void ActorVehicleAssemble::playWheelLoopSnd(VEHICLE_WHEELSOUND_STATE state)
{
	VehicleConfig config = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig;
	float ratio = config.wheeltravel_val1 + config.wheeltravel_val2*(getCurSpeedShow() * 100 / config.wheeltravel_sound_speed);
	float baseVol = config.wheeltravel_sound_volume;
	if (ratio < 0) ratio = 0;

	auto centroid = getChassisMassCenter();
	WCoord pos = getPosition() + WCoord(centroid.x, centroid.y, centroid.z);
	if (g_pPlayerCtrl)
	{
		auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
		if (PlayerRidComp && /*(g_pPlayerCtrl->m_RidingActor == this->getObjId())*/ PlayerRidComp->checkRidingByActorObjId(this->getObjId()) )
			pos = convertWcoord(getRiddenBindSeatPos(g_pPlayerCtrl));
	}

	if (state == MOVE_START)
	{
		if (!m_vehicleSnd.wheelPlaySnd)
			m_vehicleSnd.wheelPlaySnd = m_pWorld->getEffectMgr()->playLoopSound(pos, config.wheeltravel_sound.c_str(), baseVol*ratio, 1.0f);
		else
		{
			m_vehicleSnd.wheelPlaySnd->setVolume(baseVol*ratio);
			m_vehicleSnd.wheelPlaySnd->setPosition(pos.toVector3());
		}
	}
	else if (state == MOVE_END)
	{
		OGRE_RELEASE(m_vehicleSnd.wheelPlaySnd);
	}

	//LOG_INFO("wheel vol:%f", baseVol*ratio);

}

void ActorVehicleAssemble::playEngineLoopSnd(VEHICLE_ENGINESOUND_STATE state)
{
	VehicleConfig config = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig;
	float work_ratio = config.work_sound_val1 + config.work_sound_val2*(getCurSpeedShow() * 100 / config.engine_sound_speed);
	float work_base_pitch = config.work_sound_pitch;
	float work_base_vol = config.work_sound_volume;

	auto centroid = getChassisMassCenter();
	WCoord pos = getPosition() + WCoord(centroid.x, centroid.y, centroid.z);

	if (g_pPlayerCtrl)
	{
		auto PlayerRidComp = g_pPlayerCtrl->getRiddenComponent();
		if (PlayerRidComp && /*(g_pPlayerCtrl->m_RidingActor == this->getObjId())*/PlayerRidComp->checkRidingByActorObjId(this->getObjId()))
			pos = convertWcoord(getRiddenBindSeatPos(g_pPlayerCtrl));
	}
	if (work_ratio < 0) work_ratio = 0;
	//if (brake_ratio < 0) brake_ratio = 0;
	switch (state)
	{
	case WORK_START:
		if (!m_vehicleSnd.engineWorkSnd)
			m_vehicleSnd.engineWorkSnd = m_pWorld->getEffectMgr()->playLoopSound(pos, config.work_sound.c_str(), work_base_vol*work_ratio, work_base_pitch*work_ratio);
		else
		{
			m_vehicleSnd.engineWorkSnd->setPitch(work_ratio*work_base_pitch);
			m_vehicleSnd.engineWorkSnd->setVolume(work_ratio*work_base_vol);
			m_vehicleSnd.engineWorkSnd->setPosition(pos.toVector3());
			//m_pWorld->getEffectMgr()->playParticleEffect(config.create_effect.c_str(), pos, 40, 0, 0, true, config.create_effect_range);
		}

		OGRE_RELEASE(m_vehicleSnd.engineIgnitionSnd);
		break;

	case IGNITION_START:
		if (getCurSpeedShow() * 100 < config.startup_sound_threshold)
		{
			if (!m_vehicleSnd.engineIgnitionSnd)
				m_vehicleSnd.engineIgnitionSnd = m_pWorld->getEffectMgr()->playLoopSound(pos, config.startup_sound.c_str(), config.startup_sound_volume, 1.0f);
			else
				m_vehicleSnd.engineIgnitionSnd->setPosition(pos.toVector3());
		}
		else
			OGRE_RELEASE(m_vehicleSnd.engineIgnitionSnd);
		OGRE_RELEASE(m_vehicleSnd.engineWorkSnd);
		break;

	case BRAKE_START:
		OGRE_RELEASE(m_vehicleSnd.engineWorkSnd);
		OGRE_RELEASE(m_vehicleSnd.engineIgnitionSnd);
		if (getCurSpeedShow() * 100 > config.brake_sound_threshold)
		{
			m_pWorld->getEffectMgr()->playSound(pos, config.brake_sound.c_str(), config.brake_sound_volume, 1.0f);
			m_vehicleSnd.engineState = BRAKE_PROCESS;
			//LOG_INFO("play brake...");
		}
		break;

	case ENGINE_NONE:
		OGRE_RELEASE(m_vehicleSnd.engineWorkSnd);
		OGRE_RELEASE(m_vehicleSnd.engineIgnitionSnd);
		m_vehicleSnd.workdelayCount = (int)config.engine_sound_delaytime * 20;
		break;

	default:
		break;
	}

}

void ActorVehicleAssemble::playThrusterLoopSnd(int state, const char *name)
{
	VehicleConfig config = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig;
	float work_ratio = config.work_sound_val1 + config.work_sound_val2 * (getCurSpeedShow() * 100 / config.engine_sound_speed);
	float work_base_pitch = config.work_sound_pitch;
	float work_base_vol = config.work_sound_volume;

	auto centroid = getChassisMassCenter();
	WCoord pos = getPosition() + WCoord(centroid.x, centroid.y, centroid.z);

	if (state == 1) {
		if (!m_vehicleSnd.thrusterSnd)
			m_vehicleSnd.thrusterSnd = m_pWorld->getEffectMgr()->playLoopSound(pos, name, 1.0f, 1.0f);
		else
		{
			m_vehicleSnd.thrusterSnd->setPitch(work_ratio * work_base_pitch);
			m_vehicleSnd.thrusterSnd->setVolume(work_ratio * work_base_vol);
			m_vehicleSnd.thrusterSnd->setPosition(pos.toVector3());
		}
	}
	else {
		OGRE_RELEASE(m_vehicleSnd.thrusterSnd);
	}
}

void ActorVehicleAssemble::playSThrusterLoopSnd(int state, const char *name, float volume)
{
	VehicleConfig config = GetWorldManagerPtr()->m_SurviveGameConfig->vehicleconfig;
	float work_ratio = config.work_sound_val1 + config.work_sound_val2 * (getCurSpeedShow() * 100 / config.engine_sound_speed);
	float work_base_pitch = config.work_sound_pitch;
	float work_base_vol = config.work_sound_volume;

	auto centroid = getChassisMassCenter();
	WCoord pos = getPosition() + WCoord(centroid.x, centroid.y, centroid.z);

	if (state == 1) {
		if (!m_vehicleSnd.sthrusterSnd)
			m_vehicleSnd.sthrusterSnd = m_pWorld->getEffectMgr()->playLoopSound(pos, name, volume, 1.0f);
		else
		{
			m_vehicleSnd.sthrusterSnd->setPitch(work_ratio * work_base_pitch);
			m_vehicleSnd.sthrusterSnd->setVolume(volume);
			m_vehicleSnd.sthrusterSnd->setPosition(pos.toVector3());
		}
	}
	else {
		OGRE_RELEASE(m_vehicleSnd.sthrusterSnd);
	}
}

DynamicVehicleDecorator* ActorVehicleAssemble::getCostPartWithPos(const WCoord &blockPos)
{
	for (int i = 0; i < (int)m_CostParts.size(); i++)
	{
		if (blockPos == m_CostParts[i]->getPos())
		{
			return m_CostParts[i];
		}
	}
	return NULL;
}

MINIW::SThrusterParam * ActorVehicleAssemble::getSThrusterParamWithPos(const WCoord &blockpos)
{
	int groupid = getPartGroupIdWithPos(blockpos);
	Rainbow::Vector3f tmpPos = Rainbow::Vector3f((float)blockpos.x, (float)blockpos.y, (float)blockpos.z);
	if (groupid >= (int)m_DynamicCreateParams.size())
		return NULL;
	for (int i = 0; i < (int)m_DynamicCreateParams[groupid].mSThrusterParams.size(); i++)
	{
		if (tmpPos == m_DynamicCreateParams[groupid].mSThrusterParams[i].mBlockPos)
		{
			return &m_DynamicCreateParams[groupid].mSThrusterParams[i];
		}
	}
	// 	auto iter = m_DynamicCreateParams[groupid].mSThrusterParams.find(MINIW::OPos(blockpos.x, blockpos.y, blockpos.z));
	// 	if (iter != m_DynamicCreateParams[groupid].mSThrusterParams.end())
	// 	{
	// 		return &iter->second;
	// 	}
	// 	return &m_DynamicCreateParams[groupid].mSThrusterParams[Rainbow::Vector3f(blockpos.x, blockpos.y, blockpos.z)];
	return NULL;
}

MINIW::SThrusterParam * ActorVehicleAssemble::getSThrusterParamWithGroupIdIndex(int groupid, int index)
{
	return &m_DynamicCreateParams[groupid].mSThrusterParams[index];
}

MINIW::ThrusterParam * ActorVehicleAssemble::getThrusterParamWithGroupIdIndex(int groupid, int index)
{
	return &m_DynamicCreateParams[groupid].mThrusterParams[index];
}

MINIW::BoatThrusterParam* ActorVehicleAssemble::getBoatThrusterParamWithGroupIdIndex(int groupid, int index)
{
	return &m_DynamicCreateParams[groupid].mBoatThrusterParams[index];
}



bool ActorVehicleAssemble::energyCost(int cost, int costType)
{
	if (m_bIsUnlimitEnergy) return true;
	if (!m_hasfuel) return false;
	int totalCost = cost;
	bool isCost = false;
	for (int i = 0; i < (int)m_CostParts.size(); i++)
	{
		if (m_CostParts[i]->getFuncDef()->func_id == 5)
		{
			DynamicCost* dynamicCost = dynamic_cast<DynamicCost*>(m_CostParts[i]);
			if (dynamicCost)
			{
				WCoord blockpos = dynamicCost->getPos();
				int x = blockpos.x;
				int y = blockpos.y;
				int z = blockpos.z;
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				int curValue = dynamicCost->getCurValue();
				if (srcBlock.getResID() > 0 &&
					curValue > 0 && m_Blocks[x][y][z]->m_iCurLife > 0)
				{
					isCost = true;
					if (curValue >= totalCost)
					{
						int leaveFuel = curValue - totalCost;
						setFuelWithIndex(leaveFuel, i);
						totalCost = 0;
						break;
					}
					else
					{
						totalCost -= curValue;
						setFuelWithIndex(0, i);
					}
				}
			}
		}
	}
	if (isCost && totalCost > 0)
	{
		m_hasfuel = false;
		return false;
	}
	return true;
}

void ActorVehicleAssemble::getHasCollideLogicBlockRealPos(std::map<WCoord, WCoord> &list)
{
	for (int i = 0; i < (int)m_vActorCollideLogicBlocks.size(); i++)
	{
		list[m_vActorCollideLogicBlocks[i]] = (this->convertWcoord(m_vActorCollideLogicBlocks[i]));
	}
}

WCoord ActorVehicleAssemble::getRiderPosition(ClientActor *ridden)
{
	Rainbow::Vector3f seatPos(0, 0, 0);
	if (getNumRiddenPos() > 1)
	{
		int i = findRiddenIndex(ridden);
		if (i <= 0)
			seatPos = m_SeatPos.toVector3();
		else
			seatPos = m_OtherSeatPos[i - 1].toVector3();
	}
	else
	{
		seatPos = m_SeatPos.toVector3();
	}

	if (getLocoMotion() == NULL) return m_SeatPos;
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	Rainbow::Vector3f pos = loc->m_Position.toVector3();
	Rainbow::Vector3f addPos = loc->m_RotateQuat * seatPos;
	//loc->m_RotateQuat.rotate(addPos, seatPos);
	pos += addPos;
	return WCoord(pos.x, pos.y, pos.z);
	//return WCoord(0, 0, 0);
}

int ActorVehicleAssemble::findRiddenIndex(ClientActor* ridden)
{
	if (ridden == NULL)
		return -1;

	int n = getNumRiddenPos();
	for (int i = 0; i < n; i++)
	{
		WORLD_ID riddenByActorID = getRiddenByActorID(i);
		WORLD_ID objectId = ridden->getObjId();
		if (riddenByActorID == objectId)
			return i;
	}

	return -1;
}

bool ActorVehicleAssemble::isInAir()
{
	//特效播放会自动同步客机，所以先只在主机做判断
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc)
	{
		if (!m_pWorld->isRemoteMode())
			return loc->getIsInAir();
	}
	return false;
}

WCoord ActorVehicleAssemble::getMassCenterWcoord()
{
	auto pos = this->getChassisMassCenter();
	WCoord centroid = getPosition() + WCoord(pos.x, pos.y, pos.z);
	return centroid;
}

bool ActorVehicleAssemble::isBurning()
{
	//减少调用次数，只有在m_effectTick=0时，才会调用
	if (m_effectTick > 0) return false;

	for (int x = 0; x < MAX_DIM_X; x++)
	{
		for (int y = 0; y < MAX_DIM_Y; y++)
		{
			for (int z = 0; z < MAX_DIM_Z; z++)
			{
				if (m_Blocks[x][y][z]->m_iCurLife == 0)
					continue;
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (!srcBlock || srcBlock->isEmpty())
					continue;
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(m_Blocks[x][y][z]->m_Block.getResID());
				if (!physicsPartsDef)
					continue;
				//车轮特殊处理一下
				bool isWheelPart = false;
				//if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					for (int i = 0; i < (int)physicsPartsDef->EffectFunctions.size(); i++)
					{
						PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
						if (functiondef->func_id == 1) //车轮
						{
							isWheelPart = true;
							break;
						}
					}
				}
				WCoord block_wpos = convertWcoord(WCoord(x, y, z));
				WCoord minpos = isWheelPart ? CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)) : CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2));
				WCoord maxpos = isWheelPart ? CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE, 0, BLOCK_SIZE)) : CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE, BLOCK_SIZE / 2));
				for (int Z = minpos.z; Z <= maxpos.z; Z++)
				{
					for (int X = minpos.x; X <= maxpos.x; X++)
					{
						for (int Y = minpos.y; Y <= maxpos.y; Y++)
						{
							int id = m_pWorld->getBlockID(X, Y, Z);
							if (id == BLOCK_FIRE || BlockMaterialMgr::isLava(id)/*id == BLOCK_STILL_LAVA || id == BLOCK_FLOW_LAVA*/)
							{
								return true;
							}
						}
					}
				}

			}
		}
	}
	return false;
}

bool ActorVehicleAssemble::inInLava()
{
	//减少调用次数，只有在m_effectTick=0时，才会调用
	if (m_effectTick > 0) return false;

	for (int x = 0; x < MAX_DIM_X; x++)
	{
		for (int y = 0; y < MAX_DIM_Y; y++)
		{
			for (int z = 0; z < MAX_DIM_Z; z++)
			{
				if (m_Blocks[x][y][z]->m_iCurLife == 0)
					continue;
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				if (!srcBlock || srcBlock->isEmpty())
					continue;
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(m_Blocks[x][y][z]->m_Block.getResID());
				if (!physicsPartsDef)
					continue;
				//车轮特殊处理一下
				bool isWheelPart = false;
				//if (m_pWorld && !m_pWorld->isRemoteMode())
				{
					for (int i = 0; i < (int)physicsPartsDef->EffectFunctions.size(); i++)
					{
						PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
						if (functiondef->func_id == 1) //车轮
						{
							isWheelPart = true;
							break;
						}
					}
				}
				WCoord block_wpos = convertWcoord(WCoord(x, y, z));
				WCoord minpos = isWheelPart ? CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE)) : CoordDivBlock(block_wpos - WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2));
				WCoord maxpos = isWheelPart ? CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE, 0, BLOCK_SIZE)) : CoordDivBlock(block_wpos + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE, BLOCK_SIZE / 2));
				for (int Z = minpos.z; Z <= maxpos.z; Z++)
				{
					for (int X = minpos.x; X <= maxpos.x; X++)
					{
						for (int Y = minpos.y; Y <= maxpos.y; Y++)
						{
							int id = m_pWorld->getBlockID(X, Y, Z);
							if (BlockMaterialMgr::isLava(id)/*id == BLOCK_STILL_LAVA || id == BLOCK_FLOW_LAVA*/)
							{
								return true;
							}
						}
					}
				}

			}
		}
	}
	return false;
}

VehicleBlock* ActorVehicleAssemble::getVehicleBlock(const WCoord &pos)
{
	return getVehicleBlock(pos.x, pos.y, pos.z);
}

VehicleBlock* ActorVehicleAssemble::getVehicleBlock(int x, int y, int z)
{
	if (x >= 0 && y >= 0 && z >= 0 && x<MAX_DIM_X && y<MAX_DIM_Y && z<MAX_DIM_Z)
	{
		return m_Blocks[x][y][z];
	}
	else
	{
		return &m_EmptyBlock;
	}
}

Block ActorVehicleAssemble::getBlock(const WCoord &pos) const
{
	return getBlock(pos.x, pos.y, pos.z);
}

Block ActorVehicleAssemble::getBlock(int x, int y, int z) const
{
	if (x >= 0 && y >= 0 && z >= 0 && x<MAX_DIM_X && y<MAX_DIM_Y && z<MAX_DIM_Z)
	{
		return m_Blocks[x][y][z]->m_Block;
	}
	else
	{
		return m_EmptyBlock.m_Block;
	}
}

Block ActorVehicleAssemble::getNeighborBlock(const WCoord &pos, DirectionType dir) const
{
	WCoord ng = NeighborCoord(pos, dir);
	return getBlock(ng);
}

int ActorVehicleAssemble::getBlockID(const WCoord &pos)
{
	return getBlock(pos).getResID();
}

int ActorVehicleAssemble::getBlockData(const WCoord &pos)
{
	return getBlock(pos).getData();
}

int ActorVehicleAssemble::getBlockDataEx(const WCoord &pos)
{
	return getBlock(pos).getDataEx();
}

void ActorVehicleAssemble::updateChassisEntity(const std::vector<WCoord> &pos)
{
#ifdef IWORLD_SERVER_BUILD
	return;
#endif
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (!loc->m_PhysActor)
		return;
	if (ActorVehicleAssemble::m_bEnterWorld)
		return;
	std::vector<MechaMeshObject*> chassis_pmeshes(m_BlockGroupNum);
	WCoord start = WCoord(0, 0, 0);
	WCoord centerPos = WCoord(start.x + MAX_DIM_X / 2, start.y, start.z + MAX_DIM_Z / 2);
	char *mechaSectionCheck = new char[m_BlockGroupNum];
	memset(mechaSectionCheck, 0, m_BlockGroupNum);
	for (int i = 0; i < (int)pos.size(); i++)
	{
		VehicleBlock* pVehicleBlock = getVehicleBlock(pos[i]);
		Block pBlock = pVehicleBlock->m_Block;
		WCoord blockpos = pos[i] - centerPos;
		if (pVehicleBlock->m_GroupIdSelf >= 0 && pVehicleBlock->m_GroupIdSelf < m_BlockGroupNum)
		{
			m_pMechaSections[pVehicleBlock->m_GroupIdSelf]->addBlock(blockpos, pBlock, 0xff);
			if (pBlock.getResID() != BLOCK_SUSPENSION_JOINT && pBlock.getResID() != BLOCK_JOINT_ARM_PRISMATIC)
				mechaSectionCheck[pVehicleBlock->m_GroupIdSelf] = 1;
			if (pBlock.getResID() == BLOCK_JOINT_SPHERICAL || pBlock.getResID() == BLOCK_JOINT_T_REVOLUTE)
			{
				mechaSectionCheck[pVehicleBlock->m_GroupIdChild - 1] = 1;
			}
		}
	}
	/*for (int y = 0; y < MAX_DIM_Y; y++)
	{
	for (int z = 0; z < MAX_DIM_Z; z++)
	{
	for (int x = 0; x < MAX_DIM_X; x++)
	{
	WCoord pos(start.x + x, start.y + y, start.z + z);
	Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
	if (srcBlock.getResID() == BLOCK_JOINT_SPHERICAL || srcBlock.getResID() == BLOCK_JOINT_T_REVOLUTE)
	{
	if (mechaSectionCheck[m_Blocks[x][y][z]->m_GroupIdJoint - 1])
	{
	mechaSectionCheck[m_Blocks[x][y][z]->m_GroupId] = 1;
	}
	}
	}
	}
	}*/

	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		
		if (mechaSectionCheck[i])
			chassis_pmeshes[i] = MechaMeshObject::Create(false);//  ENG_NEW(MechaSectionMesh)(false);
		else
			chassis_pmeshes[i] = NULL;
	}
	std::map<int, MechaMeshObject*> entitiesChassisExMesh;


	std::vector<WCoord> meshexs;
	for (int y = 0; y < MAX_DIM_Y; y++)
	{
		for (int z = 0; z < MAX_DIM_Z; z++)
		{
			for (int x = 0; x < MAX_DIM_X; x++)
			{
				WCoord pos(start.x + x, start.y + y, start.z + z);
				Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
				//if (!mechaSectionCheck[m_Blocks[x][y][z]->m_GroupId])
				//	continue;
				if (!srcBlock || srcBlock->isEmpty())
					continue;
				if (m_Blocks[x][y][z]->m_iCurLife == 0)
				{
					continue;
				}
				WCoord srcblockpos = pos - centerPos;
				//m_pMechaSection->addBlock(blockpos, *srcBlock, 0xff);

				int srcblockid = srcBlock->getResID();
				//非车轮, 即车身方块
				if (!isFunction(srcblockid, WHEELFUN))
				{
					int blockGroupId = m_Blocks[x][y][z]->m_GroupIdSelf;
					if (blockGroupId >= 0 && blockGroupId < m_BlockGroupNum)
					{
#ifndef IWORLD_SERVER_BUILD
						BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(srcblockid);
						if (mtl)
						{
							if (srcblockid != BLOCK_SUSPENSION_JOINT && srcblockid != BLOCK_JOINT_ARM_PRISMATIC && mechaSectionCheck[m_Blocks[x][y][z]->m_GroupIdSelf])
							{
								mtl->createBlockMesh(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), srcblockpos, chassis_pmeshes[blockGroupId]->GetSectionMesh());
							}
						}
						if (srcblockid == BLOCK_JOINT_SPHERICAL || srcblockid == BLOCK_JOINT_T_REVOLUTE)//需要渲染两个地方
						{
							if ((m_Blocks[x][y][z]->m_GroupIdChild > 0) && ((m_Blocks[x][y][z]->m_GroupIdChild - 1) < (int)m_JointCreateParams.size()) && mechaSectionCheck[m_Blocks[x][y][z]->m_GroupIdChild - 1])
							{
								//mtl->createBlockMeshEx(m_pMechaSections[blockGroupId]->GetBuildSectionMeshData(), srcblockpos, chassis_pmeshes[m_Blocks[x][y][z]->m_GroupIdJoint - 1]);
								meshexs.push_back(WCoord(x, y, z));
							}
						}
#endif						
					}

				}
			}
		}
	}
	for (int i = 0; i<(int)pos.size(); i++)
	{
		VehicleBlock* pVehicleBlock = getVehicleBlock(pos[i]);
		Block* pBlock = &pVehicleBlock->m_Block;
		WCoord blockpos = pos[i] - centerPos;
		if (pVehicleBlock->m_GroupIdSelf >= 0 && pVehicleBlock->m_GroupIdSelf < m_BlockGroupNum)
		{
			if (pBlock->getResID() == BLOCK_SUSPENSION_JOINT || pBlock->getResID() == BLOCK_JOINT_ARM_PRISMATIC)
			{
				BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(pBlock->getResID());
				if (mtl)
				{
					entitiesChassisExMesh[(pos[i].x << 10) + (pos[i].y << 5) + pos[i].z] = MechaMeshObject::Create(false);
#ifndef IWORLD_SERVER_BUILD
					mtl->createBlockMesh(m_pMechaSections[pVehicleBlock->m_GroupIdSelf]->GetBuildSectionMeshData(), blockpos, entitiesChassisExMesh[(pos[i].x << 10) + (pos[i].y << 5) + pos[i].z]->GetSectionMesh());
#endif
				}
			}
		}
	}


#ifndef IWORLD_SERVER_BUILD
	for (int i = 0; i<(int)meshexs.size(); i++)
	{
		int x = meshexs[i].x;
		int y = meshexs[i].y;
		int z = meshexs[i].z;
		WCoord blockpos = meshexs[i] - centerPos;
		BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(m_Blocks[x][y][z]->m_Block.getResID());
		if (mtl)
		{
			if (m_Blocks[x][y][z]->m_GroupIdChild > 0 && m_Blocks[x][y][z]->m_GroupIdChild <= m_BlockGroupNum && m_ChassisBlockNums[m_Blocks[x][y][z]->m_GroupIdChild - 1]>0)
			{
				if (mechaSectionCheck[m_Blocks[x][y][z]->m_GroupIdChild - 1])
					mtl->createBlockMeshEx(m_pMechaSections[m_Blocks[x][y][z]->m_GroupIdSelf]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[m_Blocks[x][y][z]->m_GroupIdChild - 1]->GetSectionMesh());
			}
			else if (m_ChassisBlockNums[m_Blocks[x][y][z]->m_GroupIdSelf]>0)
			{
				if (mechaSectionCheck[m_Blocks[x][y][z]->m_GroupIdSelf])
					mtl->createBlockMeshEx(m_pMechaSections[m_Blocks[x][y][z]->m_GroupIdSelf]->GetBuildSectionMeshData(), blockpos, chassis_pmeshes[m_Blocks[x][y][z]->m_GroupIdSelf]->GetSectionMesh());
			}
		}
	}
#endif
	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		int phyindex = loc->getPhysIndexWithGroupId(i);
		if (m_ChassisBlockNums[i] && mechaSectionCheck[i] && phyindex < (int)m_EntitiesChassis.size())
		{
			m_EntitiesChassis[phyindex]->DestroyGameObject();
			//OGRE_RELEASE(m_EntitiesChassis[phyindex]));
		}
	}
	std::vector<Rainbow::Entity*> entitiesChassis = m_EntitiesChassis;
	m_EntitiesChassis.clear();
	for (int i = 0; i<m_BlockGroupNum; i++)
	{
		Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
		if (i > 0 && m_JointCreateParams.size() > 1)
		{
			offset = m_JointCreateParams[i].mPos0;
		}
		int phyindex = loc->getPhysIndexWithGroupId(i);
		if (chassis_pmeshes[i])
		{
			if (m_ChassisBlockNums[i]>0)
			{
				Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
				if (i>0 && m_JointCreateParams.size()>0)
				{
					offset = m_JointCreateParams[i].mPos0;
				}
				if (mechaSectionCheck[i] || i >= (int)entitiesChassis.size())
					createChassisEntity(chassis_pmeshes[i], offset);
				else
					m_EntitiesChassis.push_back(entitiesChassis[i]);
			}
			else
			{
				//chassis_pmeshes[i]->Release();
			}
		}
		else if (m_ChassisBlockNums[i]>0 && phyindex < (int)entitiesChassis.size() && entitiesChassis[phyindex])
		{
			m_EntitiesChassis.push_back(entitiesChassis[phyindex]);
		}
	}

	//独立的方块处理
	auto iterblock = entitiesChassisExMesh.begin();
	while (iterblock != entitiesChassisExMesh.end())
	{
		Rainbow::Vector3f offset = Rainbow::Vector3f::zero;
		//fromvalue>>10, (fromvalue>>5)&0x1f, (fromvalue & 0x1f)
		VehicleBlock *srcBlock = m_Blocks[iterblock->first >> 10][(iterblock->first >> 5) & 0x1f][iterblock->first & 0x1f];
		if (srcBlock->m_GroupIdSelf>0 && m_JointCreateParams.size()>0)
		{
			offset = m_JointCreateParams[srcBlock->m_GroupIdSelf].mPos0;
		}
		Rainbow::Entity* chassis_entity = Rainbow::Entity::Create();
		chassis_entity->AttachToScene(m_pWorld->getScene());
		iterblock->second->OnCreate();
		iterblock->second->SetPosition(Rainbow::Vector3f(-BLOCK_SIZE / 2 - offset.x, -offset.y, -BLOCK_SIZE / 2 - offset.z));  // -m_nOffsetYWorkshopAndWorld * BLOCK_SIZE
		chassis_entity->BindObject(0, iterblock->second);
		//chassis_entity->addRenderUsageBits(RU_SHADOWMAP);
		//iterblock->second->addRenderUsageBits(RU_SHADOWMAP);

		auto iter_entity = m_EntitiesChassisEx.find(iterblock->first);
		if (iter_entity != m_EntitiesChassisEx.end())
		{
			//iter_entity->second->release();
			/*iter_entity->second->DestroyGameObject();*/
			DESTORY_GAMEOBJECT_BY_COMPOENT(iter_entity->second);
			m_EntitiesChassisEx.erase(iter_entity);
		}
		m_EntitiesChassisEx[iterblock->first] = chassis_entity;

		/*Vector4f lightparam(0,0,0,0);
		WCoord center = getMassCenterWcoord();
		center.y += getLocoMotion()->m_BoundHeight/2 + 100;
		if (m_pWorld)
		{
		Vector4f lightparams[3] = {};
		CollideAABB box;
		WCoord start, dims;
		getVehicleColliderBox(start, dims);
		box.dim = dims;
		box.pos = start;
		m_pWorld->getBlockLightValue2(lightparams[0].x, lightparams[0].y, CoordDivBlock(center - dims/2));
		m_pWorld->getBlockLightValue2(lightparams[1].x, lightparams[1].y, CoordDivBlock(center +  dims/2));
		m_pWorld->getBlockLightValue2(lightparams[2].x, lightparams[2].y, CoordDivBlock(center));
		lightparam.x = max(max(lightparams[0].x, lightparams[1].x), lightparams[2].x);
		lightparam.y = max(max(lightparams[0].y, lightparams[1].y), lightparams[2].y);
		}

		MechaSectionMesh *mesh = dynamic_cast<MechaSectionMesh*>(iterblock->second);
		if (mesh)
		{
		mesh->setLight(lightparam.x, lightparam.y);
		}*/
		iterblock++;
	}
	OGRE_DELETE_ARRAY(mechaSectionCheck);
	resetEntityPos(0);
}

bool ActorVehicleAssemble::setBlockAll(const WCoord &pos, int blockid, int data, int flags, int dataEx)
{
	if (!m_pVehicleWorld) return false;
	
	if (pos.x < 0 || pos.y < 0 || pos.z < 0 || pos.x >= MAX_DIM_X || pos.y >= MAX_DIM_Y || pos.z >= MAX_DIM_Z) return false;

	Block pBlock = getBlock(pos);
	int oldid = pBlock.getResID();
	int olddata = pBlock.getData();
	auto oldMtl = pBlock.GetBlockMaterial();
	BlockMaterial *oldmtl = NULL;

	if (oldid == blockid && olddata == data) return false;
	else
	{
		m_Blocks[pos.x][pos.y][pos.z]->m_Block.setAll(blockid, data);

		oldmtl = g_BlockMtlMgr.getMaterial(oldid);
		BlockMaterial* newmtl = g_BlockMtlMgr.getMaterial(blockid);

		if (!m_pWorld->isRemoteMode() && oldmtl)
		{
			oldmtl->DoOnBlockRemoved(m_pVehicleWorld, pos, oldid, olddata);
		}

		if (oldid != blockid && oldmtl && oldmtl->hasContainer() && !BlockMaterial::isBuddyBlockID(oldid, blockid) && flags != -1)
		{
			m_pVehicleWorld->getContainerMgr()->destroyContainer(pos);
		}
		if (blockid != oldid)
		{
			if (blockid == 0)
			{
				m_Blocks[pos.x][pos.y][pos.z]->m_iCurLife = 0;
				m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdSelf = 0;
				if (oldid == BLOCK_CLAW)
				{
					unbindOneSonVehicle(pos.x, pos.y, pos.z);
				}
			}
			else {
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
				if (physicsPartsDef)
				{
					m_Blocks[pos.x][pos.y][pos.z]->m_iCurLife = physicsPartsDef->Life;
					m_Blocks[pos.x][pos.y][pos.z]->m_GroupIdSelf = 0;
				}
				
			}

			bool oldphy = false;
			const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(oldid);
			if (blockDef && (blockDef->PhyCollide > 0))
			{
				oldphy = true;
			}

			bool newphy = false;
			blockDef = GetDefManagerProxy()->getBlockDef(blockid);
			if (blockDef && (blockDef->PhyCollide > 0))
			{
				newphy = true;
			}

			if (newphy != oldphy)
				m_mustReCreatePhysics = true;
		}

		if (newmtl)
		{
			if (!m_pVehicleWorld->isRemoteMode())
			{
				newmtl->DoOnBlockAdded(m_pVehicleWorld, pos);
			}

			if (newmtl->hasContainer())
			{
				WorldContainer *container = m_pVehicleWorld->getContainerMgr()->getContainer(pos);
				if (container == NULL)
				{
					container = newmtl->createContainer(m_pVehicleWorld, pos);
					if (container)
					{
						container->m_OwnerUin = 0;
						m_pVehicleWorld->getContainerMgr()->spawnContainer(container);
					}
				}

				if (container) container->updateContainingBlockInfo();
			}
		}
	}

	blockChange(pos);
	//updateChassisEntity(pos);
	return true;
}

void ActorVehicleAssemble::unbindOneSonVehicle(int x, int y, int z)
{
	Block pBlock = getBlock(WCoord(x, y, z));
	//int oldid = pBlock.getResID();
	//if (oldid == BLOCK_CLAW)
	{
		if (m_BindVehilcIDs.size())
		{
			//(x<<10) + (y<<5) + z
			auto son = m_BindVehilcIDs.begin();
			while (son != m_BindVehilcIDs.end())
			{
				if (son->first == ((x << 10) + (y << 5) + z))
				{
					for (int i = 0; i<(int)m_ClawBlocks.size(); i++)
					{
						if (m_ClawBlocks[i].x == x && m_ClawBlocks[i].y == y && m_ClawBlocks[i].z == z)
						{
#ifndef IWORLD_SERVER_BUILD
							EffectDeleteProxy::SafeDeleteEffect(m_ClawEffects[i]);
#endif
							break;
						}
					}

					ClientActor *actor = (static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(son->second));
					ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
					if (vehicle)
					{
						long long fatherid = vehicle->getRootFatherVehilcID();
						ActorVehicleAssemble* vehicle_ = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(fatherid));
						if (vehicle_)
						{
							vehicle_->detachAllPhysActor();
						}
						vehicle->m_FatherVehilcID = 0;
						vehicle->m_FatherVehilcPos = WCoord(0, 0, 0);
					}
					if (actor)
					{
						auto functionWrapper = actor->getFuncWrapper();
						if (functionWrapper)
						{
							functionWrapper->setCanMove(true);
						}
						auto vehicleComponent = actor->getActorBindVehicle();
						if (vehicleComponent)
						{
							vehicleComponent->UnBind(true);
						}
					}
					m_BindVehilcIDs.erase(son);
					break;
				}
				son++;
			}
		}
	}
}

bool ActorVehicleAssemble::setBlockData(const WCoord &pos, int data, int flags)
{
	Block pBlock = getBlock(pos);
// 	int oldid = pBlock.getResID();
// 	int olddata = pBlock.getData();

	bool changed = false;
	if (pBlock.getData() != data)
	{
		m_Blocks[pos.x][pos.y][pos.z]->m_Block.setData(data);
		changed = true;
	}
	// 	if (olddata!=data)
	// 	{
	// 		const BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(oldid);
	// 		if (blockDef && blockDef->PhyCollide == 3)
	// 			m_mustReCreatePhysics = true;
	// 	}
	//if (changed)
	//   blockChange(pos);

	//updateChassisEntity(pos);
	return changed;
}

bool ActorVehicleAssemble::setBlockDataEx(const WCoord &pos, int dataEx)
{
	Block pBlock = getBlock(pos);
	bool changed = false;
	if (pBlock.getDataEx() != dataEx)
	{
		m_Blocks[pos.x][pos.y][pos.z]->m_Block.setDataEx(dataEx);
		changed = true;
	}
	return changed;
}

void ActorVehicleAssemble::blockChange(const WCoord &blockpos)
{
	//统计变化
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		if (m_NumBlocks < 64)
		{
			if (blockpos.x < 0 || blockpos.y<0 || blockpos.z < 0 || blockpos.x >= MAX_DIM_X || blockpos.y >= MAX_DIM_Y || blockpos.z >= MAX_DIM_Z)
			{
				return;
			}
			unsigned int n = (unsigned int)((blockpos.x << 16) | (blockpos.z << 8) | blockpos.y);
			for (int i = 0; i < m_NumBlocks; i++)
			{
				if (m_ChangedBlocks[i] == n) return;
			}
			m_ChangedBlocks[m_NumBlocks++] = n;
		}
	}
}

void ActorVehicleAssemble::markBlockForUpdate(const WCoord &blockpos, bool notify)
{
	//统计变化
	blockChange(blockpos);
	//updateChassisEntity(blockpos);
}

void ActorVehicleAssemble::sendChunkUpdateToPlayers()
{
	if (m_NumBlocks == 0) return;
	if (m_NumBlocks >= 64)
	{
		std::vector<WorldContainer *> containers;
		VehicleContainerMgr* containermgr = NULL;
		if (m_pVehicleWorld)
		{
			containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		}
		SyncVehicleBlock sync_blocks[MAX_DIM_X][MAX_DIM_Y][MAX_DIM_Z];

		/**/
		for (int x = 0; x < MAX_DIM_X; x++)
		{
			for (int y = 0; y < MAX_DIM_Y; y++)
			{
				for (int z = 0; z < MAX_DIM_Z; z++)
				{
					sync_blocks[x][y][z].m_Block.setAll(m_Blocks[x][y][z]->m_Block.getResID(), m_Blocks[x][y][z]->m_Block.getData());
					sync_blocks[x][y][z].m_Info = (m_Blocks[x][y][z]->m_iCurLife << 16) + m_Blocks[x][y][z]->m_GroupIdSelf;

					if (containermgr && m_Blocks[x][y][z]->m_Block.getResID() && m_Blocks[x][y][z]->m_iCurLife > 0)
					{
						WCoord blockpos(x, y, z);
						WorldContainer* container = containermgr->getContainer(blockpos);
						if (container) containers.push_back(container);
					}
				}
			}
		}

		int containersize = 0;
		unsigned char * containerdata = NULL;
		flatbuffers::FlatBufferBuilder builder;
		if (!containers.empty())
		{
			auto vec = CreateContainerVec(builder, containers);
			auto obj = FBSave::CreateChunkContainers(builder, vec);
			builder.Finish(obj);
			containerdata = builder.GetBufferPointer();
			//size_t srclen = builder.GetSize();
			containersize = builder.GetSize();
		}


		PB_VehicleAssembleBlockAllHC vehicleAssembleBlockAllHC;
		//unsigned char *src = (unsigned char *)sync_blocks;
		unsigned char* src = new unsigned char[sizeof(sync_blocks) + containersize];
		size_t srclen = sizeof(sync_blocks) + containersize;
		memcpy(src, sync_blocks, sizeof(sync_blocks));
		if (containersize)
			memcpy(src + sizeof(sync_blocks), containerdata, containersize);
		int compresstype = CompressTool::COMPRESS_LZMA;
		CompressTool ctool(compresstype);
		size_t destlen = ctool.compressBound(srclen);

		char* blobDetail = new char[destlen];
		if (!ctool.compress(blobDetail, destlen, src, srclen, 0))
		{
			assert(0);
			OGRE_DELETE_ARRAY(blobDetail);
			OGRE_DELETE_ARRAY(src);
			return;
		}
		vehicleAssembleBlockAllHC.set_objid(getObjId());
		vehicleAssembleBlockAllHC.set_blobdetail(blobDetail, destlen);
		vehicleAssembleBlockAllHC.set_unziplen(srclen | (compresstype << 28));
		vehicleAssembleBlockAllHC.set_bloblen(destlen);


		VehicleAssembleLocoMotion* loc = static_cast<VehicleAssembleLocoMotion*>(getLocoMotion());
		//同步位置信息
		for (int i = 0; i < (int)loc->m_ChassisUpdatePos.size(); i++)
		{
			PB_VehiclePosDesc* descPB = vehicleAssembleBlockAllHC.add_chassispos();

			WCoord chassisPos = WCoord(loc->m_ChassisUpdatePos[i].m_PPosition.x, loc->m_ChassisUpdatePos[i].m_PPosition.y, loc->m_ChassisUpdatePos[i].m_PPosition.z);
			PB_Vector3* pos = descPB->mutable_position();
			pos->set_x(chassisPos.x);
			pos->set_y(chassisPos.y);
			pos->set_z(chassisPos.z);

			Quaternionf chassisRot = loc->m_ChassisUpdatePos[i].m_RotateQuat;
			PB_Quaternion* rot = descPB->mutable_rotatequat();
			rot->set_x(chassisRot.x);
			rot->set_y(chassisRot.y);
			rot->set_z(chassisRot.z);
			rot->set_w(chassisRot.w);
		}

		for (int i = 0; i < (int)loc->m_WheelUpdatePos.size(); i++)
		{
			PB_VehiclePosDesc* descPB = vehicleAssembleBlockAllHC.add_wheelpos();

			WCoord wheelPos = WCoord(loc->m_WheelUpdatePos[i].m_PPosition.x, loc->m_WheelUpdatePos[i].m_PPosition.y, loc->m_WheelUpdatePos[i].m_PPosition.z);
			PB_Vector3* pos = descPB->mutable_position();
			pos->set_x(wheelPos.x);
			pos->set_y(wheelPos.y);
			pos->set_z(wheelPos.z);

			Quaternionf wheelRot = loc->m_WheelUpdatePos[i].m_RotateQuat;
			PB_Quaternion* rot = descPB->mutable_rotatequat();
			rot->set_x(wheelRot.x);
			rot->set_y(wheelRot.y);
			rot->set_z(wheelRot.z);
			rot->set_w(wheelRot.w);
		}

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLEASSEMBLEBLOCK_ALL_HC, vehicleAssembleBlockAllHC, this, true);
		OGRE_DELETE_ARRAY(blobDetail);
		OGRE_DELETE_ARRAY(src);
	}
	else
	{
		PB_VehicleAssembleBlockUpdateHC vehicleAssembleBlockUpdateHC;
		std::vector<WorldContainer *> containers;
		VehicleContainerMgr* containermgr = NULL;
		if (m_pVehicleWorld)
		{
			containermgr = dynamic_cast<VehicleContainerMgr*>(m_pVehicleWorld->getContainerMgr());
		}
		for (int i = 0; i < m_NumBlocks; i++)
		{
			unsigned int pos = m_ChangedBlocks[i];
			WCoord blockpos((int)(pos >> 16), (int)(pos & 255), (int)((pos >> 8) & 255));
			unsigned int v = getBlock(blockpos).getOriginData();
			unsigned int vex = getBlock(blockpos).getDataEx();
			::game::hc::AssembleBlockInfo* info = vehicleAssembleBlockUpdateHC.add_blockinfo();
			info->set_block(pos);
			info->set_data(v);
			info->set_info((m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_iCurLife << 16) | m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_GroupIdSelf);
			info->set_blockex(vex);
			if (containermgr)
			{
				WorldContainer *container = containermgr->getContainer(blockpos);
				if (container) containers.push_back(container);
			}
		}

		if (!containers.empty())
		{
			flatbuffers::FlatBufferBuilder builder;
			auto vec = CreateContainerVec(builder, containers);
			auto obj = FBSave::CreateChunkContainers(builder, vec);
			builder.Finish(obj);

			unsigned char *src = builder.GetBufferPointer();
			size_t srclen = builder.GetSize();
			if (srclen < MAX_BLOCKUPDATE_CLEN)
			{
				vehicleAssembleBlockUpdateHC.set_containerbuf((const char*)src, srclen);
			}
		}

		vehicleAssembleBlockUpdateHC.set_objid(getObjId());

		VehicleAssembleLocoMotion* loc = static_cast<VehicleAssembleLocoMotion*>(getLocoMotion());
		//同步位置信息
		for (int i = 0; i < (int)loc->m_ChassisUpdatePos.size(); i++)
		{
			PB_VehiclePosDesc* descPB = vehicleAssembleBlockUpdateHC.add_chassispos();

			WCoord chassisPos = WCoord(loc->m_ChassisUpdatePos[i].m_PPosition.x, loc->m_ChassisUpdatePos[i].m_PPosition.y, loc->m_ChassisUpdatePos[i].m_PPosition.z);
			PB_Vector3* pos = descPB->mutable_position();
			pos->set_x(chassisPos.x);
			pos->set_y(chassisPos.y);
			pos->set_z(chassisPos.z);

			Quaternionf chassisRot = loc->m_ChassisUpdatePos[i].m_RotateQuat;
			PB_Quaternion* rot = descPB->mutable_rotatequat();
			rot->set_x(chassisRot.x);
			rot->set_y(chassisRot.y);
			rot->set_z(chassisRot.z);
			rot->set_w(chassisRot.w);
		}

		for (int i = 0; i < (int)loc->m_WheelUpdatePos.size(); i++)
		{
			PB_VehiclePosDesc* descPB = vehicleAssembleBlockUpdateHC.add_wheelpos();

			WCoord wheelPos = WCoord(loc->m_WheelUpdatePos[i].m_PPosition.x, loc->m_WheelUpdatePos[i].m_PPosition.y, loc->m_WheelUpdatePos[i].m_PPosition.z);
			PB_Vector3* pos = descPB->mutable_position();
			pos->set_x(wheelPos.x);
			pos->set_y(wheelPos.y);
			pos->set_z(wheelPos.z);

			Quaternionf wheelRot = loc->m_WheelUpdatePos[i].m_RotateQuat;
			PB_Quaternion* rot = descPB->mutable_rotatequat();
			rot->set_x(wheelRot.x);
			rot->set_y(wheelRot.y);
			rot->set_z(wheelRot.z);
			rot->set_w(wheelRot.w);
		}
		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_VEHICLEASSEMBLEBLOCK_UPDATE_HC, vehicleAssembleBlockUpdateHC, this, true);
	}

	//统一一次重新生成模型,避免一个tick因为方块状态的多次改动导致多次生成模型。
	std::vector<WCoord> posv_;
	for (int i = 0; i < m_NumBlocks; i++)
	{
		unsigned int pos = m_ChangedBlocks[i];
		WCoord blockpos((int)(pos >> 16), (int)(pos & 255), (int)((pos >> 8) & 255));
		posv_.push_back(blockpos);
	}
	if (m_mustReCreatePhysics)
	{
		if (m_isReCreate == true)
			reCreateVehicleAssemble();
	}
	else
	{
		updateChassisEntity(posv_);
	}
	m_NumBlocks = 0;
}

bool ActorVehicleAssemble::getJointParamsWithType(int type, std::vector<MINIW::JointCreateParam> &list)
{
	bool ret = false;
	for (int i = 0; i < (int)m_JointCreateParams.size(); i++)
	{
		if (type == m_JointCreateParams[i].mType)
		{
			ret = true;
			list.push_back(m_JointCreateParams[i]);
		}
	}
	return ret;
}

void ActorVehicleAssemble::ChangeDataToGraph()
{
	if (m_VehicleBlockLines.size() == 0)
	{
		return;
	}
	if (m_LineGraph == NULL)
	{
		return;
	}
	std::vector<VehicleBlockLine> tempBlockLines;
	for (auto it = m_VehicleBlockLines.begin(); it != m_VehicleBlockLines.end(); it++)
	{
		if (it->canedit == '2')
		{
			continue;
		}

		int from = -1;
		int target = -1;
		std::map<int, VehicleNodeStruct>::iterator iter1 = m_VehicleBlockNodes.find(it->from);
		if (iter1 != m_VehicleBlockNodes.end())
		{
			from = iter1->second.data;
		}

		std::map<int, VehicleNodeStruct>::iterator iter2 = m_VehicleBlockNodes.find(it->to);
		if (iter2 != m_VehicleBlockNodes.end())
		{
			target = iter2->second.data;
		}
		if (from > -1 && target > -1)
		{
			m_LineGraph->SetValueUndirectedMatric(from, target, 1);
			// 缓存所有的非重复to对应的连线，最后统一通知onNotify一下
			if (it->canedit == '1' || it->canedit == '3')
			{
				bool bFind = false;
				for (auto iter3 = tempBlockLines.begin(); iter3 != tempBlockLines.end(); iter3++)
				{
					if ((it->to) == (iter3->to))
					{
						bFind = true;
					}
				}
				if (!bFind)
				{
					tempBlockLines.push_back((*it));
				}
			}
		}
	}

	for (auto iter = tempBlockLines.begin(); iter != tempBlockLines.end(); iter++)
	{
		WCoord position = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);

		// 所有的toblock都 notify一下endblock
		BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(m_pVehicleWorld->getBlockID(position));
		if (pmtl)
		{
			//pmtl->onNotify(m_pVehicleWorld, position, m_pVehicleWorld->getBlockID(position));
			pmtl->DoOnNotify(m_pVehicleWorld, position, m_pVehicleWorld->getBlockID(position));
			/*pmtl->Event().Emit("OnNotify", SandboxContext()
				.SetData_Usertype<World>("world", m_pVehicleWorld)
				.SetData_UserObject<WCoord>("pos", position)
				.SetData_Number("blockid", (double)m_pVehicleWorld->getBlockID(position))
			);*/
		}
	}
}

bool ActorVehicleAssemble::checkifOverheatPartTryWorking(WCoord pos)
{
	int id = getBlockID(pos);

	//推进器
	if (IsThrusterBlockID(id))
	{
		int thrusterData = getVehicleWorld()->getBlockData(pos);
		int active = thrusterData & 8;
		if (active)
			return true;
	}
	return false;
}

bool ActorVehicleAssemble::checkifOverheatPartCanWork(WCoord pos)
{
	for (int i = 0; i < (int)m_Overheats.size(); i++) {
		PhysicsPartsDef::EffectFunctionsDef* funcDef = m_Overheats[i]->getFuncDef();
		if (funcDef && funcDef->func_id == 6 && pos == m_Overheats[i]->getPos())
		{
			DynamicOverheat* dynamicOverheat = dynamic_cast<DynamicOverheat*>(m_Overheats[i]);
			if (dynamicOverheat)
				return !dynamicOverheat->isThreshold();
		}
	}

	return true;
}

void ActorVehicleAssemble::renderBlockNodes(WCoord blockpos)
{
	if (g_pPlayerCtrl && g_pPlayerCtrl->getCamera())
	{
		VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
		for (auto iter = m_VehicleBlockNodes.begin(); iter != m_VehicleBlockNodes.end(); iter++)
		{
			// 不可编辑节点 跳过
			if (iter->second.canedit != '1')
				continue;

			int posval = iter->first;
			int posx = posval >> 10;
			int posy = (posval >> 5) & 0x1f;
			int posz = posval & 0x1f;
			WCoord pos(posx, posy, posz);

			// 有连线起始点的 根据isLineMatch看看是否有匹配
			if (m_OneLineStart >= 0 && vehicleModule)
			{
				const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(pos));
				if (physicsPartsDef == NULL)
				{
					continue;
				}
				int value = (physicsPartsDef->PartsType << 15) + physicsPartsDef->SubType;
				if (!vehicleModule->isLineMatch(m_OneLineStart, value, pos))
				{
					continue;
				}
			}
			else
			{
				bool foundBlock = false;
				if (!blockpos.isZero())
				{
					//1. pos为鼠标悬停的方块，显示
					if (pos == blockpos)
						foundBlock = true;
					else
					{
						//2. 鼠标悬停方块连接的方块，显示
						for (auto it = m_VehicleBlockLines.begin(); it != m_VehicleBlockLines.end(); it++)
						{
							// 如果是不开放编辑的连线 无论如何都不显示
							if (it->canedit != '1' && it->canedit != '3')
							{
								continue;
							}
							int frompos = it->from;
							int x = frompos >> 10;
							int y = frompos >> 5 & 0x1f;
							int z = frompos & 0x1f;
							WCoord pos1(x, y, z);
							Block srcBlock = m_pVehicleWorld->getBlock(pos1);
							if (srcBlock.isEmpty())
								continue;

							int topos = it->to;
							x = topos >> 10;
							y = topos >> 5 & 0x1f;
							z = topos & 0x1f;
							WCoord pos2(x, y, z);

							//如果起始点都不是blockpos，continue
							if ((pos1 == blockpos && pos2 == pos) || (pos1 == pos && pos2 == blockpos))
							{
								foundBlock = true;
								break;
							}
							else
								continue;
						}
					}
					if (!foundBlock) continue;
				}
			}

			WCoord worldpos = convertWcoord(pos);
			if (iter->second.groupid <= 0)
			{
				worldpos.y += 50;
			}

			int uisize = 25; // BLOCK_SIZE / 4;
			if (!m_FindBlockPosition.isZero() && pos == m_FindBlockPosition)
			{
				uisize = 40;
			}
			Rainbow::WorldPos origin = worldpos.toWorldPos();
			Rainbow::Vector3f v3look  = MINIW::Normalize(g_pPlayerCtrl->getCamera()->getLookDir());
			Rainbow::Vector3f v3right  = MINIW::Normalize(CrossProduct(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), v3look));
			Rainbow::Vector3f v3up = CrossProduct(v3look, v3right);
			v3up *= (float)uisize;

			Rainbow::Vector3f pos1 = -Rainbow::Vector3f(v3right.x * uisize, 0, v3right.z * uisize) + v3up;
			Rainbow::Vector3f pos2 = Rainbow::Vector3f(v3right.x * uisize, 0, v3right.z * uisize) + v3up;
			v3up *= 2;
			Rainbow::Vector3f pos3 = pos1 - v3up;
			Rainbow::Vector3f pos4 = pos2 - v3up;
			BlockGeomVert v1;
			v1.pos.x = short(pos1.x);
			v1.pos.y = short(pos1.y);
			v1.pos.z = short(pos1.z);
			v1.pos.w = 0x7f7f;
			v1.color = BlockColor(255, 255, 255, 255);
			//v1.light = BlockColor::white;

			BlockGeomVert v2;
			v2.pos.x = short(pos2.x);
			v2.pos.y = short(pos2.y);
			v2.pos.z = short(pos2.z);
			v2.pos.w = 0x7f7f;
			v2.color = BlockColor(255, 255, 255, 255);
			//v2.light = BlockColor::white;

			BlockGeomVert v3;
			v3.pos.x = short(pos3.x);
			v3.pos.y = short(pos3.y);
			v3.pos.z = short(pos3.z);
			v3.pos.w = 0x7f7f;
			v3.color = BlockColor(255, 255, 255, 255);
			//v3.light = BlockColor::white;

			BlockGeomVert v4;
			v4.pos.x = short(pos4.x);
			v4.pos.y = short(pos4.y);
			v4.pos.z = short(pos4.z);
			v4.pos.w = 0x7f7f;
			v4.color = BlockColor(255, 255, 255, 255);
			//v4.light = BlockColor::white;

			v1.uv.x = short(0);
			v1.uv.y = short(0);

			v2.uv.x = short(0);
			v2.uv.y = short(4096);

			v3.uv.x = short(4096);
			v3.uv.y = short(4096);

			v4.uv.x = short(4096);
			v4.uv.y = short(0);

			std::vector<BlockGeomVert>verts1;
			verts1.push_back(v1);
			verts1.push_back(v2);
			verts1.push_back(v3);
			verts1.push_back(v4);

			std::vector<unsigned short>indices;
			indices.push_back(0);
			indices.push_back(1);
			indices.push_back(2);

			indices.push_back(1);
			indices.push_back(2);
			indices.push_back(3);


			VehicleBlock* block = m_Blocks[posx][posy][posz];
			if (block)
			{
				int porttype = iter->second.porttype;
				m_pWorld->getRender()->getCurveScreenRender()->addRect(porttype, origin, WCoord(0, 0, 0), verts1, indices);
			}
		}
	}
}


void ActorVehicleAssemble::renderBlockLines(WCoord blockpos)
{
	if (g_pPlayerCtrl == NULL || m_VehicleBlockLines.size() == 0)
	{
		return;
	}
	World *pworld = g_pPlayerCtrl->getWorld();
	if (pworld == NULL)
	{
		return;
	}
	VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
	for (auto it = m_VehicleBlockLines.begin(); it != m_VehicleBlockLines.end(); it++)
	{
		// 如果是不开放编辑的连线 无论如何都不显示
		if (it->canedit != '1' && it->canedit != '3')
		{
			continue;
		}
		int frompos = it->from;
		int x = frompos >> 10;
		int y = frompos >> 5 & 0x1f;
		int z = frompos & 0x1f;
		WCoord pos1(x, y, z);
		Block srcBlock = m_pVehicleWorld->getBlock(pos1);
		if (srcBlock.isEmpty())
			continue;
		WCoord startpos = convertWcoord(pos1);
		if (m_Blocks[x][y][z]->m_GroupIdSelf <= 0)
		{
			startpos.y += 50;
		}

		int topos = it->to;
		x = topos >> 10;
		y = topos >> 5 & 0x1f;
		z = topos & 0x1f;
		WCoord pos2(x, y, z);

		//如果起始点都不是blockpos，continue
		if (!blockpos.isZero() && pos1 != blockpos && pos2 != blockpos)
			continue;

		Block endBlock = m_pVehicleWorld->getBlock(pos2);
		if (endBlock.isEmpty())
			continue;

		WCoord end_pos = convertWcoord(pos2);
		if (m_Blocks[x][y][z]->m_GroupIdSelf <= 0)
		{
			end_pos.y += 50;
		}

		// 在拉线过程中 不匹配起始node的相关 node和line都不显示
		if (m_OneLineStart >= 0 && vehicleModule)
		{
			const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(pos1));
			if (physicsPartsDef == NULL)
			{
				continue;
			}
			int value = (physicsPartsDef->PartsType << 15) + physicsPartsDef->SubType;
			if (!vehicleModule->isLineMatch(m_OneLineStart, value, pos1))
			{
				continue;
			}

			physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(pos2));
			if (physicsPartsDef == NULL)
			{
				continue;
			}
			value = (physicsPartsDef->PartsType << 15) + physicsPartsDef->SubType;
			if (!vehicleModule->isLineMatch(m_OneLineStart, value, pos2))
			{
				continue;
			}
		}

		MINIW::CatmullRomCurve cc;
		Rainbow::Vector3f p1(0.0f, 0.0f, 0.0f);
		Rainbow::Vector3f p2 = (startpos - end_pos).toVector3();
		if (p2.x == 0 && p2.y == 0 && p2.z == 0)
		{
			return;
		}

		if (g_pPlayerCtrl->getCamera())
		{
			int porttype = -1;
			if (vehicleModule)
			{
				porttype = vehicleModule->getBlockPhysicsPortType(getBlockID(pos1));
			}
			if (porttype > -1)
			{
				Rainbow::Vector3f p0, p3;
				p0 = p1*2.0f - p2;
				Rainbow::Vector3f dir0;

				Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
				p3 = p2*2.0f - p1;

				cc.setControlPoints(p0, p1, p2, p3);
				cc.setNormals(camlookdir, camlookdir);
				// 这里就是暂时区分了 节点和连线的区别
				int mtlId = porttype + 5;

				pworld->getRender()->getCurveScreenRender()->addCurve(mtlId, cc, end_pos, startpos, 6.0f, 3.0f);
			}
		}
	}
}

void ActorVehicleAssemble::renderVehicleActionerBlockNumber(WCoord blockpos)
{
	if (g_pPlayerCtrl == NULL || g_pPlayerCtrl->getCurToolID() != ITEM_VEHICLE_LINK_TOOL)
	{
		return;
	}

	if (m_pVehicleWorld == NULL || m_pVehicleWorld->getContainerMgr() == NULL)
	{
		return;
	}

	//这里是相对坐标 物理机械的世界
	WCoord startPos;

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getStartBlockNode",
		SandboxContext(nullptr));
	if (result.IsExecSuccessed())
	{
		startPos = result.GetData_UserObject<WCoord>("startPos");
	}
	WCoord actionPos;

	if (!startPos.isZero() && BLOCK_ACTIONER == m_pVehicleWorld->getBlockID(startPos))
	{
		actionPos = startPos;
	}
	else if (!m_FindBlockPosition.isZero() && BLOCK_ACTIONER == m_pVehicleWorld->getBlockID(m_FindBlockPosition))
	{
		actionPos = m_FindBlockPosition;
	}
	else
		return;

	World *pworld = g_pPlayerCtrl->getWorld();
	if (pworld == NULL)
	{
		return;
	}

	if (g_pPlayerCtrl->getCamera())
	{
		VehicleContainerActioner* container = dynamic_cast<VehicleContainerActioner*>(m_pVehicleWorld->getContainerMgr()->getContainer(actionPos));
		if (container)
		{
			int maxnumber = container->getActionerPartsCount();
			for (int i = 0; i < maxnumber; i++)
			{
				WCoord position;
				if (container->getPartPosByIndex(i, position))
				{
					int posVal = (position.x << 10) + (position.y << 5) + position.z;

					//如果不是要打印的方块，则不打印
					if (!blockpos.isZero() && position != blockpos)
						continue;

					auto iter = m_VehicleBlockNodes.find(posVal);
					if (iter != m_VehicleBlockNodes.end())
					{
						WCoord worldpos = convertWcoord(position);
						if (iter->second.groupid <= 0)
						{
							worldpos.y += 50;
						}

						int uisize = 25;

						Rainbow::WorldPos origin = worldpos.toWorldPos();
						Rainbow::Vector3f v3look  = MINIW::Normalize(g_pPlayerCtrl->getCamera()->getLookDir());
						Rainbow::Vector3f v3right  = MINIW::Normalize(CrossProduct(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), v3look));
						Rainbow::Vector3f v3up = CrossProduct(v3look, v3right);
						v3up *= (float)uisize;

						Rainbow::Vector3f pos1 = -Rainbow::Vector3f(v3right.x * uisize, 0, v3right.z * uisize) + v3up;
						Rainbow::Vector3f pos2 = Rainbow::Vector3f(v3right.x * uisize, 0, v3right.z * uisize) + v3up;
						v3up *= 2;
						Rainbow::Vector3f pos3 = pos1 - v3up;
						Rainbow::Vector3f pos4 = pos2 - v3up;
						BlockGeomVert v1;
						v1.pos.x = short(pos1.x);
						v1.pos.y = short(pos1.y);
						v1.pos.z = short(pos1.z);
						v1.pos.w = 0x7f7f;
						v1.color = BlockColor(255, 255, 255, 255);
						//v1.light = BlockColor::white;

						BlockGeomVert v2;
						v2.pos.x = short(pos2.x);
						v2.pos.y = short(pos2.y);
						v2.pos.z = short(pos2.z);
						v2.pos.w = 0x7f7f;
						v2.color = BlockColor(255, 255, 255, 255);
						//v2.light = BlockColor::white;

						BlockGeomVert v3;
						v3.pos.x = short(pos3.x);
						v3.pos.y = short(pos3.y);
						v3.pos.z = short(pos3.z);
						v3.pos.w = 0x7f7f;
						v3.color = BlockColor(255, 255, 255, 255);
						//v3.light = BlockColor::white;

						BlockGeomVert v4;
						v4.pos.x = short(pos4.x);
						v4.pos.y = short(pos4.y);
						v4.pos.z = short(pos4.z);
						v4.pos.w = 0x7f7f;
						v4.color = BlockColor(255, 255, 255, 255);
						//v4.light = BlockColor::white;


						v1.uv.x = short(0);
						v1.uv.y = short(0);

						v2.uv.x = short(4096);
						v2.uv.y = short(0);

						v3.uv.x = short(0);
						v3.uv.y = short(4096);

						v4.uv.x = short(4096);
						v4.uv.y = short(4096);

						std::vector<BlockGeomVert>verts1;
						verts1.push_back(v1);
						verts1.push_back(v2);
						verts1.push_back(v3);
						verts1.push_back(v4);

						std::vector<unsigned short>indices;
						indices.push_back(1);
						indices.push_back(2);
						indices.push_back(0);

						indices.push_back(1);
						indices.push_back(3);
						indices.push_back(2);
						// 这里的8 注意看材质的索引吧
						int number = i + 8;

						pworld->getRender()->getCurveScreenRender()->addRect(number, origin, WCoord(-1, -1, -1), verts1, indices);
					}
				}
			}
		}
	}
}

bool ActorVehicleAssemble::checkPlayerIsInRange(int range)
{
	if (!g_pPlayerCtrl || g_pPlayerCtrl->getCurToolID() != ITEM_VEHICLE_LINK_TOOL)
	{
		m_bShowGraph = false;
		return false;
	}

	const WCoord& pos = g_pPlayerCtrl->getPosition();

	const WCoord& vehiclepos = getPosition();

	if (pos.distanceTo(vehiclepos) > range)
		m_bShowGraph = false;
	else
		m_bShowGraph = true;

	return m_bShowGraph;
}

void ActorVehicleAssemble::addBlockLines(WCoord startNode, WCoord endNode)
{
	Block startblock = getBlock(startNode);
	Block endblock = getBlock(endNode);
	if (startblock.isEmpty() || endblock.isEmpty())
	{
		return;
	}
	VehicleBlockLine line;
	line.from = (startNode.x << 10) + (startNode.y << 5) + startNode.z;
	line.to = (endNode.x << 10) + (endNode.y << 5) + endNode.z;

	std::vector<VehicleBlockLine>::iterator iter = m_VehicleBlockLines.begin();
	// 找到相同记录 对应操作为删除这条连线
	// 未找到相同记录 对应操作为添加这条连线
	bool find = false;
	bool bDefaultValue = false;
	for (; iter != m_VehicleBlockLines.end(); iter++)
	{
		if (iter->from == line.from && iter->to == line.to)
		{
			if (iter->canedit == '1')
				iter->canedit = '2';
			else if (iter->canedit == '2')
			{
				iter->canedit = '1';
				bDefaultValue = true;
			}
			else
				m_VehicleBlockLines.erase(iter);
			find = true;
			break;
		}
	}
	if (!find)
	{
		// 检查最大值是否满足
		if (checkMaxValue(line.from, startblock.getResID(), line.to, endblock.getResID()))
		{
			line.canedit = '3';

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_findDefaultConnect",
				SandboxContext(nullptr)
				.SetData_Number("startblockID", startblock.getResID())
				.SetData_Number("endblock", endblock.getResID())
				.SetData_Bool("order", true));
			if (result.IsExecSuccessed())
			{
				char editval = result.GetData_String("editval")[0];
				if (editval == '8')
					line.canedit = '3';
				else
					line.canedit = editval;
			}

			m_VehicleBlockLines.push_back(line);
		}
		else
			return;
	}

	int from = -1;
	int target = -1;
	std::map<int, VehicleNodeStruct>::iterator iter1 = m_VehicleBlockNodes.find(line.from);
	if (iter1 != m_VehicleBlockNodes.end())
	{
		from = iter1->second.data;
	}

	std::map<int, VehicleNodeStruct>::iterator iter2 = m_VehicleBlockNodes.find(line.to);
	if (iter2 != m_VehicleBlockNodes.end())
	{
		target = iter2->second.data;
	}

	if (from > -1 && target > -1)
	{
		if (find && !bDefaultValue)
			m_LineGraph->SetValueUndirectedMatric(from, target, 0);
		else
			m_LineGraph->SetValueUndirectedMatric(from, target, 1);

		if (m_pVehicleWorld)
		{
			int BlockID = m_pVehicleWorld->getBlockID(startNode);
			// 动作序列器 相应动作
			if (BlockID == BLOCK_ACTIONER)
			{
				VehicleContainerActioner* container = dynamic_cast<VehicleContainerActioner*>(m_pVehicleWorld->getContainerMgr()->getContainer(startNode));
				if (container)
				{
					if (find && !bDefaultValue)
						container->DeletePart(endNode, endblock.getResID());
					else
						container->AddNewPart(endNode, endblock.getResID());
				}
			}

			// 客机先不给操作container
			if (!m_pVehicleWorld->isRemoteMode())
			{
				ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(startNode));
				if (container)
				{
					bool bDelKeyBind = false;
					if (find && !bDefaultValue)
					{
						container->deleteOnePartData(endNode);
						bDelKeyBind = true;
					}
					else
						container->addOnePartData(endNode, false);


					//添加部件对应的键位信息
					container->addKeyBindInfo(endNode, bDelKeyBind);
					markBlockForUpdate(startNode, 0);
					markBlockForUpdate(endNode, 0);
				}
			}
		}
	}
	// 添加或者删除连线之后 notify一下endblock
	BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(endblock.getResID());
	if (pmtl)
	{
		//pmtl->onNotify(m_pVehicleWorld, endNode, endblock.getResID());
		pmtl->DoOnNotify(m_pVehicleWorld, endNode, endblock.getResID());
		/*pmtl->Event().Emit("OnNotify", SandboxContext()
			.SetData_Usertype<World>("world", m_pVehicleWorld)
			.SetData_UserObject<WCoord>("pos", endNode)
			.SetData_Number("blockid", (double)endblock.getResID())
		);*/
	}

	if (m_pVehicleWorld && !m_pVehicleWorld->isRemoteMode())
	{
		blockChange(startNode);
		blockChange(endNode);
	}
}

char ActorVehicleAssemble::findBlockLines(WCoord startNode, WCoord endNode)
{
	int from = (startNode.x << 10) + (startNode.y << 5) + startNode.z;
	int to = (endNode.x << 10) + (endNode.y << 5) + endNode.z;

	std::vector<VehicleBlockLine>::iterator iter = m_VehicleBlockLines.begin();
	for (; iter != m_VehicleBlockLines.end(); iter++)
	{
		if ((iter->from == from && iter->to == to) || (iter->from == to && iter->to == from))
		{
			return iter->canedit;
		}
	}
	return '8';
}

std::string ActorVehicleAssemble::getDriverSeatKeyData(int uin)
{
	ClientPlayer* player = static_cast<ClientPlayer*>(GetWorldManagerPtr()->getPlayerByUin(uin));
	if (player == NULL)return "";
	if (!m_pVehicleWorld) return "";

	WCoord seatpos = getRiddenBindSeatPos(player);
	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
	if (!container)return "";
	container->RefreshSeatBindKeys();

	std::string jsonstr = "";
	jsonxx::Object obj;
	jsonxx::Array blockArray;

	for (auto keyiter = container->m_BindKeyData.begin(); keyiter != container->m_BindKeyData.end(); keyiter++)
	{
		for (auto partiter = keyiter->second.begin(); partiter != keyiter->second.end(); partiter++)
		{
			jsonxx::Object nodesObj;
			nodesObj << "keyid" << keyiter->first;
			nodesObj.import("x", jsonxx::Number(partiter->position.x));
			nodesObj.import("y", jsonxx::Number(partiter->position.y));
			nodesObj.import("z", jsonxx::Number(partiter->position.z));
			nodesObj.import("blockid", jsonxx::Number(getBlockID(partiter->position)));
			nodesObj.import("index", jsonxx::Number(partiter->index));
			nodesObj.import("heatpercent", jsonxx::Number(GetPartOverheatPercent(partiter->position)));
			nodesObj.import("isthreshold", jsonxx::Boolean(!checkifOverheatPartCanWork(partiter->position)));
			nodesObj.import("powerpercent", jsonxx::Number(GetPartSThrusterPowerPercent(partiter->position)));

			blockArray << nodesObj;
		}
	}

	obj << "nodes" << blockArray;
	jsonstr = obj.json();
	return jsonstr;
}

std::string ActorVehicleAssemble::getActionerLineNode(WCoord& pos)
{
	std::string jsonstr = "";
	jsonxx::Object obj;
	jsonxx::Array blockArray;
	int startpos = (pos.x << 10) + (pos.y << 5) + pos.z;
	for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
	{
		if (startpos == iter->from)
		{
			WCoord targetPos = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);
			jsonxx::Object nodesObj;
			nodesObj << "x" << (iter->to >> 10);
			nodesObj << "y" << ((iter->to >> 5) & 0x1f);
			nodesObj << "z" << (iter->to & 0x1f);
			nodesObj << "blockid" << getBlockID(targetPos);
			blockArray << nodesObj;
		}
	}
	obj << "nodes" << blockArray;
	jsonstr = obj.json();
	return jsonstr;
}

void ActorVehicleAssemble::uiInteractBlock(int blockid, WCoord pos, int type, bool isclicked, int keyid, int remoteuin/*=0*/)
{
	// 客机传给主机进行操作
	if (m_pWorld && m_pWorld->isRemoteMode())
	{
		PB_VehicleAssembleLineOperateCH lineOpereateCH;
		lineOpereateCH.set_objid(getObjId());
		lineOpereateCH.set_blockid(blockid);
		PB_Vector3* blockpos = lineOpereateCH.mutable_blockpos();
		blockpos->set_x(pos.x);
		blockpos->set_y(pos.y);
		blockpos->set_z(pos.z);
		lineOpereateCH.set_isclicked(isclicked);
		lineOpereateCH.set_type(type);
		lineOpereateCH.set_keyid(keyid);

		GetGameNetManagerPtr()->sendToHost(PB_VEHICLE_ASSEMBLE_LINE_OPERATE_CH, lineOpereateCH);
		return;
	}
	blockid = m_pVehicleWorld->getBlockID(pos);
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
	if (physicsPartsDef)
	{
		WCoord seatpos(0, 0, 0);
		if (remoteuin > 0)
		{
			ClientActor *player = static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(remoteuin);
			if (player)
				seatpos = getRiddenBindSeatPos(player);
		}
		else
			seatpos = getRiddenBindSeatPos(g_pPlayerCtrl);

		if (seatpos.isZero())
		{
			return;
		}

		ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
		if (container)
		{
			if (type == 0)	//长按
			{
				container->addOnePartData(pos, isclicked);
			}
			else if (type == 1)	//点击
			{
				bool oldvalue = container->getPartData(pos);
				container->addOnePartData(pos, !oldvalue);
			}
		}

		BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
		if (physicsPartsDef->PortTriggerType == 0)
		{
			if (pmtl)
			{
				int withKeyId = blockid;
				if (BLOCK_STHRUSTER == blockid)
				{
					if (isclicked)
					{
						withKeyId += 1 << 12;
					}
					withKeyId += keyid << 16;

					if (hasFuel())
					{
						pmtl->DoOnNotify(m_pVehicleWorld, pos, withKeyId);
						/*DoOnNotify
						pmtl->Event().Emit("OnNotify", SandboxContext()
							.SetData_Usertype<World>("world", m_pVehicleWorld)
							.SetData_UserObject<WCoord>("pos", pos)
							.SetData_Number("blockid", (double)withKeyId)
						);*/
					}
					else
					{
						//缺少燃料，给相应的提示
						PlayerControl *playercontrol = dynamic_cast<PlayerControl *>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(remoteuin));
						if (playercontrol)
						{
							playercontrol->postInfoTips(12332);
						}
					}
				}
				else
				{
					pmtl->DoOnNotify(m_pVehicleWorld, pos, withKeyId);
					/*pmtl->Event().Emit("OnNotify", SandboxContext()
						.SetData_Usertype<World>("world", m_pVehicleWorld)
						.SetData_UserObject<WCoord>("pos", pos)
						.SetData_Number("blockid", (double)withKeyId)
					);*/
				}
			}
		}
		else if (physicsPartsDef->PortTriggerType == 1)
		{
			if (pmtl && pmtl->getBlockSettingAttState(ENABLE_BEOPERATED) > 0)
			{
				// 触发开始 先重置所有的连线block遍历状态
				m_LineGraph->ResetNode();
				// 这里isvisited字段设置为true 驾驶座的
				int data = (seatpos.x << 10) + (seatpos.y << 5) + seatpos.z;
				int index = m_LineGraph->GetNodeIndexByData(data);
				if (index >= 0)
				{
					m_LineGraph->m_NodeArray[index].isvisited = true;
				}

				//如果是释放按钮，则不再触发
				if (isclicked)
					pmtl->DoOnTrigger(m_pVehicleWorld, pos, DIR_NOT_INIT, g_pPlayerCtrl);
			}
		}
		blockChange(seatpos);
		// 主机执行完毕，通知传过来的客机进行ui状态更新
		/*if (remoteuin > 0)
		{
		bool state = GetKeyBindPartState(pos);
		PB_VehicleAssembleLineOperateHC vehicleAssembleLineOperateHC;
		vehicleAssembleLineOperateHC.set_objid(getObjId());
		vehicleAssembleLineOperateHC.set_state(state);
		PB_Vector3* blockpos = vehicleAssembleLineOperateHC.mutable_blockpos();
		blockpos->set_x(pos.x);
		blockpos->set_y(pos.y);
		blockpos->set_z(pos.z);
		GetGameNetManagerPtr()->sendToClient(remoteuin,PB_VEHICLE_ASSEMBLE_LINE_OPERATE_HC,vehicleAssembleLineOperateHC);
		}*/
	}
}

bool ActorVehicleAssemble::checkMaxValue(int from, int fromblockid, int to, int toblockid)
{
	int maxout = 0;
	int maxin = 0;
	std::vector<VehicleBlockLine>::iterator iter = m_VehicleBlockLines.begin();
	for (; iter != m_VehicleBlockLines.end(); iter++)
	{
		if (iter->from == from)
			maxout += 1;
		if (iter->to == to)
			maxin += 1;
	}

	const PhysicsPartsDef* partsDef = GetDefManagerProxy()->getPhysicsPartsDef(fromblockid);
	if (partsDef == NULL)
		return false;
	const PhysicsPartsTypeInfoDef* partTypeInfoDef = GetDefManagerProxy()->getPhysicsPartsTypeDef(partsDef->PartsType, partsDef->SubType);
	if (partTypeInfoDef == NULL)
		return false;
	if (partTypeInfoDef->iMaxOutNum <= maxout)
	{
		MINIW::ScriptVM::game()->callFunction("VehicleGameTips", "ii", 12310, partTypeInfoDef->iMaxOutNum);
		return false;
	}

	partsDef = GetDefManagerProxy()->getPhysicsPartsDef(toblockid);
	if (partsDef == NULL)
		return false;
	partTypeInfoDef = GetDefManagerProxy()->getPhysicsPartsTypeDef(partsDef->PartsType, partsDef->SubType);
	if (partTypeInfoDef == NULL)
		return false;
	if (partTypeInfoDef->iMaxInNum <= maxin)
	{
		MINIW::ScriptVM::game()->callFunction("VehicleGameTips", "ii", 12311, partTypeInfoDef->iMaxInNum);
		return false;
	}
	return true;
}

void ActorVehicleAssemble::resetVehicleLine()
{
	auto iter = m_VehicleBlockLines.begin();

	std::map<int, std::vector<SeatPartData>> partDatas;
	for (; iter != m_VehicleBlockLines.end(); )
	{
		auto iter1 = m_VehicleBlockNodes.find(iter->from);
		auto iter2 = m_VehicleBlockNodes.find(iter->to);
		if (iter1 == m_VehicleBlockNodes.end() || iter2 == m_VehicleBlockNodes.end())
		{
			iter = m_VehicleBlockLines.erase(iter);
			//		LOG_INFO("m_VehicleBlockLines.erase from %d to %d",iter->from, iter->to);
			continue;
		}

		// 主机进行container更新
		if (iter != m_VehicleBlockLines.end() && m_pVehicleWorld && !m_pVehicleWorld->isRemoteMode())
		{
			int fromvalue = iter->from;
			WCoord position(fromvalue >> 10, (fromvalue >> 5) & 0x1f, (fromvalue & 0x1f));
			if (isSeatPosition(position) > 0)
			{
				SeatPartData partdata;
				partdata.position = WCoord((iter->to&(0x1f << 10)) >> 10, (iter->to&(0x1f << 5)) >> 5, iter->to & 0x1f);
				ContainerDriverSeat* seatcontainer = dynamic_cast<ContainerDriverSeat*>(getVehicleWorld()->getContainerMgr()->getContainer(position));
				if (seatcontainer)
					partdata.ischarge = seatcontainer->getPartData(partdata.position);
				else
					partdata.ischarge = false;
				auto iter3 = partDatas.find(iter->from);
				if (iter3 == partDatas.end())
					partDatas[iter->from].push_back(partdata);
				else
					iter3->second.push_back(partdata);
			}
		}

		iter++;
	}

	std::map<int, std::vector<SeatPartData>>::iterator iter4 = partDatas.begin();
	for (; iter4 != partDatas.end(); iter4++)
	{
		int firstvalue = iter4->first;
		WCoord position(firstvalue >> 10, (firstvalue >> 5) & 0x1f, (firstvalue & 0x1f));
		ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(position));
		if (container)
		{
			container->resetPartData(iter4->second);
		}
	}
}

bool ActorVehicleAssemble::TriggerControlKeys(int uin, int keyid, int keytype, bool active)
{
	PlayerControl *playercontrol = dynamic_cast<PlayerControl *>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(uin));
	if (playercontrol == NULL)
		return false;
	WCoord seatpos = getRiddenBindSeatPos(playercontrol);
	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
	if (!container || container->m_BindKeyData.find(keyid) == container->m_BindKeyData.end() || container->m_BindKeyData[keyid].size() <= 0)
		return false;

	//玩家使用WASD键来操控载具时，检测载具是否包含引擎和燃料， 只有在有车轮或者船的螺旋推进器情况下才会显示
	if (keyid >= 1 && keyid <= 4 && keytype == 1 && (getWheelBlockNum() > 0 || m_nDynamicBoatThrusterCount > 0 ))
	{
		//缺少引擎
		if (m_Engines.size() == 0)
		{
			playercontrol->postInfoTips(12333);
		}
		else if (m_Engines[0].blockid == BLOCK_VEHICLEENGINE)
		{
			int totalFuel = 0;
			for (int i = 0; i < (int)m_CostParts.size(); i++)
			{
				DynamicCost* dynamicCost = dynamic_cast<DynamicCost *>(m_CostParts[i]);
				if (dynamicCost)
					totalFuel += dynamicCost->getCurValue();
			}

			//缺少燃料
			if (totalFuel <= 0)
			{
				playercontrol->postInfoTips(12332);
			}
		}
	}

	for (auto iter = container->m_BindKeyData[keyid].begin(); iter != container->m_BindKeyData[keyid].end(); iter++)
	{
		int PartID = getBlockID(iter->position);
		const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(PartID);
		if (physicsPartsDef == NULL)
			continue;
		const PhysicsPartKeyDef* partKeyDef = GetDefManagerProxy()->getPhysicsPartKeyDefByIndex(PartID, iter->index - 1);
		if (partKeyDef == NULL || partKeyDef->KeyType != keytype)
			continue;

		int TriggerType = physicsPartsDef->PortTriggerType;

		if (TriggerType == 0 || TriggerType == 1)
		{
			uiInteractBlock(PartID, iter->position, keytype, active, keyid);

			if (keytype == 0 && active)
				m_CurOperateKeys.push_back(keyid);
			else
			{
				auto iter_temp = find(m_CurOperateKeys.begin(), m_CurOperateKeys.end(), keyid);
				if (iter_temp != m_CurOperateKeys.end())
					m_CurOperateKeys.erase(iter_temp);
			}
			/*if (m_pWorld && m_pWorld->isRemoteMode())
			{
			return false;
			}*/

		}
		else
		{
			switch (PartID)
			{
			case BLOCK_DRIVERS_SEAT:
				//驾驶座
				//key1: getout;		key2: reset
				if (iter->index == 1)
				{
					playercontrol->dismountActor();
				}
				else if (iter->index == 2)
				{
					reset();
				}
				break;
			case BLOCK_BOAT_RUDDER:
				//船舵
				if (iter->index == 1)
				{
					playercontrol->dismountActor();
				}
				else if (iter->index == 2)
				{
					reset();
				}
				break;
			case BLOCK_PASSENGER_SEAT:
				//乘客座
				//key1： getout
				if (iter->index == 1)
				{
					playercontrol->dismountActor();
				}
				break;
			case BLOCK_VEHICLEENGINE:
				//有限引擎
				//key1: speedup;	key2: speeddown
				active = hasFuel() ? active : false;
				if (iter->index == 1)
				{
					playercontrol->m_VehicleControlInputs->setAccelKeyPressed(active);
				}
				else if (iter->index == 2)
				{
					playercontrol->m_VehicleControlInputs->setBrakeKeyPressed(active);
				}
				break;
			case BLOCK_INFINITEENGINE:
				//无限引擎
				//key1: speedup;	key2: speeddown
				// 				active = hasFuel() ? active : false;
				if (iter->index == 1)
				{
					playercontrol->m_VehicleControlInputs->setAccelKeyPressed(active);
				}
				else if (iter->index == 2)
				{
					playercontrol->m_VehicleControlInputs->setBrakeKeyPressed(active);
				}
				break;
			case BLOCK_FRONT_LEFT_WHEEL:
				//转向车轮
				//key1: turnleft;	key2: turnright
				if (iter->index == 1)
				{
					playercontrol->m_VehicleControlInputs->setSteerLeftKeyPressed(active);
				}
				else if (iter->index == 2)
				{
					playercontrol->m_VehicleControlInputs->setSteerRightKeyPressed(active);
				}
				break;
			case BLOCK_BOAT_THRUSTER:
				//螺旋推进器 
				//key1: turnleft;	key2: turnright
				if (iter->index == 1)
				{
					playercontrol->m_VehicleControlInputs->setSteerLeftKeyPressed(active);
				}
				else if (iter->index == 2)
				{
					playercontrol->m_VehicleControlInputs->setSteerRightKeyPressed(active);
				}
				break;
			default:
				//获取配表信息
				PhysicsPartsDef::EffectFunctionsDef* seatFun = NULL;
				for (int i = 0; i<(int)physicsPartsDef->EffectFunctions.size(); i++)
				{
					PhysicsPartsDef::EffectFunctionsDef* functiondef = (PhysicsPartsDef::EffectFunctionsDef*)&physicsPartsDef->EffectFunctions[i];
					if (functiondef->func_id == 3) //座位
					{
						seatFun = functiondef;
					}
				}
				if (seatFun)
				{
					if (iter->index == 1)
					{
						playercontrol->dismountActor();
					}
				}
				break;
			}
		}
	}
	//取键位控制的第一个部件的状态用来判断UI显示
	/*WCoord pos = container->m_BindKeyData[keyid][0].position;
	int index = container->m_BindKeyData[keyid][0].index;
	const PhysicsPartKeyDef* partKeyDef = GetDefManagerProxy()->getPhysicsPartKeyDefByIndex(getBlockID(pos), index - 1);
	if (partKeyDef&&partKeyDef->KeyType == 1)
	UpdateKeyBindUIState(pos, GetKeyBindPartState(pos));*/
	return true;
}

float ActorVehicleAssemble::GetPartOverheatPercent(WCoord pos)
{
	for (int i = 0; i < (int)m_Overheats.size(); i++)
	{
		DynamicOverheat* dynamicOverheat = dynamic_cast<DynamicOverheat*>(m_Overheats[i]);
		if (dynamicOverheat)
		{
			//OverheatPart part = m_Overheats[i];
			if (m_Overheats[i]->getPos() != pos)
				continue;

			float maxHeat = (float)dynamicOverheat->getFuncDef()->func.overheatfun.MaxHeat;
			float curHeat = (float)dynamicOverheat->getCurHeat();
			//LOG_INFO("CURHEAT,%f MAXHEAT:%f", curHeat, maxHeat);
			return curHeat / maxHeat;
		}
	}
	return 0;
}

float ActorVehicleAssemble::GetPartSThrusterPowerPercent(WCoord pos)
{
	for (int i = 0; i < (int)m_DynamicCreateParams.size(); i++)
	{
		if (m_DynamicCreateParams[i].mSThrusterParams.size())
		{
			for (int j = 0; j < (int)m_DynamicCreateParams[i].mSThrusterParams.size(); j++)
			{
				if (pos == m_DynamicCreateParams[i].mSThrusterParams[j].mBlockPos && m_DynamicCreateParams[i].mSThrusterParams[j].m_fPowerMax > 0)
				{
					return m_DynamicCreateParams[i].mSThrusterParams[j].m_fCurPower / m_DynamicCreateParams[i].mSThrusterParams[j].m_fPowerMax;
				}
			}
		}
	}
	return 0;
}

bool ActorVehicleAssemble::GetKeyBindPartState(WCoord pos)
{
	//if (!m_pWorld || m_pWorld->isRemoteMode())
	//return false;
	bool state = false;
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(pos));
	if (physicsPartsDef)
	{
		if (physicsPartsDef->PortTriggerType == 0)
		{
			state = m_pVehicleWorld->isBlockIndirectlyGettingPowered(pos);
		}
		else if (physicsPartsDef->PortTriggerType == 1)
		{
			state = (getBlockData(pos) & 8) > 0;
		}
	}
	return state;
}

void ActorVehicleAssemble::UpdateKeyBindUIState(WCoord pos, bool state)
{
	if (!m_pVehicleWorld)return;
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(getBlockID(pos));
	WCoord seatpos = getRiddenBindSeatPos(g_pPlayerCtrl);
	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
	if (!container || !physicsPartsDef) return;

	if (physicsPartsDef->PortTriggerType == 0 || physicsPartsDef->PortTriggerType == 1)
	{
		auto keyiter = container->m_BindKeyData.begin();
		bool isExist = false;
		/*while (keyiter != container->m_BindKeyData.end())
		{
		for (int i = 0; i < keyiter->second.size(); i++)
		{
		const PhysicsPartKeyDef* keyDef = GetDefManagerProxy()->getPhysicsPartKeyDefByIndex(getBlockID(pos), keyiter->second[i].index - 1);
		if (keyiter->second[i].position == pos && keyDef->KeyType == 1)
		{
		MINIW::ScriptVM::game()->callFunction("UpdateVehicleKeyUI", "ib", keyiter->first, state);
		isExist = true;
		break;
		}
		}

		if (isExist)
		break;
		keyiter++;
		}*/

	}

}

bool ActorVehicleAssemble::GetKeyBindPartState(int x, int y, int z)
{
	return GetKeyBindPartState(WCoord(x, y, z));
}

void ActorVehicleAssemble::GetFromPartBindLineInfo(WCoord frompos, std::vector<WCoord>&pos)
{
	pos.clear();
	for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
	{
		WCoord position = WCoord(iter->from >> 10, (iter->from >> 5) & 0x1f, iter->from & 0x1f);
		if (getBlockID(position) > 0 && frompos == position)
		{
			WCoord topos = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);
			if (getBlockID(topos) > 0)
				pos.push_back(topos);
		}
	}

}

void ActorVehicleAssemble::GetToPartBindLineInfo(WCoord topos, std::vector<WCoord>&pos)
{
	pos.clear();
	for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
	{
		WCoord position = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);
		if (getBlockID(position) > 0 && topos == position)
		{
			WCoord frompos = WCoord(iter->from >> 10, (iter->from >> 5) & 0x1f, iter->from & 0x1f);
			if (getBlockID(frompos) > 0)
				pos.push_back(frompos);
		}
	}
}

int ActorVehicleAssemble::getEngineNum()
{
	return m_EngineNum;
}

bool ActorVehicleAssemble::getSteeringSwitch()
{
	return m_hasSteeringSwitch;
}

int ActorVehicleAssemble::isSeatPosition(WCoord position)
{
	if (position == m_SeatRelativePos)
	{
		return 1;
	}
	std::vector<WCoord>::iterator it = m_OtherSeatPos.begin();
	for (; it != m_OtherSeatPos.end(); it++)
	{
		WCoord seatpos = *it / BLOCK_SIZE;
		if (seatpos == position)
		{
			return 2;
		}
	}
	return 0;
}

void ActorVehicleAssemble::resetFindBlock(WCoord position)
{
	m_FindBlockPosition = position;
}

void ActorVehicleAssemble::refreshAllSeatBindKeys()
{
	ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(m_SeatRelativePos));
	if (container)
	{
		//if (m_pContainerWorkshop || (m_bMakeByItemUse&&container->m_BindKeyData.size() <= 0))
		if (m_pContainerWorkshop || (container->m_BindKeyData.size() <= 0))
		{
			container->addKeyBindInfo(m_SeatRelativePos);
			for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
			{
				WCoord position = WCoord(iter->from >> 10, (iter->from >> 5) & 0x1f, iter->from & 0x1f);
				if (position == m_SeatRelativePos && iter->canedit != '2')
				{
					WCoord topos = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);
					int BlockID = getBlockID(topos);
					if (BlockID > 0)
						container->addKeyBindInfo(topos);
					if (BlockID == BLOCK_VEHICLEENGINE || BlockID == BLOCK_INFINITEENGINE)
						m_EngineNum++;
					else if (!m_hasSteeringSwitch&&(BlockID == BLOCK_FRONT_LEFT_WHEEL  ||  BlockID == BLOCK_BOAT_THRUSTER ))
						m_hasSteeringSwitch = true;
				}
			}
		}
		container->RefreshSeatBindKeys();
	}

	for (int i = 0; i < (int)m_OtherSeatPos.size(); i++)
	{
		WCoord centerPos = WCoord(MAX_DIM_X / 2, 0, MAX_DIM_Z / 2);
		WCoord seatpos = m_OtherSeatPos[i] / BLOCK_SIZE + centerPos;
		container = dynamic_cast<ContainerDriverSeat*>(m_pVehicleWorld->getContainerMgr()->getContainer(seatpos));
		if (container)
		{
			if (m_pContainerWorkshop || (m_bMakeByItemUse&&container->m_BindKeyData.size() <= 0))
			{
				container->m_BindKeyData.clear();
				container->addKeyBindInfo(seatpos);
				for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
				{
					WCoord position = WCoord(iter->from >> 10, (iter->from >> 5) & 0x1f, iter->from & 0x1f);
					if (position == seatpos)
					{
						WCoord topos = WCoord(iter->to >> 10, (iter->to >> 5) & 0x1f, iter->to & 0x1f);
						if (getBlockID(topos) > 0)
							container->addKeyBindInfo(topos);
					}
				}
			}
			container->RefreshSeatBindKeys();
		}
	}
}

void ActorVehicleAssemble::autoMakeLines(std::vector<WCoord> lineNodes)
{
	// 开始校验 自动连接的线
	VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
	if (m_pVehicleWorld == NULL || vehicleModule == NULL || m_VehicleBlockLines.size() > 0)
		return;

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_getPhysicsDefaultConnect",
		SandboxContext(nullptr));
	if (result.IsExecSuccessed())
	{
		if (result.GetData_Bool("b"))
		{
			return;
		}
	}


	// 1、先清理连线节点不存在的 连线
	// 2、校验一下 默认连接的连线 是否还符合默认连接的条件（因为节点可能被换成了别的了） canedit属性修正一下
	// 3、添加没有的默认连线
	auto iter3 = m_VehicleBlockLines.begin();
	for (; iter3 != m_VehicleBlockLines.end(); )
	{
		// 校验默认连线
		WCoord startpos(((iter3->from) >> 10), (((iter3->from) >> 5) & 0x1f), ((iter3->from) & 0x1f));
		WCoord endpos(((iter3->to) >> 10), (((iter3->to) >> 5) & 0x1f), ((iter3->to) & 0x1f));

		int fromblockid = m_pVehicleWorld->getBlockID(startpos);
		int toblockid = m_pVehicleWorld->getBlockID(endpos);
		if (fromblockid == 0 || toblockid == 0)
		{
			iter3 = m_VehicleBlockLines.erase(iter3);
			continue;
		}

		// 校验默认连线
		bool order = true;
		char editval = '8';
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_findDefaultConnect",
			SandboxContext(nullptr)
			.SetData_Number("startblockID", fromblockid)
			.SetData_Number("endblock", toblockid)
			.SetData_Bool("order", order));
		if (result.IsExecSuccessed())
		{
			order = result.GetData_Bool("order");
			editval = result.GetData_String("editval")[0];
		}

		if (editval == '8')
		{
			iter3 = m_VehicleBlockLines.erase(iter3);
			continue;
		}
		if (order == false)
		{
			int temppos = iter3->from;
			iter3->from = iter3->to;
			iter3->to = temppos;
		}
		if (editval == '3' || editval == '0' || (editval == '1' && iter3->canedit != '2'))
		{
			iter3->canedit = editval;
		}
		iter3++;
	}

	// 找出额外的 默认连接

	for (int i = 0; i < (int)lineNodes.size(); i++)
	{
		for (int j = i + 1; j < (int)lineNodes.size(); j++)
		{
			int startblockid = m_pVehicleWorld->getBlockID(lineNodes[i]);
			int endblockid = m_pVehicleWorld->getBlockID(lineNodes[j]);
			if (startblockid == 0 || endblockid == 0)
				continue;


			int data1 = (lineNodes[i].x << 10) + (lineNodes[i].y << 5) + lineNodes[i].z;

			int data2 = (lineNodes[j].x << 10) + (lineNodes[j].y << 5) + lineNodes[j].z;

			bool defaultOrder = true;
			char editval = vehicleModule->findDefaultConnect(startblockid, endblockid, defaultOrder);
			if (editval == '8')
			{
				continue;
			}
			else if (editval == '0' || editval == '1')
			{
				// 检查是否这条线已经存在
				char canedit;
				if (defaultOrder)
					canedit = findBlockLines(lineNodes[i], lineNodes[j]);
				else
					canedit = findBlockLines(lineNodes[j], lineNodes[i]);

				if (canedit == '8')
				{
					VehicleBlockLine line;
					line.from = defaultOrder ? data1 : data2;
					line.to = defaultOrder ? data2 : data1;
					line.canedit = editval;
					m_VehicleBlockLines.push_back(line);
					//	LOG_INFO("ActorVehicleAssemble::autoMakeLines lines id is %d and %d",startblockid, endblockid);
				}
			}
		}
	}
	//	LOG_INFO("ActorVehicleAssemble::autoMakeLines lines number is %d",m_VehicleBlockLines.size());
}

bool ActorVehicleAssemble::isBoxIntersect(const CollideAABB &box)
{
	OPTICK_EVENT();
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc && loc->m_ChassisUpdatePos.size())
	{
		vector<WCoord> checkPosList;
		int colX = (box.dim.x - 1) / BLOCK_SIZE + 1;
		int colY = (box.dim.y - 1) / BLOCK_SIZE + 1;
		int colZ = (box.dim.z - 1) / BLOCK_SIZE + 1;
		float insideOffsetX = (float)box.dim.x / colX;
		float insideOffsetY = (float)box.dim.y / colY;
		float insideOffsetZ = (float)box.dim.z / colZ;

		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ() + box.dim.z));

		for (int i = 1; i < colX; i++)
		{
			for (int j = 1; j < colY; j++)
			{
				for (int k = 1; k < colZ; k++)
				{
					checkPosList.push_back(WCoord(box.minX() + i*insideOffsetX, box.minY() + j*insideOffsetY, box.minZ() + k*insideOffsetZ));
				}
			}
		}

		for (int m = 0; m < (int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1, loc->m_ChassisUpdatePos[m].m_UpdateRot,
			// 				Rainbow::Vector3f(WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.x),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.y),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.z)));
			tm.Invert_Full();
			for (auto iter = checkPosList.begin(); iter != checkPosList.end(); iter++)
			{
				Rainbow::Vector3f loaclPos;
				tm.MultiplyPoint3(iter->toVector3(), loaclPos);
				loaclPos.x += (MAX_DIM_X / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.z += (MAX_DIM_Z / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.y += BLOCK_HALFSIZE;
				int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
				if (m > 0 && m_JointCreateParams.size() > 0)
				{
					Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
					loaclPos += offset;
				}
				loaclPos.x /= 100.0f;
				loaclPos.y /= 100.0f;
				loaclPos.z /= 100.0f;
				//cube containing origin point
				int x = (int)floor(loaclPos.x);
				int y = (int)floor(loaclPos.y);
				int z = (int)floor(loaclPos.z);
				if (x >= 0 && y >= 0 && z >= 0 && x < MAX_DIM_X && y < MAX_DIM_Y && z < MAX_DIM_Z)
				{
					VehicleBlock *srcBlock = m_Blocks[x][y][z];
// 					int blockID = srcBlock->m_Block.getResID();
					if (srcBlock->m_GroupIdSelf != groupIdPhy)
					{
						continue;
					}
					if (srcBlock->m_Block.getResID() != 0 && srcBlock->m_iCurLife > 0)
					{
						return true;
					}
				}
			}
		}

		for (int i = 0; i < (int)m_vWheelBlocksPos.size(); i++)
		{
			WCoord wpos = convertWcoord(m_vWheelBlocksPos[i]);
			WCoord minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y - BLOCK_SIZE, wpos.z - BLOCK_SIZE / 2);
			WCoord maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y, wpos.z + BLOCK_SIZE / 2);
			if (!(minpos.x >= box.maxX() || minpos.y >= box.maxY() || minpos.z >= box.maxZ() || maxpos.x <= box.minX() || maxpos.y <= box.minY() || maxpos.z <= box.minZ()))
			{
				return true;
			}
		}
	}

	return false;
}

void ActorVehicleAssemble::getIntersectPosList(const CollideAABB &box, vector<WCoord> &list)
{
	OPTICK_EVENT();
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc && loc->m_ChassisUpdatePos.size())
	{
		vector<WCoord> checkPosList;
		int colX = (box.dim.x - 1) / BLOCK_SIZE + 1;
		int colY = (box.dim.y - 1) / BLOCK_SIZE + 1;
		int colZ = (box.dim.z - 1) / BLOCK_SIZE + 1;
		float insideOffsetX = (float)box.dim.x / colX;
		float insideOffsetY = (float)box.dim.y / colY;
		float insideOffsetZ = (float)box.dim.z / colZ;

		//checkPosList.push_back(WCoord(box.centerX(), box.centerY(), box.centerZ()));

		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ() + box.dim.z));

		for (int i = 0; i <= colX; i++)
		{
			for (int j = 0; j <= colY; j++)
			{
				for (int k = 0; k <= colZ; k++)
				{
					checkPosList.push_back(WCoord(box.minX() + i*insideOffsetX, box.minY() + j*insideOffsetY, box.minZ() + k*insideOffsetZ));
				}
			}
		}

		map<WCoord, int> retList;

		for (int m = 0; m < (int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1, loc->m_ChassisUpdatePos[m].m_UpdateRot,
			// 				Rainbow::Vector3f(WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.x),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.y),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.z)));
			tm.Invert_Full();
			int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
			for (auto iter = checkPosList.begin(); iter != checkPosList.end(); )
			{
				Rainbow::Vector3f loaclPos;
				tm.MultiplyPoint3(iter->toVector3(), loaclPos);
				loaclPos.x += (MAX_DIM_X / 2)*BLOCK_FSIZE;
				loaclPos.z += (MAX_DIM_Z / 2)*BLOCK_FSIZE;
				loaclPos.y += BLOCK_HALFSIZE;
				if (m > 0 && m_JointCreateParams.size() > 0)
				{
					Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
					loaclPos += offset;
				}
				loaclPos.x /= 100.0f;
				loaclPos.y /= 100.0f;
				loaclPos.z /= 100.0f;
				//cube containing origin point
				int x = (int)floor(loaclPos.x);
				int y = (int)floor(loaclPos.y); // 去掉 +0.5   发现人下半身触碰到方块，检测不到
				int z = (int)floor(loaclPos.z + 0.5);  //这里的 +0.5是否要去掉，待定

				if (x >= 0 && y >= 0 && z >= 0 && x < MAX_DIM_X && y < MAX_DIM_Y && z < MAX_DIM_Z)
				{
					//LOG_INFO("~!~!~! get x = %d, y = %d, z = %d", x, y, z);
					//LOG_INFO("~!~!~! get loaclPosx = %f, loaclPosy = %f, loaclPosz = %f", loaclPos.x, loaclPos.y, loaclPos.z);

					VehicleBlock *srcBlock = m_Blocks[x][y][z];
// 					int blockID = srcBlock->m_Block.getResID();
					if (srcBlock->m_GroupIdSelf != groupIdPhy)
					{
						iter++;
						continue;
					}

					if (srcBlock->m_Block.getResID() != 0 && srcBlock->m_iCurLife > 0)
					{
						WCoord okPos = WCoord(x, y, z);
						if (retList.find(okPos) == retList.end())
						{
							retList[okPos] = 1;
							list.push_back(okPos);
						}
					}
					iter = checkPosList.erase(iter);
				}
				else
				{
					iter++;
				}
			}
		}


		for (int i = 0; i < (int)m_vWheelBlocksPos.size(); i++)
		{
			WCoord wpos = convertWcoord(m_vWheelBlocksPos[i]);
			WCoord minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y - BLOCK_SIZE, wpos.z - BLOCK_SIZE / 2);
			WCoord maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y, wpos.z + BLOCK_SIZE / 2);
			if (!(minpos.x >= box.maxX() || minpos.y >= box.maxY() || minpos.z >= box.maxZ() || maxpos.x <= box.minX() || maxpos.y <= box.minY() || maxpos.z <= box.minZ()))
			{
				list.push_back(m_vWheelBlocksPos[i]);
			}
		}
	}
}

bool ActorVehicleAssemble::checkIntersectBox(const CollideAABB &box)
{
	OPTICK_EVENT();
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc && loc->m_ChassisUpdatePos.size())
	{
		vector<WCoord> checkPosList;
		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.maxX(), box.maxY(), box.maxZ()));

		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ()));
		checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ() + box.dim.z));
		checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ()));

		for (int m = 0; m < (int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			// 			tm.makeSRTMatrix(1, loc->m_ChassisUpdatePos[m].m_UpdateRot,
			// 				Rainbow::Vector3f(WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.x),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.y),
			// 					WorldPrecisePos::Fix2Flt(loc->m_ChassisUpdatePos[m].m_PUpdatePos.z)));
			tm.Invert_Full();
			for (auto iter = checkPosList.begin(); iter != checkPosList.end(); iter++)
			{
				Rainbow::Vector3f loaclPos;
				tm.MultiplyPoint3(iter->toVector3(), loaclPos);
				loaclPos.x += (MAX_DIM_X / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.z += (MAX_DIM_Z / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.y += BLOCK_HALFSIZE;
				int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
				if (m > 0 && m_JointCreateParams.size() > 0)
				{
					Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
					loaclPos += offset;
				}
				loaclPos.x /= 100.0f;
				loaclPos.y /= 100.0f;
				loaclPos.z /= 100.0f;
				//cube containing origin point
				int x = (int)floor(loaclPos.x);
				int y = (int)floor(loaclPos.y);
				int z = (int)floor(loaclPos.z);
				if (x >= 0 && y >= 0 && z >= 0 && x < MAX_DIM_X && y < MAX_DIM_Y && z < MAX_DIM_Z)
				{
					VehicleBlock *srcBlock = m_Blocks[x][y][z];
// 					int blockid = srcBlock->m_Block.getResID();
					if (srcBlock->m_GroupIdSelf != groupIdPhy)
					{
						continue;
					}
					if (srcBlock->m_Block.getResID() != 0 && srcBlock->m_iCurLife > 0)
					{
						return true;
					}
				}
			}
		}

		for (int i = 0; i < (int)m_vWheelBlocksPos.size(); i++)
		{
			WCoord wpos = convertWcoord(m_vWheelBlocksPos[i]);
			WCoord minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y - BLOCK_SIZE, wpos.z - BLOCK_SIZE / 2);
			WCoord maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y, wpos.z + BLOCK_SIZE / 2);
			if (!(minpos.x >= box.maxX() || minpos.y >= box.maxY() || minpos.z >= box.maxZ() || maxpos.x <= box.minX() || maxpos.y <= box.minY() || maxpos.z <= box.minZ()))
			{
				return true;
			}
		}
	}
	return false;
}

bool ActorVehicleAssemble::intersectBox(const CollideAABB &box, int &_blockID, int &_x, int &_y, int &_z, bool isPrecise)
{
	OPTICK_EVENT();
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	if (loc && loc->m_ChassisUpdatePos.size())
	{
		vector<WCoord> checkPosList;

		checkPosList.push_back(WCoord(box.centerX(), box.centerY(), box.centerZ()));
		if (isPrecise)
		{
			checkPosList.push_back(WCoord(box.minX(), box.centerY(), box.centerZ()));
			checkPosList.push_back(WCoord(box.maxX(), box.centerY(), box.centerZ()));
			checkPosList.push_back(WCoord(box.centerX(), box.minY(), box.centerZ()));
			checkPosList.push_back(WCoord(box.centerX(), box.maxY(), box.centerZ()));
			checkPosList.push_back(WCoord(box.centerX(), box.centerY(), box.minZ()));
			checkPosList.push_back(WCoord(box.centerX(), box.centerY(), box.maxZ()));
			// 			checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ()));
			// 			checkPosList.push_back(WCoord(box.maxX(), box.maxY(), box.maxZ()));
			// 
			// 			checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ()));
			// 			checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY() + box.dim.y, box.minZ()));
			// 			checkPosList.push_back(WCoord(box.minX() + box.dim.x, box.minY(), box.minZ() + box.dim.z));
			// 			checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ() + box.dim.z));
			// 			checkPosList.push_back(WCoord(box.minX(), box.minY(), box.minZ() + box.dim.z));
			// 			checkPosList.push_back(WCoord(box.minX(), box.minY() + box.dim.y, box.minZ()));
		}

		for (int m = 0; m < (int)loc->m_ChassisUpdatePos.size(); m++)
		{
			Matrix4x4f tm = loc->m_ChassisUpdatePosTM[m];
			tm.Invert_Full();
			for (auto iter = checkPosList.begin(); iter != checkPosList.end(); iter++)
			{
				Rainbow::Vector3f loaclPos;
				tm.MultiplyPoint3(iter->toVector3(), loaclPos);
				loaclPos.x += (MAX_DIM_X / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.z += (MAX_DIM_Z / 2 + 0.5f)*BLOCK_FSIZE;
				loaclPos.y += BLOCK_HALFSIZE;
				int groupIdPhy = loc->getGroupIdWithPhysIndex(m);
				if (m > 0 && m_JointCreateParams.size() > 0)
				{
					Rainbow::Vector3f offset = m_JointCreateParams[groupIdPhy].mPos0;
					loaclPos += offset;
				}
				loaclPos.x /= 100.0f;
				loaclPos.y /= 100.0f;
				loaclPos.z /= 100.0f;
				//cube containing origin point
				int x = (int)floor(loaclPos.x);
				int y = (int)floor(loaclPos.y);
				int z = (int)floor(loaclPos.z);
				if (x >= 0 && y >= 0 && z >= 0 && x < MAX_DIM_X && y < MAX_DIM_Y && z < MAX_DIM_Z)
				{
					VehicleBlock *srcBlock = m_Blocks[x][y][z];
					int blockid = srcBlock->m_Block.getResID();
					if (srcBlock->m_GroupIdSelf != groupIdPhy)
					{
						continue;
					}
					if (blockid != 0 && srcBlock->m_iCurLife > 0)
					{
						bool isButton = false;
						bool isPressure = false;
						if (isPressureBlock(blockid))
							isPressure = true;
						else if (isButtonBlock(blockid))
							isButton = true;

						if (isPressure || isButton)
						{
							WCoord minpos;
							WCoord maxpos;
							Block *srcBlock = &m_Blocks[x][y][z]->m_Block;
							int blockdata = srcBlock->getData();
							WCoord wpos = convertWcoord(WCoord(x, y, z));

							if (isPressure)
							{
								//感压板，只取下面的1/4部分作为碰撞盒;   感压板的方向只有2个： 0和2
								int dir = blockdata & 7;
								WCoord directionCoord;
								if (dir == 0 || dir == 1)   //向上
								{
									/*minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y, wpos.z - BLOCK_SIZE / 2) + WCoord(BLOCK_SIZE / 8, 0, BLOCK_SIZE / 8);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE / 4, wpos.z + BLOCK_SIZE / 2) - WCoord(BLOCK_SIZE / 8, 0, BLOCK_SIZE / 8);*/

									minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y, wpos.z - BLOCK_SIZE / 2);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE / 4, wpos.z + BLOCK_SIZE / 2);
								}
								else if (dir == 2 || dir == 3) //向下
								{
									minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE * 3 / 4, wpos.z - BLOCK_SIZE / 2) + WCoord(BLOCK_SIZE / 8, 0, BLOCK_SIZE / 8);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE, wpos.z + BLOCK_SIZE / 2) - WCoord(BLOCK_SIZE / 8, 0, BLOCK_SIZE / 8);
								}
							}
							else if (isButton)
							{
								int dir = blockdata & 3;
								if (dir == 0)
								{
									minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y, wpos.z - BLOCK_SIZE / 2);
									maxpos = WCoord(wpos.x - BLOCK_SIZE / 4, wpos.y + BLOCK_SIZE / 2, wpos.z + BLOCK_SIZE / 2);
								}
								else if (dir == 1)
								{
									minpos = WCoord(wpos.x + BLOCK_SIZE / 4, wpos.y, wpos.z - BLOCK_SIZE / 2);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE / 2, wpos.z + BLOCK_SIZE / 2);
								}
								else if (dir == 2)
								{
									minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y, wpos.z - BLOCK_SIZE / 2);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE / 2, wpos.z - BLOCK_SIZE / 4);
								}
								else if (dir == 3)
								{
									minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y, wpos.z + BLOCK_SIZE / 4);
									maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y + BLOCK_SIZE / 2, wpos.z + BLOCK_SIZE / 2);
								}
							}

							if (minpos.x >= box.maxX() || minpos.y >= box.maxY() || minpos.z >= box.maxZ() || maxpos.x <= box.minX() || maxpos.y <= box.minY() || maxpos.z <= box.minZ())
								continue;
						}
						_blockID = blockid;
						_x = x;
						_y = y;
						_z = z;
						return true;
					}
				}
			}
		}

		for (int i = 0; i < (int)m_vWheelBlocksPos.size(); i++)
		{
			WCoord wpos = convertWcoord(m_vWheelBlocksPos[i]);
			WCoord minpos = WCoord(wpos.x - BLOCK_SIZE / 2, wpos.y - BLOCK_SIZE, wpos.z - BLOCK_SIZE / 2);
			WCoord maxpos = WCoord(wpos.x + BLOCK_SIZE / 2, wpos.y, wpos.z + BLOCK_SIZE / 2);
			if (!(minpos.x >= box.maxX() || minpos.y >= box.maxY() || minpos.z >= box.maxZ() || maxpos.x <= box.minX() || maxpos.y <= box.minY() || maxpos.z <= box.minZ()))
			{
				_blockID = getBlockID(m_vWheelBlocksPos[i]);
				_x = m_vWheelBlocksPos[i].x;
				_y = m_vWheelBlocksPos[i].y;
				_z = m_vWheelBlocksPos[i].z;
				return true;
			}
		}
	}

	return false;
}

RigidDynamicActor* ActorVehicleAssemble::getFatherVehicleDynamicActor()
{
	if (m_FatherVehilcID)
	{
		assert(m_FatherVehilcID != getObjId());
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_FatherVehilcID));
		if (vehicle)
		{
			//Rainbow::RigidDynamicActor* dynamic_actor = vehicle->getLocoMotion()->
			VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
			if (locoVehicle && locoVehicle->m_PhysActorVec.size())
			{
				int group = vehicle->getPartGroupIdWithPos(m_FatherVehilcPos);
				int index = locoVehicle->getPhysIndexWithGroupId(group);
				if (index && index < (int)locoVehicle->m_PhysActorVec.size())
				{
					return  locoVehicle->m_PhysActorVec[index];
				}
				else
				{
					return  locoVehicle->m_PhysActorVec[0];
				}
			}
		}
	}
	return NULL;
}

MINIW::VehicleArticulation* ActorVehicleAssemble::getFatherVehicleArticulation()
{
	if (m_FatherVehilcID)
	{
		assert(m_FatherVehilcID != getObjId());
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(static_cast<ClientActorMgr*>(m_pWorld->getActorMgr())->findActorByWID(m_FatherVehilcID));
		if (vehicle)
		{
			//Rainbow::RigidDynamicActor* dynamic_actor = vehicle->getLocoMotion()->
			VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(vehicle->getLocoMotion());
			if (locoVehicle)
			{
				return locoVehicle->m_VehicleArticulation;
			}
		}
	}
	return NULL;
}

// 生成载具时，排除不需要生成的方块，现在不需要生成方块：
// 1. 投射装置上的方块
void ActorVehicleAssemble::excludeBlocks()
{
	for (int y = 0; y < MAX_DIM_Y; y++)
	{
		for (int z = 0; z < MAX_DIM_Z; z++)
		{
			for (int x = 0; x < MAX_DIM_X; x++)
			{
				Block srcBlock = m_Blocks[x][y][z]->m_Block;
				if (m_Blocks[x][y][z]->m_iCurLife == 0 || srcBlock.isEmpty())
					continue;

				int blockid = srcBlock.getResID();
				int blockdata = srcBlock.getData();

				//生成载具时，投掷装置上面的方块不生成，  方块的位置在投掷装置的尾部
				if ((BLOCK_CATAPULT == blockid) && ((blockdata & 4) != 0))
				{
					if (y + 1 < MAX_DIM_Y)
					{
						Block *delBlock = &m_Blocks[x][y + 1][z]->m_Block;
						if (!delBlock->isEmpty() || m_Blocks[x][y + 1][z]->m_iCurLife != 0)
							delBlock->setAllData(0);
					}
				}
				if ((BLOCK_SPRING_GOLD == blockid || BLOCK_SPRING_IRON == blockid))
				{
					WCoord nextpos = g_DirectionCoord[blockdata & 7];
					if (((x + nextpos.x) < MAX_DIM_X) && ((y + nextpos.y) < MAX_DIM_Y) && ((z + nextpos.z) < MAX_DIM_Z))
					{
						Block *delBlock = &m_Blocks[x + nextpos.x][y + nextpos.y][z + nextpos.z]->m_Block;
						if (y + 1 < MAX_DIM_Y)
							if (!delBlock->isEmpty() || m_Blocks[x][y + 1][z]->m_iCurLife != 0)
								delBlock->setAllData(0);
					}
				}
			}
		}
	}
}

//更新载具的执行方块
void ActorVehicleAssemble::updateVehicleBlock(const WCoord& blockpos, int blockid, int blockdata, int groupid)
{
	if (blockid != 0)
	{
		const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockid);
		if (!physicsPartsDef)
			return;

		setBlockAll(blockpos, blockid, blockdata);
		m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_iCurLife = physicsPartsDef->Life;
		m_Blocks[blockpos.x][blockpos.y][blockpos.z]->m_GroupIdSelf = groupid;
	}
	else
	{
		setBlockAll(blockpos, blockid, 0);
	}
}

void ActorVehicleAssemble::resetBlockRealPos()
{
	VehicleAssembleLocoMotion* locoVehicle = static_cast<VehicleAssembleLocoMotion*>(getLocoMotion());
	if (locoVehicle)
	{
		locoVehicle->m_UpdateCountResetPos = locoVehicle->m_UpdateCount;
	}
}

void ActorVehicleAssemble::refreshVehicleExtentForInit()
{
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TransferMgr_setTransferVehicle",
		SandboxContext(nullptr)
		.SetData_Number("curMapID", m_pWorld->getCurMapID())
		.SetData_UserObject("vehicleAssemble", this)
		.SetData_UserObject("m_vTransferPos", m_vTransferPos)
		.SetData_UserObject("m_pVehicleWorld", m_pVehicleWorld));

}

void ActorVehicleAssemble::changeStraightFlexibleShape(const WCoord & blockpos, int dir, float stretchingSize)
{
	//判断方块是否存在
	bool changeFlag = false;
	float halfBlockSize = BLOCK_FSIZE / 2;
	float quarterBlockSize = BLOCK_FSIZE / 4;
	float newSize = stretchingSize + 1;
	VehicleBlock* vehicleBlock = getVehicleBlock(blockpos);
	if (vehicleBlock &&  vehicleBlock->m_Block.getResID() > 0)
	{
		int groupid = vehicleBlock->m_GroupIdSelf;
		VehicleAssembleLocoMotion *locoVehicle = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());

		auto itera = locoVehicle->m_PhysGroupExData.find(groupid);
		if (itera == locoVehicle->m_PhysGroupExData.end() || (locoVehicle->m_PhysActorVec.size() > itera->second && locoVehicle->m_PhysActorVec[itera->second] == NULL))
		{
			return;
		}



		std::map<WCoord, std::vector<Collider*>>::iterator iter = locoVehicle->m_mapFlexibleShapes.find(blockpos);
		if (iter != locoVehicle->m_mapFlexibleShapes.end() && iter->second.size())
		{
			if (DIR_NOT_INIT == dir)
			{
				dir = vehicleBlock->m_Block.getData() & 7;
			}
			BoxCollider* shape = dynamic_cast<BoxCollider*>(iter->second[0]);
			if (shape == NULL) return;
			Vector3f trans = shape->GetCenter();
			Vector3f oldHalfExtent = shape->GetSize() * 0.5f;
			Vector3f halfExtent;
			Vector3f oldSize = oldHalfExtent / halfBlockSize;

			if (DIR_POS_X == dir)
			{
				if (oldSize.x != newSize)
				{
					changeFlag = true;
					trans.x += halfBlockSize * (newSize - oldSize.x);
					halfExtent = Vector3f(halfBlockSize * newSize, oldHalfExtent.y, oldHalfExtent.z);
				}
			}
			else if (DIR_NEG_X == dir)
			{
				if (oldSize.x != newSize)
				{
					changeFlag = true;
					trans.x -= halfBlockSize * (newSize - oldSize.x);
					halfExtent = Vector3f(halfBlockSize * newSize, oldHalfExtent.y, oldHalfExtent.z);
				}
			}
			else if (DIR_POS_Z == dir)
			{
				if (oldSize.z != newSize)
				{
					changeFlag = true;
					trans.z += halfBlockSize * (newSize - oldSize.z);
					halfExtent = Vector3f(oldHalfExtent.x, oldHalfExtent.y, halfBlockSize * newSize);
				}
			}
			else if (DIR_NEG_Z == dir)
			{
				if (oldSize.z != newSize)
				{
					changeFlag = true;
					trans.z -= halfBlockSize * (newSize - oldSize.z);
					halfExtent = Vector3f(oldHalfExtent.x, oldHalfExtent.y, halfBlockSize * newSize);
				}
			}
			else if (DIR_POS_Y == dir)
			{
				if (oldSize.y != newSize)
				{
					changeFlag = true;
					trans.y += halfBlockSize * (newSize - oldSize.y);
					halfExtent = Vector3f(oldHalfExtent.x, halfBlockSize * newSize, oldHalfExtent.z);
				}
			}
			else if (DIR_NEG_Y == dir)
			{
				if (oldSize.y != newSize)
				{
					changeFlag = true;
					trans.y -= halfBlockSize * (newSize - oldSize.y);
					halfExtent = Vector3f(oldHalfExtent.x, halfBlockSize * newSize, oldHalfExtent.z);
				}
			}

			if (changeFlag)
			{
				shape->SetCenter(trans);
				shape->SetSize(halfExtent * 2.0f);
			}
		}
	}
}

void ActorVehicleAssemble::updateSuspensionEntity(std::vector<WCoord> blockPosVec)
{
	updateChassisEntity(blockPosVec);
}

int ActorVehicleAssemble::getConnectGroupid(int groupid)
{
	if (groupid >= m_BlockGroupNum)
		return 0;

	return m_JointCreateParams[groupid].mGroupId0;
}

void ActorVehicleAssemble::TransferActionerDataToVehicle(World *pworld, ContainerWorkshop* pContainerWorkshop)
{
	if (NULL == pworld || NULL == pContainerWorkshop)
	{
		return;
	}
	WCoord startPos = pContainerWorkshop->getStartPos();
	WCoord dim = pContainerWorkshop->getDim();
	// 修正两个WCoord数据
	if (startPos.x > startPos.x + dim.x)
		startPos.x = startPos.x + dim.x;

	if (startPos.y > startPos.y + dim.y)
		startPos.y = startPos.y + dim.y;

	if (startPos.z > startPos.z + dim.z)
		startPos.z = startPos.z + dim.z;

	dim.x = Rainbow::Abs(dim.x);
	dim.y = Rainbow::Abs(dim.y);
	dim.z = Rainbow::Abs(dim.z);

	// 遍历工作台所有的m_actioners
	for (auto iter = pContainerWorkshop->m_actioners.begin(); iter != pContainerWorkshop->m_actioners.end(); iter++)
	{
		WCoord postion(iter->x, iter->y, iter->z);
		VehicleContainerActioner* worldContainer = dynamic_cast<VehicleContainerActioner*>(pworld->getContainerMgr()->getContainer(postion));
		if (NULL == worldContainer)
		{
			continue;
		}
		std::string actionerData = worldContainer->getActionerDataStr(true, startPos);

		VehicleContainerActioner* vehicleContainer = dynamic_cast<VehicleContainerActioner*>(m_pVehicleWorld->getContainerMgr()->getContainer(postion - startPos));
		if (NULL == vehicleContainer)
		{
			continue;
		}

		vehicleContainer->updateActionerData(actionerData.c_str());
	}
}

void ActorVehicleAssemble::getAllActiveSTrusters(vector<MINIW::SThrusterParam *> & list)
{
	for (int i = 0; i < (int)m_DynamicCreateParams.size(); i++)
	{
		if (m_DynamicCreateParams[i].mSThrusterParams.size())
		{
			for (int j = 0; j < (int)m_DynamicCreateParams[i].mSThrusterParams.size(); j++)
			{
				MINIW::SThrusterParam * pSTruster = &m_DynamicCreateParams[i].mSThrusterParams[j];
				int blockData = getVehicleWorld()->getBlockData(pSTruster->mBlockPos);
				bool active = blockData >= 8;
				if (active || pSTruster->m_bIsControl)
				{
					list.push_back(pSTruster);
				}
			}
		}
	}
}

MINIW::JointCreateParam & ActorVehicleAssemble::getJointCreateParam(int groupid)
{
	return m_JointCreateParams[groupid];
}

bool ActorVehicleAssemble::getEngineType()
{
	for (int i = 0; i < (int)m_Engines.size(); i++)
	{
		if (m_Engines[i].blockid)
		{
			if (m_Engines[i].blockid == BLOCK_INFINITEENGINE)
				return true;
		}
	}
	return false;
}

void ActorVehicleAssemble::GetAllLinedBlockPos(WCoord position, std::vector<WCoord>& blocks)
{
	for (auto iter = m_VehicleBlockLines.begin(); iter != m_VehicleBlockLines.end(); iter++)
	{
		// 校验默认连线
		WCoord startpos(((iter->from) >> 10), (((iter->from) >> 5) & 0x1f), ((iter->from) & 0x1f));
		WCoord endpos(((iter->to) >> 10), (((iter->to) >> 5) & 0x1f), ((iter->to) & 0x1f));

		if (position == startpos)
		{
			blocks.push_back(endpos);
		}
		else if (position == endpos)
		{
			blocks.push_back(startpos);
		}
	}
}

void ActorVehicleAssemble::UpdateActionerInfo(std::vector<WCoord>& vehicleactioners, World *pworld)
{
	//更新每个序列器数据
	for (auto iter = vehicleactioners.begin(); iter < vehicleactioners.end(); iter++)
	{
		vector<WCoord>topos;
		VehicleContainerActioner* container = dynamic_cast<VehicleContainerActioner*>(m_pVehicleWorld->getContainerMgr()->getContainer(*iter));
		if (container)
		{
			GetFromPartBindLineInfo(*iter, topos);
			container->UpdatePartNotExist();
			//此处需要将老的数据刷新一下
			for (auto positer = topos.begin(); positer < topos.end(); positer++)
			{
				container->AddNewPart(*positer, getBlockID(*positer));
				blockChange(*positer);
			}

			if (m_pContainerWorkshop)
			{
				WCoord start = m_pContainerWorkshop->GetAbsoluteStartPos();
				WCoord world_pos = (*iter) + start;
				VehicleContainerActioner* blockactioner = dynamic_cast<VehicleContainerActioner*>(pworld->getContainerMgr()->getContainer(world_pos));
				if (blockactioner)
				{
					std::string data = blockactioner->getActionerDataStr(true, start);
					container->updateActionerData(data.c_str());
				}
			}
		}
	}
}

//转轴方块，滑动方块，开关，动作序列器，推进器，发射装置，红外感应方块、感应器、液压臂,机翼、尾翼、航空推进器、铰链方块、爪子
void ActorVehicleAssemble::UpdateStaticDate(int blockid)
{
	if (blockid == BLOCK_REVOLUTE_JOINT && ((m_StatisticData & 2) == 0))
		m_StatisticData += 2;
	else if (blockid == BLOCK_PRISMATIC_JOINT)
		m_StatisticData |= 4;
	else if (blockid == BLOCK_JOINT_SWITCH)
		m_StatisticData |= 8;
	else if (blockid == BLOCK_ACTIONER)
		m_StatisticData |= 16;
	else if (blockid == BLOCK_THRUSTER)
		m_StatisticData |= 32;
	else if (blockid == BLOCK_EMITTER || blockid == BLOCK_THROWER)
		m_StatisticData |= 64;
	else if (blockid == BLOCK_SENSOR_DISTANCE)
		m_StatisticData |= 128;
	else if (blockid == BLOCK_SENSOR_VALUE_LIGHT || blockid == BLOCK_SENSOR_VALUE_HEIGHT || blockid == BLOCK_SENSOR_VALUE_VECLOCITY)
		m_StatisticData |= 256;
	else if (blockid == BLOCK_JOINT_ARM_PRISMATIC)
		m_StatisticData |= 512;
	else if (BLOCK_WING == blockid)
		m_StatisticData |= 1024;
	else if (BLOCK_EMPENNAGE == blockid)
		m_StatisticData |= 2048;
	else if (BLOCK_STHRUSTER == blockid)
		m_StatisticData |= 4096;
	else if (1053 == blockid)	//信号接收器
		m_StatisticData |= 8192;
	else if (BLOCK_SPRING_IRON == blockid)		//铁弹簧
		m_StatisticData |= 16384;
	else if (BLOCK_CATAPULT == blockid)			//投掷装置
		m_StatisticData |= 32768;
	else if (BLOCK_LUKER == blockid)			//地刺陷阱
		m_StatisticData |= 65536;
	else if (BLOCK_PRESSURE_WOOD == blockid)	//木质感压板
		m_StatisticData |= 131072;
	else if (BLOCK_FUNNEL == blockid)			//漏斗
		m_StatisticData |= 262144;
	else if (BLOCK_WOODDOOR == blockid)			//木门
		m_StatisticData |= 524288;
	else if (BLOCK_GOAL == blockid)				//得分方块
		m_StatisticData |= 1048576;
	else if (BLOCK_TRANSFER == blockid)			//传送点方块
		m_StatisticData |= 2097152;
	else if (BLOCK_TRANSFER_CORE == blockid)	//传送点核心方块
		m_StatisticData |= 4194304;
	else if (BLOCK_SUSPENSION_JOINT == blockid)  //悬挂
		m_StatisticData |= 8388608;
	else if (BLOCK_WOOD_BUTTON == blockid)  //木质按钮
		m_StatisticData |= 16777216;
	else if (BLOCK_JOINT_T_REVOLUTE == blockid) //铰链方块
		m_StatisticData |= 33554432;
	else if (BLOCK_CLAW == blockid)				//爪子
		m_StatisticData |= 67108864;
}


void ActorVehicleAssemble::updateEnergyCost()
{
	if (m_Engines.size())
	{
		for (int i = 0; i < (int)m_Engines.size(); i++)
		{
			if (BLOCK_INFINITEENGINE == m_Engines[i].blockid)
			{
				m_bIsUnlimitEnergy = true;
				m_hasfuel = true;
				break;
			}
		}
	}

	if (!m_bIsUnlimitEnergy)
	{
		m_hasfuel = false;
		if (m_CostParts.size() > 0)
		{
			for (int i = 0; i < (int)m_CostParts.size(); i++)
			{
				DynamicCost* dynamicCost = dynamic_cast<DynamicCost *>(m_CostParts[i]);
				if (dynamicCost)
				{
					WCoord blockpos = dynamicCost->getPos();
					int x = blockpos.x;
					int y = blockpos.y;
					int z = blockpos.z;
					Block srcBlock = m_Blocks[x][y][z]->m_Block;
					if (srcBlock.getResID() > 0 &&
						dynamicCost->getCurValue() > 0)
					{
						if (m_Blocks[x][y][z]->m_iCurLife <= 0)
						{
							m_Blocks[x][y][z]->m_iCurFuel = 0;
						}
						else
						{
							m_hasfuel = true;
							break;
						}
					}
				}
			}
		}

		if (m_hasfuel && m_Engines.size())
		{
			int useMachineIndex = 0;
			int totalCost = 0;
			for (int i = 0; i < (int)m_Engines.size(); i++)
			{
				if (BLOCK_VEHICLEENGINE == m_Engines[i].blockid)
				{
					useMachineIndex = i;
					totalCost += m_Engines[i].costValue * m_Engines[i].num;
				}
			}

			if (totalCost)
			{
				if (m_Engines[useMachineIndex].costintervalCounter < m_Engines[useMachineIndex].costInterval)
				{
					if (g_pPlayerCtrl)
					{
					
						auto RidComp = g_pPlayerCtrl->getRiddenComponent();
						if (RidComp && RidComp->isVehicleDriver())
						{
							if (GetClientInfoProxy()->isPC())
							{
								if (g_pPlayerCtrl->m_VehicleControlInputs->getAccelKeyPressed() || g_pPlayerCtrl->m_VehicleControlInputs->getBrakeKeyPressed())
									++m_Engines[useMachineIndex].costintervalCounter;
							}
							else if (GetClientInfoProxy()->isMobile())
							{
								if (g_pPlayerCtrl->m_VehicleControlInputs->getAccel() || g_pPlayerCtrl->m_VehicleControlInputs->getBrake())
									++m_Engines[useMachineIndex].costintervalCounter;
							}
						}
					}
				}

				//达到部件消耗间隔，执行消耗统计
				if (m_Engines[useMachineIndex].costintervalCounter >= m_Engines[useMachineIndex].costInterval)
				{
					m_Engines[useMachineIndex].costintervalCounter = 0;
					//达到消耗更新间隔，执行消耗操作
					if (m_AttribUpdateCount % AttribUpdateInterval == 0)
					{
						energyCost(totalCost, 1);
					}
				}
			}
		}
	}
}

void ActorVehicleAssemble::vehicleDecoratorTick()
{
	for (int i = 0; i < (int)m_Overheats.size(); i++)
	{
		m_Overheats[i]->tick();
	}

	for (int i = 0; i < (int)m_CostParts.size(); i++)
	{
		m_CostParts[i]->tick();
	}
}

void ActorVehicleAssemble::getsyncData(jsonxx::Object &data)
{
	ClientActor::getsyncData(data);
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	for (int i=0; i<(int)loc->m_ChassisUpdatePos.size(); i++)
	{
		char buff[32];
		sprintf(buff, "cPos_%d", i);
		data<<buff<<jsonxx::Object();
		jsonxx::Object &obj_ = (jsonxx::Object &)data.get<jsonxx::Object>(buff);
		WCoord chassisPos = WCoord(loc->m_ChassisUpdatePos[i].m_PPosition.x, loc->m_ChassisUpdatePos[i].m_PPosition.y, loc->m_ChassisUpdatePos[i].m_PPosition.z);
			
		obj_<<"x"<<chassisPos.x;
		obj_<<"y"<<chassisPos.y;
		obj_<<"z"<<chassisPos.z;
		Rainbow::Quaternionf &chassisRot = loc->m_ChassisUpdatePos[i].m_RotateQuat;
		obj_<<"rx"<<chassisRot.x;
		obj_<<"ry"<<chassisRot.y;
		obj_<<"rz"<<chassisRot.z;
		obj_<<"rw"<<chassisRot.w;
	}
	for (int i=0; i<(int)loc->m_WheelUpdatePos.size(); i++)
	{
		char buff[32];
		sprintf(buff, "wPos_%d", i);
		data<<buff<<jsonxx::Object();
		jsonxx::Object &obj_ = (jsonxx::Object &)data.get<jsonxx::Object>(buff);
		WCoord wheelPos = WCoord(loc->m_WheelUpdatePos[i].m_PPosition.x, loc->m_WheelUpdatePos[i].m_PPosition.y, loc->m_WheelUpdatePos[i].m_PPosition.z);
		obj_<<"x"<<wheelPos.x;
		obj_<<"y"<<wheelPos.y;
		obj_<<"z"<<wheelPos.z;
		Rainbow::Quaternionf &wheelRot = loc->m_WheelUpdatePos[i].m_RotateQuat;
		obj_<<"rx"<<wheelRot.x;
		obj_<<"ry"<<wheelRot.y;
		obj_<<"rz"<<wheelRot.z;
		obj_<<"rw"<<wheelRot.w;
	}
	if (getSTrustersCount())
	{
		std::vector<MINIW::SThrusterParam *> list;
		getAllActiveSTrusters(list);
		for (int i=0; i<(int)list.size(); i++)
		{
			char buff[32];
			sprintf(buff, "tPos_%d", i);
			data<<buff<<jsonxx::Array();
			jsonxx::Array &obj_ = (jsonxx::Array &)data.get<jsonxx::Array>(buff);
			obj_<<list[i]->mBlockPos.x<<(int)list[i]->mBlockPos.y<<(int)list[i]->mBlockPos.z<<(int)list[i]->m_nChangeState<<list[i]->m_fCurPower;
		}
	}
}

void ActorVehicleAssemble::setsyncData(jsonxx::Object &data)
{
	ClientActor::setsyncData(data);
	VehicleAssembleLocoMotion *loc = static_cast<VehicleAssembleLocoMotion *>(getLocoMotion());
	int num = 0;
	auto iter = data.kv_map().begin();
	while (iter != data.kv_map().end())
	{
		const std::string &name = iter->first;
		if (name[0] == 'c')
		{
			if (sscanf(name.c_str(),"cPos_%d",&num) != 1 && num < loc->m_ChassisUpdatePos.size())
			{
				WCoord chassisPos(
					loc->m_ChassisUpdatePos[num].m_PPosition.x, 
					loc->m_ChassisUpdatePos[num].m_PPosition.y, 
					loc->m_ChassisUpdatePos[num].m_PPosition.z
				);
				Rainbow::Quaternionf &chassisRot = loc->m_ChassisUpdatePos[num].m_RotateQuat;
				jsonxx::Value &obj_ = *iter->second;
				if (obj_.type_ == jsonxx::Value::OBJECT_)
				{
					jsonxx::Object &obj = *obj_.object_value_; 
					if (obj.has<jsonxx::Number>("x"))
					{
						chassisPos.x = obj.get<jsonxx::Number>("x");
					}
					if (obj.has<jsonxx::Number>("y"))
					{
						chassisPos.y = obj.get<jsonxx::Number>("y");
					}
					if (obj.has<jsonxx::Number>("z"))
					{
						chassisPos.z = obj.get<jsonxx::Number>("z");
					}
					if (obj.has<jsonxx::Number>("rx"))
					{
						chassisRot.x = obj.get<jsonxx::Number>("rx");
					}
					if (obj.has<jsonxx::Number>("ry"))
					{
						chassisRot.y = obj.get<jsonxx::Number>("ry");
					}
					if (obj.has<jsonxx::Number>("rz"))
					{
						chassisRot.z = obj.get<jsonxx::Number>("rz");
					}
					if (obj.has<jsonxx::Number>("rw"))
					{
						chassisRot.w = obj.get<jsonxx::Number>("rw");
					}
				}
			}
		}
		else if (name[0] == 'w')
		{
			if (sscanf(name.c_str(),"wPos_%d",&num) != 1)
			{
				WCoord chassisPos(
					loc->m_WheelUpdatePos[num].m_PPosition.x, 
					loc->m_WheelUpdatePos[num].m_PPosition.y, 
					loc->m_WheelUpdatePos[num].m_PPosition.z
				);
				Rainbow::Quaternionf &chassisRot = loc->m_WheelUpdatePos[num].m_RotateQuat;
				jsonxx::Value &obj_ = *iter->second;
				if (obj_.type_ == jsonxx::Value::OBJECT_)
				{
					jsonxx::Object &obj = *obj_.object_value_; 
					if (obj.has<jsonxx::Number>("x"))
					{
						chassisPos.x = obj.get<jsonxx::Number>("x");
					}
					if (obj.has<jsonxx::Number>("y"))
					{
						chassisPos.y = obj.get<jsonxx::Number>("y");
					}
					if (obj.has<jsonxx::Number>("z"))
					{
						chassisPos.z = obj.get<jsonxx::Number>("z");
					}
					if (obj.has<jsonxx::Number>("rx"))
					{
						chassisRot.x = obj.get<jsonxx::Number>("rx");
					}
					if (obj.has<jsonxx::Number>("ry"))
					{
						chassisRot.y = obj.get<jsonxx::Number>("ry");
					}
					if (obj.has<jsonxx::Number>("rz"))
					{
						chassisRot.z = obj.get<jsonxx::Number>("rz");
					}
					if (obj.has<jsonxx::Number>("rw"))
					{
						chassisRot.w = obj.get<jsonxx::Number>("rw");
					}
				}
			}
		}
		else if (name[0] == 't')
		{
			if (sscanf(name.c_str(),"tPos_%d",&num) != 1)
			{
				jsonxx::Value &obj_ = *iter->second;
				if (obj_.type_ == jsonxx::Value::ARRAY_)
				{
					jsonxx::Array &obj = *obj_.array_value_; 
					WCoord pos;
					pos.x = obj.get<jsonxx::Number>(0);
					pos.y = obj.get<jsonxx::Number>(1);
					pos.z = obj.get<jsonxx::Number>(2);
					int level = obj.get<jsonxx::Number>(3);
					int power = obj.get<jsonxx::Number>(4);
					MINIW::SThrusterParam * pSTruster = getSThrusterParamWithPos(pos);
					if (pSTruster)
					{
						pSTruster->m_nChangeState = level;
						pSTruster->m_fCurPower = (float)power;
					}
				}
			}
		}
	}
}


void ActorVehicleAssemble::RenderCurve()
{
	if (m_bShowGraph && GET_SUB_SYSTEM(VehicleMgr) &&
		(GET_SUB_SYSTEM(VehicleMgr)->getVehicleAssembleWID() == 0 || (GET_SUB_SYSTEM(VehicleMgr)->getVehicleAssembleWID() > 0 && GET_SUB_SYSTEM(VehicleMgr)->getVehicleAssembleWID() == getObjId())))
	{
		//如果有指定的节点，则只显示包含指定节点的连接线
		renderBlockNodes(m_FindBlockPosition);
		renderBlockLines(m_FindBlockPosition);
		renderVehicleActionerBlockNumber(m_FindBlockPosition);
	}
}


void ActorVehicleAssemble::fillWaterMaskBlock(VehicleWaterMaskData& mask_data, const WCoord& centerPos)
{
	const WCoord& rangeMin = mask_data.rangeMin;
	const WCoord& rangeMax = mask_data.rangeMax;

	if (rangeMin == rangeMax) return;

	Block maskBlock = Block(100);

	int maxy = rangeMax.y;
	//if (rangeMax.y - rangeMin.y > 2)
	//{
	//	maxy = rangeMax.y - 1;
	//}

	int xDist = rangeMax.x - rangeMin.x;
	int zDist = rangeMax.z - rangeMin.z;
	if (zDist >= xDist)
	{
		for (int y = rangeMin.y; y <= maxy; y++)
		{
			for (int z = rangeMin.z; z <= rangeMax.z; z++)
			{
				WCoord startPoint(0, 0, 0);
				WCoord endPoint(0, 0, 0);

				bool findStartPoint = false;
				bool findEndPoint = false;
				for (int x = rangeMin.x; x <= rangeMax.x; x++)
				{
					Block* block = &m_Blocks[x][y][z]->m_Block;

					if (block != nullptr && !block->isEmpty())
					{
						int next = x + 1;
						if (next >= rangeMax.x) break;
						Block* nextBlock = &m_Blocks[next][y][z]->m_Block;
						if (nextBlock == nullptr || nextBlock->isEmpty())
						{
							if (checkBlockCanFill(next, y, z, rangeMin, rangeMax, DirectionType::DIR_POS_Z))
							{
								startPoint.x = next;
								startPoint.y = y;
								startPoint.z = z;
								findStartPoint = true;
							}
							break;
						}
					}
				}
				if (findStartPoint)
				{
					for (int x = rangeMax.x; x >= rangeMin.x; x--)
					{
						Block* block = &m_Blocks[x][y][z]->m_Block;
						if (block != nullptr && !block->isEmpty())
						{
							int next = x - 1;
							if (next <= rangeMin.x) break;
							Block* nextBlock = &m_Blocks[next][y][z]->m_Block;
							if (nextBlock == nullptr || nextBlock->isEmpty())
							{
								if (checkBlockCanFill(next, y, z, rangeMin, rangeMax, DirectionType::DIR_POS_Z))
								{
									endPoint.x = next;
									endPoint.y = y;
									endPoint.z = z;
									findEndPoint = true;
									break;
								}
							}
						}
					}
				}
				if (findStartPoint && findEndPoint)
				{
					for (int x = startPoint.x; x <= endPoint.x; x++)
					{
						WCoord pos(x, y, z);
						WCoord blockpos = pos - centerPos;
						mask_data.mechaSection->addBlock(blockpos, maskBlock, 0xff);
						mask_data.fillCount += 1;
					}
				}
			}
		}
	}
	else
	{
		for (int y = rangeMin.y; y <= maxy; y++)
		{
			for (int x = rangeMin.x; x <= rangeMax.x; x++)
			{
				WCoord startPoint(0, 0, 0);
				WCoord endPoint(0, 0, 0);

				bool findStartPoint = false;
				bool findEndPoint = false;
				for (int z = rangeMin.z; z <= rangeMax.z; z++)
				{
					Block* block = &m_Blocks[x][y][z]->m_Block;
					if (block != nullptr && !block->isEmpty())
					{
						int next = z + 1;
						if (next >= rangeMax.z) break;
						Block* nextBlock = &m_Blocks[x][y][next]->m_Block;
						if (nextBlock == nullptr || nextBlock->isEmpty())
						{
							if (checkBlockCanFill(x, y, next, rangeMin, rangeMax, DirectionType::DIR_POS_X))
							{
								startPoint.x = x;
								startPoint.y = y;
								startPoint.z = next;
								findStartPoint = true;
								break;
							}

						}
					}
				}
				if (findStartPoint)
				{
					for (int z = rangeMax.z; z >= rangeMin.z; z--)
					{
						Block* block = &m_Blocks[x][y][z]->m_Block;
						if (block != nullptr && !block->isEmpty())
						{
							int next = z - 1;
							if (next <= rangeMin.z) break;
							Block* nextBlock = &m_Blocks[x][y][next]->m_Block;
							if (nextBlock == nullptr || nextBlock->isEmpty())
							{
								if (checkBlockCanFill(x, y, next, rangeMin, rangeMax, DirectionType::DIR_POS_X))
								{
									endPoint.x = x;
									endPoint.y = y;
									endPoint.z = next;
									findEndPoint = true;
									break;
								}
							}
						}
					}
				}
				if (findStartPoint && findEndPoint)
				{
					for (int z = startPoint.z; z <= endPoint.z; z++)
					{
						WCoord pos(x, y, z);
						WCoord blockpos = pos - centerPos;
						mask_data.mechaSection->addBlock(blockpos, maskBlock, 0xff);
						mask_data.fillCount += 1;
					}
				}
			}
		}
	}
}


void ActorVehicleAssemble::initWaterMaskMaterial()
{
	if (m_WaterMaskMat == nullptr)
	{
		m_WaterMaskMat = GetMaterialManager().LoadFromFile("Materials/MiniGame/Block/block_opaque.templatemat")->CreateInstance();
		m_WaterMaskMat->SetStencilRef(1 << 0);
		m_WaterMaskMat->SetColorWriteMask(0);
		m_WaterMaskMat->SetCullMode(CullMode::kCullOff);
		m_WaterMaskMat->SetStencilCompFunc(CompareFunction::kFuncAlways);
		m_WaterMaskMat->SetStencilOpFail(StencilOp::kStencilOpKeep);
		m_WaterMaskMat->SetStencilOpPass(StencilOp::kStencilOpReplace);
		m_WaterMaskMat->SetStencilOpZFail(StencilOp::kStencilOpKeep);
		m_WaterMaskMat->SetDepthBias(1);
		m_WaterMaskMat->SetSlopeScaledDepthBias(0.5f);
		m_WaterMaskMat->SetDepthWrite(DepthWrite::kDepthWriteDisable);
	}
}


bool ActorVehicleAssemble::destroyBlock(const int& x, const int& y, const int& z)
{
	if (m_Blocks[x][y][z]->m_Block.isEmpty()) return false;
	m_Blocks[x][y][z]->m_iCurLife = 0;
	std::stringstream stringstream;
	stringstream << x << "_" << y << "_" << z;
	std::string strPos;
	stringstream >> strPos;
	WCoord coord(x, y, z);
	ContactDesc contactDesc;
	contactDesc.m_BlockPos = coord;
	contactDesc.m_BlockID = m_Blocks[x][y][z]->m_Block.getResID();
	contactDesc.m_bdestroy = true;
	contactDesc.m_bIsDrop = false;
	m_ContactDescs[strPos] = contactDesc;
	int blockResId = contactDesc.m_BlockID;
	check_link_core(false);
	check_dismount(coord);
	if (blockResId == BLOCK_BOAT_FLOATBUCKET)
	{
		m_floatBucketMgr.removeInfo(WCoord(x, y, z));
	}
	return true;
}
bool ActorVehicleAssemble::addBlock(const int& x, const int& y, const int& z, const int& dir, const int& blockID, ClientPlayer* player, IntersectResult* result)
{
	if (m_EngineNum > 0 && (blockID == BLOCK_INFINITEENGINE || blockID == BLOCK_VEHICLEENGINE)) return false;


	auto blockdef = GetDefManagerProxy()->getPhysicsActorDef(blockID);
	if (!blockdef || blockdef->SecondBuild == -1) return false;
	const PhysicsPartsDef* physicsPartsDef = GetDefManagerProxy()->getPhysicsPartsDef(blockID);
	if (physicsPartsDef && physicsPartsDef->IsCore)
	{
		return false;
	}


	if (x < 0 || x >= MAX_DIM_X) return false;
	if (y < 0 || y >= MAX_DIM_Y) return false;
	if (z < 0 || z >= MAX_DIM_Z) return false;
	
	
	bool ret = player->placeBlock(blockID, x, y, z,dir,0.f, 0.f, 0.f, false, false, 0, m_pVehicleWorld);
	/*
	BlockMaterial * mtl = g_BlockMtlMgr.getMaterial(blockID);
	if (!mtl->canPutOntoFace(m_pVehicleWorld->getWorldProxy(), WCoord(x, y, z), dir))
	{
		return false;
	}

	SandboxContext ctx = SandboxContext()
		.SetData_Usertype<World>("world", m_pVehicleWorld)
		.SetData_UserObject<WCoord>("pos", WCoord(x, y, z))
		.SetData_Number("face", dir)
		.SetData_Number("hitptx", result->facepoint.x)
		.SetData_Number("hitpty", result->facepoint.y)
		.SetData_Number("hitptz", result->facepoint.z)
		.SetData_Number("def_blockdata", 0);

	int blockdata = mtl->EXEC_USEMODULE(GetPlaceBlockData, &ctx);
	if (blockdata < 0) return false;
	int blockdataByPlayer = mtl->EXEC_USEMODULE(GetPlaceBlockDataByPlayer, m_pVehicleWorld, player);
	if (blockdata == 0)
		blockdata = blockdataByPlayer;

	setBlockAll(WCoord(x, y, z), blockID, blockdata);
	*/
	if(ret)
		check_link_core(false);

	if (ret && blockID == BLOCK_BOAT_FLOATBUCKET)
	{
		m_floatBucketMgr.addInfo(WCoord(x, y, z));
	}
	return ret;
}
bool ActorVehicleAssemble::leftClickInteract(ClientActor* actor)
{
	ClientPlayer* player = actor->ToCast<ClientPlayer>();
	if (!player) return false;
	if (!canDig())
	{
		return false;
	}
	bool ret = false;
	int currItemID = player->getCurToolID();
	// 手持连线钳 先返回
	if (currItemID == ITEM_VEHICLE_LINK_TOOL || currItemID == ITEM_WRENCH)
	{
		return ret;
	}
	int x = -1, y = -1, z = -1;
	intersect(player, x, y, z);
	if (x >= 0 && y >= 0 && z >= 0)
	{
		Block* srcBlock = &m_Blocks[x][y][z]->m_Block;
		if (srcBlock->isEmpty() || m_Blocks[x][y][z]->m_iCurLife == 0)
		{
			return ret;
		}
	}
	return ret;
}
bool ActorVehicleAssemble::checkBlockCanFill(int x, int y, int z, const WCoord& rangeMin, const WCoord& rangeMax, DirectionType dir)
{
	bool result = true;
	float emptyPercent = 1.0f;
	if (dir == DirectionType::DIR_POS_Z)
	{
		bool leftHitBlock = false;
		bool rightHitBlock = false;
		for (int offsetZ = z + 1; offsetZ <= rangeMax.z; offsetZ++)
		{
			Block* block = &m_Blocks[x][y][offsetZ]->m_Block;
			if (block != nullptr && !block->isEmpty())
			{
				rightHitBlock = true;
				break;
			}
		}

		for (int offsetZ = z - 1; offsetZ >= rangeMin.z; offsetZ--)
		{
			Block* block = &m_Blocks[x][y][offsetZ]->m_Block;
			if (block != nullptr && !block->isEmpty())
			{
				leftHitBlock = true;
				break;
			}
		}
		if (leftHitBlock && rightHitBlock)
		{
			return true;
		}
	}
	else if (dir == DirectionType::DIR_POS_X)
	{
		bool leftHitBlock = false;
		bool rightHitBlock = false;
		for (int offsetX = x + 1; offsetX <= rangeMax.x; offsetX++)
		{
			Block* block = &m_Blocks[offsetX][y][z]->m_Block;
			if (block != nullptr && !block->isEmpty())
			{
				rightHitBlock = true;
				break;
			}
		}

		for (int offsetX = x - 1; offsetX >= rangeMin.x; offsetX--)
		{
			Block* block = &m_Blocks[offsetX][y][z]->m_Block;
			if (block != nullptr && !block->isEmpty())
			{
				leftHitBlock = true;
				break;
			}
		}
		if (leftHitBlock && rightHitBlock)
		{
			return true;
		}
	}
	return false;
	//return emptyPercent <= 0.4f;
}

EnginePart::EnginePart(WCoord position, PhysicsPartsDef::EffectFunctionsDef* def, int groupid_, int blockid_, int costvalue, short costinterval) :engineDef(def), blockid(blockid_), num(1), costValue(costvalue), costInterval(costinterval), costintervalCounter(0)
{
	m_mPosGroup[position] = groupid_;
}

void ActorVehicleAssemble::updateVechileFloatBucketInfo(const WCoord& pos, bool inWater)
{
	for (auto& p : m_floatBucketMgr.infos)
	{
		if (p.pos == pos)
		{
			if (p.isInLoaded != inWater)
			{
				m_floatBucketMgr.needUpdateInfo = true;
				p.isInLoaded = inWater;
				m_floatBucketMgr.shouldAddFloatBucketTick = false;
			}
			break;
		}
	}
}

bool ActorVehicleAssemble::shouldAddFloat()
{
	if (!m_floatBucketMgr.shouldAddFloatBucketTick)
	{
		return false;
	}
	if (!m_floatBucketMgr.needUpdateInfo)
	{
		return m_floatBucketMgr.isInLoaded;
	}
	if (m_floatBucketMgr.infos.empty())
	{
		return false;
	}
	bool ret = true;
	for (auto& p : m_floatBucketMgr.infos)
	{
		if (!p.isInLoaded)
		{
			ret = false;
			break;
		}
	}
	m_floatBucketMgr.isInLoaded = ret;
	m_floatBucketMgr.needUpdateInfo = false;
	return ret;
}

void ActorVehicleAssemble::initVechileFloat()
{
	m_floatBucketMgr.shouldAddFloatBucketTick = true;
}

void VehicleFloatBucketMgr::clear()
{
	infos.clear();
	needUpdateInfo = false;
	isInLoaded = false;
}

void VehicleFloatBucketMgr::addInfo(const WCoord& pos)
{
	for (auto& p : infos)
	{
		if (p.pos == pos)
		{
			return;
		}
	}
	infos.push_back(VehicleFloatBucketInfo(pos));
}

void VehicleFloatBucketMgr::removeInfo(const WCoord& pos)
{
	for (auto p = infos.begin(); p != infos.end(); p++)
	{
		if (p->pos == pos)
		{
			infos.erase(p);
			return;
		}
	}
}