#include "CraftingQueue.h"
#include "PlayerControl.h"
#include "backpack.h"
#include "world.h"

CraftingQueue::CraftingQueue(ClientPlayer* player,QueueUpdateCallback qCallback, ProgressUpdateCallback pCallback)
    :mPlayer(player), queueCallback(qCallback), progressCallback(pCallback) { }

void CraftingQueue::addTask(int craftingId, int count, int ticksPerItem) {

	if (queue.size() >= 8) //限制制作队列最多8个任务
	{
		mPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000901);
		return; // 队列已满
	}

	/*if (queue.size() >= 30) {
		return;
	}*/
    queue.emplace_back(craftingId, count, ticksPerItem);
   // queue.push_back(craft::Task(craftingId, count, ticksPerItem));
	auto backpack = mPlayer->getBackPack();
	if (backpack != nullptr)
		backpack->doPlayerPreDeductCraftMaterials(craftingId, count);
    queueCallback(queue, mPlayer);
}

void CraftingQueue::loadAddTask(int craftingId, int count, int ticksPer, int ticksPerItem)
{
	queue.emplace_back(craftingId, count, ticksPer, ticksPerItem);
	// queue.push_back(craft::Task(craftingId, count, ticksPerItem));
	auto backpack = mPlayer->getBackPack();
	//if (backpack != nullptr)
	//	backpack->doPlayerPreDeductCraftMaterials(craftingId, count);
	queueCallback(queue, mPlayer);
}

void CraftingQueue::removeTask(int index) {
    if (index >= 0 && index < queue.size()) {
		auto backpack = mPlayer->getBackPack();
		if (backpack != nullptr)
		{
			//backpack->doPlayerCraftFromWithhold(queue[index].craftingId, queue[index].count);
			backpack->doPlayerReturnPreDeductedMaterialsByCraft(queue[index].craftingId, queue[index].count);
		}
		queue.erase(queue.begin() + index);
        queueCallback(queue, mPlayer);
    }
}

void CraftingQueue::swapTasks(int index1, int index2) {
    if (index1 >= 0 && index1 < queue.size() && index2 >= 0 && index2 < queue.size()) {
        std::swap(queue[index1], queue[index2]);

        // 如果交换的是第一个任务，重置其 remainingTicks
        if (index1 == 0 || index2 == 0) {
            queue[0].remainingTicks = queue[0].ticksPerItem;
        }

        queueCallback(queue, mPlayer);
    }
}

void CraftingQueue::tick() {
    if (mPlayer->GetWorld()->isRemoteMode())
        return;
    if (queue.empty()) return;

    craft::Task& task = queue.front();
    task.remainingTicks--;

	// 🎯 每 20 tick 更新 UI 进度
	if (task.remainingTicks % 20 == 0) {
		//progressCallback(task);
        queueCallback(queue, mPlayer);
	}

    if (task.remainingTicks == 0) {
        task.count--;
        task.remainingTicks = task.ticksPerItem;
        //progressCallback(task);
        queueCallback(queue, mPlayer);

		auto backpack = mPlayer->getBackPack();
		if (backpack != nullptr)
		{
			int remain = 0;
			//bool suc = backpack->doCrafting(task.craftingId, &remain, 1);
			bool suc = backpack->doPlayerCraftFromWithhold(task.craftingId, &remain, 1);
			if (!suc)
			{
				//sendError2Client(uin, PB_ERROR_CRAFT_NOT_ENOUGH);
			}
			else if (remain > 0)
			{
				//sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
			}
			//return;
		}

        if (task.count == 0) {
            queue.erase(queue.begin());
            queueCallback(queue, mPlayer);
        }
    }
}

int CraftingQueue::getCountDown()
{
    if (queue.empty()) return 0;
    const craft::Task& task = queue.front();
    return task.remainingTicks / 20;
}

float CraftingQueue::getProgress()
{
    if (queue.empty()) return 0.0f;
    const craft::Task& task = queue.front();
    if (task.ticksPerItem <= 0) return 0.0f;
    
    // 计算当前物品的进度百分比 (0-1)
    float progress = (float)(task.ticksPerItem - task.remainingTicks) / task.ticksPerItem;
    
    // 确保进度在0-1范围内
    if (progress < 0.0f) progress = 0.0f;
    if (progress > 1.0f) progress = 1.0f;
    return progress;
}

int CraftingQueue::getQueueSize()
{
	return queue.size();
}

int CraftingQueue::getCraftId(int index)
{
	if (index >= 0 && index < queue.size()) {
		return queue[index].craftingId;
	}
	return -1;
}

int CraftingQueue::getCraftCount(int index)
{
	if (index >= 0 && index < queue.size()) {
		return queue[index].count;
	}
	return -1;
}

int CraftingQueue::getCraftTicks(int index)
{
	if (index >= 0 && index < queue.size()) {
		return queue[index].ticksPerItem / 20;
	}
	return -1;
}

void CraftingQueue::ClearQueue()
{
    queue.clear();
}

craft::Task CraftingQueue::getTask(int index)
{
	if (index >= 0 && index < queue.size()) {
		return queue[index];
	}
	return craft::Task();
}

void CraftingQueue::Net2Client(int craftingId, int count, int ticksPer, int remainingTick)
{
	queue.emplace_back(craftingId, count, ticksPer, remainingTick);
	//queueCallback(queue, mPlayer);
}

