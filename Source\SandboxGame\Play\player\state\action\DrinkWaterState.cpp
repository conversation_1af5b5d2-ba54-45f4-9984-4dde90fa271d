#include "DrinkWaterState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PlayerStateController.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "OgreTimer.h"

#include "PlayerAttrib.h"
#include "SoundComponent.h"
#include "ClientActorFuncWrapper.h"
#include "SandboxIdDef.h"

DrinkWaterState::DrinkWaterState(PlayerControl* host) : PlayerState(host), DrinkWaterStateAction(nullptr), m_DrinkStartMark(0), 
                                                       m_DrinkItemDuration(0), m_PlayDrinkTicks(0),
                                                       m_DrinkMark(0),
                                                       m_HasFinished(false)
{
	m_StateID = "DrinkWater";
}

DrinkWaterState::~DrinkWaterState()
{

}

void DrinkWaterState::doBeforeEntering()
{
	m_OperateTicks = 0;
	m_DrinkStartMark = Rainbow::Timer::getSystemTick();
	createMarkMD5(m_DrinkStartMark);
	m_CurToolID = m_Host->getCurToolID();
	m_CurShortcut = m_Host->getCurShortcut();

	const FoodDef* fooddef = GetDefManagerProxy()->getFoodDef(m_CurToolID);
	if (fooddef == NULL)
	{
		return;
	}
	//tick转换成毫秒
	m_DrinkItemDuration = fooddef->UseTime * 50;
	const ItemDef* def = GetDefManagerProxy()->getItemDef(m_CurToolID);
	if (def->Type == 100 /*== 水囊id*/)
	{
		if (!m_Host->getWorld()->isRemoteMode())
		{
			auto soundComp = m_Host->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound("misc.drink", 1.0f, 1.0f);
			}
		}

		m_PlayDrinkTicks = 0;
		m_DrinkMark = m_DrinkItemDuration - 300;
		m_HasFinished = false;
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
	}
}

std::string DrinkWaterState::update(float dtime)
{
	auto functionWrapper = m_Host->getFuncWrapper();
	if (!m_CurToolID) return "ToActionIdle";

	if ((IsUseActionEnd() || m_Host->getCurToolID()!=m_CurToolID || m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead()))
	{
		m_Host->useItem(m_CurToolID, PLAYEROP_STATUS_CANCEL);
		m_Host->setOperate(PLAYEROP_NULL);
		return "ToActionIdle";
	}
	//效果禁用，也可能喝水但效果不好无法恢复水分 code by：开发者
	if (m_Host->getPlayerAttrib() != NULL && !m_Host->getPlayerAttrib()->IsEnableEatFood())
	{
		return "ToActionIdle";
	}

	int curtick = Rainbow::Timer::getSystemTick();

	if(m_HasFinished && curtick-m_DrinkStartMark >= m_DrinkItemDuration+300)
	{
		m_HasFinished = false;
		m_DrinkStartMark = Rainbow::Timer::getSystemTick();
		createMarkMD5(m_DrinkStartMark);
		m_Host->m_PlayerAnimation->performEat(); // 假设有performDrink动画
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_BEGIN);
		return "";
	}

	if(!m_HasFinished && curtick-m_DrinkStartMark>=m_DrinkItemDuration)
	{
		//检查m_DrinkStartMark是否被篡改
		if (!checkMarkMD5(m_DrinkStartMark))
		{
			m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
			m_Host->setOperate(PLAYEROP_NULL);
			return "ToActionIdle";
		}
		m_HasFinished = true;
		m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_END);
		m_Host->m_PlayerAnimation->performIdle();
		m_OperateTicks = 0;
		return "";
	}

	m_PlayDrinkTicks += Rainbow::TimeToTick(dtime);
	if(m_PlayDrinkTicks >= 500)
	{
		if (!m_Host->getWorld()->isRemoteMode())
		{
			auto soundComp = m_Host->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound("misc.drink", 1.0f, 1.0f);
			}
		}
		m_PlayDrinkTicks = 0;
	}

	return "";
}

void DrinkWaterState::doBeforeLeaving()
{
	m_Host->useItem(m_Host->getCurToolID(), PLAYEROP_STATUS_CANCEL);
	m_Host->m_PlayerAnimation->performIdle();
}

void DrinkWaterState::OnTick(float elapse)
{
	m_OperateTicks++;
} 