#include "ToolDefCsv.h" 
#include "OgreUtils.h"
#include "ModManager.h"
#include "OgreStringUtil.h"
using MINIW::CSVParser; 
IMPLEMENT_LAZY_SINGLETON(ToolDefCsv) 

ToolDefCsv::ToolDefCsv() 
{
	m_ToolTable.clear();
	m_MineToolIcon.clear();
} 

ToolDefCsv::~ToolDefCsv() 
{ 
	onClear();
} 

void ToolDefCsv::onParse(CSVParser& parser) 
{
	parser.SetTitleLine(1);
	int numLines = (int)parser.GetNumLines();
	for (int i=2; i<numLines; ++i)
	{
		int id = parser[i]["ID"].Int();
		if(id == 0) continue;

		ToolDef def;
		def.ID = id;
		def.Name = parser[i]["Name"].Str(); 

		def.Type = parser[i]["Type"].Int();
		def.AvatarPartID = parser[i]["AvatarPartID"].Int();

		def.Level = parser[i]["Level"].Int();
		def.SkillCD = parser[i]["SkillCD"].Float();
		def.Efficiency = parser[i]["Efficiency"].Float();
		def.AttackType = parser[i]["AttackType"].Short();

		// 攻击力（0-9：近战、远程、爆炸、物理、火、毒、混乱、电、冰、魔法）
		def.Attacks[0] = 0;
		def.Attacks[1] = 0;
		def.Attacks[2] = 0;
		// 近战
		if (def.AttackType == 0)
		{
			def.Attacks[0] = parser[i]["Attack"].Short();
		}
		// 远程
		else if (def.AttackType == 1)
		{
			def.Attacks[1] = parser[i]["Attack"].Short();
		}
		// 爆炸
		else if(def.AttackType == 2)
		{
			def.Attacks[2] = parser[i]["Attack"].Short();
		}
		def.Attacks[3] = 0;
		def.Attacks[4] = parser[i]["AttackFire"].Float();
		def.Attacks[5] = parser[i]["AttackPoison"].Float();
		def.Attacks[6] = parser[i]["AttackWither"].Float();
		def.Attacks[7] = 0; // 默认电攻击0，暂未投入电伤害和防御
		def.Attacks[8] = parser[i]["AttackIce"].Float();
		def.Attacks[9] = parser[i]["AttackMagic"].Float();

		//物理防御（0-9：近战、远程、爆炸、物理、火、毒、混乱、电、冰、魔法）
		def.Armors[0] = parser[i]["MeleeDefence"].Float();
		def.Armors[1] = parser[i]["RangeDefence"].Float();
		def.Armors[2] = parser[i]["ExplosionDefence"].Float();
		def.Armors[3] = 0; 
		def.Armors[4] = parser[i]["BeastDefence"].Float();    // 野兽
		def.Armors[5] = parser[i]["RadiationDefence"].Float();  // 辐射
		def.Armors[6] = parser[i]["TemptureDefence"].Float();      // 温度  TODO 新的三种抗性实装
		def.Armors[7] = parser[i]["ArmorWither"].Float();
		def.Armors[8] = parser[i]["ArmorIce"].Float();
		def.Armors[9] = parser[i]["ArmorMagic"].Float();
		// 防御值统一调整为减伤，万分比
		for (int j = 0; j < 10; j++)
		{
			if (def.Armors[j] > 0)
			{
				def.Armors[j] = def.Armors[j] / 10000.0f;
			}
		}

		def.MoveSpeed = parser[i]["MoveSpeed"].Short();
		def.SwimSpeed = parser[i]["SwimSpeed"].Short();

		def.Duration = parser[i]["Duration"].Int();
		def.RepairDecresePercent = parser[i]["RepairDecreasePercent"].Float() * 0.01;
		def.Farmingspeed = parser[i]["Farmingspeed"].Int();
		def.CollectDuration = parser[i]["CollectDuration"].Int();
		def.AtkDuration = parser[i]["AtkDuration"].Int();

		//视野亮度值
		def.ViewLight = parser[i]["ViewLight"].Short();
		def.ResPressureLevel = parser[i]["ResPressureLevel"].Short();
		def.PressDuration = parser[i]["PressDuration"].Short();

		for(int r=0; r<6; r++)
		{
			char colname[64];
			sprintf(colname, "RepairID%d", r+1);
			def.RepairId[r] = parser[i][colname].Int();
			sprintf(colname, "RepairAmount%d", r+1);
			def.RepairAmount[r] = parser[i][colname].Int();
		}

		def.Score = parser[i]["Score"].Float();
		def.Enchant = parser[i]["Enchant"].Int();
		def.Tunestone = parser[i]["Tunestone"].Int();
		def.ConsumeID = parser[i]["ConsumeItemID"].Int();
		def.ConsumeCount = parser[i]["ConsumeCount"].Int();
		def.AccumulatorTime = parser[i]["AccumulatorTime"].Float();
		def.AccumulatorType = parser[i]["AccumulatorType"].Int();
		def.HandDigSeq = parser[i]["HandDigSeq"].Int();
		def.HandStartSeq = parser[i]["HandStartSeq"].Int();
		def.HandLoopSeq = parser[i]["HandLoopSeq"].Int();
		def.HandAtkSeq = parser[i]["HandAtkSeq"].Int();
		def.ToolStartSeq = parser[i]["ToolStartSeq"].Int();
		def.ToolLoopSeq = parser[i]["ToolLoopSeq"].Int();
		def.ToolAtkSeq = parser[i]["ToolAtkSeq"].Int();
		def.BodyStandSeq = parser[i]["BodyStandSeq"].Int();
		def.BodyDigSeq = parser[i]["BodyDigSeq"].Int();
		def.BodyLoopSeq = parser[i]["BodyLoopSeq"].Int();
		def.BodyAtkSeq = parser[i]["BodyAtkSeq"].Int();

		def.PlayerIdleSeq = parser[i]["PlayerIdleSeq"].Int();
		def.PlayerWalkSeq = parser[i]["PlayerWalkSeq"].Int();
		def.PlayerRunSeq = parser[i]["PlayerRunSeq"].Int();
		def.PlayerSneakSeq = parser[i]["PlayerSneakSeq"].Int();
		def.PlayerSneakWalkSeq = parser[i]["PlayerSneakWalkSeq"].Int();
		def.PlayerJumpSeq = parser[i]["PlayerJumpSeq"].Int();

		def.SwitchModel = parser[i]["SwitchModel"].Int();
		def.IsPlayerCustom = false;
		def.SubType= parser[i]["SubType"].Int();
		def.SubLevel = parser[i]["SubLevel"].Int();
		def.BodyAtkEffect = parser[i]["BodyAtkEffect"].Str();
		def.AtkSound = parser[i]["AtkSound"].Str();
		def.FireSound = parser[i]["FireSound"].Str();
		def.UseSound = parser[i]["UseSound"].Str();
		def.EnchantEffectScale = parser[i]["EnchantEffectScale"].Int();

		def.SpeedAdd = parser[i]["SpeedAdd"].Float();
		def.CanThrow = parser[i]["CanThrow"].Bool();
		def.AccumulatorExpend = parser[i]["AccumulatorExpend"].Int();
		def.punchBuffId = parser[i]["punchBuffId"].Int();
		def.punchBuffV = parser[i]["punchBuffV"].Int();
		def.TemperatureDefense = parser[i]["TemperatureDefense"].Float();
		std::string key1 = "EffectBankID";
		std::string key2 = "Default";
		for (int k = 1; k <= MAX_BUFF_ATTRIBS; k++)
		{
			def.EffInfo[k - 1].CopyID = parser[i][(key1 + toString(k)).c_str()].Int();

			vector<string> vDefault;
			vDefault.clear();
			std::string sDefault = parser[i][(key2 + toString(k)).c_str()].String();
			Rainbow::StringUtil::split(vDefault, sDefault, "|");
			if (vDefault.size() == 0) {
				auto effectDef = g_DefMgr.getBuffEffectDef(def.EffInfo[k - 1].CopyID);
				if (effectDef) {
					for (int m = 0; m < MAX_BUFF_ATTRIBS; m++) {
						if (effectDef->EffectParam[m].UIType > 0) { break; }

						def.EffInfo[k - 1].Value[m] = effectDef->EffectParam[m].Default;
					}
				}
			}
			else {
				for (int h = 0; h < MAX_BUFF_ATTRIBS && h < (int)vDefault.size(); h++) {
					def.EffInfo[k - 1].Value[h] = atoi(vDefault[h].c_str());
				}
			}
		}

		//-----------战斗相关-----------
		vector<string> vTemp;
		std::string sDefault = parser[i]["AtkComboSeq"].Str();
		Rainbow::StringUtil::split(vTemp, sDefault, "|");
		for (unsigned int index = 0; index < vTemp.size(); index++) {
			if (index >= MAX_ATTACK_CONFIG) continue;
			def.AtkComboSeq[index] = vTemp[index].c_str();
		}
		def.SwimAtkSeq = parser[i]["SwimAtkSeq"].Str();
		def.RideAtkSeq = parser[i]["RideAtkSeq"].Str();

		def.ComboTimer = parser[i]["ComboTimer"].Int();
		def.TouReduce = parser[i]["TouReduce"].Int();
		// 不填默认是5（策划:cjt）
		if (def.TouReduce == 0)
		{
			def.TouReduce = 5;
		}
		def.AtkSpeed = parser[i]["AtkSpeed"].Float();

		def.Toughness = parser[i]["Toughness"].Int();
		def.TouRecoverDelay = parser[i]["TouRecoverDelay"].Float();
		def.TouRecoverSpeed = parser[i]["TouRecoverSpeed"].Int();
		def.RepelRes = parser[i]["RepelRes"].Int();
		def.MarkScore = parser[i]["MarkScore"].Int();

		def.IsGroup = parser[i]["IsGroup"].Int() > 0;
		m_ToolTable.AddRecord(def.ID, def);

		if ((def.Type > 0 && def.Level > 0) || def.ID == 11000)
		{
			int key = def.Type * 100 + def.Level;
			if (m_MineToolIcon.find(key) == m_MineToolIcon.end())
			{
				m_MineToolIcon[key] = def.ID;
			}
		}
	}
} 
void ToolDefCsv::onClear() 
{ 
} 
const char* ToolDefCsv::getName() 
{ 
    return "tooldef"; 
} 
const char* ToolDefCsv::getClassName() 
{ 
    return "ToolDefCsv"; 
} 

std::map<int, int> &ToolDefCsv::getMineToolIcon()
{
	load();

	return m_MineToolIcon;
}

DefDataTable<ToolDef> &ToolDefCsv::getToolTable()
{
	load();

	return m_ToolTable;
}

int ToolDefCsv::getNum()
{
	load();

	return m_ToolTable.GetRecordSize();
}

const ToolDef *ToolDefCsv::get(int id)
{
	load();

	ToolDef* def = g_ModMgr.tryGetToolDef(id);
	if (def != nullptr)
	{
		return def;
	}

	return m_ToolTable.GetRecord(id);
}

const ToolDef *ToolDefCsv::getByIndex(int index)
{
	return m_ToolTable.GetRecordByIndex(index);
}

ToolDef* ToolDefCsv::getOriginal(int id)
{
	load();

	return m_ToolTable.GetRecord(id);
}

ToolDef* ToolDefCsv::addByCopy(int id, int copyId)
{
	load();

	const ToolDef* templateToolDef = get(copyId);
	if (!templateToolDef)
		return NULL;

	return add(id, templateToolDef);
}

void ToolDefCsv::add(int id, int type)
{	
	load();

	int defaultId = 10000 - type;
	const ToolDef* templateToolDef = get(defaultId);
	if(templateToolDef == NULL) return;

	add(id, templateToolDef);
}

ToolDef* ToolDefCsv::add(int id, const ToolDef* templateToolDef)
{	
	if(templateToolDef == NULL) return NULL;
	ToolDef tmpToolDef;
	tmpToolDef.ID = id;
	tmpToolDef.Name = templateToolDef->Name;

	tmpToolDef.Type = templateToolDef->Type;
	tmpToolDef.Level = templateToolDef->Level;
	tmpToolDef.SkillCD = templateToolDef->SkillCD;
	tmpToolDef.Efficiency = templateToolDef->Efficiency;
	tmpToolDef.AttackType = templateToolDef->AttackType;
	memcpy(tmpToolDef.Attacks, templateToolDef->Attacks, sizeof(templateToolDef->Attacks));
	memcpy(tmpToolDef.Armors, templateToolDef->Armors, sizeof(templateToolDef->Armors));
	tmpToolDef.MoveSpeed = templateToolDef->MoveSpeed;
	tmpToolDef.Duration = templateToolDef->Duration;
	tmpToolDef.CollectDuration = templateToolDef->CollectDuration;
	tmpToolDef.AtkDuration = templateToolDef->AtkDuration;

	for (int r = 0; r < 6; r++)
	{
		char colname[64];
		sprintf(colname, "RepairID%d", r + 1);
		tmpToolDef.RepairId[r] = templateToolDef->RepairId[r];
		sprintf(colname, "RepairAmount%d", r + 1);
		tmpToolDef.RepairAmount[r] = templateToolDef->RepairAmount[r];
	}

	tmpToolDef.Score = templateToolDef->Score;
	tmpToolDef.Enchant = templateToolDef->Enchant;
	tmpToolDef.Tunestone = templateToolDef->Tunestone;
	tmpToolDef.ConsumeID = templateToolDef->ConsumeID;
	tmpToolDef.ConsumeCount = templateToolDef->ConsumeCount;
	tmpToolDef.AccumulatorTime = templateToolDef->AccumulatorTime;
	tmpToolDef.AccumulatorType = templateToolDef->AccumulatorType;
	tmpToolDef.HandDigSeq = templateToolDef->HandDigSeq;
	tmpToolDef.HandStartSeq = templateToolDef->HandStartSeq;
	tmpToolDef.HandLoopSeq = templateToolDef->HandLoopSeq;
	tmpToolDef.HandAtkSeq = templateToolDef->HandAtkSeq;
	tmpToolDef.ToolStartSeq = templateToolDef->ToolLoopSeq;
	tmpToolDef.ToolLoopSeq = templateToolDef->ToolLoopSeq;
	tmpToolDef.ToolAtkSeq = templateToolDef->ToolAtkSeq;
	tmpToolDef.BodyStandSeq = templateToolDef->BodyStandSeq;
	tmpToolDef.BodyDigSeq = templateToolDef->BodyDigSeq;
	tmpToolDef.BodyLoopSeq = templateToolDef->BodyLoopSeq;
	tmpToolDef.BodyAtkSeq = templateToolDef->BodyAtkSeq;
	tmpToolDef.SwitchModel = templateToolDef->SwitchModel;
	tmpToolDef.IsPlayerCustom = false;
	tmpToolDef.SubType = templateToolDef->SubType;
	tmpToolDef.SubLevel = templateToolDef->SubLevel;
	tmpToolDef.BodyAtkEffect = templateToolDef->BodyAtkEffect;
	tmpToolDef.AtkSound = templateToolDef->AtkSound;
	tmpToolDef.FireSound = templateToolDef->FireSound;
	tmpToolDef.UseSound = templateToolDef->UseSound;
	tmpToolDef.EnchantEffectScale = templateToolDef->EnchantEffectScale;
	
	tmpToolDef.SpeedAdd = templateToolDef->SpeedAdd;
	tmpToolDef.CanThrow = templateToolDef->CanThrow;

	tmpToolDef.Toughness = templateToolDef->Toughness;
	tmpToolDef.TouRecoverDelay = templateToolDef->TouRecoverDelay;
	tmpToolDef.TouRecoverSpeed = templateToolDef->TouRecoverSpeed;
	tmpToolDef.RepelRes = templateToolDef->RepelRes;
	tmpToolDef.MarkScore = templateToolDef->MarkScore;
	tmpToolDef.TemperatureDefense = templateToolDef->TemperatureDefense;

	m_ToolTable.AddRecord(id, tmpToolDef);

	if ((tmpToolDef.Type > 0 && tmpToolDef.Level > 0) || tmpToolDef.ID == 11000)
	{
		int key = tmpToolDef.Type * 100 + tmpToolDef.Level;
		if (m_MineToolIcon.find(key) == m_MineToolIcon.end())
		{
			m_MineToolIcon[key] = tmpToolDef.ID;
		}
	}

	return m_ToolTable.GetRecord(id);
}
