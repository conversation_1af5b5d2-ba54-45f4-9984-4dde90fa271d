#include "PlayerStateController.h"
#include "PlayerFSM.h"
#include "PlayerControl.h"

#include "PlayerStateFactory.h"
#include "SandboxParam.h"
#include "SandboxEventDispatcherManager.h"

using namespace MNSandbox;


PlayerStateController::PlayerStateController()
{
	init();
}

void PlayerStateController::clear()
{
	for (auto it = m_fsms.begin(); it != m_fsms.end(); it++)
	{
		ENG_DELETE( it->second );
	}
	m_fsms.clear();

	for (auto it = m_states.begin(); it != m_states.end(); it++)
	{
		ENG_DELETE( it->second );
	}
	m_states.clear();
}


PlayerStateController::~PlayerStateController()
{
	clear();
}

void PlayerStateController::init()
{
	PlayerStateFactory::init();
	clear();
	MINIW::ScriptVM::game()->callFunction("F_Controller_Default_onInit","u[PlayerStateController]", this);
	//constructMovementFSM();
	//constructActionFSM();

	//MINIW::ScriptVM::game()->callFunction("LuaPlayerStateRegisterHandle","u[PlayerStateController]", this);
}
// 用于调试枪械 的lua逻辑进行热重载 --- by charlesxie
void PlayerStateController::resetAllStates()
{
	clear();
	MINIW::ScriptVM::game()->callFunction("F_Controller_Default_onInit", "u[PlayerStateController]", this);
}

std::string PlayerStateController::getCurrentStateID(const char* uniqueFsmName)
{
	PlayerFSM* fsm = getFSM(uniqueFsmName);
	if(!fsm)
		return "";
	PlayerState* st = fsm->getCurrentState();
	if(!st)
		return "";
	return st->getStateID();
}
std::string PlayerStateController::getMovementState()
{
	return getCurrentStateID("move");
}

std::string PlayerStateController::getActionState()
{
	return getCurrentStateID("action");
}

std::string PlayerStateController::getActionBodyState()
{
	return getCurrentStateID("actionBody");
}

void PlayerStateController::update(float dtime)
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (!g_pPlayerCtrl->isDead())
	{
		for (auto it = m_fsms.rbegin(); it != m_fsms.rend(); it++)
		{
			it->second->update(dtime);
		}
	}
	//LOG_INFO("CurrentMovementState: %d" , m_MovementFSM->getCurrentState()->m_StateID);
	//LOG_INFO("CurrentActionState: %d" , m_ActionFSM->getCurrentState()->m_StateID);
}

void PlayerStateController::tick(float dtime)
{
	if (g_pPlayerCtrl == NULL)
	{
		return;
	}

	if (!g_pPlayerCtrl->isDead())
	{
		for (auto it = m_fsms.rbegin(); it != m_fsms.rend(); it++)
		{
			it->second->tick(dtime);
		}
	}
}

bool PlayerStateController::addFSM(const char* uniqueFsmName)
{
	if(uniqueFsmName == NULL || strlen(uniqueFsmName) == 0)
		return false;
	if(m_fsms.find(uniqueFsmName) == m_fsms.end()){
		m_fsms.insert(std::make_pair(uniqueFsmName, ENG_NEW(PlayerFSM)()));
		return true;
	}
	return false;
}

bool PlayerStateController::addState(const char* cplusplusClassName)
{
	if(cplusplusClassName == NULL || g_pPlayerCtrl == NULL)
		return false;

	PlayerState* st = PlayerStateFactory::Create(cplusplusClassName,g_pPlayerCtrl);
	if(!st){
		LOG_WARNING("cplusplus State create failed! unknown class name: %s!  extend cplusplus state class must register to PlayerStateFactory.",cplusplusClassName);
		assert(false);
		return false;
	}
	
	if(st->getStateID() == "" || m_states.find(st->getStateID()) != m_states.end()){
		LOG_WARNING("register failed! invalid stateid:%s",st->getStateID().c_str());
		assert(false);
		ENG_DELETE(st);
		return false;
	}
	m_states.insert(std::make_pair(st->getStateID(), st));//add state
	return true;
}

bool PlayerStateController::addStateExByLua(const char* stateid, const char* luaComponentName)
{
	if(stateid == NULL ||  strlen(stateid) == 0)
		return false;

	if (g_pPlayerCtrl == NULL)
	{
		return false;
	}

	//create luastate
	PlayerState* st = PlayerStateFactory::Create("PlayerLuaState", g_pPlayerCtrl);
	st->Event().Emit("OnBindScript", NS_SANDBOX::SandboxContext(nullptr).SetData_String(luaComponentName));
	st->setStateID(stateid);
	//bind component
	//register
	if(st->getStateID() == "" || m_states.find(st->getStateID()) != m_states.end()){
		LOG_WARNING("register failed! invalid stateid:%s",st->getStateID().c_str());
		assert(false);
		ENG_DELETE(st);
		return false;
	}
	m_states.insert(std::make_pair(st->getStateID(), st));//add state
	return true;
}

bool PlayerStateController::registerStateToFSM(const char* stateid, const char* toFsmName)
{
	if(stateid == NULL || toFsmName == NULL || strlen(stateid) == 0 || strlen(toFsmName) == 0)
		//if(stateId == NULL || luaComponentName == NULL)
		return false;

	auto it = m_states.find(stateid);
	if(it == m_states.end())
		return false;

	PlayerFSM* fsm = getFSM(toFsmName);
	if(!fsm){
		addFSM(toFsmName);
		fsm = getFSM(toFsmName);
	}
	assert(fsm != NULL);
	fsm->addState(it->second);
	return true;
}


bool PlayerStateController::addStateTransition(const char* stateId, const char* transName, const char* toStateId)
{
	if(stateId == NULL || transName == NULL || toStateId == NULL)
		return false;

	auto pTarget = m_states.find(stateId);
	if(pTarget == m_states.end()){//
		return false;
	}
	assert(pTarget->second != NULL);
	pTarget->second->addTransition(transName,toStateId);
	return true;
}

/*

void PlayerStateController::constructMovementFSM()
{
	auto pIdleState = ENG_NEW(IdleState)(g_pPlayerCtrl); 
    registerState(pIdleState);
	pIdleState->addTransition("ToJump", "Jump");
	pIdleState->addTransition("ToWalk", "Walk");
	pIdleState->addTransition("ToSneak", "Sneak");
	pIdleState->addTransition("ToFly", "Fly");
	pIdleState->addTransition("ToJetpack", "Jetpack");
	pIdleState->addTransition("ToSwim", "Swim");

	auto pWalkState = ENG_NEW(WalkState)(g_pPlayerCtrl); 
    registerState(pWalkState);
	pWalkState->addTransition("ToIdle", "Idle");
	pWalkState->addTransition("ToFly", "Fly");
	pWalkState->addTransition("ToJetpack", "Jetpack");
	pWalkState->addTransition("ToSwim", "Swim");


	auto pFlyState = ENG_NEW(FlyState)(g_pPlayerCtrl); 
    registerState(pFlyState);
	pFlyState->addTransition("ToIdle", "Idle");

	auto pJetpackState = ENG_NEW(JetpackState)(g_pPlayerCtrl); 
    registerState(pJetpackState);
	pJetpackState->addTransition("ToIdle", "Idle");
	pJetpackState->addTransition("ToWalk", "Walk");
	pJetpackState->addTransition("ToSwim", "Swim");
	pJetpackState->addTransition("ToFly", "Fly");

	auto pSwimState = ENG_NEW(SwimState)(g_pPlayerCtrl); 
    registerState(pSwimState);
	pSwimState->addTransition("ToIdle", "Idle");
	pSwimState->addTransition("ToFly", "Fly");


	//pJumpState = ENG_NEW(JumpState)(g_pPlayerCtrl);
	//pSneakState = ENG_NEW(SneakState)(g_pPlayerCtrl);

	auto fsm = ENG_NEW(PlayerFSM)();
	fsm->addState(pIdleState);
	fsm->addState(pWalkState);
	fsm->addState(pFlyState);
	fsm->addState(pJetpackState);
	fsm->addState(pSwimState);
	m_fsms["move"] = fsm;
}

void PlayerStateController::constructActionFSM()
{
	auto pActionIdleState = ENG_NEW(ActionIdleState)(g_pPlayerCtrl); 
    registerState(pActionIdleState);
	pActionIdleState->addTransition("ToChargeAttack", "ChargeAttack");
	pActionIdleState->addTransition("ToDig", "Dig");
	pActionIdleState->addTransition("ToRangeDig", "RangeDig");
	pActionIdleState->addTransition("ToChargeDig", "ChargeDig");
	pActionIdleState->addTransition("ToEat", "Eat");
	pActionIdleState->addTransition("ToDrinkWater", "DrinkWater");
	pActionIdleState->addTransition("ToUse", "Use");
	pActionIdleState->addTransition("ToGunUse", "GunUse");
	pActionIdleState->addTransition("ToItemSkillUse", "ItemSkillUse");
	pActionIdleState->addTransition("ToShoot", "Shoot");
	pActionIdleState->addTransition("ToPassBall", "PassBall");
	pActionIdleState->addTransition("ToTackle", "Tackle");
	pActionIdleState->addTransition("ToGravityGunUse", "GravityGunUse");
	pActionIdleState->addTransition("ToGravityGunCharge", "GravityGunCharge");
	pActionIdleState->addTransition("ToBlockShot", "BlockShot");

	pActionIdleState->addTransition("ToObstruct", "Obstruct");
	pActionIdleState->addTransition("ToGrab", "Grab");
	pActionIdleState->addTransition("ToDribbleRun", "DribbleRunBasketBall");
	pActionIdleState->addTransition("ToPassBasketBall", "PassBasketBall");
	pActionIdleState->addTransition("ToShootBasketBall", "ShootBasketBall");
	pActionIdleState->addTransition("ToRecover", "Recover");
	pActionIdleState->addTransition("ToExploit", "Exploit");
	
	auto pDigState = ENG_NEW(DigState)(g_pPlayerCtrl); 
    registerState(pDigState);
	pDigState->addTransition("ToActionIdle", "ActionIdle");

	auto pAdvancedDigState = ENG_NEW(RangeDigState)(g_pPlayerCtrl); 
    registerState(pAdvancedDigState);
	pAdvancedDigState->addTransition("ToActionIdle", "ActionIdle");

	auto pChargeDigState = ENG_NEW(ChargeDigState)(g_pPlayerCtrl); 
    registerState(pChargeDigState);
	pChargeDigState->addTransition("ToActionIdle", "ActionIdle");

	auto pGunUseState = ENG_NEW(GunUseState)(g_pPlayerCtrl); 
    registerState(pGunUseState);
	pGunUseState->addTransition("ToActionIdle", "ActionIdle");

	auto pChargeAttackState = ENG_NEW(ChargeAttackState)(g_pPlayerCtrl); 
    registerState(pChargeAttackState);
	pChargeAttackState->addTransition("ToActionIdle", "ActionIdle");

	auto pEatState = ENG_NEW(EatState)(g_pPlayerCtrl); 
    registerState(pEatState);
	pEatState->addTransition("ToActionIdle", "ActionIdle");

	auto pDrinkWaterState = ENG_NEW(DrinkWaterState)(g_pPlayerCtrl); 
    registerState(pDrinkWaterState);
	pDrinkWaterState->addTransition("ToActionIdle", "ActionIdle");

	auto pUseState = ENG_NEW(UseState)(g_pPlayerCtrl); 
    registerState(pUseState);
	pUseState->addTransition("ToActionIdle", "ActionIdle");

	auto pUseItemSkillState = ENG_NEW(ChargeItemSkillState)(g_pPlayerCtrl); 
    registerState(pUseItemSkillState);
	pUseItemSkillState->addTransition("ToActionIdle", "ActionIdle");

	auto pShootState = ENG_NEW(ShootState)(g_pPlayerCtrl); 
    registerState(pShootState);
	pShootState->addTransition("ToActionIdle", "ActionIdle");




	auto pPassBallState = ENG_NEW(PassBallState)(g_pPlayerCtrl); 
    registerState(pPassBallState);
	pPassBallState->addTransition("ToActionIdle", "ActionIdle");

	auto pTackleState = ENG_NEW(TackleState)(g_pPlayerCtrl); 
    registerState(pTackleState);
	pTackleState->addTransition("ToActionIdle", "ActionIdle");

	auto pGravityGunUseState = ENG_NEW(GravityGunUseState)(g_pPlayerCtrl); 
    registerState(pGravityGunUseState);
	pGravityGunUseState->addTransition("ToActionIdle", "ActionIdle");

	auto pGravityGunChargeState = ENG_NEW(GravityGunChargeState)(g_pPlayerCtrl); 
    registerState(pGravityGunChargeState);
	pGravityGunChargeState->addTransition("ToActionIdle", "ActionIdle");

	auto pObstructState = ENG_NEW(ObstructState)(g_pPlayerCtrl); 
    registerState(pObstructState);
	pObstructState->addTransition("ToActionIdle", "ActionIdle");

	auto pGrabState = ENG_NEW(GrabState)(g_pPlayerCtrl); 
    registerState(pGrabState);
	pGrabState->addTransition("ToActionIdle", "ActionIdle");

	auto pBlockShotState = ENG_NEW(BlockShotState)(g_pPlayerCtrl); 
    registerState(pBlockShotState);
	pBlockShotState->addTransition("ToActionIdle", "ActionIdle");

	auto pDribbleRunBasketBallState = ENG_NEW(DribbleRunBasketBallState)(g_pPlayerCtrl); 
    registerState(pDribbleRunBasketBallState);
	pDribbleRunBasketBallState->addTransition("ToActionIdle", "ActionIdle");

	auto pPassBasketBallState = ENG_NEW(PassBasketBallState)(g_pPlayerCtrl); 
    registerState(pPassBasketBallState);
	pPassBasketBallState->addTransition("ToActionIdle", "ActionIdle");

	auto pShootBasketBallState = ENG_NEW(ShootBasketBallState)(g_pPlayerCtrl); 
    registerState(pShootBasketBallState);
	pShootBasketBallState->addTransition("ToActionIdle", "ActionIdle");

	auto pRecoverState = ENG_NEW(RecoverState)(g_pPlayerCtrl); 
    registerState(pRecoverState);
	pRecoverState->addTransition("ToActionIdle", "ActionIdle");

	auto pExploitState = ENG_NEW(ExploitState)(g_pPlayerCtrl); 
    registerState(pExploitState);
	pExploitState->addTransition("ToActionIdle", "ActionIdle");

	auto actionFSM = ENG_NEW(PlayerFSM)();
	actionFSM->addState(pActionIdleState);
	actionFSM->addState(pDigState);
	actionFSM->addState(pAdvancedDigState);
	actionFSM->addState(pChargeDigState);
	actionFSM->addState(pChargeAttackState);
	actionFSM->addState(pEatState);
	actionFSM->addState(pDrinkWaterState);
	actionFSM->addState(pUseState);
	actionFSM->addState(pGunUseState);
	actionFSM->addState(pUseItemSkillState);
	actionFSM->addState(pShootState);
	actionFSM->addState(pPassBallState);
	actionFSM->addState(pTackleState);
	actionFSM->addState(pGravityGunUseState);
	actionFSM->addState(pGravityGunChargeState);
	actionFSM->addState(pObstructState);
	actionFSM->addState(pGrabState);
	actionFSM->addState(pBlockShotState);
	actionFSM->addState(pDribbleRunBasketBallState);
	actionFSM->addState(pPassBasketBallState);
	actionFSM->addState(pShootBasketBallState);
	actionFSM->addState(pRecoverState);
	actionFSM->addState(pExploitState);
	m_fsms["action"] = actionFSM;
}
*/

void PlayerStateController::performMoveTransition(const std::string &t)
{
	PlayerFSM* fsm = getFSM("move");
	if (fsm != nullptr)
	{
		fsm->performTransition(t);
	}
}

bool PlayerStateController::performActionTransition(const std::string &t)
{
	PlayerFSM* fsm = getFSM("action");
	if (fsm != nullptr)
	{
		return fsm->performTransition(t);
	}
	return false;
}

bool PlayerStateController::performActionBodyTransition(const std::string& t)
{
	PlayerFSM* fsm = getFSM("actionBody");
	if (fsm != nullptr)
	{
		return fsm->performTransition(t);
	}
	return false;
}

void PlayerStateController::onDie()
{
	if (getActionState() != "ActionIdle"){
		PlayerFSM* fsm = getFSM("action");
		if(fsm)
			fsm->performTransition("ToActionIdle");
	}
	
	if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->setOperate(PLAYEROP_NULL);
	}
	
}

void PlayerStateController::onSwitchThingInHand()
{
	if(getActionState() != "ActionIdle"){
		PlayerFSM* fsm = getFSM("action");
		if(fsm)
			fsm->performTransition("ToActionIdle");
	}

	if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->setOperate(PLAYEROP_NULL);
	}
}

PlayerState* PlayerStateController::getCurrentMovementStatePtr()
{
	PlayerFSM* fsm = getFSM("move");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getCurrentState();
}

PlayerState* PlayerStateController::getCurrentActionStatePtr()
{
	PlayerFSM* fsm = getFSM("action");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getCurrentState();
}

PlayerState* PlayerStateController::getCurrentActionBodyStatePtr()
{
	PlayerFSM* fsm = getFSM("actionBody");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getCurrentState();
}

PlayerState* PlayerStateController::findMoveStatePtr(const std::string& szMoveState)
{
	PlayerFSM* fsm = getFSM("move");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getState(szMoveState);
}

PlayerState* PlayerStateController::findActionStatePtr(const std::string& szActionState)
{
	PlayerFSM* fsm = getFSM("action");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getState(szActionState);
}

PlayerState* PlayerStateController::findActionStatePtr(const char* szActionState)
{
	PlayerFSM* fsm = getFSM("action");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getState(szActionState);
}

PlayerState* PlayerStateController::findActionBodyStatePtr(const std::string& szActionState)
{
	PlayerFSM* fsm = getFSM("actionBody");
	if (!fsm)
	{
		return nullptr;
	}
	return fsm->getState(szActionState);
}

#ifdef IWORLD_SERVER_BUILD
PlayerState* PlayerStateController::findActionStatePtr(const std::string& szActionState, PlayerControl* pPlayer)
{
	PlayerFSM* fsm = getFSM("action");
	if (!fsm)
	{
		return nullptr;
	}
	auto pState = fsm->getState(szActionState);
	if (nullptr == pState)
	{
		std::string szState = szActionState + "State";
		pState = PlayerStateFactory::Create(szState.c_str(), pPlayer);
		if (nullptr != pState)
		{
			fsm->addState(pState);
			auto pActionBase = dynamic_cast<ActionBase*>(pState);
			if (nullptr != pActionBase)
			{
				pActionBase->setPlayer(pPlayer);
			}
			return pState;
		}
		else
		{
			return nullptr;
		}
	}
	else
	{
		return pState;
	}
}

PlayerState* PlayerStateController::findActionStatePtr(const char* szActionState, PlayerControl* pPlayer)
{
	PlayerFSM* fsm = getFSM("action");
	if (!fsm)
	{
		return nullptr;
	}
	auto pState = fsm->getState(szActionState);
	if(pState)
		return pState;

	std::string szState(szActionState);
	szState.append("State");
	pState = PlayerStateFactory::Create(szState.c_str(), pPlayer);
	if (nullptr != pState)
	{
		fsm->addState(pState);
		auto pActionBase = dynamic_cast<ActionBase*>(pState);
		if (nullptr != pActionBase)
		{
			pActionBase->setPlayer(pPlayer);
		}
		return pState;
	}

	return nullptr;
}

PlayerState* PlayerStateController::findActionBodyStatePtr(const std::string& szActionState, PlayerControl* pPlayer)
{
	PlayerFSM* fsm = getFSM("actionBody");
	if (!fsm)
	{
		return nullptr;
	}
	auto pState = fsm->getState(szActionState);
	if (nullptr == pState)
	{
		std::string szState = szActionState + "State";
		pState = PlayerStateFactory::Create(szState.c_str(), pPlayer);
		if (nullptr != pState)
		{
			fsm->addState(pState);
			auto pActionBase = dynamic_cast<ActionBase*>(pState);
			if (nullptr != pActionBase)
			{
				pActionBase->setPlayer(pPlayer);
			}
			return pState;
		}
		else
		{
			return nullptr;
		}
	}
	else
	{
		return pState;
	}
}

#endif

void PlayerStateController::setClientPlayer(ClientPlayer* pPlayer)
{
	for (auto it = m_fsms.rbegin(); it != m_fsms.rend(); it++)
	{
		(it->second)->setClientPlayer(pPlayer);
	}
}

