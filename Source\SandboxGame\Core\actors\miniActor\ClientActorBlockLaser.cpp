
#include "ClientActorBlockLaser.h"
#include "OgreUtils.h"
#include "world.h"
#include "ClientActorLiving.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "ClientActorManager.h"
#include "special_blockid.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "OgreRay.h"
#include "Entity/OgreEntity.h"
#include "Sound/OgreSoundSystem.h"
#include "container_stone_core.h"
#include "ProjectileLocoMotion.h"

#include "CurveFace.h"
#include "OgreBezierCurve.h"
#include "WorldRender.h"
#include "GameCamera.h"
#include "PlayerControl.h"
#include "container_world.h"
#include "CameraModel.h"
#include "BlockReflectMirror.h"
#include "BlockMaterialMgr.h"
#include "ActorManager.h"
#include "ClientActorHelper.h"
#include "AttackedComponent.h"
#include "SoundComponent.h"
using namespace MINIW;
using namespace Rainbow;

ClientActorBlockLaser::ClientActorBlockLaser(): 
m_nPower(0)
, m_nextActorObj(0),  m_initActorObj(0), m_bBlockShoot(true), m_initBlockPos(0,0,0), m_nBlockDir(0), lastSetPowerpos(0,0,0),m_nShootCount(0)
{
   m_HasImpackActor = true;
   m_bPassActor = true;
}

ClientActorBlockLaser::ClientActorBlockLaser(bool blockshoot, int power, WORLD_ID actorObj, WCoord &blockPos, int dir): m_nPower(power)
, m_nextActorObj(0), m_initActorObj(actorObj), m_initBlockPos(blockPos), m_bBlockShoot(blockshoot), m_nBlockDir(dir), lastSetPowerpos(0,0,0)
, m_nShootCount(0)
{
	m_HasImpackActor = true;
	m_bPassActor = true;
}

ClientActorBlockLaser::~ClientActorBlockLaser()
{
}

flatbuffers::Offset<FBSave::SectionActor> ClientActorBlockLaser::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto startpos = WCoordToCoord3(m_StartPos);
	auto initBlockPos = WCoordToCoord3(m_initBlockPos);
	auto laststpowerpos = WCoordToCoord3(lastSetPowerpos);

	auto obj = FBSave::CreateActorBlockLaser(builder, basedata, m_ItemID, &startpos, &initBlockPos, m_nBlockDir
	, m_initActorObj, m_nextActorObj, m_nPower,m_bBlockShoot ,m_ShootingActorID, &laststpowerpos);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorBlockLaser, obj.Union());
}


bool ClientActorBlockLaser::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorBlockLaser *>(srcdata);
	loadActorCommon(src->basedata());
	init(src->itemid(), NULL);
	m_StartPos = Coord3ToWCoord(src->startpos());
	m_initBlockPos = Coord3ToWCoord(src->initBlockPos());
	m_nBlockDir = src->blockDir();
	m_initActorObj = src->initActorObj();
	m_nextActorObj = src->nextActorObj();
	m_nPower = src->power();
	m_bBlockShoot = src->blockShoot();
	m_ShootingActorID = src->shooter();
	if (src->lastSetPowerpos())
	lastSetPowerpos = Coord3ToWCoord(src->lastSetPowerpos());
	ProjectileLocoMotion* loco = dynamic_cast<ProjectileLocoMotion *>(getLocoMotion());
	if (loco)
	loco->syncPos = getLocoMotion()->getPosition();
	return true;
}

void ClientActorBlockLaser::initFlySound()
{
	SoundCreateInfo3D info;
	info.mindistance = 1600.0f;
	info.maxdistance = 600000.0f;
	info.volume = 1.0f;
	info.pitch = 0.8f + GenRandomFloat()*0.4f;
	info.pos = getPosition().toVector3();
	info.velocity.Set(0,0,0);
	info.isloop = true;
}

void ClientActorBlockLaser::setNeedClearEx(int delay_ticks)
{
	if (!needClear())
	{	
		setNeedClear(delay_ticks);
		m_pWorld->setSpecialBlockPower(lastSetPowerpos, 0);
		m_pWorld->notifyBlockSides(lastSetPowerpos, 0);
	}
}

void ClientActorBlockLaser::tick()
{
	if(m_pWorld->isRemoteMode())
	{
		ClientActor::tick();
		return;
	}

	if (needClear())
	{
		return;
	}

	int power = m_nPower;
	if (m_nShootCount > 10 && m_nShootCount%5 == 0)
	{
		//�ж�һЩ��ʧ������
		if(m_initActorObj)
		{
			ClientActorBlockLaser *laserpre = getBlockLaserPre();
			if (laserpre == NULL || laserpre->getNextActorObj() != getObjId())
			{
				setNeedClearEx();
				return;
			}
		}
		if (m_bBlockShoot)
		{
			BlockStoneCoreContainer *container = dynamic_cast<BlockStoneCoreContainer *>(getWorld()->getContainerMgr()->getContainer(m_initBlockPos));
			if (container)
			{
				if (container->m_nDir != m_nBlockDir)
				{
					setNeedClearEx();
					return;
				}
				else if(container->m_nActorObj != getObjId())
				{
					setNeedClearEx();
					return;	
				}
				power = container->m_nPower;
			}
			else
			{
				setNeedClearEx();
				return;
			}
		}

		if (power != m_nPower)
		{
			setPower(power);
		} 
	}
	ClientActor::tick();

	//�ж����߾���
	if(getLocoMotion()== NULL) return;
	Rainbow::Vector3f motion = getLocoMotion()->m_Motion;
	motion.Normalize();
	WCoord nowpos = getLocoMotion()->getPosition();
	nowpos -= m_StartPos;
	int len = (int)nowpos.lengthSquared();
	if (power <= -15)
	{
		power = -14;
	}
	int maxlen = power * 100 + 1500;
	if(len >= (maxlen*maxlen))
	{
		Rainbow::Vector3f motlen = motion;
		motlen *= (float)maxlen;
		WCoord pos = m_StartPos + motlen;
		getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
		getLocoMotion()->m_Motion = motion;
		len = maxlen*maxlen;
	}

	if (m_nShootCount%50 == 0)
	{
		if (m_ProjectileDef != NULL && m_nBlockDir < 7 && m_bBlockShoot && !m_ProjectileDef->TriggerEffect.empty())
		{
			Rainbow::ColorQuad cq(255, 255, 255, 255);
			char effectPath[128];
			sprintf(effectPath, "particles/%s.ent", m_ProjectileDef->TriggerEffect.c_str());
			m_pWorld->getEffectMgr()->playParticleEffectAsync(effectPath, m_StartPos, 50, 0, 0, true);
		}
	}
	//����ж�
	{
		WORLD_ID shootid = 0;
		MINIW::WorldRay ray;
		ray.m_Origin = m_StartPos.toWorldPos();
		ray.m_Dir = motion;
		ray.m_Range = Rainbow::Sqrt(len + 20);
		//��Ҫ�䵽�Լ�
		ActorExcludes excludes;
		excludes.addActor(this);

		IntersectResult inter_result1, inter_result2;
		IntersectResult *presult;
		//����Ӧ�ü���team���ж�
		int excludeTeam = 0;
		ActorLiving *living = dynamic_cast<ActorLiving *>(getShootingActor());
		std::vector<IClientActor*> actors;
		inter_result2.actors = actors;
		WorldPickResult intertype = m_pWorld->pickAll(ray, &inter_result2, excludes, PICK_METHOD_SOLID_ALL, excludeTeam);
		presult = &inter_result2;
		WCoord endPos = getLocoMotion()->getPosition();
		if(intertype == WorldPickResult::BLOCK) //block
		{
			if (!m_bBlockShoot || m_initBlockPos != presult->block)  
			{
				WCoord blockpos = presult->block;
				//����ƫ��
				Rainbow::Vector3f targetpos = ray.m_Origin.toVector3() + ray.m_Dir * presult->collide_t;
				WCoord pos(targetpos.x, targetpos.y, targetpos.z); 
				getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
				resetSpeed();
				onImpactWithBlockInner(&blockpos, presult->face);
				endPos = targetpos;
			}
		}
		//���߼��ʹ�õ���ԭ���ȼ���,�����ʯͷ��ס,�����Ǳ�ʵ�ʳ���,���ð�Χ�м��һ��
		CollideAABB laserBox;
		{
			int range = BLOCK_SIZE / 2;
			int minx = Min(m_StartPos.x, endPos.x) - range;
			int maxx = Max(m_StartPos.x, endPos.x) + range;
			int minz = Min(m_StartPos.z, endPos.z) - range;
			int maxz = Max(m_StartPos.z, endPos.z) + range;
			int miny = Min(m_StartPos.y, endPos.y) - range;
			int maxy = Max(m_StartPos.y, endPos.y) + range;
			laserBox.setPoints(WCoord(minx, miny, minz), WCoord(maxx, maxy, maxz));
		}

		if(presult->actors.size()) //actor
		{
			if (m_nShootCount%4 == 0)
			{
				OneAttackData atkdata;
				//memset(&atkdata, 0, sizeof(atkdata));
				atkdata.atktype = ATTACK_BLOCK_LASER;
				atkdata.damage_armor = true;
				atkdata.atkpoints = m_ProjectileDef->AttackValue;
				if (atkdata.atkpoints != 0 || atkdata.buffId != 0)
				{
					if(presult){
						ClientActor* actor = NULL;
						CollideAABB actorBox;
						for (int i = 0; i<(int)presult->actors.size(); i++)
						{
							actor = presult->actors[i]->GetActor();
							if (actor)
							{
								actor->getHitCollideBox(actorBox);
								if (!actorBox.intersect(laserBox))
								{
									continue;
								}
								auto component = actor->getAttackedComponent();
								if (component)
								{
									component->attackedFrom(atkdata, NULL);
								}
							}
							//(*presult->actors)[i]->attackedFrom(atkdata, NULL);	
						}
					}
				}
			}
		}
		ClientActorBlockLaser *laser = getBlockLaserNext();
		if (laser && (laser->getShootActorID() && laser->getShootActorID() != shootid))
		{
			laser->setNeedClearEx();
			m_nextActorObj = 0;
		}
	}
	m_nShootCount++;
}

void ClientActorBlockLaser::onImpactWithActor(ClientActor *actor, const std::string& partname)
{
	
}

void ClientActorBlockLaser::doTrigger()
{
	if (m_nBlockDir < 7 && m_bBlockShoot && !m_ProjectileDef->TriggerEffect.empty())
	{
		Rainbow::ColorQuad cq(255, 255, 255, 255);
		char effectPath[128];
		sprintf(effectPath, "particles/%s.ent", m_ProjectileDef->TriggerEffect.c_str());
		m_pWorld->getEffectMgr()->playParticleEffectAsync(effectPath, getPosition(), 100, 0, 0, true, 0, 0, cq);
	}

	//�?放声�?
	float pitch = 1.2f / (GenRandomFloat()*0.2f + 0.9f);
	float volume = 1.0f;
	auto sound = getSoundComponent();
	if (sound)
	{
		sound->playSound(m_ProjectileDef->TriggerSound, volume, pitch);
	}
	
}

ClientActorBlockLaser *ClientActorBlockLaser::getBlockLaserPre()
{
	if (!m_pWorld)
	{
		return nullptr;
	}
	if (!m_initActorObj)
	{
		return NULL;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return nullptr;
	ClientActorBlockLaser* laser = dynamic_cast<ClientActorBlockLaser *>(actorMgr->findActorByWID(m_initActorObj));
	return laser;
}

ClientActorBlockLaser *ClientActorBlockLaser::getBlockLaserNext()
{
	if (!m_pWorld)
	{
		return nullptr;
	}
	if (!m_nextActorObj)
	{
		return NULL;
	}
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	if (!actorMgr) return nullptr;
	ClientActorBlockLaser* laser = dynamic_cast<ClientActorBlockLaser *>(actorMgr->findActorByWID(m_nextActorObj));
	return laser;	
}

void ClientActorBlockLaser::onImpactWithBlockInner(const WCoord *blockpos, int face)
{
	//对�?�方块的位置充能
	if (m_pWorld)
	{
		WCoord blockTarget;
		if (m_nBlockDir < 7 && m_bBlockShoot)
		{
			blockTarget =  NeighborCoord(*blockpos, ReverseDirection(m_nBlockDir));
			m_pWorld->setSpecialBlockPower(blockTarget, (m_nBlockDir<<4) + 15);
		}
		else
		{
			int blockdir = m_pWorld->getBlockData(*blockpos) & 7;
			/*if (blockdir == face)
			{
				blockTarget =  NeighborCoord(*blockpos, face);
				m_pWorld->setSpecialBlockPower(blockTarget, (ReverseDirection(face)<<4) + 15);
				face = (face + 1) & 7;
			}*/
			blockTarget =  NeighborCoord(*blockpos, face);
			m_pWorld->setSpecialBlockPower(blockTarget, (ReverseDirection(face)<<4) + 15);
		}
		if (lastSetPowerpos != blockTarget)
		{
			//doTrigger();			
			m_pWorld->setSpecialBlockPower(lastSetPowerpos, 0);
			//通知方块
			m_pWorld->notifyBlockSides(blockTarget, 0);
			m_pWorld->notifyBlockSides(lastSetPowerpos, 0);
			lastSetPowerpos = blockTarget;	
			if (getNextActorObj())
			{
				ClientActorBlockLaser *laser = getBlockLaserNext();
				if (laser)
				{
				    laser->setNeedClearEx();
					m_nextActorObj = 0;
				}
			}
		}
		else if (lastSetPowerpos == blockTarget && m_nShootCount%10 == 0)
		{
			//doTrigger();
			m_pWorld->notifyBlockSides(blockTarget, 0);
		}
		ProjectileLocoMotion *loc = static_cast<ProjectileLocoMotion *>(getLocoMotion());
		if (loc)
		{
			loc->m_InGround = false;
		}
		int blockid = m_pWorld->getBlockID(*blockpos);
		if (blockid == BLOCK_REFLECT_MIRROR && m_nShootCount > 5)
		{
			 int blockdata = m_pWorld->getBlockData(*blockpos);
			 BlockReflectMirror* blockMat = dynamic_cast<BlockReflectMirror*>(g_BlockMtlMgr.getMaterial(blockid));

			 if ((m_nPower - 1) <= -15)
			{
				return;
			}
			 if (blockMat)
			 blockMat->reflectLaser(m_pWorld, *blockpos, blockdata, this);
		}
	}
}

void ClientActorBlockLaser::onImpactWithBlock(const WCoord *blockpos, int face)
{ 
	//现在不会调用到这�?
	return;
}

void ClientActorBlockLaser::onCollideWithPlayer(ClientActor*player)
{

}

bool ClientActorBlockLaser::needCheckVisible()
{
	return false;
}

void ClientActorBlockLaser::update(float dtime)
{
	ClientActor::update(dtime);
	WorldPos pos = getLocoMotion()->getFramePosition();
	AddCurve();
}

void ClientActorBlockLaser::AddCurve()
{
	WCoord handpos;
	handpos = m_StartPos;
	MINIW::CatmullRomCurve cc;
	Rainbow::Vector3f p1(0.0f, 0.0f, 0.0f);
	Rainbow::Vector3f p2 = (handpos - getLocoMotion()->getPosition()).toVector3();
	if (p2.x == 0 && p2.y == 0 && p2.z == 0)
	{
		return;
	}
	if (g_pPlayerCtrl && g_pPlayerCtrl->getCamera())
	{
		Rainbow::Vector3f p0, p3;
		p0 = p1*2.0f - p2;
		//Rainbow::Vector3f dir0;

		Rainbow::Vector3f camlookdir = g_pPlayerCtrl->getCamera()->getLookDir();
		p3 = p2*2.0f - p1;

		cc.setControlPoints(p0, p1, p2, p3);
		cc.setNormals(camlookdir, camlookdir);

		int mtlId = 7;

		m_pWorld->getRender()->getCurveRender()->addCurve(mtlId, cc, getLocoMotion()->getPosition(), 6.0f, 3.0f);
	}
}

void ClientActorBlockLaser::resetSpeed()
{
	getLocoMotion()->m_Motion  = MINIW::Normalize(getLocoMotion()->m_Motion);
	if (m_ProjectileDef)
	{
		float speed = m_ProjectileDef->InitSpeed;
		getLocoMotion()->m_Motion *= speed;
	} 
}

void ClientActorBlockLaser::setPower(int power)
{
	if (m_nextActorObj && m_pWorld)
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		ClientActorBlockLaser* laser = dynamic_cast<ClientActorBlockLaser *>(actorMgr->findActorByWID(m_nextActorObj));
		if (laser && m_nPower != power)
		{   
		    laser->setNeedClearEx();
		}
	}
	if (power > m_nPower)
		resetSpeed();
	if (power != m_nPower)
	{
		m_nPower = power;
		m_pWorld->setSpecialBlockPower(lastSetPowerpos, 0);
		m_pWorld->notifyBlockSides(lastSetPowerpos, 0);
		lastSetPowerpos = WCoord(0, 0, 0);
	}
}

ClientActorBlockLaser *ClientActorBlockLaser::shootBlockLaserByBlock(World *pworld, int itemid, const WCoord &pos, int facedir, float speed, int power, const WCoord &blockPos, ClientActorBlockLaser *pre)
{
	WORLD_ID initActorObj = 0;

	
	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;

	if (pre)
	{
		initActorObj = pre->getObjId();
	}

	auto def = GetDefManagerProxy()->getProjectileDef(itemid, true);
	if (def == NULL) return NULL;

	ClientActorBlockLaser *laser = NULL;
	Rainbow::Vector3f dir;
	WCoord _pos = pos;
	if (facedir>=7 && pre)
	{
		Rainbow::Vector3f dirtemp;
		Rainbow::Quaternionf quat;
		// todo_yanxiongjian
		//quat.setEulerAngle((facedir - 7) * 22.5f, 0, 0);
		quat = AngleEulerToQuaternionf(Vector3f(0, (facedir - 7) * 22.5f, 0));

		Rainbow::Quaternionf quatlaser;
		pre->getLocoMotion()->ActorLocoMotion::getRotation(quatlaser);
		// todo_yanxiongjian
		Rainbow::Vector3f dir1 = quat.GetAxisZ();
		//Rainbow::Vector3f dir1;
		dir1  = MINIW::Normalize(dir1);
		Rainbow::Vector3f dir2 = pre->getLocoMotion()->m_Motion;
		dir2  = MINIW::Normalize(dir2);
		float facecos = DotProduct(dir1, -dir2);
		if (facecos != -1 && facecos != 1 && facecos>0.001)
		{
			//quatlaser.rotate(quat.GetAxisZ(), 180);
			quatlaser = AxisAngleToQuaternionf(quat.GetAxisZ(), Deg2Rad(180)) * quatlaser;
			dirtemp = quatlaser.GetAxisZ();
			dirtemp  = MINIW::Normalize(dirtemp);
			dir = dirtemp;
			//if (DotProduct(dir, dir1) > 0)
			{
				dirtemp *= (BLOCK_SIZE/2 + 10);
				_pos = pos + dirtemp;
			}
			//else
			//{
			//    return false;
			//}
			laser = SANDBOX_NEW(ClientActorBlockLaser, true, power, initActorObj, (WCoord &)blockPos, facedir);
			laser->init(itemid);
		}
		else
		{
			return NULL;
		}
		//pre->setNextActorObj(laser->getObjId());
	}
	else
	{
		laser = SANDBOX_NEW(ClientActorBlockLaser, true, power, initActorObj, (WCoord &)blockPos, facedir);
		laser->init(itemid);
		dir = g_DirectionCoord[facedir].toVector3();
	}

	actorMgr->spawnActor(laser, _pos, 0, 0);
	laser->m_StartPos = laser->getPosition();
	if (pre)
	{
		pre->setNextActorObj(laser->getObjId());
	}
	ProjectileLocoMotion *locomove = static_cast<ProjectileLocoMotion *>(laser->getLocoMotion());

	locomove->setThrowableHeading(dir, speed, 0);
	laser->playMotion(laser->m_ProjectileDef->TailEffect.c_str());
	laser->m_AttackPoints = laser->m_ProjectileDef->AttackValue;
	if (laser->m_ProjectileDef->TriggerCondition == 4)
	{
		laser->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触�?
	else if (laser->m_ProjectileDef->TriggerCondition == 5)
	{
		laser->m_ImpactTimeMark = -1;
	}
	return laser;
}

ClientActorBlockLaser *ClientActorBlockLaser::shootBlockLaserByLaser(World *pworld, int itemid, const WCoord &pos, const Rainbow::Vector3f &dir, float speed, int power, ClientActorBlockLaser *pre, WORLD_ID shootingActorID)
{
	WORLD_ID initActorObj = 0;

	if (!pworld) return NULL;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return NULL;

	if (pre)
	{
		initActorObj = pre->getObjId();
	}
	WCoord blockPos;
	ClientActorBlockLaser *laser = SANDBOX_NEW(ClientActorBlockLaser, false, power, initActorObj, blockPos, 0);
	laser->init(itemid);

	actorMgr->spawnActor(laser, pos, 0, 0);
	laser->m_StartPos = laser->getPosition();
	if (pre)
	{
		pre->setNextActorObj(laser->getObjId());
	}
	laser->m_ShootingActorID = shootingActorID;

	ProjectileLocoMotion *locomove = static_cast<ProjectileLocoMotion *>(laser->getLocoMotion());
	locomove->setThrowableHeading(dir, speed, 0);

	laser->playMotion(laser->m_ProjectileDef->TailEffect.c_str());
	laser->m_AttackPoints = laser->m_ProjectileDef->AttackValue;
	if (laser->m_ProjectileDef->TriggerCondition == 4)
	{
		laser->m_ImpactTimeMark = 0;
	}
	// TriggerCondition==5 不触�?
	else if (laser->m_ProjectileDef->TriggerCondition == 5)
	{
		laser->m_ImpactTimeMark = -1;
	}
	return laser;
}

void ClientActorBlockLaser::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);

	pworld->addSpecialUnit(this);
}

void ClientActorBlockLaser::leaveWorld(bool keep_inchunk)
{
	m_pWorld->removeSpecialUnit(this);

	ClientActor::leaveWorld(keep_inchunk);
}

void ClientActorBlockLaser::getViewBox(CollideAABB &box)
{
	if (m_nPower <= -15)
	{
		m_nPower = -14;
	}
	Rainbow::Vector3f motion = getLocoMotion()->m_Motion;
	motion  = MINIW::Normalize(motion);
	int maxlen = m_nPower * 100 + 1500;
	motion *= (float)maxlen;
	box.dim = WCoord(motion.x, motion.y, motion.z);
	box.pos = m_StartPos;
}

