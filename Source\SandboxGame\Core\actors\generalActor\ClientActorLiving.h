
#ifndef __CLIENTACTOR_LIVING_H__
#define __CLIENTACTOR_LIVING_H__

#include "IActorLiving.h"
#include "clientActor/ClientActor.h"
#include "OgreShared.h"

namespace Rainbow {
	namespace UILib {
		class ModelView;
	}
}
class AttackingTargetComponent;
class HPProgressComponent;
class FishingComponent;
class LightningChainComponent;
class MotionCtrlParam;
class LivingAttrib;
class ActorFollow;
class WorldContainer;
class ContainerManualEmitter;
class VacantVortexComponent;
class ClientAquaticComponent;
class TeamComponent;
struct AttackDef;
struct ToolDef;
class TemperatureComponent;
class RadiationComponent;

enum FUIMODELSPRITE_ATTACH_FROM
{
	FUIMODELSPRITE_ATTACH_FROM_NULL = 0,
	FUIMODELSPRITE_ATTACH_FROM_ROLEFRAME = 1,
};

class EXPORT_SANDBOXGAME ActorLiving;
class ActorLiving : public ClientActor,  public IActorLiving { //tolua_exports
//HPProgressCom---begin
	DECLARE_SCENEOBJECTCLASS_NOCREATE(ActorLiving)
private:
	using ModelView = Rainbow::UILib::ModelView;
public:
	void createEvent();
	//tolua_begin
	ATTACK_TYPE getLastAttackedType();
	int getHurtType();
	virtual void addHPEffect(float hp)override;
	virtual void addArmorEffect(float hp) override;
	void setArmorProgressDirty();
	virtual void breakInvisible() {}	// 20210910：打断隐身  codeby： keguanqiang
	virtual bool isInvisible() { return false; }	// 20210910：是否隐身  codeby： keguanqiang
	virtual void setHPProgressDirty(); 	// 20210910：血条设置需要更新  codeby： keguanqiang
	//tolua_end

	virtual void setMoveForward(float speed) {}				// 向前移动，>0:向前移动，<0:向后移动
	virtual void setMoveStrafing(float speed) {}				// 横向移动，>0:向右移动，<0:向左移动
	virtual void onMotionCtrlOver(const MotionCtrlParam& param) {}	// 用于行动结束回调(这里使用虚函数的方式而非注册函数)
	virtual void walkForward(int param, int exid = 0) {}				// 向前移动多少格，<0:保持向后移动
	virtual void walkStrafing(int param, int exid = 0) {}			// 向右移动多少格，<0:保持向左移动
	virtual void walkUp(int param, int exid = 0) {}					// 锁定镜头向前移动多少格，<0:保持向后移动
	virtual void walkRight(int param, int exid = 0) {}				// 锁定镜头向右移动多少格，<0:保持向左移动

	virtual void livingHPtick();
	virtual void mobHPTick();

	virtual void DoRepairBlock(const WCoord& blockpos);
	virtual bool DoHurtBlock(const WCoord& blockpos, ATTACK_TYPE attacktype, float attackPoints, bool& isDestroy);
	
protected:
	void armorTick();
	
	
	virtual HPProgressComponent* getHPProgressComponent();
//HPProgressCom---end

//TeamComponent--begin
public:
	//tolua_begin
	void setTeam(int id ,bool ResetAttr = true);
	virtual int getTeam() override;
	bool isSameTeam(ActorLiving *target); //同一边,  不应该攻击
	void addGameScoreByRule(int ruleid, int num = 1); //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int

	void setSunHurt(bool sunHurt);
	bool getSunHurt();
	virtual int getTeamType(ActorLiving* target); //获取队伍关系类型
	void setDieInDay(bool dieInDay, int deathAnimId = -1);
	//tolua_end
	void bindHPProgress(HPProgressComponent *pComponent);
#pragma region MyRegion
	virtual int GetTeamType(IActorLiving* target) override;
#pragma endregion
protected:
	HPProgressComponent	*m_pHPProgress;
    TeamComponent* m_pTeamComponent;
//TeamComponent--end

public:
	//tolua_begin
	ActorLiving();

	virtual void enterWorld(World* pworld);
	virtual void leaveWorld(bool keep_inchunk);
	virtual void tick();
	void attackTick();

	virtual void update(float dtime);
	virtual bool canBePushed();
	virtual bool preventActorSpawning()
	{
		return true;
	}
	virtual bool needWalkEffects()
	{
		return true;
	}
	virtual int getStepHeight()
	{
		return BLOCK_SIZE/2;
	}

	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *actor);
	virtual void playHurtSound();
	virtual void playDeathSound();
	virtual void playStepSound();
	virtual void playClimbStepSound();
	virtual void playAttackSound();
	virtual float getSoundVolume();
	virtual float getSoundPitch();
	virtual bool canBeCollidedWith()
	{
		//return !isDead();
		return true;
	}

	virtual void onBuffChange(int chgtype, int buffid, int bufflv, int buffticks, int buffInstanceId=0); //chgtype=0:add, 1:remove, 2:clear all
	virtual bool isBurning() override;
	virtual void setFire(int buffid, int bufflv, int ticks = -1, long long fromObjid = 0);
	virtual ActorBody *newActorBody() = 0;//创建的对象 

	ClientActor *getAtkingTarget();
	int getAtkingTimer();
	void setAtkingTarget(ClientActor *pActor, int targetIndex = 1);
	int getAttackAnimTicks(ATTACK_TYPE attacktype);
	bool isAttacking();

	bool canActorBeSeen(ClientActor *actor);
	bool isActorInLookDir(ClientActor *actor, float fov, bool ignore_y=false);
	virtual bool canAttack();
	virtual bool attackActor(ClientActor *target, int seq = 2, int targetIndex = 1); //916冒险 2021/08/18 codeby:wudeshen
	virtual void moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks);

	virtual void playAnim(int seq, bool include_me = false, int seq1=127, int isLoop = -1);
	virtual void stopAnim(int seq, bool include_me = false);
	virtual bool castShadow() override;
	virtual int getOxygenUseInterval();
	virtual float getOxygenUseRate(); //0-1.0f,  0完全不消耗氧气
	virtual WCoord getEyePosition();
	virtual void setScale(float s);	//设置模型大小
	virtual float getScale();
	virtual void updateScale();
	virtual void setCustomScale(float scale);
	virtual float getCustomScale()
	{
		return m_CustomScale;
	}

	LivingAttrib* getLivingAttrib();
	
	int GetAttchUIFrom();
	void attachUIModelView(ModelView *modelview, int index=0, int attachFrom=FUIMODELSPRITE_ATTACH_FROM_NULL);
	void detachUIModelView(int index=0);

	ActorBody *getUIViewBody();
	virtual bool playActForTrigger(int act); // 播放动画
	bool playActForTriggerForActorbody(int act);

	bool isApplyOxyPack();
	virtual void doActualItemSkillAttack(ClientActor *target, ATTACK_TYPE type, int demage, int touReduce = -1);

	
	bool isMineTool(int blockid, const ToolDef *tooldef, float maxhardness = Rainbow::MAX_FLOAT);
	//tolua_end

	int getMineBlockTicks(int toolid, int blockid, int blockdata, BLOCK_MINE_TYPE* minetype_ret = NULL);

	LightningChainComponent* m_LightningChainComponent = nullptr;
	LightningChainComponent* getLightningChainComponent();

	FishingComponent* m_pFishingComponent = nullptr;
	FishingComponent* getFishingComponent();
	void bindFishingComponent(MNSandbox::SceneComponent* pComponent);

	virtual VacantVortexComponent* getVacantVortexComponent() { return nullptr; }

	//tolua_begin
	virtual int  getAnimBodyId() override { return m_nAnimBodyId; }
	void setAnimBodyId(int animBodyId) { m_nAnimBodyId = animBodyId; }
	//tolua_end

	//tolua_begin
	virtual void setRidingActor(ClientActor *p);

	//bool checkCanAddFollow(WORLD_ID objId, int dist, int timeOut, WORLD_ID curTargetId);
	//bool checkContinueFollow(WORLD_ID objId);
	//bool isEquipGarland();

	ModelView *getAttachedModelView()
	{
		return m_UIModelView;
	}
	int getAttachedModelViewIndex()
	{
		return m_ModelViewIndex;
	}

	// 防御状态相关接口
	virtual bool IsInDefanceState() { return m_isInDefanceState; }
	void setInDefanceState(bool isDefanceState);

	// 设置强制位移效果
	void SetForcedMove(float knockback, float knockup, ClientActor* actor);

	//tolua_end
	virtual void getsyncData(jsonxx::Object &data);
	virtual void setsyncData(jsonxx::Object &data);

	virtual ToAttackTargetComponent* getToAttackTargetComponent() override {
		return m_pToAttackTargetComponent;
	}
	void cacheToAttackTargetComponent(ToAttackTargetComponent* pToAttackTargetComponent)
	{
		m_pToAttackTargetComponent = pToAttackTargetComponent;
	}

	virtual TemperatureComponent* getTemperatureComponent() override {
		return m_pTemperatureComponent;
	}
	void cacheTemperatureComponent(TemperatureComponent* pTemperatureComponent)
	{
		m_pTemperatureComponent = pTemperatureComponent;
	}

	virtual RadiationComponent* getRadiationComponent() override {
		return m_pRadiationComponent;
	}
	void cacheRadiationComponent(RadiationComponent* pRadiationComponent)
	{
		m_pRadiationComponent = pRadiationComponent;
	}

	virtual BlockEnvEffectsComponent* getBlockEnvEffectsComponent() override {
		return m_pBlockEnvEffectsComponent;
	}
	void cacheBlockEnvEffectsComponent(BlockEnvEffectsComponent* pBlockEnvEffectsComponent)
	{
		m_pBlockEnvEffectsComponent = pBlockEnvEffectsComponent;
	}

	virtual ClientAquaticComponent* getClientAquaticComponent() { return nullptr; }

	/**
		获取ActorFollow组件
	*/
	ActorFollow* getActorFollow();

	AttackingTargetComponent* m_pAttackingTargetComponent = nullptr;
	virtual AttackingTargetComponent* getAttackingTargetComponent() { return m_pAttackingTargetComponent; }

protected:
	virtual ~ActorLiving();
	virtual void doActualAttack(ClientActor *target, int targetIndex = 1) = 0;
	virtual void doActualRangeAttack(ClientActor *target) = 0;
	virtual float getUIModelViewScale()
	{
		return 1.0f;
	}
	virtual int getBufferId()
	{
		return 0;
	}
	virtual bool checkGameRule(ActorLiving*) { return true; }

	void NotifyPlayAnim2Tracking(int anim, bool include_me = false, int anim1=127, int isLoop = 0);
	void NotifyStopAnim2Tracking(int anim, bool include_me = false);

	friend class AttackingTargetComponent;
	
protected:
	//AttackingTargetComponent* m_pAttackingTargetCom;
	ActorBody*                m_UIViewBody;
	//ServerInterpolTick*       m_pServerInterpolTick;
	/**
		组件ActorFollow
	*/
	ActorFollow*              m_pActorFollow;
	ToAttackTargetComponent* m_pToAttackTargetComponent = nullptr;
	TemperatureComponent* m_pTemperatureComponent = nullptr;
	BlockEnvEffectsComponent* m_pBlockEnvEffectsComponent = nullptr;
	RadiationComponent* m_pRadiationComponent = nullptr;

	float m_CustomScale;

	//将ActorBody与ModelView的关联信息提到这一层
	ModelView *m_UIModelView;
	int m_ModelViewIndex;
	int m_attchUIFrom;
	int m_nAnimBodyId;

	bool m_isInDefanceState;		// 是否在防御状态

public:
	bool ComboAttackCalculate{ false }; //是否强制进入连击状态的攻击结算
	int ComboAttackDefNum{ 0 }; //当前连击总数
	AttackDef* dataAttackDef{ nullptr }; //连击数据
	virtual bool ComboAttackStateRunning() ;

}; //tolua_exports

#endif
