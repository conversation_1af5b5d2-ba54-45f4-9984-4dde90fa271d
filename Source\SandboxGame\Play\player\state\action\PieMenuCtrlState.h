﻿#pragma once
#include "PlayerState.h"
//tolua_begin
enum MenuCtrlType
{
	MenuCtrlType_NONE,
	MenuCtrlType_SOCLOCKCTRL,
	MenuCtrlType_BUILDING_PLANNER_CTRL,//放建筑的选择
	MenuCtrlType_WATER_STORAGE_CTRL,//蓄水选择
	MenuCtrlType_BUILDING_MODIFY_CTRL,//建筑升级和拆除
};
//tolua_end

class PieMenuCtrlState :public PlayerState
{
public:
	PieMenuCtrlState(PlayerControl* host);
	virtual ~PieMenuCtrlState();
	virtual void doBeforeEntering() override;
	virtual std::string update(float dtime) override;
	virtual void doBeforeLeaving() override;
	virtual void OnTick(float elapse) final;

    bool OpenPieMenu();
    bool ClosePieMenu();

private:
	bool IsSupportedBlockType(const std::string& blockType);
    bool m_isPieMenuOpen;
	int eventid;
};