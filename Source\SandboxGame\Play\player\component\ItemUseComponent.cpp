#include "ItemUseComponent.h"
#include "GunUseComponent.h"
#include "CustomGunUseComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "SandboxGameDef.h"
#include "PlayerAttrib.h"
#include "defdata.h"
#include "GunGridDataComponent.h"
#include "SandboxGameDef.h"
#include "PlayerControl.h"
#include "GameCamera.h"
#include "SandBoxManager.h"
#include "SandboxEventDispatcherManager.h"
#include "PermitsDef.h"
#include "EntryGridDataComponent.h"
#include "ModEntryMgr.h"
#include "WorldManager.h"
#include "CameraModel.h"
#include "backpack.h"
#include "ActorBody.h"

using namespace MNSandbox;

IMPLEMENT_COMPONENTCLASS(ItemUseComponent)
ItemUseComponent::ItemUseComponent()
{
	m_pOwnerPlayer = NULL;
	m_destroyBlockPermit = false;
	m_hasInitDestroyBlockPermit = false;
}

ItemUseComponent::~ItemUseComponent()
{
}

void ItemUseComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_pOwnerPlayer = dynamic_cast<ClientPlayer*>(owner);
	if (m_pOwnerPlayer) {
		m_pOwnerPlayer->BindItemComponent(this);
	}
	Super::OnEnterOwner(owner);
	BindOnTick();
}
void ItemUseComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	UnBindOnTick();
	if (m_pOwnerPlayer) {
		m_pOwnerPlayer->BindItemComponent(nullptr);
	}
	m_pOwnerPlayer = nullptr;
	//m_lastGunData = nullptr;
	Super::OnLeaveOwner(owner);
}
void ItemUseComponent::OnTick()
{
	
}

void ItemUseComponent::OnUpdate(float dt)
{

}

void ItemUseComponent::onSetCurShortcut(int i)
{
	if (!m_pOwnerPlayer)
	{
		return;
	}

	int itemid = m_pOwnerPlayer->getCurToolID();
	const CustomGunDef* customGundef = GetDefManagerProxy()->getCustomGunDef(itemid);

	/*if (m_lastGunData && !m_refreshGunOnHandle)
	{
		DoGunModEntry(m_lastGunData, false);
	}*/

	PlayerAttrib* attr = m_pOwnerPlayer->getPlayerAttrib();
	BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
	GunGridDataComponent* gunData = dynamic_cast<GunGridDataComponent*>(itemgrid->getGunDataComponent());

	bool makerMode = GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode();

	if (m_pOwnerPlayer == g_pPlayerCtrl)
	{
		//新枪械
		if (customGundef && (gunData || (!gunData && makerMode)))
		{
			GunUseComponent* comp = m_pOwnerPlayer->getGunComponent();
			comp->setGunDef(NULL);
			comp->setIsFire(false);
			comp->setIsReload(false);
			comp->reset();

			m_pOwnerPlayer->m_GunHoleState = GunHoldState::CUSTOMGUN;
			CustomGunUseComponent* newGunComp = m_pOwnerPlayer->sureCustomGunComponent();
			if (newGunComp)
			{
				if (!gunData)
				{
					if (makerMode)
					{
						//这里表示从资源背包放置进快捷栏
						gunData = dynamic_cast<GunGridDataComponent*>(itemgrid->addGunDataComponent());
						gunData->setGunId(itemid);
						gunData->GenModEntryByMade();
						gunData->RandomParam();
					}
				}
				newGunComp->Init(gunData);
				//m_lastGunData = gunData;
				DoGunModEntry(gunData, true);

				if (g_pPlayerCtrl == m_pOwnerPlayer)
				{
					if (g_pPlayerCtrl->getViewMode() == CAMERA_FPS)
					{
						//第一人称不做修改
					}
					else if (g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK || g_pPlayerCtrl->getViewMode() == CAMERA_TPS_BACK_SHOULDER)
					{
						//第三人称不做修改
					}
					else
					{
						//其他视角 切换至第一人称
						g_pPlayerCtrl->setViewMode(CAMERA_FPS);
					}
					if (g_pPlayerCtrl->m_CameraModel)
					{
						//之后会加载手中的枪，设置一个标志，后面播动画用
						g_pPlayerCtrl->m_CameraModel->SetWeaponModelIsLoading(true);
					}
				}
				else
				{
					//其他player切枪，IDLE 检测
					ActorBody* body = m_pOwnerPlayer->getBody();
					if (body)
					{
						body->setCurAnim(SEQ_GUN_IDLE, 1);
					}
				}
			}
		}
		else
		{
			//m_lastGunData = nullptr;
			CustomGunUseComponent* newGunComp = m_pOwnerPlayer->getCustomGunComponent();
			if (newGunComp)
			{
				newGunComp->Init(NULL);
			}
			else
			{
				m_pOwnerPlayer->clearGunUseUpdate();
			}

			//老枪械
			const GunDef* def = GetDefManagerProxy()->getGunDef(itemid);
			GunUseComponent* comp = m_pOwnerPlayer->getGunComponent();
			if (def != NULL)
			{
				m_pOwnerPlayer->m_GunHoleState = GunHoldState::GUN;
				PlayerAttrib* attr = m_pOwnerPlayer->getPlayerAttrib();
				BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
				comp->setMagazine(itemgrid->getUserDataInt());
				comp->setGunDef(def);
				comp->setZoom(false);
				comp->setUpdate();
			}
			else
			{
				m_pOwnerPlayer->m_GunHoleState = GunHoldState::NOGUN;
				comp->setGunDef(NULL);
				comp->setIsFire(false);
				comp->setIsReload(false);
				comp->reset();
			}
		}
	}


	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerMode())
	{
		//代码 by yangjia
		const ItemEquipDef* itemEquipDef = GetDefManagerProxy()->getEquipDef(itemid);
		if (itemEquipDef)
		{
			PlayerAttrib* attr = m_pOwnerPlayer->getPlayerAttrib();
			BackPackGrid* itemgrid = attr->getEquipGrid(EQUIP_WEAPON);
			EntryGridDataComponent* equipData = dynamic_cast<EntryGridDataComponent*>(itemgrid->getEntryDataComponent());
			if (!equipData)
			{
				equipData = dynamic_cast<EntryGridDataComponent*>(itemgrid->addEntryDataComponent());
				equipData->setEquipId(itemid);
				equipData->GenModEntryByMade();
			}
		}
	}
}

bool ItemUseComponent::canDestroyBlock()
{
	if (m_hasInitDestroyBlockPermit)
		return m_destroyBlockPermit;

	m_destroyBlockPermit = false;
	m_hasInitDestroyBlockPermit = true;

	SandboxResult resultCanCSPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_pOwnerPlayer->getUin())
		.SetData_Number("blockid", 0)
		.SetData_Number("bit", CS_PERMIT_DESTROY_BLOCK));
	if (resultCanCSPermit.IsExecSuccessed())
	{
		m_destroyBlockPermit = resultCanCSPermit.GetData_Bool();
		if (!m_destroyBlockPermit)
			return false;
	}

	m_destroyBlockPermit = false;
	SandboxResult resultCanPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
		SandboxContext(nullptr)
		.SetData_Number("uin", m_pOwnerPlayer->getUin())
		.SetData_Number("blockid", 0)
		.SetData_Number("bit", CS_PERMIT_DESTROY_BLOCK));
	if (resultCanPermit.IsExecSuccessed())
	{
		m_destroyBlockPermit = resultCanPermit.GetData_Bool();
	}

	return m_destroyBlockPermit;
}

void ItemUseComponent::DoGunModEntry(GunGridDataComponent* gunData, bool enable)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isRemote()) //客机不需要执行
	{
		return;
	}

	if (!m_pOwnerPlayer || !gunData) return;

	std::vector<ModEntryCreate> entrys;
	gunData->GetModEntrys(entrys);
	for (const auto& item : entrys)
	{
		GetModEntryMgr().SetModEntryTrig(enable, m_pOwnerPlayer->getObjId(), item.entryID);
	}

	PlayerAttrib* attr = m_pOwnerPlayer->getPlayerAttrib();
	if (!attr) return;
	int itemid = gunData->getGunId();
	if (enable)
	{
		attr->addEquipEntryBuff(itemid, 0);
	}
	else
	{
		attr->removeEquipEntryBuff(itemid);
	}
}

void ItemUseComponent::DoCurShotGunModEntry(bool enable)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isRemote()) //客机不需要执行
	{
		return;
	}

	if (!m_pOwnerPlayer)
	{
		return;
	}

	int shortcutIndex = m_pOwnerPlayer->getCurShortcut() + m_pOwnerPlayer->getShortcutStartIndex();
	BackPack* pack = m_pOwnerPlayer->getBackPack();
	if (!pack)
	{
		return;
	}
	BackPackGrid* grid = pack->index2Grid(shortcutIndex);
	if (!grid)
	{
		return;
	}

	GunGridDataComponent* comp = dynamic_cast<GunGridDataComponent*>(grid->getGunDataComponent());
	if (!comp)
	{
		return;
	}

	DoGunModEntry(comp, enable);

	return;
}