#include "MpGameSuviveNetHandler.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "WorldManager.h"
#include "WorldMeta.h"
#include "GameEvent.h"
#include "GameEvent.h"
#include "VehicleWorld.h"
#include "MpActorManager.h"
#include "GameNetManager.h"
#include "backpack.h"
#include "container.h"
#include "DefManagerProxy.h"
#include "PlayerControl.h"
#include "ClientActorManager.h"
#include "container_computer.h"
#include "OgreTimer.h"
#include "OgreScriptLuaVM.h"
#include "OgreUtils.h"
#include "GameCamera.h"

#include "LivingLocoMotion.h"
#include "ActorBody.h"
#include "ClientMob.h"
#include "MpActorTrackerEntry.h"

#include "container_signs.h"
#include "special_blockid.h"

#include "world.h"
#include "ActorTrainCar.h"
#include "ActorRocket.h"

#include "container_buildblueprint.h"
#include "VehicleMgr.h"
#include "CustomModelMgr.h"
#include "FullyCustomModelMgr.h"
#include "ObserverEventManager.h"

#include "VehicleContainerActioner.h"
#include "PermitsDef.h"

#include "PCControl.h"
#include "TouchControl.h"
#include "container_sensor.h"
#include "container_tombstone.h"
#include "container_altar.h"
#include "GameMode.h"
#include "RoomSyncResMgr.h"
#include "ActorCSProto.h"
#include "ActorVehicleAssemble.h"
#include "SandboxGFunc.h"

#include "SandboxPlayerObject.h"
#include "SandboxPlayersRoot.h"

#include "FurnaceContainer.h"
#include "RuneDef.h"
#include "container_pot.h"
#include "StarStationTransferMgr.h"
#include "container_starstationtransfercabin.h"
#include "SandBoxManager.h"

#include "RoomClient.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientFlyMob.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "PlayerAttrib.h"
#include "ZmqProxy.h"
#include "OgreStringUtil.h"
#include "FishingComponent.h"

#include "MpGameSurvive.h"
#include "ClientActorFuncWrapper.h"
#include "FallComponent.h"
#include "EffectComponent.h"
#include "SoundComponent.h"
#include "RiddenComponent.h"
#include "component/GunUseComponent.h"
#include "LockCtrlComponent.h"
#include "SocRevivePointComponent.h"

#include "container_decomposition.h"

#include "DigState.h"
#include "AttackBlockState.h"
#include "ExploitState.h"
#include "FootballStateAction.h"
#include "BasketballStateAction.h"
#include "PushSnowBallStateAction.h"
#include "AdvancedDigState.h"
#include "GameInfoProxy.h"
#include "ClientInfoProxy.h"
#include "ClientErrCode.h"
#include "SprayPaintMgr.h"
#include "PlayerCheat.h"
#include "ThornBallComponent.h"
#include "ICloudProxy.h"
#include "cloud/ActionLogger.h"
#include <regex>
#ifdef IWORLD_SERVER_BUILD
#include "cloud/ActionLoggerClient.h"
#include "cloud/ServerPerf.h"
#include "ClientGameStandaloneServer.h"
#include "cloud/BigDataTraining.h"
#include "ActionLoggerBigData.h"
#include "cloud/MapConfig.h"
#include "cloud/TeleportRegister.h"
#include "cloud/CloudServerActivity.h"
#include "ActionLoggerBigData.h"
#include "cloud/MapConfig.h"
#include "DataHubService.h"
#include "ClientGameManager.h"
#include "MiniReportMgr.h"
#include "ServerConfig.h"
#endif

#include "Platforms/PlatformInterface.h"
#include "SandboxGlobalNotify.h"
#include "PlayerLocoMotion.h"
#include "minisystem/base/Plugin.h"
#include "CarryComponent.h"

#include "navigationpath.h"
#include "EffectManager.h"
#include <time.h>

#include "ClientGameManager.h"
#include "Misc/FrameTimeManager.h"
#include "CustomModelPacking.h"
#include "MoveControl.h"
#ifdef __PC_LINUX__
#include <sys/time.h>
#endif
#include "AttackJsonManager.h"

#include "ContainerStove.h"
#include "component/CustomGunUseComponent.h"

#include "IWorldConfig.h"
#include "ClientGameManager.h"
#include "Network/HttpManager.h"
#include "ClientUrl.h"
// #include "MeteorShowerManager.h"

#define  OFFLINE_MODE_ENABLED 1

using namespace Rainbow;
using namespace MINIW;
using namespace MNSandbox;

#define MPGAME_HOST_DEBUG_LOG 1
#if MPGAME_HOST_DEBUG_LOG
#define MPGAME_HOST_LOG(...) \
    do { \
        WarningStringMsg("[MpGameSurviveHostHandler] " __VA_ARGS__); \
    } while(0)
#else
#define MPGAME_HOST_LOG(...)
#endif

std::string _GetWordWallReqUrl(const char* content, const std::string& desUinList, int sendUin, const std::string& s2_, const std::string& pure_s2t_, const std::string& addParamStr)
{
	std::string httpCheckString = ClientUrl::GetUrlString("HttpCheckString");
	int env = GetClientInfoProxy()->getGameData("game_env");
	int time = (int)MINIW::GetTimeStamp();
#ifndef IWORLD_SERVER_BUILD
	MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &time);
#endif

	std::ostringstream ssurl;
	ssurl << httpCheckString.c_str() << "miniw/wordwall?act=checktxt2";
	ssurl << "&key=" << Http::CurlUrlEncode(content).c_str();
	ssurl << "&time=" << time;
	ssurl << "&env=" << env;
	ssurl << "&token=" << 0;
	ssurl << "&source=" << 1;
	ssurl << "&function=" << "in_map_chat";
	ssurl << "&behalf_check=" << (GetClientInfoProxy()->getUin() == sendUin ? 0 : 1);
	if (desUinList.size() > 0)
		ssurl << "&des_uin=" << desUinList;

	if (s2_.size() > 0 && pure_s2t_.size() > 0)
	{
		std::ostringstream sstk;
		sstk << time << s2_.c_str() << sendUin;

		ssurl << "&s2t=" << pure_s2t_.c_str();
		ssurl << "&auth=" << gFunc_getmd5(sstk.str());
	}

	std::string url = ssurl.str();
	if (addParamStr.length() > 0)
	{
		url.append("&");
		url.append(addParamStr);
	}

	auto L = MINIW::ScriptVM::game()->getLuaState();
	if (ScriptVM::isCurrentThreadIsMainThread(L))
	{
		//add param
		int top = lua_gettop(L);

		//encode s7
		lua_getglobal(L, "g_ns_http_sec_encodeS7Url_V2");
		if (lua_isfunction(L, -1))
		{
			lua_pushstring(L, url.c_str());
			if (0 == lua_pcall(L, 1, 1, 0))// 调用函数，1个参数，1个返回值
			{
				if (lua_isstring(L, -1)) {
					// 获取字符串及其长度
					size_t len;
					const char* luaStr = lua_tolstring(L, -1, &len);
					url = luaStr;
				}
			}
		}
		lua_settop(L, top);
	}

	return url;
}


namespace ConnectionCheck
{
	extern void SetUinConnStat(int uin, int st);
};

/**
 * @brief 判断使用物品技能是否有作弊嫌疑
 * 
 * @param player 
 * @param itemIndex 道具所在位置
 * @param clientItemId 客户端上传的使用物品ID
 * @param cheatKey 判断作弊时记录作弊的事件
 * @param skillid 使用的物品技能
 * @return true 有作弊嫌疑
 */
bool CheckItemCheat(ClientPlayer* player, int itemIndex, int clientItemId, const std::string& cheatKey, int skillid = 0)
{
	// 增加道具验证  codeby:liusijia
	int itemId = player->getBackPack()->getGridItem(itemIndex);
	bool cheat = false;
	if (itemId != clientItemId)
	{
		cheat = true;
	}
	else if (skillid)
	{
		auto def = GetDefManagerProxy()->getItemDef(itemId);
		if (!def)
		{
            cheat = true;
        }
		else
		{
			cheat = true;
			for (auto iter=def->SkillID.begin(); iter!=def->SkillID.end(); ++iter)
			{
				if (*iter == skillid)
                {
                    cheat = false;
                    break;
                }
			}
		}
	}
	if (cheat)
	{
#ifdef IWORLD_SERVER_BUILD
		jsonxx::Object cheat;
		cheat << "client_index" << itemIndex;
		cheat << "client_itemid" << clientItemId;
		cheat << "server_itemid" << itemId;
		cheat << "client_skillid" << skillid;
		ActionLogger::ErrorLog(player->getUin(), player->getOWID(), cheatKey, cheat);
#endif		
		return true;
	}
	return false;
}


void MpGameSurviveNetHandler::onHostAddPartner(int uin)
{
	//刚进入的玩家心跳检测延长120秒，避免下载慢被踢  （此处是同步线上版本延长心跳检测时间）
	GetGameNetManagerPtr()->m_HostRecvHeartBeart[uin] = Rainbow::Timer::getSystemTick() + 120000;
	GameNetHostMsgHandler::onHostAddPartner(uin);

#ifdef IWORLD_SERVER_BUILD
	// 客户端首次连接
	PB_RoomExtraInfoHC info;
	// std::string extra = GetRoomManager().getCloudServerRoomExtraData();
    std::string extra;
    MNSandbox::GetGlobalEvent().Emit<std::string &>("RoomManager_getCloudServerRoomExtraData", extra);
	
	info.set_room_extra(extra);

	std::string cmurl, mapmd5;
	long long mapID;
	if (ClientGameManager::getInstance()->getCMURL(cmurl, mapmd5, mapID))
	{
		info.set_cmurl(cmurl);
		info.set_mapmd5(mapmd5);
		info.set_mapid(mapID);
	}

	GameNetManager::getInstance()->sendToClient(uin, PB_SYNC_ROOM_EXTRA_HC, info);
	GetICloudProxyPtr()->SimpleSLOG("3.onHostAddPartner send RoomExtraData %d ", uin);
#endif
}

void MpGameSurviveNetHandler::onHostConnectFailed()
{
	GameNetHostMsgHandler::onHostConnectFailed();
}

void MpGameSurviveNetHandler::onHostConnectionLost(int uin)
{
	if (uin <= 0) return;

	GameNetHostMsgHandler::onHostConnectionLost(uin);

#ifdef IWORLD_SERVER_BUILD
	ActionLoggerBigData::getInstance()->LogServerPlayerLeave(uin, 1);
#endif

	onPlayerLeave(uin);
}

static void SendResetPosition2Client(ClientPlayer *player, float yaw, float pitch)
{
	if (player->getWorld() == NULL) return;
	MpActorTrackerEntry *entry = player->getWorld()->getMpActorMgr()->getTrackerEntry(player->getObjId());
	if (entry == NULL) return;

	entry->sendActorMovementToClient(player->getUin(), player, yaw, pitch);
}

bool checkErrorNickName(const char* nickName)
{
	bool error = true;
	int len = strlen(nickName);
	for (int i = 0; i < len; i++)
	{
		if (nickName[i] != ' ' && nickName[i] != '	')
		{
			error = false;
			break;
		}
	}
	return error;
}

ClientPlayer *MpGameSurviveNetHandler::checkPlayerByMsg2Host(int uin, bool ignoreDead/* =false */)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return NULL;
	}
	if (!ignoreDead && player->isDead()) 
	{
		return NULL;
	}
	else if (player->isInSpectatorMode() && player->getSpectatorType() != SPECTATOR_TYPE_FOLLW)
	{
		return NULL;
	}
	else return player;
}

ClientPlayer* MpGameSurviveNetHandler::checkDownedPlayerByMsg2Host(int uin)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return NULL;
	}

	// 检查玩家是否死亡
	if (player->isDead())
	{
		return NULL;
	}
	// 检查玩家是否处于倒地状态
	PlayerAttrib* playerAttrib = dynamic_cast<PlayerAttrib*>(player->getAttrib());
	if (playerAttrib && playerAttrib->isPlayerDowned())
	{
		// 向客户端发送倒地状态错误码
		//sendError2Client(uin, PB_ERROR_PLAYER_DOWNED);
		return NULL;
	}
	else if (player->isInSpectatorMode() && player->getSpectatorType() != SPECTATOR_TYPE_FOLLW)
	{
		return NULL;
	}
	else return player;
}

void MpGameSurviveNetHandler::RoleEnterWorld2HostLast(int uin, ClientPlayer *player)
{
	MPGAME_HOST_LOG("RoleEnterWorld2HostLast uin:%d", uin);
	WorldManager *worldMgr = m_root->getWorldMgr();
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (!mp || !worldMgr)
	{
		MPGAME_HOST_LOG("RoleEnterWorld2HostLast failed, mp or worldMgr is null");
		return;
	}
	PlayerBriefInfo *roleinfo = mp->findPlayerInfoByUin(uin);
	if (!roleinfo)
	{
		MPGAME_HOST_LOG("RoleEnterWorld2HostLast failed, roleinfo is null");
		return;
	}
	RoomClient * room = GameNetManager::getInstance()->getRoomClient();
	if (room)
	{
		// 检查新加入的用户是否合法
		MPGAME_HOST_LOG("RoleEnterWorld2HostLast room is not null");
		room->reqCheckMemberExist(GameNetManager::getInstance()->getMyUin(), uin);
	}

	if (player)
	{
		mp->checkGameLeader(uin, ROOM_ACTION_ENTER);
		player->setUIControlMode(roleinfo->UICtrlMode);
		player->setLang(roleinfo->lang);
		player->setApiid(roleinfo->apiid);
		player->setEnterTs(time(0));
		//GetClientInfoProxy()->setApiid(roleinfo->apiid);
		//GetClientInfoProxy()->setLang(roleinfo->lang);


		//触发器创建的特效/音效同步给客机
		if (worldMgr->isGameMakerRunMode())
		{
			MPGAME_HOST_LOG("syncEffect2Player uin:%d", uin);
			player->getWorld()->syncEffect2Player(uin);
		}

		//等级经验信息发送给客机
		player->syncLevelMode();
		MPGAME_HOST_LOG("syncLevelMode uin:%d", uin);
		player->m_AccoutSkinID = roleinfo->skinid;

		//同步复活点给客机
		//player->syncRevivePoint(uin);
		SandboxContext context;
		context.SetData_Number("uin", uin);
		player->Event().Emit("revive_syncRevivePoint", context);
		
		if (player->getWorld())
		{	
			//同步QQ播放器
			MPGAME_HOST_LOG("syncQQPlayer uin:%d", uin);
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("Player_Enter_CH", SandboxContext(nullptr).SetData_Number("uin", double(uin)));
			//同步长音效给客机
			MPGAME_HOST_LOG("syncLongSounds2Player uin:%d", uin);
			player->getWorld()->syncLongSounds2Player(uin);
		}
		//同步音乐方块
		//worldMgr->syncMusicClub(uin);

		MPGAME_HOST_LOG("syncPlayerEnter uin:%d", uin);
		worldMgr->syncPlayerEnter(uin);
#ifdef IWORLD_SERVER_BUILD
		DataHubService::GetInstance().OnPlayerEnter(uin);
		GetICloudProxyPtr()->onPlayerEnter(player);
#endif
	}

#ifdef IWORLD_SERVER_BUILD

	//g_ClientMgr  GetClientInfoProxy()->
	jsonxx::Object logger_json;
	logger_json << "player_start_time" << roleinfo->enterTime;
	logger_json << "map_type" << GetClientInfoProxy()->getEnterParam("maptag");
	logger_json << "cloud_area" << GetIWorldConfigProxy()->getGameData("game_env");
	logger_json << "map_cap" << GetClientInfoProxy()->getCurGame()->getMaxPlayerNum();
	logger_json << "node_ip" << GetClientInfoProxy()->getEnterParam("ip");
	if (roleinfo->game_session_id[0])
		logger_json << "game_session_id" << roleinfo->game_session_id;
	if (room)
		logger_json << "stay" << (room->isStayMember(uin) ? 1 : 0);
	// OWORLD* desc = OWorldList::GetInstance().findWorldDesc(worldMgr->getWorldId());
	WorldDesc* desc = GetClientInfoProxy()->findWorldDesc(worldMgr->getWorldId());
	if (desc)
	{
		// logger_json << "map_name" << desc->OWName;
		logger_json << "map_name" << desc->worldname;
	}
	if (room && room->isStayMember(uin))
		ActionLogger::InfoLog(uin, worldMgr->getWorldId(), "player_reenter", logger_json);
	else
		ActionLogger::InfoLog(uin, worldMgr->getWorldId(), "player_enter", logger_json);

	MPGAME_HOST_LOG("json log %s", logger_json.json_nospace().c_str());

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_tryRefreshHostFriend", SandboxContext(nullptr));

#endif
	// 传送点同步客机
	MPGAME_HOST_LOG("syncPlayerTransferData uin:%d", uin);
	worldMgr->syncPlayerTransferData(uin);

	SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_SyncEnterWorldHost", SandboxContext(nullptr).SetData_Number("uin", uin));

	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_BOSS_SHIELD_STATE);
	worldMgr->syncPlayerEnter(uin);

#ifndef IWORLD_SERVER_BUILD   
	MINIW::ScriptVM::game()->callFunction("CheckPlayerInfo2EnterWorld", "iiii", uin, roleinfo->model, roleinfo->geniuslv, roleinfo->skinid);
	MINIW::ScriptVM::game()->callFunction("reportPlayerEnterWorldNativeCall", "i", uin);
#else
	//char csAuth[128] = { 0 };
	//房间服和租赁服发送加入检测
	{
		char csUin[128] = { 0 };
		sprintf(csUin, "%d", uin);
		MPGAME_HOST_LOG("rentLuaEvent uin:%d", uin);
		MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "new_player", csUin);
	}

	MPGAME_HOST_LOG("rentPlayerEnterWorld uin:%d", uin);
	MINIW::ScriptVM::game()->callFunction("rentPlayerEnterWorld", "i", uin);
	if (player) {
		ActionLoggerBigData::getInstance()->LogServerPlayerEnter(uin);
	}
#endif

	char content[256];
	const char* systeminfo = GetDefManagerProxy()->getStringDef(3162);
	sprintf(content, "%s%s", roleinfo->nickname, systeminfo);
	//定义一个用户进入游戏提示，该类型不进行文字过滤
	MPGAME_HOST_LOG("sendChat %s%s uin:%d", content, systeminfo, uin);
	sendChat(content, 5);

	ObserverEvent_Player obevent(uin);
	ObserverEventManager::getSingleton().OnTriggerEvent("Game.AnyPlayer.EnterGame", &obevent);

	MPGAME_HOST_LOG("ReportHostMultiGame uin:%d", uin);
	MINIW::ScriptVM::game()->callFunction("ReportHostMultiGame", "i", uin);
}

void MpGameSurviveNetHandler::handleRoleEnterWorld2Host(int uin, const PB_PACKDATA &pkg)
{
#ifdef IWORLD_SERVER_BUILD
	GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host %d ", uin);
#endif
	PB_RoleEnterWorldCH roleEnterWorldCH;
	if (!roleEnterWorldCH.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleRoleEnterWorld2Host PB_RoleEnterWorldCH");
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, proto error! %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() pb parse err");
		return;
	}

	WorldManager *worldMgr = m_root->getWorldMgr();

	if (worldMgr == NULL)
	{
		sendError2Client(uin, PB_ERROR_WRONG_REQUESTER);
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, worldmgr null %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() worldmgr is null");
		return;
	}

	long long wid = 0;
	auto world = worldMgr->getWorld(0);
	if (world) wid = world->getOWID();

	if (uin == 0) {
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, uin is 0, %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() uin is 0, forbid enter");
		return;
	}

#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
	// player 已存在不让登录，避免作弊玩家把uin改成其他玩家，导致其他玩家被踢
	ClientPlayer* check_player = uin2Player(uin);
	if (check_player)
	{
		if (!check_player->IsOffline())
		{
			MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, player is exist %d ", uin);
			GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, player is exist %d ", uin);
			ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() player is exist");
			return;
		}
	}
#else
	//普通联机 
	ClientPlayer* check_player = uin2Player(uin);
	if (check_player)
	{
		if (!check_player->IsOffline())
		{
			//player 已存在不让登录，避免作弊玩家把uin改成其他玩家，导致其他玩家被踢
			LOG_INFO("MpGameSurviveNetHandler::handleRoleEnterWorld2Host() role_enter_err player uin:%ld is exist", uin);
			return;
		}
	}
#endif

	if (uin != roleEnterWorldCH.uin())
	{
		MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, uin no same %d, proto uin:%d", uin, roleEnterWorldCH.uin());
		sendError2Client(uin, PB_ERROR_WRONG_REQUESTER);
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, uin no same %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() uin != roleEnterWorldCH.uin()");
		return;
	}

	if (GetGameNetManagerPtr()->getConnection()->isMemberFull())
	{
		MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, member full %d ", uin);
		GetGameNetManagerPtr()->getConnection()->kickoffMember(uin, ERR_CODE_ROOM_FULL);
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, member full %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() getConnection()->isMemberFull() full");
		return;
	}

	if (GetGameNetManagerPtr()->m_HostRecvHeartBeart.find(uin) == GetGameNetManagerPtr()->m_HostRecvHeartBeart.end())
	{
#if !STUDIO_SERVER && defined(IWORLD_SERVER_BUILD)
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, heartbeat no exist %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() can't find heartbeat");
#endif
		return; //已经断线了
	}

	MNSandbox::GlobalNotify::GetInstance().NotifyHostClientEnterRoom(uin);
	if (GetClientInfoProxy()->isClosing())
	{
		MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, server is closing %d ", uin);
		sendError2Client(uin, PB_ERROR_ROLE_ENTER_SERVER_CLOSING);
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, server is closing %d ", uin);
		ActionLogger::SimpleErrLog(uin, 0, "role_enter_err", "server closing");
		return;
	}

#ifdef IWORLD_SERVER_BUILD
	if (!AntiSetting::CheckUserAuth(uin, roleEnterWorldCH.auth()))
	{
		MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, auth check faield %d, auth:%s", uin, roleEnterWorldCH.auth().c_str());
		GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ERR_CODE_CHEAT_LOGIN);
		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, auth check faield %d ", uin);
		return;
	}

#endif

	MPGAME_HOST_LOG("HostRequestUGCData uin:%d", uin);
	MINIW::ScriptVM::game()->callFunction("HostRequestUGCData", "i", uin);
	if (GetGameNetManagerPtr()->getRoomClient()) // 通知房间服务器增加玩家数
	{
		GetGameNetManagerPtr()->getRoomClient()->updateRoomIncPlayer(GetClientInfoProxy()->getUin(), uin);
	}

	const PB_RoleInfo &src = roleEnterWorldCH.roleinfo();

	PlayerBriefInfo *roleinfo = NULL;

#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
	// 云服加载机制修改, 此处不调用addPlayerBriefInfo, 将PlayerBriefInfo传入zmqmgr保存, dataserver返回后再将PlayerBriefInfo加入mpgamesurvive中
	PlayerBriefInfo realRoleInfo(uin);
	roleinfo = &realRoleInfo;
#else
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		roleinfo = mp->findPlayerInfoByUin(uin);
		if (!roleinfo)
		{
			roleinfo = mp->addPlayerBriefInfo(uin);
		}
		MPGAME_HOST_LOG("findPlayerInfoByUin uin:%d", uin);
	}
#endif
	//PlayerBriefInfo *roleinfo = findPlayerInfoByUin(uin);
	//if (roleinfo == NULL) roleinfo = addPlayerBriefInfo(uin);
	assert(roleinfo);

	roleinfo->model = src.model();
	roleinfo->geniuslv = roleEnterWorldCH.geniuslv();
	roleinfo->skinid = src.skinid();
	roleinfo->frameid = src.frameid();
	roleinfo->vipinfo.vipType = roleEnterWorldCH.vipinfo().viptype();
	roleinfo->vipinfo.vipLevel = roleEnterWorldCH.vipinfo().viplevel();
	roleinfo->vipinfo.vipExp = roleEnterWorldCH.vipinfo().vipexp();
	roleinfo->accountSkinID = src.skinid();
	std::string nickname = src.nickname();
	roleinfo->setOrder();
	// 去除非法的颜色标签
	if (nickname.find("#") != std::string::npos)
	{
		nickname = Rainbow::StringUtil::replace(nickname, "#", "");
	}
	MyStringCpy(roleinfo->nickname, sizeof(roleinfo->nickname), nickname.c_str());
	
	// 悦享赛事称号 ******** by wuyuwang
	std::string bptitle = src.bptitle();
	MyStringCpy(roleinfo->bptitle, sizeof(roleinfo->bptitle), bptitle.c_str());
	
#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
	MpGameSurvive* mp2 = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp2)
	{
		MPGAME_HOST_LOG("addCheckNickName uin:%d, nickname:%s", uin, nickname.c_str());
		mp2->addCheckNickName(uin, nickname);
	}
#endif
	if (src.has_customjson())
		MyStringCpy(roleinfo->customjson, sizeof(roleinfo->customjson), src.customjson().c_str());
	else
	{
		roleinfo->customjson[0] = 0;
	}

	if (roleEnterWorldCH.has_game_session_id())
		MyStringCpy(roleinfo->game_session_id, sizeof(roleinfo->game_session_id), roleEnterWorldCH.game_session_id().c_str());
		
	roleinfo->UICtrlMode = roleEnterWorldCH.uictrlmode();
	if (roleEnterWorldCH.has_lang())
		roleinfo->lang = roleEnterWorldCH.lang();
	else
		roleinfo->lang = 0;
	if (roleEnterWorldCH.has_country())
		MyStringCpy(roleinfo->country, sizeof(roleinfo->country), roleEnterWorldCH.country().c_str());
	else
		roleinfo->country[0] = 0;

	if (roleEnterWorldCH.has_apiid())
		roleinfo->apiid = roleEnterWorldCH.apiid();
	else
		roleinfo->apiid = 999;

	MyStringCpy(roleinfo->auth, sizeof(roleinfo->auth), roleEnterWorldCH.auth().c_str());
	roleinfo->enterTime = MINIW::GetTimeStamp();

	MPGAME_HOST_LOG("roleinfo->enterTime uin:%d, enterTime:%lld, apiid:%d, auth:%s", uin, roleinfo->enterTime, roleinfo->apiid, roleinfo->auth);

	// 物理itemid同步给客机
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_syncPlayerAllVehicleItemid",
		SandboxContext(nullptr)
		.SetData_Number("uin", uin));

	ClientPlayer *player = uin2Player(uin);
	if (nullptr != player) //把owglobal和player的初始数据发给client
	{
		MPGAME_HOST_LOG("uin2Player got. uin:%d", uin);

		LockCtrlComponent* lockctrl = player->GetComponent<LockCtrlComponent>();
		if (lockctrl)
		{
			lockctrl->SetLastPasswd(lockctrl->GetLastPasswd());
		}

		SocRevivePointComponent* SocRevivePoint = player->GetComponent<SocRevivePointComponent>();
		if (SocRevivePoint)
		{
			SocRevivePoint->OnPlayerEnter();
		}

		player->changePlayerModel(ComposePlayerIndex(roleinfo->model, roleinfo->geniuslv, roleinfo->skinid));
		player->getBody()->setPlayerFrameId(roleinfo->frameid);
		PB_RoleEnterWorldHC roleEnterWorldHC;
		roleEnterWorldHC.set_uin(uin);
		worldMgr->saveGlobal(roleEnterWorldHC.mutable_globalinfo());

		PB_PlayerInfo* pPlayerInfo = roleEnterWorldHC.mutable_playerinfo();
		pPlayerInfo->set_objid(player->getObjId());
		player->changeRoleData(pPlayerInfo->mutable_roledata());
		pPlayerInfo->set_anim(player->getBody()->getCurAnim(0));
		pPlayerInfo->set_anim1(player->getBody()->getCurAnim(1));
		pPlayerInfo->set_animweapon(player->getBody()->getCurAnimWeapon());
		pPlayerInfo->set_bodycolor(player->getBody()->getBodyColor());
		pPlayerInfo->set_customscale(player->getCustomScale());
		pPlayerInfo->set_actseqid(player->getBody()->getActSeqID());
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
		SANDBOX_ASSERT(player->GetNodeid() != 0);
		pPlayerInfo->set_nodeid(player->GetNodeid());
		// LOG_INFO("[player_nodeid] send player nodeid: %d , objid:%d", player->GetNodeid(), player->getObjId());
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
		auto sawtooth_ = pPlayerInfo->mutable_sawtooth();
		auto thornBall = player->getThornBallComponent();
		if (thornBall != nullptr)
		{
			for (int i = 0; i < thornBall->getThornAnchorNum(); i++)
			{
				auto info = thornBall->getThornAnchorAt(i);
				PB_SawtoothInfo* roleinfodata = sawtooth_->Add();
				roleinfodata->set_sawtoothid(info.anchorId);
				PB_Vector3* vec = roleinfodata->mutable_pos();
				vec->set_x(info.pos.x);
				vec->set_y(info.pos.y);
				vec->set_z(info.pos.z);
			}
		}
		PB_SkillCDData* pSkillCDData = roleEnterWorldHC.mutable_skillcddata();
		player->saveSkillCDCompToPB(pSkillCDData);
		PB_SkillExpandCDDataGather* pSkillExpandCDDataGather = roleEnterWorldHC.mutable_skillexpandcddatagather();
		player->saveSkillExtendCDCompToPB(pSkillExpandCDDataGather);

		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
		if (mp)
		{
			mp->fillCurWorldDesc(roleEnterWorldHC.mutable_worlddesc());
		}


		// 基础设置，如果有开局介绍，同步给客机相关文件
		const int urllen = 255;
		char url[urllen + 1] = { 0 };
		MINIW::ScriptVM::game()->callFunction("GetCurUploadingIntroUrl", ">s", url);
		url[urllen] = 0;
		if (strlen(url) > 1)
		{
			MPGAME_HOST_LOG("GetCurUploadingIntroUrl uin:%d, url:%s", uin, url);
			roleEnterWorldHC.set_url(url);
		}

		//家园的出生点特殊处理
		SandboxResult homeresult = SandboxEventDispatcherManager::GetGlobalInstance().
			Emit("Homeland_getSpawnBlockPos", SandboxContext(nullptr).SetData_Number("SpawnPosType", 0));
		if (homeresult.IsSuccessed() && player->getWorld())
		{
			WCoord pt = homeresult.GetData_UserObject<WCoord>("wcoord");
			player->gotoBlockPos(player->getWorld(), pt, false);
			PB_PlayerInfo* playerInfo = roleEnterWorldHC.mutable_playerinfo();
			PB_RoleData* roleData = playerInfo->mutable_roledata();
			PB_Pos* rolePos = roleData->mutable_pos();
			WCoord realPos = player->getPosition();
			rolePos->set_x(realPos.x);
			rolePos->set_y(realPos.y);
			rolePos->set_z(realPos.z);
		}

		MPGAME_HOST_LOG("sendToClient ROLE_ENTER_WORLD_HC uin:%d", uin);
		GetGameNetManagerPtr()->sendToClient(uin, PB_ROLE_ENTER_WORLD_HC, roleEnterWorldHC);

		worldMgr->syncPlayerEnter(uin);
		if (worldMgr->m_RuleMgr && worldMgr->isGameMakerRunMode() && ROOM_BY_PLAYER == GetClientInfoProxy()->getRoomHostType())
		{
			int sval = worldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_PLAYERATTR_SAVE);
			if (sval == 1) //已打开开关
			{
				long long fromowid = worldMgr->getFromWorldID();
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_loadRoleArchData", SandboxContext(nullptr)
					.SetData_Usertype("playerControl", player)
					.SetData_Number("fromowid", fromowid));
			}
			else if (worldMgr->isUGCMode())
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_UgcModeLoadCloudVar", SandboxContext(nullptr)
					.SetData_Number("uin", player->getUin()));
			}
		}
		RoleEnterWorld2HostLast(uin, player);
	}
	else
	{
#if defined(IWORLD_SERVER_BUILD) && !STUDIO_SERVER
		LOG_INFO("send miniw::RTMySQL::LoadAllPlayerData: uin = %d", uin);
		long long appid = GetClientInfoProxy()->GetServerAppID();
		long long wid = g_WorldMgr->getFromWorldID();
		if (appid > 0) {
			bool useAppID = cloudserver::gCloudMapConfig.nDataGlobal == cloudserver::DataInteroperable::kPlayerDataOnly
				|| cloudserver::gCloudMapConfig.nDataGlobal == cloudserver::DataInteroperable::kAll;
			if (useAppID)
				wid = appid;
		}
		int teamid = 0;
		if (mp2)
		{
			teamid = mp2->getPreSetPlayerTeam(uin);  // 通过其它途径预设的队伍ID(如msgbus)
			if (teamid <= 0)
			{
				teamid = roleEnterWorldCH.has_specify_team() ? roleEnterWorldCH.specify_team(): 0;  // 客户端上传的队伍ID
				SLOG(INFO) << "PresetTeam enter uin=" << uin <<" client specify teamid=" << teamid << ";";
				MPGAME_HOST_LOG("PresetTeam enter uin=%d, client specify teamid=%d", uin, teamid);
				if (teamid > 0)
					mp2->onPreSetPlayerTeam(uin, teamid);
			}
		}
		else
		{
			SLOG(INFO) << "PresetTeam enter MpGameSurvive not found uin=" << uin << ";";
			MPGAME_HOST_LOG("PresetTeam enter MpGameSurvive not found uin=%d", uin);
			teamid = roleEnterWorldCH.has_specify_team() ? roleEnterWorldCH.specify_team(): 0;  // 客户端上传的队伍ID
		}
		if (cloudserver::gCloudMapConfig.bNeedTeam && teamid <= 0)
		{  // 本房间必须带队伍ID进入
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ERR_CODE_NEED_TEAM);
			GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, needteam %d", uin);
			jsonxx::Object log;
			log << "teamid" << teamid;
			log << "msg" << "need teamid";
			ActionLogger::InfoLog(uin, 0, "role_enter_err", log);
			ConnectionCheck::SetUinConnStat(uin, 4);
			return;
		}
		if (cloudserver::gCloudMapConfig.bNeedTeam && (!g_WorldMgr || !g_WorldMgr->m_RuleMgr || !g_WorldMgr->m_RuleMgr->canAddTeam(teamid)))
		{  // 无法加入指定的队伍, 踢出
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, ERR_CODE_TEAM_NOT_ENTERABLE);
			GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host failed, preset team cant join %d teamid:%d", uin, teamid);
			jsonxx::Object log;
			log << "teamid" << teamid;
			log << "msg" << "cant join target team, team may full";
			ActionLogger::InfoLog(uin, 0, "role_enter_err", log);
			ConnectionCheck::SetUinConnStat(uin, 4);

			return;
		}

		GetICloudProxyPtr()->SimpleSLOG("4.MpGameSurviveNetHandler:handleRoleEnterWorld2Host succ, start loaddata %d", uin);
		g_zmqMgr->LoadPlayerDataFromDataServer(wid, uin, roleinfo);
#else
		MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
		if (mp)
		{
			player = mp->onPlayerEnter(uin);
		}
		if (worldMgr->m_RuleMgr && worldMgr->isGameMakerRunMode() && ROOM_BY_PLAYER == GetClientInfoProxy()->getRoomHostType())
		{
			int sval = worldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_PLAYERATTR_SAVE);
			if (sval == 1) //已打开开关
			{
				long long fromowid = worldMgr->getFromWorldID();
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_loadRoleArchData", SandboxContext(nullptr)
					.SetData_Usertype("playerControl", player)
					.SetData_Number("fromowid", fromowid));
			}
			else if (GetClientInfoProxy()->GetEditorSceneSwitch() == 1)
			{
				SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_UgcModeLoadCloudVar", SandboxContext(nullptr)
					.SetData_Number("uin", player->getUin()));
			}
		}
		RoleEnterWorld2HostLast(uin, player);
#endif
	}


	if (check_player)
	{
		if (check_player->IsOffline())
		{
			check_player->resetChunkViewer();//重新请求请求chunk
#if OFFLINE_MODE_ENABLED == 0
			check_player->SetOffline(false);
#endif
			MpActorTrackerEntry* entry = check_player->getWorld()->getMpActorMgr()->getTrackerEntry(check_player->getObjId());
			if (entry)
			{
				player->getWorld()->getMpActorMgr()->removeTrackingPlayer(player);
				//entry->onPlayerOnline();
			}
		}
	} else {
		MPGAME_HOST_LOG("handleRoleEnterWorld2Host failed, check_player is null");
	}
}

void MpGameSurviveNetHandler::handleRoleLeaveWorld2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_RoleLeaveWorldCH roleLeaveWorldCH;
	roleLeaveWorldCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	SimpleSLOG("MpGameSurviveNetHandler::handleRoleLeaveWorld2Host player leave room %d target:%d", uin, roleLeaveWorldCH.uin());

#ifdef IWORLD_SERVER_BUILD	
	if (GetClientInfoProxy()->isRentType(1))
	{
		auto        cliInfo = GetClientInfoProxy();
		const char* s_uin   = cliInfo->getEnterParam("account");
		int owner_uin = atoi(s_uin);
		if (g_zmqMgr && owner_uin == uin)
		{
			int initoktime = g_zmqMgr->GetInitokTime();
			if (initoktime > 0)
			{
				unsigned int now_ = Rainbow::Timer::getSystemTick();
				int freeSec = 30;
				if (g_zmqMgr->personalroom_timeout_freeSec1 > 0)
					freeSec = g_zmqMgr->personalroom_timeout_freeSec1;
				if (now_ / 1000 - initoktime < freeSec)
				{
					MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "personal_close", "");					
					GetICloudProxyPtr()->SimpleSLOG("personal_rent_room closeroom14 [%d], %d,%d,%d",
					                                owner_uin, now_ / 1000, initoktime, freeSec);
					g_zmqMgr->CloseRoom(2, "");
				}
				else
				{
					if (GetGameNetManagerPtr())
					{
						RoomClient *RoomClient = GetGameNetManagerPtr()->getRoomClient();
						if (RoomClient && g_zmqMgr)
						{
							MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "personal_close", "");							
							RoomClient->roomOwnerConsumePersonalRentroomItem(owner_uin);
							GetICloudProxyPtr()->SimpleSLOG("personal_rent_room closeroom15 [%d], %d,%d,%d",
							                                owner_uin, now_ / 1000, initoktime, freeSec);
							g_zmqMgr->CloseRoom(2, "");
						}
					}
				}
			}
		}
	}
#endif	

	ClientPlayer *player = uin2Player(uin);

	//bool bNotifyClientLeave = false;//kickoff 会触发一次离开通知,标记过滤一下
	if (player)
	{
		player->onAnimInterrupt();
#if OFFLINE_MODE_ENABLED
		player->SetOffline(true);
#endif
	}
	if (ROOM_SERVER_RENT != GetGameInfoProxy()->GetRoomHostType())
	{
		if (uin != roleLeaveWorldCH.uin())
		{
			sendError2Client(uin, PB_ERROR_WRONG_REQUESTER);
			return;
		}
		kickoff(uin);
		//bNotifyClientLeave = true;
	} else
	{
		if (uin == roleLeaveWorldCH.uin())
		{
			kickoff(uin);
			return;
		}
		
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getCSAuthority",
			SandboxContext(nullptr).SetData_Number("uin", uin));
		CloudServerAuthority kickerAuthority;
		if (result.IsExecSuccessed())
		{
			kickerAuthority = result.GetData_UserObject<CloudServerAuthority>();
		}
		if (kickerAuthority.Type > CS_AUTHORITY_MANAGER || kickerAuthority.Type < CS_AUTHORITY_ROOM_OWNER)
		{
			return;
		}
		MINIW::ScriptVM::game()->callFunction("RentKickPlayer", "iiibi", roleLeaveWorldCH.uin(), 0, kickerAuthority.Type, false, uin);
	}

	//if (!bNotifyClientLeave)
	//{
	//	MNSandbox::GlobalNotify::GetInstance().NotifyHostClientLeaveRoom(uin);
	//}
}

namespace AntiSetting{
	extern unsigned SwitchNoCollision;
	extern bool IsSwitchOff(unsigned value);
	extern bool forceUseNewSync(int uin);
}

void MpGameSurviveNetHandler::handleRoleMove2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	if (!player->isSimPlayer() && AntiSetting::forceUseNewSync(uin))
	{
		ActionLogger::SimpleErrLog(uin, 0, "cheat_use_old_move", "");
		return;
	}

	PB_RoleMoveCH roleMoveCH;
	roleMoveCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	const PB_MoveMotion &motion = roleMoveCH.movemotion();
	bool posChange = motion.changeflags() & 1;
	bool yawChange = motion.changeflags() & 2;
	// 仅作为移动上报心跳的协议 不实际作用
	if (!posChange && !yawChange)
		return;

	ActorLocoMotion *locmove = player->getLocoMotion();
	WCoord oldpos = locmove->m_Position;
	WCoord targetpos = locmove->m_Position;
	float targetpitch = locmove->m_RotationPitch;
	float targetyaw = locmove->m_RotateYaw;
	WCoord addmotion(roleMoveCH.addmotion().x(), roleMoveCH.addmotion().y(), roleMoveCH.addmotion().z());

	if (player->isCurrentActionState("Sleep") || (motion.mapid() > 0 && motion.mapid() - 1 != player->getCurMapID()))
	{
		SendResetPosition2Client(player, targetyaw, targetpitch);
		return;
	}

	//在传送仓中不能移动 by:Jeff
	if (player->isSittingInStarStationCabin())
	{
		SendResetPosition2Client(player, targetyaw, targetpitch);
		return;
	}

	//LOG_INFO("rolemove: flags(%d), pos(%d,%d,%d)", motion.ChangeFlags, motion.Position.X, motion.Position.Y, motion.Position.Z);
	if (posChange)
	{
		//玩家传送之后上报的位置如果超过1个chunk，则不接受
		if (player->m_gotoPosChunkXZ.y == 1)
		{
			WCoord toCheckPos = MPVEC2WCoord(motion.position());
			int xCheck = CoordDivSection(toCheckPos.x);
			int zCheck = CoordDivSection(toCheckPos.z);
			int absX = abs(player->m_gotoPosChunkXZ.x - xCheck);
			int absZ = abs(player->m_gotoPosChunkXZ.z - zCheck);
			if (absX > 1 || absZ > 1)
			{
				//不接受，应该上报一次传送的目标位置
				//不设置targetpos
				//LogStringMsg("Bryan PosChange not accept %d %d", absX, absZ);
			}
			else
			{
				player->m_gotoPosChunkXZ.x = -1;
				player->m_gotoPosChunkXZ.z = -1;
				player->m_gotoPosChunkXZ.y = -1;
				targetpos = toCheckPos;
				//LogStringMsg("Bryan PosChange accept %d %d", absX, absZ);
			}
		}
		else
		{
			targetpos = MPVEC2WCoord(motion.position());
			//LogStringMsg("Bryan PosChange1 %d %d %d", targetpos.x, targetpos.y, targetpos.z);
		}
	}
	if (motion.changeflags() & 2)
	{
		targetpitch = AngleChar2Float(motion.pitch());
		targetyaw = AngleChar2Float(motion.yaw());
		if (player->GetCheatHandler())
			player->GetCheatHandler()->checkRotation(targetyaw, targetpitch);
	}

	bool oldjetfly = false;
	auto functionWrapper = player->getFuncWrapper();
	if (functionWrapper)
	{
		oldjetfly = functionWrapper->getJetpackFlying();
		if (player->getCurDorsumID() == ITEM_JETPACK && (motion.changeflags() & 4) != 0) functionWrapper->setJetpackFlying(true);
		else functionWrapper->setJetpackFlying(false);
	}


	bool onground = (motion.changeflags() & 8) != 0;
	bool flymode = (motion.changeflags() & 16) != 0;

	bool moveOnVehicle = false;
	WCoord dp = targetpos - locmove->m_Position;

	PlayerLocoMotion* playerloco = static_cast<PlayerLocoMotion*>(locmove);
	if (roleMoveCH.has_vehiclepos())
	{
		moveOnVehicle = true;
		//if (playerloco->isOnVehicle())
		//{
		//	WCoord posnow(roleMoveCH.vehiclepos().x(), roleMoveCH.vehiclepos().y(), roleMoveCH.vehiclepos().z());
		//	WCoord post = playerloco->getGlobalVehiclePos(posnow);
		//	//WCoord post(roleMoveCH.vehiclepos().x(), roleMoveCH.vehiclepos().y(), roleMoveCH.vehiclepos().z());
		//	if (playerloco->m_bOnVehicleRemote)
		//	{
		//	   dp = post - playerloco->getGlobalVehiclePos(playerloco->m_OnVehiclePosRemote);
		//	}
		//	else
		//	   dp = post - playerloco->getGlabalPos();
		//	playerloco->m_bOnVehicleRemote = true;
		//	playerloco->m_OnVehiclePosRemote = posnow;
		//	if (dp.lengthSquared() <= 16)
		//	{
		//		return;
		//	}
		//}
		//else
		//{
		//	playerloco->m_bOnVehicleRemote = true;
		//	playerloco->m_OnVehiclePosRemote = WCoord(roleMoveCH.vehiclepos().x(), roleMoveCH.vehiclepos().y(), roleMoveCH.vehiclepos().z());
		//}
	}
	else
	{
		//playerloco->m_bOnVehicleRemote = false;
	}
	float dx = Rainbow::Min(Rainbow::Abs(float(dp.x)), Rainbow::Abs(locmove->m_Motion.x));
	float dy = Rainbow::Min(Rainbow::Abs(float(dp.y)), Rainbow::Abs(locmove->m_Motion.y));
	float dz = Rainbow::Min(Rainbow::Abs(float(dp.z)), Rainbow::Abs(locmove->m_Motion.z));
	if (dx*dx + dy*dy + dz*dz > 10.0f*10.0f*BLOCK_FSIZE*BLOCK_FSIZE) //moved too quickly
	{
		//locmove->m_Position = locmove->m_TickPosition.m_LastTickPos;
		locmove->setPosition(locmove->m_TickPosition.m_LastTickPos.x, locmove->m_TickPosition.m_LastTickPos.y, locmove->m_TickPosition.m_LastTickPos.z);
		SendResetPosition2Client(player, targetyaw, targetpitch);
		return;
	}

	if (player->GetCheatHandler())
	{
		bool needCheck = posChange || flymode;
		int checkCode = needCheck ? player->GetCheatHandler()->CheckMoveCheat(motion.changeflags(), targetpos, roleMoveCH.speed()) : 0;
		if (checkCode != 0)
		{
			SendResetPosition2Client(player, targetyaw, targetpitch);
			// player->GetCheatHandler()->NotifyOwnerCheat(checkCode);
			return;
		}
	}

#ifndef IWORLD_DEV_BUILD
	if (!player->checkClientInputMotion(dp, onground))
	{
		LOG_INFO("mpgame: check failed");
		locmove->m_Position = locmove->m_TickPosition.m_LastTickPos;
		SendResetPosition2Client(player, targetyaw, targetpitch);
		return;
	}
#endif
	if (locmove->m_OnGround && !onground && dp.y > 0)
	{
		player->getPlayerAttrib()->useStamina(STAMINA_JUMP);
	}

	CollideAABB box;
	locmove->getCollideBox(box);
	int edge = -6;
	box.expand(edge, edge, edge);
	bool nocollide1 = player->getWorld()->checkNoCollisionBoundBox(box, player);
	Rainbow::Vector3f dpv3 = dp.toVector3();
	ActorLocoMotion::CheckMotionValid(dpv3);
	if (onground)
	{
		dpv3.y -= 10;
	}
	locmove->doMoveStep(dpv3);
	locmove->setOnGround(onground);
	locmove->m_InRun = (motion.changeflags() & (1 << (19 - 1))) != 0;
	player->addMoveStats(dp);

	dp = targetpos - locmove->m_Position;
	if (dp.y > -BLOCK_SIZE / 2 && dp.y < BLOCK_SIZE / 2)
	{
		dp.y = 0;
	}

	//主要用来推动物理
	if (addmotion.x != 0 || addmotion.y != 0 || addmotion.z != 0)
	{
		Rainbow::Vector3f adv3((float)addmotion.x, (float)addmotion.y, (float)addmotion.z);
		ActorLocoMotion::CheckMotionValid(adv3);
		locmove->doMoveStep(adv3);
	}

	//locmove->m_Position = targetpos;
	locmove->m_RotateYaw = targetyaw;
	locmove->m_RotationPitch = targetpitch;
	locmove->setPosition(targetpos.x, targetpos.y, targetpos.z);
	//player->getBody()->setLookAt(targetyaw, targetpitch, 10.0f, 10.0f);
	if (nocollide1)
	{
		bool movedwrong = false;
	/*	if (dp.lengthSquared() > 80 * 80)
		{
			movedwrong = true;
		}
		else*/
		{
			locmove->getCollideBox(box);
			box.expand(edge, edge, edge);
			//bool nocollide2 = player->getWorld()->checkNoActorCollision(box, player);
			bool nocollide2 = player->getWorld()->checkNoActorCollisionByMass((float)player->getMass(), box, player);

			if (!nocollide2) movedwrong = true;
		}

		//20211012 codeby:chenwei 装扮互动动画拉动舞伴可能会超出距离判定，不需要重置位置
		if (movedwrong && !player->getBody()->isPlayingSkinAct())
		{
			//locmove->m_Position = locmove->m_TickPosition.m_LastTickPos;
			locmove->setPosition(oldpos.x, oldpos.y, oldpos.z);
			SendResetPosition2Client(player, targetyaw, targetpitch);
			return;
		}
	}
#ifdef IWORLD_DEV_BUILD
	if (!flymode)
	{
		if (!player->getLocoMotion()->onTheBlock(BLOCK_HOTCRYSTAL))
		{
			FallComponent::calFallMotion_Base(player, (float)(locmove->m_Position.y - oldpos.y), onground);
		}
		else
		{
			auto functionWrapper = player->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}
		}
	}
	else
	{
		auto functionWrapper = player->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
	}
#else
	if (!GetWorldManagerPtr()->isGodMode())
	{
		if (!player->getLocoMotion()->onTheBlock(BLOCK_HOTCRYSTAL))
		{
			FallComponent::calFallMotion_Base(player, (float)(locmove->m_Position.y - oldpos.y), onground);
		}
		else
		{
			auto functionWrapper = player->getFuncWrapper();
			if (functionWrapper)
			{
				functionWrapper->setFallDistance(0);
			}
		}

	}
	else if (flymode)
	{
		auto functionWrapper = player->getFuncWrapper();
		if (functionWrapper)
		{
			functionWrapper->setFallDistance(0);
		}
	}
#endif

	if (functionWrapper && oldjetfly != functionWrapper->getJetpackFlying())
	{
		auto effectComponent = player->getEffectComponent();
		if (effectComponent)
		{
			if (functionWrapper->getJetpackFlying())
			{
				effectComponent->playBodyEffect(TOOLFX_JETPACK2);
			}
			else
			{
				effectComponent->stopBodyEffect(TOOLFX_JETPACK2);
			}
		}
	}
	WCoord reset_pos;
	if (player->GetCheatHandler() && player->GetCheatHandler()->checkCollide(oldpos, reset_pos)){
		locmove->setPosition(reset_pos.x, reset_pos.y, reset_pos.z);
		SendResetPosition2Client(player, targetyaw, targetpitch);
		return;
	}
}

void MpGameSurviveNetHandler::handleSyncChunkData2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_SyncChunkDataCH syncChunkDataCH;
	syncChunkDataCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	bool succeed = false;
	int mapid = syncChunkDataCH.sectioncoord().y();
	int x = syncChunkDataCH.sectioncoord().x();
	int z = syncChunkDataCH.sectioncoord().z();

	if (player->getWorld() && player->getWorld()->getCurMapID() == mapid)
	{
		ChunkIndex index(x, z);
		ChunkViewerList *viewlist = player->getWorld()->getWatchers(index);
		if (viewlist)
		{
			//LOG_INFO("onRequestChunk: uin=%d, index=(%d,%d)", uin, index.x, index.z);
			if (syncChunkDataCH.has_trunkmd5())
				succeed = viewlist->onRequestChunk(player->getChunkViewer(), syncChunkDataCH.trunkmd5().c_str());
			else
				succeed = viewlist->onRequestChunk(player->getChunkViewer());
		}
	}

	if (!succeed)
	{
		PB_SyncChunkDataHC syncChunkDataHC;
		syncChunkDataHC.set_initialize(1);
		syncChunkDataHC.set_sectionflags(0);
		PB_ChunkSaveDB *pChunkData = syncChunkDataHC.mutable_chunkdata();
		pChunkData->set_owid(0);
		pChunkData->set_mapid(mapid);
		pChunkData->set_x(x);
		pChunkData->set_z(z);

		GetGameNetManagerPtr()->sendToClient(uin, PB_SYNC_CHUNK_DATA_HC, syncChunkDataHC, 0, true, RELIABLE_ORDERED, HIGH_PRIORITY, 1);
	}
}

void MpGameSurviveNetHandler::handleActorRevive2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_ActorReviveCH actorRevive;
	actorRevive.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int reviveType = actorRevive.type();
	long long objId = actorRevive.objid();

	if (uin != objId)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	if (player->isDead())
	{
		if (player->GetCheatHandler() && !player->GetCheatHandler()->checkRevive(reviveType))
			return;
		
		player->revive(reviveType);
	}
}

void MpGameSurviveNetHandler::handleActorGetAccountItem2Host(int uin, const PB_PACKDATA &pkg)
{
	// 禁止添加道具协议
//#ifndef IWORLD_SERVER_BUILD
//  ClientPlayer *player = checkPlayerByMsg2Host(uin);
//  if (player == NULL) return;
//
//  PB_GetAccountItemsCH getAccountItemsCH;
//  getAccountItemsCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
//
//  int itemId = getAccountItemsCH.itemid();
//  int num = getAccountItemsCH.num();
//
//  player->gainItems(itemId, num, 1);
//#endif
}

void MpGameSurviveNetHandler::handleRoleGotoPos2Host(int uin, const PB_PACKDATA &pkg)
{  // 按主机设置的传送点飞, 禁止客户端指定目标位置 2024.03.15 by huanglin
	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;
	player->gotoTeleportPos();
}

void MpGameSurviveNetHandler::handleBlockInteract2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BlockInteractCH blockInteractCH;
	blockInteractCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	Rainbow::Vector3f vec(blockInteractCH.colptx() / 100.0f, blockInteractCH.colpty() / 100.0f, blockInteractCH.colptz() / 100.0f);
	WCoord blockpos = MPVEC2WCoord(blockInteractCH.blockpos());
	if (blockInteractCH.has_blueprintid())
	{
		if (auto attrib = player->getPlayerAttrib())
		{
			attrib->SetCurBuildingId(blockInteractCH.blueprintid());
		}
	}
	if (!player->interactBlock(blockpos, (DirectionType)blockInteractCH.face(), vec))
	{
		if (player->isPCControl())
		{
			int itemid = player->getCurToolID();
			const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);
			//小彩蛋点击方块交互不做染色（解决客机2次染色问题）//创造锤和全息道具也不做处理
			if (def && def->UseTarget == ITEM_USE_CLICKBUTTON && itemid != ITEM_COLORED_EGG_SMALL
				&& (ITEM_STONE_HAMMER > itemid || itemid > ITEM_TITANIUM_HAMMER) 
				&& ITEM_HOLOGRAPHIC != itemid )
			{
				player->useItem(itemid, PLAYEROP_STATUS_BEGIN);
			}
		}
		else if (!player->getWorld()->isBlockLiquid(blockpos))
		{
			auto pState = player->getCurrentActionStatePtr();
			if (nullptr != pState)
			{
				auto pDigState = dynamic_cast<DigState*>(pState);
				if (nullptr != pDigState)
				{
					pDigState->digBlock(blockpos, (DirectionType)blockInteractCH.face(), PLAYEROP_STATUS_BEGIN);
				}
				else
				{
					auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
					if (nullptr != pRangDigState)
					{
						pRangDigState->digBlock(blockpos, (DirectionType)blockInteractCH.face(), PLAYEROP_STATUS_BEGIN);
					}
				}
			}
		//	player->digBlock(blockpos, (DirectionType)blockInteractCH.face(), PLAYEROP_STATUS_BEGIN);
			player->tickOperate();
			player->endCurOperate();
		}
	}
	else
	{
		// 20210910：打断隐身  codeby： keguanqiang
		player->breakHorseInvisible();	

		// 成功，尝试上报建筑工升职记活动记录
		player->generalTaskReportPlaceBlock();
	}
}

void MpGameSurviveNetHandler::handleAttackBlock2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BlockAttackCH CH;
	CH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WCoord blockpos = MPVEC2WCoord(CH.blockpos());
	
	long long tick = CH.has_clienttick() ? CH.clienttick() : 0;

	//todo 反外挂
	if (player->GetCheatHandler())
	{
		/*World* world = nullptr;
		if (vehActor)
		{
			world = vehActor->getVehicleWorld();
		}
		if (CH.status() == PLAYEROP_STATUS_BEGIN)
		{
			if (!player->GetCheatHandler()->CheckClickBlock(blockpos, 0, world))
				return;
		}
		if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchDigCost) && !player->GetCheatHandler()->checkClientTick(tick))
		{
			jsonxx::Object log;
			log << "tick" << tick;
			log << "lasttick" << player->GetCheatHandler()->getLastClientTick();
			ActionLogger::ErrorLog(uin, 0, "cheat_attack_tick", log);
			return;
		}*/
	}

	// Get the appropriate action state for block attacks
	auto pState = player->getActionStatePtr("AttackBlock");
	if (nullptr != pState)
	{
		int mineticks;
		int blockid = 0;
		auto pWorld = player->getWorld();
		blockid = pWorld->getBlockID(blockpos);
		if (blockid == 0)
		{
			//记录下位置，防止原先在本方块上敲，后面又切到id为0的位置敲，然后再切回来的时候，不走进度直接敲掉了方块
			player->m_CurDigBlockPos = blockpos;
			return ;
		}
		mineticks = player->getMineBlockTicks(player->getCurToolID(), blockid, pWorld->getBlockData(blockpos), &player->m_MineType);
		player->attackBlock(blockpos, DIG_METHOD_NORMAL);
	}
}
//客机请求抽奖
void MpGameSurviveNetHandler::handlePlayerAltarLuckyDraw2Host(int uin, const PB_PACKDATA &pkg)
{
    ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
    if (player == NULL) return;

    PB_AltarLuckyDrawCH data;
    bool  ret = data.ParseFromArray(pkg.MsgData, pkg.ByteSize);
    if (!ret)
    {
        LOG_INFO("Error handlePlayerAltarLuckyDraw2Host Size %d ", pkg.ByteSize);
        return;
    }
    WCoord targetpos = MPVEC2WCoord(data.pos());
    auto container = dynamic_cast<AltarContainer*>(player->getWorld()->getContainerMgr()->getContainer(targetpos));
    if (container)
        container->doLuckyDrawByClientReq(uin);
}

void MpGameSurviveNetHandler::handleBlockPunch2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BlockPunchCH blockPunchCH;
	blockPunchCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	long long vehID = blockPunchCH.vehicleobjid();
	WCoord blockpos = MPVEC2WCoord(blockPunchCH.blockpos());
	ClientActor* actor = objId2ActorOnClient(vehID);
	auto vehActor = dynamic_cast<ActorVehicleAssemble*>(actor);
	long long tick = blockPunchCH.clienttick();
	if (player->GetCheatHandler())
	{
		World* world = nullptr;
		if (vehActor)
		{
			world = vehActor->getVehicleWorld();
		}
		if (blockPunchCH.status() == PLAYEROP_STATUS_BEGIN)
		{
			if (!player->GetCheatHandler()->CheckClickBlock(blockpos, 0, world))
				return;
		}
		if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchDigCost) && !player->GetCheatHandler()->checkClientTick(tick))
		{
			jsonxx::Object log;
			log << "tick" << tick;
			log << "lasttick" << player->GetCheatHandler()->getLastClientTick();
			ActionLogger::ErrorLog(uin, 0, "cheat_punch_tick", log);
			return;
		}
	}
	
	LOG_WARNING("digBlock status = %d digmethod = %d", blockPunchCH.status(), blockPunchCH.digmethod());

	auto pState = player->getActionStatePtr("Dig");
	if (nullptr != pState)
	{
		if (vehID != 0 && actor )
		{
			assert(vehActor && "对应的actor不是载具");
			auto pDigState = dynamic_cast<DigState*>(pState);
			if (nullptr != pDigState)
			{
				pDigState->digBlockWithVehicle(blockpos, (DirectionType)blockPunchCH.face(), blockPunchCH.status(), vehActor, (DIG_METHOD_T)blockPunchCH.digmethod(), tick);
			}
			else
			{
				auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
				if (nullptr != pRangDigState)
				{
					pRangDigState->digBlockWithVehicle(blockpos, (DirectionType)blockPunchCH.face(), blockPunchCH.status(), vehActor, (DIG_METHOD_T)blockPunchCH.digmethod(), tick);
				}
			}
		}
		else {
			auto pDigState = dynamic_cast<DigState*>(pState);
			if (nullptr != pDigState)
			{
				pDigState->digBlock(blockpos, (DirectionType)blockPunchCH.face(), blockPunchCH.status(), (DIG_METHOD_T)blockPunchCH.digmethod(), tick);
			}
			else
			{
				auto pRangDigState = dynamic_cast<RangeDigState*>(pState);
				if (nullptr != pRangDigState)
				{
					pRangDigState->digBlock(blockpos, (DirectionType)blockPunchCH.face(), blockPunchCH.status(), (DIG_METHOD_T)blockPunchCH.digmethod(), tick);
				}
			}
		}


		
	}
	//player->digBlock(blockpos, (DirectionType)blockPunchCH.face(), blockPunchCH.status(), (DIG_METHOD_T)blockPunchCH.digmethod());
}

void MpGameSurviveNetHandler::handleBlockExploit2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BlockExploitCH blockExploitCH;
	blockExploitCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WCoord blockpos = MPVEC2WCoord(blockExploitCH.blockpos());
	auto pState = player->getActionStatePtr("Exploit");
	if (nullptr != pState)
	{
		auto pExploitState = dynamic_cast<ExploitState*>(pState);
		if (nullptr != pExploitState)
		{
			pExploitState->exploitBlock(blockpos, (DirectionType)blockExploitCH.face(), blockExploitCH.status(), blockExploitCH.picktype());
		}
	}
	//player->exploitBlock(blockpos, (DirectionType)blockExploitCH.face(), blockExploitCH.status(), blockExploitCH.picktype());
}

void MpGameSurviveNetHandler::handleItemUse2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_ItemUseCH itemUseCH;
	itemUseCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord use_pos = MPVEC2WCoord(itemUseCH.curpos());
	if (player->GetCheatHandler())
		use_pos = player->GetCheatHandler()->GetUsetItemPosition(use_pos, itemUseCH.itemid());

	player->setGunInfo(-1, itemUseCH.curyaw(), itemUseCH.curpitch(), use_pos.toVector3());
	if (player->getGunLogical() != NULL) player->getGunLogical()->setFireInterval(itemUseCH.fireinterval());

	bool onshift = itemUseCH.shift() == 1 ? true : false;
	LOG_INFO("handleItemUse2Host uin:%d id:%d, st:%d shift:%d", uin, itemUseCH.itemid(), itemUseCH.status(), onshift);
	player->useItem(itemUseCH.itemid(), itemUseCH.status(), onshift, itemUseCH.usetick()/*, itemUseCH.fireinterval() TODO(liusijia)*/);
}

void MpGameSurviveNetHandler::handleSetHook2Host(int uin, const PB_PACKDATA &pkg)
{
    PB_SetHookCH setHookCH;
	setHookCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = NULL;//dynamic_cast<ClientPlayer *>(objId2ActorOnClient(setHookCH.objid()));
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		player = dynamic_cast<ClientPlayer *>(mp->objId2ActorOnClient(setHookCH.objid()));
	}
	if (player == NULL)
	{
		return;
	}
	player->ClientPlayer::setHookObj(setHookCH.hookid(), false);
}

void MpGameSurviveNetHandler::handlePlayerSpecialSkill2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;
	player->doSpecialSkill();
}

void MpGameSurviveNetHandler::handleItemSkillUse2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_ItemSkillUseCH itemSkillUseCH;
	itemSkillUseCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	// 增加道具验证  codeby:liusijia
	if (CheckItemCheat(player, itemSkillUseCH.has_itemindex()? itemSkillUseCH.itemindex(): 0, itemSkillUseCH.itemid(), "cheat_item_skill_use", itemSkillUseCH.skillid()))
	{
		return;
	}
	Rainbow::Vector3f currentEyePos;
	if(itemSkillUseCH.mutable_curpos() != NULL){
		currentEyePos.Set((float)itemSkillUseCH.mutable_curpos()->x(), (float)itemSkillUseCH.mutable_curpos()->y(), (float)itemSkillUseCH.mutable_curpos()->z());
	}
	Rainbow::Vector3f currentDir(itemSkillUseCH.curdirx(), itemSkillUseCH.curdiry(), itemSkillUseCH.curdirz());

	const ItemSkillDef* skilldef = GetDefManagerProxy()->getItemSkillDef(itemSkillUseCH.skillid());
	if(skilldef)
	{
		std::vector<WORLD_ID> idvec;
		std::vector<WORLD_ID> idvecPhysic;
		std::vector<WCoord> wCoordVec;
		WCoord centerPos;

		player->setCurItemSkillID(itemSkillUseCH.skillid()); //by Jeff
		currentDir  = MINIW::Normalize(currentDir);
		if(currentDir.x == 0)
		currentDir.x = 0.001f;
		if(currentDir.y == 0)
		currentDir.y = 0.001f;
		if(currentDir.z == 0)
		currentDir.z = 0.001f;
		
		if(!((itemSkillUseCH.status() == PLAYEROP_STATUS_BEGIN) && (skilldef->ChargeType > 0)))
		{
			//判断一下有没有刷怪效果
			bool haveCallMob = false;
			for(int i = 0; i<(int)skilldef->SkillFuncions.size(); i++)
			{	
				ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[i];
				if(functiondef == NULL) continue;
				if(functiondef->oper_id == 5) //召唤
				{
					haveCallMob = true;
				}
			}
			if(skilldef->TargetType == 0)
			{			
				idvec =player->doPickActorByItemSkill(itemSkillUseCH.skillid(), centerPos, currentEyePos, currentDir);			
				//如果有召唤效果的话也要选择一下方块,以便刷怪
				if(haveCallMob)
				{
					wCoordVec = player->doPickBlockByItemSkill(itemSkillUseCH.skillid(), currentEyePos, currentDir);
				}
				// 102 击飞技能 要有物理属性
				bool isKnockSkill = false;
				for (int j = 0; j<(int)skilldef->SkillFuncions.size(); j++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[j];
					if (functiondef->oper_id == 4)
					{
						isKnockSkill = true;
					}
				}
				if ( isKnockSkill )
				{
					idvecPhysic = player->doPickPhysicsActorByItemSkill(itemSkillUseCH.skillid(), centerPos, currentEyePos, currentDir);
					if (idvecPhysic.size() > 0)
					{
						idvec.insert(idvec.end(),idvecPhysic.begin(),idvecPhysic.end());
					}
				}
			}
			else if(skilldef->TargetType == 1)
			{
				wCoordVec = player->doPickBlockByItemSkill(itemSkillUseCH.skillid(), currentEyePos, currentDir);
			}
			else if(skilldef->TargetType == 2)
			{
				player->setCurItemSkillID(itemSkillUseCH.skillid());
				idvec = player->doPickPhysicsActorByItemSkill(itemSkillUseCH.skillid(), centerPos, currentEyePos, currentDir);
			}
		}
	// 巴啦啦魔法棒  20210721  codeby： wudeshen
		player->useItemSkill(itemSkillUseCH.itemid(), itemSkillUseCH.status(), itemSkillUseCH.skillid(), currentEyePos, currentDir, wCoordVec, idvec, centerPos, itemSkillUseCH.clientparam());
	}
}

void MpGameSurviveNetHandler::handleSpecialItemUse2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkPlayerByMsg2Host(uin, true);
	if (player == NULL) return;

	PB_SpecialItemUseCH specialItemUseCH;
	specialItemUseCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (specialItemUseCH.gridindex() < 0)
	{
		// liusijia 1.28开始兑换星星走新的校验流程，在lua协议COIN_CONVERT_STAR C++中屏蔽逻辑，仅保留日志
		//player->starConvert(-specialItemUseCH.gridindex());
		jsonxx::Object cheat;
		cheat << "star_num" << -specialItemUseCH.gridindex();  // 客机上传的获取星星数量
		ActionLogger::ErrorLog(uin, 0, "cheat_star_convert_new", cheat);
	}
	else
	{
		// 反外挂 useSpecialItem 由主机计算生成的物品和数量  2022.08.31 by huanglin
		int add_item_id = 0;
		int add_item_count = 0;
		int grid_item_id = player->getBackPack()->getGridItem(specialItemUseCH.gridindex());
		if (grid_item_id <= 0)
			return;
		MINIW::ScriptVM::game()->callFunction("genAccountItemUseResult", "i>ii", grid_item_id, &add_item_id, &add_item_count);
		if (specialItemUseCH.itemid() != add_item_id || add_item_count != specialItemUseCH.itemnum()){
			jsonxx::Object cheat;
			cheat << "client_add_item_id" << specialItemUseCH.itemid();  // 客机指定获得的物品ID
			cheat << "client_add_item_count" << specialItemUseCH.itemnum();  // 客机指定获得的数量
			cheat << "client_use_item" << grid_item_id;  // 客机指定消耗格子上的物品ID
			cheat << "server_add_item_id" << add_item_id;  // 主机计算应获得的物品ID
			cheat << "server_add_item_count" << add_item_count;  // 主机计算应获得的数量
			ActionLogger::ErrorLog(uin, 0, "cheat_use_special_item", cheat);
		}
		if (add_item_id > 0 && add_item_count > 0)
			player->useSpecialItem(specialItemUseCH.gridindex(), add_item_id, add_item_count);
	}
}

void MpGameSurviveNetHandler::handleActorInteract2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_ActorInteractCH actorInteractCH;
	actorInteractCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor *target = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(actorInteractCH.target());
	if (target == NULL)
	{
		return;
	}
	if (player->GetCheatHandler() && !player->GetCheatHandler()->checkInteractTarget(target, actorInteractCH.itype()))
	{
		return;
	}
	player->setGunInfo(0, actorInteractCH.curyaw(), actorInteractCH.curpitch(), Rainbow::Vector3f((float)actorInteractCH.curpos().x(), (float)actorInteractCH.curpos().y(), (float)actorInteractCH.curpos().z()));
	//if(msgdata->itype == 0) player->attackActor(target);
	//else player->interactActor(target);
	if (actorInteractCH.has_collidepos())
	{
		player->m_PickResult.collide_pos = MPVEC2WCoord(actorInteractCH.collidepos());
	}
	if (player->interactActor(target, actorInteractCH.itype(), actorInteractCH.iplot() > 0))	// 20210910：打断隐身  codeby： keguanqiang
	{
		ActorLiving *living = dynamic_cast<ActorLiving*>(target);
		if (living)
		{
			living->breakInvisible();
		}
		player->breakHorseInvisible();
	}
}

void MpGameSurviveNetHandler::handleActorStopAnim2Host(int uin, const PB_PACKDATA &pkg) // PB_ACTOR_STOP_ANIM_CH
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;
	ActorBody *body = player->getBody();
	if (body)
	{
		PB_ActorStopAnimCH actorStopAnimCH;
		actorStopAnimCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		if (actorStopAnimCH.isseq())
		{
			if (body->getEntity())
			{
				body->getEntity()->StopAnim(actorStopAnimCH.anim());
				//广播给其他客机
				PB_ActorStopAnimHC actorStopAnimHC;
				actorStopAnimHC.set_actorid(player->getObjId());
				actorStopAnimHC.set_anim(actorStopAnimCH.anim());
				actorStopAnimHC.set_isseq(true);
				player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_STOP_ANIM_HC, actorStopAnimHC, player);
			}
		}
		else
		{
			body->stopAnim(actorStopAnimCH.anim());
		}
	}
}

void MpGameSurviveNetHandler::handleActorAnim2Host(int uin, const PB_PACKDATA &pkg) // PB_ACTOR_ANIM_CH
{
	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	ActorBody *body = player->getBody();
	if (body)
	{
		PB_ActorAnimCH actorAnimCH;
		actorAnimCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

		if ((actorAnimCH.has_actid() && actorAnimCH.actid() != body->getActID() && body->getActID() > 0)
			|| (actorAnimCH.has_actidtrigger() && actorAnimCH.actidtrigger() != body->getActTriggerID() && body->getActTriggerID() > 0)
			|| (actorAnimCH.has_animseq() && actorAnimCH.animseq() != body->getAnimSeq()))
		{
			body->setCurAnim(-1, 0);
			player->stopMotion(30000);
			body->stopAnim(SEQ_PLAY_ACT);
		}
		if (actorAnimCH.has_actid() && actorAnimCH.actid() >= 0 && actorAnimCH.actid() != body->getActID())
		{
			auto def = GetDefManagerProxy()->getPlayActDef(actorAnimCH.actid());
			if (def)
			{
				// 检测skin是否匹配
				bool success = true;
				if (def->SkinID > 0)
				{
					int curSkinId = body->getSkinID();
					if (curSkinId == 0 || (curSkinId != def->SkinID && curSkinId != def->SkinID2))
					{
						// 皮肤检测不通过
						success = false;
						actorAnimCH.set_actid(-1);
					}
				}
				player->stopMotion(30000);
				if (success)
					body->playMotion(def->Effect.c_str(), 30000);
			}
		}
		else if (actorAnimCH.has_actidtrigger() && actorAnimCH.actidtrigger() >= 0 && actorAnimCH.actidtrigger() != body->getActTriggerID())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(actorAnimCH.actidtrigger());
			if (def)
			{
				player->stopMotion(30000);
				player->playMotion(def->Effect.c_str(), 30000);
			}
		}

		//2021-09-14 codeby:chenwei 设置装扮互动副动作标记
		if (actorAnimCH.has_sideact())
		{
			body->setSideAct(actorAnimCH.sideact());
		}

		if (actorAnimCH.has_actid())
		{
			body->setAct(actorAnimCH.actid());
		}
		if (actorAnimCH.has_actidtrigger())
		{
			body->setActTrigger(actorAnimCH.actidtrigger());
		}
		if (actorAnimCH.has_animseq())
		{
			body->setAnimSeq(actorAnimCH.animseq());
		}

		if (actorAnimCH.has_anim1() && actorAnimCH.anim1() == 127)
		{
			if (actorAnimCH.has_anim())
			{				
				if (actorAnimCH.has_isloop())
				{
					body->playAnim(actorAnimCH.anim(), actorAnimCH.isloop());
				}
				else
				{
					body->playAnim(actorAnimCH.anim());
				}
			}
		}
		else
		{
			if (actorAnimCH.has_anim())
			{
				body->setCurAnim(actorAnimCH.anim(), 0);
			}
			if (actorAnimCH.has_anim1())
			{
				body->setCurAnim(actorAnimCH.anim1(), 1);
			}
		}
		if (actorAnimCH.has_anim())
		{
			int anim = actorAnimCH.anim();
			if (anim == SEQ_SHAPE_SHIFT)
			{
				RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef(player->getSkinID());
				if (def)
				{
					int destId = def->ChangeContact[0];
					if(destId == 3494)//牛魔 客机向主机请求变身
						player->showSpecailTryShapeAnim();

					char effectName[64] = { 0 };
					sprintf(effectName, "horsechange_%d", destId);
					auto effectComponent = player->getEffectComponent();
					if (effectComponent)
					{
						effectComponent->playBodyEffect(effectName);
					}
					//路障和擎天圣客机变身后停止人形特效
					if ((destId == 4652 || destId == 4658) && effectComponent)
					{
						effectComponent->stopBodyEffect(def->Effect);
					}

					char soundName[64] = { 0 };
					sprintf(soundName, "ent.%d.change1", destId);
					auto sound = player->getSoundComponent();
					if (sound)
					{
						sound->playSound(soundName, 1.0f, 1.0f);
					}
				}
			}
		}
		if (actorAnimCH.has_animweapon())
		{
			body->setCurAnimWeapon(actorAnimCH.animweapon());
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerSkinning2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == nullptr) return;

	PB_PlayerSkinning playerSkinningHC;
	playerSkinningHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	long long mobid = playerSkinningHC.mobid();
	ClientActor* pMob = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(mobid);
	if (pMob == nullptr) return;

	ClientMob* pClientMob = dynamic_cast<ClientMob*>(pMob);
	if (pClientMob == nullptr) return;

	switch (playerSkinningHC.op())
	{
	case eStartSkinning:
	{
		pClientMob->startSkinningServer(player);
	}
	break;
	case eCancelSkinning:
	{
		pClientMob->cancelSkinningServer(player);
	}
	break;
	case eFinishSkinning:
	{
		pClientMob->finishSkinningServer(player);
	}
	break;
	default:
		break;
	}
}

void MpGameSurviveNetHandler::handleAccountHorse2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_AccountHorseCH accountHorseCH;
	accountHorseCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (player->GetCheatHandler())
	{
		if (!player->GetCheatHandler()->checkSummonHorse(accountHorseCH.horseid()))
			return;
	}
	if (accountHorseCH.cmdtype() == ACCHORSE_CMD_SUMMON)
	{
		MINIW::ScriptVM::game()->callFunction("CheckPlayerRideInfo2Summon", "iib", uin, accountHorseCH.horseid(), false);
	}
	else if (accountHorseCH.cmdtype() == ACCHORSE_CMD_LAYEGG) player->accountHorseEgg();
}

/**
 * @brief 判断移动到目标位置的物品是否符合装备的移动规则
 * 	源于反外挂需要
 * 
 * @param item_id 要移动的物品id
 * @param to_index 要移动到的位置
 * @param uin 移动者的uin, 用于记录反外挂日志
 * @return bool 目标位置非装备栏时直接返回成功, 否则返回物品对应栏位与目标位置是否匹配, 匹配则返回true
 * @note 2022.10.10 by huanglin
 */
//装备类型与装备栏位置解绑, 后续要防作弊应该判断是否有重复相同类型装备----charles xie
bool checkEquipIndex(int item_id, int to_index, int uin){
	//if (IsEquipIndex(to_index)){ // 目标位置是装备栏
	//	auto def = GetDefManagerProxy()->getToolDef(item_id);
	//	if (!def){
	//		return false;
	//	}
	//	if (ToolType2EquipIndex(def->Type) != to_index){
	//		// 物品栏位与装备栏位不匹配
	//		#ifdef IWORLD_SERVER_BUILD
	//		jsonxx::Object cheat;
	//		cheat << "to_index" << to_index;
	//		cheat << "item_id" << item_id;
	//		ActionLogger::ErrorLog(uin, 0, "cheat_wrong_equip", cheat);
	//		#endif
	//		return false;
	//	}
	//}
	//else if (IsHorseEquipIndex(to_index)){ // 目标位置是装备栏   装备类型与装备栏位置解绑, 后续要防作弊应该判断是否有重复相同类型装备 ----charles xie
	//	auto def = GetDefManagerProxy()->getToolDef(item_id);
	//	if (!def){
	//		return false;
	//	}
	//	if (ToolType2EquipIndex(def->Type) != to_index){
	//		// 物品栏位与装备栏位不匹配
	//		#ifdef IWORLD_SERVER_BUILD
	//		jsonxx::Object cheat;
	//		cheat << "to_index" << to_index;
	//		cheat << "item_id" << item_id;
	//		ActionLogger::ErrorLog(uin, 0, "cheat_wrong_equip", cheat);
	//		#endif
	//		return false;
	//	}
	//}
	return true;
}

void MpGameSurviveNetHandler::handleBackPackGridSwap2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackGridSwapCH backPackGridSwapCH;
	backPackGridSwapCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int fromIndex = backPackGridSwapCH.fromgridid();
	int toIndex = backPackGridSwapCH.togridid();

	auto backpack = player->getBackPack();
	if (backpack != nullptr)
	{
		int from_item_id = backpack->getGridItem(fromIndex);
		int to_item_id = backpack->getGridItem(toIndex);

		// 判断移动到目标位置的物品是否符合装备的移动规则 2022.10.10 by huanglin
		if (from_item_id && !checkEquipIndex(from_item_id, toIndex, uin)){
			return;
		}
		if (to_item_id && !checkEquipIndex(to_item_id, fromIndex, uin)){
			return;
		}
		if (from_item_id == to_item_id)
		{
			if (!backpack->mergeItem(fromIndex, toIndex))
			{
				backpack->swapItem(fromIndex, toIndex);
			}
		}
		else
		{
			backpack->swapItem(fromIndex, toIndex);
		}

		//if (toIndex == CUSTOMMODEL_START_INDEX && g_pPlayerCtrl)
			//g_pPlayerCtrl->statisticToWorld(uin, 30014, "", g_pPlayerCtrl->getCurWorldType());
	}
}

void MpGameSurviveNetHandler::handleBackPackGridDiscard2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackGridDiscardCH backPackGridDiscardCH;
	backPackGridDiscardCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int index = backPackGridDiscardCH.gridid();
	int num = backPackGridDiscardCH.num();

	/*
	auto backpack = player->getBackPack();
	if (backpack != nullptr)
	{
		backpack->discardItem(index, num);
	}
	*/
	if(player)
		player->discardItem(index, num);
}

void MpGameSurviveNetHandler::handleBackPackGridEquipWeapon2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackEquipWeaponCH backPackEquipWeaponCH;
	backPackEquipWeaponCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int index = backPackEquipWeaponCH.gridid();
	if (g_WorldMgr && g_WorldMgr->isUGCEditMode())
	{
		//if (index > 39) index = 39;
		if (index < 0) index = 0;
	}
	else if (g_WorldMgr && g_WorldMgr->isUGCEditBuildMode())
	{
		if (index > 23) index = 0;
		if (index < 0) index = 23;
	}
	else
	{
		if (index > 7) index = 0;
		if (index < 0) index = 7;
	}
	player->onSetCurShortcut(index);

	PB_BackPackEquipWeaponHC backPackEquipWeaponHC;
	backPackEquipWeaponHC.set_gridid(index);

	GetGameNetManagerPtr()->sendToClient(uin, PB_BACKPACK_EQUIP_WEAPON_HC, backPackEquipWeaponHC);

	int tmp = player->getCurToolID();
	LOG_INFO("handleBackPackGridEquipWeapon2Host %d", tmp);

	if(g_pPlayerCtrl && g_pPlayerCtrl->isInSpectatorMode() 
	&& g_pPlayerCtrl->getSpectatorType() == SPECTATOR_TYPE_FOLLW)
	{
		g_pPlayerCtrl->switchCurrentItem();
	}

}

void MpGameSurviveNetHandler::handleBackPackSort2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackSortCH backPackSortCH;
	backPackSortCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int index = backPackSortCH.baseindex();
	player->sortPack(index);
}

void MpGameSurviveNetHandler::handleStorageBoxSort2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	player->sortStorageBox();
}

void MpGameSurviveNetHandler::handleBackPackSetItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackSetItemCH backPackSetItemCH;
	if (!backPackSetItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleBackPackSetItem2Host PB_BackPackSetItemCH parse err!!");
		return;
	}

	int index_base = (backPackSetItemCH.toindex() / GRID_INDEX_BASIS) * GRID_INDEX_BASIS;
	if (backPackSetItemCH.itemid() == ITEM_PAINTTANK
	 && (index_base != BACKPACK_START_INDEX && index_base != EXT_BACKPACK_START_INDEX && index_base != SHORTCUT_START_INDEX && index_base != MOUSE_PICKITEM_INDEX) // 仅支持放入背包/快捷栏/鼠标栏
	)
	{
		jsonxx::Object cheat;
		cheat << "num" << backPackSetItemCH.num();
		cheat << "itemid" << backPackSetItemCH.itemid();
		cheat << "index" << backPackSetItemCH.toindex();
		ActionLogger::InfoLog(uin, 0, "cheat_set_item", cheat);
		return;
	}
	
	if (player->getWorld() && ((GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
								|| player->getWorld()->getMapSpecialType() == HOME_GARDEN_WORLD 
								|| backPackSetItemCH.itemid() == ITEM_PAINTTANK))	
	//20211028 喷漆罐道具可以随便设置到格子里 code by:keguanqiang
	{
		player->getBackPack()->setItem(backPackSetItemCH.itemid(), backPackSetItemCH.toindex(), 1);
	}
}

// 仅提供编书台使用  其他道具不可以使用
void MpGameSurviveNetHandler::handleBackPackSetItemWithoutLimit2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	if (player->getWorld())
	{
		PB_BackPackSetItemWithoutLimitCH backPackSetItemCH;
		backPackSetItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		int toindex = backPackSetItemCH.toindex();
		int itemid = backPackSetItemCH.itemid();
		int num = backPackSetItemCH.num();
		jsonxx::Object log;
		log << "client_itemid" << itemid;
		log << "client_count" << num;
		log << "client_toindex" << toindex;

		bool isPolaroidPhoto = itemid == ITEM_POLAROID_PHOTO;
		//是否检查格子
		bool isCheckToIndex = true;
		//如果是相片就不检查格子
		if (isPolaroidPhoto)
			isCheckToIndex = false;

		bool isToIndexInvalid = false;
		//相片不检查放置的格子，因为有空位都会放
		if (isCheckToIndex)
			isToIndexInvalid = (!(toindex >= 32000 && toindex < 32500) && toindex != 40000);

		static std::set<int> legalItems = {11803, 11804, 11806, ITEM_POLAROID_PHOTO, 0};
		if (isToIndexInvalid ||
			num < 0 || itemid < 0 
			|| (AntiSetting::gCheatConfig.SwitchCheckSetItem && legalItems.count(itemid) == 0)  // 限定ID, 用开关控制
		)
		{
			ActionLogger::ErrorLog(uin, 0, "cheat_set_item", log);
			return;
		}

		PackContainer* container = (PackContainer*)player->getBackPack()->getContainer(toindex);
		if (!container || !container->index2Grid(toindex))
		{
			ActionLogger::ErrorLog(uin, 0, "cheat_set_item_indexerr", log);
			return;
		}

		ActionLogger::ErrorLog(uin, 0, "set_item_limit", log);
		// 清空
		if (num == 0) itemid = 0;
		player->getBackPack()->setItemWithoutLimit(itemid, toindex, num, backPackSetItemCH.userdata_str().c_str());
	}
}

void MpGameSurviveNetHandler::handleBackPackMoveItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackMoveItemCH backPackMoveItemCH;
	backPackMoveItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int to_index = backPackMoveItemCH.toindex();
	int from_index = backPackMoveItemCH.fromindex();
	
	// 判断移动到目标位置的物品是否符合装备的移动规则 2022.10.10 by huanglin
	if (!checkEquipIndex(player->getBackPack()->getGridItem(from_index), to_index, uin)){
		return;
	}
	if (backPackMoveItemCH.num() <= 0) player->getBackPack()->shiftMoveItem(from_index, to_index);
	else
	{
		player->getBackPack()->moveItem(from_index, to_index, backPackMoveItemCH.num());

		if (from_index >= STORAGE_START_INDEX && from_index < CRAFT_START_INDEX) //存储箱的item被拿
			GetWorldManagerPtr()->getWorldInfoManager()->takeItemFormContainerNoticeActorVillager(player->getWorld(), player, player->getCurOpenedContainerPos());
		//if (from_index == CUSTOMMODEL_START_INDEX + 1 && g_pPlayerCtrl)
			//g_pPlayerCtrl->statisticToWorld(uin, 30016, "", g_pPlayerCtrl->getCurWorldType());
	}
}

void MpGameSurviveNetHandler::handleBackPackRemoveItem2Host(int uin, const PB_PACKDATA &pkg)
{
	// codeby:liusijia 2022/07/19 无地方使用
	ActionLogger::SimpleErrLog(uin, 0, "backpack_remove_item", "should not use!");
	/*ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackRemoveItemItemCH backPackRemoveItemCH;
	backPackRemoveItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int gridIndex = backPackRemoveItemCH.gridindex();
	int num = backPackRemoveItemCH.num();

	if (num <= 0)
	{
		jsonxx::Object cheat;
		cheat << "client_num" << num;
		ActionLogger::ErrorLog(uin, 0, "cheat_remove_item", cheat);
		return;
	}

	auto backpack = player->getBackPack();
	if (backpack != nullptr)
	{
		backpack->removeItem(gridIndex, num);
	}*/
}

void MpGameSurviveNetHandler::handleCraftItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_CraftItemCH craftItemCH;
	craftItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto backpack = player->getBackPack();
	if (backpack != nullptr)
	{
		int remain = 0;
		bool suc = backpack->doCrafting(craftItemCH.craftid(), &remain, craftItemCH.num());
		if (!suc)
		{
			sendError2Client(uin, PB_ERROR_CRAFT_NOT_ENOUGH);
		}
		else if (remain > 0)
		{
			sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
		}
		return;
	}
	return;
}

void MpGameSurviveNetHandler::handlePlayerWakeUp2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;
	player->SetOffline(false);
}

void MpGameSurviveNetHandler::handleNewRepairItem2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_NewRepairItemCH newRepairItemCH;
	newRepairItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int tgtGridIdx = newRepairItemCH.tgtgrididx();
	int repairDur = newRepairItemCH.repairdur();
	int mat1Id = newRepairItemCH.mat1id();
	int mat2Id = newRepairItemCH.mat2id();
	int repairCount = newRepairItemCH.repaircount();
	int starCount = newRepairItemCH.starcount();
	
	player->NewRepair(tgtGridIdx, repairDur, mat1Id, mat2Id, repairCount, starCount);
}
void MpGameSurviveNetHandler::handleEnchantItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto backpack = player->getBackPack();
	if (backpack == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_EnchantItemCH enchantItemCH;
	enchantItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int tgtIdx = enchantItemCH.gridindex();
	int frmIdx = enchantItemCH.frmgridindex();

	int enchants[5];
	for (int i = 0; i < 5; i++)
	{
		enchants[i] = enchantItemCH.enchantids().Get(i);
	}

	// 检查是不是有新的/不同的附魔增加
	bool same = true;
	for (int i = 0; i < MAX_ITEM_ENCHANTS; ++i)
	{
		if (enchants[i] == 0) continue;
		bool found = false;
		for (int j = 0, _n = backpack->getGridEnchantNum(tgtIdx); j < _n; ++j)
		{
			if (enchants[i] == backpack->getGridEnchantId(tgtIdx, j))
			{
				found = true;
				break;
			}
		}
		if (!found) { same = false; break; }
	}
	if (same)
	{
		sendError2Client(uin, PB_ERROR_ENCHANT_NOT_CHANGED);
		return;
	}

	// 判断是否够需要的条件
	if (!player->canEnchant(tgtIdx, frmIdx, enchants))
	{
		sendError2Client(uin, PB_ERROR_ENCHANT_NOT_ENOUGH);
		return;
	}

	int cost = player->calcEnchantCost(tgtIdx, enchants);
	int stars = 0;
	auto attr = player->getAttrib();
	auto playerAttr = attr == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(attr);
	if (playerAttr == nullptr)
		stars = 0;
	else
	{
		stars = playerAttr->getExp() / EXP_STAR_RATIO;
	}

	if (stars < cost)
	{
		sendError2Client(uin, PB_ERROR_STAR_NOT_ENOUGH);
		return;
	}

	int resultIdx = player->enchant(tgtIdx, frmIdx, enchants);

	if (resultIdx >= 0)
	{
		PB_EnchantItemSuccessHC enchantItemSuccessHC;
		enchantItemSuccessHC.set_gridindex(resultIdx);

		GetGameNetManagerPtr()->sendToClient(uin, PB_ENCHANT_ITEM_SUCCESS_HC, enchantItemSuccessHC);
	}
	else
	{
		sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
		return;
	}
}

void MpGameSurviveNetHandler::handleRuneOperate2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto backpack = player->getBackPack();
	if (backpack == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_RuneOperateCH runeOpCH;
	runeOpCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int type = runeOpCH.optype();
	int resultIdx = -1;
	int execResult = -1;
	switch (type)
	{
		case RuneOperateAuth://鉴定
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_AuthenticateClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).SetData_Number("index3", runeOpCH.index3()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number();
			}
		}
		break;
		case RuneOperateMerge://合成
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_MergeRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).SetData_Number("index3", runeOpCH.index3()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number();
			}
		}
		break;
		case RuneOperateInlayAdd://镶嵌追加
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_InlayAddRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number();
			}
		}
		break;
		case RuneOperateInlayReplace://镶嵌替换
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_InlayReplaceRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).SetData_Number("index3", runeOpCH.index3()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number();
			}
		}
		break;
		case RuneOperateGenerateAddInlay://镶嵌替换
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateAddInlayRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).
				SetData_Number("index3", runeOpCH.index3()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number("gridIndex");
				execResult = (int)result.GetData_Number("result");
			}
		}
		break;
		case RuneOperateGenerateReplaceInlay://镶嵌替换
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("Rune_GenerateReplaceInlayRuneClient",
				SandboxContext(nullptr).SetData_Number("index1", runeOpCH.index1()).SetData_Number("index2", runeOpCH.index2()).
				SetData_Number("index3", runeOpCH.index3()).SetData_Number("index4", runeOpCH.index4()).SetData_Usertype("player", player));
			if (result.IsExecSuccessed())
			{
				resultIdx = (int)result.GetData_Number("gridIndex");
				execResult = (int)result.GetData_Number("result");
			}
		}
		break;
	}
	if (resultIdx >= 0)
	{
		PB_RuneOperateSuccessHC resultHC;
		resultHC.set_optype(type);
		resultHC.set_gridindex(resultIdx);
		resultHC.set_result(execResult);
		GetGameNetManagerPtr()->sendToClient(uin, PB_RUNE_OPERATE_SUCCESS_HC, resultHC);
	}
	else
	{
		sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
		return;
	}
}

void MpGameSurviveNetHandler::handleEnchantItemRandom2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto backpack = player->getBackPack();
	if (backpack == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_EnchantItemCH enchantItemCH;
	enchantItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int gindex = enchantItemCH.gridindex();

	auto itemDef = GetDefManagerProxy()->getItemDef(backpack->getGridItem(gindex));
	if (itemDef == nullptr) return;
	auto costDef = GetDefManagerProxy()->getEnchantMentDef(itemDef->StuffType);
	int cost = costDef->Cost;

	int stars = 0;
	auto attr = player->getAttrib();
	auto playerAttr = attr == nullptr ? nullptr : dynamic_cast<PlayerAttrib*>(attr);
	if (playerAttr == nullptr) stars = 0;
	else stars = playerAttr->getExp() / EXP_STAR_RATIO;

	// by huanjiang 判断是否够需要的条件
	if (stars < cost)
	{
		sendError2Client(uin, PB_ERROR_STAR_NOT_ENOUGH);
		return;
	}

	int resultIdx = player->enchantRandom(gindex);

	if (resultIdx >= 0)
	{
		PB_EnchantItemSuccessHC enchantItemSuccessHC;
		enchantItemSuccessHC.set_gridindex(resultIdx);

		GetGameNetManagerPtr()->sendToClient(uin, PB_ENCHANT_ITEM_SUCCESS_HC, enchantItemSuccessHC);
	}
	else
	{
		sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
		return;
	}
}

void MpGameSurviveNetHandler::handleRepairItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto backpack = player->getBackPack();
	if (backpack == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_RepairItemCH repairItemCH;
	repairItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int gindex = repairItemCH.gridindex();
	auto grid = backpack->index2Grid(gindex);

	int toolId = backpack->getGridToolType(gindex);
	if (toolId < 0 || nullptr == grid || grid->def == nullptr || grid->def->ID == 0)
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	int ret = player->repair(gindex);
	if (ret >= 0)
	{
		PB_RepairItemSuccessHC repairItemSuccessHC;
		repairItemSuccessHC.set_gridindex(ret);

		GetGameNetManagerPtr()->sendToClient(uin, PB_REPAIR_ITEM_SUCCESS_HC, repairItemSuccessHC);
	}
}

void MpGameSurviveNetHandler::handleLootItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto backpack = player->getBackPack();
	if (backpack == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_BackPackLootCH backPackLootCH;
	backPackLootCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int gridIndex = backPackLootCH.fromindex();
	int num = backPackLootCH.num();

	if (num <= 0)
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	if (backpack != nullptr)
	{
		int remain = player->lootItem(gridIndex, num);
		// if (remain > 0)
		// {
		// 	sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
		// }
		// else
		// {
		// 	//if (gridIndex == CUSTOMMODEL_START_INDEX + 1 && g_pPlayerCtrl)
		// 		//g_pPlayerCtrl->statisticToWorld(uin, 30016, "", g_pPlayerCtrl->getCurWorldType());
		// }
	}
}

void MpGameSurviveNetHandler::handleStoreItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BackPackStoreCH backPackStoreCH;
	backPackStoreCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int gridIndex = backPackStoreCH.fromindex();
	int num = backPackStoreCH.num();

	auto backpack = player->getBackPack();
	if (backpack != nullptr)
	{
		BackPackGrid* pgrid = backpack->index2Grid(gridIndex);
		if (pgrid)
		{
			int count = pgrid->getNum();
			if (num > count || num <= 0)  // 八成作弊
			{
				jsonxx::Object cheat;
				cheat << "client_num" << num;
				cheat << "server_num" << count;
				cheat << "item_id" << pgrid->getItemID();
				ActionLogger::ErrorLog(uin, player->getOWID(), "cheat_item_storage", cheat);

				num = count;
			}
		}

		int stored = backpack->addStorageItem(gridIndex, num, player->getOpenContainerBaseIndex());
		if (stored < num)
		{
			sendError2Client(uin, PB_ERROR_STORAGE_FULL);
		}
		if (stored > 0 && !(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode()))
		{
			backpack->removeItem(gridIndex, stored);
		}
	}
}

void MpGameSurviveNetHandler::handleCloseContainer2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	player->closeContainer();
}

void MpGameSurviveNetHandler::handleNeedContainerPassword2Host(int uin, const PB_PACKDATA &pkg)
{
   	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_NeedContainerPasswordCH needContainerPasswordCH;
	needContainerPasswordCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos = MPVEC2WCoord(needContainerPasswordCH.pos());

	auto container = dynamic_cast<WorldStorageBoxPassword*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (container)
	{
		//if (Container->m_nPassWord != -1 && Container->m_nPassWord == needContainerPasswordCH.password())
		{
			player->setContainersPassword(blockPos, needContainerPasswordCH.password());  
		}
		player->openContainer(container);
		return ;
	}
	if (needContainerPasswordCH.has_vehicleobjid())
	{
		tdr_longlong objid = needContainerPasswordCH.vehicleobjid();
		ClientActor *actor = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid);
		if (actor) {
			ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
			if (vehicle && vehicle->getVehicleWorld())
			{
				container = dynamic_cast<WorldStorageBoxPassword*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(blockPos));
				if (container)
				{
					player->setContainersPassword(blockPos, needContainerPasswordCH.password());
					player->openContainer(container);
				}
			}
		}
	}
}

void MpGameSurviveNetHandler::handleNpcTrade2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_NpcTradeCH npcTradeCH;
	npcTradeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	player->npcTrade(npcTradeCH.optype(), npcTradeCH.index(), npcTradeCH.watchad() > 0, npcTradeCH.rewardnum());
}

void MpGameSurviveNetHandler::handleApplyPermits2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	MINIW::ScriptVM::game()->callFunction("OnReceiveApplyPermits", "i", uin);
}

void MpGameSurviveNetHandler::handleChat2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
#ifndef IWORLD_SERVER_BUILD
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
#else
		if (GetClientInfoProxy()->getUin() != uin)
		{
			sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
			return;
		}
#endif
	}
	// if (ChatLimiter::getInstance()->isMute(uin))
	// 	return;

	if (player != nullptr && checkErrorNickName(player->getNickname()))
	{
		return;
	}

	PB_ChatCH chatCH;
	chatCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	std::string contentStr = chatCH.content();
	
	// 限制聊天长度
	if (contentStr.length() > AntiSetting::gCheatConfig.SwitchChatContentLength)
	{
		sendError2Client(uin, ERR_CODE_CHAT_LENGTH_LIMIT);
		jsonxx::Object log;
		log << "content" << contentStr;
		log << "size" << contentStr.length();
		ActionLogger::InfoLog(uin, 0, "cheat_err_content", log);
		return;
	}
	{
		static char *buf = nullptr;
		if (!buf)
			buf = new char[AntiSetting::gCheatConfig.SwitchChatContentLength + 1];
		int replaceCount = 0;
		memcpy(buf, contentStr.c_str(),contentStr.length());
		buf[contentStr.length()] = '\0';
		for (int i=0; i < contentStr.length(); i++)
		{
			if (contentStr[i] == '\n' || contentStr[i] == '\r')
			{
				replaceCount++;
				buf[i] = ' ';
			}
		}
		if (replaceCount)
		{
			jsonxx::Object log;
			log << "content" << contentStr;
			log << "replaceCount" << replaceCount;
			log << "replaceContent" << buf;
			ActionLogger::InfoLog(uin, 0, "cheat_err_content", log);
			contentStr = buf;
		}
	}
	// if (ChatLimiter::getInstance()->checkChat(uin, contentStr, chatCH.extend()))
	// 	return;
	// 去除非法的颜色标签
	if (contentStr.find("#") != std::string::npos || contentStr.find("[") != std::string::npos)
	{
		regex pattern2("#c[0-9a-f]{3,6}");
		contentStr = std::regex_replace(contentStr, pattern2, "");

		regex pattern("#[RGBKYWPLb]");
		contentStr = std::regex_replace(contentStr, pattern, "");
		
		contentStr = Rainbow::StringUtil::replace(contentStr, "[", "");
		contentStr = Rainbow::StringUtil::replace(contentStr, "]", "");
		if (contentStr.size() != chatCH.content().size())
		{
			jsonxx::Object log;
			log << "old" << chatCH.content();
			log << "new" << contentStr;
			ActionLogger::InfoLog(uin, 0, "cheat_err_content", log);
		}
	}
#ifdef IWORLD_SERVER_BUILD
	//云服检测聊天
	if (GetClientInfoProxy()->getGameData("game_env") % 10 == 0)
	{
		bool ret = true;
		MINIW::ScriptVM::game()->callFunction("CS_Chat2HostCheck", "iiiss>b", uin, chatCH.targetuin(), chatCH.chattype(), contentStr.c_str(), chatCH.extend().c_str(), &ret);
		if (!ret) return;
	}
#else
	if (GetClientInfoProxy()->getPlayerPermits(uin, CS_PERMIT_MUTE) == 1){
		return;
	}
#endif  //IWORLD_SERVER_BUILD

	if (chatCH.chattype() == 0 && player)
	{
		if (player->getOpenContainerBaseIndex() == SIGNS_START_INDEX)
		{
			std::string temp = contentStr;
			std::string transtr = chatCH.translate();
			if (transtr.compare("") != 0)
			{
				MNSandbox::SandboxResult sandboxResult = MNSandbox::SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WorldStringTranslateMgr_getTransByMultiplekeys", MNSandbox::SandboxContext(nullptr)
					.SetData_Number("type", 14)
					.SetData_String("oldVal", transtr));
				if (sandboxResult.IsExecSuccessed())
				{
					temp = sandboxResult.GetData_String();
				}
			}

			WorldSignsContainer *container = dynamic_cast<WorldSignsContainer *>(player->getBackPack()->getContainer(SIGNS_START_INDEX));
			ContainerComputer* Computer = dynamic_cast<ContainerComputer*>(player->getBackPack()->getContainer(SIGNS_START_INDEX));
			if (container || Computer)
			{
				bool hasFilter = false;
				bool hasDirtyWords = false;
				MINIW::ScriptVM::game()->callFunction("CheckFilterString", "sb>b", temp.c_str(),false,&hasFilter);
				MINIW::ScriptVM::game()->callFunction("C_GetFilterScore", "sb>b", temp.c_str(),false,&hasDirtyWords);
				if (!hasFilter && !hasDirtyWords)
				{
					if (container)
					{
						container->setText(temp.c_str());
					}
					else if (Computer && player->getAttrib())
					{
						Computer->setText(temp.c_str(), player->getPlayerAttrib());
					}
				}
				player->closeContainer();
				return;
			}
		}
		if (contentStr.c_str()[0] == '/')
		{
			bool canUsecmd = IsOpenCmd();
#ifdef IWORLD_SERVER_BUILD
			if (!canUsecmd)
			{
				MINIW::ScriptVM::game()->callFunction("CheckCanServerCmd", "i>b", uin, &canUsecmd);
				if (canUsecmd)
				{
					ActionLogger::SimpleErrLog(uin, 0, "use_cmd", contentStr);
				}
				else
				{
					if (contentStr.find("/sstat") == 0)  // 特殊指令支持所有人使用
					{
						canUsecmd = true;
						jsonxx::Object usecmd;
						usecmd << "cmd" << contentStr;
						ActionLogger::SimpleErrLog(uin, 0, "use_simple_cmd", contentStr);
					}
				}
			}

			Mini::GetHttpReportMgr().luaInterfaceReportStudio(contentStr);   //studio开发者调试接口 haima 20240710

#endif

			if (canUsecmd)
			{
				if (g_WorldMgr->isNewSandboxNodeGame())
				{
					std::istringstream iss(contentStr);
					std::string command;
					iss >> command;

					if (command == "/tp")
					{
						float x, y, z;
						if (iss >> x >> y >> z)
						{
							auto studioPlayer = MNSandbox::GetCurrentPlayersNodeRoot()->GetLocalPlayer()->ToCast<ScenePlayerObject>();
							auto playerCharacter = studioPlayer->GetCharacter()->ToCast<SceneActorObject>();
							playerCharacter->SetWorldPosition(Vector3f(x, y, z));
						}
					}
				}
				else
				{
					player->execCmd(contentStr.c_str() + 1);
					return;
				}
			}

		}
	}

#ifdef IWORLD_UNIVERSE_BUILD
	afterhandleChat2Host(uin, chatCH.targetuin(), contentStr, chatCH.language(), chatCH.translate(), chatCH.extend());
#else
#ifdef IWORLD_SERVER_BUILD
	if (chatCH.wwtk1() == "CloudHost" && chatCH.wwtk2() == m_lifeToken)
	{
		afterhandleChat2Host(uin, chatCH.targetuin(), contentStr, chatCH.language(), chatCH.translate(), chatCH.extend());
	}
	else
#endif
	{
		static bool s_initconf = false;
		static bool if_open_chat3rdCheck = true;
		if (!s_initconf)
		{
			s_initconf = true;
			MINIW::ScriptVM::game()->callFunction("CheckNsVersionIf", "sb>b", "chat3rdCheck", true, &if_open_chat3rdCheck);
		}
		bool needCheckWordWall = if_open_chat3rdCheck;

		size_t contentLen = contentStr.size();
		size_t startIdx = 0;
		if (contentLen > 1 && '&' == contentStr[0])
		{
			startIdx = 1;
		}
		bool isAllNum = true;
		for (size_t index = startIdx; index < contentLen; index++)
		{
			if (!(contentStr[index] >= '0' && contentStr[index] <= '9'))
			{
				isAllNum = false;
				break;
			}
		}

		if (isAllNum)
			needCheckWordWall = false;
		if (!needCheckWordWall) {
			afterhandleChat2Host(uin, chatCH.targetuin(), contentStr, chatCH.language(), chatCH.translate(), chatCH.extend());
		}
		else if (chatCH.wwparam().find((std::ostringstream() << "uin=" << uin).str()) == std::string::npos)
		{
			ClientPlayer* player = uin2Player(uin);
			if (player)
				player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, Rainbow::GetIClientInfo().GetDefStringById(16314).c_str());
		}
#ifdef IWORLD_SERVER_BUILD
		else if (contentStr[0] == '@') // 消息以@开头，发送给智能NPC
		{
			// 找第一个空格，提取NPC名称
			//@sys hi,are you ok?
			auto pos = contentStr.find(' ');
			if (pos == std::string::npos)
			{
				return;
			}
			auto npcName = contentStr.substr(1, pos - 1);
			if (npcName.empty())
			{
				return;
			}
			// 提取内容
			auto content = contentStr.substr(pos + 1);

			GameServerConfig* servercfg = GameServerConfig::getSingleton();
			std::string chatUrl = servercfg->getString("chat_server", "ainpc_chat_server");
			int fromUin = uin;
			string targetNpc = npcName;
			int language = chatCH.language();
			std::string translate = chatCH.translate();
			std::string extend = chatCH.extend();
			// 构造POST数据
			jsonxx::Object postObj;
			postObj << "from_uin" << std::to_string(fromUin)
				<< "from_name" << player->getNickname()
				<< "target_npc" << targetNpc
				<< "language" << language
				<< "translate" << translate
				<< "extend" << extend
				<< "content" << content;

			core::string postData = postObj.json();

			if (player)
				player->onSendChat(content);

			// 添加header
			core::string header = "Content-Type: application/json";

			// 发起POST请求
			Http::GetHttpManager().HttpPost(
				chatUrl,																										  // url
				"",																												  // sec
				header,																											  // header
				postData,																										  // postData
				"",																												  // ca_path
				GetClientGameManagerPtr()->getCurGame(),																		  // user_data
				nullptr,																										  // progress callback
				[this, fromUin, targetNpc, content, language, translate, extend](bool success, Rainbow::Http::WebRequest* task) { // end callback
					if (success)
					{
						Rainbow::Http::MiniHttpRequest* request = static_cast<Rainbow::Http::MiniHttpRequest*>(task);
						MpGameSurvive* task_data = reinterpret_cast<MpGameSurvive*>(request->GetUserData());
						if (!task_data)
							return;

						if (GetClientGameManagerPtr()->getCurGame() != task_data || !GetClientGameManagerPtr()->getCurGame()->getNetHandler())
							return;

						if (GetClientGameManagerPtr()->getCurGame()->getNetHandler() != this)
							return;

						std::string rsp = request->GetResponse();

						bool passed = false;
						jsonxx::Object result;
						if (result.parse(request->GetResponse()))
						{
							int code = result.get<jsonxx::Number>("code");
							if (0 == code)
							{
								std::string reply = result.get<jsonxx::String>("reply");
								std::string msg = result.get<jsonxx::String>("message");
								{
									jsonxx::Object log;
									log << "code" << code;
									log << "reply" << reply;

									ActionLogger::InfoLog(fromUin, 0, "reply", log);
								}

								// this->afterhandleChat2Host(fromUin, 0, reply, language, translate, extend);
								this->afterhandleNpcChat2Host(targetNpc, fromUin, content, reply, language, translate, extend);

								return;
							}
						}
					}
				});
			// 发送回显自己的消息
			if (player)
			{
				PB_ChatHC chatHC;
				chatHC.set_chattype(0);
				chatHC.set_content(content);
				chatHC.set_language(language);
				chatHC.set_translate(translate);
				chatHC.set_speaker(player->getNickname());
				chatHC.set_uin(fromUin);

				GetGameNetManagerPtr()->sendToClient(fromUin, PB_CHAT_HC, chatHC, 0, false);
			}
		}
#endif
		else
		{
			std::string checkWordWallUrl = _GetWordWallReqUrl(contentStr.c_str(), getAllPlayerUinsStr(), uin, "", "", chatCH.wwparam());
			int fromUin = uin;
			int targetUin = chatCH.targetuin();
			int language = chatCH.language();
			std::string translate = chatCH.translate();
			std::string extend = chatCH.extend();
			Http::GetHttpManager().Request(
				checkWordWallUrl, "", GetClientGameManagerPtr()->getCurGame(), 0, nullptr,
				[this, fromUin, targetUin, contentStr, language, translate, extend](bool success, Rainbow::Http::WebRequest* request) {
					MpGameSurvive* task_data = reinterpret_cast<MpGameSurvive*>(request->GetUserData());
					if (!task_data)
						return;

					if (GetClientGameManagerPtr()->getCurGame() != task_data || !GetClientGameManagerPtr()->getCurGame()->getNetHandler())
						return;

					if (GetClientGameManagerPtr()->getCurGame()->getNetHandler() != this)
						return;

					bool passed = false;
					jsonxx::Object result;
					if (result.parse(request->GetResponse()))
					{
						if (result.has<jsonxx::Number>("ret"))
						{
							int code = result.get<jsonxx::Number>("ret");
							if (0 == code)
							{
								this->afterhandleChat2Host(fromUin, targetUin, contentStr, language, translate, extend);
								return;
							}
							else
							{
								std::string failedTips = "";

								if (6 == code)
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(121); //文本内容审核不通过
								else if (25 == code)
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(100218);//实名、手机认证不通过
								else if (26 == code)
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(10643);//手机认证不通过
								else if (27 == code)
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(22037);//实名认证不通过
								else if (4 == code)
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(3893);//网络错误
								else
									failedTips = Rainbow::GetIClientInfo().GetDefStringById(3468).append((std::ostringstream() << "(" << code << ")").str());

								ClientPlayer* player = uin2Player(fromUin);
								if (player)
									player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, failedTips.c_str());
								return;
							}
						}
					}

					ClientPlayer* player = uin2Player(fromUin);
					if (player)
						player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 0, 0, Rainbow::GetIClientInfo().GetDefStringById(37).c_str());
				}
			);
		}

	}
#endif
}

void MpGameSurviveNetHandler::afterhandleNpcChat2Host(const string& npc, const int fromUin, const std::string& contentStr, const std::string& reply, int language, const std::string& translate, const std::string& extend)
{
	ClientPlayer* player = uin2Player(fromUin);
	if (player == nullptr)
	{
#ifndef IWORLD_SERVER_BUILD
		sendError2Client(fromUin, PB_ERROR_OP_NOT_FOUND);
		return;
#else
		if (GetClientInfoProxy()->getUin() != fromUin)
		{
			sendError2Client(fromUin, PB_ERROR_OP_NOT_FOUND);
			return;
		}
#endif
	}

	// 发送npc回复
	{
		PB_ChatHC chatHC;
		chatHC.set_chattype(0);
		chatHC.set_content(reply);
		chatHC.set_language(language);
		chatHC.set_translate(translate);
		chatHC.set_speaker(npc);
		chatHC.set_uin(0);

		GetGameNetManagerPtr()->sendToClient(fromUin, PB_CHAT_HC, chatHC, 0, false);
	}
}

void MpGameSurviveNetHandler::afterhandleChat2Host(const int fromUin, const int targetUin, const std::string& contentStr, int language, const std::string& translate, const std::string& extend)
{
	ClientPlayer* player = uin2Player(fromUin);
	if (player == nullptr)
	{
#ifndef IWORLD_SERVER_BUILD
		sendError2Client(fromUin, PB_ERROR_OP_NOT_FOUND);
		return;
#else
		if (GetClientInfoProxy()->getUin() != fromUin)
		{
			sendError2Client(fromUin, PB_ERROR_OP_NOT_FOUND);
			return;
		}
#endif
	}

	PB_ChatHC chatHC;
	chatHC.set_chattype(0);  // 通过此处出去的消息一律认为是普通聊天, 主机发送系统消息可直接调用 MpGameSurviveNetHandler::sendChat 2022.07.13 by huanglin
	chatHC.set_content(contentStr);
	chatHC.set_language(language);
	chatHC.set_translate(translate);
	//2021-12-20 codeby: wangyang 会员聊天气泡
	chatHC.set_extend(extend);

#ifndef IWORLD_SERVER_BUILD
	chatHC.set_speaker(player->getNickname());
	chatHC.set_uin(player->getUin());
#else
	if (player != nullptr)
	{
		chatHC.set_speaker(player->getNickname());
		chatHC.set_uin(player->getUin());
	}
	else
	{
		chatHC.set_speaker("");
		chatHC.set_uin(0);
	}
#endif

	//发送消息回调
	if (player != nullptr) player->onSendChat(contentStr);

	if (targetUin > 0)
		GetGameNetManagerPtr()->sendToClient(targetUin, PB_CHAT_HC, chatHC, 0, false);
	else
		GetGameNetManagerPtr()->sendBroadCast(PB_CHAT_HC, chatHC, 0, false);
}

//20210914装扮互动发起 cody-by: wangyu
void MpGameSurviveNetHandler::handleActorInvite2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_ActorInviteCH actorInviteCH;
	actorInviteCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PB_ActorInviteHC actorInviteHC;
	actorInviteHC.set_invitetype(actorInviteCH.invitetype());
	actorInviteHC.set_actid(actorInviteCH.actid());
	actorInviteHC.set_targetuin(player->getUin());
	actorInviteHC.set_inviterposx(actorInviteCH.inviterposx());
	actorInviteHC.set_inviterposz(actorInviteCH.inviterposz());

	if (GetGameNetManagerPtr())
	{
		GetGameNetManagerPtr()->sendToClient(actorInviteCH.targetuin(), PB_ACTORINVITE_HC, actorInviteHC, 0, false);
	}
}


void MpGameSurviveNetHandler::handleYMVoice2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_YMVoiceCH yMVoiceCH;
	yMVoiceCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PB_YMVoiceHC yMVoiceHC;
	yMVoiceHC.set_uin(uin);
	yMVoiceHC.set_micswitch(yMVoiceCH.ymmicswitch());
	yMVoiceHC.set_speakerswitch(yMVoiceCH.ymspeakerswitch());
	yMVoiceHC.set_ymmemberid(yMVoiceCH.ymmemberid());
	yMVoiceHC.set_ymmemberrole(yMVoiceCH.ymmemberrole());
	GetGameNetManagerPtr()->sendBroadCast(PB_YM_VOICE_HC, yMVoiceHC, 0, false);
}

void MpGameSurviveNetHandler::handlePlayerMount2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayerMountActorCH playerMountActorCH;
	playerMountActorCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = playerMountActorCH.actorid();
	bool isShapeShift = false;
	if (playerMountActorCH.has_isshapeshift())
		isShapeShift = playerMountActorCH.isshapeshift();

	ClientActor *actor= NULL;
	if (isShapeShift)
	{
		RoleSkinDef *def = GetDefManagerProxy()->getRoleSkinDef((int)objid);
		if (def)
		{
			MINIW::ScriptVM::game()->callFunction("CheckPlayerRideInfo2Summon", "iib", uin, def->ChangeContact[0], true);
		}
	}	
	else
	{
		actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;
		if (actor && actor->sureRiddenComponent() && !actor->sureRiddenComponent()->canBeRided(player))
			return;

		int seatindex = playerMountActorCH.interactblockid();
		if (actor != NULL && player->isDead()) return;

		player->mountActor(actor, false, seatindex);
	}
}

void MpGameSurviveNetHandler::handleGetAccountItems2Host(int uin, const PB_PACKDATA &pkg)
{
	// 协议无使用
	//ClientPlayer *player = checkPlayerByMsg2Host(uin);
	//if (player == NULL) return;

	//PB_GetAccountItemsCH getAccountItemsCH;
	//getAccountItemsCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	//int itemid = getAccountItemsCH.itemid();
	//int num = getAccountItemsCH.num();

	//if (itemid <= 0 || num <= 0)
	//{
	//	sendError2Client(uin, PB_ERROR_WRONG_ARGS);
	//	return;
	//}

	//int cnt = player->gainItems(itemid, num, 1);
	//if (cnt < num) // 背包加了cnt个，一共给了num个，还有num-cnt个掉地上了
	//{
	//	sendError2Client(uin, PB_ERROR_BACKPACK_FULL);
	//}
}

void MpGameSurviveNetHandler::handlePlayerMoveInput2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto RidComp = player->getRiddenComponent();
	if (RidComp && RidComp->getRidingActor())
	{
		PB_PlayerMoveInputCH playerMoveInputCH;
		playerMoveInputCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		LivingLocoMotion *loc = static_cast<LivingLocoMotion *>(player->getLocoMotion());

		loc->m_MoveForward = playerMoveInputCH.moveforward();
		loc->m_MoveStrafing = playerMoveInputCH.movestrafing();
		player->m_MoveForward = loc->m_MoveForward;
		player->m_MoveRight = loc->m_MoveStrafing;
		bool isjumping = playerMoveInputCH.jumping() != 0;
		if (isjumping != player->getLocoMotion()->getJumping())
		{
			player->setJumping(isjumping);
		}
	}
}

void MpGameSurviveNetHandler::handlePlayerSleep2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_PlayerSleepCH playerSleepCH;
	playerSleepCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (playerSleepCH.flags() == 0)
	{
	}
	else if (playerSleepCH.flags() == 1)
	{
		player->wakeUp(true, false, false);
	}
	else if (playerSleepCH.flags() == 2)
	{
		player->standUpFromChair();
	}
	else if (playerSleepCH.flags() == 3)
	{
		player->playerTrySleep();
	}
}

void MpGameSurviveNetHandler::handleHeartBeat2Host(int uin, const PB_PACKDATA &pkg)
{
	// 心跳包不可以在这里新增，如果没有，说明连接已断开，残留的协议不要处理
	auto& heartbeats = GameNetManager::getInstance()->m_HostRecvHeartBeart;
	if (heartbeats.find(uin) == heartbeats.end())
	{
#if !STUDIO_SERVER && defined(IWORLD_SERVER_BUILD)
		ActionLogger::SimpleErrLog(uin, 0, "heartbeat_not_exsit", "MpGameSurviveNetHandler::handleHeartBeat2Host() uin not in heartbeat list");
#endif
		return;
	}

	/* 客户端加速检测   codeby:liusijia 20211015
	 * 1. 服务器下发心跳协议时携带服务器当前时间戳 T1
	 * 2. 客户端接受到心跳协议时，记录T1，记录客户端当前时间戳T2   正常情况下T2 = T1 + 网络延时
	 * 3. 客户端每经过10秒上报心跳包，此时客户端时间戳T3，客户端从收到服务器心跳包经历时间DT = T3 - T2
	 * 4. 客户端上报 T1及T4   其中T4=T1 + DT = T1 + (T3 - T2)
	 * 5. 服务器收到新的客户端心跳包时，服务器当前时间戳为 T5，正常情况下T5 = T4 + 网络延时
	 * 6. 服务器验证客户端上报的T1 及 T5
	 * 如果客户端使用加速软件，则T4会大于T5，且精度在2倍网络延时，精度非常的高
	 */

	 // 客户端连接初期会发多次heartbeat，那个时候应该是没有player的
	PB_HeartBeatCH heartBeatCH;
	if (!heartBeatCH.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleHeartBeat2Host PB_HeartBeatCH");
		return;
	}

	unsigned int curTime = Rainbow::Timer::getSystemTick();

	auto player = uin2Player(uin);
	if (player && player->GetCheatHandler())
	{
		unsigned int serverTime = heartBeatCH.server_time();
		unsigned int clientTime = heartBeatCH.client_time();
		if (player->GetCheatHandler()->CheckHeartBeatCheat(serverTime, clientTime, ""))  // TODO(liusijia) heartBeatCH.aceinfo()
			return;
	}

#ifndef IWORLD_SERVER_BUILD
	// 2022-02-08 codeby:liusijia 如果非云服，则服主不应该有heartbeat，防止其他用户作弊发房主的心跳，过滤一下
	if (uin == GameNetManager::getInstance()->getHostUin())
	{
		return;
	}
#endif

	uint64_t now = Timer::getSystemTick();
	// 特定情况下有延长心跳超时，这里优先使用更长的
	if (GameNetManager::getInstance()->m_HostRecvHeartBeart[uin] < now)
		GameNetManager::getInstance()->m_HostRecvHeartBeart[uin] = now;

	// beatcode需要回传
	heartBeatCH.set_server_time(curTime);
	heartBeatCH.clear_client_time();

	GetGameNetManagerPtr()->sendToClient(uin, PB_HEARTBEAT_HC, heartBeatCH);
}

void MpGameSurviveNetHandler::handleGunSpread2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_GunInfoCH gunInfoCH;
	gunInfoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	player->setGunInfo(gunInfoCH.curspread(), gunInfoCH.curjaw(), gunInfoCH.curpitch(), Rainbow::Vector3f((float)gunInfoCH.curpos().x(), (float)gunInfoCH.curpos().y(), (float)gunInfoCH.curpos().z()));
}

void MpGameSurviveNetHandler::handleSyncSetInfo2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_SetInfoCH setInfoCH;
	setInfoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	player->setSelectedColor(setInfoCH.color());
}

void MpGameSurviveNetHandler::handleGunDoReload2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_GunDoReloadCH gunDoReloadCH;
	gunDoReloadCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	bool isNewGun = gunDoReloadCH.iscustomgun();
	//新枪暂时无防作弊检测，后续可能会加 TODO
	if (!isNewGun && player->GetCheatHandler() && !player->GetCheatHandler()->checkGunReload(gunDoReloadCH.usetick()))
		return;

	if (gunDoReloadCH.nocheck())
	{
		player->doReloadWithoutCheck(gunDoReloadCH.num(), gunDoReloadCH.curshortcut());
	}
	else
	{
		player->doReload(gunDoReloadCH.bulletid(), gunDoReloadCH.num(), isNewGun, gunDoReloadCH.curshortcut());
	}
}

void MpGameSurviveNetHandler::handleTrainMove2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	if (player->getWorld() == NULL) return;

	PB_TrainMoveCH trainMoveCH;
	trainMoveCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ActorTrainCar *car = dynamic_cast<ActorTrainCar *>(player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(trainMoveCH.objid()));
	if (car == NULL) return;
	auto RidComp = car->sureRiddenComponent();
	if (RidComp && RidComp->getRiddenByActorID() != player->getObjId()) return;
	//if (!car->isHead()) return;

	car->onSetRailFromPlayerCtrl(MPVEC2WCoord(trainMoveCH.railknot()), trainMoveCH.outindex(), trainMoveCH.curvet(), trainMoveCH.carreverse(), trainMoveCH.motionx(), trainMoveCH.motiony(), trainMoveCH.motionz());
}

void MpGameSurviveNetHandler::handleSyncGridUserData2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_ItemGridUserData itemGridUserData;
	itemGridUserData.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(itemGridUserData.uin());
	if (player != nullptr)
	{
		if(itemGridUserData.type() && itemGridUserData.type() == 1)
			player->writeInstruction(itemGridUserData.gridindex(), std::string(itemGridUserData.userdatastr()));
		if (itemGridUserData.type() && itemGridUserData.type() == 2)//相册
			player->writePolaroidAlbumInfo(itemGridUserData.gridindex(), std::string(itemGridUserData.userdatastr()));
		else
			player->writeLetters(itemGridUserData.gridindex(), std::string(itemGridUserData.userdatastr()));

		// 不使用下面的方式同步UserDataStr给其他客机，只在客机使用时才同步

		//if (player->writeLetters(msg.Body.SyncGridUserData2Host.GridIndex, std::string(msg.Body.SyncGridUserData2Host.UserDataStr)))
		//{
		//	MPMSGPKG pkg;

		//	pkg.Head.HeadLen = pkg.Head.BodyLen = 0;
		//	pkg.Head.MsgCode = OW_SYNC_GRIDUSERDATA_HC;

		//	pkg.Body.SyncGridUserData2Client.Uin = msg.Body.SyncGridUserData2Host.Uin;
		//	pkg.Body.SyncGridUserData2Client.GridIndex = msg.Body.SyncGridUserData2Host.GridIndex;
		//	strcpy(pkg.Body.SyncGridUserData2Client.UserDataStr, msg.Body.SyncGridUserData2Host.UserDataStr);
		//}
	}
}

void MpGameSurviveNetHandler::handleYMChangeRole2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_YMChangeRoleCH ymChangeRoleCH;
	ymChangeRoleCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("RespYMChangeRole", "i", ymChangeRoleCH.changeresult());
}

void MpGameSurviveNetHandler::handleSpectatorMode2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SetSpectatorModeCH setSpectatorModeCH;
	setSpectatorModeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (setSpectatorModeCH.uin() && setSpectatorModeCH.uin() != uin)
	{
		jsonxx::Object log;
		log << "uin" << setSpectatorModeCH.uin();
		log << "mode" << setSpectatorModeCH.spectatormode();
		
		ActionLogger::InfoLog(uin, 0, "cheat_spectator_target", log);
	}
	ClientPlayer *player = uin2Player(uin);

	if (player && GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr && GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE))
	{
		PLAYER_SPECTATOR_MODE mode = (PLAYER_SPECTATOR_MODE)(int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE);
		// 按照地图设置的观战模式来, 不能随意更改
		player->ClientPlayer::setSpectatorMode(mode);
		player->revive(0);

		PB_SetSpectatorModeHC setSpectatorModeHC;
		setSpectatorModeHC.set_uin(uin);
		setSpectatorModeHC.set_spectatormode(mode);

		GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORMODE_HC, setSpectatorModeHC);

		GetWorldManagerPtr()->signChangedToSync(uin, BIS_INSPECTATOR);
	}
}

void MpGameSurviveNetHandler::handleSpectatorType2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SetSpectatorTypeCH setSpectatorTypeCH;
	setSpectatorTypeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (setSpectatorTypeCH.uin() && setSpectatorTypeCH.uin() != uin)
	{
		jsonxx::Object log;
		log << "uin" << setSpectatorTypeCH.uin();
		log << "type" << setSpectatorTypeCH.spectatortype();
		
		ActionLogger::InfoLog(uin, 0, "cheat_spectator_target", log);
	}
	ClientPlayer *player = uin2Player(uin);
	if (player && GetWorldManagerPtr()
		&& (player->getSpectatorMode() == SPECTATOR_MODE_JUDGE || (GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWMODE)
		&& GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_SET_VIEWTYPE) == 2)))
	{
		player->ClientPlayer::setSpectatorType((PLAYER_SPECTATOR_TYPE)setSpectatorTypeCH.spectatortype());

		PB_SetSpectatorModeHC setSpectatorModeHC;
		setSpectatorModeHC.set_uin(uin);
		setSpectatorModeHC.set_spectatormode(setSpectatorTypeCH.spectatortype());

		GetGameNetManagerPtr()->sendBroadCast(PB_SET_SPECTATORTYPE_HC, setSpectatorModeHC);
	}
}

void MpGameSurviveNetHandler::handleSetSpetatorPlayer2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		return;
	}

	PB_SetSpectatorPlayerCH setSpectatorPlayerCH;
	setSpectatorPlayerCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!player->isInSpectatorMode() || uin != setSpectatorPlayerCH.spectatoruin())
	{
		return;
	}

	ClientPlayer* target = uin2Player(setSpectatorPlayerCH.tospectatoruin());;
	if (target)
	{
		target->setSpectatorUin(uin);

		PB_SetSpectatorPlayerHC setSpectatorPlayerHC;
		setSpectatorPlayerHC.set_spectatoruin(uin);
		setSpectatorPlayerHC.set_tospectatoruin(target->getUin());

		GetGameNetManagerPtr()->sendToClient(target->getUin(), PB_SET_SPECTATOR_PLAYER_HC, setSpectatorPlayerHC);
	}
}

void MpGameSurviveNetHandler::handleActorTeleport2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_ActorTeleportCH actorTeleportCH;
	actorTeleportCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

#ifdef IWORLD_SERVER_BUILD
	/** 
	 * 1. 防止客机要求传送其它玩家, 破坏其它玩家游戏
	 * 2. 防止客机要求随意传送自己
	 * 2024.02.06 by huanglin
	*/
	int objid = (int)actorTeleportCH.objid();
	if (objid != uin)
	{
		jsonxx::Object log;
		log << "dest_uin" << objid;
		
		jsonxx::Array arr;
		arr << actorTeleportCH.targetpos().x() << actorTeleportCH.targetpos().y() << actorTeleportCH.targetpos().z();
		log << "dest_pos" << arr;
		ActionLogger::InfoLog(uin, 0, "cheat_spectator_teleport", log);
		return;
	}
#endif
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		return;
	}
	if (!player->isInSpectatorMode())
	{  // 此协议为观战模式专用, 需要检测模式
		return;
	}
	player->gotoPos(player->getWorld(), MPVEC2WCoord(actorTeleportCH.targetpos()), true);
}

void MpGameSurviveNetHandler::handleSetPlayerModelAni2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SetPlayerModelAniCH setPlayerModelAniCH;
	setPlayerModelAniCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(setPlayerModelAniCH.spectatoruin());
	if (player)
	{
		PB_SetPlayerModelAniHC setPlayerModelAniHC;
		setPlayerModelAniHC.set_spectatoruin(setPlayerModelAniCH.spectatoruin());
		setPlayerModelAniHC.set_tospectatoruin(setPlayerModelAniCH.tospectatoruin());
		setPlayerModelAniHC.set_modelanimaltype(setPlayerModelAniCH.modelanimaltype());
		setPlayerModelAniHC.set_modelanimalext(setPlayerModelAniCH.modelanimalext());

		GetGameNetManagerPtr()->sendToClient(setPlayerModelAniCH.spectatoruin(), PB_SET_PLAYER_MODEL_ANI_HC, setPlayerModelAniHC, 0, false);
	}
}

void MpGameSurviveNetHandler::handleSetMyViewModeToSpectator2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SendMyViewmodeToSpectatorCH sendMyViewmodeToSpectatorCH;
	sendMyViewmodeToSpectatorCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(sendMyViewmodeToSpectatorCH.spectatoruin());
	if (player)
	{
		PB_SendMyViewmodeToSpectatorHC sendMyViewmodeToSpectatorHC;
		sendMyViewmodeToSpectatorHC.set_spectatoruin(sendMyViewmodeToSpectatorCH.spectatoruin());
		sendMyViewmodeToSpectatorHC.set_tospectatoruin(sendMyViewmodeToSpectatorCH.tospectatoruin());
		sendMyViewmodeToSpectatorHC.set_myviewmode(sendMyViewmodeToSpectatorCH.myviewmode());

		GetGameNetManagerPtr()->sendToClient(sendMyViewmodeToSpectatorCH.spectatoruin(), PB_SEND_VIEWMODE_SPECTATOR_HC, sendMyViewmodeToSpectatorHC, 0, false);
	} 
}

void MpGameSurviveNetHandler::handleSetBobblingToSpectator2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SetBobbingToSpectatorCH setBobbingCH;
	setBobbingCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(setBobbingCH.spectatoruin());
	if (player)
	{
		if(player == g_pPlayerCtrl)
		{
			 g_pPlayerCtrl->getCamera()->setBobbing(setBobbingCH.bobbing());
		}
		else
		{
			PB_SetBobbingToSpectatorHC setBobbingHC;
			setBobbingHC.set_spectatoruin(setBobbingCH.spectatoruin());
			setBobbingHC.set_tospectatoruin(setBobbingCH.tospectatoruin());
			setBobbingHC.set_bobbing(setBobbingCH.bobbing());

			GetGameNetManagerPtr()->sendToClient(setBobbingCH.spectatoruin(), PB_SET_BOBBING_SPECTATOR_HC, setBobbingHC, 0, false);
		}
	}
}

void MpGameSurviveNetHandler::handleBallOperate2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_BallOperateCH ballOperateCH;
	ballOperateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	if (ballOperateCH.type() == PLAYEROP_CATCH_BALL)
	{
#ifdef IWORLD_SERVER_BUILD
		if (ballOperateCH.has_actorid())
		{
			jsonxx::Object cheat;
			cheat << "dest" << ballOperateCH.actorid();
			ActionLogger::InfoLog(uin, 0, "cheat_catch_ball", cheat);
		}
		return;
#endif
		if (!AntiSetting::IsSwitchOff(AntiSetting::gCheatConfig.SwitchCatchBallProtocol))
		{// 此协议已经不会进入 2022.12.19 by huanglin
			auto ball = ballOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(ballOperateCH.actorid());
			FootballStateAction::doCatchBall(player, ball);
		}
		//player->doCatchBall(ball);
	}
	else if (ballOperateCH.type() == PLAYEROP_SHOOT || ballOperateCH.type() == PLAYEROP_PASS_BALL)
	{
		auto ball = ballOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(ballOperateCH.actorid());
		FootballStateAction::doKickBall(player, ballOperateCH.type(), (float)ballOperateCH.extenddata(), ball);
		//player->doKickBall(ballOperateCH.type(), (float)ballOperateCH.extenddata(), ball);
	}
	else if (ballOperateCH.type() == PLAYEROP_TACKLE)
	{
		FootballStateAction::doTackle(player);
		//player->doTackle();
	}
	else if (ballOperateCH.type() == PLAYEROP_TACKLE_END)
	{
		FootballStateAction::endTackle(player);
		//player->endTackle();
	}
	else if (ballOperateCH.type() == PLAYEROP_BALL_CHARGE_BEGIN)
	{
		FootballStateAction::beginChargeKickBall(player);
		//player->beginChargeKickBall();
	}
}

void MpGameSurviveNetHandler::handleBasketBallOperator2Host(int uin, const PB_PACKDATA& pkg)
{
	PB_BasketBallOperate basketballOperatorCH;
	basketballOperatorCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;
	auto ball = basketballOperatorCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(basketballOperatorCH.actorid());
	WCoord target_pos = MPVEC2WCoord(basketballOperatorCH.pos());
	if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_PASS)
	{
		BasketballStateAction::basketBallOPStart(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPStart(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_PASS_END)
	{
		BasketballStateAction::doKickBasketBall(player, basketballOperatorCH.type(), (BasketballFall)basketballOperatorCH.fallresult(), (float)basketballOperatorCH.extenddata(), ball, target_pos, basketballOperatorCH.yaw(), basketballOperatorCH.pitch(),basketballOperatorCH.selectedactorid());
		//player->doKickBasketBall(basketballOperatorCH.type(), (BasketballFall)basketballOperatorCH.fallresult(), (float)basketballOperatorCH.extenddata(), ball, target_pos, basketballOperatorCH.yaw(), basketballOperatorCH.pitch(),basketballOperatorCH.selectedactorid());
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_SHOOT)
	{
		BasketballStateAction::basketBallOPStart(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPStart(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_SHOOT_END)
	{
		BasketballStateAction::doKickBasketBall(player, basketballOperatorCH.type(), (BasketballFall)basketballOperatorCH.fallresult(), (float)basketballOperatorCH.extenddata(), ball, target_pos, basketballOperatorCH.yaw(), basketballOperatorCH.pitch(),basketballOperatorCH.selectedactorid());
		//player->doKickBasketBall(basketballOperatorCH.type(), (BasketballFall)basketballOperatorCH.fallresult(), (float)basketballOperatorCH.extenddata(), ball, target_pos, basketballOperatorCH.yaw(), basketballOperatorCH.pitch(),basketballOperatorCH.selectedactorid());
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_OBSTRUCT)
	{
		BasketballStateAction::basketBallOPStart(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPStart(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_OBSTRUCT_END)
	{
		BasketballStateAction::basketBallOPEnd(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPEnd(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_GRAB)
	{
		BasketballStateAction::basketBallOPStart(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPStart(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_GRAB_END)
	{
		BasketballStateAction::doBasketBallGrabMove(player, basketballOperatorCH.yaw());
		BasketballStateAction::basketBallOPEnd(player, basketballOperatorCH.type(), NULL);
		//player->basketBallOPEnd(basketballOperatorCH.type(), NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_BLOCK_SHOT)
	{
		BasketballStateAction::basketBallOPStart(player, PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
		//player->basketBallOPStart(PLAYEROP_BASKETBALL_BLOCK_SHOT, NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_BLOCK_SHOT_END)
	{
		BasketballStateAction::basketBallOPEnd(player, PLAYEROP_BASKETBALL_BLOCK_SHOT_END, NULL);
		//player->basketBallOPEnd(PLAYEROP_BASKETBALL_BLOCK_SHOT_END, NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_DRIBBLERUN)
	{
		auto ball = basketballOperatorCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(basketballOperatorCH.actorid());
		BasketballStateAction::doRunDribbleRunBasketBall(player, ball);
		//player->doRunDribbleRunBasketBall(ball);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_DRIBBLERUN_END)
	{
		BasketballStateAction::basketBallOPEnd(player, PLAYEROP_BASKETBALL_DRIBBLERUN_END, NULL);
		//player->basketBallOPEnd(PLAYEROP_BASKETBALL_DRIBBLERUN_END, NULL);
	}
	else if (basketballOperatorCH.type() == PLAYEROP_BASKETBALL_CHARGE_BEGIN)
	{
		BasketballStateAction::beginChargeThrowBall(player);
		//player->beginChargeThrowBall();
	}
}

void MpGameSurviveNetHandler::handlePushSnowBallOperate2Host(int uin, const PB_PACKDATA& pkg)
{
	PB_PushSnowBallOperateCH pushsnowballOperateCH;
	pushsnowballOperateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	if (pushsnowballOperateCH.type() == PLAYEROP_PUSHSNOWBALL_SHOOT)
	{
		auto ball = pushsnowballOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(pushsnowballOperateCH.actorid());
		PushSnowBallStateAction::doKickBall(player, pushsnowballOperateCH.type(), (float)pushsnowballOperateCH.extenddata(), ball);
	}
	else if (pushsnowballOperateCH.type() == PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN)
	{
		PushSnowBallStateAction::beginChargeKickBall(player);
	}
	else if (pushsnowballOperateCH.type() == PLAYEROP_PUSHSNOWBALL_MAKEBALL && pushsnowballOperateCH.has_targetpos())
	{
		auto target = pushsnowballOperateCH.targetpos();
		WCoord pos(target.x(), target.y(), target.z());
		PushSnowBallStateAction::doMakeSnowBall(player, pos);
	}
	else if (pushsnowballOperateCH.type() == PLAYEROP_PUSHSNOWBALL_MAKEMAN)
	{
		auto catchball = pushsnowballOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(pushsnowballOperateCH.actorid());
		auto ball = pushsnowballOperateCH.extenddata() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(pushsnowballOperateCH.extenddata());
		PushSnowBallStateAction::doMakeSnowMan(player, catchball, ball);
	}
	else if (pushsnowballOperateCH.type() == PLAYEROP_PUSHSNOWBALL_JUMP)
	{
		PushSnowBallStateAction::doJump(player);
	}
}

void MpGameSurviveNetHandler::handleGravityOperate2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_GravityOperateCH gravityOperateCH;
	gravityOperateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	if (gravityOperateCH.type() == PLAYEROP_CATCH_GRAVITYACTOR)
	{
		auto actor = gravityOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(gravityOperateCH.actorid());
		// 物理技能抓取配置functiondef->oper_id == 11对应的distance和high
		// functiondef->func.physicsCatchfun.distance, functiondef->func.physicsCatchfun.high
		// 这里就不取表了 有些麻烦
		player->doCatchGravityActor(actor,250,150);
	}
	else if (gravityOperateCH.type() == PLAYEROP_THROW_GRAVITYACTOR)
	{
		auto actor = gravityOperateCH.actorid() == 0 ? NULL : player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(gravityOperateCH.actorid());
		player->doThrowGravityActor(gravityOperateCH.type(), (float)gravityOperateCH.extenddata(), actor);
	}
	else if (gravityOperateCH.type() == PLAYEROP_GRAVITY_CHARGE_BEGIN)
	{
		player->beginChargeThrowGravityActor();
	}
}

void MpGameSurviveNetHandler::handleRocketTeleport2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_RocketTeleportCH rocketTeleportCH;
	rocketTeleportCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) return;

	auto RidComp = player->getRiddenComponent();
	ActorRocket *rocket = NULL;
	if (RidComp)
	{
		rocket = dynamic_cast<ActorRocket*>(RidComp->getRidingActor());
	}
	if (rocket)
	{
		rocket->changeState(HOLD_STILL);
		player->teleportByRocket(rocketTeleportCH.mapid());
		rocket->setNeedClear();
	}
}

void MpGameSurviveNetHandler::handlePlayAct2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_PlayActCH playActCH;
	playActCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player && player->getBody())
	{
		auto actorBody = player->getBody();
		int actid = playActCH.actid();
		int actidTrigger = playActCH.actidtrigger();

		if ((actorBody->getActID() > 0 && actid == actorBody->getActID())
			|| (actorBody->getActTriggerID() > 0 && actidTrigger == actorBody->getActTriggerID()))
		{
			if (auto defTrigger = GetDefManagerProxy()->getTriggerActDef(actidTrigger))
			{
				// 触发器动作
				actorBody->setActTrigger(actidTrigger);

				if (actorBody->getCurAnim(0) == SEQ_PLAY_ACT)
				    actorBody->setCurAnim(-1, 0);
				actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
				player->stopMotion(30000);
				player->playMotion(defTrigger->Effect.c_str(), 30000);
			}
			else
			{
				// 普通动作
				actorBody->setAct(actid);

				if (actorBody->getCurAnim(0) == SEQ_PLAY_ACT)
				    actorBody->setCurAnim(-1, 0);
				actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
				player->stopMotion(30000);
				if (auto def = GetDefManagerProxy()->getPlayActDef(actid))
				{
					player->playMotion(def->Effect.c_str(), 30000);
				}
			}
		}

		if(player != NULL && player->getWorld() != NULL) {
			player->onPlayActEvent(actid);
			PB_PlayActHC playActHC;
			playActHC.set_uin(player->getUin());
			playActHC.set_actid(actid);
			playActHC.set_actidtrigger(actidTrigger);
			player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_ACT_HC, playActHC, player);
		}
	}
}


//2021-09-14 codeby:chenwei 处理主机装扮互动消息
void MpGameSurviveNetHandler::handlePlaySkinAct2Host(int uin, const PB_PACKDATA &pkg) //PB_PLAYER_SKIN_ACT_CH
{
	PB_PlaySkinActCH playSkinActCH;
	playSkinActCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);

	//20210929 codeby:chenwei 新增邀请和接收方uin变量
	int inviteUin = playSkinActCH.inviteuin();
	int acceptUin = playSkinActCH.acceptuin();

	//2021-09-17 codeby:chenwei 添加健壮性判断、修改变量名
	ClientPlayer *invitePlayer = uin2Player(inviteUin);
	ClientPlayer *acceptPlayer = uin2Player(acceptUin);
	if (invitePlayer == NULL || acceptPlayer == NULL)
		return;

	//20210929 codeby:chenwei 新增body变量和健壮性判断
	ActorBody *inviteBody = invitePlayer->getBody();
	ActorBody *acceptBody = acceptPlayer->getBody();
	if (NULL == inviteBody || NULL == acceptBody)
		return;

	//20211009 codeby:chenwei 停止老舞伴的动画
	int act = playSkinActCH.actid();

	//20211012 codeby:chenwei 非邀请方和被邀请方的老舞伴才停止动画,并优化接口参数
	auto stopSkinAct = [this, inviteUin, acceptUin, act](int uin) {
		if (uin > 0 && uin != inviteUin && uin != acceptUin)
		{
			ClientPlayer *player = uin2Player(uin);
			if (player && player->getBody())
			{
				ActorBody *body = player->getBody();
				const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(body->getActID());
				if (def)
				{
					auto sound = player->getSoundComponent();
					if (sound)
					{
						sound->stopSoundByTrigger(def->Sound.c_str(), false);
					}
				}
				body->setAct(-1);
				body->setCurAnim(-1, 0);
				player->stopMotion(30000);
				player->setSkinPartnerUin(NULL);
			}
		}
	};
	stopSkinAct(invitePlayer->getSkinPartnerUin());
	stopSkinAct(acceptPlayer->getSkinPartnerUin());

	//20210929 codeby:chenwei 优化客机邀请方播放动作
	auto func_setPlayerAct = [act](ActorBody *actorBody, bool isSideAct) {
		if (act > 0)
		{
			actorBody->setSideAct(isSideAct);
			actorBody->setAct(act);
			actorBody->setCurAnim(-1, 0);
			actorBody->setCurAnim(SEQ_PLAY_ACT, 0);
			actorBody->playSkinActMotion(act, 30000); //20210929 codeby:chenwei 播放装扮互动动作特效
			actorBody->setAnimSeq(actorBody->getAnimSeq() + 1);
		}
	};

	//20210927 codeby:chenwei 添加接收方移动到邀请方指定位置进行动作播放
	WCoord invitePos = invitePlayer->getPosition();
	WCoord acceptPointPos = invitePlayer->getSkinActTargetPos(act, invitePos); //20210928 codeby:chenwei 抽取获取接收方位置到方法 20211008 codeby:chenwei 修改方法名

	//移动在指定位置
	if (acceptPlayer->getLocoMotion() && invitePlayer->getLocoMotion())
	{
		Rainbow::Vector3f  dir = invitePos.toVector3();
		dir = MINIW::Normalize(dir);
		float yaw;
		float pitch;
		Direction2PitchYaw(&yaw, &pitch, dir);

		//acceptPlayer->getLocoMotion()->setMoveDir(dir);
		//acceptPlayer->setMoveControlYaw(invitePlayer->getLocoMotion()->m_RotateYaw);
		//20240724 codeby:wangyu 区分新旧互动动作 旧互动动作有主副动作 位置固定
		if ((act < 10000))
		{
			acceptPlayer->gotoPos(acceptPlayer->getWorld(), acceptPointPos, true);
// 			if (acceptPlayer->isNewMoveSyncSwitchOn())
// 			{
// 				acceptPlayer->setMoveControlYaw(yaw);
// 				acceptPlayer->setMoveControlPitch(pitch);
// 			}
// 			else
// 			{
// 				acceptPlayer->setFaceYaw(yaw);
// 			}
		}
		//acceptPlayer->getLocoMotion()->gotoPosition(acceptPointPos, invitePlayer->getLocoMotion()->m_RotateYaw, 0.0f);
		//acceptPlayer->getLocoMotion()->m_TickPosition.beginTick(acceptPointPos);
	}

	//20211008 codeby:chenwei 修改接口位置
	invitePlayer->setSkinPartnerUin(acceptUin);
	acceptPlayer->setSkinPartnerUin(inviteUin); //20210927 codeby：chenwei 设置舞伴uin

	//20210927 codeby:chenwei 如果是自己是受邀方则修改摄像机方位和移动到对应位置
	if (g_pPlayerCtrl && acceptPlayer->getUin() == g_pPlayerCtrl->getUin())
	{
		g_pPlayerCtrl->m_pCamera->setRotate(invitePlayer->getLocoMotion()->m_RotateYaw - 180.0f, 0.0f);
		g_pPlayerCtrl->switchSkinActView();
	}

	//20211009 codeby:chenwei 播放音效
	const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(act);
	if (def)
	{
		auto sound1= invitePlayer->getSoundComponent();
		if (sound1)
		{
			sound1->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, true);
		}
		auto sound2 = acceptPlayer->getSoundComponent();
		if (sound2)
		{
			sound2->playSoundByTrigger(def->Sound.c_str(), 1.0, 1.0, false, true);
		}
	}

	//20210929 codeby:chenwei 优化接口参数和设置舞伴uin
	func_setPlayerAct(acceptBody, true);

	if (player->getWorld() != NULL) {
		PB_PlaySkinActHC playSkinActHC;
		playSkinActHC.set_actid(act);
		playSkinActHC.set_inviteuin(invitePlayer->getUin());
		playSkinActHC.set_acceptuin(acceptPlayer->getUin());
		player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYER_SKIN_ACT_HC, playSkinActHC, player);
	}


}

//2021-10-08 codeby:chenwei 处理邀请方和被邀请方动作设置
void MpGameSurviveNetHandler::handleStopSkinAct2Host(int uin, const PB_PACKDATA &pkg) //PB_ACTOR_STOP_SKIN_ACT_CH
{
	PB_ActorStopSkinActCH actorStopSkinActCH;
	actorStopSkinActCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto stopSkinAct = [this](int uin)
	{
		ClientPlayer *player = uin2Player(uin);

		if (player && player->getBody())
		{
			ActorBody* playerBody = player->getBody();
			int oldAct = playerBody->getActID();
			playerBody->setAct(-1);
			playerBody->setCurAnim(-1, 0);
			player->stopMotion(30000);
			player->setSkinPartnerUin(NULL);

			const PlayActDef *def = GetDefManagerProxy()->getPlayActDef(oldAct);
			if (def)
			{
				auto sound = player->getSoundComponent();
				if (sound)
				{
					sound->stopSoundByTrigger(def->Sound.c_str(), true);
				}
			}
		}

		//20211012 codeby:chenwei 停止动画时恢复视角
		if (g_pPlayerCtrl && uin == g_pPlayerCtrl->getUin())
		{
			g_pPlayerCtrl->recoverActView();
		}
	};

	stopSkinAct(actorStopSkinActCH.actorid1());
	stopSkinAct(actorStopSkinActCH.actorid2());

	ClientPlayer *player = uin2Player(uin);
	if (player)
	{
		PB_ActorStopSkinActHC actorStopSkinActHC;
		actorStopSkinActHC.set_actorid1(actorStopSkinActCH.actorid1());
		actorStopSkinActHC.set_actorid2(actorStopSkinActCH.actorid2());
		player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_STOP_SKIN_ACT_HC, actorStopSkinActHC, player);
	}
}


void MpGameSurviveNetHandler::handlePlayerSkin2Host(int uin, const PB_PACKDATA &pkg)
{
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (nullptr == worldMgr)
	{
		LOG_INFO("WorldMgr == NULL");
		return;
	}
	PB_PlayerBriefInfo playerBriefInfo;
	playerBriefInfo.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// 防止客机修改其他人的皮肤造型等, 此结构体会被原样转发给其它客机, 2024.05.10 by huanglin
	if (playerBriefInfo.uin() != uin)
		return;
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player)
	{
		int playerindex = playerBriefInfo.playerindex();
		int skinId = PlayerIndex2Skin(playerindex);

		if (player->GetCheatHandler())
		{
			bool canuse = player->GetCheatHandler()->CheckSkinUse(skinId);
			if (!canuse)
			{
				jsonxx::Object cheat;
				cheat << "client_skinId" << skinId;
				cheat << "client_player_index" << playerindex;
				if (player->getBody())
					cheat << "server_old_player_index" << player->getBody()->getPlayerIndex();
				ActionLogger::ErrorLog(player->getUin(), player->getOWID(), "cheat_skin_use", cheat);
				return;
			}
		}

		char custom[256] = "";
		char custommodel[256] = "";
		MyStringCpy(custom, sizeof(custom), playerBriefInfo.customjson().c_str());
		MyStringCpy(custommodel, sizeof(custommodel), playerBriefInfo.custommodel().c_str());

		player->changePlayerModel(playerBriefInfo.playerindex(), 0, custom, custommodel);
		player->m_AccoutSkinID = playerBriefInfo.acctountskinid();
		player->m_originSkinId = PlayerIndex2Skin(playerBriefInfo.playerindex());
		player->m_strOriginCustomJson = playerBriefInfo.custommodel();
		player->restoreSkinByReason(4);//4 表示使用商城等方式改变装扮
		if (player->getBody())
		{
			player->getBody()->setPlayerIndex(playerBriefInfo.playerindex());
			player->getBody()->setCustomSkins(custom);
			auto nickName = playerBriefInfo.nickname().c_str();
			auto teamValue = playerBriefInfo.teamid();
			player->getBody()->setDispayName(nickName, teamValue);
			// 同步给其他客机
			player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOT_SET_CUSTOM_HC, playerBriefInfo, player);
		}
	}
}

void MpGameSurviveNetHandler::handleCloseDialogue2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	player->closePlotDialogue();
}

void MpGameSurviveNetHandler::handleAnswerTask2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_AnswerTaskCH answerTaskCH;
	answerTaskCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	if (answerTaskCH.type() == 1)
		player->addTask(answerTaskCH.taskid(), answerTaskCH.plotid());
	else if (answerTaskCH.type() == 2)
		player->removeTask(answerTaskCH.taskid());
}

void MpGameSurviveNetHandler::handleCompleteTask2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_CompleteTaskCH completeTaskCH;
	completeTaskCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	player->completeTask(completeTaskCH.taskid());
}

void MpGameSurviveNetHandler::handleCreateBlueprint2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_CreateBlueprintHC createBlueprintCH;
	createBlueprintCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		//sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	WCoord blockPos = MPVEC2WCoord(createBlueprintCH.point());
	std::string sheetname = createBlueprintCH.sheetname();
	auto Container = dynamic_cast<WorldContainer*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (Container)
	{
		Container->StartWorking(sheetname.c_str(), player->getNickname());
	}
}

void MpGameSurviveNetHandler::handleSaveTombStone2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_SaveTombStoneHC saveTombStoneHC;
	saveTombStoneHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		return;
	}

	WCoord blockPos = MPVEC2WCoord(saveTombStoneHC.point());	
	auto Container = dynamic_cast<TombStoneContainer*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (Container)
	{
		std::string title = saveTombStoneHC.title();
		Container->setTombStoneTitle(title);
	}
}

void MpGameSurviveNetHandler::handleDeformationSkin2Host(int uin, const PB_PACKDATA & pkg)
{
	PB_PlayerDeformationSkinCH deformationSkin;
	deformationSkin.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	RoleSkinDef* def = GetDefManagerProxy()->getRoleSkinDef( player->getSkinID());
	player->DeformationSkin(def);
}

void MpGameSurviveNetHandler::handleResetDeformation2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_PlayerResetDeformationCH resetMsg;
	resetMsg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	ClientPlayer* actor = dynamic_cast<ClientPlayer*> (player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(resetMsg.actorid()));
	if (actor)
		actor->resetDeformation(2);
}

void MpGameSurviveNetHandler::handleRestoreDefomation2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_PlayerRestoreTransformSkinCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	player->restoreSkin();
}

void MpGameSurviveNetHandler::handleBluePrintPreBlock2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_BluePrintPreBlockCH bluePrintPreBlockCH;
	bluePrintPreBlockCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	WCoord blockPos = MPVEC2WCoord(bluePrintPreBlockCH.blockpos());
	auto container = dynamic_cast<ContainerBuildBluePrint*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (container)
	{
		container->syncPreBlocks(uin);
	}
}

void MpGameSurviveNetHandler::handleMakeCustomModel2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_MakeCustomModelCH makeCustomModelCH;
	makeCustomModelCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	WCoord blockPos = MPVEC2WCoord(makeCustomModelCH.point());
	auto Container = dynamic_cast<WorldContainer*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (Container)
	{
		int type = 0;
		if (makeCustomModelCH.has_modeltype())
			type = makeCustomModelCH.modeltype();
		Container->MakeCustoModel(uin, makeCustomModelCH.modelname().c_str(), makeCustomModelCH.modeldesc().c_str(), type);
	}
}

void MpGameSurviveNetHandler::handleSelectMobSpawnBlock2Host(int uin, const PB_PACKDATA & pkg)
{
	PB_SelectMobSpawnerCH selectSpawnContainerCH;
	selectSpawnContainerCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos = MPVEC2WCoord(selectSpawnContainerCH.point());

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	auto Container = dynamic_cast<WorldContainer*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	if (Container)
	{
		Container->setBrushMonsterAttr(selectSpawnContainerCH.mobresid(), selectSpawnContainerCH.spawncount(),
			selectSpawnContainerCH.maxnearbymobs(), selectSpawnContainerCH.spawnwide(), selectSpawnContainerCH.spawnhigh(),
			selectSpawnContainerCH.minspawndelay(), selectSpawnContainerCH.isnumberdetection(),
			selectSpawnContainerCH.isspawndelay());
		player->getWorld()->markBlockForUpdate(blockPos, true);
	}
}
 void MpGameSurviveNetHandler::handleTransfer2Host(int uin, const PB_PACKDATA & pkg)
 {
	 SandboxEventDispatcherManager::GetGlobalInstance().
		 Emit("Protocol_Message_Dispatcher",
			 SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_RECORD_CH).
			 SetData_Number("uin", uin).
			 SetData_Userdata("PB_PACKDATA", "packagedata", (void*)&pkg));
	 /*PB_TransferRecordHC oneTransferRecord;
	 oneTransferRecord.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	 if (transferModule)
	 {
		 if (oneTransferRecord.isedit())
			 transferModule->updateTransferDataByOthers(oneTransferRecord, false);
		 else
			 transferModule->deleteTransferDataByOthers(oneTransferRecord, false);
	 }*/
 }
 void MpGameSurviveNetHandler::handleTransferStatus2Host(int uin, const PB_PACKDATA & pkg)
 {
	 SandboxEventDispatcherManager::GetGlobalInstance().
		 Emit("Protocol_Message_Dispatcher",
			 SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_TRANSFER_STATUS_CH).
			 SetData_Number("uin", uin).
			 SetData_Userdata("PB_PACKDATA", "packagedata", (void*)&pkg));
	 /*PB_TransferNameTipHC transferStatusHC;
	 transferStatusHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	 
	 TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	 if (transferModule)
	 {
		 transferModule->updateTransferStatusByClient(transferStatusHC);
	 }*/
 }
 void MpGameSurviveNetHandler::handleTransferTarget2Host(int uin, const PB_PACKDATA & pkg)
 {
	 SandboxEventDispatcherManager::GetGlobalInstance().
		 Emit("Protocol_Message_Dispatcher",
			 SandboxContext(nullptr).SetData_Number("msgCode", (int)PB_ACTOR_TRANSFER_CH).
			 SetData_Number("uin", uin).
			 SetData_Userdata("PB_PACKDATA", "packagedata", (void*)&pkg));
	/* PB_TransferTargetHC transferTargetHC;
	 transferTargetHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	 TransferMgr* transferModule = GET_SUB_SYSTEM(TransferMgr);
	 if (transferModule)
	 {
		 transferModule->transferTargetByClient(transferTargetHC);
	 }*/
 }
 void MpGameSurviveNetHandler::handleSyncLoveAmbassadorIcon2Host(int uin,const PB_PACKDATA & pkg)
 {
	 PB_MobDisplayData data;
	 data.ParseFromArray(pkg.MsgData,pkg.ByteSize);
	 int mobId = data.mobid();
	 int itemId = data.itemid();
	 int itemCount = data.itemcount();
	 int animId = data.animid();
	 
	 ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	 if (player && player->getWorld() && player->getWorld()->getActorMgr())
	 {
		 player->getWorld()->getActorMgr()->ToCastMgr()->updateLoveAmbassadorIcon(mobId,itemId,itemCount,animId,true);
	 }
 }

 void MpGameSurviveNetHandler::handleNpcShopGetInfo2Host(int uin, const PB_PACKDATA & pkg)
 {
	 //if (!g_pPlayerCtrl) { return; }

	 PB_GetNpcShopInfoCH data;
	 data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 PB_RespNpcShopInfoHC respDataHC;
	 PB_NpcShopData* pNpcShopData = respDataHC.mutable_npcshopinfo();

	 int iRet = 0;
	 if (GetWorldManagerPtr())
		iRet = GetWorldManagerPtr()->getWorldInfoManager()->getNpcShopInfo(data.shopid(), pNpcShopData);
	 if (iRet == 0) {
		 GetGameNetManagerPtr()->sendToClient(uin, PB_NPCSHOP_RESPGETSHOPINFO_HC, respDataHC);
	 }
 }

 void MpGameSurviveNetHandler::handleNpcShopBuySku2Host(int uin, const PB_PACKDATA &pkg)
 {
	 //if (!g_pPlayerCtrl) { return; }

	 PB_BuyNpcShopItemCH data;
	 data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 NpcShopInfo skuinfo;
	 int iRet = 0;
	 if (GetWorldManagerPtr())
		iRet = GetWorldManagerPtr()->getWorldInfoManager()->buyNpcShopSku(data.shopid(), data.skuid(), data.buycount(), skuinfo);
	 if (iRet == 0) {
		 ClientPlayer *player = checkPlayerByMsg2Host(uin);
		 iRet = player ? player->updateNpcShopItemChange(data.shopid(), data.skuid(), data.buycount()) : 2;
	 }

	 PB_NotifyNpcShopBuySkuHC notifyBuyHC;
	 notifyBuyHC.set_ret(iRet);
	 notifyBuyHC.set_shopid(data.shopid());
	 notifyBuyHC.set_skuid(data.skuid());
	 notifyBuyHC.set_leftnum(skuinfo.iLeftCount);
	 notifyBuyHC.set_endtime(skuinfo.iEndTime);
	 notifyBuyHC.set_uin(uin);
	 notifyBuyHC.set_buycount(data.buycount());
	 GetGameNetManagerPtr()->sendToClient(uin, PB_NPCSHOP_NOTIFYBUY_HC, notifyBuyHC);
 }

 void MpGameSurviveNetHandler::handleCloseEditActorModel2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_CloseEditActorModelCH closeEditActorModelCH;
	 closeEditActorModelCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }
	 player->onCloseEditActorModel(closeEditActorModelCH);
 }

 void MpGameSurviveNetHandler::handlePackGiftItemChg2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_PackGiftNotifyItemChgCH data;
	 data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	 if (!player) { return; }

	// TODO(alaska) 这里应该改成主机自己随机  下个版本修复
	// 改为主机随机  huanglin 2022.09.26
	/*
	int iCostItemId = data.costiteminfo() / 1000;
	int iCostItemNum = data.costiteminfo() % 1000;
	std::map<int, int> addMap;
	addMap.clear();
	int itemid, itemnum;
	for (int i = 0; i < data.addlist_size(); i++) {
		itemid = data.addlist(i) / 1000;
		itemnum = data.addlist(i) % 1000;
		addMap[itemid] = itemnum;
	}

	if (player->GetCheatHandler() && 
		player->GetCheatHandler()->CheckPackGiftCheat(packindex, iCostItemId, iCostItemNum, addMap))
		return;
	*/
	// 改为主机随机  huanglin 2022.09.26
	std::string s;
	int ret = player->openPackGift(0, data.shortcutidx(), 0, s);
	// addMap.clear();
 }

 void MpGameSurviveNetHandler::handleVehiclePreBlock2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_VehiclePreBlockCH vehiclePreBlockCH;
	 vehiclePreBlockCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 WCoord blockPos = MPVEC2WCoord(vehiclePreBlockCH.blockpos());
	 auto container = dynamic_cast<ContainerWorkshop*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	 if (container)
	 {
		 container->preVehicleBlock();
		 container->syncPreBlocks(uin);
	 }
 }

 void MpGameSurviveNetHandler::handleVehicleItemUse2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_VehicleItemUseCH data;
	 data.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	 if (!player) { return; }

	 auto pos = data.pos();
	 SandboxEventDispatcherManager::GetGlobalInstance().Emit("VehicleMgr_createVehicleWithItem",
		 SandboxContext(nullptr)
		 .SetData_Userdata("ClientPlayer", "player", player)
		 .SetData_Number("x", pos.x())
		 .SetData_Number("y", pos.y())
		 .SetData_Number("z", pos.z())
		 .SetData_Number("dir", data.dir())
		 .SetData_Number("shortcutidx", data.shortcutidx()));
 }
 
 void MpGameSurviveNetHandler::handleVehicleStartBlock2Host(int uin, const PB_PACKDATA &pkg) 
 {
	 PB_VehicleStartBlockCH vehicleStartBlockCH;
	 vehicleStartBlockCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 WCoord blockPos = MPVEC2WCoord(vehicleStartBlockCH.blockpos());
	 auto container = dynamic_cast<ContainerWorkshop*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
	 if (container)
	 {
		 container->startBlocksByClient(uin);
	 }
 }

 void MpGameSurviveNetHandler::handleVehicleAttribChange2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_VehicleAttribChangeCH vehicleAttribChangeCH;
	 vehicleAttribChangeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }
	 ClientActor* actor = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(vehicleAttribChangeCH.objid());
	 if (actor == NULL) return;
	 ActorVehicleAssemble *vehicle = dynamic_cast<ActorVehicleAssemble *>(actor);
	 if (vehicle)
	 {
		 if (vehicleAttribChangeCH.has_fuel())
		 {
			 int dFuel = vehicleAttribChangeCH.fuel();
			 int index = vehicleAttribChangeCH.partindex();

			 WCoord pos((index >> 10), (index >> 5) & 0x1f, (index & 0x1f));
			 vehicle->setFuelWithBlockPos(dFuel, pos);
		 }
		 if (vehicleAttribChangeCH.has_enginestate())
		 {
			 int state = vehicleAttribChangeCH.enginestate();
			 vehicle->syncEngineState((VEHICLE_ENGINESOUND_STATE)state);
		 }
	 }
 }

 void MpGameSurviveNetHandler::handleWorkshopItemInfo2Host(int uin, const PB_PACKDATA &pkg)
 {
	 PB_WorkshopItemInfoCH workshopItemInfoCH;
	 workshopItemInfoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 VehicleMgr* vehicleModule = GET_SUB_SYSTEM(VehicleMgr);
	 if (vehicleModule)
	 {
		 WCoord blockPos = MPVEC2WCoord(workshopItemInfoCH.containerpos());
		 auto container = dynamic_cast<ContainerWorkshop*>(player->getWorld()->getContainerMgr()->getContainer(blockPos));
		 if (container)
		 {
			container->sendItemInfoToClient(uin);
		 }
	 }
 }

 void MpGameSurviveNetHandler::handlePlayerVehicleMoveInput2Host(int uin, const PB_PACKDATA &pkg)
 {
	 ClientPlayer *player = checkPlayerByMsg2Host(uin);
	 if (player == NULL) return;

	 auto RidComp = player->getRiddenComponent();
	 if (RidComp && RidComp->getRidingActor())
	 {
		 PB_PlayerVehicleMoveInputCH playerVehicleMoveCH;
		 playerVehicleMoveCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		 ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(RidComp->getRidingActor());
		 if (vehicle)
		 {
			 VehicleAssembleLocoMotion *vehicleLoc = static_cast<VehicleAssembleLocoMotion*> (vehicle->getLocoMotion());
			 if (vehicleLoc)
			 {
				 vehicleLoc->m_MoveAccel = vehicle->hasFuel() ? playerVehicleMoveCH.accel() : 0;
				 vehicleLoc->m_MoveBrake = vehicle->hasFuel() ? playerVehicleMoveCH.brake() : 0;
				 vehicleLoc->m_MoveLeft = playerVehicleMoveCH.left() ;
				 vehicleLoc->m_MoveRight =  playerVehicleMoveCH.right() ;
			 }
		 }
	 }
 }

 void MpGameSurviveNetHandler::handlePlayerResetVehicle2Host(int uin, const PB_PACKDATA &pkg)
 {
	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 PB_PlayerResetVehicleCH playerResetVehicleCH;
	 playerResetVehicleCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 tdr_longlong objid = playerResetVehicleCH.actorid();
	 ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

	 if (NULL == actor || actor != NULL && player->isDead()) return;

	 ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	 if (vehicle)
		 vehicle->reset();
 }

 void MpGameSurviveNetHandler::handlePlayerMotionStateChange2Host(int uin, const PB_PACKDATA &pkg)
 {
	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 PB_PlayerMotionStateChangeCH playerMotionStateChangeCH;
	 playerMotionStateChangeCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 player->setMotionState(playerMotionStateChangeCH.statetype(), playerMotionStateChangeCH.stateswitch());
 }

 void MpGameSurviveNetHandler::handlePlayerClick2Host(int uin, const PB_PACKDATA &pkg)
 {
	 ClientPlayer *player = uin2Player(uin);
	 if (player == nullptr)
	 {
		 sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		 return;
	 }

	 PB_PlayerClickCH playerClickCH;
	 playerClickCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	 auto objid = playerClickCH.objid();
	 if(objid > 0)
		player->clickActorOnTrigger(objid, playerClickCH.actorid());
	 else
	 {
		 WCoord blockPos = MPVEC2WCoord(playerClickCH.blockpos());
		 if (player->GetCheatHandler())
		 {
			 if (!player->GetCheatHandler()->CheckClickBlock(blockPos, playerClickCH.blockid()))
				 return;
		 }
		 player->clickBlockOnTrigger(playerClickCH.blockid(), blockPos);
	 }
		 
 }
 
void MpGameSurviveNetHandler::handlePlayerSelectShortcut2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayerSelectShortcutCH tmpCH;
	tmpCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	 
	player->selectShortcutOnTrigger(tmpCH.index()); 
}

void MpGameSurviveNetHandler::handleTriggerPlayerAttri2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_TRIGGERPLAYERATTRICH triggerPlayerAttrCH;
	triggerPlayerAttrCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	std::vector<int> attrilist;
	for (int i = 0; i < triggerPlayerAttrCH.obactorattrlist_size(); i++) 
	{
		attrilist.push_back(triggerPlayerAttrCH.obactorattrlist(i));
	}
	player->attriChangeOnTrigger(attrilist);
}

void MpGameSurviveNetHandler::handlePlayerAttrScale2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_PlayerScaleHC playerScaleHC;
	playerScaleHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
#ifdef IWORLD_SERVER_BUILD
	// 反外挂需求 防止客机利用此协议修改其他玩家模型大小 2022.11.04 by huanglin
	ClientActor *actor = NULL;
	if (playerScaleHC.has_objid())
	{
		IClientActor* iactor = g_WorldMgr ? g_WorldMgr->findActorByWID(playerScaleHC.objid()) : NULL;
		actor = iactor ? iactor->GetActor() : nullptr;
	}	
	if ((actor && actor->isPlayer() && actor->getObjId() != uin) || 
		(playerScaleHC.has_uin() && playerScaleHC.uin() != uin))
	{
		jsonxx::Object cheat;
		cheat << "scale" << playerScaleHC.scale();
		cheat << "dest_objid" << playerScaleHC.objid();
		cheat << "dest_uin" << playerScaleHC.uin();
		ActionLogger::ErrorLog(uin, 0, "cheat_scale", cheat);
		return;
	}
#endif
	GetGameNetManagerPtr()->sendBroadCast(PB_PLAYER_ATTR_SCALE_HC, playerScaleHC);
}

void MpGameSurviveNetHandler::handleReqDownloadResUrl2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_ReqDownLoadResUrlCH reqDownLoadResUrlCH;
	reqDownLoadResUrlCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (!RoomSyncResMgr::getSingletonPtr())
		return;

	RoomSyncResMgr::getSingletonPtr()->reqDownloadUrl(reqDownLoadResUrlCH.type(), reqDownLoadResUrlCH.externdata(), uin);

	/*if (reqDownLoadResUrlCH.type() == 1)
	{
		if (!FullyCustomModelMgr::GetInstancePtr())
			return;

		std::string skey = reqDownLoadResUrlCH.externdata();
		FullyCustomModelMgr::GetInstancePtr()->reqUploadFullyCustomModel(skey, skey, uin, FCM_HOST_UPLOAD_BY_CLIENT_SYNC);
	}*/
}

void MpGameSurviveNetHandler::handleCloseFullyCustomModelUI2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_CloseFullyCustomModelUICH closeFullyCustomModelUICH;
	closeFullyCustomModelUICH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	player->onCloseFullyCustomModelUI(closeFullyCustomModelUICH);
}

void MpGameSurviveNetHandler::handlePlayerNavFinished2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	
	if (player->getNavigator()) {
		player->getNavigator()->clearMoveForward();

		LivingLocoMotion *loc = static_cast<LivingLocoMotion *>(player->getLocoMotion());
		if(loc != NULL) loc->clearTarget();
	}
}

void MpGameSurviveNetHandler::handlePlayTriggerSound2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayEffectHC playEffectHC;
	playEffectHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	const PB_EffectTriggerSound& sound = playEffectHC.triggersound();
	if (playEffectHC.effecttype() == PB_EFFECT_STOPPARTICLE)
	{
		if (player->getWorld())
		{
			const PB_EffectParticle &particle = playEffectHC.particle();
			WCoord pos = MPVEC2WCoord(particle.pos());
			char path[256] = { 0 };
			sprintf(path, "%s", particle.name().c_str());
			player->getWorld()->getEffectMgr()->stopParticleEffect(path, pos, false);
		}
	}

	if (playEffectHC.effecttype() == PB_EFFECT_TIRGGERSOUND && sound.playstate() != 0 && sound.playstate() != PLAYSTAT_PLAYEX)
	{
		if (sound.playstate() == PLAYSTAT_STOP && player->getWorld())
		{
			player->getWorld()->getEffectMgr()->stopPosSound(MPVEC2WCoord(sound.pos()), sound.name().c_str(), false);
		}
		GetGameNetManagerPtr()->sendBroadCast(PB_PLAYEFFECT_HC, playEffectHC);
		return;
	}

	if (!player->getWorld())
		return;

	if(playEffectHC.effecttype() == PB_EFFECT_TIRGGERSOUND)
		player->getWorld()->getMpActorMgr()->sendMsgToNearPlayers(PB_PLAYEFFECT_HC, playEffectHC, BlockCenterCoord(MPVEC2WCoord(sound.pos())), 16 * BLOCK_SIZE, true, UNRELIABLE_SEQUENCED);

#ifndef IWORLD_SERVER_BUILD
	//20210824 qq钢琴乐器 codeby:zoulongjin
	//播放音效
	if (playEffectHC.effecttype() == PB_EFFECT_TIRGGERSOUND)
	{
		player->getWorld()->getEffectMgr()->playPosSoundEX(MPVEC2WCoord(sound.pos()), sound.name().c_str(), sound.volume(), sound.pitch(), sound.isloop(), false);
	}

	//LOG_INFO("handlePlayTriggerSound2Host effectclass %d", playEffectHC.effectclass());
	//播放特效
	if (playEffectHC.effectclass() > 0 && playEffectHC.mutable_particle() && playEffectHC.mutable_particle()->name() != "")
	{
		//LOG_INFO("handlePlayTriggerSound2Host particle_name %s", playEffectHC.mutable_particle()->name().c_str());
		float effectScale = playEffectHC.effectscale() / 1000.0f;
		const PB_EffectParticle& pt = playEffectHC.particle();
		WCoord pos = MPVEC2WCoord(pt.pos());
		char path[256] = { 0 };
		sprintf(path, "%s", pt.name().c_str());
		int lifeTime = pt.has_age() ? pt.age() : 0;
		player->getWorld()->getEffectMgr()->playParticleEffectForTrigger(path, pos, effectScale, lifeTime);
	}
#endif
}

void MpGameSurviveNetHandler::handlePlayerJumpOnce2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayerCommonSetHC playerJumpHC;
	playerJumpHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	GetGameNetManagerPtr()->sendBroadCast(PB_PLAYER_JUMP_HC, playerJumpHC);
}


void MpGameSurviveNetHandler::handleCloudServerPlayerPermit2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_CSPlayerPermitCH csPlayerPermitCH;
	csPlayerPermitCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_updateCSPermitMsgByClient",
		SandboxContext(nullptr).SetData_Number("srcuin", uin).SetData_Usertype<game::ch::PB_CSPlayerPermitCH>("csPlayerPermitHC", &csPlayerPermitCH));

}

void MpGameSurviveNetHandler::handleCloudServerAuthority2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getCSAuthority",
		SandboxContext(nullptr).SetData_Number("uin", uin));
	CloudServerAuthority authority;
	if (result.IsExecSuccessed())
	{
		authority = result.GetData_UserObject<CloudServerAuthority>();
	}
	if (authority.Type > CS_AUTHORITY_MANAGER || authority.Type < CS_AUTHORITY_ROOM_OWNER)
		return;
		
	PB_CSAuthorityHC csAuthorityHC;
	csAuthorityHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_onCSAuthorityReset",
		SandboxContext(nullptr).SetData_Usertype<game::hc::PB_CSAuthorityHC>("csAuthorityHC", &csAuthorityHC));

	GameNetManager::getInstance()->sendBroadCast(PB_CLOUDSERVER_AUTHORITY_HC,csAuthorityHC);
}

void MpGameSurviveNetHandler::handleSSTask2Host(int uin, const PB_PACKDATA &pkg)
{
//#ifdef _SCRIPT_SUPPORT_
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_SSTaskHC taskHC;
	taskHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	MINIW::ScriptVM::game()->callFunction("SSTaskReceiveForHost", "iis", uin, taskHC.taskid(), taskHC.paramjson().c_str());

//#endif
}

void MpGameSurviveNetHandler::handleVehicleAssembleLine2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_VehicleAssembleLineCH vehicleAssembleLineCH;
	vehicleAssembleLineCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	tdr_longlong objid = vehicleAssembleLineCH.objid();
	ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	int from = vehicleAssembleLineCH.from();
	int to = vehicleAssembleLineCH.to();

	if (vehicle)
	{
		WCoord start(from >> 10, (from >> 5) & 0x1f, from & 0x1f);
		WCoord end(to >> 10, (to >> 5) & 0x1f, to & 0x1f);
		vehicle->addBlockLines(start, end);
		PB_VehicleAssembleLineHC vehicleAssembleLineHC;
		vehicleAssembleLineHC.set_objid(objid);
		vehicleAssembleLineHC.set_from(from);
		vehicleAssembleLineHC.set_to(to);
		GetGameNetManagerPtr()->sendBroadCast(PB_VEHICLE_ASSEMBLE_LINE_HC, vehicleAssembleLineHC);
	}
}

void MpGameSurviveNetHandler::handleVehicleAssembleLineOperate2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_VehicleAssembleLineOperateCH lineOpereateCH;
	lineOpereateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	tdr_longlong objid = lineOpereateCH.objid();
	ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	if (vehicle)
	{
		WCoord pos(lineOpereateCH.blockpos().x(),lineOpereateCH.blockpos().y(), lineOpereateCH.blockpos().z());
		vehicle->uiInteractBlock(lineOpereateCH.blockid(), pos, lineOpereateCH.type(), lineOpereateCH.isclicked(), lineOpereateCH.keyid(),uin);
	}
}

void MpGameSurviveNetHandler::handleUpdateActionerData2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_UpdateActionerDataCH ActionerUpdateCH;
	ActionerUpdateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	tdr_longlong objid = ActionerUpdateCH.objid();
	ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);

	WCoord blockpos(ActionerUpdateCH.blockpos().x(), ActionerUpdateCH.blockpos().y(), ActionerUpdateCH.blockpos().z());
	VehicleContainerActioner* container = NULL;
	if (vehicle&&vehicle->getVehicleWorld())
		container = dynamic_cast<VehicleContainerActioner*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(blockpos));
	else if (player->getWorld())
		container = dynamic_cast<VehicleContainerActioner*>(player->getWorld()->getContainerMgr()->getContainer(blockpos));
	if (container&&ActionerUpdateCH.has_datastr())
		container->updateActionerData(ActionerUpdateCH.datastr().c_str());
	
}

void MpGameSurviveNetHandler::handleVehicleWorkshopLine2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_VehicleWorkshopLineCH vehicleWorkshopLineCH;
	vehicleWorkshopLineCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer *player = uin2Player(uin);
	if (player == nullptr)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	WCoord blockpos(vehicleWorkshopLineCH.containerpos().x(), vehicleWorkshopLineCH.containerpos().y(), vehicleWorkshopLineCH.containerpos().z());
	if (player->getWorld() && player->getWorld()->getContainerMgr())
	{
		ContainerWorkshop *container = dynamic_cast<ContainerWorkshop *>(player->getWorld()->getContainerMgr()->getContainer(blockpos));
		if (container)
		{
			WCoord frompos(vehicleWorkshopLineCH.frompos().x(), vehicleWorkshopLineCH.frompos().y(), vehicleWorkshopLineCH.frompos().z());
			WCoord topos(vehicleWorkshopLineCH.topos().x(), vehicleWorkshopLineCH.topos().y(), vehicleWorkshopLineCH.topos().z());
			container->addBlockLines(frompos,topos);
		}
	}
}

void MpGameSurviveNetHandler::handleCloudServerChangePlayerTeam2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_CSChangePlayerTeamCH csChangePlayerTeamCH;

	csChangePlayerTeamCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	// 反外挂需求 此协议仅允许修改自己的队伍 2022.11.04 by huanglin
	bool cheat = uin != csChangePlayerTeamCH.uin();
#ifdef IWORLD_SERVER_BUILD
	// 反外挂需求 此协议仅在进入地图时选择队伍发送, 防止玩家在游戏中途通过此协议更改队伍
	cheat = cheat || player->getTeam() != 0;
#endif
	if (cheat)
	{
		jsonxx::Object log;
		log << "dest_uin" << csChangePlayerTeamCH.uin();
		log << "cur_team" << player->getTeam();
		ActionLogger::ErrorLog(uin, 0, "cheat_change_team", log);
		return;
	}

	MINIW::ScriptVM::game()->callFunction("RentChangePlayerTeam", "ii", csChangePlayerTeamCH.uin(), csChangePlayerTeamCH.teamid());

}

void MpGameSurviveNetHandler::handleCloudServerAutoMute2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_CSRentRoomAutoMuteCH csRentRoomAutoMuteCH;
	csRentRoomAutoMuteCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int minutes = csRentRoomAutoMuteCH.spampreventionminutes();
//	LOG_INFO("Cloud Server Auto Mute %d %d %d", uin, minutes, GetClientInfoProxy()->m_GameNetMgr->getHostUin());
	// 租赁服房主计算方式需要特殊处理

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getCSAuthority",
		SandboxContext(nullptr).SetData_Number("uin", uin));
	CloudServerAuthority authority;
	if (result.IsExecSuccessed())
	{
		authority = result.GetData_UserObject<CloudServerAuthority>();
	}

	if (CS_AUTHORITY_MANAGER >= authority.Type)
	{
		LOG_INFO("Cloud Server Auto Mute %d", minutes);
		MINIW::ScriptVM::game()->callFunction("requestCloudServerSetSpamPrevention", "ii", minutes, authority.Type);
	}
}

void MpGameSurviveNetHandler::handleWorkshopLineUpdate2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_VehicleAssembleLineUpdateCH lineUpdateCH;
	lineUpdateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WCoord blockpos(lineUpdateCH.blockpos().x(), lineUpdateCH.blockpos().y(), lineUpdateCH.blockpos().z());
	if (player->getWorld() && player->getWorld()->getContainerMgr())
	{
		ContainerWorkshop *container = dynamic_cast<ContainerWorkshop *>(player->getWorld()->getContainerMgr()->getContainer(blockpos));
		if (container)
		{
			container->calculateConnectMap();
			container->genVehicleBlockNode(true);
		}
	}
}

void MpGameSurviveNetHandler::handleMapEditHandle2Host(int uin, const PB_PACKDATA &pkg) {
	// 云服联机房间不响应
	if (GAME_NET_MP_GAME_NOT_INIT < GetClientInfoProxy()->getMultiPlayer() && ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
	{
		return;
	}
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	if (player->GetCheatHandler() && !player->GetCheatHandler()->CheckUseMapEdit()){
		return;
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("MapEditManager_handleMapEditHandle2Host",SandboxContext(nullptr)
		.SetData_Userdata("PB_PACKDATA", "pkg", (void*)&pkg)
		.SetData_Usertype("player", player));
}

void MpGameSurviveNetHandler::handleMapEditRevoke2Host(int uin, const PB_PACKDATA &pkg) {
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("MapEditManager_handleMapEditRevoke2Host",SandboxContext(nullptr)
		.SetData_Userdata("PB_PACKDATA", "pkg", (void*)&pkg));
};

void MpGameSurviveNetHandler::handleCloudRoomOnwerStartGame2Host(int uin, const PB_PACKDATA &pkg)
{
	LOG_INFO("MpGameSurviveNetHandler::handleCloudRoomOnwerStartGame2Host");
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	WorldManager *worldMgr = m_root->getWorldMgr();
	if (worldMgr && worldMgr->isGameMakerRunMode())
	{
		if (worldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_STARTMODE) == 0
			&& worldMgr->m_RuleMgr->getGameStage() == CGAME_STAGE_PREPARE)
			worldMgr->m_RuleMgr->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
	}
}

void MpGameSurviveNetHandler::handleCloudRoomKickOff2Host(int uin, const PB_PACKDATA &pkg)
{
	LOG_INFO("MpGameSurviveNetHandler::handleCloudRoomKickOff2Host");
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_CSKickOffDataCH csKickoffCH;
	csKickoffCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	LOG_INFO("master kick player : selfuin [%d] , roleCSKickoff.uin() %d [kickertype is %d]",uin, csKickoffCH.uin(), csKickoffCH.kickertype());

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getCSAuthority",
		SandboxContext(nullptr).SetData_Number("uin", uin));
	CloudServerAuthority kickerAuthority;
	if (result.IsExecSuccessed())
	{
		kickerAuthority = result.GetData_UserObject<CloudServerAuthority>();
	}
	if (kickerAuthority.Type > CS_AUTHORITY_MANAGER || kickerAuthority.Type < CS_AUTHORITY_ROOM_OWNER)
	{
		return;
	}
	MINIW::ScriptVM::game()->callFunction("RentKickPlayer", "iiibi", csKickoffCH.uin(), 0, csKickoffCH.kickertype(), false, uin);
}

void MpGameSurviveNetHandler::handleUsePackingFCMItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_UsePackingFCMItemCH usePackingFCMItemCH;
	usePackingFCMItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	// 使用主机上的手持物品, 防止客机随意指定ID
	if (usePackingFCMItemCH.itemid() != player->getCurToolID())
	{
#ifdef IWORLD_SERVER_BUILD
		jsonxx::Object log;
		log << "cur_tool" << player->getCurToolID();
		log << "client_item" << usePackingFCMItemCH.itemid();
		ActionLogger::ErrorLog(uin, 0, "cheat_fcm_item", log);
#endif
		return;
	}
	player->usePackingFCMItem(usePackingFCMItemCH.itemid(), WCoord(usePackingFCMItemCH.usepos().x(), usePackingFCMItemCH.usepos().y(), usePackingFCMItemCH.usepos().z()));
}

void MpGameSurviveNetHandler::handleCreatePackingCM2Host(int uin, const PB_PACKDATA &pkg)
{
#ifdef IWORLD_SERVER_BUILD
	return;  // 云服不使用此功能, 防止此协议被利用来不断创建cm消耗资源, 最终导致崩服  2024.01.24 by huanglin
#endif
	LOG_INFO("kekeke handleCreatePackingCM2Host");
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_CreatePackingCMCH createPackingCMCH;
	createPackingCMCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (CustomModelPacking::GetInstancePtr())
	{
		CreatePackingFcmInfo info;
		info.name = createPackingCMCH.name();
		info.desc = createPackingCMCH.desc();
		info.mode = (PACKING_CM_RORATE_MODE)createPackingCMCH.rotatetype();
		info.packingMode = createPackingCMCH.createtype();

		info.offsetPos = WCoord(createPackingCMCH.offsetpos().x(), createPackingCMCH.offsetpos().y(), createPackingCMCH.offsetpos().z());
		info.startPos = WCoord(createPackingCMCH.startpos().x(), createPackingCMCH.startpos().y(), createPackingCMCH.startpos().z());
		info.endPos = WCoord(createPackingCMCH.endpos().x(), createPackingCMCH.endpos().y(), createPackingCMCH.endpos().z());
		info.uin = player->getUin();

		CustomModelPacking::GetInstancePtr()->createPackingFcm(info);
	}
		
}

void MpGameSurviveNetHandler::handlePlayerInputContent2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_InputContentCH inputContentCH;
	inputContentCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	std::string content = inputContentCH.content();
	player->onInputContent(content, false);
}

void MpGameSurviveNetHandler::handlePlayerInputKeys2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_InputKeyCH keyInputCH;
	keyInputCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int keyVal = keyInputCH.keytype(); 
	std::string keyType = keyInputCH.eventtype();
	player->triggerInputEvent(keyVal, keyType.c_str());
}

void MpGameSurviveNetHandler::handleSensorContainerData2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL || !player->getWorld() || !player->getWorld()->getActorMgr())
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_SensorContainerDataCH sensorContainerDataCH;
	sensorContainerDataCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos = MPVEC2WCoord(sensorContainerDataCH.blockpos());
	int sensorValue = sensorContainerDataCH.sensorvalue();
	bool sensorRever = sensorContainerDataCH.isbreverse();
	tdr_longlong objid = sensorContainerDataCH.objid();
	ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	WorldContainer* container = NULL;
	if (vehicle&&vehicle->getVehicleWorld())
		container = vehicle->getVehicleWorld()->getContainerMgr()->getContainer(blockPos);
	else if (player->getWorld())
		container = player->getWorld()->getContainerMgr()->getContainer(blockPos);

	if (container)
	{
		//红外感应
		WorldSensorContainer *curcontainer = dynamic_cast<WorldSensorContainer*>(container);
		if (curcontainer)
		{
			curcontainer->setSensorValue(sensorValue);
		}
		else
		{
			//感应器
			WorldValueSensorContainer *curcontainer = dynamic_cast<WorldValueSensorContainer*>(container);
			if (curcontainer)
			{
				curcontainer->setSensorValueAndIsRever(sensorValue, sensorRever);
			}

		}
	}
}

void MpGameSurviveNetHandler::handleCarryActor2Host(int uin, const PB_PACKDATA &pkg)
{
#ifdef IWORLD_SERVER_BUILD
	return;  // 云服禁用此接口, 防止玩家吸怪 2024.02.28 by huanglin
#endif

	ClientPlayer *player = uin2Player(uin);
	if (player == NULL || !player->getWorld() || !player->getWorld()->getActorMgr())
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayerCarryActorCH playerCarryActorCH;
	playerCarryActorCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_longlong objid = playerCarryActorCH.actorid();
	ClientActor *actor = objid > 0 ? player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(objid) : NULL;

#ifdef IWORLD_SERVER_BUILD
	// 反外挂 防止客机使用此协议影响其他玩家 2022.11.04 by huanglin
	if (actor && actor->sureCarryComponent() && !actor->sureCarryComponent()->canCarried(player))
	{
		jsonxx::Object log;
		log << "objid" << objid;
		log << "def_id" << actor->getDefID();
		ActionLogger::ErrorLog(uin, 0, "cheat_carry_actor", log);
		return;
	}
#endif

	WCoord pos = MPVEC2WCoord(playerCarryActorCH.pos());
	player->carryActor(actor, pos);
}

void MpGameSurviveNetHandler::handleVillagerModifyName2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL || !player->getWorld() || !player->getWorld()->getActorMgr())
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_VillagerModifyName csVillagerModifyNameCH;
	csVillagerModifyNameCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	tdr_longlong objid = csVillagerModifyNameCH.objid();
	auto name = csVillagerModifyNameCH.name();
	ClientActor *actor = NULL;// objId2ActorOnClient(objid);
	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		actor = mp->objId2ActorOnClient(objid);
	}
	if (actor == NULL)
	{
		return;
	}
	ClientMob *mob = dynamic_cast<ClientMob *>(actor);
	if (mob)
		mob->setDisplayName(name);
}

void MpGameSurviveNetHandler::handleCustomModelPre2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_CustomModelPrepareCH customModelPreCH;
	customModelPreCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	bool havefile = customModelPreCH.havefile();
	int index = customModelPreCH.index();
	player->setLoadSpeedUp(havefile);
	CustomModelMgr::GetInstancePtr()->syncCustomModelData(player, index, CUSTOM_MODEL_TYPE, !havefile);
}

void MpGameSurviveNetHandler::handleMoveMobBackpack2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_MoveMobBackpackItemCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->moveMobItem(msg.gridindex(), msg.movetype());
}

void MpGameSurviveNetHandler::handleMobInteractItem2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_InteractMobBackpackItemCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->InteractMobItem(msg.fromindex(), msg.toindex());
}

void MpGameSurviveNetHandler::handleRolePushArch2Host(int uin, const PB_PACKDATA &pkg)
{
	// LOG_INFO("MpGameSurviveNetHandler::handleRolePushArch2Host(): uin = %d", uin);

	// Note by cloud. 2021.09.07
	// 云服增加了玩家数据上云MySQL功能，包含了这些数据的存储：https://mini1.feishu.cn/docs/doccnb0Lg7b7CndHn2nDlxlRbGe
	// 之前的房间数据互通功能，也存储了数据。这些数据和云服存储的数据存在部分重合
	// 按照当前流程，房间数据互通部分会从客户端上传，从而覆盖服务器的数据，会导致数据不一致
	// 做如下改动：非云服的情况才执行此上传覆盖逻辑，云服不执行
	// [[但是：玩家的云变量部分需要保持不变。]] 2021.10.23 by huanglin  变动如下
	// 云服将云变量的读写也转入data server, 因此不处理此处客户端上传存档
	// 其它模式(普通联机房, 单机)的房间继续使用原机制
	
#ifdef IWORLD_SERVER_BUILD
		return;
#endif
		WorldManager* worldMgr = m_root->getWorldMgr();
		if (worldMgr)
		{
			char path[256];
			sprintf(path, "data/rolearch/u%d", uin);
			if (!gFunc_isStdioDirExist(path)) //先创建
			{
				gFunc_makeStdioDir(path);
			}

			PB_PlayerArchEntityCH playerArchCH;
			playerArchCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

			sprintf(path, "data/rolearch/u%d/w%lld.p", uin, worldMgr->getFromWorldID());
			if (gFunc_isStdioDirExist(path)) //先创建
			{
				gFunc_deleteStdioFile(path);
			}
			GetFileManager().SaveToWritePath(path,(const char *)playerArchCH.userdata().c_str(), playerArchCH.userdata().size());
			return;
		}


}

void MpGameSurviveNetHandler::handleSummonPet2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_SUMMONPETCH summonpetCH;
	summonpetCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->summonPet(summonpetCH.monsterid(),summonpetCH.serverid(), summonpetCH.petid(), summonpetCH.stage(), summonpetCH.quality(), (summonpetCH.has_petname() ? summonpetCH.petname() : ""));
}

void MpGameSurviveNetHandler::handleRequestAvtarModel2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ChangeModelMgr_notifiyModel",SandboxContext(nullptr)
		.SetData_Number("uin", uin)
		.SetData_Userdata("PB_PACKDATA", "pkg", (void*)&pkg));
}

void MpGameSurviveNetHandler::handleFurnaceTemperature2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_FurnaceTemperatureCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos = MPVEC2WCoord(msg.blockpos());

	FurnaceContainer* container = NULL;
	if (player->getWorld() && player->getWorld()->getContainerMgr())
		container = static_cast<FurnaceContainer*>(player->getWorld()->getContainerMgr()->getFurnace(blockPos.x, blockPos.y, blockPos.z));

	if (container)
		container->setTemperatureLev(msg.lev());
}

void MpGameSurviveNetHandler::handlePotStartMake2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_PlayerPotSetMakeCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos(msg.x(), msg.y(), msg.z());
	//锅变成了炉子用炉子这个协议
	WorldStove* container = NULL;
	WorldPot* potcontainer = NULL;

	if (player->getWorld() && player->getWorld()->getContainerMgr())
	{
		container = dynamic_cast<WorldStove*>(player->getWorld()->getContainerMgr()->getContainer(blockPos.x, blockPos.y, blockPos.z));
		potcontainer= dynamic_cast<WorldPot*>(player->getWorld()->getContainerMgr()->getContainer(blockPos.x, blockPos.y, blockPos.z));
	}
		
	if (!container && !potcontainer)
		return;
	if (msg.make())
	{
		if (container)
		{
			if (container->isCooking())
				return;
			container->startCooking(player, msg.craftid(), msg.num());
		}
		if (potcontainer)
		{
			if (potcontainer->IsMaking())
				return;
			potcontainer->StartMakeProduct(player, msg.craftid(), msg.num());
		}
	}
	else
	{
		if (potcontainer)
		{
			if (!potcontainer->IsMaking())
				return;
			potcontainer->CancelMake(player);
		}
	}
}

void MpGameSurviveNetHandler::handleTakePotItem2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_PlayerTakeContainerGridItemCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord blockPos(msg.x(), msg.y(), msg.z());
	WorldPot* container = NULL;
	if (player->getWorld() && player->getWorld()->getContainerMgr())
		container = dynamic_cast<WorldPot*>(player->getWorld()->getContainerMgr()->getContainer(blockPos.x, blockPos.y, blockPos.z));
	if (!container)
		return;
	container->TakeGridData(player, msg.gridindex());
}


void MpGameSurviveNetHandler::handlePlayerRevivePoint2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	
	PB_PlayerRevivePointCH PlayerRevivePointCH;
	PlayerRevivePointCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (uin != PlayerRevivePointCH.uin())
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	WCoord revivepoint = MPVEC2WCoord(PlayerRevivePointCH.revivepoint());
	WCoord spawnpoint = PlayerRevivePointCH.has_spawnpoint() ? MPVEC2WCoord(PlayerRevivePointCH.spawnpoint()) : WCoord(0, -1, 0);
	
	SandboxContext context;
	context.SetData_UserObject("spawn", spawnpoint);
	context.SetData_UserObject("revive", revivepoint);
	context.SetData_Number("mapid", PlayerRevivePointCH.mapid());
	player->Event().Emit("revive_setAccountWorldPoint", context);
	//player->setAccountWorldPoint(PlayerRevivePointCH.mapid(), spawnpoint, revivepoint);

	//冒险模式复活消耗星星
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isAdventureMode())
	{
		player->ReviveCostExp();
	}
	
	if (GetWorldManagerPtr())
	{
		GetWorldManagerPtr()->setClientAccountWorldPointMap(uin, PlayerRevivePointCH.mapid(), spawnpoint, revivepoint);
	}
}


void MpGameSurviveNetHandler::handleVoiceInform2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}	
	PB_VoiceInformCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_VOICE_INFORM_CH_2", SandboxContext(nullptr).SetData_Number("player_uin", uin).
		SetData_UserObject<PB_VoiceInformCH>("msg", msg)
	);
}

void MpGameSurviveNetHandler::handleStarStationChangeNameStatus2Host(int uin, const PB_PACKDATA& pkg) 
{
	//ClientPlayer *player = uin2Player(uin);
	//if (player == NULL)
	//{
	//	sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
	//	return;
	//}

	//PB_ChangeStarStationNameStatus msg;
	//msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	//GetStarStationTransferMgr().updateStarStationDef(msg.starstationid(), msg.starstationname(), msg.isactive(), msg.issign());
	//StarStationTransferDef *starStationDef = GetStarStationTransferMgr().getStarStationDef(msg.starstationid());
	//if(starStationDef)
	//{
	//	WCoord consolePos(starStationDef->consolePosX, starStationDef->consolePosY, starStationDef->consolePosZ);
	//	WorldStarStationTransferConsoleContainer* container = dynamic_cast<WorldStarStationTransferConsoleContainer*>(player->getWorld()->getContainerMgr()->getContainer(consolePos));
	//	if (container && msg.isactive())
	//	{
	//		container->findInactiveNeighborTransferCabinBlockAndUpdateStatus(player->getWorld());
	//	}
	//}
}

void MpGameSurviveNetHandler::handleLeaveStarStationCabin2Host(int uin, const PB_PACKDATA& pkg) 
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_LeaveStarStationCabin msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(uin != msg.uin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	player->standUpFromChair();
}

void MpGameSurviveNetHandler::handleUpdateStarStationCabinStatus2Host(int uin, const PB_PACKDATA& pkg) 
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_UpdateStarStationCabinStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateStarStationCabinStatus(msg.starstationid(), MPVEC2WCoord(msg.cabinpos()), msg.status());
}

void MpGameSurviveNetHandler::handleUpdateStarStationStatusEnd2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_UpdateStarStationCabinStatus msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().updateStarStationCabinStatusEnd(msg.starstationid(), msg.status());
}

void MpGameSurviveNetHandler::handleAddStarStationTransferDesc2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_addStarStationTransferDesc msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().addStarStationTransferDesc(msg.srcstarstationid(), msg.descstarstationid(), uin);
}

void MpGameSurviveNetHandler::handleAddUnfinishedTransferRecord2Host(int uin, const PB_PACKDATA& pkg) 
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_AddUnfinishedTransferRecord msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_UnfinishedStarStationTransferRecord pbUnfinishedTransferRecord = msg.unfinishedtransferrecord();
	UnfinishedStarStationTransferRecord unfinishedTransferRecord;
	unfinishedTransferRecord.destStarStationID = pbUnfinishedTransferRecord.deststarstationid();
	unfinishedTransferRecord.destMapID = pbUnfinishedTransferRecord.destmapid();
	unfinishedTransferRecord.srcCabinPosX = pbUnfinishedTransferRecord.srccabinpos().x();
	unfinishedTransferRecord.srcCabinPosY = pbUnfinishedTransferRecord.srccabinpos().y();
	unfinishedTransferRecord.srcCabinPosZ = pbUnfinishedTransferRecord.srccabinpos().z();
	unfinishedTransferRecord.status = pbUnfinishedTransferRecord.cabinstatus();
	GetStarStationTransferMgr().addUnfinishedTransferRecord(msg.srcstarstationid(), unfinishedTransferRecord);
}

void MpGameSurviveNetHandler::handleRemoveUnfinishedTransferRecord2Host(int uin, const PB_PACKDATA& pkg) 
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_RemoveUnfinishedTransferRecord msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	GetStarStationTransferMgr().removeUnfinishedTransferRecord(msg.starstationid(),MPVEC2WCoord(msg.cabinpos()));
}

void MpGameSurviveNetHandler::handleStarStationTransferTarget2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayerTransferByStarStationCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(uin != msg.uin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	GetStarStationTransferMgr().transferPlayerByClient(msg);
}

void MpGameSurviveNetHandler::handleStarStationRequest2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_StarStationTransferDeductFeeCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if(uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	int nRet = 0;
	int transferType = 0;
	if(msg.has_srcstarstationid()) //星站传送
	{
		if (msg.srcstarstationid() > 0)
		{
			int mapID = -1;
			int costStar = 5;
			if (msg.has_destmapid())
			{
				mapID = msg.destmapid();
			}

			if (msg.has_coststar())
			{
				costStar = msg.coststar();
			}

			transferType = 0;
			nRet = GetStarStationTransferMgr().useStarForTransfer(player, msg.srcstarstationid(), msg.cabinpos().x(), msg.cabinpos().y(), msg.cabinpos().z(), msg.deststarstationid(), mapID, costStar);
		}
	}
	else
	{
		if (msg.has_transfertype())
		{
			transferType = 2;
			nRet = GetStarStationTransferMgr().useToolsToTransfer(player, msg.deststarstationid(), 1);
		}
		else
		{
			int costStar = 5;
			if (msg.has_coststar())
			{
				costStar = msg.coststar();
			}

			transferType = 1;
			nRet = GetStarStationTransferMgr().useStarForSanJiaoMenTransfer(player, msg.deststarstationid(), costStar);
		}
	}
	
	PB_StarStationTransferDeductFeeHC deductFeeHC;
	deductFeeHC.set_result(nRet);
	deductFeeHC.set_transfertype(transferType);
	GetGameNetManagerPtr()->sendToClient(uin, PB_NOTIFY_STARSTATION_TRANSFER_RESULT_HC, deductFeeHC);
}

void MpGameSurviveNetHandler::handleUpdateAchievement2Host(int uin, const PB_PACKDATA &pkg)
{
	PB_AchievementUpdateCH msg;
	// //2021/07/14 参数填错 codeby:wudeshen
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MNSandbox::GetGlobalEvent().Emit<game::ch::PB_AchievementUpdateCH &>("AchievementManager_handleAchievementUpdate2Host", msg);
}

//20210714购买菜谱是否刷新
void MpGameSurviveNetHandler::handleHomeLandMenuBuySuccess2Host(int uin ,const PB_PACKDATA &pkg)
{
	PB_HomeLandMenuBuyHC menuBuyHc;
	GetGameNetManagerPtr()->sendBroadCast(PB_HOMELAND_COOK_MENUBUY_HC, menuBuyHc);
	MINIW::ScriptVM::game()->callFunction("FreshHomelandMenuData", NULL);
}

/*
	20210724:特惠家具购买同步刷新状态 codeby：yangzhenyu
*/
void MpGameSurviveNetHandler::handleHomeLandSpecialFurnitureBuySuccess2Host(int uin ,const PB_PACKDATA &pkg)
{
	PB_HomeLandSpecialFurnitureBuyHC furnitureBuyHC;
	GetGameNetManagerPtr()->sendBroadCast(PB_HOMELAND_COOK_SPFURNITUREBUY_HC, furnitureBuyHC);
	MINIW::ScriptVM::game()->callFunction("FreshHomelandSpecialFurnitureData", NULL);
}

void MpGameSurviveNetHandler::handleHomeLandShopCell2Host(int uin,const PB_PACKDATA &pkg)
{
	PB_HomeLandShopCellCH shopcellCH;
	shopcellCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	PB_HomeLandShopCellHC shopcellHC;
	shopcellHC.set_uin(shopcellCH.uin());
	shopcellHC.set_itemid(shopcellCH.itemid());
	shopcellHC.set_num(shopcellCH.num());
	GetGameNetManagerPtr()->sendBroadCast(PB_HOMELAND_FARM_SHOP_HC, shopcellHC);
	MINIW::ScriptVM::game()->callFunction("FreshHomelandFarmshopSellData", "iii",shopcellCH.uin(),shopcellCH.itemid(),shopcellCH.num());
}

void MpGameSurviveNetHandler::handleGainItemsToBackPack2Host(int uin, const PB_PACKDATA &pkg)
{
	//  禁止直接添加道具协议
//#ifndef IWORLD_SERVER_BUILD
//	ClientPlayer *player = uin2Player(uin);
//	if (player == NULL)
//	{
//		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
//		return;
//	}
//
//	PB_GainItemsToBackPackCH msg;
//	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
//	if(uin != msg.playeruin())
//	{
//		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
//		return;
//	}
//	int itemId = msg.itemid();
//	int num = msg.itemnum();
//	player->gainItems(itemId, num, 1);
//#endif
}

void MpGameSurviveNetHandler::handleGainItemsUserDataStrToBackPack2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_GainItemsUserDatastrToBackPackCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int itemId = msg.itemid();
	int num = msg.itemnum();
	if (!player->GetCheatHandler()->checkGainMusicPaper(itemId, num))
		return;
	
	std::string userdata_str = msg.userdata_str();
	player->gainItemsUserdata(itemId, num, userdata_str.c_str());

}

void MpGameSurviveNetHandler::handleUseMusicYuPu2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_UseMusicYuPuCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int itemId = msg.itemid();
	int num = msg.itemnum();
	int index = msg.itemindex();

	if (num <= 0)
	{
		jsonxx::Object cheat;
		cheat << "client_num" << num;
		ActionLogger::ErrorLog(uin, 0, "cheat_remove_yupu", cheat);
		return;
	}

	player->getBackPack()->removeItem(index, num);
}

void MpGameSurviveNetHandler::handleDanceByPlaying2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_DanceByPlayingCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int64 mob_uin = msg.mobuin();
	int player_uin = msg.playeruin();
	std::ostringstream ss;
	ss << mob_uin;
	std::string mob_uinKey = ss.str();
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_MobDanceByPlayingCH", SandboxContext(nullptr).SetData_String("mob_uin", mob_uinKey).SetData_Number("player_uin", player_uin));
}


void MpGameSurviveNetHandler::handleStopDanceByPlaying2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_DanceByPlayingCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int64 mob_uin = msg.mobuin();
	int player_uin = msg.playeruin();
	std::ostringstream ss;
	ss << mob_uin;
	std::string mob_uinKey = ss.str();
	SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_StopMobDanceByPlayingCH", SandboxContext(nullptr).SetData_String("mob_uin", mob_uinKey).SetData_Number("player_uin", player_uin));
}

void MpGameSurviveNetHandler::handleStartPlayingPiano2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_StartActCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int pianoplaystate = msg.playingstate();
	int actID = msg.actid();
	player->playAnim(actID, false, 127, pianoplaystate);
}

void MpGameSurviveNetHandler::handleStopPlayingPiano2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_StopActCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	int actID = msg.actid();
	player->stopAnim(actID);
}

void MpGameSurviveNetHandler::handleShowTopBrand2Host(int uin, const PB_PACKDATA &pkg)
{
	// 因本协议消息不会经过敏感词筛选, 已被外挂利用, 不再由客客机发起, 业务需要时建议由主机触发  2024.07.17 by huanglin
	return;

	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_TopBrandCH topBrandCH;
	topBrandCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != topBrandCH.targetuin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}
	player->setTopBrand(topBrandCH.brandname().data());


#ifdef IWORLD_SERVER_BUILD
	{
		PB_TopBrandHC topBrandHC;
		topBrandHC.set_targetuin(uin);
		topBrandHC.set_brandname(topBrandCH.brandname().data());
		GetGameNetManagerPtr()->sendBroadCast(PB_TOP_BRAND_HC, topBrandHC);
	}
#endif
}

/*
	20210823:使用背包道具兑换其他背包道具 codeby：wangyu
*/
void MpGameSurviveNetHandler::handleExchangeItemsToBackPack2Host(int uin, const PB_PACKDATA &pkg)
{
	// codeby:liusijia 2022/07/19 经过跟wangyu确认，此协议目前不用，注释掉
	ActionLogger::SimpleErrLog(uin, 0, "exchange_items_back_pack", "should not use!");

	/*ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_ExchangeItemsToBackPackCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	int useitemid = msg.useitemid();
	int usenum = msg.useitemnum();
	int gainitemid = msg.gainitemid();
	int gainnum = msg.gainitemnum();
	int type = msg.opertype();
	int result = 0;
	result = player->exchangeItems(useitemid, usenum, gainitemid, gainnum);

	PB_ExchangeItemsToBackPackResultHC exchangeItemsResult;
	exchangeItemsResult.set_result(result);
	exchangeItemsResult.set_opertype(type);
	if (GetGameNetManagerPtr())
	{
		GetGameNetManagerPtr()->sendToClient(uin, PB_EXCHANGEITEMSTOBACKPACKRESULT_HC, exchangeItemsResult);
	}*/
}

void MpGameSurviveNetHandler::handleCoustomUi2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_CoustomUIEvent msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_CoustomUIEvent", SandboxContext(nullptr).SetData_Number("uin",double(uin)).SetData_UserObject<PB_CoustomUIEvent>("msg", msg));
}

void MpGameSurviveNetHandler::handleNewAdNpcAddExp2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_AddExpCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	int exp = msg.starnum() * EXP_STAR_RATIO;
	// 用于消耗 exp必须是负数
	if (exp >= 0)
	{
		jsonxx::Object cheat;
		cheat << "cost" << exp;
		ActionLogger::InfoLog(uin, 0, "cheat_new_adnpc_cost", cheat);
		return;
	}

	int result = 0;
	PlayerAttrib *playerAttr = player->getPlayerAttrib();
	if (playerAttr && GetGameNetManagerPtr())
	{
		int expBefore = playerAttr->getExp();
		if (exp > 0 || (exp < 0 && expBefore >= abs(exp)))
		{
			playerAttr->addExp(exp);
			if ((expBefore + exp) == playerAttr->getExp())
			{
				result = 1;
			}
		}

		PB_AddExpResultHC addExpResult;
		addExpResult.set_uin(uin);
		addExpResult.set_op(msg.op());
		addExpResult.set_result(result);

		GetGameNetManagerPtr()->sendToClient(uin, PB_ADDEXPRESULT_HC, addExpResult);
	}
}

void MpGameSurviveNetHandler::handldUseHearth2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_UseHearthCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (uin != msg.playeruin())
	{
		sendError2Client(uin, PB_ERROR_WRONG_ARGS);
		return;
	}

	WCoord blockPos = WCoord(msg.hearthpos().x(), msg.hearthpos().y(), msg.hearthpos().z());
	// 打开灶台
	if (msg.isuse())
	{
		if (player->getLocoMotion())
		{
			player->getLocoMotion()->m_yOffset = 0;
			player->setPosition(msg.playerpos().x(), msg.playerpos().y(), msg.playerpos().z());
			//player->setFaceYaw(playerYaw);
			player->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
		}

		player->playAnim(SEQ_COOK);

		
		if (player->getWorld() && player->getWorld()->blockExists(blockPos))
		{
			int blockdata = player->getWorld()->getBlockData(blockPos);

			player->getWorld()->setBlockData(blockPos, blockdata + 4);
		}
	}
	// 关闭灶台
	else
	{
		player->stopAnim(SEQ_COOK);
		player->playAnim(SEQ_STAND);

		if (player->getWorld() && player->getWorld()->blockExists(blockPos))
		{
			int blockdata = player->getWorld()->getBlockData(blockPos);

			player->getWorld()->setBlockData(blockPos, blockdata - 4);
		}
	}
}

//916冒险 2021/08/18 codeby:wudeshen
void MpGameSurviveNetHandler::handleAnswerLanternBird2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_AnswerLanternBird_CH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ClientMob* mob = player->getWorld()->getActorMgr()->ToCastMgr()->findMobByWID(msg.uin());
	if (!mob)
		return;
	MINIW::ScriptVM::game()->callFunction("RiddleBird_PlayerAnswer", "u[ClientPlayer]u[ClientMob]i", player, mob, msg.answer());
}



void MpGameSurviveNetHandler::handlePlayerCloseUI2Host(int uin, const PB_PACKDATA &pkg) {
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayeCloseUICH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	MINIW::ScriptVM::game()->callFunction("PlayerCloseUI", "u[ClientPlayer]ss", player, msg.uiname().c_str(), msg.uiparam().c_str());
};


void MpGameSurviveNetHandler::handleChangeQQMusicPlayer2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_ChangeQQMusicPlayerCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_ChangeQQMusicPlayerCH", SandboxContext(nullptr).SetData_Number("uin",double(uin)).SetData_UserObject<PB_ChangeQQMusicPlayerCH>("msg", msg));
}

void MpGameSurviveNetHandler::handleCustomMsg2Host(int uin, const PB_PACKDATA &pkg)
{
	//ClientPlayer *player = uin2Player(uin);
	//if (player == NULL)
	//{
	//	sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
	//	return;
	//}
	PB_Custom_Msg msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	char* name = (char*)msg.msgname().c_str(); 
	char* content = (char*)msg.content().c_str(); 
	int srcLen = msg.ziplen();
#ifdef IWORLD_SERVER_BUILD
	GetGameNetManagerPtr()->record_recved_7000_msg(name, srcLen);
#endif	
	SDB_NETMONITOR_RECV_CUSTOM((unsigned)(pkg.ByteSize + PB_PROTO_HEAD_LEN + 9), name, uin)
	//jsonxx::Object obj;
	//obj.parseBinary((unsigned char*)content, srcLen);
	//obj<<"player_uin"<<uin;
	//GetSandBoxManager().doEventEx(name, &obj);
	GetSandBoxManager().doEventExBin(name, uin, content, srcLen);
}
void MpGameSurviveNetHandler::handleHomelandRanchAnimalState2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_HomeLandRanchUpdateAnimalStateCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_RanchAnimalState2Host", SandboxContext(nullptr).SetData_UserObject<PB_HomeLandRanchUpdateAnimalStateCH>("ranchUpdateAnimalState", msg));
}

void MpGameSurviveNetHandler::handleHomelandRanchFooderInfo2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_OneHomelandFooderRanchAnimalCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("Homeland_RanchFooderInfo2Host", SandboxContext(nullptr).SetData_UserObject<PB_OneHomelandFooderRanchAnimalCH>("fooderRanchAnimal", msg));
}

//20210926: MiniClub音乐  codeby:wangshuai
void MpGameSurviveNetHandler::handleMiniClubPlayer2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_MiniClubMusicPlayerCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_MiniClubMusicPlayerCH", SandboxContext(nullptr).SetData_Number("uin",double(uin)).SetData_UserObject<PB_MiniClubMusicPlayerCH>("msg", msg));
}

//20211101 手持物品 codeby:luoshuai
void MpGameSurviveNetHandler::handlePlayerEquipWeapon2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_EquipWeaponCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	//MusicClubSync::GetInstancePtr()->BroadcastEquipWeapon(uin, msg);
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
		Emit("PB_EquipWeaponCH", SandboxContext(nullptr).SetData_Number("uin", double(uin)).SetData_UserObject<PB_EquipWeaponCH>("msg", msg));
}

//20210915 音乐方块 codeby:huangxin
void MpGameSurviveNetHandler::handleChangeQQMusicClub2Host(int uin, const PB_PACKDATA& pkg)
{
	//ClientPlayer* player = uin2Player(uin);
	//if (player == NULL)
	//{
	//	sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
	//	return;
	//}
	//PB_ChangeQQMusicClubCH msg;
	//msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	//SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
	//	Emit("PB_ChangeQQMusicClubCH", SandboxContext(nullptr).SetData_Number("uin", double(uin)).SetData_UserObject<PB_ChangeQQMusicClubCH>("msg", msg));
}

void MpGameSurviveNetHandler::handleAdShopBuy2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_BuyAdShopGoods buyGoods;
	if (!buyGoods.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleAdShopBuy2Host PB_BuyAdShopGoods");
		return;
	}

	int tabId = buyGoods.tabid();
	int goodId = buyGoods.goodid();
	int step = buyGoods.step();

	if (player->GetCheatHandler() && !player->GetCheatHandler()->checkADShopCheat(tabId, goodId))
	{ // 操作检查异常
		return;
	}
	
	// 1 表示开始购买  2表示迷你币等消耗已完成
	if (step == 1)
	{
		// 服务器只有clientplayer 只存在成功失败两种状态
		int success = player->tryBuyAdNpcGood(tabId, goodId);
		buyGoods.set_success(success);
	}
	else if (step == 2)
	{
		player->onBuyAdNpcGood(tabId, goodId);
	}

	GetGameNetManagerPtr()->sendToClient(uin, PB_BUY_AD_SHOP_GOOD_HC, buyGoods);
}

void MpGameSurviveNetHandler::handleAchievementAward2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_GetAchievementAwardCH getAward;
	if (!getAward.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleAchievementAward2Host PB_GetAchievementAwardCH");
		return;
	}
	player->getAchievementAward(getAward.taskid());
}

void MpGameSurviveNetHandler::handleCheckInfo2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_UploadCheckInfoCH info;
	if (!info.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleCheckInfo2Host PB_UploadCheckInfoCH");
		return;
	}

	player->onClientUploadCheckInfo(info.info_type(), info.detail());
}

void MpGameSurviveNetHandler::handleGetAdShopExtraAward2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_GetAdShopExtraAwardCH award;
	if (!award.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleGetAdShopExtraAward2Host PB_GetAdShopExtraAwardCH");
		return;
	}

	player->tryGetAdShopExtraAward(award.award_id(), award.item_id(), award.item_count());
}

void MpGameSurviveNetHandler::handleExtraStoreItem2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_ExtractStoreItemCH store;
	if (!store.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleExtraStoreItem2Host PB_ExtractStoreItemCH");
		return;
	}

	{  // 开发者商店提取道具已不走此协议 2024.07.29 by huanglin
		jsonxx::Object cheat;
		cheat << "index" << store.store_index();
		cheat << "item" << store.item_id();
		cheat << "count" << store.item_count();
		ActionLogger::InfoLog(uin, 0, "cheat_extra_store", cheat);
		return;
	}

	player->tryExtractStoreItem(store.store_index(), store.item_id(), store.item_count());
}

void MpGameSurviveNetHandler::handleClientActionLog2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_SyncClientActionLogCH actionlog;
	if (!actionlog.ParseFromArray(pkg.MsgData, pkg.ByteSize))
	{
		PROTO_PARSE_ERR(uin, "handleClientActionLog2Host PB_SyncClientActionLogCH");
		return;
	}

	const std::string eventName = actionlog.event();
	const std::string detail = actionlog.detail();
	if (eventName.size() > 0 && detail.size() > 0)
	{
		jsonxx::Object log;
		if (log.parse(actionlog.detail()) && player->GetCheatHandler())
		{
			player->GetCheatHandler()->OnClientAction(eventName, log);
		}
	}
}

//
void MpGameSurviveNetHandler::handlePlayEffect2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_PlayEffectCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	EffectManager *effectmgr = player->getWorld()->getEffectMgr();
	ActorManagerInterface* actormgrinterface = player->getWorld()->getActorMgr();
	if (effectmgr == NULL)
		return;
	if (msg.effecttype() == PB_EFFECT_SOUND_NEW || msg.effecttype() == PB_EFFECT_SOUND_NEW_FOR_TRACK)
	{
		const PB_EffectSoundNew &sound = msg.soundnew();
		LongSound ss;
		ss.volume = sound.volume();
		ss.pitch = sound.pitch();
		ss.duration = sound.duration();
		ss.startTime = sound.starttime();
		strcpy(ss.path, sound.name().c_str());
		strcpy(ss.extraStr, sound.extrastr().c_str());
		strcpy(ss.url, sound.url().c_str());
		ss.instrumentCode = sound.instrumentcode();
		ss.trackId = sound.trackid();
		ss.soundPos = sound.soundpos();
		ss.setPos(MPVEC2WCoord(sound.pos()));
		ss.objid = sound.objid();
		ss.type = sound.soundtype();
		ss.isLoop = sound.isloop();
		ss.effecttype = msg.effecttype();
		effectmgr->playLongSoundHost(ss, msg.effecttype());
	}
	else if (msg.effecttype() == PB_EFFECT_SOUND_NEW_STOP)
	{
		const PB_EffectSoundNew &sound = msg.soundnew();
		if (sound.objid() > 0)
		{
			effectmgr->stopLongSound3D(sound.objid(), sound.name());
		}
		else if (sound.objid() == -1)
		{
			effectmgr->stopLongSound2D(sound.name());
		}
		else
		{
			effectmgr->stopLongSound3D(MPVEC2WCoord(sound.pos()), sound.name());
		}
	}
	else if (msg.effecttype() == PB_EFFECT_SOUND_NOTE)
	{
#ifdef DEDICATED_SERVER
		if (player->getBackPack())
		{
			int shortcutIndex = player->getCurShortcut() + player->getShortcutStartIndex();
			int itemId = player->getBackPack()->getGridItem(shortcutIndex);
			auto def = GetDefManagerProxy()->getItemDef(itemId);
			if (itemId == 0 || def == nullptr || def->Type != ITEM_TYPE_MUSIC)
			{
				jsonxx::Object cheat;
				cheat << "itemid" << itemId;
				if (def)
					cheat << "type" << def->Type;
				ActionLogger::InfoLog(uin, 0, "cheat_music_item", cheat);
				return;
			}
		}
#endif
		const PB_EffectSoundNew &sound = msg.soundnew();
		effectmgr->playMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode(), sound.volume(), sound.tpqcount());
	}
	else if (msg.effecttype() == PB_EFFECT_SOUND_NOTE_STOP)
	{
		const PB_EffectSoundNew &sound = msg.soundnew();
		effectmgr->stopMidiMusicNote(sound.objid(), sound.notecode(), sound.instrumentcode());
	}
	else if (msg.effecttype() == PB_EFFECT_STRINGACTORBODY)
	{
		if (!actormgrinterface)
		{
			return;
		}
		ClientActorMgr* actormgr = actormgrinterface->ToCastMgr();
		const PB_EffectStringActorBody& eb = msg.stringactorbody();
		float effectScale = msg.effectscale() / 1000.0f;
		float loopPlayTime = eb.loopplaytime();

		ClientActor* actor = actormgr->findActorByWID(eb.objid());
		if (actor)
		{
			auto effectComponent = actor->getEffectComponent();
			if (effectComponent)
			{
				auto effectComponent = actor->getEffectComponent();
				if (effectComponent)
				{
					if (eb.status() == 0)
					{
						effectComponent->playBodyEffectForTriggerNoSync((char*)eb.effectname().c_str(), effectScale, loopPlayTime);
					}
					else if (eb.status() == 10 || eb.status() == 1000) {
						// 应该调用主机播放接口，才能同步给其他客机
						//effectComponent->playBodyEffectClient((char*)eb.effectname().c_str(), effectScale, loopPlayTime);
						effectComponent->playBodyEffect((char*)eb.effectname().c_str(), effectScale, loopPlayTime, false);
					}
					else
						//effectComponent->stopBodyEffectClient((char*)eb.effectname().c_str());
						effectComponent->stopBodyEffect((char*)eb.effectname().c_str());
				}
			}

		}
	}
}

//20211020 主机收到同步设置喷漆贴图ID的处理 codeby:柯冠强
void MpGameSurviveNetHandler::handleSprayPaintInfo2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	if (g_WorldMgr == NULL) return;//防止崩溃 code_by:huangfubin 2022.5.27

	PB_SprayPaintInfoCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	SprayPaintMgr* sprayPaintMgr = dynamic_cast<SprayPaintMgr*>(g_WorldMgr->getSandboxMgr("SprayPaintMgr"));
	if (sprayPaintMgr)
	{
		sprayPaintMgr->setSprayPaintId(uin, msg.paintid());
	}
}
//20230309 黑板文字同步 codeby:huangxin
void MpGameSurviveNetHandler::handleChangeBlockData2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}

	PB_BlockDataCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
#ifdef IWORLD_UNIVERSE_BUILD
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
	Emit("PB_BlockDataCH", SandboxContext(nullptr).SetData_Number("uin", double(uin)).SetData_UserObject<PB_BlockDataCH>("msg", msg));
#else
	//SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
	//	Emit("PB_BlockDataCH", SandboxContext(nullptr).SetData_Number("uin", double(uin)).SetData_UserObject<PB_BlockDataCH>("msg", msg));
#endif
}

void MpGameSurviveNetHandler::handleCheatCheck2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer *player = uin2Player(uin);
	if (player == NULL)
	{
		sendError2Client(uin, PB_ERROR_OP_NOT_FOUND);
		return;
	}
	PB_HostCheckCheat msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	switch(msg.checktype()){
		case CCT_JumpHeight:
			if (!msg.has_jump()){
				LOG_WARNING("handleCheatCheck2Host jump %d range not found", CCT_JumpHeight);
				return;
			}
			if (player->GetCheatHandler())
			{
				auto &jump = msg.jump();
				player->GetCheatHandler()->CheckJump(jump.jumpheight(), jump.onground(), jump.airjump());
			}
			break;
		case CCT_TackleRange:
			if (!msg.has_tackle()){
				LOG_WARNING("handleCheatCheck2Host tackle %d range not found", CCT_TackleRange);
				return;
			}
			if (player->GetCheatHandler())
				player->GetCheatHandler()->CheckTackleRange(msg.tackle().range());
			break;
		case CCT_GrabRange:
			if (!msg.has_grab()){
				LOG_WARNING("handleCheatCheck2Host grab %d range not found", CCT_GrabRange);
				return;
			}
			if (player->GetCheatHandler())
				player->GetCheatHandler()->CheckGrabRange(msg.grab().range());
			break;
		case CCT_DribbleRange:
			if (!msg.has_dribble()){
				LOG_WARNING("handleCheatCheck2Host dribble %d range not found", CCT_DribbleRange);
				return;
			}
			if (player->GetCheatHandler())
				player->GetCheatHandler()->CheckDribbleRange(msg.dribble().range());
			break;
		case CCT_Clip:
			if (!msg.has_clip()){
				LOG_WARNING("handleCheatCheck2Host clip %d not found", CCT_Clip);
				return;
			}
			if (player->GetCheatHandler()){
				auto &clip = msg.clip();
				if (clip.has_bound_height() && clip.has_bound_size())
					player->GetCheatHandler()->CheckClipBound(clip.bound_height(), clip.bound_size());
				if (clip.has_radius() && clip.has_half_height())
					player->GetCheatHandler()->CheckClipController(clip.radius(), clip.half_height());
			}
			break;
		default:
			LOG_WARNING("handleCheatCheck2Host checktype unknow %d", int(msg.checktype()));
	}
}

void MpGameSurviveNetHandler::handlePvpActivityConfig2Host(int uin, const PB_PACKDATA& pkg)
{
#ifdef IWORLD_SERVER_BUILD
	ClientPlayer* player = uin2Player(uin);
	if (player)
	{
		PB_PvpActivityConfigCH msg;
		msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		player->SetPvpActivityConfig(msg);
	}
#endif
}

void MpGameSurviveNetHandler::handleExposePos2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_ExposePosChangeCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->SetExposePosToOther(pbCH.isexpose());
}


/**
 * @brief 处理客机上传埋点玩家数据, 目前仅用于云服
 */
void MpGameSurviveNetHandler::handleSetClientPlayerInfo2Host(int uin, const PB_PACKDATA& pkg)
{
#ifdef IWORLD_SERVER_BUILD
	PB_UploadClientInfoCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	ActionLoggerBigData::getInstance()->setClientPlayerInfo(uin, msg.info().c_str());
#endif
}

void MpGameSurviveNetHandler::handleStartFishing2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_STARTFISHINGCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	WCoord targetPos = MPVEC2WCoord(pbCH.targetpos());
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->setFishingTarget(targetPos);
		pCom->startFishing();
	}
}

void MpGameSurviveNetHandler::handleEndFishing2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_ENDFISHINGCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	auto* pCom = player->getFishingComponent();
	if (pCom)
	{
		pCom->endFishing();
	}
}

void MpGameSurviveNetHandler::handleQuitFishing2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_ENDFISHINGCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	auto* pCom = player->m_pFishingComponent;
	if (pCom)
	{
		pCom->forceQuitFishing();
	}
}

void MpGameSurviveNetHandler::handleEndPlayFish2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	auto* pCom = player->m_pFishingComponent;
	if (pCom)
	{
		pCom->endPlayFish();
	}
}

void MpGameSurviveNetHandler::handlePlayWeaponEffectHost(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (!player)
		return;

	PB_PlayWeaponEffectCH playWeaponEffectCH;
	playWeaponEffectCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	tdr_ulonglong target_objId = playWeaponEffectCH.objid();
	ClientPlayer* target_player = checkPlayerByMsg2Host(target_objId);
	if (target_player && target_player->getBody())
	{
		if (playWeaponEffectCH.effectstatus() == 0)
			target_player->getBody()->playWeaponMotion(playWeaponEffectCH.effectname().c_str(), true, playWeaponEffectCH.effectid(), (float)playWeaponEffectCH.effectscale() / 100.0f);
		else
			target_player->getBody()->stopWeaponMotion(playWeaponEffectCH.effectid());
	}

	PB_PlayWeaponEffectHC playWeaponEffectHC;
	playWeaponEffectHC.set_effectname(playWeaponEffectCH.effectname());
	playWeaponEffectHC.set_effectid(playWeaponEffectCH.effectid());
	playWeaponEffectHC.set_effectscale(playWeaponEffectCH.effectscale());
	playWeaponEffectHC.set_effectstatus(playWeaponEffectCH.effectstatus());
	playWeaponEffectHC.set_objid(target_objId);
	player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYWEAPONEFFECT_HC, playWeaponEffectHC, player);
}

void MpGameSurviveNetHandler::handlePlayAnimation2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (!player)
		return;

	PB_ActorPlayAnimCH actorPlayAnimCH;
	actorPlayAnimCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor* actor = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(actorPlayAnimCH.objid());
	if (!actor)
		return;

	ActorBody* body = actor->getBody();
	if (body)
	{
		if (actorPlayAnimCH.preseqid() > -1)
		{
			if (body->getEntity() && body->getEntity()->GetMainModel() && body->getEntity()->GetMainModel()->IsKindOf<Rainbow::ModelLegacy>())
			{
				Rainbow::ModelLegacy* legacymodel = static_cast<Rainbow::ModelLegacy*>(body->getEntity()->GetMainModel());
				if (legacymodel != nullptr && legacymodel->GetAnimPlayer())
				{
					legacymodel->GetAnimPlayer()->SetAnimPriority(actorPlayAnimCH.preseqid(), actorPlayAnimCH.prelayer());
				}
			}
		}
		body->playAnimCheck(actorPlayAnimCH.seqid(), actorPlayAnimCH.loop(), actorPlayAnimCH.speed(), actorPlayAnimCH.layer());
	}

	if (actorPlayAnimCH.triggerattack())
		player->attackOnTrigger();

	PB_ActorPlayAnimHC actorPlayAnimHC;
	actorPlayAnimHC.set_objid(actorPlayAnimCH.objid());
	actorPlayAnimHC.set_seqid(actorPlayAnimCH.seqid());
	actorPlayAnimHC.set_loop(actorPlayAnimCH.loop());
	actorPlayAnimHC.set_speed(actorPlayAnimCH.speed());
	actorPlayAnimHC.set_layer(actorPlayAnimCH.layer());
	actorPlayAnimHC.set_preseqid(actorPlayAnimCH.preseqid());
	actorPlayAnimHC.set_prelayer(actorPlayAnimCH.prelayer());
	player->getWorld()->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_PLAY_ANIM_HC, actorPlayAnimHC, player);
}

void MpGameSurviveNetHandler::handleActorAttack2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	ClientActorMgr* actorManager{ nullptr };
	if (player && player->getWorld() && player->getWorld()->getActorMgr())
		actorManager = player->getWorld()->getActorMgr()->ToCastMgr();
	else
		return;

	PB_ActorAttackCH actorAttackCH;
	actorAttackCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	ClientActor* actor = actorManager->findActorByWID(actorAttackCH.objid());
	if (!actor || !actor->IsKindOf<ActorLiving>())
		return;

	ActorLiving* actorLiving = actor->ToCast<ActorLiving>();
	if (!actorLiving)
		return;

	auto funcCanAttack = [](ClientPlayer* player, ClientActor* target)
	{
		if (player && player->canAttack() && target && target->alive() && GetClientInfoProxy())
			return player->performInteractActor(target, GetClientInfoProxy()->isMobile() ? 1 : 0, false, true);
		else
			return false;
	};

	//触发器事件
	if (actorAttackCH.triggerattackhit() && actorAttackCH.targetids_size() > 0 && player->canAttack() && GetClientInfoProxy())
	{
		auto actorID = actorAttackCH.targetids(0);
		ClientActor* target = actorManager->findActorByWID(actorID);
		if (funcCanAttack(player, target))
		{
			player->attackHitOnTrigger(actorID, target->getDefID());
		}
	}

	//判断连击状态的伤害计算
	bool comboAtkCal = false;
	if (!actorAttackCH.attackdefname().empty())
	{
		AttackJsonManager* atkJsonMgr = GET_SUB_SYSTEM(AttackJsonManager);
		if (atkJsonMgr)
		{
			AttackDef* atkDef = atkJsonMgr->getAttackDef(actorAttackCH.attackdefname());
			if (atkDef)
			{
				actorLiving->ComboAttackCalculate = true;
				actorLiving->dataAttackDef = atkDef;
				comboAtkCal = true;
			}
		}
	}
	//计算伤害
	for (size_t i = 0; i < actorAttackCH.targetids_size(); i++)
	{
		ClientActor* target = actorManager->findActorByWID(actorAttackCH.targetids(i));
		if (funcCanAttack(player, target))
		{
			actorLiving->attackActor(target);
		}
	}
	//还原状态
	if (comboAtkCal)
	{
		actorLiving->ComboAttackCalculate = false;
		actorLiving->dataAttackDef = nullptr;
	}
}

void MpGameSurviveNetHandler::handleActorDefanceState2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	ClientActorMgr* actorManager{ nullptr };
	if (player && player->getWorld() && player->getWorld()->getActorMgr())
		actorManager = player->getWorld()->getActorMgr()->ToCastMgr();
	else
		return;

	PB_ActorDefanceStateCH actorDefanceStateCH;
	actorDefanceStateCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor* actor = actorManager->findActorByWID(actorDefanceStateCH.objid());
	if (!actor || !actor->IsKindOf<ActorLiving>())
		return;

	ActorLiving* living = dynamic_cast<ActorLiving*>(actor);
	if (living)
	{
		bool isDefanceState = actorDefanceStateCH.defancestate();
		living->setInDefanceState(isDefanceState);
	}
}

void MpGameSurviveNetHandler::handleSyncMove2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	game::ch::PB_MoveSyncCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	
	unsigned id = pbCH.id();

	for (int i = 0; i < pbCH.flag_change_size(); ++i)
	{
		auto &flag_change = pbCH.flag_change(i);
		player->changeMoveFlag(flag_change.type(), flag_change.on());
	}

	unsigned long long tick = pbCH.tick();
	bool tick_valid = player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(tick);
	if (pbCH.has_pos())
	{
		if (tick_valid)
			player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), tick);
		else
			player->setCheckMoveResult(id, MPVEC2WCoord(pbCH.pos()), 0);
	}

	if (pbCH.has_move_opera())
	{
		auto move_info = pbCH.move_opera();
		player->setMoveControl(move_info.opera(), move_info.yaw() / 1000.0f, move_info.pitch() / 1000.0f, tick_valid ? tick: 0);
	}

}

void MpGameSurviveNetHandler::handleRespSyncMove2Host(int uin, const PB_PACKDATA &pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	PB_ResetPosResponeCH pbCH;
	pbCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	if (player->GetCheatHandler() && player->GetCheatHandler()->checkClientTick(pbCH.tick()))
		player->onClientRspSyncPos(pbCH.tick());
	else
		player->onClientRspSyncPos(0);
}

//PB_ACTOR_PLAY_SOUND_CH
void MpGameSurviveNetHandler::handleActorPlaySound2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (!player) return;
	SoundComponent* soundComp = player->getSoundComponent();
	if (soundComp)
	{
		PB_ActorPlaySoundCH actorPlaySoundCH;
		actorPlaySoundCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		soundComp->playSound(actorPlaySoundCH.name().c_str(), actorPlaySoundCH.volume(), actorPlaySoundCH.pitch(), 3, actorPlaySoundCH.fixpitch());
	}
}


void MpGameSurviveNetHandler::handlePlayerReviveRequest2host(int uin, const PB_PACKDATA& pkg)
{
	// 反序列化消息
	PB_PlayerReviveRequestCH ch;
	if (!ch.ParseFromArray(pkg.MsgData, pkg.ByteSize)) {
		return;
	}

	// 获取关键数据
	long long playerId = ch.player_id();        // 救援者ID
	long long targetId = ch.target_id();        // 被救援者ID
	int action = ch.action();                   // 动作类型(1=开始救援, 2=取消救援, 3=完成救援)

	// 获取救援者(当前操作的玩家)
	ClientPlayer* reviverPlayer = checkPlayerByMsg2Host(uin);
	if (!reviverPlayer || reviverPlayer->getObjId() != playerId) {
		return;
	}

	// 获取目标玩家(被救援的倒地玩家)
	ClientPlayer* targetPlayer = dynamic_cast<ClientPlayer*>(g_WorldMgr->findActorByWID(targetId));
	if (!targetPlayer) {
		return;
	}

	// 获取目标玩家的倒地状态组件
	PlayerAttrib* targetAttrib = targetPlayer->getPlayerAttrib();
	if (!targetAttrib) {
		return;
	}

	PlayerDownedStateAttrib* downedStateAttrib = targetAttrib->getDownedStateAttrib();
	if (!downedStateAttrib) {
		return;
	}

	// 检查目标玩家是否真的处于倒地状态(除了取消救援的请求外)
	if (action != 2 && !targetAttrib->isPlayerDowned()) {
		return;
	}

	// 根据动作类型处理请求
	switch (action) {
	case 1: { // 开始救援
		// 检查距离
		float distance = reviverPlayer->getPosition().distanceTo(targetPlayer->getPosition());
		const float MAX_REVIVE_DISTANCE = downedStateAttrib->getConfig().revive_max_distance;

		if (distance > MAX_REVIVE_DISTANCE) {
			return; // 距离太远，忽略请求
		}

		// 开始救援
		downedStateAttrib->startRevive(reviverPlayer->getObjId());

		// 广播状态变化到所有客户端
		PB_PlayerDownedStateChangeHC notification;
		//notification.set_player_id(targetId);
		//notification.set_state(1); // 1表示正在被救援
		//notification.set_reviver_id(playerId);
		//GetGameNetManagerPtr()->boardcastToAllClient(PB_PLAYER_DOWNED_STATE_CHANGE_HC, notification);
		break;
	}
	case 2: { // 取消救援
		// 验证是否由当前救援者取消
		if (downedStateAttrib->getReviverActorId() != reviverPlayer->getObjId()) {
			return; // 不是当前救援者，忽略请求
		}

		// 取消救援
		downedStateAttrib->cancelRevive();

		// 广播状态变化到所有客户端
		PB_PlayerDownedStateChangeHC notification;
		//notification.set_player_id(targetId);
		//notification.set_state(0); // 0表示倒地但未被救援
		//notification.set_reviver_id(0);
		//GetGameNetManagerPtr()->boardcastToAllClient(PB_PLAYER_DOWNED_STATE_CHANGE_HC, notification);
		break;
	}
	case 3: { // 完成救援
		// 验证是否由当前救援者完成
		if (downedStateAttrib->getReviverActorId() != reviverPlayer->getObjId()) {
			return; // 不是当前救援者，忽略请求
		}

		// 检查距离
		float distance = reviverPlayer->getPosition().distanceTo(targetPlayer->getPosition());
		const float MAX_REVIVE_DISTANCE = downedStateAttrib->getConfig().revive_max_distance;

		if (distance > MAX_REVIVE_DISTANCE) {
			downedStateAttrib->cancelRevive();
			return; // 距离太远，取消救援
		}

		// 检查救援进度是否足够
		if (downedStateAttrib->getReviveProgress() < 100) {
			return; // 进度不足，忽略请求
		}

		// 完成救援，使用配置的血量恢复百分比
		float reviveHealthPercent = downedStateAttrib->getConfig().max_revive_health_percent;
		downedStateAttrib->completeRevive(reviveHealthPercent);

		//// 广播状态变化到所有客户端
		//PB_PlayerDownedStateChangeHC notification;
		//notification.set_player_id(targetId);
		//notification.set_state(2); // 2表示已救援成功
		//notification.set_reviver_id(playerId);
		//GetGameNetManagerPtr()->boardcastToAllClient(PB_PLAYER_DOWNED_STATE_CHANGE_HC, notification);
		break;
	}
	default:
		// 未知动作类型，忽略请求
		break;
	}
}

void MpGameSurviveNetHandler::handleActorShoot2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	CustomGunUseComponent* comp = player->sureCustomGunComponent();
	if (comp)
	{
		PB_ActorShootCH msg;
		msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
		comp->onPlayerShoot(msg);
	}
}

void MpGameSurviveNetHandler::handleActorFirework2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_ActorFireworkCH msg;
	msg.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->createFirework(13, msg.fireworkid());
}

void MpGameSurviveNetHandler::handleDecomposition2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;
	PB_DecompositionCH ch;
	ch.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	WCoord blockpos(ch.x(), ch.y(), ch.z());
	if (player->getWorld()->getContainerMgr())
	{
		WorldContainer* container = player->getWorld()->getContainerMgr()->getContainer(blockpos);
		ContainerDecomposition* Decomposition = dynamic_cast<ContainerDecomposition*>(container);
		if (Decomposition)
		{
			Decomposition->OnMessage(ch.type(),ch.data());
		}
	}
}

void MpGameSurviveNetHandler::handleAllSingleBuildData2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	PB_AllSingleBuildDataHC hc;
	const auto &AllCityData = player->getWorld()->getCityMgr()->GetAllSingleBuildData();
	for (const auto& it : AllCityData)
	{
		const CityBuildConfig::SingleBuildData* singleBuildData = GetCityConfigInterface()->getSingleBuildDataByName(it.second.buildName);
		if (!singleBuildData)
		{
			continue;
		}
		PB_SingleBuildData* itemdata = hc.add_builddatas();
		itemdata->set_buildname(std::to_string(singleBuildData->m_nameKey));

		for (const auto& itembuilddata : it.second.buildData)
		{
			PB_SingleBuildItemData* subItemData = itemdata->add_builditem();
			
			subItemData->set_x(itembuilddata.x);
			subItemData->set_z(itembuilddata.z);
			subItemData->set_crangex(itembuilddata.rangeX);
			subItemData->set_crangez(itembuilddata.rangeZ);
			subItemData->set_brangex(itembuilddata.bRangeX);
			subItemData->set_brangex(itembuilddata.bRangeZ);
		}
	}

	GameNetManager::getInstance()->sendToClient(uin, PB_AllSingleBuildData_HC, hc);
}

void MpGameSurviveNetHandler::handleResearch2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	player->research();
}

void MpGameSurviveNetHandler::handleTechBlueprint2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL)
		return;

	player->UseSelectTechBlueprints();
}

void MpGameSurviveNetHandler::handleBlockTrigger2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkDownedPlayerByMsg2Host(uin);
	if (player == NULL) return;

	PB_BlockInteractCH blockInteractCH;
	blockInteractCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	Rainbow::Vector3f vec(blockInteractCH.colptx() / 100.0f, blockInteractCH.colpty() / 100.0f, blockInteractCH.colptz() / 100.0f);
	WCoord blockpos = MPVEC2WCoord(blockInteractCH.blockpos());
	//if (blockInteractCH.has_blueprintid())
	//{
	//	if (auto attrib = player->getPlayerAttrib())
	//	{
	//		attrib->SetCurBuildingId(blockInteractCH.blueprintid());
	//	}
	//}
	player->triggerBlock(blockpos, (DirectionType)blockInteractCH.face(), vec);
}

void MpGameSurviveNetHandler::handlePlayerKillme(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;

	player->kill();
}

void MpGameSurviveNetHandler::handlePlayerSocMoveItem(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;

	PB_BackPackSetItemCH backPackSetItemCH;
	backPackSetItemCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	player->socMoveItem(backPackSetItemCH.itemid(), backPackSetItemCH.toindex(), backPackSetItemCH.num());
}

void MpGameSurviveNetHandler::handleDrinkWater2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;

	PB_DrinkWaterCH drinkwaterHC;
	drinkwaterHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->drinkWaterWithBlock(drinkwaterHC.x(), drinkwaterHC.y(), drinkwaterHC.z(), 100, drinkwaterHC.type(), drinkwaterHC.itemid());
}

void MpGameSurviveNetHandler::handleFillWater2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;

	PB_FillWaterCH fillwaterHC;
	fillwaterHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->fillWaterInBug(WCoord(fillwaterHC.x(), fillwaterHC.y(), fillwaterHC.z()), fillwaterHC.grididx(), fillwaterHC.type());
}

void MpGameSurviveNetHandler::handleProcessBuildBlock2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;

	PB_ProcessBuildBlockCH builddata;
	builddata.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->processBuildBlock(builddata.type(), WCoord(builddata.x(), builddata.y(), builddata.z()), builddata.has_level() ? builddata.level() : 0);
}