
#ifndef __UGCVocelModel_H__
#define __UGCVocelModel_H__

#define REPAIR_BUILD_CONTAINER 0

#include "voxelmodel.h"

#include "world_types.h"
#include "OgreBlock.h"
#include "container_world.h"
#include "UGCBluePrint_generated.h"
#include "BaseClass/SharePtr.h"

struct UGCBlockData
{
	Block data;
	WCoord relativepos;
};

struct UGCPreBlocksData
{
	WCoord pos;
	unsigned int blockdata;

	UGCPreBlocksData()
	{
		pos = WCoord(0, 0, 0);
		blockdata = 0;
	}
};

struct UGCBlocksData
{
	Vector3f pos;
	unsigned int blockdata;
	bool isAir;

	UGCBlocksData()
	{
		pos = Vector3f(0, 0, 0);
		blockdata = 0;
		isAir = false;
	}
};

struct UGCBluePrintContainer
{
	WorldContainer *container;
	WCoord relativepos;
};


struct UGCBluePrintMaterial
{
	int itemid;
	int num;
	int durable;
	int enchantnum;
	int enchants[MAX_ITEM_ENCHANTS];

	GridRuneData runedata;
	int getIthEnchant(int i)
	{
		assert(i >= 0 && i < enchantnum);
		return enchants[i];
	}
};

//tolua_begin
enum UGCBluePrintType
{
	CustomBluePrint,		//自定义蓝图
	OfficialBluePrint,		//官方蓝图
	ResBluePrint,			//资源库蓝图

	MaxBluePrint,			
};
//tolua_end

#define UGCBluePrint_File_Suffix ".vbp"

class ActorBody;

class VoxelModel;
class UGCBluePrint //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	UGCBluePrint();
	UGCBluePrint(WCoord start, WCoord dim);
	virtual ~UGCBluePrint();

	const std::string& GetKey()
	{
		return m_sKey;
	}

	const std::string& GetName()
	{
		return m_sName;
	}

	bool LoadFinish()
	{
		return m_loader;
	}

	void SetKey(const std::string& fileName)
	{
		m_sKey = fileName;
	}

	void SetName(const std::string& name)
	{
		m_sName = name;
	}

	int GetType()
	{
		return m_iType;
	}

	WCoord GetDim()
	{
		return m_Dim;
	}

	ActorBody *GetActorBody();

	void DeleteActorBody();

	void SetTexture(Rainbow::SharePtr<Rainbow::Texture2D> tex)
	{
		m_texture = tex;
	}

	Rainbow::SharePtr<Rainbow::Texture2D> GetTexture()
	{
		return m_texture;
	}

	//tolua_end
	

	bool Load(long long owid, const std::string& filename, UGCBluePrintType type);
	bool Load(const std::string& path);
	bool LoadByStdioPath(const std::string& path);//这个接口传如的path 不用加前缀了（gamePath2StdioPath）
	bool Save(long long owid, const std::string& filename, UGCBluePrintType type, bool rewrite = false);
	bool Save2Vbp(VoxelModel* pModel, const std::string& sFileName, const std::string& bluePrintName);
	bool SaveObj2Vbp(const char* sObjPath, const std::string& sFileName, std::string& sModelName);

	bool SetBluePrintData(World *pworld);
	//void buildBluePrint(World *pworld, WCoord start);
	WCoord ConvertWcoordByRotate(int rotatetype, WCoord pos);
	void PlayBuildSound(World *pworld, int blockid, WCoord pos);
	void PreBuildBluePrint(WCoord start, std::vector<UGCBlocksData>& preblocks);
	bool BlockHasDirrection(int blockId);

	ActorBody* CreateActorBody();
	void GetPreBlocks(std::vector<WCoord> &vpos, std::vector<unsigned int> &vcolor);

	void GetGridUserdataStr(std::string &str);

	WCoord GetStartPos()
	{
		return m_Start;
	}

	const std::string& GetAuthName()
	{
		return m_sAuthName;
	}

	int GetAuthUin()
	{
		return m_iAuthUin;
	}

	UGCBluePrintType GetBluePrintType()
	{
		return (UGCBluePrintType)m_iType;
	}

	void AddMaterial(int id);
	void AddMaterial(WorldContainer *container);
	int GetMaterialsNum();
	UGCBluePrintMaterial *GetMaterialInfo(int index);

	void SetDescInfo(const WCoord &start, const WCoord &dim, const std::string& authname, int authuin, const std::string& skey, UGCBluePrintType type);

	static std::string GetBluePrintDir(long long owid, UGCBluePrintType type);
	static std::string GetPackageDir(std::string fileName);
	std::string GetFullFilePath(long long owid, const std::string& filename, UGCBluePrintType type);
	void MakeFullFilePath(long long owid, const std::string& filename, UGCBluePrintType type);
	//公司内部使用
	void Save4Insider(World* pworld, const std::string& filename, const WCoord& startPos, const WCoord& dim);
private:
#if REPAIR_BUILD_CONTAINER
	struct recreateContainerData
	{
		WorldContainer* pcontainer;
		const FBSave::Coord3* relativepos;
	};
	bool fixContainerData(const Block& block, const FBSave::Coord3* relativepos, std::vector<recreateContainerData>& vNewContainer);
	bool fixContainerData(const Block& block, const FBSave::Coord3* relativepos, std::unordered_map<WCoord, recreateContainerData, WCoordHashCoder>& mNewContainer);
#endif // REPAIR_BUILD_CONTAINER
	struct recreateContainerData
	{
		WorldContainer* pcontainer;
		const FBSave::Coord3* relativepos;
	};
	bool fixContainerData(const Block& block, const FBSave::Coord3* relativepos, std::vector<recreateContainerData>& vNewContainer);
	bool fixContainerData(const Block& block, const FBSave::Coord3* relativepos, std::unordered_map<WCoord, recreateContainerData, WCoordHashCoder>& mNewContainer);

	bool LoadData(void *buf, int buflen);
	void SetAreaInfo(const WCoord &start, const WCoord &dim);
private:
	WCoord m_Start;
	WCoord m_Dim;
	std::string m_sAuthName;
	int m_iAuthUin;
	std::string m_sKey;
	std::string m_sName;
	int m_iType;
	long long m_iWid;

	flatbuffers::FlatBufferBuilder m_Builder;

	std::vector<UGCBluePrintMaterial> m_Materials;

	ActorBody* m_ActorBody;
	Rainbow::SharePtr<Rainbow::Texture2D> m_texture;

	bool m_loader;
	std::map<int, Rainbow::FixedString> m_PrefabMap;
};//tolua_exports

#endif
