#ifndef __I_PLAYER_CMD_COMMAND_H__
#define __I_PLAYER_CMD_COMMAND_H__ 1
#include <string>
#include <vector>
#include "ClientPlayer.h"
#include "OgreWCoord.h"
#include "SandboxGame.h"

using std::string;
using std::vector;

//tolua_begin
class IPlayerCmdCommand 
{
public:
    virtual void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) = 0;
};

class AddBackPackItemCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddBluePrintCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddBuffCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddDungeonChestCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddEnchantmentCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddExpCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddHpCommmand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddRadiationCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetOverflowHPCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddStrengthCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetOverflowStrengthCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ToggleUseStrengthCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddMiniCoinCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddUnlockItemCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AddWorldTimeCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CallDevScriptCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CallLuaFunctionCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class EXPORT_SANDBOXGAME CallLuaGMFunctionCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CallScriptSupportGMFunctionCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ChangeMapOwnerUinCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ChangePlayerModelCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ChangeRuleCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ClearBlocksCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ClearMobsCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetTimeScaleMobsCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateEcosysUnitCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateFireworkCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateLightningCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreatePortalCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateWorldRecordCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class EncryptModCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ExportChunksCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ExportStudioMapChunksCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ExportFullyCustomModelCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ExportModCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ExportSTLCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
    void exportSTL(ClientPlayer& clientPlayer, WCoord &start, WCoord&end, std::string stlname);
};

class FilterStringCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GenIconsCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GodCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GotoRecordTickCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class HorseEggCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class KillSelfCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class LiteCallCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class LoadCsvCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class LoadLuaCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class LoadModCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class MobGenCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class MutateCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class OperateCameraCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PauseRecordingCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PlaceVoxelModelCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PlayAnimCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PlayMotionCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PlayParticleEffectCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PostBlockCountInfoCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PostEcosystemInfoCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TransferBiomeCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateCanyonCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateNewCaveCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CreateDoubleWeaponIconCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};
class CreateAllBlockIconCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CountBlockInThisChunk : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PostEnchantmentInfoCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class PostWorldStatisticsCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class RemoveBlockCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class RemoveBuffCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class RemoveEnchantmentCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SaveVoxelModel2Command : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SaveVoxelModelCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetAchievementCompletionNumCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetBlockTickCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetCubicLeavesCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetDayColorCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetDayTimeSpeedCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetFaceExpressionCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetFogTimeRangeCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetFogVisibleRangeCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetGameVarCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetGlobalSoundCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetHoursCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetMoveSpeedCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ChangeSkinCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetNightColorCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetPlayerGamePermitsCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetProjectileDataCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetRecordSpeedCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetReflectCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetScreenBrightnessCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetShadowCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetTestItemIconIndexCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetTorchLightColorCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetTrainDataCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetViewRangeCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SpawnActorCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SpawnModActorCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};



class SpawnPetCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SummonHorseCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SummonPetCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchAirWallVisibilityCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchBluePrintOfficialCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchDebugPhysicsCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchDestroyEffectVisibilityCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchDisplayHurtDataCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchFogCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchGameModCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SwitchRobotTestCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TeleportCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TestAvatarCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TestGasUICommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ToggleDangerNightCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ToggleRainCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ToggleThunderCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ToggleSnowCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TrackBlockCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TryShiftShapeCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class UnlockItemCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};


class RuneCommand : public IPlayerCmdCommand{//code by:tanzhenyu
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GotoVolcanoCommand : public IPlayerCmdCommand{//code by:tanzhenyu
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ProfileCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class DanceCommand : public IPlayerCmdCommand {//code by:huangxin
public:
	void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 展示服务器状态信息
class ServerStatCommand : public IPlayerCmdCommand {//code by:liusijia
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 展示服务器状态信息
class ServerCloseCommand : public IPlayerCmdCommand {//code by:liusijia
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 开启关闭服务器日志
class SwitchServerLogCommand : public IPlayerCmdCommand {//code by:liusijia
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 开启关闭服务器分析日志
class ServerPerfCommand : public IPlayerCmdCommand {//code by:liusijia
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

//清除微缩方块、生物指令
class ClearCustomCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

//清除指定id方块
class ClearSPBlockCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

//清除显示板
class ClearDisplayBoardCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

//打开显示帧率
class FPSGraphicCommand : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

//Godray
class GodrayCommand : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};


class CullDistanceCommand : public IPlayerCmdCommand 
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 引擎的Profiler指令
class ProfilerCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class DuststormCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

//水印开关，免责
class WaterInfoCommand : public IPlayerCmdCommand{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class BlockTestCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class SyncTestCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class TempestCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class BlizzardCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class WaterPressureCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class GENModelCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class SwitchSkyboxCommand : public IPlayerCmdCommand 
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class SandboxCommand : public IPlayerCmdCommand {
public:
    virtual void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class SwitchGraphicQuality : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class SwitchEffectQuality : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class TemperatureCommand :public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GenTerrianPicture : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class  AdjustPlayerCommand : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

class ChangeWeatherCommand : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class BotConversationsCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class TaskCommand : public IPlayerCmdCommand {
public:
	virtual void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};


class OpenAuroraCommand : public IPlayerCmdCommand
{
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};


class SnowCoverCommand : public IPlayerCmdCommand
{
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ModelLeafCommand : public IPlayerCmdCommand
{
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ReloadShaderCommand : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ClearSoundCacheCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

//tolua_end
class DebugActorBox : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class DebugTLBox : public IPlayerCmdCommand
{
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class ComboAttackLogCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GetNearActorCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GunAdsCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GunAdstCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GunLookWeaponCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GunAutoFireAdsCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class EntryTrigCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class QueryPrefabLoadStateCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GameTuneCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class GltfCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class VehCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class AirDropCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetFlySpeedCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class SetVisionSizeCommand : public IPlayerCmdCommand {
public:
	void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

class CorpseCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const std::vector<string>& params) override;
};

// 加载蓝图命令
class LoadBluePrintCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 加载蓝图命令
class CameraParamCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 穿墙GM
class WallCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 修改碰撞
class MonsterCollideCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 修改手持位置
class HandItemDefCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 修改道具缩放大小
class ItemScaleCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
};

// 建筑物保护区开关
class CloseBuildProtectCommand : public IPlayerCmdCommand {
public:
    CloseBuildProtectCommand(bool isClose):m_bIsProtected(isClose) {};
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
private:
    bool m_bIsProtected = false;
};

class CharlesDebugCommand : public IPlayerCmdCommand {
public:
    void exec(ClientPlayer& clientPlayer, const vector<string>& params) override;
private:
};
#endif//__I_PLAYER_CMD_COMMAND_H__
