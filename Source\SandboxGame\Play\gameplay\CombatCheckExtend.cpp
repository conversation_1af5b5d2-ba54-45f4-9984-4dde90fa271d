#include "CombatCheckExtend.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "IActorLiving.h"
#include "IActorLocoMotion.h"
#include "world.h"
#include "section.h"
#include "ActorManagerInterface.h"
#include "PlayerControl.h"
#include "gameplay/boundaryGeometry/BoundaryContact.h"
#include "gameplay/boundaryGeometry/BoundaryHelper.h"
#include "blocks/BlockMaterialMgr.h"

static const int enableSectionRange = CHUNK_SIZE_X * 2;

//static bool getSectionRange(const WrapBoxBody& box, World* pworld, Rainbow::Vector3i& retMinSection, Rainbow::Vector3i& retMaxSection)
//{
//	//判断8个顶点各在哪个section位置, 找出一个section范围
//	auto dimX = box.vectorX() * box.getDimX();
//	auto dimZ = box.vectorZ() * box.getDimZ();
//	auto dimY = box.vectorY() * box.getDimY();
//	auto minPoint = box.getMinPos();
//	Rainbow::Vector3i maxSection(MIN_INT, MIN_INT, MIN_INT);
//	Rainbow::Vector3i minSection(MAX_INT, MAX_INT, MAX_INT);
//	for (int y = 0; y <= 1; y++)
//	{
//		for (int z = 0; z <= 1; z++)
//		{
//			for (int x = 0; x <= 1; x++)
//			{
//				//这里使用的都是世界坐标
//				auto point = minPoint +  y * dimY + z * dimZ + x * dimX;
//				int value = CoordDivSection(floor(point.x));
//				maxSection.x() = std::max(value, maxSection.x());
//				minSection.x() = std::min(value, minSection.x());
//
//				value = CoordDivSection(floor(point.y));
//				maxSection.y() = std::max(value, maxSection.y());
//				minSection.y() = std::min(value, minSection.y());
//
//				value = CoordDivSection(floor(point.z));
//				maxSection.z() = std::max(value, maxSection.z());
//				minSection.z() = std::min(value, minSection.z());
//			}
//		}
//	}
//	//怪物所在的section由位置确定,所以包围盒可能相交,但是由于怪物在q其他section上找不到.
//	maxSection += Rainbow::Vector3i(1, 1, 1);
//	minSection -= Rainbow::Vector3i(1, 1, 1);
//	auto center = box.center();
//	//做个粗略估算
//	int aboutDistance = box.getDimX() + box.getDimY() + box.getDimZ() + enableSectionRange;
//	if (abs(maxSection.x() * CHUNK_SIZE_X - center.x) > aboutDistance ||
//		abs(minSection.x() * CHUNK_SIZE_X - center.x) > aboutDistance)
//	{
//		assert(0);
//		return false;
//	}
//	if (abs(maxSection.y() * SECTION_SIZE - center.y) > aboutDistance ||
//		abs(minSection.y() * SECTION_SIZE - center.y) > aboutDistance)
//	{
//		assert(0);
//		return false;
//	}
//	if (abs(maxSection.z() * CHUNK_SIZE_Z - center.z) > aboutDistance ||
//		abs(minSection.z() * CHUNK_SIZE_Z - center.z) > aboutDistance)
//	{
//		assert(0);
//		return false;
//	}
//	retMinSection = minSection;
//	retMaxSection = maxSection;
//	return true;
//}

static bool getNearbyActor(IClientActor* actorMain, std::vector<IClientActor*>& actors, const Rainbow::Vector4f& boxInfo)
{
	auto holder = CombatCheckExtend::getActorCurAttackBox(actorMain, boxInfo);
	//现在攻击盒用的box
	auto box = holder.box();
	actors.clear();
	World* pworld = actorMain->getWorld();
	if (!pworld)
	{
		return false;
	}
	if (!pworld->getActorMgr())
	{
		return false;
	}
	actors.reserve(20);
	//Rainbow::Vector3i maxSection;
	//Rainbow::Vector3i minSection;
	//if (!getSectionRange(box, pworld, minSection, maxSection))
	//{
	//	return false;
	//}
	////对这个范围内的section都遍历下
	//for (int y = minSection.y(); y <= maxSection.y(); y++)
	//{
	//	for (int z = minSection.z(); z <= maxSection.z(); z++)
	//	{
	//		for (int x = minSection.x(); x <= maxSection.x(); x++)
	//		{
	//			auto section = pworld->getSectionBySCoord(x, y, z);
	//			if (section)
	//			{
	//				for (auto& actor : section->m_Actors)
	//				{
	//					if (actor == actorMain)
	//					{
	//						continue;
	//					}
	//					if (CombatCheckExtend::checkActorBoxCollide(actor, box))
	//					{
	//						actors.push_back(actor);
	//					}
	//				}
	//			}
	//		}
	//	}
	//}
	int maxRange = static_cast<int>(std::max(std::max(box.getDimX(), box.getDimY()), box.getDimZ()));
	maxRange *= 2; //扩大一些
	pworld->getActorMgr()->SelectNearbyActor(box, maxRange, actors, actorMain);
	/*IClientActor* horse = NULL;
	if (actorMain->getRiddenComponent())
	{
		horse = actorMain->getRiddenComponent()->getRidingActor();
	}
	pworld->getActorMgr()->selectNearActors(box.center(), maxRange, [&actorMain, &horse](IClientActor* actor) {
		return actor != actorMain && actor != horse;
	}, [&actors, &box](IClientActor* actor) {
		if (CombatCheckExtend::checkActorBoxCollide(actor, box))
		{
			actors.emplace_back(actor);
		}
	});*/
	return true;
}

//bool CombatCheckExtend::getBoxMobs(World* pworld, const Rainbow::Vector3f& pos, std::vector<ClientActor*>& mobs, const Rainbow::Vector3f& boxInfo)
//{
//
//	auto holder = CombatCheckExtend::getDrawPosBox(pos, boxInfo);
//	//auto box = holder
//
//	//if (!pworld || !pworld->getActorMgr())
//	//{
//	//	return false;
//	//}
//	//mobs.clear();
//	//int maxRange = getCheckRange(holder);
//	//maxRange *= 1.5; //扩大一些
//	//pworld->getActorMgr()->selectNearActors(holder.center(), maxRange, [](ClientActor* actor) {
//	//	return true;
//	//	}, [&mobs, &holder](ClientActor* actor) {
//	//		if (CombatCheckExtend::checkActorBoxCollide(actor, holder))
//	//		{
//	//			mobs.emplace_back(actor);
//	//		}
//	//	});
//	return true;
//}

bool CombatCheckExtend::getNeighborMobs(World* pworld, const BoundaryGeometryHolder& holder, std::vector<IClientActor*>& mobs, IClientActor* actor, int range)
{
	if (!pworld || !pworld->getActorMgr())
	{
		return false;
	}
	int exrange = -1;
	WCoord centerPos;
	switch (holder.getType())
	{
		//case BoundaryGeometryType::eSPHERE:		put<PxSphereGeometry>(geometry); break;
		case BoundaryGeometryType::eBOX:
		{
			const auto& box = holder.box();
			centerPos = box.center();
			exrange = exrange < 0 ? static_cast<int>(std::max(std::max(box.getDimX(), box.getDimY()), box.getDimZ())) * 1.5f : exrange;
			break;
		}
		case BoundaryGeometryType::eSPHERE:
		{
			const auto& sphere = holder.sphere();
			centerPos = sphere.center();
			exrange = exrange < 0 ? sphere.radius() * 1.5f : exrange;
			break;
		}
		case BoundaryGeometryType::eCylinder:
		{
			const auto& cylinder = holder.cylinder();
			centerPos = cylinder.center();
			exrange = exrange < 0 ? static_cast<int>(std::max(cylinder.radius(), cylinder.height())) * 1.5f : exrange;
			break;
		}
		case BoundaryGeometryType::eGEOMETRY_COUNT:
		case BoundaryGeometryType::eINVALID:		break;
		default:break;
	};
	mobs.clear();
	pworld->getActorMgr()->SelectNeighborMobs(holder, centerPos, exrange, mobs, actor);
	/*ClientActor* horse = NULL;
	if (actor && actor->getRiddenComponent())
	{
		horse = actor->getRiddenComponent()->getRidingActor();
	}
	pworld->getActorMgr()->selectNearActors(centerPos, exrange, [&actor, &horse](ClientActor* tactor) {
		return tactor != actor && tactor != horse;
	}, [&mobs, &holder](ClientActor* actor) {
		if (CombatCheckExtend::checkActorBoxCollide(actor, holder))
		{
			mobs.emplace_back(actor);
		}
	});*/
	return true;
}

//bool  CombatCheckExtend::getActorBoxMobs(ClientActor* actor, const Rainbow::Vector3f& pos, std::vector<ClientActor*>& mobs, const Rainbow::Vector3f& boxInfo)
//{
//	if (!actor)
//	{
//		return false;
//	}
//
//	auto holder = CombatCheckExtend::getDrawActorBox(actor,pos, boxInfo);
//	auto box = holder.box();
//	World* pworld = actor->getWorld();
//	if (!pworld)
//	{
//		return false;
//	}
//	if (!pworld->getActorMgr())
//	{
//		return false;
//	}
//	mobs.clear();
//	int maxRange = static_cast<int>(std::max(std::max(box.getDimX(), box.getDimY()), box.getDimZ()));
//	maxRange *= 1,5; //扩大一些
//	ClientActor* horse = NULL;
//	if (actor->getRiddenComponent())
//	{
//		horse = actor->getRiddenComponent()->getRidingActor();
//	}
//	pworld->getActorMgr()->selectNearActors(box.center(), maxRange, [&actor, &horse](ClientActor* tactor) {
//		return tactor != actor && tactor != horse;
//		}, [&mobs, &box](ClientActor* tactor) {
//			if (CombatCheckExtend::checkActorBoxCollide(tactor, box))
//			{
//				mobs.emplace_back(tactor);
//			}
//		});
//	return true;
//
//}

bool CombatCheckExtend::getNeighborBlocks(World* pworld, const BoundaryGeometryHolder& holder, std::list<WCoord>& blocks, int range)
{
	if (!pworld)
	{
		return false;
	}
	WCoord minpos, maxpos;
	switch (holder.getType())
	{
		//case BoundaryGeometryType::eSPHERE:		put<PxSphereGeometry>(geometry); break;
	case BoundaryGeometryType::eBOX:
	{
		const auto& box = holder.box();
		minpos = box.getMinPos();
		maxpos = box.getMaxPos();
		break;
	}
	case BoundaryGeometryType::eSPHERE:
	{
		const auto& sphere = holder.sphere();
		minpos = sphere.getMinPos();
		maxpos = sphere.getMaxPos();
		break;
	}
	case BoundaryGeometryType::eCylinder:
	{
		const auto& cylinder = holder.cylinder();
		minpos = cylinder.getMinPos();
		maxpos = cylinder.getMaxPos();
		break;
	}
	case BoundaryGeometryType::eGEOMETRY_COUNT:
	case BoundaryGeometryType::eINVALID:		break;
	default:break;
	};
	blocks.clear();
	WCoord grid1 = CoordDivBlock(minpos);
	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x, maxpos.y, maxpos.z));

	int miny = (std::min(grid1.y, grid2.y));
	int maxy = (std::max(grid1.y, grid2.y));


	int minz = (std::min(grid1.z, grid2.z));
	int maxz = (std::max(grid1.z, grid2.z));

	int minx = (std::min(grid1.x, grid2.x));
	int maxx = (std::max(grid1.x, grid2.x));

	for (int z = minz; z <= maxz; z++)
	{
		for (int y = miny; y <= maxy; y++)
		{
			for (int x = minx; x <= maxx; x++)
			{
				Block pblock = pworld->getBlock(WCoord(x, y, z));
				if (pblock.isEmpty()) continue;
				BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
				if (blockmtl && blockmtl->defBlockMove())
				{
					blocks.push_back(WCoord(x, y, z));
				}
			}
		}
	}
	return true;
}

//bool CombatCheckExtend::getBoxBlocks(World* pworld, const Rainbow::Vector3f& pos, std::list<WCoord>& blocks, const Rainbow::Vector3f& boxInfo)
//{
//	if (!pworld)
//	{
//		return false;
//	}
//	blocks.clear();
//	WCoord tpos(pos);
//	auto holder = CombatCheckExtend::getPosBox(tpos.toVector3(), boxInfo);
//	auto box = holder.box();
//	WCoord minpos(box.getMinPos()), maxpos(box.getMaxPos());
//	WCoord grid1 = CoordDivBlock(minpos);
//	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x , maxpos.y , maxpos.z)); 
//
//	int miny = (std::min(grid1.y, grid2.y));
//	int maxy = (std::max(grid1.y, grid2.y));
//
//
//	int minz = (std::min(grid1.z, grid2.z));
//	int maxz = (std::max(grid1.z, grid2.z));
//
//	int minx = (std::min(grid1.x, grid2.x));
//	int maxx = (std::max(grid1.x, grid2.x));
//
//	for (int z = minz; z <= maxz; z++)
//	{
//		for (int y = miny; y <= maxy; y++)
//		{
//			for (int x = minx; x <= maxx; x++)
//			{
//				Block pblock = pworld->getBlock(WCoord(x, y, z));
//				if (pblock.isEmpty()) continue;
//				BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
//				if (blockmtl && blockmtl->defBlockMove())
//				{
//					blocks.push_back(WCoord(x, y, z));
//				}
//			}
//		}
//	}
//
//
//	return true;
//}

//bool  CombatCheckExtend::getActorBoxBlocks(ClientActor* actor, const Rainbow::Vector3f& pos, std::list<WCoord>& blocks, const Rainbow::Vector3f& boxInfo)
//{
//	if (!actor)
//	{
//		return false;
//	}
//	auto holder = CombatCheckExtend::getDrawActorBox(actor, pos, boxInfo);
//	auto box = holder.box();
//	World* pworld = actor->getWorld();
//	if (!pworld)
//	{
//		return false;
//	}
//	blocks.clear();
//	WCoord minpos(box.getMinPos()), maxpos(box.getMaxPos());
//	WCoord grid1 = CoordDivBlock(minpos);
//	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x, maxpos.y, maxpos.z)); 
//	int miny = (std::min(grid1.y, grid2.y));
//	int maxy = (std::max(grid1.y, grid2.y));
//	WCoord p0(box.x0y0z0()); //底部四个点
//	WCoord p1(box.x0y0z1());
//	WCoord p4(box.x1y0z0());
//	WCoord p5(box.x1y0z1());
//	p0 = CoordDivBlock(p0);
//	p1 = CoordDivBlock(p1);
//	p4 = CoordDivBlock(p4);
//	p5 = CoordDivBlock(p5);
//	int maxx = std::max(std::max(p0.x, p1.x), std::max(p4.x, p5.x));
//	int minx = std::min(std::min(p0.x, p1.x), std::min(p4.x, p5.x));
//	int maxz = std::max(std::max(p0.z, p1.z), std::max(p4.z, p5.z));
//	int minz = std::min(std::min(p0.z, p1.z), std::min(p4.z, p5.z));
//
//	std::vector<WCoord> rect;
//	rect.resize(4);
//	rect[0] = p0;
//	rect[1] = p1;
//	rect[2] = p4;
//	rect[3] = p5;
//	for (int y = miny; y <= maxy; y++)
//	{
//		for (int z = minz; z <= maxz; z++)
//		{
//
//			for (int x = minx; x<= maxx; x++)
//			{
//				if (IsInRect(x, z, rect))
//				{
//					Block pblock = pworld->getBlock(WCoord(x, y, z));
//					if (pblock.isEmpty()) continue;
//					BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(pblock.getResID());
//					if (blockmtl && blockmtl->defBlockMove())
//					{
//						blocks.push_back(WCoord(x, y, z));
//					}
//				}
//
//			}
//		}
//	
//	}
//	return true;
//}

 bool  CombatCheckExtend::IsInRect(int x, int z, const std::vector< WCoord>& rect)
 {
	const WCoord& a = rect[0];
	const WCoord& b = rect[1];
	const WCoord& c = rect[2];
	const WCoord& d = rect[3];

	//WCoord p0(box.x0y0z0()); //底部四个点
	//WCoord p1(box.x0y0z1());
	//WCoord p4(box.x1y0z0());
	//WCoord p5(box.x1y0z1());

	/*BoundaryHelper::drawLine(box.x0y0z0(), box.x0y0z1(), pworld, color);
BoundaryHelper::drawLine(box.x0y0z1(), box.x1y0z1(), pworld, color);*/
//BoundaryHelper::drawLine(box.x1y0z1(), box.x1y0z0(), pworld, color);
//BoundaryHelper::drawLine(box.x1y0z0(), box.x0y0z0(), pworld, color);



	int A = GetCross(a, b, x, z);
	int B = GetCross(d, c, x, z);
	int C = GetCross(b, d, x, z);
	int D = GetCross(c, a, x, z);


	return A * B > 0 && C * D > 0;

 }


#ifdef ENABLE_PLAYER_CMD_COMMAND
void  CombatCheckExtend::drawTLBoxActorBox(IClientActor* actor, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
{
	if (!actor)
	{
		return ;
	}
	auto holder = CombatCheckExtend::getDrawActorBox(actor, pos, boxInfo);
	auto box = holder.box();
	auto color = BoundaryGeDrawColor_Green;
	BoundaryHelper::drawBox(box, actor->getWorld(), color);
	BoundaryHelper::drawLine(box.center(), box.center(), actor->getWorld(), color);
}

void  CombatCheckExtend::drawTLBox(World* pworld, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
{
	if (!pworld)
	{
		return ;
	}
	WCoord tpos(pos);
	auto holder = CombatCheckExtend::getDrawPosBox(tpos.toVector3(), boxInfo);
	auto box = holder.box();
	auto color = BoundaryGeDrawColor_Green;
	BoundaryHelper::drawBox(box, pworld, color);
	BoundaryHelper::drawLine(box.center(), box.center(), pworld, color);
}
#endif

static bool haveNearByActor(IClientActor* actorMain, const Rainbow::Vector4f& boxInfo)
{
	World* pworld = actorMain->getWorld();
	if (!pworld)
	{
		return false;
	}
	auto holder = CombatCheckExtend::getActorCurAttackBox(actorMain, boxInfo);
	//现在攻击盒用的box
	auto box = holder.box();
	//Rainbow::Vector3i maxSection;
	//Rainbow::Vector3i minSection;
	//if (!getSectionRange(box, pworld, minSection, maxSection))
	//{
	//	return false;
	//}
	////对这个范围内的section都遍历下
	//for (int y = minSection.y(); y <= maxSection.y(); y++)
	//{
	//	for (int z = minSection.z(); z <= maxSection.z(); z++)
	//	{
	//		for (int x = minSection.x(); x <= maxSection.x(); x++)
	//		{
	//			auto section = pworld->getSectionBySCoord(x, y, z);
	//			if (section)
	//			{
	//				for (auto& actor : section->m_Actors)
	//				{
	//					if (actor == actorMain)
	//					{
	//						continue;
	//					}
	//					if (CombatCheckExtend::checkActorBoxCollide(actor, box))
	//					{
	//						return true;
	//					}
	//				}
	//			}
	//		}
	//	}
	//}
	int maxRange = static_cast<int>(std::max(std::max(box.getDimX(), box.getDimY()), box.getDimZ()));
	maxRange *= 2; //扩大一些
	
	bool find = pworld->getActorMgr()->SelectNearActors(box, maxRange, actorMain);
	return find;
}

bool CombatCheckExtend::actorHaveEnemyInAttackRange(IClientActor* actor, const Rainbow::Vector4f& boxInfo)
{
	if (!actor)
	{
		return false;
	}
	World* pwolrd = actor->getWorld();
	if (!pwolrd)
	{
		return false;
	}
	return haveNearByActor(actor, boxInfo);
}

bool CombatCheckExtend::getActorAttackMobs(IClientActor* actor, std::vector<IClientActor*>& mobs, const Rainbow::Vector4f& boxInfo)
{
	if (!actor)
	{
		return false;
	}
	return getNearbyActor(actor, mobs, boxInfo);
}

static bool isFriendly(IClientActor* actor1, IClientActor* actor2)
{
	IActorLiving* living = dynamic_cast<IActorLiving*>(actor2);
	if (living == nullptr)
	{
		return false;
	}
	IActorLiving* actorLiving = dynamic_cast<IActorLiving*>(actor1);
	if (!actorLiving)
	{
		return false;
	}
	{
		int teamType = living->GetTeamType(actorLiving);
		if (TEAM_TYPE_FRIENDLY == teamType)
		{
			return true;
		}
	}
	return false;
}

bool CombatCheckExtend::actorHaveCanAttakEnemy(IClientActor* actor, std::vector<IClientActor*>& mobs, const Rainbow::Vector4f& boxInfo)
{
	if (!actor)
	{
		return false;
	}
	if (!getActorAttackMobs(actor, mobs, boxInfo))
	{
		return false;
	}
	mobs.erase(std::remove_if(mobs.begin(), mobs.end(), [&actor](IClientActor* a)->bool
	{
		return !actorCanSeeActor(actor, a) || isFriendly(actor, a);
	}), mobs.end());
	return true;
}

static int test_x = 150;
static int test_y = 100;
static int test_dis = -200;
static bool useAABB = false;

BoundaryGeometryHolder CombatCheckExtend::getActorCurAttackBox(IClientActor* actor, const Rainbow::Vector4f& boxInfo)
{
	IActorLiving* player = dynamic_cast<IActorLiving*>(actor);
	if (!player)
	{
		return BoundaryGeometryHolder(BoundaryBoxGeometry());
	}
	auto holder = getActorBox(actor);
	auto box = holder.box();
	auto eyeHeight = actor->getEyeHeight();
	auto eyeCenter = box.center() + Rainbow::Vector3f(0, eyeHeight - (box.getDimY() / 2), 0);
	//攻击包围盒attackBox围着 eyeCenter转动, 所以eyeCenter 与 attackBox 中心点距离不变
	//初始化人物朝着-x方向
	//2点之间的向量, x,y均相同
	Rainbow::Vector3f dim(boxInfo.x, boxInfo.y, boxInfo.z);
	Rainbow::Vector3f dis(0, 0, boxInfo.w);
	//todo 读取配置
	//auto bodyRotate = player->getBodyDir().NormalizeSafe();
	float headRotatePitch = -(actor->getFacePitch());
//	float headRotateYaw = (player->getFaceYaw());
	//人物自身的旋转矩阵
	Rainbow::Matrix3x3f tm = box.getRotate();
	//tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw));
	Rainbow::Matrix3x3f tmPitch;
	tmPitch.SetAxisAngle(Rainbow::Vector3f(1, 0, 0), Rainbow::Radians(headRotatePitch));
	tm = tm * tmPitch;

	dis = tm.MultiplyVector3(dis);
	auto boxCenter = eyeCenter + dis;
	//if (useAABB)
	//{
	//	return BoundaryGeometryHolder(BoundaryBoxGeometry(boxCenter, dim, Rainbow::Matrix3x3f::identity));
	//}
	//OBB
	//先确定轴.
	Rainbow::Vector3f axiesX = tm.GetColumn(0);
	Rainbow::Vector3f axiesY = tm.GetColumn(1);
	Rainbow::Vector3f axiesZ = tm.GetColumn(2);
	return BoundaryGeometryHolder(BoundaryBoxGeometry(boxCenter, dim, tm));
}
BoundaryGeometryHolder CombatCheckExtend::getPosBox( const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
{
	Rainbow::Vector3f dim(boxInfo.x * BLOCK_FSIZE, boxInfo.y * BLOCK_FSIZE, boxInfo.z * BLOCK_FSIZE);
	return BoundaryGeometryHolder(BoundaryBoxGeometry(pos, dim, Rainbow::Matrix3x3f::identity));
}
BoundaryGeometryHolder CombatCheckExtend::getDrawPosBox(const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
{
	WCoord wpos(pos);
	WCoord wdim(boxInfo);
	Rainbow::Vector3f pboxInfo = boxInfo;
	pboxInfo.x *= BLOCK_FSIZE;
	pboxInfo.y *= BLOCK_FSIZE;
	pboxInfo.z *= BLOCK_FSIZE;
	return BoundaryGeometryHolder(BoundaryBoxGeometry(wpos.toVector3(), pboxInfo, Rainbow::Matrix3x3f::identity));
}

 BoundaryGeometryHolder CombatCheckExtend::getActorBox(IClientActor* actor, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
{
	 auto holder = getActorBox(actor);
	 auto box = holder.box();
	 float headRotatePitch = -(actor->getNavigationFacePitch());
	 float headRotateYaw = (actor->getFaceYaw());
	 Rainbow::Vector3f boxcenter = box.center();
	 boxcenter.x = boxcenter.x / BLOCK_FSIZE;
	 boxcenter.y = boxcenter.y / BLOCK_FSIZE;
	 boxcenter.z = boxcenter.z / BLOCK_FSIZE;
	 Rainbow::Vector3f centerbox = boxcenter + pos;
	 Rainbow::Vector3f dim(boxInfo.x, boxInfo.y, boxInfo.z);
	 Rainbow::Matrix3x3f tm;
	 tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw));
	 Rainbow::Matrix3x3f tmPitch;
	 tmPitch.SetAxisAngle(Rainbow::Vector3f(1, 0, 0), Rainbow::Radians(headRotatePitch));
	 tm *= tmPitch;
	 return BoundaryGeometryHolder(BoundaryBoxGeometry(centerbox, dim, tm));
}

 BoundaryGeometryHolder CombatCheckExtend::getDrawActorBox(IClientActor* actor, const Rainbow::Vector3f& pos, const Rainbow::Vector3f& boxInfo)
 {
	 auto holder = getActorBox(actor);
	 auto box = holder.box();

	 float headRotatePitch = -(actor->getNavigationFacePitch());
	 float headRotateYaw = (actor->getFaceYaw());
	 //根据朝向计算位置
	 Rainbow::Vector3f ppos;
	 float yaw = headRotateYaw;
	 float vx = pos.x * BLOCK_FSIZE;
	 float vz = pos.z * BLOCK_FSIZE;
	 float r = std::sqrt(vx * vx + vz * vz);
	 if (r <= std::numeric_limits<float>::epsilon())
	 {
		 ppos = box.center();
		 ppos.y += (pos.y * BLOCK_FSIZE);
	 }
	 else
	 {
		 vx = vx / r;
		 vz = vz / r;
		 float yaw2 = std::atan2(vz, vx);
		 float yaw3 = yaw + yaw2;
		 Rainbow::Vector3f vec;
		 vec = Yaw2FowardDir(yaw3);
		 vec.x *= r;
		 vec.z *= r;
		 ppos = box.center();
		 ppos.x += vec.x;
		 ppos.y += (pos.y * BLOCK_FSIZE);
		 ppos.z += vec.z;
	 }
	 Rainbow::Vector3f centerbox = ppos;
	 Rainbow::Vector3f dim = boxInfo;
	 dim.x *= BLOCK_FSIZE;
	 dim.y *= BLOCK_FSIZE;
	 dim.z *= BLOCK_FSIZE;
	 Rainbow::Matrix3x3f tm;
	 tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw));
	 Rainbow::Matrix3x3f tmPitch;
	 tmPitch.SetAxisAngle(Rainbow::Vector3f(1, 0, 0), Rainbow::Radians(headRotatePitch));
	 tm *= tmPitch;
	 return BoundaryGeometryHolder(BoundaryBoxGeometry(centerbox, dim, tm));
 }


bool CombatCheckExtend::checkActorBoxCollide(IClientActor* actor1, const BoundaryGeometryHolder& ge)
{
	return BoundaryContact::checkCollide(getActorBox(actor1), ge);
}

//bool CombatCheckExtend::checkActorBoxCollideAABB(IClientActor* actor1, const WrapBoxBody& box2)
//{
//	if (!actor1)
//	{
//		return false;
//	}
//	return WrapBoxExtend::checkBoxBoxCollideAABB(getActorBox(actor1), box2);
//}
//
//bool CombatCheckExtend::checkActorBoxCollideOBB(IClientActor* actor1, const WrapBoxBody& box2)
//{
//	if (!actor1)
//	{
//		return false;
//	}
//	return WrapBoxExtend::checkBoxBoxCollideOBB(getActorBox(actor1), box2);
//}

std::vector<BoundaryGeometryHolder> CombatCheckExtend::getMultiActorBox(IClientActor* actor)
{
	std::vector<BoundaryGeometryHolder> ret;
	CollideAABB box;
	if (actor->getObjType() == OBJ_TYPE_ROLE)
	{
		auto loco = actor->getILocoMotion();
		if (loco)
		{
			float headRotateYaw = (loco->GetRotateYaw());
			//自身的旋转矩阵
			Rainbow::Matrix3x3f tm;
			tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw + 180));

			std::vector<CollideAABB> boxs;
			actor->getMultiCollideBox(boxs);
			for (auto& box : boxs) {
				BoundaryBoxGeometry one(Rainbow::Vector3f(box.centerX(), box.centerY(), box.centerZ()), box.dim.toVector3(), tm);
				ret.push_back(one);
			}
		}
	}
	else
	{
		auto loco = actor->getILocoMotion();
		if (loco)
		{
			float headRotateYaw = (loco->GetRotateYaw());

			//自身的旋转矩阵
			Rainbow::Matrix3x3f tm;
			tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw + 180));

			std::vector<CollideAABB> boxs;
			actor->getMultiCollideBox(boxs);
			for (auto& box : boxs) {
				BoundaryBoxGeometry one(Rainbow::Vector3f(box.centerX(), box.centerY(), box.centerZ()), box.dim.toVector3(), tm);
				ret.push_back(one);
			}
			
		}
		else
		{
			box.dim = WCoord(0, 0, 0);
			box.pos = WCoord(0, 0, 0);
		}
	}
	return ret;
}

BoundaryGeometryHolder CombatCheckExtend::getActorBox(IClientActor* actor)
{
	CollideAABB box;
	if (actor->getObjType() == OBJ_TYPE_ROLE)
	{
		actor->getCollideBox(box);
		auto loco = actor->getILocoMotion();
		if (loco)
		{
			float headRotateYaw = (loco->GetRotateYaw());
			//自身的旋转矩阵
			Rainbow::Matrix3x3f tm;
			tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw + 180));
			return BoundaryBoxGeometry(Rainbow::Vector3f(box.centerX(), box.centerY(), box.centerZ()), box.dim.toVector3(), tm);
		}
	}
	else
	{
		auto loco = actor->getILocoMotion();
		if (loco)
		{
			loco->combatAttackCollideBox(box);
			float headRotateYaw = (loco->GetRotateYaw());
			//自身的旋转矩阵
			Rainbow::Matrix3x3f tm;
			tm.SetAxisAngle(Rainbow::Vector3f(0, 1, 0), Rainbow::Radians(headRotateYaw + 180));
			return BoundaryBoxGeometry(Rainbow::Vector3f(box.centerX(), box.centerY(), box.centerZ()), box.dim.toVector3(), tm);
		}
		else
		{
			box.dim = WCoord(0, 0, 0);
			box.pos = WCoord(0, 0, 0);
		}
	}
	return BoundaryBoxGeometry(box);
}

bool CombatCheckExtend::actorCanSeeActor(IClientActor* actor1, IClientActor* actor2)
{
	if (!actor1 || !actor2)
	{
		return false;
	}
	if (!actor1->getWorld())
	{
		return false;
	}
	MINIW::WorldRay wray;
	auto p1 = getActorBox(actor1).box().center();
	auto p2 = getActorBox(actor2).box().center();

	wray.m_Origin = Rainbow::WorldPos(p1.x * Rainbow::WorldPos::UNIT, p1.y * Rainbow::WorldPos::UNIT, p1.z * Rainbow::WorldPos::UNIT);
	wray.m_Dir = (p2 - p1);
	wray.m_Range = wray.m_Dir.Length();
	if (wray.m_Range < BLOCK_FSIZE) return true;

	wray.m_Dir /= wray.m_Range;
	IntersectResult result;
	return !(actor1->getWorld()->pickGround(wray, &result, PICK_METHOD_SOLID_ALL));
}

//int CombatCheckExtend::getCheckRange(const BoundaryGeometryHolder& holder)
//{
//	int range = -1;
//	switch (holder.getType())
//	{
//		//case BoundaryGeometryType::eSPHERE:		put<PxSphereGeometry>(geometry); break;
//		case BoundaryGeometryType::eBOX:
//			const auto& box = holder.box();
//			range = static_cast<int>(std::max(std::max(box.getDimX(), box.getDimY()), box.getDimZ()));
//			break;
//		case BoundaryGeometryType::eSPHERE:
//			const auto& sphere = holder.sphere();
//			range = sphere.radius();
//			break;
//		case BoundaryGeometryType::eCylinder:
//			const auto& cylinder = holder.cylinder();
//			range = static_cast<int>(std::max(cylinder.radius(), cylinder.height()));
//			break;
//		case BoundaryGeometryType::eGEOMETRY_COUNT:
//		case BoundaryGeometryType::eINVALID:		break;
//		default:break;
//	}
//	return range;
//}

//bool CombatCheckExtend::boxCheckCollide(const WrapBoxBody& body, World* world)
//{
//	if (!world)
//	{
//		return false;
//	}
//
//}
//
//bool CombatCheckExtend::boxCollideActors(const WrapBoxBody& body, World* world, std::vector<IClientActor*>& mobs)
//{
//	if (!world)
//	{
//		return false;
//	}
//
//}

#ifdef ENABLE_PLAYER_CMD_COMMAND
void CombatCheckExtend::drawActorBox(IClientActor* actor, const Rainbow::Vector4f& boxInfo)
{
	if (!actor) return;
	// if (g_pPlayerCtrl && g_pPlayerCtrl == actor) return;  // 玩家自己的不绘制


	auto actorHolders = getMultiActorBox(actor);
	
	for (auto& holder : actorHolders)
	{
		auto& actorbox = holder.box();
		BoundaryHelper::drawBox(actorbox, actor->getWorld(), BoundaryGeDrawColor_Red, 2);
	}
	BoundaryBoxGeometry actorbox;
	if (actorHolders.size() > 0)
	{
		actorbox = actorHolders[0].box();
	}
	else
	{
		return;
	}

	auto attackBox = getActorCurAttackBox(actor, boxInfo).box();
	auto color = BoundaryGeDrawColor_Green;
	std::vector<IClientActor*> actors;
	getNearbyActor(actor, actors, boxInfo);
	for (auto& p : actors)
	{
		if (actorCanSeeActor(actor, p))
		{
			color = BoundaryGeDrawColor_Red;
		}
		BoundaryHelper::drawLine(actorbox.center(), getActorBox(p).box().center(), actor->getWorld(), color);
	}
	if (attackBox.isValid())
	{
		BoundaryHelper::drawBox(attackBox, actor->getWorld(), color);
		BoundaryHelper::drawLine(attackBox.center(), actorbox.center(), actor->getWorld(), color);
	}
}
#endif