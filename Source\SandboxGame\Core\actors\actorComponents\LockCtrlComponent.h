﻿#pragma once

#include "ActorComponent_Base.h"
#include "SandboxGame.h"
#include "backpack.h"
#include "container_backpack.h"

struct SocLock;

//锁功能组件封装  锁的操作
class EXPORT_SANDBOXGAME LockCtrlComponent : public ActorComponentBase, public GridVisitor //tolua_exports
{//tolua_exports
	DECLARE_COMPONENTCLASS(LockCtrlComponent)
protected:
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);
public:
	LockCtrlComponent();
	~LockCtrlComponent();
	
	void OnNetMessage(const std::string& data);

	virtual void OnTick() override;

	virtual void visit(const BackPackGrid* grid) override;

    bool TryAddLock(const WCoord& pos,int toolID);

	//tolua_begin
	// 两种锁通用功能
	//加锁
	void AddLock(const WCoord& pos,int type);
	//上锁
	void UpLock(const WCoord& pos);
	//解锁
	void UnLock(const WCoord& pos);
	//移除
	void DeleteLock(const WCoord& pos);

	//钥匙锁功能
	//制作钥匙
	void MakeKey(const WCoord& pos);

	//密码锁功能
	//设置主控密码
	void SetMainPassword(const WCoord& pos,int password);
	//设置访客密码
	void SetPassword(const WCoord& pos, int password);

	SocLock* GetSocLock(const WCoord& pos);

	bool IsOpen(const WCoord& pos);

	bool IsPasswdOpen(const WCoord& pos,int passwd);

	bool HasMainList(const WCoord& pos);

	bool HasKey(int keyid);

    bool IsOpenPieMenu(const WCoord& pos);

	const int GetLastPasswd() const { return _lastPasswd; };

	void SetLastPasswd(int passwd);
	//tolua_end

	//密码锁有个ui交互的流程
	void OpenBlock(const WCoord& pos);

	//提示消息
	void SendClientMessage(const std::string& str);
private:

private:
	bool Check(const WCoord& pos);
	void UpdataBlock(const WCoord& pos);

private:
	ClientPlayer* _player;
	int _keyid;
	int _lastPasswd;
	bool _haskey;
protected:
};//tolua_exports