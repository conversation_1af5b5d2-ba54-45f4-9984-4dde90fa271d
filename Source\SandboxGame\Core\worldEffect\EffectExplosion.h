#ifndef __EXPLOSION_H__
#define __EXPLOSION_H__

#include "world_types.h"
#include <vector>
#include "SandboxGame.h"

class ClientActor;
class Explosion //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	Explosion(World *world, IClientActor *actor, int explodesize, const WCoord &blockpos, bool flaming, bool smoking, bool versionEx = false);

	virtual void doExplosionA() = 0;
	virtual void doExplosionB() = 0;
	virtual void doExplosionAEx() = 0;
	virtual void doExplosionBEx() = 0;
	IClientActor *getExploder();
	//tolua_end
protected:
	World *m_World;
	IClientActor *m_Exploder;
	WCoord m_ExplodePos;
	float m_ExplodeSize;
	bool m_Smoking;
	bool m_Flaming;
	bool m_VersionEx;
	std::vector<WCoord>m_AffectedBlocks;
}; //tolua_exports

class ExplosionGeneral : public Explosion //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ExplosionGeneral(World *world, IClientActor *actor, int explodesize, const WCoord &blockpos, bool flaming, bool smoking, int damageType, std::string soundPath, std::string effectPath) 
		: Explosion(world, actor, explodesize, blockpos, flaming, smoking)
		, m_soundPath(soundPath), m_effectPath(effectPath), m_newAttackType(false), m_upHalf(false), m_atkValue(0.0f), m_fromSkill(false)
	{
		m_damageType = damageType;
	}

	virtual void doExplosionA() override;
	virtual void doExplosionB() override;
	virtual void doExplosionAEx() override;
	virtual void doExplosionBEx() override;

	void SetNewAttack(bool upHalf, float atkValue, bool fromSkill)
	{ 
		m_newAttackType = true;
		m_upHalf = upHalf;
		m_atkValue = atkValue;
		m_fromSkill = fromSkill;
	}
	//tolua_end

	void getExplodeBlocks_Old(bool explodeUpHalf);
	void getExplodeBlocks(bool explodeUpHalf);
private:
	std::vector<WCoord> icicleBlocks;
	int m_damageType; // ��ը�˺����ͣ�0������1��2����3���ҡ�4�硢5����6��Ԫ�أ�
	std::string m_soundPath;
	std::string m_effectPath;
	bool m_newAttackType;
	bool m_upHalf;
	float m_atkValue;
	bool m_fromSkill;
}; //tolua_exports

class ExplosionDir : public Explosion //tolua_exports
{ //tolua_exports
public:
	//tolua_begin
	ExplosionDir(World *world, IClientActor *actor, int explodesize, const WCoord &blockpos, bool flaming, bool smoking, int dirmask) : Explosion(world, actor, explodesize, blockpos, flaming, smoking), m_DirMask(dirmask)
	{

	}

	virtual void doExplosionA() override;
	virtual void doExplosionB() override;

	virtual void doExplosionAEx() override;
	virtual void doExplosionBEx() override;

	float getExplodeDirStrength(const WCoord &dp, int range);
	//tolua_end
private:
	int m_DirMask;
	std::vector<WCoord> icicleBlocks;
}; //tolua_exports


class EXPORT_SANDBOXGAME ExplosionAirBall : public Explosion
{
public:
	ExplosionAirBall(World* world, ClientActor* actor, const WCoord& blockpos, int explosionRadius, int atkpoint, bool isInWater = true, Rainbow::Vector3f rotation = Rainbow::Vector3f(0, 0, 0));
	virtual void doExplosionA() override;
	virtual void doExplosionB() override;
	virtual void doExplosionAEx() override;
	virtual void doExplosionBEx() override;
private:
	int m_range;
	int m_atkpoint;
	bool m_isInWater;
	Rainbow::Vector3f m_rotation;
};

EXPORT_SANDBOXGAME extern float GetBlockDensity(World* pworld, const WCoord& center, const CollideAABB& aabb);

#endif