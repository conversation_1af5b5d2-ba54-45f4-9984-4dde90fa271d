﻿#ifndef __CLIENTACTOR_MANAGER_H__
#define __CLIENTACTOR_MANAGER_H__

#include "SandboxGame.h"
#include "world_types.h"
#include "Common/OgreHashTable.h"
#include "ActorTypes.h"
#include "SandboxAutoRef.h"
#include "SandboxWeakRef.h"
#include "SandboxCallback.h"
#include "SceneManagement/GenericOctreeMGT.hpp"

#include "world_ActorExcludes.h"
#include "ActorManagerInterface.h"
#include "IClientActor.h"
#include "Collision.h"

//#include "SpaceActorManager.h"
class ClientActor;
class SpaceActorManager;
class ClientItem;
class ActorLiving;
class ClientMob;
class ClientPlayer;
class ActorVehicleAssemble;
class ActorBoss;
class Ecosystem;
class ChunkRandGen;
struct MonsterDef;
class BackPackGrid;
class ActorHorse;
class ClientAvatarSummonMob;
class BlockScene;
class IClientMob;
class WorldMapData;
class IActorBoss;
class VoxelModel;
namespace game
{
	namespace common
	{
		class PB_PlayerBriefInfo;
	}
}
typedef bool (*SelectActorFunc)(ClientActor *actor, void *userdata);

typedef Rainbow::HashTable<ChunkIndex, bool, ChunkIndexHashCoder> GenMobTable;

class ClientActorSearcher
{
public:
	// 返回值: true 继续查找
	virtual bool look(ClientActor* actor) = 0;
};

struct EXPORT_SANDBOXGAME MobPresetInfo {
	int blockId;
	int mobId;
	WCoord pos;
	unsigned int posKey;
};

struct EXPORT_SANDBOXGAME MobPresetSpawnInfo {
	unsigned int presetPosKey;
	bool isSpawned;
	short deadDay;
};

class EXPORT_SANDBOXGAME ClientActorMgr;
class ClientActorMgr : public ActorManagerInterface//tolua_exports
{ //tolua_exports
	struct LiveMob
	{
		ClientMob* mob;
		float leave_view_time;
	};
	struct SpawnMobDelayParam
	{
		SpawnMobDelayParam(const WCoord& pos, int delay_ticks, int monsterid, bool mobtype_check, bool mob_check, SpawnMobDelayCallback pfunc = NULL, float yaw = -1, SpawnMobCallback pCreateFunc = NULL, unsigned int presetPosKey = 0) :
			m_pos(pos), m_delaySpawnTicks(delay_ticks), m_monsterid(monsterid), m_mobtype_check(mobtype_check), m_mob_check(mob_check), m_pfunc(pfunc), m_yaw(yaw), m_createFunc(pCreateFunc), m_PresetPosKey(presetPosKey) {
		}
		WCoord m_pos;
		int m_delaySpawnTicks;
		int m_monsterid;
		bool m_mobtype_check;
		bool m_mob_check;
		SpawnMobDelayCallback m_pfunc;
		SpawnMobCallback m_createFunc;
		float m_yaw;
		unsigned int m_PresetPosKey;
	};

    // 生物配额管理
    struct MobQuota {
        int maxCount;           // 最大数量
        int currentCount;       // 当前数量
        float baseSpawnRate;    // 基础生成概率
        float minSpawnRate;     // 最小生成概率
        
        MobQuota(int max = 0, float baseRate = 0.0f, float minRate = 0.0f) 
            : maxCount(max), currentCount(0), baseSpawnRate(baseRate), minSpawnRate(minRate) {}
    };
    
    std::map<MOB_TYPE, MobQuota> m_mobQuotas;
    bool m_quotaSystemEnabled;
    
    // NPC配额管理方法
    void initMobQuotas();
	void initCityNpcQuotas();
	bool isInCityArea(const WCoord& pos);
    float getAdjustedSpawnRate(MOB_TYPE mobType);
    void onMobSpawned(MOB_TYPE mobType);
    void onMobDespawned(MOB_TYPE mobType);
    void updateMobCounts();
	void performWorldGenSpawningByQuota(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen);
	bool performCityNpcSpawning(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen);

public:
	ActorMGT* GetActorMGT() {
		return m_ActorMGT;
	}
	struct DebugInfo {
#if GIZMO_DRAW_ENGABLE
		const char* hoverName;
		int distance;
#endif
	};
	static DebugInfo& GetDebugInfo() {
#if GIZMO_DRAW_ENGABLE
		static DebugInfo s_debugInfo{ nullptr,0 };
#else 
		static DebugInfo s_debugInfo;
#endif
		return s_debugInfo;
	}

    void setMobQuota(MOB_TYPE mobType, int maxCount, float baseRate = 0.1f);
    int getCurrentMobCount(MOB_TYPE mobType) const;
    int getMaxMobCount(MOB_TYPE mobType) const;
    void enableQuotaSystem(bool enable) { m_quotaSystemEnabled = enable; }

	void onMobDead(ClientMob* mob);

	void onChunkLoad(Chunk* pchunk) override;
	void onChunkUnload(Chunk* pchunk) override;

	//tolua_begin
	ClientActorMgr(World* pworld);
	virtual ~ClientActorMgr();
	virtual SpaceActorManager* GetSpaceManager() override{
		return m_SpaceManager;
	};

	virtual void reset() override;
	virtual void resetByToggleGameMakerMode();
	// ActorManagerInterface
	virtual void reLoadMobAI() override;
	void prepareTick();
	void tick();
	void tickBlockLine();
	/*
		针对Actors的tick函数
	*/
	void tickActors(std::vector<ClientActor*>& actors);
	void update(float dtime);
	void updateExtras(float dtime);

	int minDistToPlayer(const WCoord& pos, ClientPlayer** ret = NULL, bool live_players = false); //和最近player的距离, pos用坐标
	void SetActorId(ClientActor* actor, long long objid = 0);
	void SetBossId(ActorBoss* boss);
	// ActorManagerInterface
	virtual void spawnActor(IClientActor *actor, long long objid = 0) override;	//20211103: 新增参数objid codeby:lulei
	void spawnBoss(ActorBoss* boss);
	void spawnActor(ClientActor* actor, const WCoord& worldpos, float yaw, float pitch, long long objid = 0); //20211103: 新增参数objid codeby:lulei
	void spawnPlayerAddRef(ClientPlayer *player,bool isTeleport = false);
	void despawnActor(MNSandbox::AutoRef<ClientActor> actor,bool isTeleport = false);
	virtual void reTrackActor() override;
	virtual void clearTrackActor() override;
	/*
	 *	创建演员
	 */
	virtual bool addActorByChunk(IClientActor *iactor);
	virtual void removeActorByChunk(IClientActor* iactor, bool keep_inchunk = false);
	void InsertLiveActors(ClientActor* actor);
	void EraseLiveActors(ClientActor* actor, bool keepinchunk = false);
	void spawnMobTogether(World* pworld, const WCoord& pos, int spawnMobID, int count);
	ClientItem* spawnItem(const WCoord& worldpos, int itemid, int num, int protodata = 0, long long objid = 0);
	ClientItem* spawnEquipOrGun(const WCoord& worldpos, int itemid, int protodata = 0, long long objid = 0);//生成新枪械或者新装备
	long long spawnEquipOrGun(int x, int y, int z, int itemid);
	ClientItem* spawnSpecialLetterItem(const WCoord& worldpos, int itemid, int num, int protodata = 0, std::string letterStr = "");
	ClientItem* spawnItem(const WCoord& worldpos, const BackPackGrid& grid, int protodata = 0, long long objid = 0);

	
	//ActorManagerInterface
	virtual bool areAllPlayersAsleep() override;
	virtual void wakeAllPlayers() override;
	//椰子有跳过夜晚
	virtual bool areAllPlayersSkipNight() override;
	virtual void wakeSkipNightPlayers() override;
	void broadcastGameInfo(int infotype, int id, int num = 0, const char* name = NULL);
	ClientPlayer* getOccupiedPlayer(const WCoord& blockpos, int flag); //flag = ACTORFLAG_SLEEP, ACTORFLAG_SIT
	ClientPlayer* getPlayerByPos(const WCoord& blockpos);

	virtual unsigned getNumBoss()
	{
		return (unsigned)m_Bosses.size();
	}
	ActorBoss* getBoss(unsigned i)
	{
		return m_Bosses[i];
	}
	ClientPlayer* selectRandomPlayer();
	//tolua_end
	ClientAvatarSummonMob* spawnMob(const WCoord& pos, int monsterid);

	ClientMob *spawnMob(const WCoord &pos, int monsterid, bool mobtype_check, bool mob_check, float yaw=-1,int mobtype= 0, std::string monsterName = "",bool trigger = true, unsigned int presetPosKey = 0);
	//ActorManagerInterface
	virtual void spawnMobDelay(const WCoord &pos, int delay_ticks, int monsterid, bool mobtype_check, bool mob_check, SpawnMobDelayCallback pfunc=NULL, float yaw=-1, SpawnMobCallback pfCreate=NULL, unsigned int presetPosKey = 0) override;
	virtual void addMobSpawnNum(int mobtype, int num) override;
	virtual void addMobSpawnNumByID(int mobId, int num) override;
	void parseNpcList();
	ClientPlayer* selectNearPlayer(const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* userdata = NULL);
	//tolua_begin
	//默认选择方式的selectNearPlayer函数（供Lua调用）
	ClientPlayer* selectNearPlayerDefault(const WCoord& pos, int range);
	//tolua_end
	void selectNearAllPlayers(std::vector<ClientPlayer*>& players, const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* userdata = NULL);
	void selectNearAllMobs(std::vector<ClientMob*>& mobs, const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* data = NULL);
	void selectNearAllMobs(std::vector<long long>& mobObjIds, const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* data = NULL);
	void selectNearAllLivings(std::vector<ActorLiving*>& livings, const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* data = NULL);
	void selectNearAllItems(std::vector<ClientItem*>& items, const WCoord& pos, int range, SelectActorFunc pfunc = nullptr, void* data = nullptr);
	void selectNearAllVehicleActors(std::vector<ActorVehicleAssemble*>& actors, const WCoord& pos, int range, SelectActorFunc pfunc = NULL, void* data = NULL);
	ClientActor* FindActor(std::function<bool(ClientActor*)> pSelectFunc);
	void FindActors(std::vector<ClientActor*>& pVector, std::function<bool(ClientActor*)> pSelectFunc);

	//通用的查找附近对象的接口，之前的接口不能改，有可能在lua中用
	//通过selector来筛选对象类型, userdata可以直接在lamda回调中处理
	//range为block size，1600表示16*BLOCK_SIZE，一个chunk大小
	//不需要再用半径来算，直接用box的range来处理
	template<typename SelectorCallback, typename TraversalCallback>
	void selectNearActors(const WCoord& pos, int range, SelectorCallback selector, TraversalCallback func) {
		Rainbow::AABB box(pos.toWorldPos().toVector3(), float(range));
		m_ActorMGT->QueryNodes(box, [this, &selector, &func] (IClientActor* iactor) {
			if (selector(iactor->GetActor())) {
				func(iactor->GetActor());
			}
		});
	}

	//tolua_begin
	virtual void performWorldGenSpawning(Ecosystem *biome, int ox, int oz, int rangex, int rangez, ChunkRandGen &randgen) override;
	void setMobGen(bool hostile, bool animal);
	//ActorManagerInterface
	virtual bool getMobGen(MOB_TYPE mobtype) override;

	WCoord getNpcPositionById(int id);
	void clearMobs(MOB_TYPE mobtype);
	void clearMobs(int objtype);
	void clearCustomMobs();
	void clearPhysxObj();
	void resetRound();
	//ActorManagerInterface
	virtual void resetActorsByCustomModel(int id) override;
	virtual void resetActorsByFullyCustomModel(std::string skey) override ;
	virtual void resetActorsByImportModel() override;
	void clearMobs();
	void ReloadActor(int defid, int iObjType = 0);
	void spawnActor(ClientActor* actor, int x, int y, int z, float yaw, float pitch);
	int sleepingMembers();
	/*
		v2:添加参数mobtype， mobtype=1（家园养殖场生物）
	*/
	ClientMob* spawnMonster(int x, int y, int z, int monsterid, float yaw = -1, int mobtype = 0, std::string monsterName = "", bool mobType_check = true);
	//20210812: 新增是否触发触发器参数 codeby:wangshuai
	ClientMob* spawnMonsterEx(int x, int y, int z, int monsterid, bool trigger = true);

	ClientMob* spawnMobByPrefab(int x, int y, int z, const std::string& monsterName);
	ClientMob* spawnMobByPrefab(int x, int y, int z, const std::string& monsterName, float yaw, float pitch);
	ClientMob* spawnMobByPrefabCheck(int x, int y, int z, const std::string& monsterName, int yaw = -1, bool mobType_check = true, bool mob_check = true, int monsterId = 0);

	ClientPlayer* findPlayerByUin(int uin);
	virtual std::vector<int> findPlayersByRange(int range = 5) override;
	ClientMob* findMobByWID(long long wid);
	ActorHorse* findActorHorsebByWID(long long wid);
	ClientActor* findActorByWID(long long wid);
	/*
		通过serverid找到对应的ClientMob，家园需求
		serverid:服务器保存的怪物id
		Warning:此函数效率很低，尽量别用（家园也很少使用，宠物放生使用）
	*/
	ClientMob* findMobByServerID(const std::string& serverid);

	virtual bool iIsActorExist(IClientActor* actor);
	bool isActorExist(ClientActor* actor);
	bool isActorExist(long long objId);
	void showDialogPlotNpc(int npcid, bool state);

	void throwItemMotion(int posX, int posY, int posZ, int itemId, int dirX, int dirY, int dirZ, int shooterObjIdHigh, int shooterObjIdLow, int num = 1);

	virtual int getNumPlayer();
	virtual int getActorCount();

	ClientPlayer* getIthPlayer(int i);
	void clearActorsWithId(int actorid, bool bKill);
	int getTeamAllMobs(int teamid);
	long long getTeamMobByIndex(int idx);//从0开始
	void updateLoveAmbassadorIcon(int objId, int itemId, int itemCount, int animId, bool isSync = false); //更新爱心大使的头顶图标显示
	const std::vector<ClientPlayer*>& getAllPlayer() { return m_Players; }

	bool existMobNearPos(const WCoord& pos, int range, WORLD_ID objId); //检测一定范围内是否存在指定objId的生物  code-by:zoulongjin
	bool changeAllTamedToWild(long long playerId, int mobDefId, int wildDefId = 0); //将指定类型的驯服生物改成野生 code-by:lizb
	void tickOneActorPublic(ClientActor* actor, bool force_tick);
	virtual bool spawnDesertTradeCaravan(long long uin, WCoord explorePos, long long saveTime) override; //生成沙漠商队
	void setShowEffectState(bool isOpen);

	void BatchActor();
	void AddBatchActor(std::vector<ClientActor*>& batchActor, ClientActor* pActor, int& nEntityCount, int& nStaticEntityCount);
	//用于AI调试时候使用
	int GetActorUpdateStep(long long objid);
	//设置
	//tolua_end
	int GetActorUpdateStep();

	void getAllLiveActors(std::vector<ClientActor*>& actors);
	void getAllEmptyActors(std::vector<ClientActor*>& actors);
	// free some res after jump to new world
	void FreeOldAllActors();

	virtual void emptyActorChange(WORLD_ID id, bool flag);

	void UpdateAllActorsVisiableDistance();

	/*
	*	检测沙虫是否已经存在
	*/
	virtual bool IsSandwormExist();

	//bool onSearchName(jsonxx::Array& resultArray, const std::string& name);
	//bool onSearchCoordinate(jsonxx::Array& resultArray, const int& x, const int& y, const int& z);
	void forEachLiveActor(const std::function<bool(ClientActor&)>& func);
	void searchLiveActors(ClientActorSearcher* seacher);

	virtual void resetLiveMobAttr();

	void OnDrawGizmo();

	/* return -1 is not blockmesh，1 is select blockmesh，0 other*/
	int isPickBlockMeshActor(ClientActor& pActor, const Rainbow::Ray& ray, float mindist, float* pt);
	ClientActor* pickActorByOctTree(Rainbow::Ray ray, float* pt, float range, ActorExcludes& excludes, Rainbow::AABB& aabbBox, BlockScene* pPrefabScene,bool *isNewModel = nullptr);
	ClientActor* pickActorPhysic(float* pt, const Rainbow::Vector2f& mousePosition);
	virtual int GetGameObjDelay() { return m_gameobjDelay; }
	virtual void IncGameObjDelay() { m_gameobjDelay++; }
#pragma region ActorManagerInterface
	virtual ClientActorMgr* ToCastMgr() override;
	virtual IClientMob* iFindMobByServerID(const std::string& serverid) override;
	virtual IClientMob* iFindMobByWID(long long wid) override;
	virtual IClientActor* iFindActorByWID(long long wid) override;
	virtual bool IsActorHorse(IClientActor* actor) override;
	virtual bool IsActorBasketBall(IClientActor* actor) override;
	virtual void SpawnPlayerAddRef(IClientPlayer* player, bool isTeleport = false) override;
	virtual void AddBossToMapData(WorldMapData* destdata);

	virtual void spawnPlayer(IClientPlayer* iplayer);

	virtual IClientMob* iSpawnMob(const WCoord& pos,
		int monsterid,
		bool mobtype_check,
		bool mob_check,
		float yaw = -1,
		int mobtype = 0,
		std::string monsterName = "",
		bool trigger = true) override;

	virtual IClientMob* CreateMobFromDef(int monsterid, int mobtype = 0, bool trigger = true, bool init = true) override;

	virtual void HandleDesertVillageBuildingNpc(std::vector<DesertVillageNpcInfo>& npcinfo, int type,
		const WCoord& villagePos, const WCoord& startPos) override;

	virtual void HandleFishingVillageNpc(const  WCoord& fishingmanPos, const  WCoord& wharfPos,
		const  WCoord& newWharfPos, std::map<WCoord, int>& spawnPos) override;

	virtual void HandleIslandBuildNpc(int type, const  WCoord& startPos) override;

	virtual void HandleIcePlantBuildNpc(int type, const  WCoord& startPos) override;
	virtual void HandleIceAltarBuildNpc(int type, const  WCoord& startPos, VoxelModel* BigBuildVM) override;

	virtual bool HandleEcosysUnitDesertVillageNpc(std::vector<IClientActor*>& nearMobs, const WCoord& gatherPos, const WCoord& gatherCenterPos ) override;

	virtual void HandleBlockDragonEggBoss(int resid, int curtool) override;
	virtual void BlockBedForPlayer(const  WCoord& blockpos) override;
	virtual void BlockBedBindActor(const  WCoord& blockpos, WORLD_ID bindactor, WorldBed* container) override;
	virtual void BlockBedBodyShow(const  WCoord& blockpos, bool occupied) override;

	virtual IClientItem* SpawnIClientItem(const WCoord& worldpos, int itemid, int num, int protodata = 0, long long objid = 0) override;
	virtual IClientItem* SpawnIClientItem(const WCoord& worldpos, const BackPackGrid& grid, int protodata = 0, long long objid = 0) override;

	virtual bool HandleBlockButtonActorCollide(int blockid, const WCoord& blockpos, int& contactActorID, bool includeVehicle, CollisionDetect& cd) override;
	virtual void selectNearAllIClientMobs(std::vector<IClientMob*>& imobs, const WCoord& pos, int range, SelectMobCallback callbacktype = NONE_CALLBACK, void* data = NULL) override;
	virtual IClientPlayer* selectNearIPlayer(const WCoord& pos, int range) override;
	virtual IClientPlayer* iGetIthPlayer(int i) override;
	virtual IClientPlayer* iFindPlayerByUin(int uin) override;
	virtual IClientPlayer* IGetOccupiedPlayer(const WCoord& blockpos, int flag) override;
	virtual bool SelectNearActors(BoundaryBoxGeometry& box, int maxRange, IClientActor* actor) override;
	virtual void SelectNearbyActor(BoundaryBoxGeometry& box, int maxRange, std::vector<IClientActor*>& actors, IClientActor* actorMain) override;
	virtual void SelectNeighborMobs(const BoundaryGeometryHolder& holder, const WCoord& centerPos, int exrange, std::vector<IClientActor*>& mobs, IClientActor* actor) override;
	virtual void DespawnClientActor(IClientActor* actor, bool isTeleport = false) override;
#pragma endregion

	// 处理横跨多个chunk的gameobject，释放chunk时检查相关actor跨了不释放的chunk 
	// 检查gameobject是否跨chunk
	void CheckLargeGameObject(ClientActor* pActor);
	void DeleteLargeGameObject(ClientActor* pActor);
	virtual bool CanRemoveChunk(CHUNK_INDEX& chunkIdx) override;

	bool addMobPreset(int blockId, short X, short Y, short Z);
	bool saveMobPresetsToJson(const std::string& filePath = "");
	bool initMobPresetsInfo();

private:
	bool isMobGen(MOB_TYPE mobtype);
	void checkMobGen();
	void trySpawnMobs(World* pworld, MOB_TYPE mobtype);
	bool spawnMobPackInChunk(World* pworld, MOB_TYPE mobtype, const WCoord& spawncenter); //true: 继续在此区块生成, false: 跳过此区块
	bool checkMobStandPoint(const MonsterDef* mondef, Chunk* pchunk, const WCoord& pos);
	void tickOneActor(ClientActor* actor, bool force_tick); //need to clear
	void getMobSpawnChunks(GenMobTable& indices);
	void trySpawnTrader(int id, int torchid, bool isholiday);
	void trySpawnLoveAmbassador(); //尝试召唤爱心大使

	//need quit
	bool performWorldGenSpawnOne(Ecosystem* biome, int ox, int oz, int rangex, int rangez, ChunkRandGen& randgen, MOB_TYPE mobtype);
	void desertExploreChunkView(std::vector<ClientPlayer*>&);
	void spawnSharkEvent();

	// 生物生成概率控制
	bool canSpawnMobByProbability(MOB_TYPE mobtype);
	int getDelaySpawnMobCount(int monsterId);
	int getDelaySpawnMobCountByType(MOB_TYPE mobtype);
#ifdef IWORLD_DEV_BUILD
    void DrawTopView();
#endif
protected:
	World* m_World;
	std::map<WORLD_ID, ClientActor*> m_EmptyActors;
	std::map<WORLD_ID, ClientActor*> m_LiveActors;
	
	std::vector<ClientPlayer*> m_Players;
	std::map<int, MNSandbox::AutoRef<ClientPlayer>> m_Index;
	std::vector<ActorBoss*> m_Bosses;
	std::map<int, WORLD_ID> m_NpcActorsWID;
	std::vector<int> m_NpcList;

	// 预设生物生成信息
	std::unordered_map<WCoord, MobPresetInfo, WCoordHashCoder> m_MobPresetMap;
	// 预设生物运行时生成信息
	std::unordered_map<unsigned int, MobPresetSpawnInfo> m_MobPresetGenerated;
	
	bool m_canSpawn3507 = false;
	bool m_canSpawn3508 = false;
	bool m_canSpawn3120 = false;

	std::vector<SpawnMobDelayParam> m_SpawnMobDelayParams;
	ActorMGT* m_ActorMGT;

	bool m_MobGen[MAX_MOBTYPE];
	bool m_NpcGen;

	int m_LiveMobNum[MAX_MOBTYPE]; // 每大类生物数量（生物类型）
	int m_GenAccumTicks[MAX_MOBTYPE];
	std::map<unsigned short, int> m_LiveMobNumByID; // 每种生物数量（生物id）

	GenMobTable m_GenMobChunks;

	int m_CurSpawnMobID;
	int m_SpawnedMobNumOnce;

	int m_PlayerExistFlag;
	std::vector<long long> m_vTeamMobs;//临时缓存

	int  m_spawnSharkPoint; //1000点产生一只鲨鱼
	MNSandbox::Callback m_spawnSharkPointCallback;
	SpaceActorManager* m_SpaceManager;

	int m_gameobjDelay = 0;
	std::string m_mobPresetConfigPath;

	//处理横跨多个chunk的gameobject，释放chunk时检查相关actor跨了不释放的chunk
	std::unordered_map<WORLD_ID, std::set<CHUNK_INDEX> > m_largeActorChunk;
	std::unordered_map<CHUNK_INDEX, std::set<WORLD_ID>, ChunkIndexHashCoder> m_chunkLargeActor;
}; //tolua_exports

#endif
