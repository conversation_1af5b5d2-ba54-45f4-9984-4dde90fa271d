#include "MapGeoReport.h"

// 引入必要的头文件
#include "Core/GameEngine.h"

#include "AssetPipeline/AssetManager.h"
#include "Misc/FrameTimeManager.h"

#include "File/Packages/PackageAsset.h"
#include "BaseClass/SharePtr.h"

#include "Core/EngineClassRegister.h"
#include "Allocator/MemoryManager.h"
#include "Utilities/Logs/LogSystem.h"
#include "Misc/PlayerSettings.h"

#include "Common/GameStatic.h"
#include "IWorldConfig.h"
#include "Misc/GameSetting.h"

#include "MiniCloud/sim/SimulateMgr.h"
#include "MiniCloud/sim/LiveStreamMgr.h"
#include "ClientInfo.h"
#include "GameInfo.h"
#include "AssetPipeline/AssetProjectSetting.h"
#include "GetClientInfo.h"
#include "LuaInterface.h"
#include "Platforms/PlatformInterface.h"
#include "ZmqProxy.h"
#include "ActionLogger.h"
#include "ClientGameStandaloneServer.h"
#include "OgreTimer.h"
#include <Core/CallBacks/GlobalCallbacks.h>
#include "EffectManager.h"
#include "WorldManager.h"
#include "ClientActorManager.h"
#include "ClientActor.h"
#include "SandboxGameNode.h"
#include "fps/statistics/SandboxStatisticsNode.h"
#include "GeoJsonParser.h"
#include "IClientPlayer.h"
#include "ClientPlayer.h"
#include "DebugJsonCmd.h"
#include "ChunkGenerator.h"
#include "ClientMob.h"
#include "worldEvent/AirDrop/AirDropEvent.h"
#include "Core/worldEvent/WorldEventManager.h"
#include "ChestMgr.h"
#include "sign/HttpSign.h"
using namespace MINIW;
using nlohmann::json;

namespace MINIW {

MapGeoReporter::MapGeoReporter() 
    : m_LastTick(0)
    , m_StartTick(0) 
    , m_ReportInterval(5000) // 默认5秒上报一次
{
}

MapGeoReporter::~MapGeoReporter() {
}

void MapGeoReporter::Tick(double deltaTime) {
    unsigned long long currentTick = Rainbow::Timer::getSystemTick();
    if (m_LastTick == 0) { // First call
        m_StartTick = m_LastTick = currentTick;
    }

    unsigned long long totalElapsed = currentTick - m_StartTick;
    if (totalElapsed >= m_ReportInterval) {
        CollectAndReportGeoInfo();
        m_StartTick = currentTick;
    }
    
    m_LastTick = currentTick;
}

void MapGeoReporter::Reset() {
    m_LastTick = 0;
    m_StartTick = 0;
}

void MapGeoReporter::SetReportInterval(unsigned long long intervalMs) {
    m_ReportInterval = intervalMs;
}

void MapGeoReporter::CollectAndReportGeoInfo() {
    // Create a Feature collection
    std::vector<Feature> features;

    // 收集各种地理信息
    CollectPlayerPositions(features);   // 玩家位置
    CollectActorPositions(features);    // NPC位置
    CollectBuildingInfo(features);      // 建筑信息
    CollectAirdropInfo(features);       // 空投信息
    CollectChestInfo(features);         // 宝箱信息

    // 获取房间和地图信息
    std::string roomId, roomName, mapId, mapVer, serverIP;
    int serverPort;
    GetRoomInfo(roomId, roomName, mapId, mapVer, serverIP, serverPort);

    // 获取地图边界信息
    int mapMaxWidth, mapMaxHeight, mapMaxX, mapMaxZ, mapMinX, mapMinZ;
    GetMapBounds(mapMaxWidth, mapMaxHeight, mapMaxX, mapMaxZ, mapMinX, mapMinZ);

    // 获取地图略缩图URL
    char pixelurl[256] = {0};
    MINIW::ScriptVM::game()->callFunction("GetPixelMapUrl", ">s", pixelurl);

    // 将所有Feature转换为FeatureCollection JSON
    json featureCollection = GeoJsonParser::featureCollectionToJson(features);
    
    featureCollection["pixelurl"] = std::string(pixelurl);
    featureCollection["roomid"] = roomId;
    featureCollection["roomName"] = roomName;
    featureCollection["mapid"] = mapId;
    featureCollection["serverIP"] = serverIP;
    featureCollection["serverPort"] = serverPort;
    featureCollection["ts"] = time(0);
    featureCollection["mapMaxWidth"] = mapMaxWidth;
    featureCollection["mapMaxHeight"] = mapMaxHeight;
    featureCollection["mapMaxX"] = mapMaxX;
    featureCollection["mapMaxZ"] = mapMaxZ;
    featureCollection["mapMinX"] = mapMinX;
    featureCollection["mapMinZ"] = mapMinZ;

    // 输出JSON字符串（不使用美化格式，节省空间）
    std::string jsonStr = GeoJsonParser::stringify(featureCollection, false);
    
    // 记录原始大小
    size_t originalSize = jsonStr.size();
    
    // 使用zlib压缩数据
    unsigned long compressedLen = 0;
    void* compressedData = MINIW::compressBinaryAllocated((void*)jsonStr.c_str(), jsonStr.size(), compressedLen);
    LOG_INFO("MapGeoInfo: jsonStr:%s", jsonStr.c_str());
    
    // 输出压缩后的日志
    LOG_INFO("MapGeoInfo: originalSize:%d, compressedSize:%d", originalSize, compressedLen);

    // 通过ZMQ上报数据
    if (g_zmqMgr) {
        WorldManager* worldMgr = GetWorldManagerPtr();
        long long owid = worldMgr ? worldMgr->getWorldId() : 0;

        miniw::map_geo_data data;
        data.set_mapid(mapId);
        data.set_map_ver(mapVer);
        data.set_roomid(roomId);
        data.set_worldid(std::to_string(owid));
        data.set_server_ip(serverIP);
        data.set_server_port(serverPort);
        data.set_geo_data(compressedData, compressedLen);
        data.set_ts(time(0));

        std::string savedata;
        data.SerializeToString(&savedata);

        g_zmqMgr->SaveDataToDataServer(miniw::RTMySQL::SaveMapGeoData, savedata.c_str(), savedata.length(), owid, 10000);
    }

    // 释放压缩数据内存
    if (compressedData) {
        free(compressedData);
    }
}

void MapGeoReporter::CollectPlayerPositions(std::vector<Feature>& features) {
    // 获取所有玩家信息
    std::vector<IClientPlayer*> players;
    if (g_WorldMgr) {
        g_WorldMgr->getAllPlayers(players);

        // 为每个玩家创建一个位置点
        for (size_t i = 0; i < players.size(); i++) {
            IClientPlayer* iplayer = players[i];
            if (!iplayer) continue;

            ClientPlayer* player = iplayer->GetPlayer();
            if (!player) continue;

            // 获取玩家位置
            WCoord playerWCoord = player->getPosition();
            double longitude = playerWCoord.x;
            double latitude = playerWCoord.z;
            double altitude = playerWCoord.y; // 高度

            GeoPoint playerPos(longitude, latitude, altitude);
            auto geometry = std::make_shared<PointGeometry>(playerPos);

            // 创建玩家Feature
            Feature playerFeature;
            playerFeature.geometry = geometry;
            playerFeature.properties["type"] = "player";
            playerFeature.properties["name"] = player->getNickname();
            playerFeature.properties["uin"] = player->getUin();
            playerFeature.properties["objid"] = (long long)player->getObjId();
            playerFeature.properties["team"] = player->getTeam();

            playerFeature.id = "player_" + std::to_string(player->getUin());

            features.push_back(playerFeature);
        }
    }
}

void MapGeoReporter::CollectActorPositions(std::vector<Feature>& features) {
    // 获取所有NPC（筛选非玩家角色）
    if (g_WorldMgr) {
        auto* p_world = g_WorldMgr->getWorld(0);
        if (p_world) {
            std::vector<ClientActor*> all_actors;
            p_world->getActorMgr()->ToCastMgr()->getAllLiveActors(all_actors);
            
            // 为每个Actor创建Feature
            for (size_t i = 0; i < all_actors.size(); i++) {
                ClientActor* actor = all_actors[i];
                if (!actor) continue;
                
                // 获取NPC位置
                WCoord npcWCoord = actor->getPosition();
                double longitude = npcWCoord.x;
                double latitude = npcWCoord.z;
                double altitude = npcWCoord.y;
                
                GeoPoint npcPos(longitude, latitude, altitude);
                auto geometry = std::make_shared<PointGeometry>(npcPos);

                // 创建NPC Feature 
                Feature npcFeature;
                npcFeature.geometry = geometry;
                npcFeature.properties["type"] = "actor";
                npcFeature.properties["obj_type"] = actor->getObjType();
                npcFeature.properties["obj_id"] = (long long)actor->getObjId();
                
                if (actor->getObjType() == 0) {
                    ClientMob* mod = dynamic_cast<ClientMob*>(actor);
                    if (mod) {
                        const MonsterDef* def = mod->getDef();
                        if (def) {
                            npcFeature.properties["id"] = def->ID;
                            npcFeature.properties["name"] = def->Name.c_str();
                        }
                    }
                }
                
                features.push_back(npcFeature);
            }
        }
    }
}

void MapGeoReporter::CollectBuildingInfo(std::vector<Feature>& features) {
    // 获取所有单独建筑数据（包括遗迹建筑）
    if (g_WorldMgr) {
        auto* p_world = g_WorldMgr->getWorld(0);
        if (p_world) {
            // 获取所有单独建筑数据
            auto singleBuilds = p_world->getCityMgr()->GetAllSingleBuildData();
            for (auto& pair : singleBuilds) {
                auto config = GetCityConfigInterface()->getSingleBuildDataByName(pair.first);
                if (!config) continue;
                
                // 处理每个建筑
                auto& buildDatas = pair.second;
                for (auto& build : buildDatas.buildData) {
                    // 创建建筑多边形（使用chunk坐标转换为世界坐标）
                    std::vector<std::vector<GeoPoint>> rings;
                    std::vector<GeoPoint> outerRing;
                    
                    // 根据建筑的位置和范围创建外环
                    // 左下角
                    outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
                    // 右下角
                    outerRing.push_back(GeoPoint((build.x + build.rangeX) * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
                    // 右上角
                    outerRing.push_back(GeoPoint((build.x + build.rangeX) * CHUNK_BLOCK_X, (build.z + build.rangeZ) * CHUNK_BLOCK_Z));
                    // 左上角
                    outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, (build.z + build.rangeZ) * CHUNK_BLOCK_Z));
                    // 闭合环（重复第一个点）
                    outerRing.push_back(GeoPoint(build.x * CHUNK_BLOCK_X, build.z * CHUNK_BLOCK_Z));
                    
                    rings.push_back(outerRing);
                    
                    // 创建内环（如果建筑足够大）
                    if (build.rangeX > 2 && build.rangeZ > 2) {
                        std::vector<GeoPoint> innerRing;
                        // 内环左下角（向内缩进1个单位）
                        innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
                        // 内环右下角
                        innerRing.push_back(GeoPoint((build.x + build.rangeX - 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
                        // 内环右上角
                        innerRing.push_back(GeoPoint((build.x + build.rangeX - 1) * CHUNK_BLOCK_X, (build.z + build.rangeZ - 1) * CHUNK_BLOCK_Z));
                        // 内环左上角
                        innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + build.rangeZ - 1) * CHUNK_BLOCK_Z));
                        // 闭合内环
                        innerRing.push_back(GeoPoint((build.x + 1) * CHUNK_BLOCK_X, (build.z + 1) * CHUNK_BLOCK_Z));
                        
                        rings.push_back(innerRing);
                    }
                    
                    auto geometry = std::make_shared<PolygonGeometry>(rings);
                    
                    // 创建建筑Feature
                    Feature buildingFeature;
                    buildingFeature.geometry = geometry;
                    buildingFeature.properties["type"] = "building";
                    buildingFeature.properties["name"] = pair.first;  // 建筑名称
                    buildingFeature.properties["position_x"] = build.x;
                    buildingFeature.properties["position_z"] = build.z;
                    buildingFeature.properties["range_x"] = build.rangeX;
                    buildingFeature.properties["range_z"] = build.rangeZ;
                    buildingFeature.id = "building_" + pair.first + "_" + std::to_string(build.x) + "_" + std::to_string(build.z);
                    
                    features.push_back(buildingFeature);
                }
            }
        }
    }
}

void MapGeoReporter::CollectAirdropInfo(std::vector<Feature>& features) {
    // 空投宝箱
    auto& chest_list = AirDropEvent::chestList;
    for (auto& chest : chest_list) {
        // 创建空投箱子位置点					
        GeoPoint chestPos(chest.pos.x, chest.pos.z, chest.pos.y);
        auto geometry = std::make_shared<PointGeometry>(chestPos);
        
        // 创建空投箱Feature
        Feature chestFeature;
        chestFeature.geometry = geometry;
        chestFeature.properties["type"] = "airdrop";
        chestFeature.properties["name"] = "airdrop_chest";

        // 使用唯一ID
        chestFeature.id = "airdrop_chest" + std::to_string(chest.event_id) + "_" + std::to_string(chest.pos.x) + "_" + std::to_string(chest.pos.z);
        
        features.push_back(chestFeature);
    }

    // 空投路线
    PluginManager* pluginManager = GetPluginManagerPtr();
    WorldEventManager* worldEventManager = pluginManager->FindSubsystem<WorldEventManager>();
    if (worldEventManager) {
        std::vector<WorldEvent*> events = worldEventManager->GetActiveEvents();
        for (auto event : events) {
            if (event->GetEventType().find("airdrop") != std::string::npos) {
                AirDropEvent* airDropEvent = dynamic_cast<AirDropEvent*>(event);
                if (airDropEvent) {
                    const Rainbow::Vector3f& startPos = airDropEvent->GetStartLocation();
                    const Rainbow::Vector3f& endPos = airDropEvent->GetEndLocation();
                    const Rainbow::Vector3f& dropPos = airDropEvent->GetDropLocation();
                    const Rainbow::Vector3f& currPos = airDropEvent->GetCurrLocation();

                    GeoPoint startGeo(startPos.x, startPos.z, startPos.y);
                    GeoPoint endGeo(endPos.x, endPos.z, endPos.y);
                    GeoPoint dropGeo(dropPos.x, dropPos.z, dropPos.y);
                    GeoPoint currGeo(currPos.x, currPos.z, currPos.y);

                    // 空投路线
                    std::vector<GeoPoint> linePoints = {startGeo, endGeo};							
                    Feature lineFeature;
                    lineFeature.geometry = std::make_shared<LineStringGeometry>(linePoints);
                    lineFeature.properties["type"] = "airdrop";
                    lineFeature.properties["name"] = "airdrop_line";
                    lineFeature.id = "airdrop_line" + std::to_string(airDropEvent->GetEventID()) + "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);
                    features.push_back(lineFeature);

                    // 飞机位置点
                    Feature craftFeature;
                    craftFeature.geometry = std::make_shared<PointGeometry>(currGeo);
                    craftFeature.properties["type"] = "airdrop";
                    craftFeature.properties["name"] = "airdrop_craft";
                    craftFeature.id = "airdrop_craft" + std::to_string(airDropEvent->GetEventID()) + "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);
                    features.push_back(craftFeature);
                    
                    // 空投位置点
                    Feature dropFeature;
                    dropFeature.geometry = std::make_shared<PointGeometry>(dropGeo);
                    dropFeature.properties["type"] = "airdrop";
                    dropFeature.properties["name"] = "airdrop_drop";
                    dropFeature.id = "airdrop_drop" + std::to_string(airDropEvent->GetEventID()) + "_" + std::to_string(dropPos.x) + "_" + std::to_string(dropPos.z);
                    features.push_back(dropFeature);
                }
            }
        }
    }
}

void MapGeoReporter::CollectChestInfo(std::vector<Feature>& features) {
    WorldManager* worldMgr = GetWorldManagerPtr();
    if (worldMgr && worldMgr->getWorld(0)) {
        ChestManager* chestMgr = worldMgr->getWorld(0)->GetChestMgr();
        if (chestMgr) {
            const std::vector<SocChestInfo>& chests = chestMgr->getChests();
            for (auto& chest : chests) {
                if (chest.isSpawned) {
                    GeoPoint chestPos(chest.pos.x, chest.pos.z, chest.pos.y);
                    auto geometry = std::make_shared<PointGeometry>(chestPos);

                    Feature chestFeature;
                    chestFeature.geometry = geometry;
                    chestFeature.properties["type"] = "chest";
                    chestFeature.properties["name"] = "chest";
                    chestFeature.properties["obj_type"] = chest.chestId;
                    chestFeature.id = "chest_" + std::to_string(chest.pos.x) + "_" + std::to_string(chest.pos.z);
                    features.push_back(chestFeature);
                }
            }
        }
    }
}

void MapGeoReporter::GetRoomInfo(std::string& roomId, std::string& roomName, std::string& mapId, 
                                 std::string& mapVer, std::string& serverIP, int& serverPort) {
    // 修复致命bug：添加对GetClientInfoProxy()的空指针检查
    ClientInfoProxy* clientProxy = GetClientInfoProxy();
    if (!clientProxy) {
        LOG_WARNING("GetClientInfoProxy() returned nullptr, using default values");
        roomId = roomName = mapId = serverIP = "";
        mapVer = g_zmqMgr ? g_zmqMgr->GetMapVer() : "";
        serverPort = 0;
        return;
    }

    // 安全的参数获取函数
    auto getEnterParamSafe = [clientProxy](const char* key) -> const char* {
        const char* result = clientProxy->getEnterParam(key);
        return result ? result : "";
    };

    // 获取房间ID
    roomId = getEnterParamSafe("room_id");			
    roomName = roomId;						
    std::string trans_msg = getEnterParamSafe("trans_msg");			
    
    if (trans_msg != "") {			
        try {
            HttpSign sign;
            std::string msg = sign.urlDecode(trans_msg);
            LOG_WARNING("trans_msg urlDecode: %s", msg.c_str());

            json transJson = json::parse(msg);
            if (transJson.contains("room_name")) {
                roomName = transJson["room_name"];
            }
        } catch (...) {  // 捕获所有异常
            LOG_WARNING("JSON解析失败或URL解码失败: %s", trans_msg.c_str());
        }
    }

    // 获取地图ID
    mapId = getEnterParamSafe("toloadmapid");
    mapVer = g_zmqMgr ? g_zmqMgr->GetMapVer() : "";
    
    serverIP = getEnterParamSafe("ip");
    
    // 安全获取端口号
    const char* portStr = getEnterParamSafe("port");
    if (portStr && strlen(portStr) > 0) {
        try {
            serverPort = std::stoi(portStr);
        } catch (const std::exception& e) {
            serverPort = 0;
            LOG_WARNING("Invalid port parameter: %s, using default port 0", portStr);
        }
    } else {
        serverPort = 0; // 使用默认端口号
        LOG_WARNING("Port parameter is missing or empty, using default port 0");
    }
}

void MapGeoReporter::GetMapBounds(int& mapMaxWidth, int& mapMaxHeight, 
                                  int& mapMaxX, int& mapMaxZ, int& mapMinX, int& mapMinZ) {
    WorldManager* worldMgr = GetWorldManagerPtr();
    if (!worldMgr || !worldMgr->getWorld(0)) {
        mapMaxWidth = mapMaxHeight = mapMaxX = mapMaxZ = mapMinX = mapMinZ = 0;
        return;
    }

    World* pWorld = worldMgr->getWorld(0);
    auto provider = pWorld->getChunkProvider();
    int chunkXMin = provider->getStartChunkX();
    int chunkXMax = provider->getEndChunkX();
    int chunkZMin = provider->getStartChunkZ();
    int chunkZMax = provider->getEndChunkZ();

    // 转换为block坐标
    mapMaxWidth = abs((chunkXMax - chunkXMin + 1) * CHUNK_BLOCK_X);
    mapMaxHeight = abs((chunkZMax - chunkZMin + 1) * CHUNK_BLOCK_Z);
    mapMaxX = chunkXMax * CHUNK_BLOCK_X;
    mapMaxZ = chunkZMax * CHUNK_BLOCK_Z;
    mapMinX = chunkXMin * CHUNK_BLOCK_X;
    mapMinZ = chunkZMin * CHUNK_BLOCK_Z;
}

} // namespace MINIW
