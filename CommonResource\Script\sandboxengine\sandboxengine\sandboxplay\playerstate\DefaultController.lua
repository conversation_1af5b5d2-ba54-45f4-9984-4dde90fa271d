local config = {
	["states"] = {
		["cpp"] = {
            {"Idle","IdleState"},--{unique stateid, cplusplus class name which registered in PlayerStateFactory}
            --{"Jump","JumpState"},
            {"Walk","WalkState"},
            --{"Sneak","SneakState"},
            {"Fly","FlyState"},
            {"Swim","SwimState"},
            --{"GunUse","GunUseState"},
            {"ItemSkillUse","ChargeItemSkillState"},
            {"Shoot","ShootState"},
            {"PassBall","PassBallState"},
            {"Tackle","TackleState"},
            {"ActionIdle","ActionIdleState"},
            {"Dig","DigState"},
            {"BuildBlockRepair","BuildBlockRepairState"},
            {"AttackBlock","AttackBlockState"},
            {"RangeDig","RangeDigState"},--"AdvancedDigState",
            {"ChargeDig","ChargeDigState"},
            {"ChargeAttack","ChargeAttackState"},
            {"Eat","EatState"},
            {"DrinkWater","DrinkWaterState"},
            {"Use","UseState"},
            {"Jetpack","JetpackState"},
            {"GravityGunUse","GravityGunUseState"},
            {"GravityGunCharge","GravityGunChargeState"},
            {"BlockShot","BlockShotState"},
            {"Obstruct","ObstructState"},
            {"Grab","GrabState"},
            {"DribbleRunBasketBall","DribbleRunBasketBallState"},
            {"PassBasketBall","PassBasketBallState"},
            {"ShootBasketBall","ShootBasketBallState"},
            {"Recover","RecoverState"},
            {"Exploit","ExploitState"},
            {"FarmOpen","FarmOpenState"},
            {"Music","MusicState"},
            {"Basketball","BasketballState"},
            {"Football","FootballState"},
            {"Sleep","SleepState"},
            {"Sit","SitState"},
	        {"UGCEditor","UGCEditorState"},
	        {"ShieldDefence", "ShieldDefenceState"},
            {"ChargeToFishing", "ChargeToFishingState"},
            {"Fishing", "FishingState"},
            {"ActionBodyIdle", "ActionIdleBodyState"},
            {"PushSnowBall","PushSnowBallState"},
            {"PushSnowBallShoot","PushSnowBallShootState"},
            {"ComboAttack","ComboAttackState"},
            {"UseEmitterMove", "UseEmitterMoveState"},
            {"UseEmitter", "UseEmitterState"},
            {"GunAdvance","GunAdvanceState"},
            {"RevivePlayer","RevivePlayerState"},
            {"PieMenuCtrl","PieMenuCtrlState"},
            {"Skin","SkinState"},
		},
		["lua"] = {
            {"GunUse","sandboxengine/sandboxengine/sandboxplay/playerstate/GunUseState.lua"},--{unique stateid, lua component file name}
		},
	},

    ["fsm"] = {
        ["move"] = {
                {"Idle","Walk","Fly","Jetpack","Swim","UseEmitterMove"},
                {"Walk","Idle","Fly","Jetpack","Swim", "UseEmitterMove"},  
                {"Fly","Idle", "UseEmitterMove"}, 
                {"Jetpack","Idle","Walk","Swim","Fly", "UseEmitterMove"},  
                {"Swim","Idle","Fly", "UseEmitterMove"},
                {"UseEmitterMove", "Idle"},                                       
        },

        ["action"] = {
                {"ActionIdle","ChargeAttack","Dig","BuildBlockRepair","AttackBlock","RangeDig","ChargeDig","Eat", "DrinkWater","Use","GunUse","ItemSkillUse","Shoot",
                    "PassBall","Tackle","GravityGunUse","GravityGunCharge","BlockShot","Obstruct","Grab",
                    "DribbleRunBasketBall","PassBasketBall","ShootBasketBall","Recover","Exploit","FarmOpen","Music", "Basketball", "Football",
                    "Sleep","ShieldDefence","ChargeToFishing","UGCEditor","PushSnowBall","ComboAttack","UseEmitter","GunAdvance",
                    "RevivePlayer","PieMenuCtrl","Skin"},
                {"Dig","ActionIdle"},  
                {"BuildBlockRepair","ActionIdle"},  
                {"AttackBlock","ActionIdle"},  
                {"RangeDig","ActionIdle"},  
                {"ChargeDig","ActionIdle"},
                {"GunUse","ActionIdle"},  
                {"ChargeAttack","ActionIdle"},  
                {"Eat","ActionIdle"},   
                {"DrinkWater","ActionIdle"},
                {"Use","ActionIdle"},  
                {"ItemSkillUse","ActionIdle"}, 
                {"Sleep","ActionIdle"}, 
                {"RevivePlayer","ActionIdle"},
                {"PieMenuCtrl","ActionIdle"},
                {"Skin","ActionIdle"},
           
                {"GravityGunUse","ActionIdle"},  
                {"GravityGunCharge","ActionIdle"},  
                {"Recover","ActionIdle"},             
                {"Exploit","ActionIdle"},
                {"FarmOpen","ActionIdle"}, 
                {"Music","ActionIdle"},
		        {"ShieldDefence","ActionIdle"},   
                {"UGCEditor","ActionIdle","Dig","BuildBlockRepair", "AttackBlock","Eat","DrinkWater", "Use", "Exploit", "GunUse", "Shoot", "ItemSkillUse", "ChargeAttack", "ChargeDig", "RangeDig", "Music"},
                {"ChargeToFishing","ActionIdle","Fishing"},
                {"Fishing","ActionIdle"},

                --basketballState
                {"Basketball","ActionIdle", "Football", "PassBasketBall", "ShootBasketBall", "DribbleRunBasketBall", "Grab", "Obstruct", "BlockShot","PushSnowBall",},
                {"PassBasketBall","Basketball"},  
                {"ShootBasketBall","Basketball"}, 
                {"DribbleRunBasketBall", "Basketball"}, 
                {"Grab", "Basketball"}, 
                {"Obstruct","Basketball"},   
                {"BlockShot","Basketball"},  

                 --footballState
                {"Football","ActionIdle", "Basketball", "Shoot", "Tackle", "PassBall","PushSnowBall",},
                {"Shoot","Football"},   
                {"Tackle","Football"},  
                {"PassBall","Football"},  

                --PushSnowBall
                {"PushSnowBall","ActionIdle","PushSnowBallShoot","Basketball","Football"},
                {"PushSnowBallShoot","PushSnowBall"},

                {"ComboAttack", "ActionIdle"},
                {"UseEmitter", "ActionIdle"},
                {"GunAdvance", "ActionIdle"},
        },
     ["actionBody"] = {
                {"ActionBodyIdle","Sit",},
                {"Sit","ActionBodyIdle"}, 
        }    
    }
}

function F_Controller_Default_onInit(stateController)
    --states
    if config.states then 
        --load cpp states
        for i,v in ipairs(config.states.cpp or {} ) do
            --local stateid = v[1]
            local classname = v[2]
            if not _G.IsServerBuild then
                print("@@@register addState ",classname)
            end
            stateController:addState(classname)
        end
        --load lua states 
        for i,v in ipairs(config.states.lua or {} ) do
            local stateid   = v[1]
            local classname = v[2]
            if not _G.IsServerBuild then
                print("@@@register addStateExByLua ",stateid, classname)
            end
            stateController:addStateExByLua(stateid, classname)
        end
    end 
    --fsms
    if config.fsm then 
        for fsmname,fsmcfg in pairs(config.fsm) do
            stateController:addFSM(fsmname)
            --transition
            for _,trans in ipairs(fsmcfg) do
                local stateid = trans[1]
                if stateid then
                    for i=2,#trans do
                        if not _G.IsServerBuild then
                            print("@@@register addStateTransition ",stateid, "To"..trans[i], trans[i])
                        end
                        stateController:addStateTransition(stateid, "To"..trans[i], trans[i])
                    end
                    if not _G.IsServerBuild then
                        print("@@@register registerStateToFSM ",stateid, fsmname)
                    end
                    stateController:registerStateToFSM(stateid, fsmname)                    
                end                
            end
        end
    end 
    --transitioncfg
end