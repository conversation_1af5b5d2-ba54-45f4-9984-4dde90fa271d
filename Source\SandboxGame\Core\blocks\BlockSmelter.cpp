
#include "BlockSmelter.h"
#include "BlockMaterialMgr.h"
#include "container_world.h"
#include "world.h"
#include "IClientPlayer.h"
#include "EffectManager.h"
#include "FurnaceContainer.h"
#include "FurnaceContainerArray.h"
#include "BlockGeom.h"
#include "SectionMesh.h"
#include "ClientPlayer.h"
using namespace MINIW;

static int ox = 0;
static int oy = -10;
static int oz = 0;

IMPLEMENT_BLOCKMATERIAL(SmelterBlockMaterial)
void SmelterBlockMaterial::onPlayRandEffect(World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	int blockId = pworld->getBlockID(blockpos);

	if (blockdata & 8) 
	{
		pworld->setBlockLightEx(blockpos.x, blockpos.y, blockpos.z, 13);
		if (!(blockId == 798 || blockId == 799 || blockId == 802)) //不是铁炉，铜炉，石炉，一些旧有得皮肤壁炉
			pworld->getEffectMgr()->playParticleEffectAsync("particles/1017.ent", BlockCenterCoord(blockpos) + WCoord(ox, oy, oz), 20, 0, 0, false);
	}
	else
	{
		pworld->setBlockLightEx(blockpos.x, blockpos.y, blockpos.z, 0);
		if (!(blockId == 798 || blockId == 799 || blockId == 802)) //不是铁炉，铜炉，石炉，一些旧有得皮肤壁炉
			pworld->getEffectMgr()->stopParticleEffect("particles/1017.ent", BlockCenterCoord(blockpos) + WCoord(ox, oy, oz), true);
		
	}
		
}

SmelterBlockMaterial::~SmelterBlockMaterial()
{
	ENG_RELEASE(m_Mtls1[0]);
	ENG_RELEASE(m_Mtls1[1]);
}

void SmelterBlockMaterial::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
	SetToggle(BlockToggle_CanOutputQuantityEnergy, true);
	if (m_LoadOnlyLogic) return;

	char texname[256];
	int gettextype;
	const char *newtexname = getBaseTexName(texname, GetBlockDef(), gettextype);
	m_Mtls1[0] = g_BlockMtlMgr.createRenderMaterial(newtexname, GetBlockDef(), gettextype, getDrawType(), getMipmapMethod());

	sprintf(texname, "%s%s", GetBlockDef()->Texture1.c_str(), "_lit");//亮起
	m_Mtls1[1] = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), gettextype, getDrawType(), getMipmapMethod());
}

void SmelterBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	if (psection == NULL) return;

	int blockdata = psection->getBlock(blockpos).getData();
	RenderBlockMaterial *rbmtl = m_Mtls1[0];

	// 只有核心方块才创建网格
	if ((blockdata & 4) != 0)
	{
		return;
	}

	if (blockdata & 8)
	{
		if (psection->getBlock(blockpos).isEmpty() == false)
		{
			int blockId = psection->getBlock(blockpos).getResID();
			if (blockId == 798 || blockId == 799 || blockId == 802) //铁炉，铜炉，石炉
			{
				rbmtl = m_Mtls1[1];
			}
		}
	}

	if (rbmtl == NULL) return;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(rbmtl);
	if (psubmesh == NULL) return;

	Block pblock = psection->getBlock(blockpos);
	if (!geom || pblock.isEmpty()) return;

	BlockGeomMeshInfo meshinfo;
	int dirbuf = pblock.getData() % 4;;
	int dir = dirbuf & 0xffff;
	int mirrortype = (dirbuf >> 16) & 3;

	if (mirrortype > 0) 
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirrortype, NULL, 0, false);
	else 
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, 0, NULL, 0, false);

	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, rbmtl->getUVTile());
}

void SmelterBlockMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int blockdata = playerTmp->getCurPlaceDir();
	pworld->setBlockData(blockpos, blockdata, 3);
}

// 壁炉特效的生命周期需单独维护
void SmelterBlockMaterial::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	MultiModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	if (pworld == NULL) return;

	if (blockdata & 8)
	{
		pworld->setBlockLightEx(blockpos.x, blockpos.y, blockpos.z, 0);
		if (!(blockid == 798 || blockid == 799 || blockid == 802))
			pworld->getEffectMgr()->stopParticleEffect("particles/1017.ent", BlockCenterCoord(blockpos) + WCoord(ox, oy, oz));
	}
	
}

bool SmelterBlockMaterial::onMultiTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint, WorldContainer* container)
{
	if (!player) return false;

	if (!pworld->isRemoteMode())
	{
		if (container)
		{
			player->openContainer(container);
		}
	}

	return true;
}

int SmelterBlockMaterial::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		// 对于板块，我们可以使用现有的朝向或者一个固定值
		return playerTmp->getCurPlaceDir();
	}
	return 0;
}

WorldContainer* SmelterBlockMaterial::createMultiContainer(World* pworld, const WCoord& blockpos)
{
	return SANDBOX_NEW(FurnaceContainerArray, blockpos, m_BlockResID);
}

int SmelterBlockMaterial::outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);
	if(container) return container->calComparatorInputOverride();
	else return 0;
}

int SmelterBlockMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

/////////////////////////////////////////////////////////////////////////////////////////


IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(SmelterBlockMaterial)
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_Dir, int)(0, "Dir", "Block", &SmelterBlockMaterialInstance::GetBlockDir, &SmelterBlockMaterialInstance::SetBlockData);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurHear, int)(1, "CurHeat", "Block", &SmelterBlockMaterialInstance::GetCurHeat);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurHear2, int)(2, "CurHeat2", "Block", &SmelterBlockMaterialInstance::GetCurHeat2);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurHear3, int)(3, "CurHeat3", "Block", &SmelterBlockMaterialInstance::GetCurHeat3);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_MaxHeat, int)(4, "MaxHeat", "Block", &SmelterBlockMaterialInstance::GetMaxHeat, &SmelterBlockMaterialInstance::SetMaxHeat);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_MaxHeat2, int)(5, "MaxHeat2", "Block", &SmelterBlockMaterialInstance::GetMaxHeat2, &SmelterBlockMaterialInstance::SetMaxHeat2);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_MaxHeat3, int)(6, "MaxHeat3", "Block", &SmelterBlockMaterialInstance::GetMaxHeat3, &SmelterBlockMaterialInstance::SetMaxHeat3);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_ProvideHeat, float)(7, "ProvideHeat", "Block", &SmelterBlockMaterialInstance::GetProvideHeat);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_ProvideHeat2, float)(8, "ProvideHeat2", "Block", &SmelterBlockMaterialInstance::GetProvideHeat2);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_ProvideHeat3, float)(9, "ProvideHeat3", "Block", &SmelterBlockMaterialInstance::GetProvideHeat3);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurMeltPro, float)(10, "CurMeltPro", "Block", &SmelterBlockMaterialInstance::GetCurMeltPro);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurMeltPro2, float)(11, "CurMeltPro2", "Block", &SmelterBlockMaterialInstance::GetCurMeltPro2);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_CurMeltPro3, float)(12, "CurMeltPro3", "Block", &SmelterBlockMaterialInstance::GetCurMeltPro3);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_Quality, int)(13, "Quality", "Block", &SmelterBlockMaterialInstance::GetQuality, &SmelterBlockMaterialInstance::SetQuality);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SmelterBlockMaterial, R_Temperature, int)(14, "Temperature", "Block", &SmelterBlockMaterialInstance::GetTemperature, &SmelterBlockMaterialInstance::SetTemperature);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()

int SmelterBlockMaterialInstance::GetCurHeat() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_CurHeat;
	return 0;
}
int SmelterBlockMaterialInstance::GetCurHeat2() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_CurHeat2;
	return 0;
}
int SmelterBlockMaterialInstance::GetCurHeat3() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_CurHeat3;
	return 0;
}

void SmelterBlockMaterialInstance::SetMaxHeat(const int& heat)
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		container->m_MaxHeat = heat;
}
void SmelterBlockMaterialInstance::SetMaxHeat2(const int& heat)
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		container->m_MaxHeat2 = heat;
}
void SmelterBlockMaterialInstance::SetMaxHeat3(const int& heat)
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		container->m_MaxHeat3 = heat;
}
int SmelterBlockMaterialInstance::GetMaxHeat() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MaxHeat;
	return 0;
}
int SmelterBlockMaterialInstance::GetMaxHeat2() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MaxHeat2;
	return 0;
}
int SmelterBlockMaterialInstance::GetMaxHeat3() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MaxHeat3;
	return 0;
}

float SmelterBlockMaterialInstance::GetProvideHeat() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_ProvideHeat;
	return 0;
}
float SmelterBlockMaterialInstance::GetProvideHeat2() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_ProvideHeat2;
	return 0;
}
float SmelterBlockMaterialInstance::GetProvideHeat3() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_ProvideHeat3;
	return 0;
}

float SmelterBlockMaterialInstance::GetCurMeltPro() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MeltTicksFloat;
	return 0;
}
float SmelterBlockMaterialInstance::GetCurMeltPro2() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MeltTicksFloat2;
	return 0;
}
float SmelterBlockMaterialInstance::GetCurMeltPro3() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_MeltTicksFloat3;
	return 0;
}

void SmelterBlockMaterialInstance::SetQuality(const int& value)
{
	int val = value;
	if (value != FurnaceContainer::FURNACE_QUALITY_IRON && value != FurnaceContainer::FURNACE_QUALITY_COPPER && value != FurnaceContainer::FURNACE_QUALITY_STONE)
		val = FurnaceContainer::FURNACE_QUALITY_IRON;

	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		container->m_quality = val;
}
int SmelterBlockMaterialInstance::GetQuality() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_quality;
	return 0;
}
void SmelterBlockMaterialInstance::SetTemperature(const int& value)
{
	int val = value;
	if (value > 3) val = 3;
	if (value < 1) val = 1;

	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		container->m_temperature = val;
}
int SmelterBlockMaterialInstance::GetTemperature() const
{
	FurnaceContainer* container = dynamic_cast<FurnaceContainer*>(m_Container);
	if (container)
		return container->m_temperature;
	return 0;
}
