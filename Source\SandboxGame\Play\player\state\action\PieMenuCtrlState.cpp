#include "PieMenuCtrlState.h"
#include "PlayerControl.h"
#include "PCControl.h"
#include "PlayerInputHelper.h"
#include "LockCtrlComponent.h"
#include "SandboxEventDispatcherManager.h"
#include "BlockMaterialMgr.h"
#include "container_sandboxGame.h"
#include "BlockArchitecturalBase.h"

PieMenuCtrlState::PieMenuCtrlState(PlayerControl* host)
    : PlayerState(host)
    , m_isPieMenuOpen(false)
{
    m_StateID = "PieMenuCtrl";
}

PieMenuCtrlState::~PieMenuCtrlState()
{
}

bool PieMenuCtrlState::OpenPieMenu()
{
    if (m_isPieMenuOpen)
    {
        return true;
    }

    m_isPieMenuOpen = true;

	int itemid = m_Host->getCurToolID();
	const ItemDef* def = GetDefManagerProxy()->getItemDef(itemid);

	if (ItemIDs::BLUEPRINT == itemid)//写死用id来做判断  建筑图纸
	{
		jsonxx::Object obj;
		const WCoord& pos = m_Host->m_PickResult.block;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		eventid = MenuCtrlType_BUILDING_PLANNER_CTRL;
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPieMenu",
			MNSandbox::SandboxContext(nullptr)
			.SetData_Number("type", eventid)
			.SetData_String("data", obj.json())
		);
		return true;
	}

    int picktype = m_Host->doPick(false, false, false);
    if (picktype == 1) //是方块
    {
        int blockid = m_Host->GetWorld()->getBlockID(m_Host->m_PickResult.block);
        if (m_Host->GetPlayer()->GetComponent<LockCtrlComponent>()->IsOpenPieMenu(m_Host->m_PickResult.block))
        {
            jsonxx::Object obj;
            const WCoord& pos = m_Host->m_PickResult.block;
            obj << "x" << pos.x;
            obj << "y" << pos.y;
            obj << "z" << pos.z;

            eventid = MenuCtrlType_SOCLOCKCTRL;
            MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPieMenu",
                MNSandbox::SandboxContext(nullptr)
                .SetData_Number("type", eventid)
                .SetData_String("data", obj.json())
            );
            return true;
        }
        auto pmtl = g_BlockMtlMgr.getSingleton().getMaterial(blockid);
        if (pmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_WaterStorage)
        {
            jsonxx::Object obj;
            const WCoord& pos = m_Host->m_PickResult.block;
            obj << "x" << pos.x;
            obj << "y" << pos.y;
            obj << "z" << pos.z;
            obj << "id" << blockid;

            eventid = MenuCtrlType_WATER_STORAGE_CTRL;
            MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPieMenu",
                MNSandbox::SandboxContext(nullptr)
                .SetData_Number("type", eventid)
                .SetData_String("data", obj.json())
            );
            return true;
        }

        // 检查是否是一个有效的方块
        BlockDef* blockDef = GetDefManagerProxy()->getBlockDef(blockid);
       
        // 手持锤子和 检查是否为支持升级的方块类型
        if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR &&
            blockDef&&IsSupportedBlockType(blockDef->Type.c_str()))
        {
            const WCoord& pos = m_Host->m_PickResult.block;
            int buildLevel = 1;
            int canundo = 0;
            auto pworld = m_Host->getWorld();
            if (pworld)
            {
                auto porgmtl = pworld->getBlockMaterial(pos);
                if (porgmtl && porgmtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Architecture)
                {
                    containerArchitecture* pcontainer = static_cast<containerArchitecture*>(pmtl->getCoreContainer(pworld, pos));
                    if (pcontainer)
                    {
                        buildLevel = pcontainer->getBluePrintLevel();
                        canundo = pcontainer->getCanUnDo() ? 1 : 0;
                    }
                }
            }
            jsonxx::Object obj;
            obj << "x" << pos.x;
            obj << "y" << pos.y;
            obj << "z" << pos.z;
            obj << "level" << buildLevel;
            obj << "undo" << canundo;


            eventid = MenuCtrlType_BUILDING_MODIFY_CTRL;
            MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPieMenu",
                MNSandbox::SandboxContext(nullptr)
                .SetData_Number("type", eventid)
                .SetData_String("data", obj.json())
            );
            return true;
        }
    }
    return false;
}

bool PieMenuCtrlState::ClosePieMenu()
{
    m_isPieMenuOpen = false;
    MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ClosePieMenu",
        MNSandbox::SandboxContext(nullptr)
        .SetData_Number("type", eventid)
    );
    return true;
}

void PieMenuCtrlState::OnTick(float elapse)
{
}

void PieMenuCtrlState::doBeforeEntering()
{
    eventid = 0;
    m_isPieMenuOpen = false;
}

void PieMenuCtrlState::doBeforeLeaving()
{
}

std::string PieMenuCtrlState::update(float dtime)
{
    bool eKeyHeld = m_Host->getPlayerInputHelper()->getEKeyDetector()->isKeyHeld();
    bool rKeyHeld = m_Host->getPlayerInputHelper()->getRKeyDetector()->isKeyHeld();
    bool RightClickKeyHeld = m_Host->getPlayerInputHelper()->getRightClickDetector()->isKeyHeld();
    if (eKeyHeld || rKeyHeld || RightClickKeyHeld)
    {
        if (!OpenPieMenu())
        {
            return "ToActionIdle";
        }
        return "";
    }

    ClosePieMenu();
    return "ToActionIdle";
}


// 辅助函数，用于检查方块类型是否支持升级
bool PieMenuCtrlState::IsSupportedBlockType(const std::string& blockType)
{
    static const char* supportedTypes[] = {
        "multiceiling",   // 天花板-地基
        "multiwall",      // 墙
        //"socautodoor",    // 2518 扶梯门
        //"socdoor",        // 2524 1X2 木门
        //"socdoubledoor",  // 2516 2X2 双木门
        "multibranch",    // 1X2 立杆
        "multistair",     // 2X2楼梯
        "multitriangle"   // 2X2斜板
    };

    const int typeCount = sizeof(supportedTypes) / sizeof(supportedTypes[0]);
    for (int i = 0; i < typeCount; ++i)
    {
        if (blockType == supportedTypes[i])
            return true;
    }

    return false;
}


