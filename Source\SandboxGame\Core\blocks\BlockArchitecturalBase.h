
#ifndef __BLOCK_ARCHITECTURAL_BASE_H__
#define __BLOCK_ARCHITECTURAL_BASE_H__

class World;
class WCoord;
class ClientPlayer;
class WorldContainer;

class BlockArchitecturalBase
{
public:

public:
	BlockArchitecturalBase();
	virtual ~BlockArchitecturalBase();
	void initBuildData(int blockid, int& bptypeid, int& bplevel);
	virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1) = 0;
	bool onDamaged(World* pworld, const WCoord& blockpos, ClientPlayer* player, float damage);
	int onUpgradeBlock(World* pworld, const WCoord& blockpos, int upgradeNum, ClientPlayer* player, bool deal = true);
	int onRepairedBlock(World* pworld, const WCoord& blockpos, ClientPlayer* player, float amount);
	void onDestroyBlock(World* pworld, const WCoord& blockpos, ClientPlayer* player);
	WorldContainer* GetArchitecturalCoreContainer(World* pworld, const WCoord& blockpos);
	// int checkResourceEnough(World* pworld, const WCoord& blockpos, int type, int upgradeNum, ClientPlayer* player, bool deal = true);
private:

}; 

#endif