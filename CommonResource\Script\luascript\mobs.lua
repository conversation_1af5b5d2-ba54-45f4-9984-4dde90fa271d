
--变异结果/鸡变异表
g_MonsterEatMutate={
	[466] = {{16,3421},},--[吃的方块]={{几率，变异生物}，{几率，变异生物}，{几率，变异生物}}
	[467] = {{16,3423},},
	[468] = {{20,3424},}
};

--蝴蝶 对应的瓶子itemID
g_ButterflyToItemID = {
	[3885] = 144,
	[3886] = 145,
	[3887] = 146,
	[3888] = 147,
	[3889] = 148,
	[3890] = 149,
	[3255] = 200421,
};

function getButterFlyChangeBlockIdRandom(targetId)
	-- 把小草变成橙色龙舌兰、野蔷薇、灰色龙舌兰、星辰花、粉色龙舌兰、若兰、白椰花、风铃花、龙血花、风信子、红色龙舌兰、黄钟花、虚空之花
	local butterFly_ChangeBlockIDs = {
		[224] = {308,313,309,302,310,301,311,300,303,304,307,312,312,200417},
	}
	local tab = butterFly_ChangeBlockIDs[targetId] or {}
	local idx = math.random(1, table.getn(tab))
	return tab[idx] or 0
end

-- 此处多写了一个文件 是因为该文件会被热更 修改的地方太多 故而只添加一个local function 能让老版本热更之后 不会有问题
local function ActorComponentCallModule(pActor,componentName,functionName, ...)
	if not pActor then
		print(functionName.." ActorComponentCallModule pActor null")
		assert(0)
		return
	end
	if pActor[functionName] then
		-- 执行
		local method = pActor[functionName]
		local ret = {method(pActor,...)} -- 返回值转成数组
		return ret[1]
	else
		local pactorObject = tolua.cast(pActor, "MNSandbox::GameObject");
		local compBase = pactorObject:GetComponentByName(componentName)
		if compBase then
			local comp = tolua.cast(compBase,componentName)
			if comp and comp[functionName] then
				local method = comp[functionName]
				local ret = {method(comp,...)}-- 返回值转成数组
				return ret[1]
			end
		end
	end
	print(functionName.."can not find. ActorComponentCallModule failed")
	assert(0)
end
function MonsterMutate(monsterid, blockid)
	if g_MonsterEatMutate[blockid] ~= nil then
	   	local prob = math.random(0,100);
		local probtable = g_MonsterEatMutate[blockid];
		for i=1, #(probtable) do  
		    if prob < probtable[i][1] then
				return probtable[i][2]
			else
				prob = prob - probtable[i][1];
			end
		end 
	end
	return 0;
end

TamedAnimal_RequestReview = -1;

--小鸡
function Chicken_OnTick(mob)
	if mob:isAdult() then
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",12052, 1)
	end
end

--企鹅
function Penguin_OnTick(mob)
	if mob:isAdult() then
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",12053, 1)
	end
end

--年兽下蛋
function Nian_OnTick(mob)
	local prob = math.random(0,10);
	if prob < 3 then
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",12567, 1)
		ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3606.egg", 1.0, 1.0)
	elseif prob < 7 then
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",12568, 1)
		ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3606.egg", 1.0, 1.0)
	elseif prob < 9 then
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",12837, 1)
		ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3606.egg", 1.0, 1.0)
	end

end

--设置BreedingItem。1是加血，2是繁殖/成长，3是副产
function  InitBreedingItem()
	MobAddBreedingItem(0,0,0)

	--鸡
	--MobAddBreedingItem(3400, 20, 3)
	--MobAddBreedingItem(3400, 234, 2)
	--MobAddBreedingItem(3400, 11534, 2)
--	MobAddBreedingItem(3400, 11400, 2)
--	MobAddBreedingItem(3400, 11401, 2)
--	MobAddBreedingItem(3400, 11402, 2)
	--小鸡
	MobAddBreedingItem(3813, 11534, 2)

	
	--牛
	MobAddBreedingItem(3401, 11534, 2)
	--小牛
	MobAddBreedingItem(3812, 11534, 2)
	--驯服牛
	MobAddBreedingItem(3891, 11534, 1)
	
	
	--猪
	--MobAddBreedingItem(3402, 241, 3)
	--MobAddBreedingItem(3402, 12577, 2)
	MobAddBreedingItem(3402, 11534, 2)
	MobAddBreedingItem(3402, 11535, 2)
	--小猪
	--MobAddBreedingItem(3811, 236, 2)
	MobAddBreedingItem(3811, 11534, 2)
	MobAddBreedingItem(3811, 11535, 2)
	
	--羊
	MobAddBreedingItem(3403, 228, 3)
	MobAddBreedingItem(3403, 11534, 2)
	--小羊
	MobAddBreedingItem(3814, 11534, 2)

	--虚空角鹿
	MobAddBreedingItem(3246, 228, 3)
	MobAddBreedingItem(3246, 11534, 2)

	--虚空角鹿 驯服
	MobAddBreedingItem(3247, 228, 3)
	MobAddBreedingItem(3247, 11534, 2)
	
	-- --冰熊野生
	-- MobAddBreedingItem(3412, 12520, 1)
	-- MobAddBreedingItem(3412, 12703, 1)
	-- MobAddBreedingItem(3412, 12704, 1)
	MobAddBreedingItem(3412, 11535, 2)
	--冰熊驯服
	MobAddBreedingItem(3405, 11535, 1)
	--小冰熊
	MobAddBreedingItem(3820, 11535, 1)
	
	--骆驼驯服
	MobAddBreedingItem(3823, 11534, 1)
	
	
	-- --迅猛龙野生
	-- MobAddBreedingItem(3406, 11535, 2)
	-- --迅猛龙驯服
	-- MobAddBreedingItem(3406, 11535, 1)
	-- --小迅猛龙
	-- MobAddBreedingItem(3818, 11535, 1)

	
	-- --企鹅
	-- --MobAddBreedingItem(3409, 12602, 2)
	-- MobAddBreedingItem(3409, 11535, 2)
	-- --小企鹅
	-- --MobAddBreedingItem(3819, 12602, 2)
	-- MobAddBreedingItem(3819, 11535, 2)
	
	--鸵鸟
	MobAddBreedingItem(3411, 11534, 2)
	MobAddBreedingItem(3411, 11535, 2)
	--小鸵鸟
	MobAddBreedingItem(3817, 11534, 1)
	MobAddBreedingItem(3817, 11535, 1)
	--驯服鸵鸟
	MobAddBreedingItem(3410, 11534, 1)
	MobAddBreedingItem(3410, 11535, 1)
	
	
	--飞鸡
	--MobAddBreedingItem(3421, 255, 2)
	MobAddBreedingItem(3421, 11534, 2)
	--小飞鸡
	MobAddBreedingItem(3893, 11534, 2)
	--驯服的飞鸡
	MobAddBreedingItem(3422, 11534, 1)

	--战斗鸡
	MobAddBreedingItem(3423, 11534, 2)
	--小战斗鸡
	MobAddBreedingItem(3894, 11534, 2)

	--野生猴子
	MobAddBreedingItem(3870, 11534, 2)
	MobAddBreedingItem(3870, 11535, 2)
	--小猴子
	MobAddBreedingItem(3895, 11534, 2)
	MobAddBreedingItem(3895, 11535, 2)
	--驯服的猴子
	MobAddBreedingItem(3871, 11534, 1)
	MobAddBreedingItem(3871, 11535, 1)
	
	--野生狼
	-- MobAddBreedingItem(3407, 12516, 2)
	-- MobAddBreedingItem(3407, 12517, 2)
	-- MobAddBreedingItem(3407, 12518, 2)
	-- MobAddBreedingItem(3407, 12519, 2)
	-- MobAddBreedingItem(3407, 12520, 2)
	-- MobAddBreedingItem(3407, 12521, 2)
	-- MobAddBreedingItem(3407, 12522, 2)
	-- MobAddBreedingItem(3407, 12523, 2)
	-- MobAddBreedingItem(3407, 12524, 2)
	-- MobAddBreedingItem(3407, 12525, 2)
	-- MobAddBreedingItem(3407, 12526, 2)
	-- MobAddBreedingItem(3407, 12530, 2)
	-- MobAddBreedingItem(3407, 12531, 2)
	MobAddBreedingItem(3407, 11535, 2)
	--小狼
	MobAddBreedingItem(3809, 11535, 1)

	--虚空狐狸
	MobAddBreedingItem(3248, 11535, 2)
	MobAddBreedingItem(3250, 11535, 2)
	MobAddBreedingItem(3252, 11535, 2)
	
	--驯服的虚空狐狸
	MobAddBreedingItem(3249, 11535, 1)
	MobAddBreedingItem(3251, 11535, 1)
	MobAddBreedingItem(3253, 11535, 1)

	
	--驯服狗
	-- MobAddBreedingItem(3408, 12516, 2)
	-- MobAddBreedingItem(3408, 12517, 2)
	-- MobAddBreedingItem(3408, 12518, 2)
	-- MobAddBreedingItem(3408, 12519, 2)
	-- MobAddBreedingItem(3408, 12520, 2)
	-- MobAddBreedingItem(3408, 12521, 2)
	-- MobAddBreedingItem(3408, 12522, 2)
	-- MobAddBreedingItem(3408, 12523, 2)
	-- MobAddBreedingItem(3408, 12524, 2)
	-- MobAddBreedingItem(3408, 12525, 2)
	-- MobAddBreedingItem(3408, 12526, 2)
	-- MobAddBreedingItem(3408, 12530, 2)
	-- MobAddBreedingItem(3408, 12531, 2)	
	MobAddBreedingItem(3408, 11535, 1)
	--小狗
	-- MobAddBreedingItem(3810, 12516, 2)
	-- MobAddBreedingItem(3810, 12517, 2)
	-- MobAddBreedingItem(3810, 12518, 2)
	-- MobAddBreedingItem(3810, 12519, 2)
	-- MobAddBreedingItem(3810, 12520, 2)
	-- MobAddBreedingItem(3810, 12521, 2)
	-- MobAddBreedingItem(3810, 12522, 2)
	-- MobAddBreedingItem(3810, 12523, 2)
	-- MobAddBreedingItem(3810, 12524, 2)
	-- MobAddBreedingItem(3810, 12525, 2)
	-- MobAddBreedingItem(3810, 12526, 2)
	-- MobAddBreedingItem(3810, 12530, 2)
	-- MobAddBreedingItem(3810, 12531, 2)
	MobAddBreedingItem(3810, 11535, 1)
		
		
	
	--萤火虫
	--MobAddBreedingItem(3419, 11053, 1)
	--MobAddBreedingItem(3419, 12504, 1)
	--MobAddBreedingItem(3419, 12511, 1)
	
	
	-- --普通化石龙
	-- MobAddBreedingItem(3430, 11329, 1)
	
	-- --化石龙
	-- MobAddBreedingItem(3431, 11329, 1)
	
	-- --超级化石龙
	-- MobAddBreedingItem(3432, 11329, 1)
	
	-- --小飞鼠
	-- MobAddBreedingItem(3433, 12503, 1)
	
	-- --敏捷飞鼠
	-- MobAddBreedingItem(3434, 12503, 1)
	
	-- --机灵飞鼠
	-- MobAddBreedingItem(3435, 12503, 1)
	
	-- --普通麒麟
	-- MobAddBreedingItem(3436, 11211, 1)
	
	-- --麒麟
	-- MobAddBreedingItem(3437, 11211, 1)
	
	-- --祥瑞麒麟
	-- MobAddBreedingItem(3438, 11211, 1)
	
	-- --小陆行鸟
	-- MobAddBreedingItem(3439, 241, 1)
	
	-- --迅捷陆行鸟
	-- MobAddBreedingItem(3440, 241, 1)
	
	-- --跃动陆行鸟
	-- MobAddBreedingItem(3441, 241, 1)
	

	-- --小白象坐骑
	-- MobAddBreedingItem(3442, 228, 1)
	-- MobAddBreedingItem(3443, 228, 1)
	-- MobAddBreedingItem(3444, 228, 1)
	
	-- --小海豹坐骑
	-- MobAddBreedingItem(3445, 12520, 1)
	-- MobAddBreedingItem(3446, 12520, 1)
	-- MobAddBreedingItem(3447, 12520, 1)
	
	-- --小猫咪坐骑
	-- MobAddBreedingItem(3454, 12521, 1)
	-- MobAddBreedingItem(3455, 12521, 1)
	-- MobAddBreedingItem(3456, 12521, 1)	
	
	-- --小天马坐骑
	-- MobAddBreedingItem(3457, 236, 1)
	-- MobAddBreedingItem(3458, 236, 1)
	-- MobAddBreedingItem(3459, 236, 1)	
	
	-- --小仓鼠坐骑
	-- MobAddBreedingItem(3460, 11404, 1)
	-- MobAddBreedingItem(3461, 11404, 1)
	-- MobAddBreedingItem(3462, 11404, 1)	
	
	-- --魔龙坐骑
	-- MobAddBreedingItem(3469, 12517, 1)
	-- MobAddBreedingItem(3470, 12517, 1)
	-- MobAddBreedingItem(3471, 12517, 1)	
	
	-- --月亮坐骑
	-- MobAddBreedingItem(3478, 302, 1)
	-- MobAddBreedingItem(3479, 302, 1)
	-- MobAddBreedingItem(3480, 302, 1)	

	-- --圣夜雪橇
	-- MobAddBreedingItem(3483, 236, 1)
	-- MobAddBreedingItem(3484, 236, 1)
	-- MobAddBreedingItem(3485, 236, 1)
	
	-- --缤纷幻想
	-- MobAddBreedingItem(3486, 11310, 1)
	-- MobAddBreedingItem(3487, 11310, 1)
	
	-- --福牛犇犇
	-- MobAddBreedingItem(3488, 236, 1)
	-- MobAddBreedingItem(3489, 236, 1)
	
	-- --巨鲸坐骑
	-- MobAddBreedingItem(3490, 12521, 1)
	-- MobAddBreedingItem(3491, 12521, 1)
	-- MobAddBreedingItem(3491, 12705, 1)
	-- MobAddBreedingItem(3491, 12706, 1)
	-- MobAddBreedingItem(3492, 12521, 1)
	-- MobAddBreedingItem(3492, 12705, 1)
	-- MobAddBreedingItem(3492, 12706, 1)
	
	-- --莲花坐骑
	-- MobAddBreedingItem(3495, 12500, 1)
	-- MobAddBreedingItem(3496, 12500, 1)
	-- MobAddBreedingItem(3497, 12500, 1)
	
	-- --九色鹿
	-- MobAddBreedingItem(4500, 236, 1)
	-- MobAddBreedingItem(4501, 236, 1)
	-- MobAddBreedingItem(4502, 236, 1)
	-- MobAddBreedingItem(4503, 236, 1)

	-- --南瓜车
	-- MobAddBreedingItem(4504, 12515, 1)
	-- MobAddBreedingItem(4505, 12515, 1)
	-- MobAddBreedingItem(4506, 12515, 1)
	-- MobAddBreedingItem(4507, 12515, 1)

	-- --竹蜻蜓坐骑
	-- MobAddBreedingItem(4509, 11329, 1)
	-- MobAddBreedingItem(4510, 11329, 1)

	-- --白天鹅坐骑
	-- MobAddBreedingItem(4512, 12525, 1)
	-- MobAddBreedingItem(4513, 12525, 1)
	-- MobAddBreedingItem(4514, 12525, 1)
	-- MobAddBreedingItem(4515, 12525, 1)
	
	-- --黑飞机坐骑
	-- MobAddBreedingItem(4517, 11329, 1)
	-- MobAddBreedingItem(4518, 11329, 1)
	
	-- --白飞机坐骑
	-- MobAddBreedingItem(4520, 11329, 1)
	-- MobAddBreedingItem(4521, 11329, 1)
	
	-- --无敌旋风坐骑
	-- MobAddBreedingItem(4523, 11329, 1)

	-- --环海洛洛坐骑
	-- MobAddBreedingItem(4525, 12525, 1)
	-- MobAddBreedingItem(4526, 12525, 1)

	-- --尼东洛洛坐骑
	-- MobAddBreedingItem(4528, 12520, 1)
	-- MobAddBreedingItem(4529, 12520, 1)
	
    --             --龙坐骑
	-- MobAddBreedingItem(4531, 12519, 1)
	-- MobAddBreedingItem(4532, 12519, 1)
	-- MobAddBreedingItem(4533, 12519, 1)

    --             --画舫坐骑
	-- MobAddBreedingItem(4535, 11326, 1)
	-- MobAddBreedingItem(4536, 11326, 1)
	-- MobAddBreedingItem(4537, 11326, 1)

    --            --魔毯坐骑
	-- MobAddBreedingItem(4539, 12565, 1)
	-- MobAddBreedingItem(4540, 12565, 1)
	-- MobAddBreedingItem(4541, 12565, 1)

    --            --机械鸟坐骑
	-- MobAddBreedingItem(4543, 12527, 1)
	-- MobAddBreedingItem(4544, 12527, 1)
	-- MobAddBreedingItem(4545, 12527, 1)

    --            --打碟机坐骑
	-- MobAddBreedingItem(4547, 12574, 1)
	-- MobAddBreedingItem(4548, 12574, 1)

    --            --花架坐骑
	-- MobAddBreedingItem(4550, 12549, 1)
	-- MobAddBreedingItem(4551, 12549, 1)

    --            --扇子坐骑
	-- MobAddBreedingItem(4553, 12601, 1)
	-- MobAddBreedingItem(4554, 12601, 1)

    --            --旺财坐骑
	-- MobAddBreedingItem(4556, 12518, 1)

    --            --帝皇驹坐骑
	-- MobAddBreedingItem(4560, 12531, 1)
	-- MobAddBreedingItem(4561, 12531, 1)
	-- MobAddBreedingItem(4562, 12531, 1)

    --            --布老虎坐骑
	-- MobAddBreedingItem(4564, 12530, 1)
	-- MobAddBreedingItem(4565, 12530, 1)
	-- MobAddBreedingItem(4566, 12530, 1)

    --            --纸鹤坐骑
	-- MobAddBreedingItem(4572, 302, 1)
	-- MobAddBreedingItem(4573, 302, 1)
	-- MobAddBreedingItem(4574, 302, 1)

    --            --画卷坐骑
	-- MobAddBreedingItem(4580, 12520, 1)
	-- MobAddBreedingItem(4580, 12703, 1)
	-- MobAddBreedingItem(4580, 12704, 1)
	-- MobAddBreedingItem(4581, 12520, 1)
	-- MobAddBreedingItem(4581, 12703, 1)
	-- MobAddBreedingItem(4581, 12704, 1)

    --            --仙钥坐骑
	-- MobAddBreedingItem(4583, 12508, 1)
	-- MobAddBreedingItem(4584, 12508, 1)
	-- MobAddBreedingItem(4585, 12508, 1)

    --            --仙剑坐骑
	-- MobAddBreedingItem(4587, 12516, 1)
	-- MobAddBreedingItem(4588, 12516, 1)

    --            --浴缸坐骑
	-- MobAddBreedingItem(4590, 12500, 1)
	-- MobAddBreedingItem(4591, 12500, 1)
	-- MobAddBreedingItem(4592, 12500, 1)

    --            --狐仙坐骑
	-- MobAddBreedingItem(4594, 12500, 1)
	-- MobAddBreedingItem(4595, 12500, 1)
	-- MobAddBreedingItem(4596, 12500, 1)

    --            --猫猫车坐骑
	-- MobAddBreedingItem(4598, 11310, 1)

    --            --骆驼坐骑
	-- MobAddBreedingItem(4602, 12610, 1)
	-- MobAddBreedingItem(4603, 12610, 1)

    --            --孔雀坐骑
	-- MobAddBreedingItem(4606, 12525, 1)
	-- MobAddBreedingItem(4607, 12525, 1)
	-- MobAddBreedingItem(4608, 12525, 1)

    --            --月饼坐骑
	-- MobAddBreedingItem(4610, 11599, 1)
	-- MobAddBreedingItem(4611, 11599, 1)
	-- MobAddBreedingItem(4612, 11599, 1)

    --            --步撵坐骑
	-- MobAddBreedingItem(4614, 12525, 1)
	-- MobAddBreedingItem(4615, 12525, 1)
	-- MobAddBreedingItem(4616, 12525, 1)

    --            --滑板坐骑
	-- MobAddBreedingItem(4619, 12522, 1)
	-- MobAddBreedingItem(4620, 12522, 1)

    --            --飞天小猪坐骑
	-- MobAddBreedingItem(4624, 12601, 1)
	-- MobAddBreedingItem(4625, 12601, 1)

    --            --兔子坐骑
	-- MobAddBreedingItem(4628, 11599, 1)
	-- MobAddBreedingItem(4629, 11599, 1)

    --            --妆盒坐骑
	-- MobAddBreedingItem(4632, 12500, 1)
	-- MobAddBreedingItem(4633, 12500, 1)
	-- MobAddBreedingItem(4634, 12500, 1)

    --            --雪花坐骑
	-- MobAddBreedingItem(4636, 12503, 1)
	-- MobAddBreedingItem(4637, 12503, 1)

	--  --牦牛
	--  MobAddBreedingItem(3912, 763, 1)
	--  MobAddBreedingItem(3912, 11534, 2)

	--  --小牦牛
	--  MobAddBreedingItem(3913, 11534, 2)

    --            --迷你变形车坐骑
	-- MobAddBreedingItem(4644, 11310, 1)

    --            --庆典之书坐骑
	-- MobAddBreedingItem(4645, 12503, 1)

    --            --咕噜兽坐骑
	-- MobAddBreedingItem(4648, 12500, 1)

    --            --粽香摇坐骑
	-- MobAddBreedingItem(4650, 12503, 1)

    --            --山川云海坐骑
	-- MobAddBreedingItem(4654, 11226, 1)

    --            --邂逅甜心坐骑
	-- MobAddBreedingItem(4656, 12500, 1)

    --            --天禄坐骑
	-- MobAddBreedingItem(4660, 12516, 1)

    --            --核桃坐骑
	-- MobAddBreedingItem(4662, 12518, 1)
	-- --雪兔
	-- MobAddBreedingItem(3910, 11534, 2)
	-- --小雪兔
	-- MobAddBreedingItem(3911, 11534, 2)
	-- --虚空雪兔
	-- MobAddBreedingItem(3254, 11534, 2)
	-- MobAddBreedingItem(3254, 200411, 1)

	-- --速龙
	-- MobAddBreedingItem(3413, 11535, 2)
	-- --虚空迅猛龙
	-- --MobAddBreedingItem(3265, 11535, 2)

	-- --小速龙
	-- MobAddBreedingItem(3818, 11535, 2)
	-- --藏狐
	-- MobAddBreedingItem(3914, 11535, 1)
	-- --熊猫
	-- MobAddBreedingItem(3416, 11534, 1)
	-- --驯服的熊猫
	-- MobAddBreedingItem(3417, 11534, 1)
	-- --骆驼
	-- MobAddBreedingItem(3822, 11534, 1)
	-- --炎炎蟹
	-- MobAddBreedingItem(3881, 11535, 1)
	-- --驯服的炎炎蟹
	-- MobAddBreedingItem(3882, 11534, 1)
	-- --雀莺
	-- MobAddBreedingItem(3883, 11534, 1)
	-- --驯服的雀莺
	-- MobAddBreedingItem(3884, 11534, 1)
	-- --小海马
	-- MobAddBreedingItem(3624, 12719, 2)
	-- --驯服海马
	-- MobAddBreedingItem(3625, 12719, 1)
end

--初始化各个生物幼年的成长时间（没有的为0）MobAddGrowTime(小生物id，-生长时间tick，-生长差值)
function InitMobGrowTime()
	-- MobAddGrowTime(0,0,0) -- 初始化 18000-15min；24000-20min；30000-25min; 600-0.5min;1200-1min; 1800-1.5min
	-- MobAddGrowTime(3813, -18000,-600) --嘟嘟鸟
	-- MobAddGrowTime(3811, -24000,-1200) --墩墩
	-- MobAddGrowTime(3812, -30000,-1800) --沃沃兽
	-- MobAddGrowTime(3814, -24000,-1200) --角鹿
	-- MobAddGrowTime(3809, -24000,-1200) --狐狸
	-- MobAddGrowTime(3810, -24000,-1200) --灵狸
	-- MobAddGrowTime(3819, -18000,-600) --企鹅
	-- MobAddGrowTime(3817, -30000,-1800) --鸵鸟
	-- MobAddGrowTime(3820, -30000,-1800) --冰熊
	-- MobAddGrowTime(3818, -30000,-1800) --迅猛龙
	-- MobAddGrowTime(3893, -18000,-600) --飞鸡（--TODO）
	-- MobAddGrowTime(3894, -18000,-600) --战斗鸡
	-- MobAddGrowTime(3895, -24000,-1200) --猴子
	-- MobAddGrowTime(3913, -30000,-1800) --牦牛
	-- MobAddGrowTime(3911, -18000,-600) --雪兔
	-- MobAddGrowTime(3624, -24000,-1200) --海马
	-- MobAddGrowTime(3850, -24000,-1200) --灯笼鱼
end
--初始化各个生物的繁殖CD（没有的为0） MobAddLoveCD（大生物id，冷却时间tick）
function InitMobLoveTime()
	-- MobAddLoveCD(0,0) -- 初始化 9600-8min；12000-10min；14400-12min；
	-- MobAddLoveCD(3400, 9600) --嘟嘟鸟
	-- MobAddLoveCD(3402, 12000) --墩墩
	-- MobAddLoveCD(3401, 14400) --沃沃兽
	-- MobAddLoveCD(3403, 12000) --角鹿
	-- MobAddLoveCD(3407, 12000) --狐狸
	-- MobAddLoveCD(3408, 12000) --灵狸
	-- MobAddLoveCD(3409, 9600) --企鹅
	-- MobAddLoveCD(3411, 14400) --鸵鸟
	-- MobAddLoveCD(3412, 14400) --冰熊
	-- MobAddLoveCD(3413, 14400) --迅猛龙
	-- MobAddLoveCD(3421, 9600) --飞鸡（--TODO）
	-- MobAddLoveCD(3423, 9600) --战斗鸡
	-- MobAddLoveCD(3870, 12000) --猴子
	-- MobAddLoveCD(3912, 14400) --牦牛
	-- MobAddLoveCD(3910, 9600) --雪兔
	-- MobAddLoveCD(3254, 9600) --虚空雪兔
	-- MobAddLoveCD(3623, 12000) --海马
	-- MobAddLoveCD(3600, 12000) --灯笼鱼
end

--狐狸
function F3407_Init(actor)
	actor:setCanRideByPlayer(false);
end
function F3407_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiFierce(11, 100, 3.0)
	--actor:addAITargetFollowingPlayer(3, 10, false, 1); --第二个参数是每tick首次查找玩家的概率, 第三个是是否可视当遮挡的时候就不跟踪了，第四个是跟随的速度
	--actor:addAiTaskSit(2) --坐下
	--actor:addAiTaskTargetOnwnerHurtee(3) --主人被打帮忙
	--actor:addAiTaskTargetOnwnerHurter(3) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
	actor:addAiLeapAtTarget(4, 40.0, 200, 400) --跳跃攻击
	actor:addAiTaskAtk(4, 0, true, 1.5) --近战攻击
	actor:addAiMate(2, 1.0, 1, 1, 822) --繁殖
	--actor:addAiTaskFollowOwner(6, 2.0, 1000,200) --跟随主人
	actor:addAiTaskBeg(7, 11302, 800) --喜欢食物
	--actor:addAiTaskTargetNonTamed(8, 3403, 300) --攻击生物
	actor:addAiTaskWatchClosest(9, 600) --看附近的玩家
	actor:addAiTaskWander(10, 1.0, 60) --闲逛
    actor:addAiTaskLookIdle(10, 60) --左右看看
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
    
    --饥饿状态触发(新增AI)
    actor:addAIHungryStatus(10, 20, 100, 1614)                                  --饥饿状态，，参数1=优先级，参数2=进入饥饿状态判断值，参数3=退出饥饿状态判断值，参数4=特效库id
    actor:addAIHungryAtkTarget(6, 3400, 5, 3000)                              --饥饿攻击目标，参数1=优先级，参数2=生物id（填0为任意生物），参数3=触发几率，参数4=搜索范围
	actor:addAIHungryAtkTarget(6, 3409, 5, 3000)
	actor:addAIHungryAtkTarget(6, 3813, 5, 3000)
    actor:addAIHungryFollowPlayer(6, 10, true, 1.0, 1000, 15)                  --饥饿跟随玩家，参数2=每tick首次查找玩家的概率，参数3=是否可视当遮挡的时候就不跟踪，参数4=跟随速度，参数5=跟随搜索范围，参数6=发起攻击时间
    actor:addAIPickupItemEx(5, 12516, 200, 500, 12, 60)                        --饥饿吃食物，参数1=优先级，参数2=掉落物ID，参数3=恢复饥饿度，参数4=搜索范围，参数5=动作id，参数6=吃东西动作的TICK，60为3秒
	actor:addAIPickupItemExItem(12517)
	actor:addAIPickupItemExItem(12518)
	actor:addAIPickupItemExItem(12519)
	actor:addAIPickupItemExItem(12522)
	actor:addAIPickupItemExItem(12523)
	actor:addAIPickupItemExItem(12526)
	actor:addAIPickupItemExItem(12530)
	actor:addAIPickupItemExItem(12531)
	actor:addAIPickupItemExItem(12520)
	actor:addAIPickupItemExItem(12521)
	actor:addAIPickupItemExItem(12524)
	actor:addAIPickupItemExItem(12525)
	actor:addAIPickupItemExItem(12052)

	--饥饿时掏嘟嘟鸟蛋的窝，移除窝中所有嘟嘟鸟蛋
	actor:addAiTaskHungryDigEgg(5, 1185, 200, 1500)
end

--tame_item: 用于驯服的物品
--tamed_mob: 驯服后的mob id
--tame_prob: 1/tame_prob的概率会驯服
function TameAnimal_Interact(mob, player, tame_item, tamed_mob, tame_prob)
	local itemid = player:getCurToolID()
	if itemid ~= tame_item then return false end
	local bTamed = false
	local itemused = (mob:onFeed(itemid)==1)
	if not mob:getAngry() then
		if math.random(1,tame_prob) == 1  then
			mob:mobTamed(player:getUin(), tamed_mob)
			if tamed_mob == 3405 or tamed_mob == 3406 or tamed_mob == 3410 or tamed_mob == 3415 or tamed_mob == 3891 then
				TamedAnimal_RequestReview = 3;
			end
			bTamed = true
		else	
			mob:playTameEffect(false)
		end
		itemused = true
	end

	if itemused then player:shortcutItemUsed() end
	return bTamed
end

function F3407_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		--[[--项圈改色逻辑已删除
			if itemid >= 11501 and itemid <= 11514 then
			color = itemid - 11500
			if mob:getCollarColor() ~= color  then
				player:shortcutItemUsed()
				mob:setCollarColor(color)
				return true
			end
		end
		--]]
		
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	TameAnimal_Interact(mob, player, 12052, 3408, 3);
	if itemid == 12052 then
		mob:getMobAttrib():addFood(mob:getDef().Food)
		return true
	end
	return false
end	

--虚空狐狸
function F3248_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:setCanRideByPlayer(false);
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	--actor:addAiFierce(4, 4, 3.0)
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
	actor:addAiLeapAtTarget(4, 40.0, 200, 400) --跳跃攻击
	actor:addAiTaskAtk(4, 0, true, 1.5) --近战攻击
	-- actor:addAiMate(2, 1.0, 1, 1, 822) --繁殖
	actor:addAiTaskBeg(7, 11302, 800) --喜欢食物
	actor:addAiTaskWatchClosest(9, 600) --看附近的玩家
	actor:addAiTaskWander(10, 1.0, 60) --闲逛
    actor:addAiTaskLookIdle(10, 60) --左右看看
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
    
    --饥饿状态触发(新增AI)
    actor:addAIHungryStatus(10, 20, 100, 1614)                                  --饥饿状态，，参数1=优先级，参数2=进入饥饿状态判断值，参数3=退出饥饿状态判断值，参数4=特效库id
    actor:addAIHungryAtkTarget(6, 3400, 5, 3000)                              --饥饿攻击目标，参数1=优先级，参数2=生物id（填0为任意生物），参数3=触发几率，参数4=搜索范围
	actor:addAIHungryAtkTarget(6, 3409, 5, 3000)
	actor:addAIHungryAtkTarget(6, 3813, 5, 3000)
    actor:addAIHungryFollowPlayer(6, 10, true, 1.0, 1000, 15)                  --饥饿跟随玩家，参数2=每tick首次查找玩家的概率，参数3=是否可视当遮挡的时候就不跟踪，参数4=跟随速度，参数5=跟随搜索范围，参数6=发起攻击时间
    actor:addAIPickupItemEx(5, 12516, 200, 500, 12, 60)                                --饥饿吃食物，参数1=优先级，参数2=掉落物ID，参数3=恢复饥饿度，参数4=搜索范围，参数5=动作id，参数6=吃东西动作的TICK，60为3秒
	actor:addAIPickupItemEx(5, 12517, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12518, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12519, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12522, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12523, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12526, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12530, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12531, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12520, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12521, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12524, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12525, 200, 500, 12, 60)
	actor:addAIPickupItemEx(5, 12052, 200, 500, 12, 60)

	--饥饿时掏嘟嘟鸟蛋的窝，移除窝中所有嘟嘟鸟蛋
	actor:addAiTaskHungryDigEgg(5, 1185, 200, 1500)
end

function F3248_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end

	TameAnimal_Interact(mob, player, 12052, 3249, 3);
	if itemid == 12052 then
		mob:getMobAttrib():addFood(mob:getDef().Food)
		return true
	end

	return false
end	

function F3248_AttackEntityAsMob(mob, actor)
	local cur = ActorComponentCallModule(mob,"VacantComponent","getCurVacantEnergy") or 0
    ActorComponentCallModule(mob,"VacantComponent","setCurVacantEnergy", cur - 2)

	return true
end

function F3250_SetAi(actor)
	F3248_SetAi(actor)
end

function F3250_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end

	TameAnimal_Interact(mob, player, 12052, 3251, 3);
	if itemid == 12052 then
		mob:getMobAttrib():addFood(mob:getDef().Food)
		return true
	end

	return false
end

function F3250_AttackEntityAsMob(mob, actor)
	return F3248_AttackEntityAsMob(mob, actor)
end

function F3252_SetAi(actor)
	F3248_SetAi(actor)
end

function F3252_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end

	TameAnimal_Interact(mob, player, 12052, 3253, 3);
	if itemid == 12052 then
		mob:getMobAttrib():addFood(mob:getDef().Food)
		return true
	end

	return false
end

function F3252_AttackEntityAsMob(mob, actor)
	return F3248_AttackEntityAsMob(mob, actor)
end


--小狼
function F3809_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3809_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSwimming(1)
	actor:addAiTaskSit(2)
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 1.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11302, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1)
	actor:addAiTaskTargetOnwnerHurter(2)
	actor:addAiTaskTargetHurtee(3, true)
	--参数  优先级，未驯服时攻击的怪物id，概率
	actor:addAiTaskTargetNonTamed(4, 3403, 200)
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引

end
function F3623_Interact(mob, player)
	local itemid = player:getCurToolID()
	if itemid == 12719 then
		TameAnimal_Interact(mob, player, 12719, 3625, 1);	
		return true
	end
	return false
end
function F3809_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if itemid ~= 0 then
			if mob:isBreedItem(itemid)>0 and mob:getAttrib():getHP() < mob:getAttrib():getMaxHP()  then
				mob:getAttrib():addHP(DefMgr:getFoodDef(itemid).HealAmount)
				player:shortcutItemUsed()
				return  true
			--[[--项圈改色逻辑已删除
			elseif 	itemid > 11500 and itemid <= 11514  then
				color = itemid - 11500
				if mob:getCollarColor() ~= color  then
					player:shortcutItemUsed()
					mob:setCollarColor(color)
					return true
				end
			--]]
			end
		end
		
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	elseif itemid == 11302 and (not mob:getAngry())  then	
		player:shortcutItemUsed()
		if math.random(0,2) == 0  then
			mob:mobTamed(player:getUin(), 3810)
		else	
			mob:playTameEffect(false)
		end
		
		return true
	elseif itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
		player:playCurToolSound()
		player:shortcutItemUsed()
		if math.random(1,5) == 4 then
			mob:setGrowingAge(-1)
		end
		return true
	end
	
	return false
end	


--狗
function F3408_SetAi(actor)
	actor.m_directSwitchAni = true --连续的切动画
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAITaskToppleOver(0)
	actor:addAITaskSitbyItem(0, 2)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11302, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1)
	actor:addAiTaskTargetOnwnerHurter(2)
	actor:addAiTaskTargetHurtee(3, true, false)
	actor:addAiTaskTempt(2, 1.0, 11535, false)--被饲料吸引

end

function F3408_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if itemid ~= 0 then
			if mob:isBreedItem(itemid)>0 and mob:getAttrib():getHP() < mob:getAttrib():getMaxHP()  then
				mob:getAttrib():addHP(DefMgr:getFoodDef(itemid).HealAmount)
				player:shortcutItemUsed()
				return  true
			--[[--项圈改色逻辑已删除
			elseif 	itemid > 11500 and itemid <= 11514  then
				color = itemid - 11500
				if mob:getCollarColor() ~= color  then
					player:shortcutItemUsed()
					mob:setCollarColor(color)
					return true
				end
			--]]
			end
		end
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			if (player:getFlagBit(16) == false and not mob:getAIToppleOver()) or mob:getAIToppleOver() then
				-- mob:setAIToppleOver(not mob:getAIToppleOver())
			end
			return true
		end	
	end
	
	return false
end	

--驯服的虚空狐狸
function F3249_SetAi(actor)
	actor.m_directSwitchAni = true --连续的切动画
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAITaskToppleOver(0)
	actor:addAITaskSitbyItem(0, 2)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	-- actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11302, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1)
	actor:addAiTaskTargetOnwnerHurter(2)
	actor:addAiTaskTargetHurtee(3, true, false)
	actor:addAiTaskTempt(2, 1.0, 11535, false)--被饲料吸引

end

function F3249_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if itemid ~= 0 then
			if mob:isBreedItem(itemid)>0 and mob:getAttrib():getHP() < mob:getAttrib():getMaxHP()  then
				mob:getAttrib():addHP(DefMgr:getFoodDef(itemid).HealAmount)
				player:shortcutItemUsed()
				return true
			end
		end
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			if (player:getFlagBit(16) == false and not mob:getAIToppleOver()) or mob:getAIToppleOver() then
				-- mob:setAIToppleOver(not mob:getAIToppleOver())
			end
			return false
		end	
	end
	
	return false
end	

function F3249_AttackEntityAsMob(mob, actor)
	local cur = ActorComponentCallModule(mob,"VacantComponent","getCurVacantEnergy") or 0
    ActorComponentCallModule(mob,"VacantComponent","setCurVacantEnergy", cur - 2)

	return true
end

function F3251_SetAi(actor)
	F3249_SetAi(actor)
end

function F3251_Interact(mob, player)
	return F3249_Interact(mob, player)
end

function F3251_AttackEntityAsMob(mob, actor)
	return F3249_AttackEntityAsMob(mob, actor)
end

function F3253_SetAi(actor)
	F3249_SetAi(actor)
end

function F3253_Interact(mob, player)
	return F3249_Interact(mob, player)
end

function F3253_AttackEntityAsMob(mob, actor)
	return F3249_AttackEntityAsMob(mob, actor)
end


--小狗
function F3810_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3810_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAITaskSitbyItem(0, 2)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskSit(2)
	actor:addAiLeapAtTarget(3, 0.4, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 1.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11302, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1)
	actor:addAiTaskTargetOnwnerHurter(2)
	actor:addAiTaskTargetHurtee(3, true, false)
	actor:addAiTaskTempt(2, 1.0, 11535, false)--被饲料吸引
end

--哮天犬
function F4200_Init(mob)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F4200_SetAi(actor)
	actor:addAiTaskPetDanceToPlayer(0, 8000, 2500, 100, 100, 2);
	--actor:addAiTaskClosestDance(1, 30, 2, 2, 100*20, 2, 10*20, 100)
	actor:addAiTaskFollowOwner(2, 2.0, 500, 300, 1200)
	actor:showSkin('part1', true);
	actor:showSkin('part2', true);
end

function F3810_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if itemid ~= 0 then
			if mob:isBreedItem(itemid)>0 and mob:getAttrib():getHP() < mob:getAttrib():getMaxHP()  then
				mob:getAttrib():addHP(DefMgr:getFoodDef(itemid).HealAmount)
				player:shortcutItemUsed()
				return  true
			--[[--项圈改色逻辑已删除
			elseif 	itemid > 11500 and itemid <= 11514  then
				color = itemid - 11500
				if mob:getCollarColor() ~= color  then
					player:shortcutItemUsed()
					mob:setCollarColor(color)
					return true
				end
			--]]
			end
		end     
	
	    if itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
		    player:playCurToolSound()
		    player:shortcutItemUsed()
		    if math.random(1,5) == 4 then
			    mob:setGrowingAge(-1)
		    end
		    return true
	    end

		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	

--年兽野生
function F3505_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskPanic(1, 3)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskSit(2)
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetHurtee(3, true)
	actor:addAiTaskTargetNearest(4, 0, true, 0.0)
	--参数  优先级，未驯服时攻击的怪物id，概率
	actor:addAiTaskTargetNonTamed(5, 3402, 200)
	actor:addAiTaskTargetNonTamed(6, 3403, 200)
	actor:addAiTaskTargetNonTamed(7, 3404, 200)
	actor:addAiTaskTargetNonTamed(8, 3401, 200)
	
end

function F3505_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		--[[--项圈改色逻辑已删除
		if itemid >= 11501 and itemid <= 11514 then
			color = itemid - 11500
			if mob:getCollarColor() ~= color  then
				player:shortcutItemUsed()
				mob:setCollarColor(color)
				return true
			end
		end
		--]]
		
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return TameAnimalInPanic_Interact(mob, player, 11327, 3506, 3);
end	

--年兽驯服
function F3506_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSwimming(1)
	actor:addAiTaskSit(2)
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskWander(7, 1.0) --闲逛
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11327, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1) --主人被打帮忙
	actor:addAiTaskTargetOnwnerHurter(2) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
	actor:addAiTaskLayEggs(6, 6000, "ent.3606.egg", 12567, 40, 12568, 40, 12837, 20);
end

function F3506_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if itemid ~= 0 then
			if mob:isBreedItem(itemid)>0 and mob:getAttrib():getHP() < mob:getAttrib():getMaxHP()  then
				mob:getAttrib():addHP(DefMgr:getFoodDef(itemid).HealAmount)
				player:shortcutItemUsed()
				return  true
			--[[--项圈改色逻辑已删除
			elseif 	itemid > 11500 and itemid <= 11514  then
				color = itemid - 11500
				if mob:getCollarColor() ~= color  then
					player:shortcutItemUsed()
					mob:setCollarColor(color)
					return true
				end
			--]]
			end
		end
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	

--福球
function F3507_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0, 60) --闲逛
end

--小恶魔野生
function F3508_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskPanic(1, 1.3, 0.5) --受伤逃跑
	actor:addAiTaskSit(2)
	actor:addAiTaskSwimming(1)
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(4, 1.0, 20, 60, 1500,15052,1,0,1)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetHurtee(2, true)
	--参数  优先级，未驯服时攻击的怪物id，概率
	--actor:addAiTaskTargetNonTamed(5, 3402, 200)
	--actor:addAiTaskTargetNonTamed(6, 3403, 200)
	--actor:addAiTaskTargetNonTamed(7, 3404, 200)
	--actor:addAiTaskTargetNonTamed(8, 3401, 200)
end

function F3508_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then

		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	return TameAnimalInPanic_Interact(mob, player, 980, 3509, 3);
end	

function TameAnimalInPanic_Interact(mob, player, tame_item, tamed_mob, tame_prob)
	local itemid = player:getCurToolID()
	if itemid ~= tame_item then return false end

	local itemused = (mob:onFeed(itemid)==1)
	if mob:getPanic() then
		if math.random(1,tame_prob) == 1  then
			mob:mobTamed(player:getUin(), tamed_mob)
		else	
			mob:playTameEffect(false)
		end
		itemused = true
	end

	if itemused then player:shortcutItemUsed() end

	return true
end

--小恶魔驯服
function F3509_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSwimming(1)
	actor:addAiTaskSit(2)
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(4, 1.0, 20, 60, 1500,15052,1,0,1)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskWander(7, 1.0) --闲逛
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 980, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1) --主人被打帮忙
	actor:addAiTaskTargetOnwnerHurter(2) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
end

function F3509_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	


--猪
function F3402_Init(mob)
end

function F3402_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(2, 1.25)
	actor:addAiMate(3, 1.0, 3, 5, 822)
	--actor:addAiTaskTempt(4, 1.2, 236, false)
	--actor:addAiTaskTempt(4, 1.25, 11400, false)--被小麦种子吸引
	--actor:addAiTaskTempt(4, 1.2, 12577, false)--被野萝卜大餐吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiTaskSleep(5, 4000, 100*20, 11311, 1, 2) --游戏里面5小时, 睡100秒
	actor:addAiTaskFollowParent(6, 1.1)
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
end
local function mobDropItem(mob,player,foodList,dropItemList)--生物随机掉落排泄物
	local ids = dropItemList and dropItemList or {11311};
	local itemid = player:getCurToolID()
	for index, value in ipairs(foodList) do
		if itemid == value then
			local result = ids[math.random(#ids)]
			if math.random(4)==1 then
				--mob:dropItem(result, 1);
				ActorComponentCallModule(mob,"DropItemComponent","dropItem", result, 1)
				ActorComponentCallModule(mob,"SoundComponent","playSound","misc.fart", 1.0, 1.0)
			end	
			player:shortcutItemUsed();
			return true
		end
	end
end
function F3402_Interact(mob, player)
	
	local foodList = {236}
	if mobDropItem(mob,player,foodList) then
		return true
	end
	return false
end

--小猪
function F3811_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3811_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.25)
	actor:addAiMate(3, 1.0)
	--actor:addAiTaskTempt(4, 1.2, 236, false)--被野萝卜吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiTaskFollowParent(5, 1.1)
	actor:addAiTaskWander(6, 1.0)
end

-- function F3811_Interact(mob, player)
-- 	local itemid = player:getCurToolID()	
-- 	if itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
-- 		player:playCurToolSound()
-- 		player:shortcutItemUsed()
-- 		if math.random(1,5) == 4 then
-- 			mob:setGrowingAge(-1)
-- 		end
-- 		return true
-- 	end
	
-- 	return false
-- end	

--野人猎手
function F3105_SetAi(actor)
	--actor:setSunHurt(true)
	--actor:addAISavageStandSleep(1, 20, 40, 0.12);	 --白天睡觉，日照+亮度大于配置则睡觉
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	--actor:addAiTaskRestrictSun(2)
	--actor:addAiTaskFleeSun(4, 1.0)
	--actor:addAiTaskPanic(1, 3.25,0.0001)
	--actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true)--最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	--actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1)
	actor:addAiTaskTargetSpecificEntity(2, 3121, 10) --攻击生物（稻草人）
	actor:addAIGetSpecialAttackattr(6, 817, 80, 1); --第二个参数是blockid,然后是每tick的概率分母,第三个参数是速度

	actor:addAIAttractBlock(2, 1200, 2, 1, 3); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是概率，第四个是走路速度，第5个是离篝火方块几格停下来
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒
	actor:addAiTaskTargetSpecificEntity(3, 3200, 10) --攻击生物（新野人）
	actor:addAiTaskTargetSpecificEntity(3, 3201, 10) --攻击生物（新野人猎手）
	actor:addAiTaskTargetSpecificEntity(3, 3202, 10) --攻击生物（新野人萌宝）
end

------------------------------------------------------------------------------------------------
-- ashcraft begin

--------------------------------------------- 动物系列 ---------------------------------------- 
-- 6050001 鸡
function F6050001_Init(mob)
end

function F6050001_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块进行巡逻
	
	actor:addAiTaskWander(5, 1.0, 60) --随机游荡巡逻
	actor:addAiTaskLookIdle(6, 60) --左右张望	
end

-- 6050003 牛
function F6050003_Init(mob)
end

function F6050003_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块进行巡逻
	
	-- 攻击相关AI
	actor:addAiTaskTargetHurtee(1, true) --受到攻击后反击
	actor:addAiTaskAtk(2, 0, false, 1.2) --近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0, 60) --随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 600) --观察附近玩家
	actor:addAiTaskLookIdle(6, 60) --左右张望
end

-- 羊
function F52104_Init(mob)
end

function F52104_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.25)
	actor:addAiMate(2, 1.0, 1, 1, 822)
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskEatLeaf(6,50, 200)  --后面那两个参数是每tick的执行概率，分别对应幼儿和成年
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
end

-- 6050005 狼
function F6050005_Init(mob)
end

function F6050005_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块进行巡逻
	
	-- 攻击相关AI
	--actor:addAiTaskTargetHurtee(1, true) --受到攻击后反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1) --主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.2) --近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0, 60) --随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 600) --观察附近玩家
	actor:addAiTaskLookIdle(6, 60) --左右张望
	
	-- 警戒系统
	actor:addAIWarning(1, 2, 200, 20) --警戒系统，发现敌人时提醒附近同伴
end

--------------------------------------------- 坐骑系列 ---------------------------------------- 
-- 6050008 小型马 
function F6050008_Init(mob)
end

function F6050008_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskPanic(2, 1.4) --受伤逃跑
	actor:addAiTaskWander(3, 1.0) --闲逛
	actor:addAiTaskWatchClosest(4, 600) --观察附近玩家
	actor:addAiTaskLookIdle(5) --左右看看
end

-- 6050009 中型马
function F6050009_Init(mob)
end
function F6050009_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskPanic(2, 1.4) --受伤逃跑
	actor:addAiTaskWander(3, 1.0) --闲逛
	actor:addAiTaskWatchClosest(4, 600) --观察附近玩家
	actor:addAiTaskLookIdle(5) --左右看看
end

-- 6050010 大型马
function F6050010_Init(mob)
end
function F6050010_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskPanic(2, 1.4) --受伤逃跑
	actor:addAiTaskWander(3, 1.0) --闲逛
	actor:addAiTaskWatchClosest(4, 600) --观察附近玩家
	actor:addAiTaskLookIdle(5) --左右看看
end

-- 6050011 异星马
function F6050011_Init(mob)
end
function F6050011_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskPanic(2, 1.4) --受伤逃跑
	actor:addAiTaskWander(3, 1.0) --闲逛
	actor:addAiTaskWatchClosest(4, 600) --观察附近玩家
	actor:addAiTaskLookIdle(5) --左右看看
end

--------------------------------------------- 人形NPC系列
-- 6050101 拾荒者
function F6050101_Init(mob)
end
function F6050101_SetAi(actor)
	-- 基础移动和路径AI
	actor:addAiTaskSwimming(1) -- 游泳能力
	actor:addAiTaskFollowDirection(1, 1.1) -- 寻找路径方块进行巡逻
	
	-- 攻击相关AI
	actor:addAiTaskTargetHurtee(1, true) -- 受到攻击后反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1) -- 主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.0) -- 近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0) -- 随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 800) -- 观察附近玩家
	actor:addAiTaskLookIdle(6) -- 左右张望
	
	-- 警戒系统
	actor:addAIWarning(1, 2, 200, 20) -- 警戒系统，发现敌人时提醒附近同伴	
end

-- 6050102 土匪
function F6050102_Init(mob)
end
function F6050102_SetAi(actor)
	-- 基础移动和路径AI
	actor:addAiTaskSwimming(1) -- 游泳能力
	actor:addAiTaskFollowDirection(1, 1.1) -- 寻找路径方块进行巡逻
	
	-- 攻击相关AI
	actor:addAiTaskTargetHurtee(1, true) -- 受到攻击后反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1) -- 主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.0) -- 近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0) -- 随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 800) -- 观察附近玩家
	actor:addAiTaskLookIdle(6) -- 左右张望
	
	-- 警戒系统
	actor:addAIWarning(1, 2, 200, 20) -- 警戒系统，发现敌人时提醒附近同伴
	
	-- 破坏能力（土匪更加凶恶）
	actor:addAiTaskBreakDoor(1) -- 破坏门的能力	
end

-- 6050103 奴隶贩子
function F6050103_Init(mob)
end
function F6050103_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300014, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒	
end

-- 6050104 遗迹守卫
function F6050104_Init(mob)
end
function F6050104_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒
end

-- 6050105 雇佣兵
function F6050105_Init(mob)
end
function F6050105_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒	
end

-- 6050106 土匪头目 使用半自动步枪
function F6050106_Init(mob)
end
function F6050106_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒	
end

-- 6050107 重甲守卫 使用AK47
function F6050107_Init(mob)
end
function F6050107_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒	
end

-- 6050108 狙击手
function F6050108_Init(mob)
end
function F6050108_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块
	actor:addAiTaskTargetNearest(2, 1, true, 0.0, 0, 1)
	-- actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true) --最后一个参数是是否在追击玩家的过程中寻找障碍躲避
	-- 1. speed 2. minatktime 3. maxattime 4. range 5. projectileid 6. power 7. buffid, 8. count
	actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500, 5300013, 10, 0, 1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒		
end

-- ashcraft end

------------------------------------------------------------------------------------------------


--野人
function F3101_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanPassClosedWoodenDoors",true)
	actor:addAISavageStandSleep(1, 20, 40, 0.12);	 --白天睡觉，优先级，触发延迟1，触发延迟2（20tick一秒，不配默认50，100）
	--actor:setSunHurt(true)
	actor:addAiTaskSwimming(1)--4，游泳
	actor:addAiTaskTargetHurtee(1, true)--1，受到攻击后反击
	actor:addAiTaskBreakDoor(1)--0，破坏门
	actor:addAiTaskFollowDirection(2, 1.1)--寻找路径方块--3
	--actor:addAiTaskPanic(1, 3.25,0.0001)--1，逃跑
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1)--1，主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.0)--3，近战攻击
	--actor:addAiTaskMoveTowardsRestriction(4, 1.0)--1，找随机位置移动
	actor:addAiTaskWander(6, 1.0)--1
	actor:addAiTaskWatchClosest(7, 800)--2，看附近玩家
	actor:addAiTaskLookIdle(7)--3，到处看看
	
	actor:addAiTaskRide(3,3407, 3000)--7，骑狼，第二个参数要骑上的怪物id，第三个参数是距离单位厘米
	actor:addAiTaskKickAway(3, 3109, 200, 100);	--7，踢动物，第二个参数是怪物id,第三个参数是每tick触发这个ai的概率，第四个参数是力度
	
	actor:addAISavageSleep(1);--7，睡觉
	actor:addAIAttractBlock(2, 1200, 2, 1, 3); --3--喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是概率，第四个是走路速度，第5个是离篝火方块几格停下来
	actor:addAIWarning(1, 2, 200, 20);--警戒--1
	actor:addAiTaskTargetSpecificEntity(3, 3200, 10) --1攻击生物（新野人）
	actor:addAiTaskTargetSpecificEntity(3, 3201, 10) --1攻击生物（新野人猎手）
	actor:addAiTaskTargetSpecificEntity(3, 3202, 10) --1攻击生物（新野人萌宝）

	actor:addAiTaskAtk_TianGou(1, 0, true, 1.5)--3，近战攻击
end

function F3101_AttackEntityAsMob(mob, actor)
	if mob:getEquipItem(5) == 0 and mob:isBurning() then
		actor:sureFireBurnComponent()
		ActorComponentCallModule(actor,"FireBurnComponent","setFire",100,1)
	end
	
	return true;
end

function MobAddRandomEquipments(mob)
	--[[
		法务版本：去掉随机装备的头部装备和花冠
	]]
	if math.random() < 0.015 * 0.75 then
		local quality = 0
		local sumprob = 0
		local r = math.random() * 100
		-- local probs = {4.17, 8.33, 4.17, 0.05, 0.15} --锁甲, 铁甲, 金甲, 钻甲, 花冠
		local probs = {4.17, 8.33, 4.17, 0.05} --锁甲, 铁甲, 金甲, 钻甲
		
		-- for i=1, 5, 1 do
		for i=1, 4, 1 do
			sumprob = sumprob + probs[i]
			if r < sumprob then
				quality = i
				break 
			end
		end

		--野人出生佩戴花冠
		if quality == 5 then
			mob:addInitEquip(0, 11228)
		else
			local factor2 = 0.25
			-- for slot=0, 3, 1 do
			for slot=1, 3, 1 do
				if slot>0 and math.random()<factor2 then break end

				local itemid = 12200 + quality*10 + slot + 1
				mob:addInitEquip(slot, itemid)
			end
		end
	end
end

function F3101_Init(mob)
	mob:setCanPickUpLoot(true)
	MobAddRandomEquipments(mob)

	local factor = 0.01
	if math.random() < factor then
		if math.random(0,2) == 0 then
			mob:addInitEquip(5, 12003) --铁剑
		else
			mob:addInitEquip(5, 11003) --铁斧
		end
	end
end

function F3105_Init(mob)
	mob:setCanPickUpLoot(true)
	MobAddRandomEquipments(mob)
end

--庆典野人
function F3106_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanPassClosedWoodenDoors",true)
	actor:addAISavageStandSleep(1, 20, 40, 0.12);	 --白天睡觉，优先级，触发延迟1，触发延迟2（20tick一秒，不配默认50，100）
	--actor:setSunHurt(true)
	actor:addAiTaskSwimming(1)--4，游泳
	actor:addAiTaskTargetHurtee(1, true)--1，受到攻击后反击
	actor:addAiTaskBreakDoor(1)--0，破坏门
	actor:addAiTaskFollowDirection(2, 1.1)--寻找路径方块--3
	--actor:addAiTaskPanic(1, 3.25,0.0001)--1，逃跑
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1)--1，主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.0)--3，近战攻击
	--actor:addAiTaskMoveTowardsRestriction(4, 1.0)--1，找随机位置移动
	actor:addAiTaskWander(6, 1.0)--1
	actor:addAiTaskWatchClosest(7, 800)--2，看附近玩家
	actor:addAiTaskLookIdle(7)--3，到处看看
	
	actor:addAiTaskRide(3,3407, 3000)--7，骑狼，第二个参数要骑上的怪物id，第三个参数是距离单位厘米
	actor:addAiTaskKickAway(3, 3109, 200, 100);	--7，踢动物，第二个参数是怪物id,第三个参数是每tick触发这个ai的概率，第四个参数是力度
	
	actor:addAISavageSleep(1);--7，睡觉
	actor:addAIAttractBlock(2, 1200, 2, 1, 3); --3--喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是概率，第四个是走路速度，第5个是离篝火方块几格停下来
	actor:addAIWarning(1, 2, 200, 20);--警戒--1
	actor:addAiTaskTargetSpecificEntity(3, 3200, 10) --1攻击生物（新野人）
	actor:addAiTaskTargetSpecificEntity(3, 3201, 10) --1攻击生物（新野人猎手）
	actor:addAiTaskTargetSpecificEntity(3, 3202, 10) --1攻击生物（新野人萌宝）

	actor:addAiTaskAtk_TianGou(1, 0, true, 1.5)--3，近战攻击
end

--野人萌宝
function F3102_SetAi(actor)
	actor:addAiTaskSwimming(1)
	--actor:addAISavageStandSleep(0, 20 ,40);	 --白天睡觉
	actor:addAiTaskBreakDoor(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskAtk(2, 0, false, 1.0)
	--actor:addAiTaskMoveTowardsRestriction(4, 1.0)
	actor:addAiTaskWander(6, 1.0)
	actor:addAiTaskWatchClosest(7, 800)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTargetHurtee(1, true)
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1)
	
	actor:addAiTaskMakeTrouble(3, 239, 160, 1, 20, true)   -- 第二个参数是方块的id, 第三个是概率 ,第四个是速度。第五个是搜寻的范围，第五个是击碎还是使用  破坏西瓜
	actor:addAiTaskMakeTrouble(3, 229, 160, 1, 20, true)   -- 破坏小麦
	actor:addAiTaskMakeTrouble(3, 230, 160, 1, 20, true)   -- 破坏南瓜
	actor:addAiTaskMakeTrouble(3, 236, 160, 1, 20, true)   -- 破坏胡萝卜
	actor:addAiTaskMakeTrouble(3, 241, 160, 1, 20, true)   -- 破坏土豆
	actor:addAiTaskMakeTrouble(3, 724, 160, 1, 20, false)  -- 乱按开关
	actor:addAiTaskMakeTrouble(3, 715, 160, 1, 20, false)  -- 乱按木按钮
	actor:addAiTaskMakeTrouble(3, 716, 160, 1, 20, false)  -- 乱按石按钮

	actor:addAIAttractBlock(2, 1200, 2, 1, 3); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是概率，第四个是走路速度，第5个是离篝火方块几格停下来
	actor:addAISavageSleep(1);
	actor:addAIWarning(1, 2, 200, 20);--警戒
	actor:addAiTaskTargetSpecificEntity(3, 3200, 10) --攻击生物（新野人）
	actor:addAiTaskTargetSpecificEntity(3, 3201, 10) --攻击生物（新野人猎手）
	actor:addAiTaskTargetSpecificEntity(3, 3202, 10) --攻击生物（新野人萌宝）

	actor:addAiTaskAtk_TianGou(1, 0, true, 1.5)
end

function F3102_AttackEntityAsMob(mob, actor)
	if mob:getEquipItem(5) == 0 and mob:isBurning() then
		actor:sureFireBurnComponent()
		ActorComponentCallModule(actor,"FireBurnComponent","setFire",100,1)
	end
	
	return true;
end

function F3102_Init(mob)
end

--爆爆蛋
function F3109_SetAi(actor)
	actor:setAiInvulnerableProb(3);--新增ai 1/3概率闪避
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:setPathHide(1)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskBoomAtk(2) --爆炸攻击
	--actor:addAiTaskAtk(4, 0, false, 1.0)
	actor:addAiTaskWander(5, 0.8)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetNearest(1, 0, true, 0.0, 0, 1)
	actor:addAiTaskTargetHurtee(2, false)
	
	actor:addAILoveBlock(1, 834, 1, 1); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是概率，第四个是走路速度
	
end

function F3109_AttackEntityAsMob(mob, actor)
	return false;
end

function F3109_OnFall(mob, fall)
	local time = mob:getTimeSinceIgnited() + fall*1.5
	if time > 30 - 5  then
		time = 30 - 5
	end
	
	mob:setTimeSinceIgnited(time)
	
	return true
end

--蝙蝠
function F3107_SetAi(actor)
	actor:addAiTaskSwimming(1)
    
    -- 夜晚饥饿跟随（主要模式）
    actor:addAIHungryFollowPlayer(2, 1.2, 600, 80, 0.7)
    
    -- 飞行吸引跟随（辅助模式）  
    actor:addAIFlyAttract(3, 800, 400, 100, 2, 40)
    
    -- 饥饿状态表现
    actor:addAIHungryStatus(4, 150, 8)
    
    -- 其他AI行为...
    actor:addAiTaskTargetHurtee(5, false)  
    actor:addAIBatAttack(6, 1, 77, 1, 20)   
    actor:addAIBatIdle(7, 7)               
    actor:addAiTaskWatchClosest(8, 800)    
    actor:addAiTaskLookIdle(9)   
    
end

function  F3107_IsPotionApplicable(buffid)
	if buffid == 6 then
		return false
	end
	
	return true		
end

--虚空蝙蝠
function F3267_SetAi(actor)
	F3107_SetAi(actor)
end

function F3267_IsPotionApplicable(buffid)
	return F3107_IsPotionApplicable(buffid)
end

--洞穴蜘蛛
function F3108_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(2, 40.0, 200, 600)
	actor:addAiTaskAtk(3, 0, false, 1.0)
	actor:addAiTaskWander(4, 0.8)
	actor:addAiTaskWatchClosest(5, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetNearest(1, 0, true, 0.5)
	actor:addAiTaskTargetHurtee(2, false)

	actor:addAiTaskAtk_TianGou(1, 0, true, 1.0)
end

function  F3108_IsPotionApplicable(buffid)
	if buffid == 6 then
		return false
	end
	
	return true		
end

function F3108_AttackEntityAsMob(mob, actor)
	actor:addBuff(6, 1)
	return true;
end

--牛
function F3401_Init(mob)
end

function F3401_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	--actor:addAiTaskPanic(1, 2.0)
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiMate(2, 1.2, 1, 1, 822)
	--actor:addAiTaskMilking(3)
	actor:addAiLoggerHeads(4, 0.8, 12578)
	--actor:addAiTaskTempt(5, 1.25, 12500, false)--被野果吸引
	--actor:addAiTaskTempt(6, 1.25, 11226, false)--被杂草吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskFollowParent(6, 1.25)
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetHurtee(3, false, false)

end

--牛交互
function F3401_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12500, 3891, 3);
	local itemid = player:getCurToolID()
	--local ids = {11311};

	if itemid == 12500 then
    	return true
    end    
	
	return false
end

--驯服的牛
function F3891_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 2.0)
	actor:addAiMate(2, 1.2)
	--actor:addAiTaskMilking(3)
	actor:addAiLoggerHeads(4, 0.8, 12578)
	--actor:addAiTaskTempt(5, 1.25, 229, false)
	actor:addAiTaskTempt(6, 1.25, 11534, false)--被杂草吸引
	actor:addAiTaskFollowParent(6, 1.25)
	actor:addAiTaskWatchClosest(8, 600)
    actor:addAiTaskLookIdle(9)
end

-- 虚空沃沃兽
function F3261_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	--actor:addAiTaskPanic(1, 2.0)
	actor:addAiTaskAtk(4, 0, true, 2.0)
	-- actor:addAiMate(2, 1.2, 1, 1, 822)
	--actor:addAiTaskMilking(3)
	actor:addAiLoggerHeads(4, 0.8, 12578)
	--actor:addAiTaskTempt(5, 1.25, 12500, false)--被野果吸引
	--actor:addAiTaskTempt(6, 1.25, 11226, false)--被杂草吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskFollowParent(6, 1.25)
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetHurtee(3, false, false)

end

-- 虚空沃沃兽交互
function F3261_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12500, 3262, 3);
	local itemid = player:getCurToolID()

	if itemid == 12500 then
    	return true
    end    
	
	return false
end

--小牛
function F3812_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3812_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 2.0)
	actor:addAiMate(2, 1.0)
	actor:addAiTaskTempt(3, 1.25, 11534, false)
	actor:addAiTaskFollowParent(4, 1.25)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
end

function F3812_Interact(mob, player)
	--[[
	local itemid = player:getCurToolID()
	if  itemid == ITEM_BUCKET then
		player:shortcutItemUsed();
		player:playCurToolSound();
		local num = player:getBackPack():addItem(ITEM_BUCKET_MILK, 1)
		if num ~= 1 then
			player:dropItem(ITEM_BUCKET_MILK, 1)
		end
		return true
	end
	--]]
	
	local itemid = player:getCurToolID()
	if itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
		player:playCurToolSound()
		player:shortcutItemUsed()
		if math.random(1,4) == 4 then
			mob:setGrowingAge(-1)
		end
		return true
	end
		
	return false
end

--变异野生飞鸡
function F3421_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 2)
	actor:setOxygenNeed(false) -- 是否需要氧气
	
	actor:addAIMutateFly(4, 221, 300, 1.6)   --优先级 方块id 平均多少时间触发一次(220tick 20tick是一秒) 速度(2倍)
	actor:addAIMutateFly(4, 258, 300, 1.6)   
	actor:addAIMutateFly(4, 220, 300, 1.6)
	actor:addAIMutateFly(4, 255, 300, 1.6)
	actor:addAIMutateFly(4, 218, 300, 1.6)
	actor:addAiTaskWander(2, 1.0)
	actor:addAiMate(2, 0.5) --繁殖
	actor:addAiTaskWatchClosest(3, 600)
	actor:addAiTaskLookIdle(5)
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
    --actor:addAiTaskFlyLoveBlock(3, 218, 500, 1.2, 400); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是发生概率(500就是25秒)，第四个是走路
end
function F3421_Interact(mob, player)
	local itemid = player:getCurToolID()

	TameAnimal_Interact(mob, player, 12601, 3422, 10);
	if itemid == 12601 then
		return true
	end
	return false
end


--变异飞鸡驯服
function F3422_SetAi(actor)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 2)
	actor:addAiMate(2, 1.2, 1, 1, 1019)
	actor:setOxygenNeed(false) -- 是否需要氧气
	
    actor:addAiTaskTempt(3, 1.0, 12601, false)--被桃子吸引
	actor:addAiTaskFlyLoveBlock(3, 218, 500, 1.2, 400); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是发生概率(500就是25秒)，第四个是走路速度,第五个参数是每次ai执行的时间(tick)
end

--变异战斗鸡
function F3423_SetAi(actor)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:setOxygenNeed(false) -- 是否需要氧气
	
	actor:addAIPatrolOnBlock(3, 550, 100, 1); --优先级 方块id tick时间(多少时间搜寻一次方块 这个时间最好弄长一点) 1是速度
	actor:addAiMate(2, 1.2, 1, 1, 1019)
	actor:addAiTargetMutate(2,550,20,102001,false,16)  --优先级,方块id（站着的方块），搜寻待攻击怪物的tick时间，  buffid, 是否离开视野就不攻击(true或false)  搜寻长度(多少方块宽)
	actor:addAiTaskAtk( 0, 0, true, 1.4) --近战攻击 这个ai是以前就有的。 一旦生物找到目标 这个ai就会起作用
	actor:addAiTaskTempt(4, 1.25, 269, false)--被荧光草吸引
	actor:addAiTaskTempt(2, 1.25, 12589, false)--被荧光弹弓吸引
	actor:addAiTaskTempt(4, 1.25, 12591, false)--被大荧光球吸引
	actor:addAiTaskWander(3, 1.0)
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
	actor:addAiMate(2, 0.5) --繁殖
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引

	actor:addAiTaskTargetOnwnerHurter(2)    --主人攻击帮忙

end

--驯服战斗鸡
--function F3423_Interact(mob, player)
--    TameAnimal_Interact(mob, player, 12604, 3426, 2);
--	local itemid = player:getCurToolID()
	
--	if itemid == 12604 then
--    	return true
--    end    
--
--	return false
--end

--变异尖叫鸡
function F3424_SetAi(actor)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:setOxygenNeed(false) -- 是否需要氧气
	
	--actor:addAiTaskPanic(1, 1.4) 这个其实还有第三个参数。
	actor:addAITargetScream(1,1000)--第一个参数优先级 第二个是固定时间尖叫的每次叫的时间间隔
	actor:addAiTaskTempt(2, 1.4, 1020, false)--被鸡饲料吸引
	--actor:addAITargetPanicBuff(3, 1.8, 102001) --荧光buff逃跑ai

end


--鸡
function F3400_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.4)
	actor:addAITargetPanicBuff(3, 1.8, 71001) --鸡毒buff逃跑
	
	-- actor:addAiMate(2, 1.0, 1, 1, 1048) --孵化改为繁殖
	--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
	--actor:addAiTaskTempt(3, 1.0, 234, true)--被水稻吸引
	actor:addAiTaskLayEggInNest(2, 1185, 12052, 80, 1185) --80个tick搜寻一下最近的嘟嘟鸟蛋窝跟普通的窝有没有合适下蛋的
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskFollowParent(4, 1.1)
	--actor:addAiTaskHatch(5, 1185, 180, 3813) --孵化40个tick, 2秒
	actor:addAiTaskSleep(6, 4000, 5*20, 0, 1, 2) --游戏里面5小时, 睡100秒
	actor:addAiTaskEatFeedBlock(7, 1020, 600) --吃40个tick, 2秒
	--1019 普通的窝 1185 嘟嘟鸟蛋窝   1019的找寻逻辑写在1185的判断里面
	actor:addAiTaskWander(11, 1.0)

	actor:addAiThrob(11, 480, 2.0)
	--actor:addAiClean(11, 120, 10.0) //模型不支持
	------------------------------------
	actor:addAiTaskWatchClosest(12, 600)
	actor:addAiTaskLookIdle(13)
	--actor:addAIEatThenMutate(1, 466, 200, 1.5, 20, 100, 71001, 200)  --优先级，被吃的方块id，平均多少时间触发一次，靠近方块的速度，吃多少时间， 倒地多少时间 ,buffid,buff时间
	actor:addAIEatThenMutate(1, 467, 200, 1.5, 20, 100, 71001, 200)
	actor:addAIEatThenMutate(1, 468, 200, 1.5, 20, 100, 71001, 200)
	
end



function F3400_OnFall(mob, fall)
	return false
end

--小鸡
function F3813_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.4)
	-- actor:addAiTaskLayEggInNest(2, 1019, 0, 80) 
	actor:addAiTaskLayEggInNest(2, 1185, 12052, 80, 1185) 
	--actor:addAiTaskHatch(2, 1185, 180, 3813) 
	actor:addAiTaskTempt(3, 1.0, 234, false)--被水稻吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskFollowParent(4, 1.1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
	
end

--小飞鸡
function F3893_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.4)
	actor:addAiMate(2, 1.0)
	actor:addAiTaskTempt(3, 1.0, 255, false)
	actor:addAiTaskFollowParent(4, 1.1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
end

--小战斗鸡
function F3894_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.4)
	actor:addAiMate(2, 1.0)
	actor:addAiTaskTempt(3, 1.0, 220, false)
	actor:addAiTaskFollowParent(4, 1.1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
end

--小猴子
function F3895_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(4, 1.4)
	actor:addAiMate(2, 1.0)
	actor:addAiTaskTempt(3, 1.0, 12601, false)
	actor:addAiTaskFollowParent(4, 1.1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
end

function F3813_OnFall(mob, fall)
	return false
end

--羊
function F3403_Interact(mob, player)
	local itemid = player:getCurToolID()

	if not GetClientInfo():isMobile() then
		if itemid == 11056 and mob:getSheared() then 
			return true;
		end
	end

    local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)
	if itemid == ITEM_SMALL_GLASS_BOTTLE then
		if mob:getFlagBit(ACTORFLAG_AI_MILKING) then
			if mob:getMilkingTimes() > 0 then
				player:playCurToolSound()
				player:shortcutItemUsed()
				mob:setMilkingTimes(mob:getMilkingTimes()-1)
				local num = player:getBackPack():addItem(ITEM_BOTTLED_MILK, 1)
				mob:setQuiteTick(0)
				mob:setFlagBit(ACTORFLAG_AI_MILKING, false);
				if num ~= 1 then
					ActorComponentCallModule(player,"DropItemComponent","dropItem",ITEM_BOTTLED_MILK, 1)
				end
			end
		else
			ActorComponentCallModule(mob,"EffectComponent","playBodyEffect",BODYFX_AI_NEEDREEDS)
		end
		return true
	--[[elseif itemid == 11400 then		-- 小麦
		local result = ids[math.random(1)];
		if math.random(10)==1 then
			mob:dropItem(result, 1);
			mob:playSound("misc.fart", 1.0, 1.0);
		end	
		player:shortcutItemUsed();
		return true
		
		--mob:playInteractionEffect();
		--]]
	elseif itemid == 11056 and not mob:getSheared() then
		-- local dropid = curblockid
		-- local dropnum = 1

  --       if userdata > 0 then
  --           dropid = GetDefaultBlockId(curblockid)
  --       end
		-- mob:dropItem(dropid, dropnum)
		-- player:shortcutItemUsed();
		-- player:playCurToolSound();
		-- mob:setSheared(true)
		-- return true
		-- 此处屏蔽剪刀功能， code-by： fukaijian
		return false 
	elseif itemid>=11501 and itemid<=11515 and curblockid-600 ~= itemid-11500 then
		mob:setColor(GetColorFromBlockInfo(600+itemid-11500, 0))
		player:shortcutItemUsed()
		return true
	end
   --[[	
	local ids = {11311};

	local itemid = player:getCurToolID()
	if itemid == 11400 then
		local result = ids[math.random(1)];
		if math.random(10)==1 then
			mob:dropItem(result, 1);
			mob:playSound("misc.fart", 1.0, 1.0);
		end	
		player:shortcutItemUsed();
		
		--mob:playInteractionEffect();
		return true
	end
	--]]

	
	return false
end	

function F3403_Init(mob)
	if mob and mob.getBody and mob:getBody().setHeadRotRange then--加个保护
		mob:getBody():setHeadRotRange(45)
	end

	local rand = math.random(0, 100)
	if rand < 5 then
		mob:setColor(GetColorFromBlockInfo(600+15, 0))    -- 15号羊毛
	elseif rand < 10 then
		mob:setColor(GetColorFromBlockInfo(600+7, 0))	
	elseif rand < 15 then
		mob:setColor(GetColorFromBlockInfo(600+8, 0))
	elseif rand < 18 then	
		mob:setColor(GetColorFromBlockInfo(600+12, 0))
	else
		if  0 == math.random(0, 500) then
			mob:setColor(GetColorFromBlockInfo(600+6, 0))
		else
			mob:setColor(GetColorFromBlockInfo(600+0, 0))
		end
	end
	
end

function F3403_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.25)
	actor:addAiMate(2, 1.0, 1, 1, 822)
	actor:addAiTaskMilking(3) --新增羊可以挤奶
	--actor:addAiTaskTempt(4, 1.1, 229, false)
	--actor:addAiTaskTempt(4, 1.25, 11226, false)--被一把杂草吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	--actor:addAiTaskEatGrass(6)  角鹿吃草逻辑删除
	actor:addAiTaskEatLeaf(6,50, 200)  --后面那两个参数是每tick的执行概率，分别对应幼儿和成年
	--actor:addAITaskEatFlower(6, 50, 200) --后面那两个参数是每tick的执行概率，分别对应幼儿和成年
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
end

--虚空角鹿
function F3246_Init(mob)
	if mob and mob.getBody and mob:getBody().setHeadRotRange then--加个保护
		mob:getBody():setHeadRotRange(45)
	end
end

function F3246_Interact(mob, player)
	local itemid = player:getCurToolID()

	TameAnimal_Interact(mob, player, 200411, 3247, 1)
	if itemid == 200411 then
		return true
	end

	if not GetClientInfo():isMobile() then
		if itemid == 11056 and mob:getSheared() then 
			return true;
		end
	end

    local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)
	if itemid == ITEM_SMALL_GLASS_BOTTLE then
		if mob:getFlagBit(ACTORFLAG_AI_MILKING) then
			if mob:getMilkingTimes() > 0 then
				local item_milk = 200412 -- 虚空鲜奶 200412
				player:playCurToolSound()
				player:shortcutItemUsed()
				mob:setMilkingTimes(mob:getMilkingTimes()-1)

				local num = player:getBackPack():addItem(item_milk, 1)
				mob:setQuiteTick(0)
				mob:setFlagBit(ACTORFLAG_AI_MILKING, false);
				if num ~= 1 then
					ActorComponentCallModule(player,"DropItemComponent", "dropItem", item_milk, 1) 
				end
			end
		else
			ActorComponentCallModule(mob,"EffectComponent","playBodyEffect",BODYFX_AI_NEEDREEDS)
		end

		return true
	end
	
	return false
end	

function F3246_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.25)
	-- actor:addAiMate(2, 1.0, 1, 1, 822)
	actor:addAiTaskMilking(3) --新增羊可以挤奶
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskEatLeaf(6,50, 200)  --后面那两个参数是每tick的执行概率，分别对应幼儿和成年
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
end

--驯服的虚空角鹿
function F3247_Init(mob)
	F3246_Init(mob)
end

function F3247_Interact(mob, player)
	local itemid = player:getCurToolID()

	if not GetClientInfo():isMobile() then
		if itemid == 11056 and mob:getSheared() then 
			return true;
		end
	end

    local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)
	if itemid == ITEM_SMALL_GLASS_BOTTLE then
		if mob:getFlagBit(ACTORFLAG_AI_MILKING) then
			if mob:getMilkingTimes() > 0 then
				local item_milk = 200412 -- 虚空鲜奶 200412
				player:playCurToolSound()
				player:shortcutItemUsed()
				mob:setMilkingTimes(mob:getMilkingTimes()-1)

				local num = player:getBackPack():addItem(item_milk, 1)
				mob:setQuiteTick(0)
				mob:setFlagBit(ACTORFLAG_AI_MILKING, false);
				if num ~= 1 then
					ActorComponentCallModule(player,"DropItemComponent", "dropItem", item_milk, 1) 
				end
			end
		else
			ActorComponentCallModule(mob,"EffectComponent","playBodyEffect",BODYFX_AI_NEEDREEDS)
		end

		return true
	end
	
	return false
end

function F3247_SetAi(actor)
	F3246_SetAi(actor)
end

--小羊
function F3814_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3814_Interact(mob, player)
	local itemid = player:getCurToolID()	
	if itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
		player:playCurToolSound()
		player:shortcutItemUsed()
		if math.random(1,5) == 4 then
			mob:setGrowingAge(-1)
		end
		return true
	end
	
	return false
end	

function F3814_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.25)
	actor:addAiMate(2, 1.0)
--	actor:addAiTaskTempt(4, 1.25, 11226, false)--被一把杂草吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskFollowParent(5, 1.1)
	--actor:addAiTaskEatGrass(6)
	actor:addAiTaskWander(7, 1.0)
	actor:addAiTaskWatchClosest(8, 600)
	actor:addAiTaskLookIdle(9)
end

--地心人
function F3501_Init(mob)
end


function F3501_SetAi(actor)
	actor:addAIHoldMonster(0, 3102, 800)
	actor:addAIHoldMonster(0, 3109, 800)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(1, true) --被攻击后反击
	--1.优先级2.搜寻半径3.中断半径
	actor:addAiTaskEarthCoreManRain(2,8,8)
	--1：优先级2.被偷取体力者疲劳时停止偷窃技能cd时间 3.开始偷取范围4.偷取体力范围5.每次偷取的体力6.每次偷取的间隔(单位:s)7.亮度8.是否需要阳光直射
	actor:addAiTaskEarthCoreManSteal(3, 120, 400, 1000, -2, 1, 0.12,true)
	actor:addAiTaskTargetNearest(4, 0, true, 0.0, 0, 1)
	actor:addAiTaskFlyAttack(4, true, 1.0, 0, 0)
	--1.优先级 2.冲击范围min 3.冲击方位max 4.冲击概率
	actor:addAiTaskEarthCoreManLash(4,300,1000,30)
	actor:addAiTaskRandFly(5, 25, 16, 2000)
	actor:addAiTaskLookIdle(5)
end

--恶霸屠夫（野生）
function F3120_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskAtk(1, 0, true, 1.2) --近战攻击
	actor:addAiTaskTargetHurtee(1, true) --被攻击后反击
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近玩家
	actor:addAiTaskTempt(3, 1.0, 11204, false)

end

function F3120_Interact(mob, player)
	--交易获得商品
	local ids = {12522,12518,12526,12516,12527};

	local itemid = player:getCurToolID()
	if itemid == 11204 then
		local result = ids[math.random(5)];
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",result, 3)
		player:shortcutItemUsed();
		ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3120.deal", 1.0, 1.0)
		mob:playInteractionEffect();
	end
	TameAnimal_Interact(mob, player, 11204, 3122, 1);
	if itemid == 11204 then
		return true
	end
	return false
end	


--恶霸屠夫（驯服）
function F3122_SetAi(actor)
	actor:addAiTaskTransfiguration(0, 3120, 6000); --变身回野生
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskAtk(1, 0, true, 1.2) --近战攻击
	actor:addAiTaskTargetHurtee(1, true) --被攻击后反击
	actor:addAiTaskTargetSpecificEntity(2, 3402, 5) --攻击生物（猪）
	actor:addAiTaskTargetSpecificEntity(2, 3401, 5) --攻击生物（牛）
	actor:addAiTaskTargetSpecificEntity(2, 3403, 5) --攻击生物（羊）
	actor:addAiTaskTargetSpecificEntity(2, 3400, 5) --攻击生物（鸡）
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近玩家
	actor:addAiTaskTempt(3, 1.0, 11204, false)
end

function F3122_Interact(mob, player)
	--交易获得商品
	local ids = {12522,12518,12516,12527};

	local itemid = player:getCurToolID()
	if itemid == 11204 then
		local result = ids[math.random(5)];
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",result, 3)
		player:shortcutItemUsed();
		ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3120.deal", 1.0, 1.0)
		--mob:playInteractionEffect();
	end
	
	return false
end	



--稻草人
function F3121_SetAi(actor)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTempt(3, 1.0, 300, false)
end

--地底人
function F3130_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskBreakDoor(1)
	actor:addAiTaskAtk(2, 0, false, 1.0)
	--actor:addAiTaskMoveTowardsRestriction(4, 1.0)
	actor:addAiTaskWander(6, 1.0)
	actor:addAiTaskWatchClosest(7, 800)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTargetHurtee(1, true)
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:setImmuneToFire(2)
	--免疫火焰伤害

end

--硫磺箭手
function F3131_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskRestrictSun(2)
	actor:addAiTaskFleeSun(4, 1.0)
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(3, 1.0, 20, 60, 1500,15051,1,100001,1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:setImmuneToFire(2)
	actor:addAiTaskTargetSpecificEntity(2, 3121, 10) --攻击生物（稻草人）
end

--混乱箭手
function F3132_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskRestrictSun(2)
	actor:addAiTaskFleeSun(4, 1.0)
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(3, 1.0, 20, 60, 1500,15068,1,201002,3)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:setImmuneToFire(2)
	actor:addAiTaskTargetSpecificEntity(2, 3121, 10) --攻击生物（稻草人）
end

--（活动）灰色三角龙
function F3170_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）棕色三角龙
function F3171_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）蓝色三角龙
function F3172_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）长剑三角龙BOSS
function F3173_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）灰色腕龙
function F3174_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）黄色腕龙
function F3175_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）绿色腕龙
function F3176_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）泰坦腕龙BOSS
function F3177_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）红色翼龙
function F3178_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）灰色翼龙
function F3179_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）紫色翼龙
function F3180_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）风神翼龙BOSS
function F3181_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）绿色副栉龙
function F3182_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）青色副栉龙
function F3183_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）红色副栉龙
function F3184_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）黑色迅猛龙
function F3185_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）青色迅猛龙
function F3186_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）紫色迅猛龙
function F3187_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）武装迅猛龙
function F3188_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）棕色双冠龙
function F3189_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）灰色双冠龙
function F3190_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）绿色剑龙
function F3191_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）蓝色剑龙
function F3192_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）霸王龙
function F3193_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）狂躁霸王龙
function F3194_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）残暴霸王龙
function F3195_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）武装霸王龙
function F3196_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--（活动）机械霸王龙
function F3197_SetAi(actor)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskTargetHurtee(4, true) --自己被打反击
end

--企鹅
function F3409_SetAi(actor)
	ActorComponentCallModule(actor, "ClientActorFuncWrapper","setCanFly", true)
	--计数搜索范围,总数,聚集的速度,结束后的cd时间,聚集时离中心点距离,跳舞的时间
	actor:addAiTaskClosestDance(0, 30, 3, 2, 100*20, 2, 10*20)
	actor:addAiTaskFollowDirection(2, 1.1)--寻找路径方块
	actor:addAiTaskPanic(2, 1.4)
	--actor:addAiMate(3, 1.0, 1, 1, 1019)
	--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
--	actor:addAiTaskTempt(4, 1.0, 12602, false)--被球球果吸引
	actor:addAiTaskFollowParent(5, 1.1)
	--actor:addAiTaskLayEggs(6, 6000, "", 12053)
	actor:addAiTaskLayEggInNest(2, 1019, 12053, 80, 150028) 

	actor:addAiTaskJumpGlissadeAmphibious(7, 50--[[触发频率，每X秒触发一次]], 70--[[水中跃起概率 %X]], 10--[[冰面滑行概率 %X]]) --新AI by 李元星
	actor:addAiTaskWanderAmphibious(8, 1, 100 --[[陆地闲逛概率 X分之一]]) --新AI by 李元星

	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(10)
end

function F3409_OnFall(mob, fall)
	return false
end

--小企鹅
function F3819_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 1.4)
	actor:addAiMate(2, 1.0)
	--actor:addAiTaskTempt(3, 1.0, 12602, false)
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiTaskFollowParent(4, 1.1)
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)
end

function F3819_OnFall(mob, fall)
	return false
end

--野生团子
function F3414_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(1, true) --被攻击后反击
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(4,1.0,90,70,1500,15053,0.8,42003,1)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskTempt(3, 1.0, 12576, false) --食物吸引
	actor:addAiTaskWatchClosest(6, 600) --看附近玩家
	actor:addAiMate(2, 0.5)
	actor:addAiTaskBeg(7, 12500, 600) --喜欢食物
end
function F3414_Interact(mob, player)
	local itemid = player:getCurToolID()
	TameAnimal_Interact(mob, player, 12576, 3415, 6);
	if itemid == 12576 then
		return true
	end
	return false
end

--团子
function F3415_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.4) --受伤逃跑
	actor:addAiMate(2, 0.5) --繁殖
	actor:addAiTaskTempt(3, 1.0, 12500, false)
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskBeg(7, 12500, 600) --喜欢食物
end

------------------------------------------------------------------------------------
-- 虚空团子
function F3244_SetAi(actor)
    ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(1, true) --被攻击后反击
	--actor:addAiTaskArrowAttack(4, 1.0, 20, 60, 1500)
	actor:addAiTaskProjectileAttack(4,1.0,90,70,1500,15053,0.8,42003,1)
	actor:addAiTaskWander(4, 1.0, 60) --闲逛
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskTempt(3, 1.0, 12576, false) --食物吸引
	actor:addAiTaskWatchClosest(6, 600) --看附近玩家
	-- actor:addAiMate(2, 0.5)
	actor:addAiTaskBeg(7, 12500, 600) --喜欢食物

    -- 虚空团子创建虚空漩涡 参数：优先级，触发概率
    actor:addAICreateVacantVortex(2, 10)
end

-- 虚空团子被喂养
function F3244_Interact(mob, player)
	local itemid = player:getCurToolID()
	TameAnimal_Interact(mob, player, 12576, 3245, 6)
	if itemid == 12576 then
		return true
	end
	return false
end
------------------------------------------------------------------------------------

--北极熊
function F3405_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.4) --受伤逃跑
	actor:addAiTaskAtk(1, 0, true, 1.2) --近战攻击
	actor:addAiMate(2, 0.5, 1, 1, 822) --繁殖
	actor:addAiTaskTempt(3, 1.0, 12546, false)
	actor:addAiTaskTempt(4, 1.0, 12524, false)
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskBeg(7, 12524, 600) --喜欢食物

end


--迅猛龙
function F3406_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskAtk(1, 0, true, 1.5) --近战攻击
	actor:addAiTaskPanic(1, 1.4) --受伤逃跑
	actor:addAiMate(2, 1, 1, 1, 822) --繁殖
	actor:addAiTaskTempt(3, 1.0, 12558, false)--驯服
	actor:addAiTaskTempt(4, 1.0, 12543, false)
	actor:addAiTaskLookIdle(5, 60) --左右看看
	actor:addAiTaskWatchClosest(6, 600) --看附近的玩家
	actor:addAiTaskBeg(7, 12543, 600) --喜欢食物

end


--鸵鸟
function F3410_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.4) --受伤逃跑
	actor:addAiMate(2, 1.0, 1, 1)
	actor:addAiTaskTempt(3, 1.0, 11321, false)
	actor:addAiTaskTempt(4, 1.0, 12508, false)
	actor:addAiTaskWatchClosest(5, 600) --看附近的玩家
	actor:addAiTaskLookIdle(6, 60) --左右看看
	actor:addAiTaskBeg(7, 12508, 600) --喜欢食物
end

-- ashcraft : 熊 主动攻击玩家
function F3412_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块进行巡逻
	
	-- 攻击相关AI
	actor:addAiTaskTargetHurtee(1, true) --受到攻击后反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1) --主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.2) --近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0, 60) --随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 600) --观察附近玩家
	actor:addAiTaskLookIdle(6, 60) --左右张望
	
	-- 警戒系统
	actor:addAIWarning(1, 2, 200, 20) --警戒系统，发现敌人时提醒附近同伴
end

-- ashcraft : 狼 主动攻击玩家
function F3425_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(1) --游泳
	actor:addAiTaskFollowDirection(1, 1.1) --寻找路径方块进行巡逻
	
	-- 攻击相关AI
	actor:addAiTaskTargetHurtee(1, true) --受到攻击后反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1) --主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.2) --近战攻击
	
	-- 巡逻和观察AI
	actor:addAiTaskWander(5, 1.0, 60) --随机游荡巡逻
	actor:addAiTaskWatchClosest(6, 600) --观察附近玩家
	actor:addAiTaskLookIdle(6, 60) --左右张望
	
	-- 警戒系统
	actor:addAIWarning(1, 2, 200, 20) --警戒系统，发现敌人时提醒附近同伴
end

-- function F3412_Interact(mob, player)
-- 	local itemid = player:getCurToolID()
-- 	TameAnimal_Interact(mob, player, 12520, 3405, 20);
-- 	TameAnimal_Interact(mob, player, 12703, 3405, 20);
-- 	TameAnimal_Interact(mob, player, 12704, 3405, 20);
-- 	if itemid == 12520 or itemid == 12703 or itemid == 12704 then
-- 		return true
-- 	end
-- 	return false
-- end

--野生迅猛龙
function F3413_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.4, 0.4) --受伤逃跑
	actor:addAiTaskTargetHurtee(2, true) --受到伤害反击
	actor:addAiLeapAtTarget(3, 50.0, 200, 500) --跳跃攻击
	actor:addAiTaskTargetNearest(3, 0, true, 0.0, 0.8) --主动攻击玩家
	actor:addAiTaskAtk(3, 0, true, 1.2) --近战攻击
	actor:addAiTaskTargetNonTamed(4, 3400, 200) --杀动物
	actor:addAiTaskTargetNonTamed(4, 3401, 200) --杀动物
	actor:addAiTaskTargetNonTamed(4, 3402, 200) --杀动物
	actor:addAiTaskTargetNonTamed(4, 3403, 200) --杀动物
	actor:addAiTaskWander(5, 1.0, 40) --闲逛
	actor:addAiTaskLookIdle(6, 60) --左右看看
	actor:addAiTaskWatchClosest(7, 1800) --看附近玩家
	actor:addAiTaskBeg(8, 12543, 600) --喜欢食物
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiMate(2, 1, 1, 1, 822) --繁殖
end

function F3413_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12543, 3406, 20);
	local itemid = player:getCurToolID()
	if itemid == 12543 then
		return true
	end
	return false
end

-- --虚空迅猛龙
-- function F3265_SetAi(actor)
-- 	F3413_SetAi(actor)
-- end

-- function F3265_Interact(mob, player)
-- 	return F3413_Interact(mob, player)
-- end

--野生鸵鸟
--function F3411_SetAi(actor)
--	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
--	actor:addAiTaskSwimming(0) --游泳
--	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
--	actor:addAiTaskPanic(1, 1.4) --受伤逃跑
--	actor:addAiMate(2, 1.0) --繁殖
--	actor:addAiTaskWander(2, 1.4, 20) --闲逛
--	actor:addAiTaskLookIdle(3, 60) --左右看看
--	actor:addAiTaskWatchClosest(4, 600) --看附近玩家
--	actor:addAiTaskBeg(7, 12508, 600) --喜欢食物
--	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
--	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
--end

function F3411_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12508, 3410, 15);
	local itemid = player:getCurToolID()
	if itemid == 12508 then
		return true
	end
	return false
end



--小北极熊
function F3820_Init(mob)
	--mob:setGrowingAge(-24600)
end

function F3820_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskPanic(2, 1.4)
	actor:addAiTaskTempt(3, 1.2, 12524, false)
	actor:addAiTaskFollowParent(4, 1.2)
	actor:addAiTaskWander(5, 1.0, 60)
	actor:addAiTaskWatchClosest(5, 600)
	actor:addAiTaskLookIdle(5, 60)
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	
end

function F3820_Interact(mob, player)
	local itemid = player:getCurToolID()	
	if itemid == 12509 and not mob:isAdult() then		-- 瓶装牛奶
		player:playCurToolSound()
		player:shortcutItemUsed()
		if math.random(1,5) == 4 then
			mob:setGrowingAge(-1)
		end
		return true
	end
	
	return false
end	

--小迅猛龙
function F3818_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskPanic(1, 1.4)
	actor:addAiTaskTempt(2, 1.2, 12543, false)--被烤鸡全家桶吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiTaskFollowParent(3, 1.2)
	actor:addAiTaskWander(4, 1.0, 60)
	actor:addAiTaskWatchClosest(4, 600)
	actor:addAiTaskLookIdle(4, 60)

end

--小鸵鸟
function F3817_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskPanic(2, 1.4)
	actor:addAiTaskTempt(3, 1.2, 12508, false)--被方西瓜片吸引
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引
	actor:addAiTaskFollowParent(4, 1.2)
	actor:addAiTaskWander(5, 1.0, 60)
	actor:addAiTaskWatchClosest(5, 600)
	actor:addAiTaskLookIdle(5, 60)
end

--化石龙初阶
function F3430_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskTempt(2, 1.2, 11302, false)
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--化石龙Ⅰ
function F3431_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskTargetHurtee(1, true) --受到伤害反击
	actor:addAiLeapAtTarget(2, 50.0, 200, 500) --跳跃攻击
	actor:addAiTaskAtk(3, 0, true, 1.2) --近战攻击
	actor:addAiTaskTempt(4, 1.2, 11302, false)
	actor:addAiTaskWatchClosest(5, 600) --看附近的玩家
	actor:addAiTaskLookIdle(6, 60) --左右看看

end

--化石龙Ⅱ
function F3432_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskTargetHurtee(1, true) --受到伤害反击
	actor:addAiLeapAtTarget(2, 50.0, 200, 500) --跳跃攻击
	actor:addAiTaskAtk(3, 0, true, 1.2) --近战攻击
	actor:addAiTaskTempt(4, 1.2, 11302, false)
	actor:addAiTaskWatchClosest(5, 600) --看附近的玩家
	actor:addAiTaskLookIdle(6, 60) --左右看看

end

--普通麒麟
function F3436_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--麒麟
function F3437_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--祥瑞麒麟
function F3438_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end


--飞鼠初阶
function F3433_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskTempt(2, 1.2, 12503, false)
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--飞鼠1
function F3434_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskTempt(2, 1.2, 12503, false)
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--飞鼠2
function F3435_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskTempt(2, 1.2, 12503, false)
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end


--灯笼鱼
function F3600_SetAi(actor)
	actor:addAiTaskRandSwim(4, 50,0,0);--闲
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskFishAttack(3, true, 1.5);
	actor:addAiMate(2, 1.0,1,1,246) --繁殖 改为在海带中繁殖 （优先级，速度，int minbabies, int maxbabies，在哪繁殖
	--第二个参数为1表示有喜欢吃的才beg
	actor:addAiTaskFishBeg(3, 1, 800, false);
	actor:addAiTaskTargetHurtee(1, true)--受到伤害反击
	actor:addAiTaskTargetNearest(4, 0, true, 0.0)--主动攻击玩家
	--actor:addAiTaskTargetNonTamed(3, 0, 5) --攻击所有生物  （优先级，攻击生物id，id=0表示攻击除同类外的所有生物，每tick攻击概率
end

function F3600_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",true)
end

function F3600_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		--if math.random(0,10) < 8  then
			player:shortcutItemUsed();
		 	player:playCurToolSound();
			mob:dropItem(13600, 1);
			mob:ClearMob();
			return true;
		--end
	end
	
	return false;
end	


--深海鱼
function F3602_SetAi(actor)
	actor:addAiTaskRandSwim(2, 100,4200,6200);--优先级 隔多久tick找下一个闲游点  最低闲游高度 最高闲游高度
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskFishAttack(1, true, 1.5);
	actor:addAiTaskTargetHurtee(1, true)--受到伤害反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)--主动攻击玩
	actor:addAiTaskTargetNonTamed(3, 3623, 200) --攻击海马  （优先级，攻击生物id，id=0表示攻击除同类外的所有生物，每tick攻击概率
	actor:addAiTaskTargetNonTamed(3, 3624, 200) --攻击小海马  （优先级，攻击生物id，id=0表示攻击除同类外的所有生物，每tick攻击概率
end

function F3602_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3602_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13602, 1)
		mob:ClearMob();
		return true;
	end
	
 	--TameAnimal_Interact(mob, player, 12524, 3405, 20);
	return false;
end	

--白色小鱼
function F3604_SetAi(actor)
	actor:addAiTaskRandSwim(2, 25,0,0);
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskFearPlayer(2, 400, 2);
	--actor:addAiTaskFishBeg(1, -1, 800, false);
	actor:addAiTaskTargetHurtee(1, true);
	actor:addAIFishSwarm(2, 25);
end

function F3604_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3604_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13604, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--橙色小鱼
function F3605_SetAi(actor)
	actor:addAiTaskRandSwim(2, 25,0,0);
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3605_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3605_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13605, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--洋红小鱼
function F3606_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3606_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3606_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13606, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--淡蓝小鱼
function F3607_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3607_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3607_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13607, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--黄色小鱼
function F3608_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3608_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3608_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13608, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--黄绿色小鱼
function F3609_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3609_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3609_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13609, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--粉色小鱼
function F3610_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3610_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3610_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13610, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--灰色小鱼
function F3611_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3611_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3611_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13611, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--淡灰色小鱼
function F3612_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3612_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3612_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13612, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--青色小鱼
function F3613_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3613_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3613_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13613, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end

--紫色小鱼
function F3614_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3614_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3614_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13614, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--蓝色小鱼
function F3615_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3615_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3615_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13615, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end	

--棕色小鱼
function F3616_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3616_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3616_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13616, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end		

--绿色小鱼
function F3617_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3617_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3617_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13617, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end

--红色小鱼
function F3618_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3618_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3618_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13618, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end

--黑色小鱼
function F3619_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishSwarm(2, 25);
end

function F3619_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3619_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11058 then
		player:shortcutItemUsed();
	    player:playCurToolSound();
		ActorComponentCallModule(mob,"DropItemComponent","dropItem",13619, 1)
		mob:ClearMob();
		return true;
	end
	
	return false;
end



--灯笼鱼苗
function F3850_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(2, 25,0,0);
	actor:addAiTaskFishBeg(1, 1, 800, false);
end

function F3850_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

--水母
function F3626_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(3, 25,5000,6200); --闲游
	actor:addAiTaskFishAttack(2, true, 2); --发起攻击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAIFishSwimToSurface(1, 25);
    actor:addAiTaskTargetHurtee(2, true)--受到伤害反击
end

--虚空水母
function F3263_SetAi(actor)
	F3626_SetAi(actor)
end 

--飞鱼
function F3627_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(3, 25,5200,6200); --闲游
	actor:addAiTaskFearPlayer(2, 400, 2);
	actor:addAIFishFly(1, 25,500,0.2,0.2);
end

--鲨鱼
function F3628_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanSwimming",true)
	actor:addAiTaskRandSwim(5, 25,0,0);
	actor:addAiTaskFishAttack(2, true, 1.5);
	actor:addAiTaskTargetHurtee(1, true)--受到伤害反击
	actor:addAIFishHoveringAround(3, 1);
end

--虚空鯊魚
function F3266_SetAi(actor)
	F3628_SetAi(actor)
end

--陆行鸟1
function F3439_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--陆行鸟2
function F3440_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--陆行鸟3
function F3441_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--猫咪坐骑1
function F3454_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--猫咪坐骑2
function F3455_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--猫咪坐骑3
function F3456_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--天马坐骑1
function F3457_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--天马坐骑2
function F3458_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--天马坐骑3
function F3459_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--仓鼠坐骑1
function F3460_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--仓鼠坐骑2
function F3461_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--仓鼠坐骑3
function F3462_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--魔龙坐骑1
function F3469_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--魔龙坐骑2
function F3470_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--魔龙坐骑3
function F3471_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--月亮坐骑1
function F3478_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--月亮坐骑2
function F3479_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--月亮坐骑3
function F3480_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--圣诞坐骑1
function F3483_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--圣诞坐骑2
function F3484_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end

--圣诞坐骑3
function F3485_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--缤纷幻想1
function F3486_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--缤纷幻想2
function F3487_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--福牛犇犇1
function F3488_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--福牛犇犇2
function F3489_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--巨鲸1
function F3490_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--巨鲸2
function F3491_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--巨鲸3
function F3492_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--莲花1
function F3495_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--莲花2
function F3496_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--莲花3
function F3497_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--九色鹿0
function F4500_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--九色鹿1
function F4501_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--九色鹿2
function F4502_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--九色鹿3
function F4503_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	actor:addAiTaskPanic(1, 1.2) --受伤逃跑
	actor:addAiTaskWatchClosest(3, 600) --看附近的玩家
	actor:addAiTaskLookIdle(4, 60) --左右看看
end
--野生熊猫
function F3416_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSwimming(1)
	actor:setAiInvulnerableProb(1);--新增ai 1/1概率闪避
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	actor:addAITaskSpecialAct(4, 300, 200, ACTORFLAG_AI_EAT)  ----发生概率，动作执行时间，动作类型
	actor:addAiTaskBeg(7, 251, 800)	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskTempt(5, 1.0, 11534, false) --食物吸引
	actor:addAiTaskWander(8, 1.0)
	actor:addAiTaskWatchClosest(9, 600)	--看附近的玩家
	actor:addAiTaskLookIdle(9)--左右看看
	actor:addAITaskFearItem(0, 2, 12526, 600);
end

function F3416_Interact(mob, player)
	local itemid = player:getCurToolID()
	if itemid == 12526 then
		mob:addAITaskDissolvedByItem(0, 2, 30) 
		return true;
	elseif(itemid == 11311)then--熊猫与便便交互消失
		player:shortcutItemUsed();
		mob:setPandaRunAwayTick(120)
		return true
	else
		TameAnimal_Interact(mob, player, 251, 3417, 30);  --驯服物品 驯服后变成的生物 概率
		if itemid == 251 then
			return true
		end
		return false
	end
end

--熊猫
function F3417_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAITaskToppleOver(0)
	actor:setAiInvulnerableProb(1);--新增ai 1/1概率闪避
	actor:addAITaskHungry(1, 2000);--参数 发生概率(越大就是越低)
	actor:addAITaskSpecialAct(2, 0, 200,ACTORFLAG_AI_EAT, 11312) --发生概率(0就是不会主动发生)，动作执行时间，动作类型，产出物品
	actor:addAiTaskSwimming(2)
	actor:addAiTaskFollowDirection(2, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskBeg(7, 251, 800)	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskTempt(5, 1.0, 251, false) --食物吸引
	actor:addAiTaskWander(8, 1.0)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:showSkin('part1', true);
	actor:addAITaskFearItem(0, 2, 12526, 600);
	
	--actor:showSkin('body01', true);
end

function F3417_Interact(mob, player)
	local itemid = player:getCurToolID()
	if itemid == 12526 then
		mob:addAITaskDissolvedByItem(0, 2, 30) 
		return true;
	elseif(itemid == 11311)then--熊猫与便便交互消失
		player:shortcutItemUsed();
		mob:setPandaRunAwayTick(120)
		return true
	else
		if mob:getTamed() then
			if  mob:getTamedOwnerID() == player:getUin() and (251 == itemid) and (not mob:getAIToppleOver())  then
				player:shortcutItemUsed();
				mob:setFlagBit(ACTORFLAG_AI_EAT, true);
				return true
			elseif (not mob:getFlagBit(ACTORFLAG_SIT)) and (not mob:getFlagBit(ACTORFLAG_AI_EAT)) then 
				mob:setAIToppleOver(not mob:getAIToppleOver())
				return true
			end
		end
	end
	return true
end

--蜜蜂
function F3418_SetAi(actor)
	--actor:addAiTaskFearPlayer(2, 400, 2);
	--actor:addAiTaskFishBeg(1, -1, 800, false);
	actor:addAiTaskTargetHurtee(1, true)
	actor:addAiTaskFlyAttack(2, true, 1.5, 44, 1);--第四个是攻击附带buffID
	actor:addAiTaskRandFly(4, 25, 16, 2000);--第二个是多少个tick寻路一次，第三个是高度限制，第四个范围限制20米
	actor:addAiTaskFlyLoveBlock(3, 133, 500, 1.2, 400); --喜欢接近某种方块的ai 第二个参数是方块id ,第三个参数是发生概率(500就是25秒)，第四个是走路速度,第五个参数是每次ai执行的时间(tick)
	actor:addAiTaskFlyLoveBlock(3, 300, 500, 1.2, 400); 
	actor:addAiTaskFlyBeg(2, 300, 800, false); --喜欢的食物
	actor:addAiTaskFlyAttract(3, 1000, 500, 15); --飞行吸引第一个是优先级，第二个是吸引范围，第三个是跟随范围，第四个是跟随超时时间
end


-- c++调用
function CanSpawnMob(monsterid, world, x,y,z)
	if not WorldMgr then
		return true
	end
	local riddleBirdID = 3897
	local festvialSavageID = 3106
	local fantomId = 3917;
	local hour = WorldMgr:getHours()
	if monsterid == riddleBirdID then
		if (hour >= 18 or hour <= 6) 
		and FesivialActivity:MidAutumnIsOpened() 
		and FesivialActivity:IsSurviveMode()
		and FesivialActivity:GetMidAutumnConfig().riddleBird then
			local air = world:isBlockAir(x,y,z);
			local cube = world:isBlockNormalCube(x,y+1,z);
			return true
		else
			return false
		end
	elseif monsterid == festvialSavageID then
		if (hour >= 18 or hour <= 6) and FesivialActivity:MidAutumnIsOpened() and FesivialActivity:IsSurviveMode() and FesivialActivity:GetMidAutumnConfig().festvialSavage then
			return true
		else
			return false
		end
	elseif monsterid == fantomId then
		--凌晨0点到6点可以召唤
		if (hour >= 0 and hour <= 6) then
			return true
		else
			return false
		end
	end
	return true
end

-- 庆典野人
function F3106_Init(actor)
	if not FesivialActivity:MidAutumnIsOpened() then
		actor:setNeedClear(0)
	end
end

function F3897_Init(actor)
	local flyMob = tolua.cast(actor,"ClientFlyMob")
	flyMob:setHpProgressBarOffsetY(50)
	if not FesivialActivity:MidAutumnIsOpened() then
		actor:setNeedClear(0)
	end
	if not CurWorld then
		return
	end
	local x,y,z =1,1,1
	x,y,z = actor:getPosition(x,y,z)
	local height = 3
	for i = height, 0, -1 do
		if CurWorld:getBlockID(x,y + height,z) == 0 then
			actor:setPosition(x,y + height,z)
			return
		end
	end

end
-- 灯谜鸟
function F3897_SetAi(actor)
	actor:addAiTaskRandFly(2, 25, 10, 2000);--第二个是多少个tick寻路一次，第三个是高度限制，第四个范围限制20米
	actor:addAiTaskFlyAttract(1, 400, 200, 150000000, 1, 1); --飞行吸引第一个是优先级，第二个是吸引范围，第三个是跟随范围，第四个是跟随超时时间, 第五个跟随数量，第六个跟随上限最多几个，第七个是生物跟随高度(100=1格)
end

function F3897_LoadData(actor, str)
	str = str or ""
	g_F3897Data = g_F3897Data or {}
	local data = JSON:decode(str) or {}
	if not data or not data.guessID then
		local config = FesivialActivity:GetMidAutumnConfig().riddles

		data.guessID = math.random(1, #config)
	end
	g_F3897Data[actor:getObjId()] = data

end

function F3897_GetLuaData(objId)
	g_F3897Data = g_F3897Data or {}
	if not g_F3897Data[objId] then
		local data = {}
		local config = FesivialActivity:GetMidAutumnConfig().riddles
		data.guessID = math.random(1, #config)
		g_F3897Data[objId] = data
	end
	return g_F3897Data[objId]
end

function F3897_SaveData(actor)
	g_F3897Data = g_F3897Data or {}
	local str = JSON:encode(g_F3897Data[actor:getObjId()]) or ""
	return str
end
-- 打开标记
g_F3897TempFlag = {}
-- 灯谜鸟
function F3897_Interact(mob, player)
	local objId = mob:getObjId()
	if g_F3897TempFlag[objId] then
		-- 当前打开的玩家还在不在
		local curPlayer = GetWorldActorMgr(CurWorld):findActorByWID(g_F3897TempFlag[objId].objId)
		if curPlayer then
			if curPlayer:getObjId() ~= player:getObjId() then
				return true
			end
		end

		if g_F3897TempFlag[objId].answer then
			return true
		end
	end
	g_F3897TempFlag[objId] = { objId = player:getObjId()}
	F3897_GetLuaData(objId)
	if not player:hasUIControl() then
		FestivalMgr:handlePlayerInteractRiddleBird(player, "MiniUIRiddlesMain", JSON:encode({objId = objId}))
	else
		local riddlesUI = GetInst("MiniUIManager"):GetUI("MiniUIRiddlesMain");
		if not riddlesUI or not riddlesUI.rootNode:isVisible() then
			GetInst("MiniUIManager"):OpenUI("main_lanternriddle","miniui/miniworld/ingame","MiniUIRiddlesMain",{objId = objId})	
		end
	end
	return true
end

function RiddleBird_PlayerAnswer(player, mob, answer)
	--print("RiddleBird_PlayerAnswer actorID", actorID, type(actorID))
	local actorID = mob:getObjId()
	if player:getWorld():isRemoteMode() then
		FestivalMgr:answerRiddleBird(player, actorID, answer)
	else
		local mob = GetWorldActorMgr(CurWorld):findActorByWID(actorID)
		if not mob then
			return
		end
		local guessID = g_F3897Data[actorID].guessID
		if not guessID then
			return
		end
		local config = FesivialActivity:GetMidAutumnConfig().riddles
		local riddleData = config[guessID]
		if riddleData.rightAnswer == answer then
			ActorComponentCallModule(mob,"DropItemComponent","dropItem",11904, 1)
			mob:playAnimById(100809)
		else
			mob:playAnimById(100810)
		end
		g_F3897TempFlag[actorID].answer = true
		mob:ClearMob(40)
	end
	-- body
end

function F3897_Release(mob)
	g_F3897Data = g_F3897Data or {}
	g_F3897Data[mob:getObjId()] = nil
	g_F3897TempFlag[mob:getObjId()] = nil
end

function F3897_OnDie(mob)
end

function F3418_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

-- 蝴蝶
function Butterfly_SetAi(actor)
    -- 收到攻击逃跑ai @param 第一个参数是ai的优先级，第二个参数是逃跑速度
    actor:addAiTaskFlyPanic(1, 3, 10)

    -- 飞行被花冠吸引ai @param: 第二个是吸引范围，第三个是跟随范围，第四个是跟随超时时间
    actor:addAiTaskFlyAttract(2, 1000, 500, 15)

    -- 停留到花的方块上ai @param: 第二个参数是监测范围距离 第三个参数是触发概率 第四、五个参数是最大和最小停留时间 第六、七个参数是停留在花上AI的最大和最小cd 
    actor:addAiTaskStayFlower(4, 4, 0.4, 120, 30, 120, 30)

    -- 随机飞行ai @param: 第二个是多少个tick寻路一次，第三个是高度限制，第四个范围限制16米
	actor:addAiTaskRandFly(5, 60, 32, 1600)

end

-- 蝴蝶可以用瓶子捕获
function Butterfly_Interact(mob, player)
	local itemid = player:getCurToolID()
	if itemid == 11320 then
		--if math.random(0,10) < 8  then
			player:shortcutItemUsed()
		 	player:playCurToolSound()
			ActorComponentCallModule(mob,"DropItemComponent","dropItem",g_ButterflyToItemID[mob:getDefID()], 1)
			mob:ClearMob()
			return true
		--end
	end
	
	return false
end	

-- 红薇蝶
function F3885_SetAi(actor)
    -- 把小草变成橙色龙舌兰ai @param: 第二个是野草id 第三个是橙色龙舌兰id, 第三个参数是监测范围距离， 第四个参数是触发概率， 第五、六个参数是改变方块AI的最大和最小cd
    --actor:addAiTaskChangeBlock(3, 224, 308, 4, 0.1, 600, 360)
    -- 把野生麦子变成野蔷薇
    --actor:addAiTaskChangeBlock(3, 243, 313, 4, 0.1, 600, 360)
    Butterfly_SetAi(actor)
end

function F3885_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3885_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

-- 兰青蝶
function F3886_SetAi(actor)
    -- 把小草变成灰色龙舌兰
    --actor:addAiTaskChangeBlock(3, 224, 309, 4, 0.1, 600, 360)
    -- 把野生麦子变成星辰花
    --actor:addAiTaskChangeBlock(3, 243, 302, 4, 0.1, 600, 360)
    Butterfly_SetAi(actor)
end

function F3886_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3886_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

-- 香粉蝶
function F3887_SetAi(actor)
    -- 把小草变成粉色龙舌兰
    --actor:addAiTaskChangeBlock(3, 224, 310, 4, 0.1, 600, 360)
    -- 把野生麦子变成丁香花
    --actor:addAiTaskChangeBlock(3, 243, 301, 4, 0.1, 600, 360)
    Butterfly_SetAi(actor)
end

function F3887_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3887_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

-- 向阳蝶
function F3888_SetAi(actor)
    -- 把小草变成小白菊
    --actor:addAiTaskChangeBlock(3, 224, 311, 4, 0.1, 600, 360)
    -- 把野生麦子变成向阳花
    --actor:addAiTaskChangeBlock(3, 243, 300, 4, 0.1, 600, 360)
    Butterfly_SetAi(actor)
end

function F3888_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3888_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

-- 龙信蝶
function F3889_SetAi(actor)
    -- 把小草变成龙血花
    --actor:addAiTaskChangeBlock(3, 224, 303, 4, 0.1, 600, 360)
    -- 把野生麦子变成风信子
    --actor:addAiTaskChangeBlock(3, 243, 304, 4, 0.1, 600, 360)
    Butterfly_SetAi(actor)
end

function F3889_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3889_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

-- 舌钟蝶
function F3890_SetAi(actor)
    -- 把小草变成红色龙舌兰
    --actor:addAiTaskChangeBlock(3, 224, 307, 4, 0.1, 480, 180)
    -- 把野生麦子变成黄钟花
    --actor:addAiTaskChangeBlock(3, 243, 312, 4, 0.1, 480, 180)
    Butterfly_SetAi(actor)
end

function F3890_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

function F3890_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

function F3255_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false)
end

-- 虚空蝴蝶
function F3255_SetAi(actor)
    -- 把小草变成橙色龙舌兰、野蔷薇、灰色龙舌兰、星辰花、粉色龙舌兰、若兰、白椰花、风铃花、龙血花、风信子、红色龙舌兰、黄钟花、虚空之花
	--参数3传0 就从lua里面随机
    actor:addAiTaskChangeBlock(3, 224, 0, 4, 0.1, 480, 180)
	
    Butterfly_SetAi(actor)
end

function F3255_Interact(mob, player)
    return Butterfly_Interact(mob, player)
end

--萤火虫

function F3419_SetAi(actor)
	--actor:addAiTaskFearPlayer(2, 400, 2);
	--actor:addAiTaskFishBeg(1, -1, 800, false);
	--actor:addAiTaskTargetHurtee(1, true) --自己被打反击
	--actor:addAiTaskFlyAttack(2, true, 1.5); --飞行攻击
	--actor:setSunHurt(true)
	actor:addAiTaskRandFly(3, 50, 16, 3000); --随机飞行
	actor:addAiTaskFlyBeg(1, 12511, 800, false); --喜欢的食物
	actor:addAiTaskPanic(2, 3.25,0.0001);
end

function F3419_Init(mob)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setAvoidWater",false);
end

function F3419_Interact(mob, player)
	local itemid = player:getCurToolID();
	if itemid == 11320 then
		--if math.random(0,10) < 8  then
			player:shortcutItemUsed();
		 	player:playCurToolSound();
			ActorComponentCallModule(mob,"DropItemComponent","dropItem",748, 1)
			mob:ClearMob();
			return true;
		--end
	end
	
	return false;
end	

--虚空萤火虫
function F3270_Init(mob)
	F3419_Init(mob)
end

function F3270_SetAi(actor)
	F3419_SetAi(actor)
end

function F3270_Interact(mob, player)
	return F3419_Interact(mob, player)
end

--游商
function F3010_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0) --闲逛
	actor:addAiTaskTempt(5, 1.0, 12529, false) --食物吸引
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
end

--沙漠商人
function F3011_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0) --闲逛
	actor:addAiTaskTempt(5, 1.0, 12529, false) --食物吸引
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
end

--冰原商人
function F3012_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0) --闲逛
	actor:addAiTaskTempt(5, 1.0, 12529, false) --食物吸引
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
end

--宇宙商人
function F3018_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0) --闲逛
	actor:addAiTaskTempt(5, 1.0, 12529, false) --食物吸引
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
end

--空岛商人
function F3019_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskPanic(1, 3.0) --恐慌
	actor:addAiTaskWander(9, 1.0) --闲逛
	actor:addAiTaskTempt(5, 1.0, 144, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskTempt(5, 1.0, 145, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskTempt(5, 1.0, 146, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskTempt(5, 1.0, 147, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskTempt(5, 1.0, 148, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskTempt(5, 1.0, 149, false) --蝴蝶玻璃瓶吸引
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
end

--爱心大使
function F3021_GetInfo(testId)
	---------可配置部分----------
	--怪物ID
	local id = 3021
	--出生时间  
	local spawnTime = 8        
	--附属物ID 
	local attachmentIds = {321,322}
	--生存时间
	local liveTime = 10 
	------------------------------
	local attachmentId = attachmentIds[math.random(2)]
	return id,spawnTime,liveTime,attachmentId
end

function F3021_SetAi(actor)
	---------可配置部分----------
	--食物吸引的AI是根据当前需求来动态获取的，不需要在此配置
	------------------------------
end

function F3021_GetInteractItemInfo()
	---------可配置部分----------
	local interactItemInfo = 
	{	
		--以下是需求物品
		{id = 828,name = "舒适的床",num = 2,appearProbability = 12,awardArea = {"A","D"},awardAreaProbability = {100,100},animId = 0,itemType = 1},
		{id = 12202,name = "皮胸甲",num = 2,appearProbability = 24,awardArea = {"A","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 12543,name = "烤全鸡",num = 2,appearProbability = 36,awardArea = {"A","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 12540,name = "长条麦包",num = 2,appearProbability = 48,awardArea = {"A","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 820,name = "书柜",num = 1,appearProbability = 60,awardArea = {"B","D"},awardAreaProbability = {100,100},animId = 0,itemType = 1},
		
		{id = 1063,name = "蓝图工作台",num = 1,appearProbability = 68,awardArea = {"B","D"},awardAreaProbability = {100,100},animId = 0,itemType = 1},
		{id = 13800,name = "过山车",num = 2,appearProbability = 76,awardArea = {"B","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 844,name = "金弹簧",num = 1,appearProbability = 84,awardArea = {"B","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 748,name = "萤火虫玻璃瓶",num = 3,appearProbability = 92,awardArea = {"B","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		{id = 11204,name = "孔雀石",num = 1,appearProbability = 100,awardArea = {"B","D"},awardAreaProbability = {100,50},animId = 0,itemType = 1},
		--以下是可交互物品
		{id = 11311,name = "动物肥料",num = 1,appearProbability = 0,awardArea = {"E"},awardAreaProbability = {100},animId = 100158,itemType = 2},
		{id = 12566,name = "坏掉的饺子",num = 1,appearProbability = 0,awardArea = {"E"},awardAreaProbability = {100},animId = 100158,itemType = 2},
		{id = 11302,name = "兽骨",num = 1,appearProbability = 0,awardArea = {"F"},awardAreaProbability = {100},animId = 100168,itemType = 2},
		{id = 12526,name = "奇怪的肘子",num = 1,appearProbability = 0,awardArea = {"F"},awardAreaProbability = {100},animId = 100168,itemType = 2},
		{id = 12527,name = "蜘蛛腿",num = 1,appearProbability = 0,awardArea = {"F"},awardAreaProbability = {100},animId = 100168,itemType = 2},
		{id = 12504,name = "蜂巢碎片",num = 1,appearProbability = 0,awardArea = {"G"},awardAreaProbability = {100},animId = 100131,itemType = 2},
		{id = 12576,name = "泡泡糖",num = 1,appearProbability = 0,awardArea = {"G"},awardAreaProbability = {100},animId = 100131,itemType = 2},
		{id = 12500,name = "野果",num = 1,appearProbability = 0,awardArea = {"G"},awardAreaProbability = {100},animId = 100131,itemType = 2},
	}
	------------------------------
	return interactItemInfo
end

function F3021_GetAwardItemInfo()
	---------可配置部分----------
	local awardItemInfo = 
	{
		A = 
		{
			{id = 300,name = "向阳花",num = 1,probability = 8},
			{id = 301,name = "丁香花",num = 1,probability = 16},
			{id = 302,name = "星辰花",num = 1,probability = 24},
			{id = 303,name = "龙血花",num = 1,probability = 32},
			{id = 304,name = "风信子",num = 1,probability = 40},
			{id = 305,name = "龙爪草",num = 1,probability = 48},
			{id = 306,name = "龙血树",num = 1,probability = 56},
			{id = 311,name = "小白菊",num = 1,probability = 64},
			{id = 312,name = "黄钟花",num = 1,probability = 72},
			{id = 313,name = "野蔷薇",num = 1,probability = 80},
	
			{id = 666,name = "硬砂块",num = 8,probability = 85},
			{id = 126,name = "魔古岩",num = 8,probability = 90},
			{id = 12240,name = "小彩蛋",num = 10,probability = 95},
			{id = 11810,name = "鞍",num = 1,probability = 100},
		},
		B = 
		{
			{id = 12550,name = "香溢麦包",num = 1,probability = 6},
			{id = 12551,name = "香溢薯条",num = 1,probability = 12},
			{id = 12552,name = "香溢方南瓜派",num = 1,probability = 18},
			{id = 12553,name = "香溢饼干",num = 1,probability = 24},
			{id = 12555,name = "香溢野萝卜",num = 1,probability = 30},
			{id = 12556,name = "香溢西瓜汁",num = 1,probability = 36},
			{id = 12557,name = "香溢水果拼盘",num = 1,probability = 42},
			{id = 12558,name = "香溢烤鸡",num = 1,probability = 48},
			{id = 12559,name = "香溢牛堡",num = 1,probability = 54},
			{id = 12560,name = "香溢羊煲",num = 1,probability = 60},
			{id = 12562,name = "香溢异味腿",num = 1,probability = 66},
			
			{id = 11315,name = "地心之眼",num = 1,probability = 72},
			{id = 126,name = "魔古岩",num = 24,probability = 78},
			{id = 12240,name = "小彩蛋",num = 24,probability = 84},
			{id = 11203,name = "蓝钻石",num = 1,probability = 90},
			{id = 550,name = "荧光晶块",num = 1,probability = 96},
			{id = 12963,name = "星星礼盒",num = 1,probability = 100},
		},
		C = 
		{
			
		},
		D = 
		{
			{id = 11806,name = "信纸",num = 1,probability = 100},
			

		},
		E = 
		{
			{id = 1003002,name = "变鸡（30s）",num = 1,probability = 14},
			{id = 1004002,name = "变牛（30s）",num = 1,probability = 21},
			{id = 1005002,name = "变羊（30s）",num = 1,probability = 55},
			{id = 1006002,name = "变恶魔（30s）",num = 1,probability = 82},
			{id = 1007002,name = "变猪（30s）",num = 1,probability = 94},
			{id = 1008002,name = "变团子（30s）",num = 1,probability = 100},
		},
		F = 
		{
			{id = 38001,name = "反向（60s）",num = 1,probability = 14},
			{id = 33001,name = "着火（1级4s）",num = 1,probability = 21},
			{id = 8003,name = "迟缓（3级5s）",num = 3,probability = 55},
			{id = 60001,name = "中毒（1级5s）",num = 1,probability = 82},
			{id = 42002,name = "泡泡（2级15s）",num = 2,probability = 100},
		},
		G = 
		{
			{id = 6003,name = "跳高高（3级60s）",num = 3,probability = 14},
			{id = 4003,name = "疾跑（3级180s）",num = 3,probability = 55},
			{id = 14003,name = "挖掘幸运（3级60s）",num = 3,probability = 82},
			{id = 3003,name = "生命恢复（3级45s）",num = 3,probability = 100},
		},
		
	}
	------------------------------
	return awardItemInfo
end

function F3021_Get_AES(isInteractable)
	---------可配置部分----------
	--需求物品交易时，爱心大使播放的动画
	local animIds = {100155}
	--需求物品交易时，播放的特效
	local effect = "aixinrenwu"
	--需求物品交易时，播放的音效
	local sound = "npc.deal_succeed"
	--交互外物品交易时，爱心大使播放的动画
	local animIdEx = 100170
	------------------------------

	if isInteractable then 
		local animId = animIds[math.random(1,#animIds)]
		return animId,effect,sound
	else
		return animIdEx 
	end 
end

F3021_CurNeedItemId = 0
F3021_CurNeedItemCount = 0
F3021_CurAnimId = 0
function F3021_GetCurNeedItemId()
	local interactItemInfo = F3021_GetInteractItemInfo()
	--根据概率随机出当前应该需求哪个物品
	local random = math.random(1,100)
	local itemIndex = 0
	for i = #interactItemInfo,1,-1 do 
		if interactItemInfo[i].appearProbability ~= 0 then
			if i > 1 then 
				if random <= interactItemInfo[i].appearProbability and random > interactItemInfo[i - 1].appearProbability then 
					itemIndex = i 
					break 
				end 
			else
				itemIndex = i 
				break 
			end 
		end
	end 
	if interactItemInfo[itemIndex] then
		F3021_CurNeedItemId = interactItemInfo[itemIndex].id 
		F3021_CurNeedItemCount = 0 
		return F3021_CurNeedItemId 
	else
		return 0
	end 
end

function F3021_GetCurNeedItemIdWithOutRandom()
	return F3021_CurNeedItemId
end

function F3021_SetCurNeedItemId(id)
	F3021_CurNeedItemId = id
end

function F3021_SetCurNeedItemCount(count)
	F3021_CurNeedItemCount = count
end

function F3021_GetCurNeedItemCount()
	local maxCount = 0
	local interactItemInfo = F3021_GetInteractItemInfo()
	for i = 1,#interactItemInfo do 
		if interactItemInfo[i].id == F3021_CurNeedItemId then
			maxCount = interactItemInfo[i].num 
			break 
		end 
	end 
	return F3021_CurNeedItemCount,maxCount
end

function F3021_GetCurAnimId()
	return F3021_CurAnimId
end

function F3021_Interact(mob, player)
	--根据配置的概率随机到本次奖励中奖的奖区
	local function getAwardAreaTable(singleItemInfo)
		local awardAreaTable = {}

		local singleItemAwardAreas = singleItemInfo.awardArea
		local singleItemAwardProbability = singleItemInfo.awardAreaProbability
		for i = 1,#singleItemAwardAreas do 
			local aArea = singleItemAwardAreas[i]
			local aAreaProbability = singleItemAwardProbability[i]
			local randomArea = math.random(1,100)
			if randomArea <= aAreaProbability then 
				table.insert(awardAreaTable,aArea)
			end 
		end 

		return awardAreaTable
	end
	--检查当前玩家交易过来的道具是不是可交互的
	local function checkIsInteractable(itemId,awardAreaTable)
		local isInteractable = false 
		if itemId == F3021_CurNeedItemId then 
			--是当前需要的道具，可交互
			isInteractable = true 
		else
			--是当前不需要的道具
			for i = 1,#awardAreaTable do 
				local aArea = awardAreaTable[i]
				if aArea == "E" or aArea == "F" or aArea == "G" then 
					--产生的奖励在BUFF区，说明在交易范围内
					isInteractable = true 
					break 
				end 
			end 
		end 
		return isInteractable
	end
	--根据中奖的奖区随机获取对应的奖励
	local function getAwardTable(awardAreaTable)
		local awardTable = {}

		for i = 1,#awardAreaTable do 
			local awardItemInfo = F3021_GetAwardItemInfo()
			local awardItemArray = awardItemInfo[awardAreaTable[i]]
			--从奖区中随机出一个奖励
			local randomAward = math.random(1,100)
			local awardIndex = 0
			for j = #awardItemArray,1,-1 do 
				if awardItemArray[j].probability ~= 0 then
					if j > 1 then 
						if randomAward <= awardItemArray[j].probability and randomAward > awardItemArray[j - 1].probability then
							awardIndex = j 
							break   
						end 
					else
						awardIndex = j
						break  
					end 
				end 
			end 
			if awardIndex ~= 0 then 
				local award = awardItemArray[awardIndex]
				table.insert(awardTable,award)
			end 
		end 

		return awardTable
	end
	--检查当前是不是BUFF奖励
	local function checkIsBuffAward(singleItemInfo)
		local isBuffAward = false
		if singleItemInfo.itemType == 2 then 
			isBuffAward = true 
		end 
		return isBuffAward
	end
	--与爱心大使交易获得道具类型的道具
	local function interactItemAwardItem(awardTable)
		local curCount,maxCount = F3021_GetCurNeedItemCount()
		local shortcutNum = player:getCurShortcutItemNum()
		local remainNum = maxCount - curCount 
		local canUseNum = shortcutNum 
		if shortcutNum > remainNum then 
			canUseNum = remainNum
		end 
		curCount = curCount + canUseNum
		if curCount == maxCount then 
			print("滿足数量需求，获得奖励")
			for i = 1,#awardTable do 
				local finalAward = awardTable[i]
				if finalAward.id == 11806 then 
					--掉落爱心大使的信件，需要额外处理
					local letterDataJson = F3021_GetLetterStr()
					ActorComponentCallModule(mob,"DropItemComponent","dropSpecialLetterItem",finalAward.id,finalAward.num,letterDataJson)
				else
					--掉落普通道具
					ActorComponentCallModule(mob,"DropItemComponent","dropItem",finalAward.id,finalAward.num)
				end 
			end 
		end 
		F3021_SetCurNeedItemCount(curCount)
		F3021_Statistics(player)
		player:shortcutItemAllUsed(canUseNum)
		--播放动画，特效，音效
		local animId,effect,sound = F3021_Get_AES(true)
		mob:getBody():playAnimBySeqId(animId)
		F3021_CurAnimId = animId
		ActorComponentCallModule(mob,"SoundComponent","playSound",sound, 1.0, 1.0)
		ActorComponentCallModule(mob,"EffectComponent","playBodyEffectByName","aixinrenwu")
		return true
	end
	--与爱心大使交易获得BUFF类型的道具
	local function interactBuffAwardItem(singleItemInfo,awardTable)
		--使用手上道具
		F3021_Statistics(player)
		player:shortcutItemUsed(true)
		--加BUFF
		local finalAward = awardTable[1] --BUFF只会存在一个
		local buffId = math.floor(finalAward.id / 1000)
		player:getPlayerAttrib():addBuff(buffId, finalAward.num)
		--播放动画
		local animId = singleItemInfo.animId 
		mob:getBody():playAnimBySeqId(animId)
		F3021_CurAnimId = animId
		return false
	end
	--与爱心大使交易它不需要的道具
	local function interactNoNeedItem()
		--播放动画
		local animId = F3021_Get_AES(false)
		mob:getBody():playAnimBySeqId(animId)
		F3021_CurAnimId = animId
		return false
	end

	--交互核心逻辑
	local itemid = player:getCurToolID()
	local interactItemInfo = F3021_GetInteractItemInfo()
	local isInteractable = false
	for i = 1,#interactItemInfo do 
		if interactItemInfo[i].id == itemid then
			--取到当前玩家交易过来的道具的配置信息
			local awardAreaTable = getAwardAreaTable(interactItemInfo[i])
			isInteractable = checkIsInteractable(itemid,awardAreaTable)
			if isInteractable then 
				local awardTable = getAwardTable(awardAreaTable)
				local isBuffAward = checkIsBuffAward(interactItemInfo[i])
				if not isBuffAward then
					print("与爱心大使交易获得道具类型的道具")
					return interactItemAwardItem(awardTable)
				else
					print("与爱心大使交易获得BUFF类型的道具")
					return interactBuffAwardItem(interactItemInfo[i],awardTable)
				end 
			end 
		end 
	end 
	if not isInteractable then 
		print("与爱心大使交易它不需要的道具")
		return interactNoNeedItem()
	end 
end	

--愛心大使埋点
function F3021_Statistics(player)
	--玩家UIN
	local playerUin = player:getUin()
	--当前交易道具ID
	local itemId = player:getCurToolID()
	
	statisticsGameEvent(60001,"%d",playerUin,"%d",itemId)
end

--获取爱心大使信纸内容
function F3021_GetLetterStr()
	---------可配置部分----------
	local titles = {21562,21564,21566,21568,21570,21572,21574,21576,21578}
	local contents = {21563,21565,21567,21569,21571,21573,21575,21577,21579}
	local authorIds = {21583,21584,21585,21586,21587,21588,21589,21590,21590}
	------------------------------
	local randomIndex = math.random(1,9)
	local title = GetS(titles[randomIndex])
	local content = GetS(contents[randomIndex])
	local authorName = GetS(authorIds[randomIndex])
	local letterId = randomIndex
	local letterData = 
	{
		title = title, 
		context= content,
		authorName = authorName,
		letterId = letterId,
	}
	local letterDataJson = JSON:encode(letterData)
	return letterDataJson
end

--获取爱心大使的魔力卷轴ID
function F3021_GetEnchantIds()
	local ids = {}
	local enchantNum = DefMgr:getCurAccordEnchantsNum()
	local randomTimes = math.random(1,5)
	for i = 1,#randomTimes do 
		local enchantId = math.random(1,enchantNum)
		local enchantLevel = math.random(1,5)
		local finalId = enchantId * 100 + enchantLevel
		table.insert(ids,finalId) 
	end 
	local idsJson = JSON:encode(ids)
	return idsJson
end

--神秘巫师
function F3103_SetAi(actor)
--	actor:addAiTaskCombine(1, 1.0)
--	actor:addAiTaskSwimming(1)
--	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
--	actor:addAiTaskPanic(1, 3.25,0.0001)
--	actor:addAiTaskRandFly(8, 50, 8, 2000);--第二个是多少个tick寻路一次，第三个是高度限制，第四个范围限制20米
--	actor:addAiTaskFlyAttack(2, true, 1.5, 44, 1);--第四个是攻击附带buffID
	--actor:addAiTaskArrowAttack(4, 1.0, 80, 60, 1500, true)--最后一个参数是是否在追击玩家的过程中寻找障碍躲避
--	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15050,1,0,1)
--	actor:addAiTaskWander(5, 1.0)
--	actor:addAiTaskWatchClosest(6, 800)
--	actor:addAiTaskLookIdle(6)
--	actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,15067,1,0,1) --发射投射物（重力结晶）
	actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end


--神秘小星球人A
function F3110_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
    actor:addAiTaskPlayerPanic(5, 4, 500, 40, 40) --玩家逃跑，第2个参数开始是: 逃跑速度, 检测范围, 持续时间, 间隔时间
    --actor:addAiTaskItemPanic(1, 1.4, 5, 40, 40, 15058, 15061) --炸弹逃跑，第2个参数开始是: 逃跑速度, 炸弹检测范围, 持续时间, 间隔时间, 物品id1, 物品id2
	--actor:addAiTaskCombine(1, 1.4, 800, 200, 200, 22) --合体，第2个参数开始是: 合体跑步速度, 合体检测范围, 间隔Tick, 转圈Tick, 转圈角度参数
	actor:addAiTaskCombine(1, 1.4, 1600, 0, 100, 0.3) --合体，第2个参数开始是: 合体跑步速度, 合体检测范围, 间隔Tick, 转圈Tick, 低于百分之多少血量合体，转圈角度参数
	actor:addAiTaskSeparatePanic(1, 1.4, 300) --分身逃跑，第2个参数开始是: 逃跑速度, 持续时间
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskTempt(3, 1.0, 12553, false)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15058,1,0,1) --发射投射物（炸弹）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskBeg(8, 12553, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	actor:addAiTaskTargetHurtee(1, true) --自己被打反击
	--参数  优先级，概率 0表示忽略，是否检查可见
	--actor:addAiTaskTargetNearest(2, 0, true, 0.0)  

	actor:addAiTaskClosestDance(6, 30, 3, 2, 100*20, 2, 10*20, 20) --交谈ai，计数搜索范围,总数,聚集的速度,结束后的cd时间,聚集时离中心点距离,持续的时间
	
	actor:addAiTaskMakeTrouble(3, 1045, 160, 1, 20, true)   -- 破坏氧气提炼装置
	actor:addAiTaskMakeTrouble(3, 800, 160, 1, 20, true)    -- 破坏工具箱
	--actor:addAiTaskMakeTrouble(3, 801, 160, 1, 20, true)    -- 破坏箱子
	actor:addAiTaskMakeTrouble(3, 802, 160, 1, 20, true)    -- 破坏熔炉
end

function F3110_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12553, 3124, 6);  --香溢饼干驯服
	local itemid = player:getCurToolID()
	if itemid == 12553 then
		return true
	end
	return false
end

--驯服神秘小星球人A
function F3124_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水
	actor:addAiTaskSwimming(1)	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSit(2)
	actor:addAiTaskProjectileAttack(4, 1.0, 20, 60, 1500,15058,1,0,1)--发射投射物（炸弹） 参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskWander(7, 1.0) --闲逛
	actor:addAiTaskBeg(8, 12553, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1) --主人被打帮忙
	actor:addAiTaskTargetOnwnerHurter(2) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
end

function F3124_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	


--神秘小星球人B
function F3111_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskCombine(1, 1.4, 1600, 0, 100, 0.3) --合体，第2个参数开始是: 合体跑步速度, 合体检测范围, 间隔Tick, 转圈Tick, 低于百分之多少血量合体，转圈角度参数
	actor:addAiTaskSeparatePanic(1, 1.4, 300) --分身逃跑，第2个参数开始是: 逃跑速度, 持续时间
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15059,1,0,1) --发射投射物（炸弹）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskBeg(8, 12557, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	--actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAiTaskClosestDance(0, 30, 3, 2, 100*20, 2, 10*20) --交谈ai，计数搜索范围,总数,聚集的速度,结束后的cd时间,聚集时离中心点距离,持续的时间
end

function F3111_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12557, 3125, 6);  --香溢水果拼盘驯服
	local itemid = player:getCurToolID()
	if itemid == 12557 then
		return true
	end
	return false
end

--驯服神秘小星球人B
function F3125_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水
	actor:addAiTaskSwimming(1)	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSit(2)
	actor:addAiTaskProjectileAttack(4, 1.0, 20, 60, 1500,15059,1,0,1)--发射投射物（炸弹） 参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskWander(7, 1.0) --闲逛
	actor:addAiTaskBeg(8, 12557, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1) --主人被打帮忙
	actor:addAiTaskTargetOnwnerHurter(2) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
end

function F3125_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	

--神秘小星球人C
function F3112_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAITaskBumpAttack(1, 1000, 30, 50, 10, 1, 60, 110, 2); --第二个参数开始 距离,眩晕tick,预施法tick,触发概率,速度,击飞高度,击退距离
	actor:addAiTaskCombine(1, 1.4, 1600, 0, 100, 0.3) --合体，第2个参数开始是: 合体跑步速度, 合体检测范围, 间隔Tick, 转圈Tick, 低于百分之多少血量合体，转圈角度参数
	actor:addAiTaskSeparatePanic(1, 1.4, 300) --分身逃跑，第2个参数开始是: 逃跑速度, 持续时间
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	--actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15060,1,0,1) --发射投射物（炸弹）
	actor:addAiTaskAtk(4, 0, true, 2.0) --近战攻击
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskBeg(8, 12552, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	--actor:addAiTaskTargetHurtee(1, false)
	--参数  优先级，概率 0表示忽略，是否检查可见
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAiTaskClosestDance(0, 30, 3, 2, 100*20, 2, 10*20) --交谈ai，计数搜索范围,总数,聚集的速度,结束后的cd时间,聚集时离中心点距离,持续的时间

end

function F3112_Interact(mob, player)
	TameAnimal_Interact(mob, player, 12552, 3126, 6);  --香溢方南瓜派驯服
	local itemid = player:getCurToolID()
	if itemid == 12552 then
		return true
	end
	return false
end

--驯服神秘小星球人C
function F3126_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水
	actor:addAiTaskSwimming(1)	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSit(2)
	--actor:addAiTaskProjectileAttack(4, 1.0, 20, 60, 1500,15060,1,0,1)--发射投射物（炸弹） 参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 1.0) --近战攻击
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiTaskWander(7, 1.0) --闲逛
	actor:addAiTaskBeg(8, 12507, 800) 	--参数 优先级，喜欢的食物（饼干），距离
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1) --主人被打帮忙
	actor:addAiTaskTargetOnwnerHurter(2) --主人攻击帮忙
	actor:addAiTaskTargetHurtee(3, true) --自己被打反击
end

function F3126_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() then
		if  mob:getTamedOwnerID() == player:getUin() and (0 == mob:isBreedItem(itemid))  then
			mob:setAISitting(not mob:getSitting())
			return true
		end
	end
	
	return false
end	

--神秘星球人AA
function F3113_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15061,1,0,1) --发射投射物（巨型炸弹）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--神秘星球人BB
function F3114_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15062,1,0,3) --发射投射物（巨型冰锥）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--神秘星球人CC
function F3115_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15063,1,0,1) --发射投射物（巨型冰锥）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--神秘星球人AB
function F3116_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15064,1,0,1) --发射投射物（冰炸弹）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--神秘星球人AC
function F3117_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15065,1,0,1) --发射投射物（火炸弹）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--神秘星球人BC
function F3118_SetAi(actor)
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskSeparate(1, 30) --分身AI，第2个参数开始是: 延迟生成分体怪物Tick	
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路是否要避水	
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskProjectileAttack(4, 1.0, 80, 60, 1500,15066,1,0,1) --发射投射物（冰火球）
	actor:addAiTaskWander(5, 1.0)
	actor:addAiTaskWatchClosest(6, 800)
	actor:addAiTaskLookIdle(6)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
end

--叮叮祖先(白)
function F3511_SetAi(actor)

	actor:setOxygenNeed(false) -- 是否需要氧气
    actor:addAiTaskWizardProjectileAttack(4, 1.0, 80, 60, 1500,15516,1,0,1) --发射投射物（巨型炸弹）
	actor:addAiTaskGhostBombAttack(3, 1.0, 80, 60, 1500,15518,1,0,4) --发射投射物（十字围杀炸弹）
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAiTaskWizardFly(5, 50, 16, 4, 2000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--咚咚祖先(蓝)
function F3512_SetAi(actor)

	actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(4, 1.0, 80, 60, 1500,15515,1,0,1) --发射投射物（蓝色小怪物投掷物）
	actor:addAiTaskGhostIceAttack(3, 1.0, 80, 60, 1500,15517,1,0,16) --发射投射物（天降冰雹）
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAiTaskWizardFly(5, 50, 16, 4, 2000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--当当祖先(红)
function F3513_SetAi(actor)

	actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskFlyAttack(4, true, 1.5, 44, 1);--第四个是攻击附带buffID
	--actor:addAiTaskGhostBumpAttack(3, 2000, 30, 50, 10, 1, 1000, 110, 5); --第二个参数开始 距离,眩晕tick,预施法tick,触发概率,速度,击飞高度,击退距离
	--actor:addAiTaskWizardProjectileAttack(3, 1.0, 80, 60, 1500,15063,1,0,6) --发射投射物（红色小怪物技能）
	actor:addAiTaskGhostBombAttack(3, 1.0, 80, 60, 1500,15063,1,0,6) --发射投射物（红色小怪物技能）
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	actor:addAiTaskWizardFly(5, 50, 16, 4, 3000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--一阶BOSS参数初始化
function F3510_Init(actor)
	if actor.setSleep then --判断Boss独有接口setSleep 触发器创建的生物不属于Boss 
		actor:setSleep(100); --第一个参数是休闲状态下等待多久时间开始回血
		actor:setBeatGround(100, 100, 150, 8,8,24); --第一个参数是左手攻击时间, 第二个参数是右手攻击时间, 第三个参数是双手攻击时间, 第四个参数是左手震飞高度(Block单位), 第五个参数是右手震飞高度, 第六个参数是双手震飞高度
		actor:setNormalState(300); --第一个参数是正常状态持续时间
	end
end

--二阶BOSS参数初始化
function F3514_Init(actor)
	-- local att = actor:getAttrib()
	-- att:setHP(0.5)
	--actor:setHP(0.5); --BOSS血量低于百分比进入狂暴状态
	--actor:setDistance(1000, 500); --第一个参数是进入远程攻击的距离, 第二个参数是进入空中攻击的玩家离地高度
	--actor:setProjectile(1, 1, 1); --第一个参数是COMBO攻击第一次投掷物数量, 第二个参数是COMBO攻击第二次投掷物数量, 第三个参数是岩石海啸投掷物数量
	if actor.setSleep then --判断Boss独有接口setSleep 触发器创建的生物不属于Boss 
		actor:setSleep(100); --第一个参数是休闲状态下等待多久时间开始回血
		actor:setAttackNormal(1500, 500, 45, 45); --第一个参数是进入远程攻击的距离, 第二个参数是进入空中攻击的玩家离地高度, 第三个参数是对地CD, 第四个参数是对空CD
		actor:setAttackCombo(20, 20, 30, 30, 30); --第一个参数是COMBO攻击第一次投掷物数量, 第二个参数是COMBO攻击第二次投掷物数量, 第三个参数是第一次投掷CD, 第四个参数是第二次投掷CD, 第五个参数是第三次激光攻击后间隔CD
		actor:setAttackLaser(50, 100); --第一个参数是激光蓄能时间, 第二个参数是激光释放时间
		actor:setAttackRockwave(200 ,300, 30); --第一个参数是岩石海啸投掷物数量, 第二个参数是准备时间, 第三个参数是释放CD
		actor:setAttackRevengeroar(20); --第一个参数是复仇吼叫时间
		actor:setStun(300); --第一个参数是虚弱晕眩时间
		actor:setTransformState(600); --第一个参数是状态切换时间
		actor:setTotem(24, 64); --第一个参数是图腾随机位置最小距离, 第二个参数是图腾随机位置最大距离
	end
end

function FCustomActor_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(2, true) --自己被打反击
	actor:addAiTaskAtk(3, 0, true, 1.0) --近战攻击
	

	--actor:addAiMate(2, 1.0)
	--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
	actor:addAiTaskTempt(3, 1.0, 1020, false)
	actor:addAiTaskWander(8, 1.0)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(10)
end

--修改野人名字
function MobModifyWildmanName(objId)
end

local eqname = {"head" ,"breast","leg","shoe","back","hand"}
local proname = { "base" ,"pro","eq" }

local function getCompareDefId(defId)
	if defId == 3200 or defId == 3202 then 
		return true
	end
	if defId == 3237 then
		return false 
	end
	if defId >= 3233 and defId <= 3239 then
		return true
	end
	return false
end


-- 获取野人显示子模型名称
function getMobSubModeName(mob, slot, level)
	local strname = ""
	if slot < 1 and slot > 6 then return "" end

	local equipid = mob:getEquipItem(slot - 1);
	local profess = AIFunctionMgr:getProfession(mob)
	local hairid = AIFunctionMgr:getHairId(mob)
	if (equipid ~= 0 or level > 0) and slot < 5 then -- 装备
		local mobtool = DefMgr:getToolDef(equipid);
		strname = string.format("%s_%s_%d", eqname[slot], proname[3], (level > 0 and level or mobtool.Level))
	elseif profess ~= 0 and (slot == 1 or slot == 5) then -- 职业
		strname = string.format("%s_%s_%d", eqname[slot], proname[2], profess);
	else
		if slot == 1 then
			
			local comDefid = getCompareDefId(mob:getDefID())
			strname = string.format("%s_%s_%d", eqname[slot], proname[1], ((comDefid) and hairid or  hairid + 3)); -- 头发索引 [3200]->[1,3] [3201]->[4,6]
			--strname = string.format("%s_%s_%d", eqname[slot], proname[1], ((mob:getDefID() == 3200 or mob:getDefID() == 3202 ) and hairid or  hairid + 3)); -- 头发索引 [3200]->[1,3] [3201]->[4,6]
		else
			local function func1(defId)
				if defId == 3202 or defId == 3234 or defId == 3239 then
					return true
				end
				return false
			end
			local function func2(defId)
				if defId == 3200 or defId == 3237 or defId == 3233 or defId == 3235 then
					return true
				end
				return false
			end
			strname = string.format("%s_%s_%d", eqname[slot], proname[1], ((func1(mob:getDefID())) and 3 or ((func2(mob:getDefID())) and 1 or 2)));
			--strname = string.format("%s_%s_%d", eqname[slot], proname[1], (mob:getDefID() == 3202 and 3 or (mob:getDefID() == 3200 and 1 or 2)));
		end
	end

	return strname
end

-- 野人初始化形象
function MobModleinit(mob)
	mob:showSkin("breast_eq_8",false)
	mob:showSkin("leg_eq_8",false)
	mob:showSkin("shoe_eq_8",false)
	mob:showSkin("head_eq_8",false)
	mob:showSkin("breast_pro_9",false)
	local num = {[1] = 7,[2] = 6,[3] = 6,[4] = 6,[5] = 6,[6] = 2}
	--初始模型都是显示的
	for i = 1, 6 do
		for j =1 ,3 do
			for k =1 , num[i] do
				mob:showSkin(string.format("%s_%s_%d", eqname[i],proname[j],k),false)  -- 隐藏所有子模
			end
		end
	end

	for i =1, 6 do
		mob:showSkin(getMobSubModeName(mob,i,0),true)  -- 隐藏所有子模
	end

end

function MobCloth(mob ,player)
	local mobid = mob:getDefID()
	local itemid = not player and 0 or player:getCurToolID()
	if mobid == 3200 or mobid == 3201 or mobid == 3202 then
		if itemid ~= 0 then
			if itemid == ITEM_SHEARS then
				-- 剪头发
				AIFunctionMgr:hairCut(mob,math.random(1,3))
				return
			end
			-- 换装备
			local mobtool = DefMgr:getToolDef(itemid)
			mob:showSkin(getMobSubModeName(mob,mobtool.Type-7,0) , false)
			mob:addInitEquip(mobtool.Type - 8,itemid)
		end
	end
end

--随机发型
function setRandHair(mob)
	AIFunctionMgr:setRandHair(mob, 0)
end

--随机发型颜色
function setRandHairColor(mob)
	local rnd = math.random(1, 15)
	local cor = math.random(0, 15)
	AIFunctionMgr:setHairColor(mob, GetColorFromBlockInfo(600+rnd,cor))
end

--驯服后的野人
function F3200_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanPassClosedWoodenDoors",true)
	-- actor:addAiTaskSwimming(1)
	-- actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	-- actor:addAiTaskPanic(1, 3.25,0.0001)
	-- actor:addAiTaskAtk(2, 0, false, 1.0)
	-- actor:addAiTaskMoveTowardsRestriction(4, 1.0)
	-- actor:addAiTaskWander(6, 1.0)
	actor:addAiTaskWatchClosest(7, 800)
	-- actor:addAiTaskLookIdle(10, 60) --左右看看
end

--野人3200初始化
function F3200_Init(mob)
	MobModleinit(mob)
	AIFunctionMgr:setFoodByTamed(mob,12502) --面条
	--随机发型
	setRandHair(mob)
	--随机发型颜色
	setRandHairColor(mob)
end

function F3200_Interact(mob, player)
	local itemid = player:getCurToolID()
	local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)

	if itemid>=11501 and itemid<=11515 and curblockid-600 ~= itemid-11500 then
		return true
	end

	return false
end

--野人3201初始化
function F3201_Init(mob)
	MobModleinit(mob)
	AIFunctionMgr:setFoodByTamed(mob,12502) --面条
	--随机发型
	setRandHair(mob)
	--随机发型颜色
	setRandHairColor(mob)
end

function F3201_Interact(mob, player)
	local itemid = player:getCurToolID()

	local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)

	if itemid>=11501 and itemid<=11515 and curblockid-600 ~= itemid-11500 then

		return true
	end

	return false
end

--野人3202初始化
function F3202_Init(mob)
	MobModleinit(mob)
	AIFunctionMgr:setFoodByTamed(mob,12502) --面条
	--随机发型
	setRandHair(mob)
	--随机发型颜色
	setRandHairColor(mob)
end

function F3202_Interact(mob, player)
	local itemid = player:getCurToolID()

	local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)

	if itemid>=11501 and itemid<=11515 and curblockid-600 ~= itemid-11500 then

		return true
	end

	return false
end

--野人纪念包裹
function F3203_Interact(mob,player)
	if mob then 
		local bagContainer = mob:getBags()
		if bagContainer then  
			for i = 0,mob:getDef().BagNum - 1 do
				local grid = bagContainer:index2Grid(i)
				if grid then 
					local itemId = grid:getItemID()
					if itemId and itemId > 0 then
						local itemCount = grid:getNum()
						if itemId == 11806 then 
							local data = grid:getUserDataInt();
							local nameData = grid:getUserdataStr();
							if data then
								local letterDataJson = F3203_GetLetterStr(mob,data, nameData)
								ActorComponentCallModule(mob,"DropItemComponent","dropSpecialLetterItem",itemId,itemCount,letterDataJson)
								grid:clear();
							end
						else
						--	mob:dropItem(itemId,itemCount)
						end 
					end 
				end 
			end 
		end
		mob:dropBagsAll();
		mob:kill();
	end 
	return true
end

--获取野人信纸内容
function F3203_GetLetterStr(mob,reasonType, mobName)
	--标题（可配置）
	local titles = {35001,35002,35003}    
	--内容1（可配置）   
	local contents1 = {35011,35012,35013}
	--内容2（可配置）
	local contents2 = {35021,35022,35023}
	--饥饿提示（可配置）
	local contents3 = {35031,35032,35033}
	--疲劳提示（可配置）
	local contents4 = {35041,35042,35043}

	local letterDataJson = ""
	local player = mob:getTamedOwner()
	if player then 
		local randomIndex = math.random(1,9)
		local title = GetS(titles[math.random(1,#titles)],player:getNickname())
		local content = ""
		local content1 = GetS(contents1[math.random(1,#contents1)])
		local content2 = GetS(contents2[math.random(1,#contents2)])
		local content3 = GetS(contents3[math.random(1,#contents3)])
		local content4 = GetS(contents4[math.random(1,#contents4)])
		if reasonType == 1 then 
			--吃不饱
			content = content1 .. content2 .. "\r" .. content3
		else
			--睡不好
			content = content1 .. content2 .. "\r" .. content4
		end 
		local authorName = mobName
		local letterData = 
		{
			title = title, 
			context = content,
			authorName = authorName,
			canSave  = 0,
		}
		letterDataJson = JSON:encode(letterData)
	end 
	return letterDataJson
end

local function setIceBaseHeadBreastBackModelShow(mob,num)
	-- body
	local head = string.format("%s%d","head_base_",num)
	mob:showSkin(head,true)
	local breast = "breast_base_1"--string.format("%s%d","breast_base_",num)
	mob:showSkin(breast,true)
end

local function setIceProHeadBreastBackModelShow(mob,num,back_num)
	-- body
	local head = string.format("%s%d","head_pro_",num)
	mob:showSkin(head,true)
	local breast = string.format("%s%d","breast_pro_",num)
	mob:showSkin(breast,true)
	local back = string.format("%s%d","back_pro_",back_num)
	mob:showSkin(back,true)
end

local function setIceFace(mob,num)
	local face = string.format("%s%d","face",num)
	mob:showSkin(face,true)
	return face
end

local function MobIceCommonModel(mob)
	mob:showAllSkins(false)
	mob:showSkin("body",true)--通用
	mob:showSkin("shoe_base1",true)--通用
	--mob:showSkin("face1",true)--通用
	mob:showSkin("leg_base1",false)--通用
end

--[[
	0 PROFESSION_NOT,
	1 PROFESSION_WOODCUTTER,			//樵夫
	2 PROFESSION_MELEE_GUARD,			//近战守卫
	3 PROFESSION_FARMER,				//农夫
	4 PROFESSION_REMOTE_GUARD,		//远程守卫
	5 PROFESSION_HELPER,				//助手
	6 PROFESSION_HUNTER,              //猎人 和比远程守卫多拾取的功能
	7 PROFESSION_ARCHITECT,           //建造师
]]
local girl = 0
local emoji 
-- 冰原野人初始化形象
local function MobIceModleinit(mob,profession)
	--local girl = 1
	--local girl = math.random(0,1)
	MobIceCommonModel(mob)
	if 0 == profession  then
		--基础 base 1boy 2girl
		setIceBaseHeadBreastBackModelShow(mob,1 + girl)
		if mob:getDefID() == 3234 then --基础冰原萌宝
		 	emoji = setIceFace(mob,1 + girl)
		else
		 	emoji = setIceFace(mob,7 + girl)
		end
	elseif 1 == profession  then
		--樵夫 1boy 2girl --背包有1 2
		setIceProHeadBreastBackModelShow(mob,1+ girl,1) 
		emoji = setIceFace(mob,3 + girl)
	elseif 2 == profession then
		--守卫 3boy 4girl --背包只有3
		setIceProHeadBreastBackModelShow(mob,3+ girl,3)
		emoji = setIceFace(mob,3 + girl)
	elseif 3 == profession  then
		--农夫 5boy 6girl --背包只有6
		setIceProHeadBreastBackModelShow(mob,5+ girl,6)
		emoji = setIceFace(mob,5 + girl)
	elseif 5 == profession then
		-- 助手
		local head = string.format("%s%d","head_base_",1+girl)
		mob:showSkin(head,true)
		local breast = string.format("%s%d","breast_base_",1+girl)
		mob:showSkin(breast,true)
		local back = string.format("%s%d","back_pro_",5)
		mob:showSkin(back,true)
		emoji = setIceFace(mob,1 + girl)
	elseif 6 == profession then
		--猎人 7boy 8girl --背包只有8
		setIceProHeadBreastBackModelShow(mob,7+ girl,8)
		emoji = setIceFace(mob,1 + girl)
	elseif 7 ==profession  then
		--工人 9boy 10girl --背包只有9
		setIceProHeadBreastBackModelShow(mob,9+ girl,9)
		emoji = setIceFace(mob,7 + girl)
	end
	AIFunctionMgr:setIceGirl(mob,girl)
	AIFunctionMgr:seticedefaultEmoji(mob,emoji)
	girl= girl+1
 	if(girl>=2) then
		girl = 0
	end
end

local  function Villager_Init(mob,profession)
	MobIceModleinit(mob,profession)
	--随机发型
	setRandHair(mob)
	--随机发型颜色
	setRandHairColor(mob)
	AIFunctionMgr:setDemand(mob,4)--需要驯服
	AIFunctionMgr:setFoodByTamed(mob,12534) --香煎兔肉
end

function Villager_Interact(mob, player)
	local itemid = player:getCurToolID()
	local curblockid, userdata = Color2BlockInfo(mob:getColor(), 600)
	if itemid>=11501 and itemid<=11515 and curblockid-600 ~= itemid-11500 then
		return true
	end
	return false
end

--冰原村民
function F3233_Init(mob)
	Villager_Init(mob,0)
end

function F3233_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--冰原野萌宝 
function F3234_Init(mob)
	Villager_Init(mob,0)
end

function F3234_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--野生冰原护卫村民 
function F3235_Init(mob)
    mob:addInitEquip(5, 12003, -1, 0)
	AIFunctionMgr:setProfession(mob, 2)
	Villager_Init(mob,2)
end

function F3235_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--野生冰原猎人村民 
function F3236_Init(mob)
	mob:addInitEquip(5, 12050, -1, 0) --猎人·弓
	AIFunctionMgr:setProfession(mob, 6)
	Villager_Init(mob,6)
end

function F3236_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--野生冰原樵夫村民 
function F3237_Init(mob)
	mob:addInitEquip(5, 11002, -1, 0) --石斧
	AIFunctionMgr:addItemToBags(mob, 11022,1) --石铲
	AIFunctionMgr:addItemToBags(mob, 11406,2) --落叶松树种子
	AIFunctionMgr:setProfession(mob, 1)
	Villager_Init(mob,1)
end

function F3237_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--野生冰原工人村民 
function F3238_Init(mob)
	mob:addInitEquip(5, 11613, -1, 0) --建造师·石质创造锤 item  11613
	AIFunctionMgr:setProfession(mob, 7)
	Villager_Init(mob,7)
end

function F3238_Interact(mob, player)
	return Villager_Interact(mob,player)
end


--冰原助手3239初始化
function F3239_Init(mob)
	--助手
	mob:addInitEquip(5, 800, -1, 0)
	AIFunctionMgr:setProfession(mob, 5)
	Villager_Init(mob,5)
end

function F3239_Interact(mob, player)
	return Villager_Interact(mob,player)
end

--纪念包裹AI
function F3203_SetAi(actor)
	actor:setAiInvulnerableProb(1);--新增ai 1/1概率闪避
end


--- F3870_Init 猴子
-- @param mob 
function F3870_Init(mob)
	if math.random(0,1000) < 30 then
		local itemid = RandomUseSpecialItem(3870)
		if itemid > 0 then
			mob:addInitEquip(5, itemid, -1, 0) -- 0百分百掉道具
		end
	end
end

local bananaId = 11599
local starBananaId = 11600

function F3870_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAIThief(4, 1.3, 400)
	actor:addAIBananaFan(4, 1.0, 200)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetHurtee(4, true)
	actor:addAiTaskAtk(3, 0, true, 1.3) --近战攻击
	actor:addAiTaskWander(7, 1.0, 120, true)
	actor:addAIClimbTree(4, 1.0, 200)
	actor:addAiMate(2, 1.0, 1, 1, 822) --繁殖
	actor:addAiTaskTempt(3, 1.0, 11534, false)--被饲料吸引
	actor:addAiTaskTempt(3, 1.0, 11535, false)--被饲料吸引

end

function F3870_AttackEntityAsMob(mob, actor)
	mob:setFlagBit(ACTORFLAG_AI_ESCAPE, true)
	AIFunctionMgr:stealItem(actor, mob)
	return true;
end

local function ReportMonkeyTamedResult(tamed)
	if not AccountManager then 
		return
	end 
	local worldDesc = AccountManager:getCurWorldDesc()
	if not worldDesc then 
		return
	end 
	
	local mapid    = 0
	if worldDesc.fromowid > 0 then 
		mapid = worldDesc.fromowid
	else
		mapid = worldDesc.worldid
	end
	local isOwnMap = 0--是否自己的地图 （1自己的地图 0别人的地图）
	if AccountManager:getMultiPlayer() == 0 or IsRoomOwner() then
		if worldDesc.realowneruin == AccountManager:getUin() then
			isOwnMap = 1
		end 
	end 
	local mapType  = 0--地图类型（冒险0 创造1 极限冒险2 创造转生存3 编辑4 编辑转玩法5 高级冒险6）
	mapType   = worldDesc.worldtype
	--游戏模式 （单机""single"" 联机""multi"" ）
	local gameMode  = AccountManager:getMultiPlayer() > 0 and "multi" or "single"
	local gameLabel = 7 --label 1=冒险, 2=创造, 3=对战, 4=电路, 5=解密, 6=跑酷, 7=其它"
	gameLabel = worldDesc.gameLabel 
	local monsterid = 3870 --猴子
	-- 3驯服成功 4失败
	local behavior  = 4
	if tamed then 
		behavior = 3
	end 
	local eventid  = 1109
	statisticsGameEventNew(eventid,tostring(mapid),tostring(isOwnMap),tostring(mapType),gameMode,tostring(gameLabel),tostring(monsterid),tostring(behavior))
end

function F3870_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getCurToolID() ~= 0 then
		if mob:GetMotionType() == IDLE or mob:GetMotionType() == BANANAFAN then
			if starBananaId == itemid then
				AIFunctionMgr:discardEquipItem(mob, 5)
				player:shortcutItemUsed()
				mob:getBody():playAnim(SEQ_MOBEAT)
			else
				return false
			end
		else
			return false
		end
	end

	local tamed = false
	if bananaId == itemid then
		tamed = TameAnimal_Interact(mob, player, itemid, 3871, 7);
		ReportMonkeyTamedResult(tamed)
	elseif starBananaId == itemid then
		tamed = TameAnimal_Interact(mob, player, itemid, 3871, 3);
		ReportMonkeyTamedResult(tamed)
	else
		ReportMonkeyTamedResult(false)
		return false
	end


	return true
end


function F3871_Init(mob)
end

local whistleID = 12580
function F3871_Interact(mob, player)
	local itemId = player:getCurToolID()
	if bananaId == itemId  or starBananaId == itemId then
		AIFunctionMgr:rotateFaceToActor(mob, player)
		mob:getBody():playAnim(SEQ_THANKS)
		player:shortcutItemUsed()
		local foodDef = DefMgr:getFoodDef(itemId)
		if foodDef then
			mob:getAttrib():addHP(foodDef.HealAnimal)
		end
		return true
	--[[elseif whistleID == itemId then
		if  mob:getTamedOwnerID() == player:getUin()  then
			if (player:getFlagBit(16) == false and not mob:getAIToppleOver()) or mob:getAIToppleOver() then
				mob:setAIToppleOver(not mob:getAIToppleOver())
			end
			return true
		end	]]
	end
	if  mob:getTamedOwnerID() == player:getUin() then
		local param =  {mobID = mob:getObjId(), ownerID = player:getUin()}
		player:InteractMobPack("MobInteractBackpack", JSON:encode(param), mob:getObjId())
		return true
	end
	return false
end

function F3871_SetAi(actor)
	actor:showSkin('part1', true);
	actor:addAITaskSitbyItem(0, 2)
	actor:addAiTaskSwimming(1)
	actor:addAiMate(2, 1.2, 1, 1, 822)
	actor:addAiTaskFollowOwner(6, 2.0, 1000,200)
	actor:addAIBananaFanTamed(7, 1.0, 100)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(3)
	actor:addAiTaskTargetOnwnerHurter(3)
end

--豹子
function F3872_SetAi(actor)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskBreakDoor(1)
	actor:addAiTaskPanic(1, 3.25,0.0001)
	--actor:addAIJumpAttack(1, 1, 5, 1000003, 1.3, 20, 5)
	--actor:addAiTaskAtk(2, 0, false, 1.0)
	actor:addAILeopardAtk(2, false, 1, 5, 1000003, 1.3, 3.0, 20)
	actor:addAiTaskMoveTowardsRestriction(4, 1.0)
	actor:addAiTaskWander(6, 1.0)
	actor:addAiTaskWatchClosest(7, 800)
	actor:addAiTaskLookIdle(7)
	actor:addAiTaskTargetHurtee(1, true)
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)

	actor:addAIWarning(1, 2, 200, 20);--警戒
end

--虚空豹子
function F3268_SetAi(actor)
	F3872_SetAi(actor)
end

--毒素野人祭司
function F3873_SetAi(actor)
	actor:setSunHurt(true)

	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,11589,1,0,1) --发射投射物（重力结晶）
	--actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500,11590,1,0,1)
	--actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--治疗野人祭司
function F3874_SetAi(actor)
	actor:setSunHurt(true)

	--actor:addAIEvade(1, 2.0, 10, 3)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,11590,1,0,1) --发射投射物（重力结晶）
	--actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500,11590,1,0,1)
	--actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--雷电野人祭司
function F3875_SetAi(actor)
	actor:setSunHurt(true)
	--actor:addAIEvade(1, 2.0, 10, 3)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,11588,1,0,1) --发射投射物（重力结晶）
	--actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500,11590,1,0,1)
	--actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--火焰野人祭司
function F3876_SetAi(actor)
	actor:setSunHurt(true)
	--actor:addAIEvade(1, 2.0, 10, 3)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,11587,1,0,1) --发射投射物（重力结晶）
	--actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500,11590,1,0,1)
	--actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--野人祭司
function F3877_SetAi(actor)
	actor:setSunHurt(true)
	--actor:addAIEvade(1, 2.0, 10, 3)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskWizardProjectileAttack(3, 1.4, 80, 60, 1500,11586,1,0,1) --发射投射物（重力结晶）
	--actor:addAiTaskProjectileAttack(3, 1.0, 80, 60, 1500,11590,1,0,1)
	--actor:addAiTaskWizardAttack(2, 0.2, 0, 0, 20, 60, 160, 46, 1, 15);--第2个参数开始是:吸过去速度, 掉血数值, 攻击范围(设置为0则读取可视范围), 定住Tick, 持续Tick, 反禁锢Tick, buffid, bufflevel, 概率
	actor:addAiTaskWizardFly(4, 50, 16, 0, 1000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end

--冰野人祭司
function F3135_SetAi(actor)
	actor:setSunHurt(true)
	--actor:addAIEvade(1, 2.0, 10, 3)
	actor:addAiTaskTargetHurtee(1, false) --自己被打反击
	actor:addAiTaskTargetNearest(2, 0, true, 0.0)
	--actor:addAiTaskTargetHurtee(1, true)
	--actor:addAiTaskWizardAttack(2, 1.5, 46, 1);--第3个是攻击附带buffID
    actor:setOxygenNeed(false) -- 是否需要氧气
	actor:addAiTaskIceWizardProjectileAttack(3, 1.4, 80, 60, 1500,11669,11670,1,1037001,1038002,1) --发射投射物
	actor:addAiTaskIceWizardFindActor(4, 15);--第二个是多少秒
	actor:addAiTaskIceWizardFly(5, 40, 30, -30, 3000);--第二个是多少个tick寻路一次，第三个是高度最高限制，第四个是高度最低限制，第五个范围限制
end


function F3135_OnDie(mob)

end

function F3517_OnDie(mob)

end

--宠物
--新写的ai
function F10001_SetAi(actor)
	--寻路是否要避水
	--[[
	对应的动作id
	//tolua_begin
	enum
	{
	SEQ_STAND = 0,
	SEQ_WALK,
	SEQ_ATTACK,
	SEQ_DIE,
	SEQ_JUMP,
	SEQ_IDLEACTION,
	SEQ_IDLEACTION2,
	SEQ_BEHIT,
	SEQ_LAYDOWN,
	SEQ_SITDOWN,
	]]--
	actor:addIdleAnimal(114, 220)--添加休闲动作
	actor:addIdleAnimal(115, 220)--添加坐下动作
	actor:addIdleAnimal(116, 220)--添加趴着动作
	actor:addIdleAnimal(117, 220)--添加开心动作
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAiTaskSwimming(1)
	--宠物跟随主人
	actor:addAiTaskPetFollowOwner(1, 2.8, 100)
	--当没有主人的时候四处游荡
	actor:addAiTaskPetWander(7, 1.0)

	--随机盯着近处的人看
	actor:addAiTaskWatchClosest(9, 600)
	--随机到处看看
	actor:addAiTaskLookIdle(9)
end
function F10002_SetAi(actor)
	F10001_SetAi(actor)
end
function F10003_SetAi(actor)
	F10001_SetAi(actor)
end
function F10004_SetAi(actor)
	F10001_SetAi(actor)
end
function F10005_SetAi(actor)
	F10001_SetAi(actor)
end
function F10006_SetAi(actor)
	F10001_SetAi(actor)
end
function F10007_SetAi(actor)
	F10001_SetAi(actor)
end
function F10008_SetAi(actor)
	F10001_SetAi(actor)
end
function F10009_SetAi(actor)
	F10001_SetAi(actor)
end
function F10010_SetAi(actor)
	F10001_SetAi(actor)
end
function F10011_SetAi(actor)
	F10001_SetAi(actor)
end
function F10012_SetAi(actor)
	F10001_SetAi(actor)
end

--家园牧场生物ai，牧场生物id不定，这里就不单独用monsterid来命名了
function FHomeRanch_SetAi(actor)
	actor:addIdleAnimal(114, 140)--添加休闲动作
	actor:addIdleAnimal(117, 140)--添加开心动作
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAIHomeRankWander(7, 1.0, 40)

	actor:addAiTaskWatchClosest(9, 400)
	actor:addAiTaskLookIdle(9)
end
--家园NPC ai
function FHomeNpc_SetAi(actor,npcType,moveX,moveZ,scope)
	if actor:getMonsterDef() and actor:getMonsterDef().ID == GetBusinessManInfoNpcId() then
	end
	actor:addAIPosWander(2,moveX,moveZ,scope,1.1,50)
end

-- 家园果实NPC埋点上报
function FHomeChestNpc_ReportEvent(actor)
	if actor:getMonsterDef() and actor:getMonsterDef().ID == GetHomeChestNpcId() then
	end
end

--炎炎蟹 设置行为初始化
function F3881_Init(mob)
	mob:setImmuneAttackType(ATTACK_FIRE)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setWaterMove",false)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setLavaMove",true)
end

--炎炎蟹 设置行为
function F3881_SetAi(actor)
--[[	--不能飞
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",true)
	--
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(1, true) --自己被打反击
	actor:addAiTaskAtk(3, 0, true, 1.0) --近战攻击
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	
	actor:addAIGetSpecialAttackattr(9, 1058, 20, 1); --第二个参数是blockid,然后是每tick的概率分母,第三个参数是速度

	--actor:addAiMate(2, 1.0)
	--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
	actor:addAiTaskTempt(3, 1.0, 1020, false)
	--闲逛
	actor:addAiTaskWander(8, 1.0, 120, false, 1)
	--看附近玩家
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)]]
	actor:addAiTaskTempt(3, 1.0, 11535, false)
	actor:addAiTaskLavaGrab(10, 1)
end

--炎炎蟹喂食驯服
function F3881_Interact(mob, player)
	local itemid = player:getCurToolID()
	-- if itemid == 12526 then
	-- 	mob:addAITaskDissolvedByItem(0, 2, 30) 
	-- 	return true;
	-- else
		TameAnimal_Interact(mob, player, 12521, 3896, 3);  --驯服物品 驯服后变成的生物 概率
		TameAnimal_Interact(mob, player, 12705, 3896, 3);  --驯服物品 驯服后变成的生物 概率
		TameAnimal_Interact(mob, player, 12706, 3896, 3);  --驯服物品 驯服后变成的生物 概率
		local itemid = player:getCurToolID()
		if itemid == 12521 or itemid == 12705 or itemid == 12706 then
			return true
		end
		return false
	
end

function F3896_Init(mob)
	mob:setImmuneAttackType(ATTACK_FIRE)
end

function F3882_Init(mob)
	mob:setImmuneAttackType(ATTACK_FIRE)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setWaterMove",false)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setLavaMove",true)
end

function F3882_SetAi(actor)
	--寻路是否要避水
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	--参数 优先级，设置了这个ai表示会游泳
	actor:addAITaskToppleOver(0)
	actor:addAITaskSitbyItem(0, 2)
	actor:addAiTaskSwimming(1)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiLeapAtTarget(3, 40.0, 200, 400)
	--参数 优先级，被攻击对象的类型，是否追踪,速度因子
	actor:addAiTaskAtk(4, 0, true, 2.0)
	actor:addAiTaskFollowOwner(5, 2.0, 1000,200)
	actor:addAiMate(6, 1.0)
	actor:addAiTaskWander(7, 1.0)
	--参数 优先级，喜欢的食物，距离
	actor:addAiTaskBeg(8, 11302, 800)
	actor:addAiTaskWatchClosest(9, 600)
	actor:addAiTaskLookIdle(9)
	actor:addAiTaskTargetOnwnerHurtee(1)
	actor:addAiTaskTargetOnwnerHurter(2)
	actor:addAiTaskTargetHurtee(3, true, false)

end

--雀莺 设置行为初始化
function F3883_Init(mob)
	mob:setImmuneAttackType(7)
	ActorComponentCallModule(mob,"ClientActorFuncWrapper","setCanFly",false)
--	mob:setImmuneHurtType(ATTACK_FIRE)
--	mob:setWaterMove(false)
--	mob:setLavaMove(true)
end

--雀莺 设置行为
function F3883_SetAi(actor)
--[[	--不能飞
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",false)
	--
	actor:addAiTaskSwimming(0)
	actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
	actor:addAiTaskTargetHurtee(1, true) --自己被打反击
	actor:addAiTaskAtk(3, 0, true, 1.0) --近战攻击
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
	
	actor:addAIGetSpecialAttackattr(9, 1058, 20, 1); --第二个参数是blockid,然后是每tick的概率分母,第三个参数是速度

	--actor:addAiMate(2, 1.0)
	--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
	actor:addAiTaskTempt(3, 1.0, 1020, false)
	--闲逛
	actor:addAiTaskWander(8, 1.0, 120, false, 1)
	--看附近玩家
	actor:addAiTaskWatchClosest(6, 600)
	actor:addAiTaskLookIdle(7)]]
	actor:addAiTaskOriole(10, 1)
	--actor:addAiTaskFlyBeg(2, 11534, 800, false);
end


function F3884_SetAi(actor)
	--[[	--不能飞
		ActorComponentCallModule(actor,"ClientActorFuncWrapper","setCanFly",false)
		--
		actor:addAiTaskSwimming(0)
		actor:addAiTaskFollowDirection(1, 1.1)--寻找路径方块
		actor:addAiTaskTargetHurtee(1, true) --自己被打反击
		actor:addAiTaskAtk(3, 0, true, 1.0) --近战攻击
		ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true) --寻路避水
		
		actor:addAIGetSpecialAttackattr(9, 1058, 20, 1); --第二个参数是blockid,然后是每tick的概率分母,第三个参数是速度
	
		--actor:addAiMate(2, 1.0)
		--引诱，参数  优先级，速度因子，种子id，是否会被角色移动惊吓
		actor:addAiTaskTempt(3, 1.0, 1020, false)
		--闲逛
		actor:addAiTaskWander(8, 1.0, 120, false, 1)
		--看附近玩家
		actor:addAiTaskWatchClosest(6, 600)
		actor:addAiTaskLookIdle(7)]]
		--actor:addAiTaskOriole(10, 1)
		actor:addAiTaskFlyFollow(1, 2.0, 200, 1000)
		--actor:addAiStayBlock(16, 2.0, 600);
		--actor:addAiTaskRandFly(5, 20, 16, 2000, 30);
end


function F3884_Interact(mob, player)
	local itemid = player:getCurToolID()
	if mob:getTamed() and mob:isBreedItem(itemid) == 0 then	
		if  mob:getTamedOwnerID() == player:getUin() then
			mob:setFallGround(not mob:getFallGround());
			return true
		end
	end
	return false
end	

function F3884_ExpressionFeedback(mob)
	--雀莺对主人的表情反馈
	ActorComponentCallModule(mob,"EffectComponent","clearEffectForTrigger")
	
	mob:playAnimById(100604)
	ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3883.tweet", 1, 1, 2)

	local effects = {"110086_happy","110086_sing","110086_doubt","110086_cry","110086_angry"}
	local effectIndex = math.random(1,#effects)
	if effectIndex > #effects then 
		effectIndex = #effects
	elseif effectIndex < 1 then 
		effectIndex = 1
	end 
	local effect = effects[effectIndex]
	ActorComponentCallModule(mob,"EffectComponent","playBodyEffectForTrigger",effect)

	local objId = mob:getObjId();
	threadpool:delay(3.5, function()
		if CurWorld then
			local newmob = GetWorldActorMgr(CurWorld):findMobByWID(objId)
			if newmob then
				newmob:playAnimById(100100)
				ActorComponentCallModule(newmob,"EffectComponent","stopBodyEffectForTrigger",effect)
			end
		end
	end)
end

--雀莺喂食驯服
function F3883_Interact(mob, player)
	local itemid = player:getCurToolID()
	if itemid == 12526 then
		mob:addAITaskDissolvedByItem(0, 2, 30) 
		return true;
	else
		TameAnimal_Interact(mob, player, 12603, 3884, 3);  --驯服物品 驯服后变成的生物 概率
		local itemid = player:getCurToolID()
		if itemid == 12603 then
			return true
		end
		return false
	end
end


-- 符文怪(3892)ai复用野人ai
function F3892_SetAi(actor)
	actor:addAiTaskSwimming(1)--4，游泳
	actor:addAiTaskTargetHurtee(1, true)--1，受到攻击后反击
	actor:addAiTaskFollowDirection(2, 1.1)--寻找路径方块--3
	actor:addAiTaskTargetNearest(2, 0, true, 0.0, 0, 1)--1，主动攻击玩家
	actor:addAiTaskAtk(2, 0, false, 1.0)--3，近战攻击
	actor:addAiTaskWander(6, 1.0)--1
	actor:addAiTaskWatchClosest(7, 800)--2，看附近玩家
	actor:addAiTaskLookIdle(7)--3，到处看看
	actor:addAIWarning(1, 2, 200, 20);--警戒--1
end

function F3892_AttackEntityAsMob(mob, actor)
	return true
end

function F3892_Init(mob)
end


--虚空幻影,一阶段
function F3515_SetAi(actor)
	-- 默认不做说明情况下： 距离单位：100为单位一个格子长度，时间单位：毫秒， 速度：正常移动为1速率的倍率
	local param = {
		-- boss出现
		appeal_time = 1000, -- 启动祭坛等待多少后boss入场，期间播放特效
		appeal_clear_range = 2000, --清理boss出生点水平半径范围方块
		appeal_clear_height = 1000, -- 清理boss出生点以上的高度的方块（小于等于appeal_clear_range）
		appeal_clear_floor = 500, -- 清理boss出生点加上这个高度的方块开始算起 清除方块
		appeal_height_distance = 1000, -- 出生后朝上方飞行距离
		appeal_height_speed = 5, -- 向上飞速率
		appeal_up_end_time = 2000, -- 飞到上空后多久进入警戒

		-- 防止原地不动
		stand_time_out = 10000, -- 超过该时间boss还在一个位置附近没有朝目标前进，触发传送

		-- 火焰冲刺
		rush_pre_range_1 = 2000, -- boss开始攻击时远离玩家的最小范围（水平）100为单位一个格子长度 实际距离是2000<= d <= 2000√2
		rush_pre_speed_1 = 4, -- 降落到目标点等高得速度倍率（正常移动为1）
		rush_pre_time_1 = 1800, -- 火焰冲刺前得蓄力时间 （时间都以毫秒为单位)
		rush_select_target_time_1 = 600, -- 蓄力倒计时多少选取目标
		rush_distance_1 = 3000, -- 冲刺最大距离， 
		rush_speed_1 = 7, --冲刺速度倍率（正常移动为1）
		rush_speed_1_1 = 9, -- 只剩一个火球时，冲刺速率
		rush_end_time_1 = 1300, -- 冲刺完成后停留时间
		rush_damage_1 = 40, -- 冲刺伤害
		rush_attack_range_1 = 500, --冲击攻击范围 ，boss朝向纳入的范围
		rush_attack_width_1 = 400, -- 冲击宽度 ，boss朝向运动的宽度
		rush_knockback_1 = 300, -- 击退距离
		rush_knockup_1 = 10, -- 击飞高度
		rush_destory_block_range_x_1 = 100, -- 包围盒加上这个范围破坏途径得方块
		rush_destory_block_range_y_1 = 100, -- 包围盒加上这个范围破坏途径得方块

		fire_size_x = 200, -- 火焰宽直径 (boss途经过程以boss为中心得x)半径
		fire_size_z = 200, -- 火焰长度 (boss途经过程以boss为中心得z)
		
		rush_cd_1 = 6000, -- 高血量冲刺cd
		rush_cd_2 = 4000, -- 低血量冲刺cd

		-- 快速冲刺(低血量)，下砸
		rush_pre_height_2 = 1000, -- boss飞到玩家正上方的高度
		rush_pre_speed_2 = 5, -- 飞向目标上方速率
		rush_pre_time_2 = 1000,
		rush_distance_2 = 3000,
		rush_speed_2 = 5,
		rush_end_time_2 = 2000,
		rush_damage_2 = 50,
		rush_attack_range_2 = 600,
		rush_knockback_2 = 100, -- 击退距离
		rush_knockup_2 = 10, -- 击飞高度
		rush_destory_block_range_x_2 = 0, -- 包围盒加上这个范围破坏途径得方块
		rush_destory_block_range_y_2 = 100, -- 包围盒加上这个范围破坏途径得方块

		low_hp = 60, -- 数值为0 - 100 ，表示低于该百分比值则为低血量状态

		--冲击波
		shoke_wave_range = 600, -- 冲击波以boss落点为中心的半径
		shoke_wave_damage = 35, --冲击波伤害
		shoke_wave_height = 100, -- 以boss落点高度，多高可以被击飞 ，100一格子高度
		shoke_wave_knockup = 100, -- 击飞高度（50大概一格高度）
		shoke_wave_knockback = 100, -- 击退距离
		shoke_wave_buffLv = 5, -- 灼烧buff等级
		shoke_wave_buffId = 1009, --灼烧buff
		
		-- 补充火球
		add_fire_pre_time = 2000, -- 冲刺完火球为0时，准备间隔多久触发回到地点生成火球
		add_fire_speed = 3, -- 回归补充火球时移动倍率
		add_fire_duration_1 = 5000, -- 到位置后补充火球所需时间
		add_fire_duration_2 = 6000, -- 低血量 到位置后补充火球所需时间
		add_fire_end_time = 2000, -- 补充完火球多少时间进入警戒

		-- 传送躲避
		tp_elude_time = 3000, -- 触发技能的伤害累计时间
		tp_elude_damage = 200,  --触发技能的累计伤害量
		tp_elude_duration = 1000, -- 蓄力时间，之后开始传送
		tp_elude_guard_time = 1000, -- 传送出来后多久进入警戒
		tp_elude_min_range = 1000, -- 离boss 当前位置 的最小距离
		tp_elude_max_range = 3000, -- 离boss 出生点 得最大距离
		tp_elude_effect_time = 3500, -- 传送门特效存在时间
		tp_elude_effect_distance = 300, -- 传送门出现距离，相对于boss面朝方向，起点是boss身后
		tp_elude_effect_show_range = 100, -- 传送门特效对于出现位置，多少格子范围可视 ，

		-- 传送吸血
		tp_hp_damage = 10, --触发技能的累计伤害量 百分比0 到 100，
		tp_hp_speed = 100, -- 吸取速度
		tp_hp_time = 5000, -- 吸血持续时间
		tp_hp_duration = 1000, -- 蓄力时间，之后开始传送
		tp_hp_rush_speed = 10, -- 朝玩家前扑速率
		tp_hp_buffTime = 5000, -- 减速buff时间
		tp_hp_buffId = 203, --减速buff
		tp_hp_buffLv= 1, -- buff等级
		tp_hp_start_time = 500, -- 前扑到玩家施加buff后多久开始吸血
		tp_hp_suck_hp = 30, -- 每次吸血多少 体力/血量
		tp_hp_suck_time = 5, -- 总共吸取多少次
		tp_hp_suck_hz = 1000, -- 吸取频率，1秒吸取一次
		tp_hp_suck_transform = 2, -- 吸取转换倍率
		tp_hp_level_range = 400, -- 玩家离开多远可以打断boss吸血
		tp_hp_defense_buffId = 210001, -- Boss增加防御的buff
		tp_hp_defense_buffLv = 1,
		tp_hp_defense_buffTime = 5000,
		tp_hp_show_range = 200, -- 出现的传送门离玩家的范围
		tp_hp_offset = 150, -- 身体前倾偏移量
		tp_hp_player_range = 400, -- 吸取该范围内的所有玩家
		tp_hp_player_height = 100, -- 上下高度

		-- 逃跑
		flee_hp = 10, -- 触发逃跑的血量百分比
		flee_time = 5000, -- 播放逃跑过程时间
	}

	local str = JSON:encode(param) or ""
	if actor.addAIVacantBoss then 
		actor:addAIVacantBoss(1, str) --boss怪ai
	end
	FesivialActivity:EndTiangou(-1)
end

--虚空幻影2
function F3516_SetAi(actor)



	
	-- 距离单位：100为单位一个格子长度，时间单位：毫秒， 速度：正常移动为1速率的倍率
	local param = {
	   BossInitHp = 4000, --boss出生时的血量
	   BossAttackStartDistance = 7000, --当多少距离内有人的时候触发boss正式开始
       --双手捶地--
       BeatGroundDistance = 900,--触发距离（玩家与boss的距离）10米,其中6米是怪物宽度半径
       BeatGroundFindTime = 500,--寻找目标时间
       BeatGroundPreTime = 500, --蓄力时间
       BeatGroundContinueTime = 500, --预警时间
       BeatGroundAttackDis = 100, --命中攻击距离2米
       BeatGroundAttackHeight = 900, --命中矩形长度3米（boss向前距离）
       BeatGroundAttackWidth = 950, --命中矩形长度4米宽度
       BeatGroundAttackBack = 250, --击退冲量(向量或力量)距离值
       BeatGroundAttackHp = 35, --击退伤害的血量
       BeatGroundAttackEndTime = 2000, --攻击后的硬直时间
       BeatGroundFireTime = 5000, --着火buff时间
	   BeatGroundSkillCD = 3000, --cd时间3秒
       --丢石头--
       throwStoneDistance = 8000, --触发距离80米（玩家与boss的距离）
       throwStoneRadius = 350, --攻击生物的范围半径2米
       throwStoneAttackBack = 250, --击退冲量(向量)距离值
       throwStoneAttackBlockWidth = 600, --命中后生成的火焰方块范围（长宽）
       throwStoneSkillCD = 3000, --cd时间3秒
	   throwStoneAttackHp = 30, --击退伤害的血量
       throwStoneFireTime = 5000, --生物着火buff时间
       --冲击波--
       ShockWaveAttackAllTime = 3000,-- 触发技能的伤害累计时间
       ShockWaveAttackAllHp = 500, --触发技能的累计伤害量
       ShockWaveAttackPreTime = 2000, --蓄力时间
       ShockWaveAttackDistance = 1000, --其中boss半径6米，再往外3米
       ShockWaveAttackBack = 250, --击退冲量(向量)距离值
       ShockWaveAttackHp = 20, --击退伤害的血量
       --召唤小怪--
	   CallEarthCoreMansBossDistance = 500,--到boss5格内停止移动
       CallEarthCoreMansAllHp = 1000, --触发技能的累计伤害量
       CallEarthCoreMansPreTime = 2000, --蓄力时间
       CallEarthCoreMansDistance = 6000, --小怪在这个范围随机召唤
       CallEarthCoreMansNum = 3, --小怪召唤数目
       CallEarthCoreMansBeKillAddHp = 250,--boss捶地击杀小怪时的生命回复量
       --召唤boss--
       CallBossAllHp = 1500, --触发技能的血量
       CallBossInitHp = 1500, --召唤出来的boss总血量
       CallBossPreTime = 5000, --蓄力时间5秒
       CallBossLiveTime = 50000, --召唤物存在倒计时
       CallBossRigorTime = 10000, --召唤物被击败时的硬直时间
       CallBossRecoverHp = 1500,  --召唤物存活时的生命回复量
       CallBossSkillCD = 300000, --cd时间300秒		
	}
	local str = JSON:encode(param) or ""
	if actor.addAIVacantBoss2 then 
	    actor:addAIVacantBoss2(1, str) --boss怪ai
	end
	FesivialActivity:EndTiangou(-1)
end

-- 年兽小怪
function F3520_SetAi(actor)
	ActorComponentCallModule(actor,"ClientActorFuncWrapper","setAvoidWater",true)
	actor:addAiTaskSwimming(0) --游泳
	actor:addAiTaskTargetNearest(3, 0, true, 0.0, 0.8) --主动攻击玩家
	actor:addAiTaskAtk(3, 0, true, 1.2) --近战攻击
	actor:addAiTaskLookIdle(6, 60) --左右看看
	actor:addAiTaskWatchClosest(7, 1800) --看附近玩家
	actor:getAttrib():setImmuneToFire(2) -- 免疫火焰伤害
end

--新春宝箱
function F3899_Init(mob)
	threadpool:delay(2,function ()
		mob:playAnimById(100100)
		mob:getBody():playMotion('baoxiang')
	end)
end

function F3899_SetAi(mob)
	mob:setAiInvulnerableProb(1);
end

--觉醒活动购票NPC  啦啦队长克莱尔
function F3900_Interact(mob, player)
	return true;
end

--野生骆驼交互
function F3822_Interact(mob, player)
	local Attrib = mob:getMobAttrib()
    local Food = Attrib:getFood()
	TameAnimal_Interact(mob, player, 12613, 3823, 5--[[math.ceil(Food / 4)]])
	local itemid = player:getCurToolID()
	if itemid == 12613 then
		AIFunctionMgr:rotateFaceToActor(mob, player)
		local Def = DefMgr:getFoodDef(12613)
		Attrib:addFood(Def.AddFood) 
		--print("camel Feed , now Hunger:"..Attrib:getFood())
		return true
	end
	return false
end

--驯服骆驼交互
function F3823_Interact(mob, player)
	local PackHorse = tolua.cast(mob,"ActorPackHorse")
	local itemId = player:getCurToolID()
	if itemId == 12613 then
		AIFunctionMgr:rotateFaceToActor(mob, player)
		player:shortcutItemUsed()
		local Def = DefMgr:getFoodDef(12613)
		mob:getMobAttrib():addFood(Def.AddFood) 
		return true
	end
	if PackHorse:hasSaddle() and PackHorse:isSuitableBox(itemId) ~=0 then
		if PackHorse:hasRightPack() == 0 then
			player:shortcutItemUsed()
			PackHorse:equipPack(itemId,1)
			return true
		elseif PackHorse:hasLeftPack() == 0 then
			player:shortcutItemUsed()
			PackHorse:equipPack(itemId,0)
			return true
		end

	end
	local ret = PackHorse:isCollideWithPack(player)
	local param = nil
	if ret == 1 then
		param =  {mobID = mob:getObjId(), ownerID = player:getUin(),indexPage = 1}
	elseif ret == 2 then
		param =  {mobID = mob:getObjId(), ownerID = player:getUin(),indexPage = 0}
	end
	if param ~= nil then
		--通知商人
		local OwnerID = mob:getMasterObjId()
		if OwnerID ~=0 then   
			local OwnerMob = mob:getActorMgr():findMobByWID(OwnerID)
			if OwnerMob and OwnerMob:getObjType()== OBJ_TYPE_DESERTBUSINESSMAN then
				local OwnerBusInessMan = tolua.cast(OwnerMob, "ActorDesertBusInessMan")
				OwnerBusInessMan:playerInteractGoods(player:getUin())
			end
		end
		
		--打开背包
		player:InteractMobPack("MobInteractBackpack", JSON:encode(param), mob:getObjId())
	elseif mob:getTamedOwnerID()== player:getUin() then
		if PackHorse:hasSaddle() then
			--骑乘
			mob:setSitting(false);
			player:mountActor(mob)
				
		end
	end

	return true
	--end
	--return false
end

function F3823_OnDie(mob)
	if mob then
		--通知商人
		local OwnerID = mob:getMasterObjId()
    	if OwnerID ~=0 then   
			local OwnerMob = mob:getActorMgr():findMobByWID(OwnerID)
			if OwnerMob and OwnerMob:getObjType()== OBJ_TYPE_DESERTBUSINESSMAN then
				local OwnerBusInessMan = tolua.cast(OwnerMob, "ActorDesertBusInessMan")
				OwnerBusInessMan:camelDead(mob:getObjId())
        	end
		end

		mob:dropBagsAll()
		
		if mob:getObjType() == OBJ_TYPE_PACKHORSE then
			local PackHorse = tolua.cast(mob,"ActorPackHorse")
			local LeftPackid = PackHorse:hasLeftPack()
			local RightPackid = PackHorse:hasRightPack()
			if LeftPackid ~= 0 then
				ActorComponentCallModule(mob,"DropItemComponent","dropItem",LeftPackid, 1)
			end
			if RightPackid ~= 0 then
				ActorComponentCallModule(mob,"DropItemComponent","dropItem",RightPackid, 1)
			end
		end
	end
end

--沙漠护卫
function F3213_Init(mob)
	MobModleinit(mob)
	--local id = mob:getObjId();
	-- threadpool:delay(1, function()
	-- 	print("my_debug1111111111111:", CurWorld, id);
	-- 	if CurWorld then
	-- 		local newmob = GetWorldActorMgr(CurWorld):findMobByWID(id)
	-- 		print("my_debug11111111111111112222222:", newmob);
	-- 		newmob:addInitEquip(0, 12221, -1, 0)
	-- 		newmob:addInitEquip(1, 12222, -1, 0)
	-- 		newmob:addInitEquip(2, 12223, -1, 0)
	-- 		newmob:addInitEquip(3, 12224, -1, 0)
	-- 		newmob:addInitEquip(5, 12003, -1, 0)
	-- 	end
	-- end)
	for i =1, 6 do
		mob:showSkin(string.format("%s_%s_%d", eqname[i], "eq", 3),true)  -- 隐藏所有子模
	end

	mob:addInitEquip(0, 12221, -1, 0)
	mob:addInitEquip(1, 12222, -1, 0)
	mob:addInitEquip(2, 12223, -1, 0)
	mob:addInitEquip(3, 12224, -1, 0)
	mob:addInitEquip(5, 12003, -1, 0)
end
--珍珠
function F3521_Interact(mob, player)
	if mob then 
		local itemGetNum = player:getBackPack():addItem(11655, 1)
		if itemGetNum ~= 1 then
			player:dropItem(11655, 1)
		end
		mob:setNeedClear();
	end 
	return true
end

--漂流瓶不能被攻击
function F3230_SetAi(actor)
	actor:setAiInvulnerableProb(1);--新增ai 1/1概率闪避
end

--漂流瓶不能被攻击
function F3231_SetAi(actor)
	actor:setAiInvulnerableProb(1);--新增ai 1/1概率闪避
end

function F3220_Init(mob)
	local initPro = {
		weapon = {
			{2200, 11002},
			{3700, 11003},
			{4350,  11004},
			{6550, 12002},
			{8050, 12003},
			{8850,  12004},
			{9500,  12010},
			{10000,  12063},
		},
		rune = {
			{2000, 601},
			{4000, 701},
			{4500, 1001},
			{6000, 1101},
			{10000, 1501},
		}
	}
	local num = math.random(0, 10000);
	local weaponId = 0;
	for _, value in ipairs(initPro.weapon) do
		if value[1] >= num then
			weaponId = value[2];
			break;
		end
	end
	local runeId = 0;
	for _, value in ipairs(initPro.rune) do
		if value[1] >= num then
			runeId = value[2];
			break;
		end
	end
	--mob:addInitEquip(0, 12216, -1, 0)
	--mob:addInitEquip(1, 12222, -1, 0)
	--mob:addInitEquip(2, 12223, -1, 0)
	--mob:addInitEquip(3, 12224, 501, 0)
	--mob:addInitEquip(5, weaponId, runeId, 0)
	local data = GridRuneItemData:new_local();
	data.rune_id = runeId;
	data.item_id = 11603;
	data.rune_val0 = 1;
	data.rune_val1 = 1;
	
	mob:addInitEquip_byRune(5, weaponId, data, 0);
end

function F3221_Init(mob)
	F3220_Init(mob);
end

function F3912_Interact(mob, player)
	local itemid = player:getCurToolID()

	if itemid == ITEM_SMALL_GLASS_BOTTLE then
		if mob:getFlagBit(ACTORFLAG_AI_MILKING) then
			if mob:getMilkingTimes() > 0 then
				player:playCurToolSound()
				player:shortcutItemUsed()
				mob:setMilkingTimes(mob:getMilkingTimes()-1)
				local num = player:getBackPack():addItem(ITEM_BOTTLED_MILK, 1)
				if num ~= 1 then
					ActorComponentCallModule(player,"DropItemComponent","dropItem",ITEM_BOTTLED_MILK, 1)
				end

				if mob:getMilkingTimes() == 0 then  
					ActorComponentCallModule(mob,"EffectComponent","stopBodyEffect",BODYFX_MILKING)
				end
			else			
				ActorComponentCallModule(mob,"EffectComponent","playBodyEffect",BODYFX_FORBIDDEN)
				ActorComponentCallModule(mob,"EffectComponent","stopBodyEffect",BODYFX_MILKING)
				ActorComponentCallModule(mob,"SoundComponent","playSound","ent.3912.jet", 1.0, 1.0)
			end
		else
			ActorComponentCallModule(mob,"EffectComponent","playBodyEffect","mob_3912_2")
		end
		return true
	end

	return false
end	

function F3912_Init(mob)
	mob:showSkin('part1', true);
	local yak = tolua.cast(mob,"ActorYak")
	if yak then 
		yak:SetMaxMilkCD(1800)   --单位tick
		yak:SetMaxHornGrowCD(6000)  --5分钟
	end
end

function F3915_Init(mob)
	--随机装备
	if not mob:isFirstCreate() then 
		return
	end
	local equipid = {12069,12316}
	local rate = {30,10}
	local randNum = math.random(100)
	local total = 0;
	local isBiger = false;
	for k,v in pairs(rate) do
		total = v+total
		if total > randNum then
			AIFunctionMgr:addItemToBags(mob, equipid[k], 1)--添加进背包
    		AIFunctionMgr:equipWeapon(mob, equipid[k], 1)
    		if equipid[k] == 12316 then  --变大变强 
	            isBiger = true;
	            local dp = 40
	            local att = mob:getAttrib()
	            att:setMaxHP(att:getMaxHP() + dp)
	            att:setHP(att:getHP()+dp)
	        end
			break
		end
	end
	if isBiger then
        mob:setCustomScale(2.1)
    else
        mob:setCustomScale(1.5)
    end
	
end

function F3915_OnDie(mob)

end

function F3910_Interact(mob, player)
	local itemid = player:getCurToolID()

	if itemid == 11226 then
		player:shortcutItemUsed()
		mob:setEaten(true)
		return true
	end

	return false
end	

--虚空雪兔交互
function F3254_Interact(mob, player)
	local itemid = player:getCurToolID()

	if itemid == 11226 then
		player:shortcutItemUsed()
		mob:setEaten(true)
		return true
	end

	return false
end


function F3911_Interact(mob, player)
	local itemid = player:getCurToolID()

	if itemid == 11226 then
		player:shortcutItemUsed()
		mob:setEaten(true)
		return true
	end

	return false
end	

--小雪兔模型缩放
function F3911_Init(mob)
	if mob then
		mob:setCustomScale(0.6)
	end
end

--虚空使教士
function F3917_SetAi(actor)
	voidFantomAi(actor);
end

--虚空使祭司
function F3918_SetAi(actor)
	voidFantomAi(actor);
end

--虚空使大祭司
function F3917_SetAi(actor)
	voidFantomAi(actor);
end

--虚空使AI
function voidFantomAi(actor)
	actor:addAiTaskWatchClosest(9, 600) --看附近的玩家
    actor:addAiTaskLookIdle(10, 60) --左右看看
end

function MobScrpitNodeInit(mob)
	MiniLog("MobScrpitNodeInit")
	if mob:getDef().ID == 3400 then	
		-- local node = Sandbox.SandboxNode.ToNode(mob)
		-- if not node then
		-- 	MiniLog("no node")
		-- 	return
		-- end

		-- MiniLog("create scriptNotify")
		-- -- local notifyNode = Sandbox.SandboxNode.new('CustomNotify');
		-- -- notifyNode.Name = "scriptNotify";
		-- -- notifyNode.parent = node;

		-- MiniLog("create MobDemo")
		-- local Demo = Services.NodeService.CreateScriptNode("MobDemo", mob)
	end
end

--虚空怪物定义
g_voidMonsterDef = {3929,3930,3931,3932,3933};
g_voidMonsterGenWeight = {850,300,350,250,250};

function GetVoidMonsterDef()
	if not g_voidMonsterDef or #g_voidMonsterDef < 1 then
		return ""
	end

	local res = g_voidMonsterDef[1]
	for index = 2, #g_voidMonsterDef do
		res = res .. "," .. g_voidMonsterDef[index]
	end

	return res
end

function GetVoidMonsterWeightDef()
	if not g_voidMonsterGenWeight or #g_voidMonsterGenWeight < 1 then
		return ""
	end

	local res = g_voidMonsterGenWeight[1]
	for index = 2, #g_voidMonsterGenWeight do
		res = res .. "," .. g_voidMonsterGenWeight[index]
	end

	return res
end

--虚空怪物难度定义
g_voidMonsterDifficultDef = {
----难度---队伍列表
	{
		difficultLevel = 1,
		teamInfos = {
			{
				name = "队伍1",
				id = 3917,
				proportion = 100,
				tick = 500,
				monster = {
					{
						ID = 3929,
						count = 2,
					},
					{
						ID = 3931,
						count = 1,
					},
					{
						ID = 3935,
						count = 1,
					},
				}
			},
		},
	},
	{
		difficultLevel = 2,
		teamInfos = {
			{
				name = "队伍2",
				proportion = 65,
				id = 3917,
				tick = 500,
				monster = {
					{
						ID = 3929,
						count = 2,
					},
					{
						ID = 3931,
						count = 1,
					},
					{
						ID = 3935,
						count = 1,
					},
				}
			},
			{
				name = "队伍3",
				proportion = 25,
				id = 3918,
				tick = 600,
				monster = {
					{
						ID = 3929,
						count = 1,
					},
					{
						ID = 3930,
						count = 1,
					},
					{
						ID = 3931,
						count = 2,
					},
					{
						ID = 3935,
						count = 2,
					},
				}
			},
			{
				name = "队伍4",
				proportion = 10,
				id = 3919,
				tick = 700,
				monster = {
					{
						ID = 3929,
						count = 1,
					},
					{
						ID = 3930,
						count = 1,
					},
					{
						ID = 3931,
						count = 1,
					},
					{
						ID = 3932,
						count = 1,
					},
					{
						ID = 3935,
						count = 1,
					},
					{
						ID = 3928,
						count = 1,
					},
				}
			},
		}
	},
	{
		difficultLevel = 3,
		teamInfos = {
			{
				name = "队伍2",
				proportion = 0,
				id = 3917,
				tick = 500,
				monster = {
					{
						ID = 3929,
						count = 2,
					},
					{
						ID = 3931,
						count = 1,
					},
					{
						ID = 3935,
						count = 1,
					},
				}
			},
			{
				name = "队伍3",
				proportion = 75,
				id = 3918,
				tick = 600,
				monster = {
					{
						ID = 3929,
						count = 1,
					},
					{
						ID = 3930,
						count = 1,
					},
					{
						ID = 3931,
						count = 2,
					},
					{
						ID = 3935,
						count = 2,
					},
				}
			},
			{
				name = "队伍4",
				proportion = 25,
				id = 3919,
				tick = 700,
				monster = {
					{
						ID = 3929,
						count = 1,
					},
					{
						ID = 3930,
						count = 1,
					},
					{
						ID = 3931,
						count = 1,
					},
					{
						ID = 3932,
						count = 1,
					},
					{
						ID = 3935,
						count = 1,
					},
					{
						ID = 3928,
						count = 1,
					},
				}
			},
		}
	},
};

function GetSiegeDifficultInfo(siegeMgr)
	if type(siegeMgr) ~= "userdata" or type(siegeMgr.CreateSiegeDifficultInfo) ~= "function" or
		 type(siegeMgr.GetSiegeDifficultInfo) ~= "function" then
		return false;
	end
	if type(g_voidMonsterDifficultDef) ~= "table" then
		return false;
	end

	local difficultCount = #g_voidMonsterDifficultDef;
	siegeMgr:CreateSiegeDifficultInfo(difficultCount);
	for index, difficultInfo in ipairs(g_voidMonsterDifficultDef) do
		local siegeDifficultInfo = siegeMgr:GetSiegeDifficultInfo(index-1);
		siegeDifficultInfo.difficultLevel = difficultInfo.difficultLevel;
		local teamInfos = difficultInfo.teamInfos;
		if type(siegeDifficultInfo.CreateMonsterTeamInfo) ~= "function" or type(siegeDifficultInfo.GetMonsterTeamInfo) ~= "function" then
			return false;
		end
		siegeDifficultInfo:CreateMonsterTeamInfo(#teamInfos);
		for teamIndex, teamInfo in ipairs(teamInfos) do
			local monsterTeamInfo = siegeDifficultInfo:GetMonsterTeamInfo(teamIndex-1);
			monsterTeamInfo.teamName = teamInfo.name;
			monsterTeamInfo.proportion = teamInfo.proportion;
			monsterTeamInfo.fantomId = teamInfo.id;
			monsterTeamInfo.tick = teamInfo.tick;
			local monsterInfos = teamInfo.monster;
			if type(monsterTeamInfo.CreateMonsterInfo) ~= "function" or type(monsterTeamInfo.GetMonsterInfo) ~= "function" then
				return false;
			end
			monsterTeamInfo:CreateMonsterInfo(#monsterInfos);
			for monsterIndex, monsterDef in ipairs(monsterInfos) do
				local monsterInfo = monsterTeamInfo:GetMonsterInfo(monsterIndex-1);
				monsterInfo.monsterID = monsterDef.ID;
				monsterInfo.monsterCount = monsterDef.count;
			end
		end
	end
	return true
end

--大小漩涡都不能被攻击
function F3241_Init(mob)
	if mob then
		mob:setAiInvulnerableProb(1);
	end
end

function F3242_Init(mob)
	if mob then
		mob:setAiInvulnerableProb(1);
	end
end

local function doMonsterDieReport(mob)

end

function F3103_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3110_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3111_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3112_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3220_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3221_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3224_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3225_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3226_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3227_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3892_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3130_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3131_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3821_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3628_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3629_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3630_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3881_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3113_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3114_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3115_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3116_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3117_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3118_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3132_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3511_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3512_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3513_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3824_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3829_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3873_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3874_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3875_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3876_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3877_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3502_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3503_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3504_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3514_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3515_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3516_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3825_OnDie(mob)
	doMonsterDieReport(mob)
end

function F3878_OnDie(mob)
	doMonsterDieReport(mob)
end

--==================== 游商商人 ====================
--[游商旅行配置]
local styleBiome = 
{
	--雨林
	[1039] = 1,
	--沙漠
	[1002] = 2, [1013] = 2,
	--沙滩
	[1019] = 3,
	--雪山
	[1008] = 4, [1056] = 4, [1057] = 4, [1058] = 4, [1059] = 4, [1060] = 4, [1061] = 4, [1062] = 4, [1063] = 4, [1010] = 4, [1016] = 4,
	--空岛
	[1040] = 5,
	--火山
	[1043] = 6,
}
setmetatable(styleBiome, {
	__index = function (t, k)
		return 0;
	end
})

local handle_F3022_BODYSHOW = nil; --游商全局唯一

function F3022_Init(mob)
	if not (mob and mob:getBody()) then
		return;
	end
	--设置服装
	local trader = tolua.cast(mob, "ActorTravelingTrader");
	if trader then
		local biomeId = trader:GetCurBiomeId(0, 0, 0);
		trader:SetModelStyle(styleBiome[biomeId]);
	end
	--客机针对【828,简易睡袋】做body隐藏
	if CurWorld and CurWorld:isRemoteMode() then
		handle_F3022_BODYSHOW = SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.ACOTRBODY_SHOW, function (content)
			if content then
				if mob:getObjId() == content.actorId then
					mob:getBody():show(content.b);
				end
			end
		end)
	end
end

function F3022_Release(mob)
	if not mob then
		return;
	end
	if CurWorld and CurWorld:isRemoteMode() then
		if handle_F3022_BODYSHOW then
			SandboxLuaMsg:unSubscibeMsgHandle(handle_F3022_BODYSHOW);
			handle_F3022_BODYSHOW = nil;
		end
	end
end

function F3022_Interact(mob, player)
	if not mob or not player then
		return false;
	end
	local trader = tolua.cast(mob, "ActorTravelingTrader");
	if not trader then
		return false;
	end
	local hungerState = trader:GetHungerState();
	if hungerState == 1 then
		local ret = trader:GiveFood(player);
		if ret then
			trader:SetHungerState(2, true);
		end
		return ret;
	else
		--打开商店界面
		local biomeId, px, py, pz = trader:GetCurBiomeId(0, 0, 0);
		MiniLog("[打开商店界面]", biomeId, px, py, pz);

		-- WorldMgr:loadTravelingTraderGoodsInfo(5)

		local param = { actorItemID = 3022, objID = player:getObjId(), 
						terrainID = biomeId, x = px, y = py, z = pz, 
						goodsData = GetInst("NpcTradeStoreMgr"):GetNpcTradeData()}
		if player == CurMainPlayer then
			GetInst("NpcTradeStoreMgr"):OpenNpcTraderStore(param);
		else
			SandboxLuaMsg.sendToClient(player:getUin(), SANDBOX_LUAMSG_NAME.Survive.NPC_TRADE_OPENUI, param)
		end
		return true;
	end
end
--==================== 游商商人 ====================

function F3917_Init(mob)
	mob:showAllSkins(false);
	mob:showSkin("body1", true);
end

function F3918_Init(mob)
	mob:showAllSkins(false);
	mob:showSkin("body2", true);
end

function F3919_Init(mob)
	mob:showAllSkins(false);
	mob:showSkin("body3", true);
end