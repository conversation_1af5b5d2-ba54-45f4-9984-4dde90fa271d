#ifndef __OPEN_CONTAINER_COMPONENT_H__
#define __OPEN_CONTAINER_COMPONENT_H__

#include "world_types.h"
#include <string>

#include "WorldRole_generated.h"
#include "Common/OgreHashTable.h"
#include "ActorComponent_Base.h"

class ClientPlayer;
class WorldContainer;
class ActorContainerMob;
class PlayerControl;
class MpPlayerControl;
class ClientWorldContainer;

namespace game {
	namespace ch {
		class PB_CloseFullyCustomModelUICH;
		class PB_CloseEditActorModelCH;
	}
}
class OpenContainerComponent : public ActorComponentBase
{
public:
	DECLARE_COMPONENTCLASS(OpenContainerComponent)

	OpenContainerComponent();

	bool checkIsOpenContainer(const WCoord &pos,int index);
	bool checkIsOpenContainer(WORLD_ID objid, int index);

	int getOpenContainerBaseIndex()const{return m_OpenContainerBase;}

	WorldContainer* getCurOpenedContainer();
	WCoord getCurOpenedContainerPos(){return m_OpenContainer;};

	int getContainersPassword(const WCoord &pos);	
	void setContainersPassword(const WCoord& pos, int password);

	virtual bool openContainer(WorldContainer *container);
	virtual bool openContainer(ActorContainerMob *container);
	virtual void closeContainer();

	virtual void npcTrade(int op, int index, bool watch_ad=false, int ad_rewardnum=1); //op==0刷新物品,  1: 兑换index的物品
	virtual int storeItem(int frmGrid, int num);

	void openEditActorModelUI(WorldContainer *container);
	virtual void closeEditActorModel(int operatetype, std::string modelname="");	
	void onCloseEditActorModel(const game::ch::PB_CloseEditActorModelCH &closeEditActorModelCH);

	void openFullyCustomModelUI(WorldContainer *container);	
	virtual void closeFullyCustomModelUI(int operatetype, std::string name="", std::string desc="");
	void syncOpenFCMUIToClient(const WCoord &blockpos, bool isedited, std::string url, int version=0, int result=0);
	void onCloseFullyCustomModelUI(const game::ch::PB_CloseFullyCustomModelUICH &closeFullyCustomModelUICH);
	void cleanupOpenedContainer();

	flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<FBSave::ContainerPassword>>> save(flatbuffers::FlatBufferBuilder &builder);
	void load(const flatbuffers::Vector<flatbuffers::Offset<FBSave::ContainerPassword>> *containerpasswords);
	int getPasswordCount() { return m_ContainersPassword.size(); }
protected:
	ClientPlayer* m_owner;
	WCoord        m_OpenContainer;//当前打开容器位置 坐标
	WORLD_ID      m_OpenContainerID;//当前 被打开容器所属objid (0:WorldContainer  其他:坐骑ActorContainerMob)
	long long     m_vehicleObjid;//当前 被打开容器所属objid  载具
	int           m_OpenContainerBase;//当前打开容器 基准index	

	typedef Rainbow::HashTable<WCoord, int, WCoordHashCoder> ContainPasswordHashTable;
	ContainPasswordHashTable m_ContainersPassword;	
};

class ControlOpenContainerComponent : public OpenContainerComponent
{
	DECLARE_COMPONENTCLASS(ControlOpenContainerComponent)
public:
	ControlOpenContainerComponent();

	virtual bool openContainer(WorldContainer *container) override;
	virtual bool openContainer(ActorContainerMob *container) override;	

	virtual bool setContainerText(int index, const char *text);	
};


class MPControlOpenContainerComponent : public ControlOpenContainerComponent
{
	DECLARE_COMPONENTCLASS(MPControlOpenContainerComponent)
public:

	MPControlOpenContainerComponent();
	virtual void npcTrade(int op, int index, bool watch_ad=false, int ad_rewardnum=1) override;
	virtual void closeContainer()override;
	virtual int storeItem(int frmGrid, int num)override;

	virtual bool setContainerText(int index, const char *text)override;

	void onOpenContainer(ClientWorldContainer *container);
	void onCloseContainer();	
private:
	ClientWorldContainer *m_CurContainer;
};

#endif