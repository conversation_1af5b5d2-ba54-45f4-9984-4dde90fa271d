/*
** Lua binding: miniSandboxGameToLua
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_miniSandboxGameToLua_open (lua_State* tolua_S);

#include "Play/gameplay/mgr/VehicleMgr.h"
#include "Play/gameplay/mgr/AIFunctionMgr.h"
#include "backpack/backpack.h"
#include "backpack/CraftingQueue.h"
#include "Modules/Environment/DangerNightManager.h"
#include "Modules/Environment/SummonMonsterSiegeMgr.h"
#include "interact/eventObserver/ActorEventListen.h"
#include "system/SandboxActorSubsystem.h"
#include "worldEffect/GameEffectManager.h"

/* function to release collected object via destructor */
#ifdef __cplusplus

static int tolua_collect_std__vector_std__string___iterator (lua_State* tolua_S)
{
 std::vector<std::string>::iterator* self = (std::vector<std::string>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_double_ (lua_State* tolua_S)
{
 std::vector<double>* self = (std::vector<double>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_char___iterator (lua_State* tolua_S)
{
 std::vector<char>::iterator* self = (std::vector<char>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_WCoord (lua_State* tolua_S)
{
 WCoord* self = (WCoord*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_Rainbow__Vector3f (lua_State* tolua_S)
{
 Rainbow::Vector3f* self = (Rainbow::Vector3f*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_WCoord___iterator (lua_State* tolua_S)
{
 std::vector<WCoord>::iterator* self = (std::vector<WCoord>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_float___iterator (lua_State* tolua_S)
{
 std::vector<float>::iterator* self = (std::vector<float>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ActorLiving____iterator (lua_State* tolua_S)
{
 std::vector<ActorLiving*>::iterator* self = (std::vector<ActorLiving*>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientItem__ (lua_State* tolua_S)
{
 std::vector<ClientItem*>* self = (std::vector<ClientItem*>*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientPlayer__ (lua_State* tolua_S)
{
 std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_WCoord_ (lua_State* tolua_S)
{
 std::vector<WCoord>* self = (std::vector<WCoord>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_SiegeDifficultInfo (lua_State* tolua_S)
{
 SiegeDifficultInfo* self = (SiegeDifficultInfo*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_jsonxx__Object (lua_State* tolua_S)
{
 jsonxx::Object* self = (jsonxx::Object*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_int_ (lua_State* tolua_S)
{
 std::vector<int>* self = (std::vector<int>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_float_ (lua_State* tolua_S)
{
 std::vector<float>* self = (std::vector<float>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientPlayer____iterator (lua_State* tolua_S)
{
 std::vector<ClientPlayer*>::iterator* self = (std::vector<ClientPlayer*>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientItem____iterator (lua_State* tolua_S)
{
 std::vector<ClientItem*>::iterator* self = (std::vector<ClientItem*>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_VoidMonsterInfo (lua_State* tolua_S)
{
 VoidMonsterInfo* self = (VoidMonsterInfo*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_double___iterator (lua_State* tolua_S)
{
 std::vector<double>::iterator* self = (std::vector<double>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_Rainbow__Vector3f___iterator (lua_State* tolua_S)
{
 std::vector<Rainbow::Vector3f>::iterator* self = (std::vector<Rainbow::Vector3f>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_std__string_ (lua_State* tolua_S)
{
 std::vector<std::string>* self = (std::vector<std::string>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_CraftingQueue (lua_State* tolua_S)
{
 CraftingQueue* self = (CraftingQueue*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_BackPack (lua_State* tolua_S)
{
 BackPack* self = (BackPack*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_VoidMonsterTeamInfo (lua_State* tolua_S)
{
 VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientActor__ (lua_State* tolua_S)
{
 std::vector<ClientActor*>* self = (std::vector<ClientActor*>*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ActorLiving__ (lua_State* tolua_S)
{
 std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_char_ (lua_State* tolua_S)
{
 std::vector<char>* self = (std::vector<char>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_ClientActor____iterator (lua_State* tolua_S)
{
 std::vector<ClientActor*>::iterator* self = (std::vector<ClientActor*>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_Rainbow__Vector3f_ (lua_State* tolua_S)
{
 std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*) tolua_tousertype(tolua_S,1,0);
	self->clear();Mtolua_delete(self);
	return 0;
}

static int tolua_collect_std__vector_int___iterator (lua_State* tolua_S)
{
 std::vector<int>::iterator* self = (std::vector<int>::iterator*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}
#endif


/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"std::vector<int>");
 tolua_usertype(tolua_S,"ActorEventListen");
 tolua_usertype(tolua_S,"GridVisitor");
 tolua_usertype(tolua_S,"WCoord");
 tolua_usertype(tolua_S,"ActorLiving");
 tolua_usertype(tolua_S,"std::vector<Rainbow::Vector3f>");
 tolua_usertype(tolua_S,"std::vector<float>::iterator");
 tolua_usertype(tolua_S,"std::vector<ActorLiving*>::iterator");
 tolua_usertype(tolua_S,"std::vector<ClientItem*>");
 tolua_usertype(tolua_S,"std::vector<WCoord>");
 tolua_usertype(tolua_S,"BackPackGrid");
 tolua_usertype(tolua_S,"jsonxx::Object");
 tolua_usertype(tolua_S,"World");
 tolua_usertype(tolua_S,"SandboxActorSubsystem");
 tolua_usertype(tolua_S,"std::vector<ClientPlayer*>::iterator");
 tolua_usertype(tolua_S,"std::vector<ClientItem*>::iterator");
 tolua_usertype(tolua_S,"BlockModBase");
 tolua_usertype(tolua_S,"std::vector<double>::iterator");
 tolua_usertype(tolua_S,"std::vector<std::string>");
 tolua_usertype(tolua_S,"EffectManager");
 tolua_usertype(tolua_S,"ClientActor");
 tolua_usertype(tolua_S,"IEventExcuteEx");
 tolua_usertype(tolua_S,"std::vector<ActorLiving*>");
 tolua_usertype(tolua_S,"GameEffectManager");
 tolua_usertype(tolua_S,"std::vector<int>::iterator");
 tolua_usertype(tolua_S,"ObserverEventListenIns");
 tolua_usertype(tolua_S,"ClientItem");
 tolua_usertype(tolua_S,"std::vector<std::string>::iterator");
 tolua_usertype(tolua_S,"std::vector<double>");
 tolua_usertype(tolua_S,"std::vector<char>::iterator");
 tolua_usertype(tolua_S,"SummonMonsterSiegeMgrInterface");
 tolua_usertype(tolua_S,"BackPack");
 tolua_usertype(tolua_S,"Rainbow::Vector3f");
 tolua_usertype(tolua_S,"std::vector<WCoord>::iterator");
 tolua_usertype(tolua_S,"SummonMonsterSiegeMgr");
 tolua_usertype(tolua_S,"std::vector<VoidMonsterTeamInfo>");
 tolua_usertype(tolua_S,"DangerNightManager");
 tolua_usertype(tolua_S,"IBackPack");
 tolua_usertype(tolua_S,"std::vector<ClientPlayer*>");
 tolua_usertype(tolua_S,"std::vector<VoidMonsterInfo>");
 tolua_usertype(tolua_S,"VoidMonsterTeamInfo");
 tolua_usertype(tolua_S,"VoidMonsterInfo");
 tolua_usertype(tolua_S,"ClientPlayer");
 tolua_usertype(tolua_S,"std::vector<float>");
 tolua_usertype(tolua_S,"DangerNightManagerInterface");
 tolua_usertype(tolua_S,"std::vector<ClientActor*>");
 tolua_usertype(tolua_S,"SandboxMgrBase");
 tolua_usertype(tolua_S,"SiegeDifficultInfo");
 tolua_usertype(tolua_S,"std::vector<Rainbow::Vector3f>::iterator");
 tolua_usertype(tolua_S,"ProgressUpdateCallback");
 tolua_usertype(tolua_S,"BaseContainer");
 tolua_usertype(tolua_S,"CraftingQueue");
 tolua_usertype(tolua_S,"QueueUpdateCallback");
 tolua_usertype(tolua_S,"GridRuneItemData");
 tolua_usertype(tolua_S,"GridCopyData");
 tolua_usertype(tolua_S,"std::vector<ClientActor*>::iterator");
 tolua_usertype(tolua_S,"std::vector<char>");
 tolua_usertype(tolua_S,"ISandboxActorSubsystem");
 tolua_usertype(tolua_S,"GameInitItem");
}

/* function: IsAlpha */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_IsAlpha00
static int tolua_miniSandboxGameToLua_IsAlpha00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnumber(tolua_S,1,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  char c = ((char)  tolua_tonumber(tolua_S,1,0));
  {
   bool tolua_ret = (bool)  IsAlpha(c);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsAlpha'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* function: IsDigit */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_IsDigit00
static int tolua_miniSandboxGameToLua_IsDigit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnumber(tolua_S,1,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  char c = ((char)  tolua_tonumber(tolua_S,1,0));
  {
   bool tolua_ret = (bool)  IsDigit(c);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsDigit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientActor*>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientActor*>::iterator* self = (const std::vector<ClientActor*>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientActor*>::iterator tolua_ret = (std::vector<ClientActor*>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientActor*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientActor*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___new00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientActor*>* tolua_ret = (std::vector<ClientActor*>*)  Mtolua_new((std::vector<ClientActor*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientActor*>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___new00_local
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientActor*>* tolua_ret = (std::vector<ClientActor*>*)  Mtolua_new((std::vector<ClientActor*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientActor*>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___delete00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___clear00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___size00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientActor*>* self = (const std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor____geti00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor____geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientActor*>* self = (const std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const ClientActor* tolua_ret = (const ClientActor*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"const ClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor____seti00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor____seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ClientActor",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  ClientActor* tolua_value = ((ClientActor*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor____geti01
static int tolua_miniSandboxGameToLua_std_vector_ClientActor____geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientActor* tolua_ret = (ClientActor*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientActor");
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_ClientActor____geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___push_back00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientActor",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  ClientActor* val = ((ClientActor*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___begin00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientActor*>::iterator tolua_ret = (std::vector<ClientActor*>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientActor*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientActor*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___end00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientActor*>::iterator tolua_ret = (std::vector<ClientActor*>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientActor*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientActor*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___erase00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientActor*>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientActor*>::iterator iter = *((std::vector<ClientActor*>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientActor*>::iterator tolua_ret = (std::vector<ClientActor*>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientActor*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientActor*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<ClientActor*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientActor___insert00
static int tolua_miniSandboxGameToLua_std_vector_ClientActor___insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientActor*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientActor*>::iterator",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,3,"ClientActor",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientActor*>* self = (std::vector<ClientActor*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientActor*>::iterator iter = *((std::vector<ClientActor*>::iterator*)  tolua_tousertype(tolua_S,2,0));
  ClientActor* val = ((ClientActor*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientActor*>::iterator tolua_ret = (std::vector<ClientActor*>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientActor*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientActor*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientActor*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientPlayer*>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientPlayer*>::iterator* self = (const std::vector<ClientPlayer*>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientPlayer*>::iterator tolua_ret = (std::vector<ClientPlayer*>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientPlayer*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientPlayer*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientPlayer*>* tolua_ret = (std::vector<ClientPlayer*>*)  Mtolua_new((std::vector<ClientPlayer*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientPlayer*>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00_local
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientPlayer*>* tolua_ret = (std::vector<ClientPlayer*>*)  Mtolua_new((std::vector<ClientPlayer*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientPlayer*>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___delete00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___clear00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___size00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientPlayer*>* self = (const std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientPlayer*>* self = (const std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const ClientPlayer* tolua_ret = (const ClientPlayer*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"const ClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer____seti00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer____seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  ClientPlayer* tolua_value = ((ClientPlayer*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti01
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientPlayer* tolua_ret = (ClientPlayer*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientPlayer");
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___push_back00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  ClientPlayer* val = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___begin00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientPlayer*>::iterator tolua_ret = (std::vector<ClientPlayer*>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientPlayer*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientPlayer*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___end00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientPlayer*>::iterator tolua_ret = (std::vector<ClientPlayer*>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientPlayer*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientPlayer*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___erase00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientPlayer*>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientPlayer*>::iterator iter = *((std::vector<ClientPlayer*>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientPlayer*>::iterator tolua_ret = (std::vector<ClientPlayer*>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientPlayer*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientPlayer*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<ClientPlayer*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientPlayer___insert00
static int tolua_miniSandboxGameToLua_std_vector_ClientPlayer___insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientPlayer*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientPlayer*>::iterator",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,3,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientPlayer*>* self = (std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientPlayer*>::iterator iter = *((std::vector<ClientPlayer*>::iterator*)  tolua_tousertype(tolua_S,2,0));
  ClientPlayer* val = ((ClientPlayer*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientPlayer*>::iterator tolua_ret = (std::vector<ClientPlayer*>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientPlayer*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientPlayer*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientPlayer*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ActorLiving*>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ActorLiving*>::iterator* self = (const std::vector<ActorLiving*>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ActorLiving*>::iterator tolua_ret = (std::vector<ActorLiving*>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ActorLiving*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ActorLiving*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ActorLiving*>* tolua_ret = (std::vector<ActorLiving*>*)  Mtolua_new((std::vector<ActorLiving*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ActorLiving*>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00_local
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ActorLiving*>* tolua_ret = (std::vector<ActorLiving*>*)  Mtolua_new((std::vector<ActorLiving*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ActorLiving*>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___delete00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___clear00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___size00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ActorLiving*>* self = (const std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ActorLiving*>* self = (const std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const ActorLiving* tolua_ret = (const ActorLiving*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"const ActorLiving");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving____seti00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving____seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ActorLiving",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  ActorLiving* tolua_value = ((ActorLiving*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti01
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ActorLiving* tolua_ret = (ActorLiving*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ActorLiving");
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___push_back00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ActorLiving",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  ActorLiving* val = ((ActorLiving*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___begin00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ActorLiving*>::iterator tolua_ret = (std::vector<ActorLiving*>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ActorLiving*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ActorLiving*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___end00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ActorLiving*>::iterator tolua_ret = (std::vector<ActorLiving*>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ActorLiving*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ActorLiving*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___erase00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ActorLiving*>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ActorLiving*>::iterator iter = *((std::vector<ActorLiving*>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ActorLiving*>::iterator tolua_ret = (std::vector<ActorLiving*>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ActorLiving*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ActorLiving*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<ActorLiving*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ActorLiving___insert00
static int tolua_miniSandboxGameToLua_std_vector_ActorLiving___insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ActorLiving*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ActorLiving*>::iterator",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,3,"ActorLiving",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ActorLiving*>* self = (std::vector<ActorLiving*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ActorLiving*>::iterator iter = *((std::vector<ActorLiving*>::iterator*)  tolua_tousertype(tolua_S,2,0));
  ActorLiving* val = ((ActorLiving*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ActorLiving*>::iterator tolua_ret = (std::vector<ActorLiving*>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ActorLiving*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ActorLiving*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ActorLiving*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientItem*>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientItem*>::iterator* self = (const std::vector<ClientItem*>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientItem*>::iterator tolua_ret = (std::vector<ClientItem*>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientItem*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientItem*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___new00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientItem*>* tolua_ret = (std::vector<ClientItem*>*)  Mtolua_new((std::vector<ClientItem*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientItem*>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___new00_local
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<ClientItem*>* tolua_ret = (std::vector<ClientItem*>*)  Mtolua_new((std::vector<ClientItem*>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<ClientItem*>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___delete00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___clear00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___size00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientItem*>* self = (const std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem____geti00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem____geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<ClientItem*>* self = (const std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const ClientItem* tolua_ret = (const ClientItem*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"const ClientItem");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem____seti00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem____seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ClientItem",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  ClientItem* tolua_value = ((ClientItem*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem____geti01
static int tolua_miniSandboxGameToLua_std_vector_ClientItem____geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientItem* tolua_ret = (ClientItem*)  self->operator[](index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientItem");
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_ClientItem____geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___push_back00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientItem",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  ClientItem* val = ((ClientItem*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___begin00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientItem*>::iterator tolua_ret = (std::vector<ClientItem*>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientItem*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientItem*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___end00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientItem*>::iterator tolua_ret = (std::vector<ClientItem*>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientItem*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientItem*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___erase00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientItem*>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientItem*>::iterator iter = *((std::vector<ClientItem*>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientItem*>::iterator tolua_ret = (std::vector<ClientItem*>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientItem*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientItem*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<ClientItem*> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_ClientItem___insert00
static int tolua_miniSandboxGameToLua_std_vector_ClientItem___insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<ClientItem*>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientItem*>::iterator",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,3,"ClientItem",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<ClientItem*>* self = (std::vector<ClientItem*>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientItem*>::iterator iter = *((std::vector<ClientItem*>::iterator*)  tolua_tousertype(tolua_S,2,0));
  ClientItem* val = ((ClientItem*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<ClientItem*>::iterator tolua_ret = (std::vector<ClientItem*>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<ClientItem*>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<ClientItem*>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<ClientItem*>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<Rainbow::Vector3f>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<Rainbow::Vector3f>::iterator* self = (const std::vector<Rainbow::Vector3f>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<Rainbow::Vector3f>::iterator tolua_ret = (std::vector<Rainbow::Vector3f>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<Rainbow::Vector3f>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<Rainbow::Vector3f>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<Rainbow::Vector3f>* tolua_ret = (std::vector<Rainbow::Vector3f>*)  Mtolua_new((std::vector<Rainbow::Vector3f>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<Rainbow::Vector3f>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00_local
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<Rainbow::Vector3f>* tolua_ret = (std::vector<Rainbow::Vector3f>*)  Mtolua_new((std::vector<Rainbow::Vector3f>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<Rainbow::Vector3f>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__delete00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__clear00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__size00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<Rainbow::Vector3f>* self = (const std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<Rainbow::Vector3f>* self = (const std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const Rainbow::Vector3f tolua_ret = (const Rainbow::Vector3f)  self->operator[](index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((Rainbow::Vector3f)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"const Rainbow::Vector3f");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(const Rainbow::Vector3f));
     tolua_pushusertype(tolua_S,tolua_obj,"const Rainbow::Vector3f");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___seti00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"Rainbow::Vector3f",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  Rainbow::Vector3f tolua_value = *((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti01
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   Rainbow::Vector3f tolua_ret = (Rainbow::Vector3f)  self->operator[](index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((Rainbow::Vector3f)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"Rainbow::Vector3f");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(Rainbow::Vector3f));
     tolua_pushusertype(tolua_S,tolua_obj,"Rainbow::Vector3f");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__push_back00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"Rainbow::Vector3f",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  Rainbow::Vector3f val = *((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__begin00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<Rainbow::Vector3f>::iterator tolua_ret = (std::vector<Rainbow::Vector3f>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<Rainbow::Vector3f>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<Rainbow::Vector3f>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__end00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<Rainbow::Vector3f>::iterator tolua_ret = (std::vector<Rainbow::Vector3f>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<Rainbow::Vector3f>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<Rainbow::Vector3f>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__erase00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<Rainbow::Vector3f>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<Rainbow::Vector3f>::iterator iter = *((std::vector<Rainbow::Vector3f>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<Rainbow::Vector3f>::iterator tolua_ret = (std::vector<Rainbow::Vector3f>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<Rainbow::Vector3f>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<Rainbow::Vector3f>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<Rainbow::Vector3f> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__insert00
static int tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<Rainbow::Vector3f>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<Rainbow::Vector3f>::iterator",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"Rainbow::Vector3f",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<Rainbow::Vector3f>* self = (std::vector<Rainbow::Vector3f>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<Rainbow::Vector3f>::iterator iter = *((std::vector<Rainbow::Vector3f>::iterator*)  tolua_tousertype(tolua_S,2,0));
  Rainbow::Vector3f val = *((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<Rainbow::Vector3f>::iterator tolua_ret = (std::vector<Rainbow::Vector3f>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<Rainbow::Vector3f>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<Rainbow::Vector3f>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<Rainbow::Vector3f>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<WCoord>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<WCoord>::iterator* self = (const std::vector<WCoord>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<WCoord>::iterator tolua_ret = (std::vector<WCoord>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<WCoord>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<WCoord>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__new00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<WCoord>* tolua_ret = (std::vector<WCoord>*)  Mtolua_new((std::vector<WCoord>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<WCoord>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__new00_local
static int tolua_miniSandboxGameToLua_std_vector_WCoord__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<WCoord>* tolua_ret = (std::vector<WCoord>*)  Mtolua_new((std::vector<WCoord>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<WCoord>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__delete00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__clear00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__size00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<WCoord>* self = (const std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord___geti00
static int tolua_miniSandboxGameToLua_std_vector_WCoord___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<WCoord>* self = (const std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const WCoord tolua_ret = (const WCoord)  self->operator[](index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WCoord)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"const WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(const WCoord));
     tolua_pushusertype(tolua_S,tolua_obj,"const WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord___seti00
static int tolua_miniSandboxGameToLua_std_vector_WCoord___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  WCoord tolua_value = *((WCoord*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord___geti01
static int tolua_miniSandboxGameToLua_std_vector_WCoord___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WCoord tolua_ret = (WCoord)  self->operator[](index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WCoord)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WCoord));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_WCoord___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__push_back00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  WCoord val = *((WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__begin00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<WCoord>::iterator tolua_ret = (std::vector<WCoord>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<WCoord>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<WCoord>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__end00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<WCoord>::iterator tolua_ret = (std::vector<WCoord>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<WCoord>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<WCoord>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__erase00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<WCoord>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<WCoord>::iterator iter = *((std::vector<WCoord>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<WCoord>::iterator tolua_ret = (std::vector<WCoord>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<WCoord>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<WCoord>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<WCoord> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_WCoord__insert00
static int tolua_miniSandboxGameToLua_std_vector_WCoord__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<WCoord>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<WCoord>::iterator",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<WCoord>* self = (std::vector<WCoord>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<WCoord>::iterator iter = *((std::vector<WCoord>::iterator*)  tolua_tousertype(tolua_S,2,0));
  WCoord val = *((WCoord*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<WCoord>::iterator tolua_ret = (std::vector<WCoord>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<WCoord>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<WCoord>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<WCoord>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_char__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<char>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<char>::iterator* self = (const std::vector<char>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<char>::iterator tolua_ret = (std::vector<char>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<char>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<char>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__new00
static int tolua_miniSandboxGameToLua_std_vector_char__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<char>* tolua_ret = (std::vector<char>*)  Mtolua_new((std::vector<char>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<char>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__new00_local
static int tolua_miniSandboxGameToLua_std_vector_char__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<char>* tolua_ret = (std::vector<char>*)  Mtolua_new((std::vector<char>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<char>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__delete00
static int tolua_miniSandboxGameToLua_std_vector_char__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__clear00
static int tolua_miniSandboxGameToLua_std_vector_char__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__size00
static int tolua_miniSandboxGameToLua_std_vector_char__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<char>* self = (const std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char___geti00
static int tolua_miniSandboxGameToLua_std_vector_char___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<char>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<char>* self = (const std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char tolua_ret = (const char)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char___seti00
static int tolua_miniSandboxGameToLua_std_vector_char___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  char tolua_value = ((char)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char___geti01
static int tolua_miniSandboxGameToLua_std_vector_char___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   char tolua_ret = (char)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_char___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__push_back00
static int tolua_miniSandboxGameToLua_std_vector_char__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  char val = ((char)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__begin00
static int tolua_miniSandboxGameToLua_std_vector_char__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<char>::iterator tolua_ret = (std::vector<char>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<char>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<char>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__end00
static int tolua_miniSandboxGameToLua_std_vector_char__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<char>::iterator tolua_ret = (std::vector<char>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<char>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<char>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__erase00
static int tolua_miniSandboxGameToLua_std_vector_char__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<char>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<char>::iterator iter = *((std::vector<char>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<char>::iterator tolua_ret = (std::vector<char>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<char>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<char>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<char> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_char__insert00
static int tolua_miniSandboxGameToLua_std_vector_char__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<char>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<char>::iterator",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<char>* self = (std::vector<char>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<char>::iterator iter = *((std::vector<char>::iterator*)  tolua_tousertype(tolua_S,2,0));
  char val = ((char)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<char>::iterator tolua_ret = (std::vector<char>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<char>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<char>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<char>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_double__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<double>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<double>::iterator* self = (const std::vector<double>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<double>::iterator tolua_ret = (std::vector<double>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<double>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<double>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__new00
static int tolua_miniSandboxGameToLua_std_vector_double__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<double>* tolua_ret = (std::vector<double>*)  Mtolua_new((std::vector<double>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<double>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__new00_local
static int tolua_miniSandboxGameToLua_std_vector_double__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<double>* tolua_ret = (std::vector<double>*)  Mtolua_new((std::vector<double>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<double>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__delete00
static int tolua_miniSandboxGameToLua_std_vector_double__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__clear00
static int tolua_miniSandboxGameToLua_std_vector_double__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__size00
static int tolua_miniSandboxGameToLua_std_vector_double__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<double>* self = (const std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double___geti00
static int tolua_miniSandboxGameToLua_std_vector_double___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<double>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<double>* self = (const std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const double tolua_ret = (const double)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double___seti00
static int tolua_miniSandboxGameToLua_std_vector_double___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  double tolua_value = ((double)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double___geti01
static int tolua_miniSandboxGameToLua_std_vector_double___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   double tolua_ret = (double)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_double___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__push_back00
static int tolua_miniSandboxGameToLua_std_vector_double__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  double val = ((double)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__begin00
static int tolua_miniSandboxGameToLua_std_vector_double__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<double>::iterator tolua_ret = (std::vector<double>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<double>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<double>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__end00
static int tolua_miniSandboxGameToLua_std_vector_double__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<double>::iterator tolua_ret = (std::vector<double>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<double>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<double>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__erase00
static int tolua_miniSandboxGameToLua_std_vector_double__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<double>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<double>::iterator iter = *((std::vector<double>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<double>::iterator tolua_ret = (std::vector<double>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<double>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<double>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<double> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_double__insert00
static int tolua_miniSandboxGameToLua_std_vector_double__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<double>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<double>::iterator",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<double>* self = (std::vector<double>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<double>::iterator iter = *((std::vector<double>::iterator*)  tolua_tousertype(tolua_S,2,0));
  double val = ((double)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<double>::iterator tolua_ret = (std::vector<double>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<double>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<double>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<double>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_float__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<float>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<float>::iterator* self = (const std::vector<float>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<float>::iterator tolua_ret = (std::vector<float>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<float>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<float>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__new00
static int tolua_miniSandboxGameToLua_std_vector_float__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<float>* tolua_ret = (std::vector<float>*)  Mtolua_new((std::vector<float>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<float>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__new00_local
static int tolua_miniSandboxGameToLua_std_vector_float__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<float>* tolua_ret = (std::vector<float>*)  Mtolua_new((std::vector<float>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<float>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__delete00
static int tolua_miniSandboxGameToLua_std_vector_float__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__clear00
static int tolua_miniSandboxGameToLua_std_vector_float__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__size00
static int tolua_miniSandboxGameToLua_std_vector_float__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<float>* self = (const std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float___geti00
static int tolua_miniSandboxGameToLua_std_vector_float___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<float>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<float>* self = (const std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const float tolua_ret = (const float)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float___seti00
static int tolua_miniSandboxGameToLua_std_vector_float___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  float tolua_value = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float___geti01
static int tolua_miniSandboxGameToLua_std_vector_float___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   float tolua_ret = (float)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_float___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__push_back00
static int tolua_miniSandboxGameToLua_std_vector_float__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  float val = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__begin00
static int tolua_miniSandboxGameToLua_std_vector_float__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<float>::iterator tolua_ret = (std::vector<float>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<float>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<float>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__end00
static int tolua_miniSandboxGameToLua_std_vector_float__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<float>::iterator tolua_ret = (std::vector<float>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<float>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<float>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__erase00
static int tolua_miniSandboxGameToLua_std_vector_float__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<float>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<float>::iterator iter = *((std::vector<float>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<float>::iterator tolua_ret = (std::vector<float>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<float>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<float>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<float> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_float__insert00
static int tolua_miniSandboxGameToLua_std_vector_float__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<float>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<float>::iterator",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<float>* self = (std::vector<float>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<float>::iterator iter = *((std::vector<float>::iterator*)  tolua_tousertype(tolua_S,2,0));
  float val = ((float)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<float>::iterator tolua_ret = (std::vector<float>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<float>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<float>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<float>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_int__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<int>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<int>::iterator* self = (const std::vector<int>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<int>::iterator tolua_ret = (std::vector<int>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<int>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<int>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__new00
static int tolua_miniSandboxGameToLua_std_vector_int__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<int>* tolua_ret = (std::vector<int>*)  Mtolua_new((std::vector<int>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<int>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__new00_local
static int tolua_miniSandboxGameToLua_std_vector_int__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<int>* tolua_ret = (std::vector<int>*)  Mtolua_new((std::vector<int>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<int>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__delete00
static int tolua_miniSandboxGameToLua_std_vector_int__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__clear00
static int tolua_miniSandboxGameToLua_std_vector_int__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__size00
static int tolua_miniSandboxGameToLua_std_vector_int__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<int>* self = (const std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int___geti00
static int tolua_miniSandboxGameToLua_std_vector_int___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<int>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<int>* self = (const std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const int tolua_ret = (const int)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int___seti00
static int tolua_miniSandboxGameToLua_std_vector_int___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int tolua_value = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int___geti01
static int tolua_miniSandboxGameToLua_std_vector_int___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->operator[](index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_int___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__push_back00
static int tolua_miniSandboxGameToLua_std_vector_int__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  int val = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__begin00
static int tolua_miniSandboxGameToLua_std_vector_int__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<int>::iterator tolua_ret = (std::vector<int>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<int>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<int>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__end00
static int tolua_miniSandboxGameToLua_std_vector_int__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<int>::iterator tolua_ret = (std::vector<int>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<int>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<int>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__erase00
static int tolua_miniSandboxGameToLua_std_vector_int__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<int>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<int>::iterator iter = *((std::vector<int>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<int>::iterator tolua_ret = (std::vector<int>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<int>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<int>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<int> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_int__insert00
static int tolua_miniSandboxGameToLua_std_vector_int__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<int>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<int>::iterator",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<int>* self = (std::vector<int>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<int>::iterator iter = *((std::vector<int>::iterator*)  tolua_tousertype(tolua_S,2,0));
  int val = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<int>::iterator tolua_ret = (std::vector<int>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<int>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<int>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<int>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator+ of class  iterator */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__iterator__add00
static int tolua_miniSandboxGameToLua_std_vector_std__string__iterator__add00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<std::string>::iterator",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<std::string>::iterator* self = (const std::vector<std::string>::iterator*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator+'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<std::string>::iterator tolua_ret = (std::vector<std::string>::iterator)  self->operator+(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<std::string>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<std::string>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.add'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__new00
static int tolua_miniSandboxGameToLua_std_vector_std__string__new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<std::string>* tolua_ret = (std::vector<std::string>*)  Mtolua_new((std::vector<std::string>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<std::string>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__new00_local
static int tolua_miniSandboxGameToLua_std_vector_std__string__new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   std::vector<std::string>* tolua_ret = (std::vector<std::string>*)  Mtolua_new((std::vector<std::string>)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"std::vector<std::string>");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__delete00
static int tolua_miniSandboxGameToLua_std_vector_std__string__delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clear of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__clear00
static int tolua_miniSandboxGameToLua_std_vector_std__string__clear00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clear'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clear();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clear'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: size of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__size00
static int tolua_miniSandboxGameToLua_std_vector_std__string__size00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<std::string>* self = (const std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'size'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->size();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'size'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string___geti00
static int tolua_miniSandboxGameToLua_std_vector_std__string___geti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::vector<std::string>* self = (const std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const std::string tolua_ret = (const std::string)  self->operator[](index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.geti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator&[] of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string___seti00
static int tolua_miniSandboxGameToLua_std_vector_std__string___seti00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  std::string tolua_value = ((std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator&[]'", NULL);
#else 
  if (!self) return 0;
#endif
  self->operator[](index) =  tolua_value;
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function '.seti'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: operator[] of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string___geti01
static int tolua_miniSandboxGameToLua_std_vector_std__string___geti01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'operator[]'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->operator[](index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_std_vector_std__string___geti00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: push_back of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__push_back00
static int tolua_miniSandboxGameToLua_std_vector_std__string__push_back00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  std::string val = ((std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'push_back'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->push_back(val);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'push_back'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: begin of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__begin00
static int tolua_miniSandboxGameToLua_std_vector_std__string__begin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'begin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<std::string>::iterator tolua_ret = (std::vector<std::string>::iterator)  self->begin();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<std::string>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<std::string>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'begin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: end of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__end00
static int tolua_miniSandboxGameToLua_std_vector_std__string__end00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'end'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<std::string>::iterator tolua_ret = (std::vector<std::string>::iterator)  self->end();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<std::string>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<std::string>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'end'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: erase of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__erase00
static int tolua_miniSandboxGameToLua_std_vector_std__string__erase00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<std::string>::iterator",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<std::string>::iterator iter = *((std::vector<std::string>::iterator*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'erase'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<std::string>::iterator tolua_ret = (std::vector<std::string>::iterator)  self->erase(iter);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<std::string>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<std::string>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'erase'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insert of class  std::vector<std::string> */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_std_vector_std__string__insert00
static int tolua_miniSandboxGameToLua_std_vector_std__string__insert00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"std::vector<std::string>",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<std::string>::iterator",0,&tolua_err)) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  std::vector<std::string>* self = (std::vector<std::string>*)  tolua_tousertype(tolua_S,1,0);
  std::vector<std::string>::iterator iter = *((std::vector<std::string>::iterator*)  tolua_tousertype(tolua_S,2,0));
  std::string val = ((std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insert'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<std::string>::iterator tolua_ret = (std::vector<std::string>::iterator)  self->insert(iter,val);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((std::vector<std::string>::iterator)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(std::vector<std::string>::iterator));
     tolua_pushusertype(tolua_S,tolua_obj,"std::vector<std::string>::iterator");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insert'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_new00
static int tolua_miniSandboxGameToLua_BackPack_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientPlayer* player = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
  {
   BackPack* tolua_ret = (BackPack*)  Mtolua_new((BackPack)(player));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BackPack");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_new00_local
static int tolua_miniSandboxGameToLua_BackPack_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientPlayer* player = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
  {
   BackPack* tolua_ret = (BackPack*)  Mtolua_new((BackPack)(player));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BackPack");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_delete00
static int tolua_miniSandboxGameToLua_BackPack_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: validateItems of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_validateItems00
static int tolua_miniSandboxGameToLua_BackPack_validateItems00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'validateItems'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->validateItems();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'validateItems'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItemWithPickUp_bySocGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData00
static int tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const GridCopyData",0,&tolua_err)) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const GridCopyData* gridcopydata = ((const GridCopyData*)  tolua_tousertype(tolua_S,2,0));
  bool isshowtips = ((bool)  tolua_toboolean(tolua_S,3,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItemWithPickUp_bySocGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItemWithPickUp_bySocGridCopyData(*gridcopydata,isshowtips);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItemWithPickUp_bySocGridCopyData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItemWithPickUp_bySocGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData01
static int tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItemWithPickUp_bySocGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItemWithPickUp_bySocGridCopyData(resid,num);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItemWithPickUp_byGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_byGridCopyData00
static int tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_byGridCopyData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const GridCopyData",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const GridCopyData* gridcopydata = ((const GridCopyData*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItemWithPickUp_byGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItemWithPickUp_byGridCopyData(*gridcopydata);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItemWithPickUp_byGridCopyData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: lootItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_lootItem00
static int tolua_miniSandboxGameToLua_BackPack_lootItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'lootItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->lootItem(fromIndex,num);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'lootItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sortPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_sortPack00
static int tolua_miniSandboxGameToLua_BackPack_sortPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int base_index = ((int)  tolua_tonumber(tolua_S,2,0));
  bool isChange = ((bool)  tolua_toboolean(tolua_S,3,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sortPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sortPack(base_index,isChange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sortPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sortStorageBox of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_sortStorageBox00
static int tolua_miniSandboxGameToLua_BackPack_sortStorageBox00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sortStorageBox'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->sortStorageBox();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sortStorageBox'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doCrafting of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_doCrafting00
static int tolua_miniSandboxGameToLua_BackPack_doCrafting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int craftingid = ((int)  tolua_tonumber(tolua_S,2,0));
  int remainNum = ((int)  tolua_tonumber(tolua_S,3,NULL));
  int num = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doCrafting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->doCrafting(craftingid,&remainNum,num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)remainNum);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doCrafting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doPlayerPreDeductCraftMaterials of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_doPlayerPreDeductCraftMaterials00
static int tolua_miniSandboxGameToLua_BackPack_doPlayerPreDeductCraftMaterials00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int craftingid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doPlayerPreDeductCraftMaterials'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->doPlayerPreDeductCraftMaterials(craftingid,num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doPlayerPreDeductCraftMaterials'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doPlayerCraftFromWithhold of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_doPlayerCraftFromWithhold00
static int tolua_miniSandboxGameToLua_BackPack_doPlayerCraftFromWithhold00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int craftID = ((int)  tolua_tonumber(tolua_S,2,0));
  int remainNum = ((int)  tolua_tonumber(tolua_S,3,NULL));
  int num = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doPlayerCraftFromWithhold'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->doPlayerCraftFromWithhold(craftID,&remainNum,num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)remainNum);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doPlayerCraftFromWithhold'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doPlayerReturnPreDeductedMaterialsByCraft of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_doPlayerReturnPreDeductedMaterialsByCraft00
static int tolua_miniSandboxGameToLua_BackPack_doPlayerReturnPreDeductedMaterialsByCraft00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int craftID = ((int)  tolua_tonumber(tolua_S,2,0));
  int resultNu = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doPlayerReturnPreDeductedMaterialsByCraft'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->doPlayerReturnPreDeductedMaterialsByCraft(craftID,resultNu);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doPlayerReturnPreDeductedMaterialsByCraft'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearEnchant of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearEnchant00
static int tolua_miniSandboxGameToLua_BackPack_clearEnchant00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearEnchant'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearEnchant(index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearEnchant'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItem_byGameInitItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItem_byGameInitItem00
static int tolua_miniSandboxGameToLua_BackPack_addItem_byGameInitItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"GameInitItem",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  GameInitItem* item = ((GameInitItem*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItem_byGameInitItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItem_byGameInitItem(item);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItem_byGameInitItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItem_byGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItem_byGridCopyData00
static int tolua_miniSandboxGameToLua_BackPack_addItem_byGridCopyData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const GridCopyData",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const GridCopyData* gridcopydata = ((const GridCopyData*)  tolua_tousertype(tolua_S,2,0));
  int priorityType = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItem_byGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItem_byGridCopyData(*gridcopydata,priorityType);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItem_byGridCopyData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItem_byGrid of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItem_byGrid00
static int tolua_miniSandboxGameToLua_BackPack_addItem_byGrid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"const BackPackGrid",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  const BackPackGrid* grid = ((const BackPackGrid*)  tolua_tousertype(tolua_S,4,0));
  int priorityType = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItem_byGrid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItem_byGrid(resid,num,*grid,priorityType);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItem_byGrid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearRune of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearRune00
static int tolua_miniSandboxGameToLua_BackPack_clearRune00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearRune'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearRune(index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearRune'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: discardItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_discardItem00
static int tolua_miniSandboxGameToLua_BackPack_discardItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'discardItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->discardItem(index,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'discardItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: enchant of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_enchant00
static int tolua_miniSandboxGameToLua_BackPack_enchant00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gridindex = ((int)  tolua_tonumber(tolua_S,2,0));
  int enchantid = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'enchant'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->enchant(gridindex,enchantid);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'enchant'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addRune of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addRune00
static int tolua_miniSandboxGameToLua_BackPack_addRune00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const GridRuneItemData",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gridindex = ((int)  tolua_tonumber(tolua_S,2,0));
  const GridRuneItemData* one = ((const GridRuneItemData*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addRune'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->addRune(gridindex,*one);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addRune'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: replaceRune of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_replaceRune00
static int tolua_miniSandboxGameToLua_BackPack_replaceRune00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const GridRuneItemData",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gridindex = ((int)  tolua_tonumber(tolua_S,2,0));
  const GridRuneItemData* one = ((const GridRuneItemData*)  tolua_tousertype(tolua_S,3,0));
  int runeIndex = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'replaceRune'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->replaceRune(gridindex,*one,runeIndex);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'replaceRune'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRuneNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getRuneNum00
static int tolua_miniSandboxGameToLua_BackPack_getRuneNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRuneNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getRuneNum(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRuneNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRuneItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getRuneItem00
static int tolua_miniSandboxGameToLua_BackPack_getRuneItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gridindex = ((int)  tolua_tonumber(tolua_S,2,0));
  int runeIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRuneItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const GridRuneItemData* tolua_ret = (const GridRuneItemData*)  self->getRuneItem(gridindex,runeIndex);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"const GridRuneItemData");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRuneItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridItem00
static int tolua_miniSandboxGameToLua_BackPack_getGridItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridItem(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridNum00
static int tolua_miniSandboxGameToLua_BackPack_getGridNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridNum(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridDuration of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridDuration00
static int tolua_miniSandboxGameToLua_BackPack_getGridDuration00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridDuration'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridDuration(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridDuration'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridMaxDuration of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridMaxDuration00
static int tolua_miniSandboxGameToLua_BackPack_getGridMaxDuration00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridMaxDuration'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridMaxDuration(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridMaxDuration'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridEnchantNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridEnchantNum00
static int tolua_miniSandboxGameToLua_BackPack_getGridEnchantNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridEnchantNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridEnchantNum(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridEnchantNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridEnchantId of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridEnchantId00
static int tolua_miniSandboxGameToLua_BackPack_getGridEnchantId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int idIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridEnchantId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridEnchantId(index,idIndex);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridEnchantId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridEnchantColor of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridEnchantColor00
static int tolua_miniSandboxGameToLua_BackPack_getGridEnchantColor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridEnchantColor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   unsigned int tolua_ret = (unsigned int)  self->getGridEnchantColor(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridEnchantColor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridToolType of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridToolType00
static int tolua_miniSandboxGameToLua_BackPack_getGridToolType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridToolType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridToolType(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridToolType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridEnough of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridEnough00
static int tolua_miniSandboxGameToLua_BackPack_getGridEnough00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridEnough'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridEnough(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridEnough'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridUserdata of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridUserdata00
static int tolua_miniSandboxGameToLua_BackPack_getGridUserdata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridUserdata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridUserdata(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridUserdata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridUserdataStr of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridUserdataStr00
static int tolua_miniSandboxGameToLua_BackPack_getGridUserdataStr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridUserdataStr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getGridUserdataStr(index);
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridUserdataStr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridSidStr of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridSidStr00
static int tolua_miniSandboxGameToLua_BackPack_getGridSidStr00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridSidStr'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getGridSidStr(index);
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridSidStr'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getModItemName of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getModItemName00
static int tolua_miniSandboxGameToLua_BackPack_getModItemName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getModItemName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getModItemName(index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getModItemName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getModItemDesc of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getModItemDesc00
static int tolua_miniSandboxGameToLua_BackPack_getModItemDesc00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getModItemDesc'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getModItemDesc(index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getModItemDesc'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getModExtradata of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getModExtradata00
static int tolua_miniSandboxGameToLua_BackPack_getModExtradata00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getModExtradata'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getModExtradata(index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getModExtradata'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridInfo00
static int tolua_miniSandboxGameToLua_BackPack_getGridInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getGridInfo(index);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridJsonxxInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridJsonxxInfo00
static int tolua_miniSandboxGameToLua_BackPack_getGridJsonxxInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridJsonxxInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   jsonxx::Object tolua_ret = (jsonxx::Object)  self->getGridJsonxxInfo(index);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((jsonxx::Object)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"jsonxx::Object");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(jsonxx::Object));
     tolua_pushusertype(tolua_S,tolua_obj,"jsonxx::Object");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridJsonxxInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setGridInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setGridInfo00
static int tolua_miniSandboxGameToLua_BackPack_setGridInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  const char* info = ((const char*)  tolua_tostring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setGridInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setGridInfo(index,info);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setGridInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setGridJsonxxInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setGridJsonxxInfo00
static int tolua_miniSandboxGameToLua_BackPack_setGridJsonxxInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"jsonxx::Object",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  jsonxx::Object* info = ((jsonxx::Object*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setGridJsonxxInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->setGridJsonxxInfo(index,info);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setGridJsonxxInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridSortId of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridSortId00
static int tolua_miniSandboxGameToLua_BackPack_getGridSortId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridSortId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridSortId(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridSortId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridCount of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridCount00
static int tolua_miniSandboxGameToLua_BackPack_getGridCount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int baseIndex = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridCount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridCount(baseIndex);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridCount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridItemName of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridItemName00
static int tolua_miniSandboxGameToLua_BackPack_getGridItemName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridItemName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getGridItemName(index);
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridItemName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGridMaxStack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getGridMaxStack00
static int tolua_miniSandboxGameToLua_BackPack_getGridMaxStack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGridMaxStack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGridMaxStack(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGridMaxStack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setItem00
static int tolua_miniSandboxGameToLua_BackPack_setItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isstring(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int grid_index = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,1));
  const char* sid_str = ((const char*)  tolua_tostring(tolua_S,5,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setItem(resid,grid_index,num,sid_str);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItemToEquip of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItemToEquip00
static int tolua_miniSandboxGameToLua_BackPack_addItemToEquip00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int grid_index = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItemToEquip'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addItemToEquip(resid,grid_index,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItemToEquip'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: removeItemFromEquip of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_removeItemFromEquip00
static int tolua_miniSandboxGameToLua_BackPack_removeItemFromEquip00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int grid_index = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'removeItemFromEquip'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->removeItemFromEquip(grid_index,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'removeItemFromEquip'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setItemWithoutLimit of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setItemWithoutLimit00
static int tolua_miniSandboxGameToLua_BackPack_setItemWithoutLimit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isstring(tolua_S,5,0,&tolua_err) ||
     !tolua_isstring(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int grid_index = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,0));
  const char* userdata_str = ((const char*)  tolua_tostring(tolua_S,5,0));
  const char* sid_str = ((const char*)  tolua_tostring(tolua_S,6,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setItemWithoutLimit'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setItemWithoutLimit(resid,grid_index,num,userdata_str,sid_str);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setItemWithoutLimit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doRepair of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_doRepair00
static int tolua_miniSandboxGameToLua_BackPack_doRepair00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int durable = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doRepair'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->doRepair(durable);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doRepair'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: showRecipeProduct of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_showRecipeProduct00
static int tolua_miniSandboxGameToLua_BackPack_showRecipeProduct00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'showRecipeProduct'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->showRecipeProduct();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'showRecipeProduct'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateProductContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_updateProductContainer00
static int tolua_miniSandboxGameToLua_BackPack_updateProductContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int base_index = ((int)  tolua_tonumber(tolua_S,2,0));
  int level = ((int)  tolua_tonumber(tolua_S,3,1));
  bool updateAll = ((bool)  tolua_toboolean(tolua_S,4,false));
  int sortFunc = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateProductContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->updateProductContainer(base_index,level,updateAll,sortFunc);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateProductContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateCookBookProductContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_updateCookBookProductContainer00
static int tolua_miniSandboxGameToLua_BackPack_updateCookBookProductContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  long long uin = ((long long)  tolua_tonumber(tolua_S,2,0));
  int base_index = ((int)  tolua_tonumber(tolua_S,3,0));
  bool updateAll = ((bool)  tolua_toboolean(tolua_S,4,false));
  int sortFunc = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateCookBookProductContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->updateCookBookProductContainer(uin,base_index,updateAll,sortFunc);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateCookBookProductContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateCraftContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_updateCraftContainer00
static int tolua_miniSandboxGameToLua_BackPack_updateCraftContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resultID = ((int)  tolua_tonumber(tolua_S,2,0));
  int base_index = ((int)  tolua_tonumber(tolua_S,3,0));
  int enough = ((int)  tolua_tonumber(tolua_S,4,0));
  int makeNum = ((int)  tolua_tonumber(tolua_S,5,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateCraftContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->updateCraftContainer(resultID,base_index,enough,makeNum);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateCraftContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateCookBookContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_updateCookBookContainer00
static int tolua_miniSandboxGameToLua_BackPack_updateCookBookContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resultID = ((int)  tolua_tonumber(tolua_S,2,0));
  int base_index = ((int)  tolua_tonumber(tolua_S,3,0));
  int enough = ((int)  tolua_tonumber(tolua_S,4,0));
  int makeNum = ((int)  tolua_tonumber(tolua_S,5,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateCookBookContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->updateCookBookContainer(resultID,base_index,enough,makeNum);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateCookBookContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addItem00
static int tolua_miniSandboxGameToLua_BackPack_addItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  int priorityType = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addItem(resid,num,priorityType);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: replaceItem_byGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_replaceItem_byGridCopyData00
static int tolua_miniSandboxGameToLua_BackPack_replaceItem_byGridCopyData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const GridCopyData",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const GridCopyData* gridcopydata = ((const GridCopyData*)  tolua_tousertype(tolua_S,2,0));
  int index = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'replaceItem_byGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->replaceItem_byGridCopyData(*gridcopydata,index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'replaceItem_byGridCopyData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: replaceItemByNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_replaceItemByNum00
static int tolua_miniSandboxGameToLua_BackPack_replaceItemByNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int resid = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'replaceItemByNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->replaceItemByNum(index,resid,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'replaceItemByNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: removeItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_removeItem00
static int tolua_miniSandboxGameToLua_BackPack_removeItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int grid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'removeItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->removeItem(grid,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'removeItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: removeItemInNormalPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_removeItemInNormalPack00
static int tolua_miniSandboxGameToLua_BackPack_removeItemInNormalPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'removeItemInNormalPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->removeItemInNormalPack(itemid,num);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'removeItemInNormalPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearPackByType of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearPackByType00
static int tolua_miniSandboxGameToLua_BackPack_clearPackByType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearPackByType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearPackByType(index);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearPackByType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearPackByType of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearPackByType01
static int tolua_miniSandboxGameToLua_BackPack_clearPackByType01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  const std::string reason = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearPackByType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearPackByType(index,reason);
   tolua_pushcppstring(tolua_S,(const char*)reason);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_BackPack_clearPackByType00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearPack00
static int tolua_miniSandboxGameToLua_BackPack_clearPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearPack();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_clearPack01
static int tolua_miniSandboxGameToLua_BackPack_clearPack01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const std::string reason = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearPack(reason);
   tolua_pushcppstring(tolua_S,(const char*)reason);
  }
 }
 return 1;
tolua_lerror:
 return tolua_miniSandboxGameToLua_BackPack_clearPack00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: shiftMoveItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_shiftMoveItem00
static int tolua_miniSandboxGameToLua_BackPack_shiftMoveItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromgrid = ((int)  tolua_tonumber(tolua_S,2,0));
  int gridtype = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'shiftMoveItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->shiftMoveItem(fromgrid,gridtype);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'shiftMoveItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: moveItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_moveItem00
static int tolua_miniSandboxGameToLua_BackPack_moveItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromindex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toindex = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'moveItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->moveItem(fromindex,toindex,num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'moveItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: swapItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_swapItem00
static int tolua_miniSandboxGameToLua_BackPack_swapItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'swapItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->swapItem(fromIndex,toIndex);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'swapItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: canPutItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_canPutItem00
static int tolua_miniSandboxGameToLua_BackPack_canPutItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'canPutItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->canPutItem(index);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'canPutItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setCreateModeShortCut of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setCreateModeShortCut00
static int tolua_miniSandboxGameToLua_BackPack_setCreateModeShortCut00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setCreateModeShortCut'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setCreateModeShortCut();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setCreateModeShortCut'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: enoughGridForItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_enoughGridForItem00
static int tolua_miniSandboxGameToLua_BackPack_enoughGridForItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'enoughGridForItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->enoughGridForItem(itemid,num);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'enoughGridForItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: enoughGridForItemMaxNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_enoughGridForItemMaxNum00
static int tolua_miniSandboxGameToLua_BackPack_enoughGridForItemMaxNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'enoughGridForItemMaxNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->enoughGridForItemMaxNum(itemid,num);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'enoughGridForItemMaxNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getShorCutEmptyGridNum of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getShorCutEmptyGridNum00
static int tolua_miniSandboxGameToLua_BackPack_getShorCutEmptyGridNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getShorCutEmptyGridNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getShorCutEmptyGridNum();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getShorCutEmptyGridNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: index2Grid of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_index2Grid00
static int tolua_miniSandboxGameToLua_BackPack_index2Grid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'index2Grid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   BackPackGrid* tolua_ret = (BackPackGrid*)  self->index2Grid(index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BackPackGrid");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'index2Grid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getItemCountInNormalPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getItemCountInNormalPack00
static int tolua_miniSandboxGameToLua_BackPack_getItemCountInNormalPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getItemCountInNormalPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getItemCountInNormalPack(itemid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getItemCountInNormalPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: searchNormalPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_searchNormalPack00
static int tolua_miniSandboxGameToLua_BackPack_searchNormalPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"GridVisitor",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  GridVisitor* visitor = ((GridVisitor*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'searchNormalPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->searchNormalPack(visitor);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'searchNormalPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSameGroupItemCountInNormalPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getSameGroupItemCountInNormalPack00
static int tolua_miniSandboxGameToLua_BackPack_getSameGroupItemCountInNormalPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSameGroupItemCountInNormalPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSameGroupItemCountInNormalPack(itemid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSameGroupItemCountInNormalPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: afterChangeGrid of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_afterChangeGrid00
static int tolua_miniSandboxGameToLua_BackPack_afterChangeGrid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gridindex = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'afterChangeGrid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->afterChangeGrid(gridindex);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'afterChangeGrid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isHomeLandGameMakerMode of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_isHomeLandGameMakerMode00
static int tolua_miniSandboxGameToLua_BackPack_isHomeLandGameMakerMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isHomeLandGameMakerMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isHomeLandGameMakerMode();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isHomeLandGameMakerMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getShortcutStartIndex of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getShortcutStartIndex00
static int tolua_miniSandboxGameToLua_BackPack_getShortcutStartIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getShortcutStartIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getShortcutStartIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getShortcutStartIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setTmpShortcutMode of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_setTmpShortcutMode00
static int tolua_miniSandboxGameToLua_BackPack_setTmpShortcutMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  bool isOpen = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setTmpShortcutMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setTmpShortcutMode(isOpen);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setTmpShortcutMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isTmpShortcutMode of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_isTmpShortcutMode00
static int tolua_miniSandboxGameToLua_BackPack_isTmpShortcutMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isTmpShortcutMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isTmpShortcutMode();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isTmpShortcutMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getShortcutGridCount of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getShortcutGridCount00
static int tolua_miniSandboxGameToLua_BackPack_getShortcutGridCount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getShortcutGridCount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getShortcutGridCount();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getShortcutGridCount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEmptyBagIndex of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getEmptyBagIndex00
static int tolua_miniSandboxGameToLua_BackPack_getEmptyBagIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEmptyBagIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getEmptyBagIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEmptyBagIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEmptyShortcutIndex of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getEmptyShortcutIndex00
static int tolua_miniSandboxGameToLua_BackPack_getEmptyShortcutIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEmptyShortcutIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getEmptyShortcutIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEmptyShortcutIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tryAddItem_byGrid of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGrid00
static int tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGrid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,4,"BackPackGrid",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int resid = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  BackPackGrid* data = ((BackPackGrid*)  tolua_tousertype(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tryAddItem_byGrid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->tryAddItem_byGrid(resid,num,data);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tryAddItem_byGrid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tryAddItem_byGridCopyData of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGridCopyData00
static int tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGridCopyData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const GridCopyData",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const GridCopyData* gridcopydata = ((const GridCopyData*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tryAddItem_byGridCopyData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->tryAddItem_byGridCopyData(*gridcopydata);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tryAddItem_byGridCopyData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getExtBackPackInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getExtBackPackInfo00
static int tolua_miniSandboxGameToLua_BackPack_getExtBackPackInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getExtBackPackInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getExtBackPackInfo();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getExtBackPackInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: loadExtBackPackInfo of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_loadExtBackPackInfo00
static int tolua_miniSandboxGameToLua_BackPack_loadExtBackPackInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  const std::string info = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'loadExtBackPackInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->loadExtBackPackInfo(info);
   tolua_pushcppstring(tolua_S,(const char*)info);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'loadExtBackPackInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: moveItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_moveItem01
static int tolua_miniSandboxGameToLua_BackPack_moveItem01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"BackPackGrid",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  BackPackGrid* srcgrid = ((BackPackGrid*)  tolua_tousertype(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  int toindex = ((int)  tolua_tonumber(tolua_S,4,0));
  int fromindex = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'moveItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->moveItem(srcgrid,num,toindex,fromindex);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)num);
  }
 }
 return 2;
tolua_lerror:
 return tolua_miniSandboxGameToLua_BackPack_moveItem00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: placeItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_placeItem00
static int tolua_miniSandboxGameToLua_BackPack_placeItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromgrid = ((int)  tolua_tonumber(tolua_S,2,0));
  int togrid = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'placeItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->placeItem(fromgrid,togrid,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'placeItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getContainer00
static int tolua_miniSandboxGameToLua_BackPack_getContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   BaseContainer* tolua_ret = (BaseContainer*)  self->getContainer(index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BaseContainer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: takeItemFrom of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_takeItemFrom00
static int tolua_miniSandboxGameToLua_BackPack_takeItemFrom00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int gindex = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  bool delsrc = ((bool)  tolua_toboolean(tolua_S,4,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'takeItemFrom'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->takeItemFrom(gindex,num,delsrc);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'takeItemFrom'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addStorageItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_addStorageItem00
static int tolua_miniSandboxGameToLua_BackPack_addStorageItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int num = ((int)  tolua_tonumber(tolua_S,3,0));
  int openContainerBase = ((int)  tolua_tonumber(tolua_S,4,STORAGE_START_INDEX));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addStorageItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->addStorageItem(index,num,openContainerBase);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addStorageItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: mendItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_mendItem00
static int tolua_miniSandboxGameToLua_BackPack_mendItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
  int mendAmount = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'mendItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->mendItem(index,mendAmount);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'mendItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: findItemInNormalPack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_findItemInNormalPack00
static int tolua_miniSandboxGameToLua_BackPack_findItemInNormalPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'findItemInNormalPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->findItemInNormalPack(itemid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'findItemInNormalPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: mergePack of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_mergePack00
static int tolua_miniSandboxGameToLua_BackPack_mergePack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int base_index = ((int)  tolua_tonumber(tolua_S,2,0));
  bool isChange = ((bool)  tolua_toboolean(tolua_S,3,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'mergePack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->mergePack(base_index,isChange);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'mergePack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: mergeItem of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_mergeItem00
static int tolua_miniSandboxGameToLua_BackPack_mergeItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'mergeItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->mergeItem(fromIndex,toIndex);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'mergeItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: attachContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_attachContainer00
static int tolua_miniSandboxGameToLua_BackPack_attachContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"BaseContainer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  BaseContainer* container = ((BaseContainer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'attachContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->attachContainer(container);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'attachContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: detachContainer of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_detachContainer00
static int tolua_miniSandboxGameToLua_BackPack_detachContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"BaseContainer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  BaseContainer* container = ((BaseContainer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'detachContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->detachContainer(container);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'detachContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ItemChangeForTrigger of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger00
static int tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  bool isPutIn = ((bool)  tolua_toboolean(tolua_S,2,0));
  int girdindex = ((int)  tolua_tonumber(tolua_S,3,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,4,0));
  int itemnum = ((int)  tolua_tonumber(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ItemChangeForTrigger'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ItemChangeForTrigger(isPutIn,girdindex,itemid,itemnum);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ItemChangeForTrigger'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: ItemChangeForTrigger of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger01
static int tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger01(lua_State* tolua_S)
{
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  bool isPutIn = ((bool)  tolua_toboolean(tolua_S,2,0));
  WCoord* blockpos = ((WCoord*)  tolua_tousertype(tolua_S,3,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,4,0));
  int itemid = ((int)  tolua_tonumber(tolua_S,5,0));
  int itemnum = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'ItemChangeForTrigger'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->ItemChangeForTrigger(isPutIn,*blockpos,blockid,itemid,itemnum);
  }
 }
 return 0;
tolua_lerror:
 return tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger00(tolua_S);
}
#endif //#ifndef TOLUA_DISABLE

/* method: copyCurShotcutToOldEdit of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_copyCurShotcutToOldEdit00
static int tolua_miniSandboxGameToLua_BackPack_copyCurShotcutToOldEdit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'copyCurShotcutToOldEdit'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->copyCurShotcutToOldEdit(fromIndex,toIndex);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'copyCurShotcutToOldEdit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: copyOldEditToCurShotcut of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_copyOldEditToCurShotcut00
static int tolua_miniSandboxGameToLua_BackPack_copyOldEditToCurShotcut00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toIndex = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'copyOldEditToCurShotcut'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->copyOldEditToCurShotcut(fromIndex,toIndex);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'copyOldEditToCurShotcut'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: insertItemsToCurShotcutEdit of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_insertItemsToCurShotcutEdit00
static int tolua_miniSandboxGameToLua_BackPack_insertItemsToCurShotcutEdit00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"std::vector<int>",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int fromIndex = ((int)  tolua_tonumber(tolua_S,2,0));
  int toIndex = ((int)  tolua_tonumber(tolua_S,3,0));
  std::vector<int> items = *((std::vector<int>*)  tolua_tousertype(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'insertItemsToCurShotcutEdit'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->insertItemsToCurShotcutEdit(fromIndex,toIndex,items);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'insertItemsToCurShotcutEdit'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: numberOfItemInShortcut of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_numberOfItemInShortcut00
static int tolua_miniSandboxGameToLua_BackPack_numberOfItemInShortcut00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  BackPack* self = (BackPack*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'numberOfItemInShortcut'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->numberOfItemInShortcut(itemid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'numberOfItemInShortcut'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSpecialItemName of class  BackPack */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_BackPack_getSpecialItemName00
static int tolua_miniSandboxGameToLua_BackPack_getSpecialItemName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"BackPack",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  int itemId = ((int)  tolua_tonumber(tolua_S,2,0));
  const char* userdata_str = ((const char*)  tolua_tostring(tolua_S,3,0));
  {
   std::string tolua_ret = (std::string)  BackPack::getSpecialItemName(itemId,userdata_str);
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSpecialItemName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_new00
static int tolua_miniSandboxGameToLua_CraftingQueue_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"QueueUpdateCallback",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"ProgressUpdateCallback",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientPlayer* player = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
  QueueUpdateCallback qCallback = *((QueueUpdateCallback*)  tolua_tousertype(tolua_S,3,0));
  ProgressUpdateCallback pCallback = *((ProgressUpdateCallback*)  tolua_tousertype(tolua_S,4,0));
  {
   CraftingQueue* tolua_ret = (CraftingQueue*)  Mtolua_new((CraftingQueue)(player,qCallback,pCallback));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"CraftingQueue");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_new00_local
static int tolua_miniSandboxGameToLua_CraftingQueue_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"QueueUpdateCallback",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"ProgressUpdateCallback",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ClientPlayer* player = ((ClientPlayer*)  tolua_tousertype(tolua_S,2,0));
  QueueUpdateCallback qCallback = *((QueueUpdateCallback*)  tolua_tousertype(tolua_S,3,0));
  ProgressUpdateCallback pCallback = *((ProgressUpdateCallback*)  tolua_tousertype(tolua_S,4,0));
  {
   CraftingQueue* tolua_ret = (CraftingQueue*)  Mtolua_new((CraftingQueue)(player,qCallback,pCallback));
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"CraftingQueue");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCountDown of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getCountDown00
static int tolua_miniSandboxGameToLua_CraftingQueue_getCountDown00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCountDown'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCountDown();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCountDown'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getProgress of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getProgress00
static int tolua_miniSandboxGameToLua_CraftingQueue_getProgress00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getProgress'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   float tolua_ret = (float)  self->getProgress();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getProgress'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getQueueSize of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getQueueSize00
static int tolua_miniSandboxGameToLua_CraftingQueue_getQueueSize00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getQueueSize'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getQueueSize();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getQueueSize'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCraftId of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getCraftId00
static int tolua_miniSandboxGameToLua_CraftingQueue_getCraftId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCraftId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCraftId(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCraftId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCraftCount of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getCraftCount00
static int tolua_miniSandboxGameToLua_CraftingQueue_getCraftCount00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCraftCount'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCraftCount(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCraftCount'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCraftTicks of class  CraftingQueue */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_CraftingQueue_getCraftTicks00
static int tolua_miniSandboxGameToLua_CraftingQueue_getCraftTicks00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"CraftingQueue",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  CraftingQueue* self = (CraftingQueue*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCraftTicks'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCraftTicks(index);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCraftTicks'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: __DangerNightManagerInterface__ of class  DangerNightManager */
#ifndef TOLUA_DISABLE_tolua_get_DangerNightManager___DangerNightManagerInterface__
static int tolua_get_DangerNightManager___DangerNightManagerInterface__(lua_State* tolua_S)
{
  DangerNightManager* self = (DangerNightManager*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '__DangerNightManagerInterface__'",NULL);
#else 
  if (!self) return 0;
#endif
#ifdef __cplusplus
   tolua_pushusertype(tolua_S,(void*)static_cast<DangerNightManagerInterface*>(self), "DangerNightManagerInterface");
#else
   tolua_pushusertype(tolua_S,(void*)((DangerNightManagerInterface*)self), "DangerNightManagerInterface");
#endif
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: monsterID of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterInfo_monsterID
static int tolua_get_VoidMonsterInfo_monsterID(lua_State* tolua_S)
{
  VoidMonsterInfo* self = (VoidMonsterInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterID'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->monsterID);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: monsterID of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterInfo_monsterID
static int tolua_set_VoidMonsterInfo_monsterID(lua_State* tolua_S)
{
  VoidMonsterInfo* self = (VoidMonsterInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterID'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->monsterID = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: monsterCount of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterInfo_monsterCount
static int tolua_get_VoidMonsterInfo_monsterCount(lua_State* tolua_S)
{
  VoidMonsterInfo* self = (VoidMonsterInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterCount'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->monsterCount);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: monsterCount of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterInfo_monsterCount
static int tolua_set_VoidMonsterInfo_monsterCount(lua_State* tolua_S)
{
  VoidMonsterInfo* self = (VoidMonsterInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterCount'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->monsterCount = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterInfo_new00
static int tolua_miniSandboxGameToLua_VoidMonsterInfo_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"VoidMonsterInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   VoidMonsterInfo* tolua_ret = (VoidMonsterInfo*)  Mtolua_new((VoidMonsterInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  VoidMonsterInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterInfo_new00_local
static int tolua_miniSandboxGameToLua_VoidMonsterInfo_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"VoidMonsterInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   VoidMonsterInfo* tolua_ret = (VoidMonsterInfo*)  Mtolua_new((VoidMonsterInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterInfo");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: monsterInfos of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterTeamInfo_monsterInfos
static int tolua_get_VoidMonsterTeamInfo_monsterInfos(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterInfos'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)&self->monsterInfos,"std::vector<VoidMonsterInfo>");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: monsterInfos of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterTeamInfo_monsterInfos
static int tolua_set_VoidMonsterTeamInfo_monsterInfos(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterInfos'",NULL);
  if ((tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<VoidMonsterInfo>",0,&tolua_err)))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->monsterInfos = *((std::vector<VoidMonsterInfo>*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: teamName of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterTeamInfo_teamName
static int tolua_get_VoidMonsterTeamInfo_teamName(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'teamName'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushcppstring(tolua_S,(const char*)self->teamName);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: teamName of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterTeamInfo_teamName
static int tolua_set_VoidMonsterTeamInfo_teamName(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'teamName'",NULL);
  if (!tolua_iscppstring(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->teamName = ((std::string)  tolua_tocppstring(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: proportion of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterTeamInfo_proportion
static int tolua_get_VoidMonsterTeamInfo_proportion(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'proportion'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->proportion);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: proportion of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterTeamInfo_proportion
static int tolua_set_VoidMonsterTeamInfo_proportion(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'proportion'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->proportion = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: fantomId of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterTeamInfo_fantomId
static int tolua_get_VoidMonsterTeamInfo_fantomId(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'fantomId'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->fantomId);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: fantomId of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterTeamInfo_fantomId
static int tolua_set_VoidMonsterTeamInfo_fantomId(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'fantomId'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->fantomId = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: tick of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_get_VoidMonsterTeamInfo_tick
static int tolua_get_VoidMonsterTeamInfo_tick(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'tick'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->tick);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: tick of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_set_VoidMonsterTeamInfo_tick
static int tolua_set_VoidMonsterTeamInfo_tick(lua_State* tolua_S)
{
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'tick'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->tick = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00
static int tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"VoidMonsterTeamInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   VoidMonsterTeamInfo* tolua_ret = (VoidMonsterTeamInfo*)  Mtolua_new((VoidMonsterTeamInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterTeamInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00_local
static int tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"VoidMonsterTeamInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   VoidMonsterTeamInfo* tolua_ret = (VoidMonsterTeamInfo*)  Mtolua_new((VoidMonsterTeamInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterTeamInfo");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateMonsterInfo of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_CreateMonsterInfo00
static int tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_CreateMonsterInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"VoidMonsterTeamInfo",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
  int amount = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateMonsterInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->CreateMonsterInfo(amount);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateMonsterInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetMonsterInfo of class  VoidMonsterTeamInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_GetMonsterInfo00
static int tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_GetMonsterInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"VoidMonsterTeamInfo",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  VoidMonsterTeamInfo* self = (VoidMonsterTeamInfo*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetMonsterInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   VoidMonsterInfo* tolua_ret = (VoidMonsterInfo*)  self->GetMonsterInfo(index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetMonsterInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: monsterTeamInfos of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_get_SiegeDifficultInfo_monsterTeamInfos
static int tolua_get_SiegeDifficultInfo_monsterTeamInfos(lua_State* tolua_S)
{
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterTeamInfos'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)&self->monsterTeamInfos,"std::vector<VoidMonsterTeamInfo>");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: monsterTeamInfos of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_set_SiegeDifficultInfo_monsterTeamInfos
static int tolua_set_SiegeDifficultInfo_monsterTeamInfos(lua_State* tolua_S)
{
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterTeamInfos'",NULL);
  if ((tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<VoidMonsterTeamInfo>",0,&tolua_err)))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->monsterTeamInfos = *((std::vector<VoidMonsterTeamInfo>*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: difficultLevel of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_get_SiegeDifficultInfo_difficultLevel
static int tolua_get_SiegeDifficultInfo_difficultLevel(lua_State* tolua_S)
{
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'difficultLevel'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->difficultLevel);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: difficultLevel of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_set_SiegeDifficultInfo_difficultLevel
static int tolua_set_SiegeDifficultInfo_difficultLevel(lua_State* tolua_S)
{
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'difficultLevel'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->difficultLevel = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00
static int tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SiegeDifficultInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SiegeDifficultInfo* tolua_ret = (SiegeDifficultInfo*)  Mtolua_new((SiegeDifficultInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SiegeDifficultInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00_local
static int tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SiegeDifficultInfo",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SiegeDifficultInfo* tolua_ret = (SiegeDifficultInfo*)  Mtolua_new((SiegeDifficultInfo)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SiegeDifficultInfo");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateMonsterTeamInfo of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SiegeDifficultInfo_CreateMonsterTeamInfo00
static int tolua_miniSandboxGameToLua_SiegeDifficultInfo_CreateMonsterTeamInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SiegeDifficultInfo",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
  int amount = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateMonsterTeamInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->CreateMonsterTeamInfo(amount);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateMonsterTeamInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetMonsterTeamInfo of class  SiegeDifficultInfo */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SiegeDifficultInfo_GetMonsterTeamInfo00
static int tolua_miniSandboxGameToLua_SiegeDifficultInfo_GetMonsterTeamInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SiegeDifficultInfo",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SiegeDifficultInfo* self = (SiegeDifficultInfo*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetMonsterTeamInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   VoidMonsterTeamInfo* tolua_ret = (VoidMonsterTeamInfo*)  self->GetMonsterTeamInfo(index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"VoidMonsterTeamInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetMonsterTeamInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateSiegeDifficultInfo of class  SummonMonsterSiegeMgr */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_CreateSiegeDifficultInfo00
static int tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_CreateSiegeDifficultInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SummonMonsterSiegeMgr",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SummonMonsterSiegeMgr* self = (SummonMonsterSiegeMgr*)  tolua_tousertype(tolua_S,1,0);
  int amount = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateSiegeDifficultInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->CreateSiegeDifficultInfo(amount);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateSiegeDifficultInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetSiegeDifficultInfo of class  SummonMonsterSiegeMgr */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_GetSiegeDifficultInfo00
static int tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_GetSiegeDifficultInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SummonMonsterSiegeMgr",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SummonMonsterSiegeMgr* self = (SummonMonsterSiegeMgr*)  tolua_tousertype(tolua_S,1,0);
  int index = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetSiegeDifficultInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   SiegeDifficultInfo* tolua_ret = (SiegeDifficultInfo*)  self->GetSiegeDifficultInfo(index);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SiegeDifficultInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSiegeDifficultInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: RegEvent of class  ActorEventListen */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_ActorEventListen_RegEvent00
static int tolua_miniSandboxGameToLua_ActorEventListen_RegEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorEventListen",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorEventListen* self = (ActorEventListen*)  tolua_tousertype(tolua_S,1,0);
  const char* eventname = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'RegEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->RegEvent(eventname);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'RegEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: createProjectileWithShooter of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_createProjectileWithShooter00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_createProjectileWithShooter00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,10,"ClientActor",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,11,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int posx = ((int)  tolua_tonumber(tolua_S,3,0));
  int posy = ((int)  tolua_tonumber(tolua_S,4,0));
  int posz = ((int)  tolua_tonumber(tolua_S,5,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,6,0));
  float diry = ((float)  tolua_tonumber(tolua_S,7,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,8,0));
  int speed = ((int)  tolua_tonumber(tolua_S,9,0));
  ClientActor* pActor = ((ClientActor*)  tolua_tousertype(tolua_S,10,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,11,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'createProjectileWithShooter'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   long long tolua_ret = (long long)  self->createProjectileWithShooter(itemid,posx,posy,posz,dirx,diry,dirz,speed,pActor,world);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'createProjectileWithShooter'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: shootArrowAuto of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootArrowAuto00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootArrowAuto00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,10,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int posx = ((int)  tolua_tonumber(tolua_S,2,0));
  int posy = ((int)  tolua_tonumber(tolua_S,3,0));
  int posz = ((int)  tolua_tonumber(tolua_S,4,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,5,0));
  float diry = ((float)  tolua_tonumber(tolua_S,6,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,7,0));
  int shooterObjIdHigh = ((int)  tolua_tonumber(tolua_S,8,0));
  int shooterObjIdLow = ((int)  tolua_tonumber(tolua_S,9,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'shootArrowAuto'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->shootArrowAuto(posx,posy,posz,dirx,diry,dirz,shooterObjIdHigh,shooterObjIdLow,world);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'shootArrowAuto'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: shootProjectileAuto of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootProjectileAuto00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootProjectileAuto00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,11,"World",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,12,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,13,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,14,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int posx = ((int)  tolua_tonumber(tolua_S,3,0));
  int posy = ((int)  tolua_tonumber(tolua_S,4,0));
  int posz = ((int)  tolua_tonumber(tolua_S,5,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,6,0));
  float diry = ((float)  tolua_tonumber(tolua_S,7,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,8,0));
  int shooterObjIdHigh = ((int)  tolua_tonumber(tolua_S,9,0));
  int shooterObjIdLow = ((int)  tolua_tonumber(tolua_S,10,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,11,0));
  int durable = ((int)  tolua_tonumber(tolua_S,12,-1));
  float speedRate = ((float)  tolua_tonumber(tolua_S,13,1.f));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'shootProjectileAuto'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->shootProjectileAuto(itemid,posx,posy,posz,dirx,diry,dirz,shooterObjIdHigh,shooterObjIdLow,world,durable,speedRate);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'shootProjectileAuto'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: throwItemAuto of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_throwItemAuto00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_throwItemAuto00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,11,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int posx = ((int)  tolua_tonumber(tolua_S,3,0));
  int posy = ((int)  tolua_tonumber(tolua_S,4,0));
  int posz = ((int)  tolua_tonumber(tolua_S,5,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,6,0));
  float diry = ((float)  tolua_tonumber(tolua_S,7,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,8,0));
  int shooterObjIdHigh = ((int)  tolua_tonumber(tolua_S,9,0));
  int shooterObjIdLow = ((int)  tolua_tonumber(tolua_S,10,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,11,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'throwItemAuto'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->throwItemAuto(itemid,posx,posy,posz,dirx,diry,dirz,shooterObjIdHigh,shooterObjIdLow,world);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'throwItemAuto'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: shootImpulseAuto of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootImpulseAuto00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootImpulseAuto00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,10,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,11,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,12,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
  int posx = ((int)  tolua_tonumber(tolua_S,3,0));
  int posy = ((int)  tolua_tonumber(tolua_S,4,0));
  int posz = ((int)  tolua_tonumber(tolua_S,5,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,6,0));
  float diry = ((float)  tolua_tonumber(tolua_S,7,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,8,0));
  int shooterObjIdHigh = ((int)  tolua_tonumber(tolua_S,9,0));
  int shooterObjIdLow = ((int)  tolua_tonumber(tolua_S,10,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,11,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'shootImpulseAuto'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->shootImpulseAuto(itemid,posx,posy,posz,dirx,diry,dirz,shooterObjIdHigh,shooterObjIdLow,world);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'shootImpulseAuto'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: createActorFirework of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_createActorFirework00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_createActorFirework00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,8,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,9,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,10,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,11,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int posx = ((int)  tolua_tonumber(tolua_S,2,0));
  int posy = ((int)  tolua_tonumber(tolua_S,3,0));
  int posz = ((int)  tolua_tonumber(tolua_S,4,0));
  float dirx = ((float)  tolua_tonumber(tolua_S,5,0));
  float diry = ((float)  tolua_tonumber(tolua_S,6,0));
  float dirz = ((float)  tolua_tonumber(tolua_S,7,0));
  int firetype = ((int)  tolua_tonumber(tolua_S,8,0));
  int firedata = ((int)  tolua_tonumber(tolua_S,9,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,10,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'createActorFirework'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->createActorFirework(posx,posy,posz,dirx,diry,dirz,firetype,firedata,world);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'createActorFirework'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: dragonFlowerAttack of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_dragonFlowerAttack00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_dragonFlowerAttack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const WCoord",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,4,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  int angle = ((int)  tolua_tonumber(tolua_S,2,0));
  const WCoord* pos = ((const WCoord*)  tolua_tousertype(tolua_S,3,0));
  World* world = ((World*)  tolua_tousertype(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'dragonFlowerAttack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->dragonFlowerAttack(angle,*pos,world);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'dragonFlowerAttack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getAllPlayers of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllPlayers00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllPlayers00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"std::vector<ClientPlayer*>",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  std::vector<ClientPlayer*>* players = ((std::vector<ClientPlayer*>*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getAllPlayers'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->getAllPlayers(*players);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getAllPlayers'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getAllNearPlayersUin of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllNearPlayersUin00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllNearPlayersUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"World",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"std::vector<int>",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  World* world = ((World*)  tolua_tousertype(tolua_S,2,0));
  std::vector<int>* uinList = ((std::vector<int>*)  tolua_tousertype(tolua_S,3,0));
  WCoord* pos = ((WCoord*)  tolua_tousertype(tolua_S,4,0));
  float range = ((float)  tolua_tonumber(tolua_S,5,0));
  bool isAll = ((bool)  tolua_toboolean(tolua_S,6,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getAllNearPlayersUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->getAllNearPlayersUin(world,*uinList,*pos,range,isAll);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getAllNearPlayersUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getModBlockMaterial of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_SandboxActorSubsystem_getModBlockMaterial00
static int tolua_miniSandboxGameToLua_SandboxActorSubsystem_getModBlockMaterial00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"SandboxActorSubsystem",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"World",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
  World* world = ((World*)  tolua_tousertype(tolua_S,2,0));
  WCoord* pos = ((WCoord*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getModBlockMaterial'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   BlockModBase* tolua_ret = (BlockModBase*)  self->getModBlockMaterial(world,*pos);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BlockModBase");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getModBlockMaterial'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: __IEventExcuteEx__ of class  SandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_get_SandboxActorSubsystem___IEventExcuteEx__
static int tolua_get_SandboxActorSubsystem___IEventExcuteEx__(lua_State* tolua_S)
{
  SandboxActorSubsystem* self = (SandboxActorSubsystem*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '__IEventExcuteEx__'",NULL);
#else 
  if (!self) return 0;
#endif
#ifdef __cplusplus
   tolua_pushusertype(tolua_S,(void*)static_cast<IEventExcuteEx*>(self), "IEventExcuteEx");
#else
   tolua_pushusertype(tolua_S,(void*)((IEventExcuteEx*)self), "IEventExcuteEx");
#endif
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* function: GetSandboxActorSubsystemIns */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GetSandboxActorSubsystemIns00
static int tolua_miniSandboxGameToLua_GetSandboxActorSubsystemIns00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnoobj(tolua_S,1,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SandboxActorSubsystem& tolua_ret = (SandboxActorSubsystem&)  GetSandboxActorSubsystemIns();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"SandboxActorSubsystem");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSandboxActorSubsystemIns'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* function: GetSandboxActorSubsystem */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GetSandboxActorSubsystem00
static int tolua_miniSandboxGameToLua_GetSandboxActorSubsystem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnoobj(tolua_S,1,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SandboxActorSubsystem* tolua_ret = (SandboxActorSubsystem*)  GetSandboxActorSubsystem();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SandboxActorSubsystem");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetSandboxActorSubsystem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: playBlockCrackEffect of class  GameEffectManager */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GameEffectManager_playBlockCrackEffect00
static int tolua_miniSandboxGameToLua_GameEffectManager_playBlockCrackEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameEffectManager",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameEffectManager* self = (GameEffectManager*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  int process = ((int)  tolua_tonumber(tolua_S,3,0));
  long long actorid = (( long long)  tolua_tonumber(tolua_S,4,0));
  DirectionType dir = ((DirectionType) (int)  tolua_tonumber(tolua_S,5,DIR_NEG_X));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'playBlockCrackEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->playBlockCrackEffect(*blockpos,process,actorid,dir);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'playBlockCrackEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: stopBlockCrackEffect of class  GameEffectManager */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GameEffectManager_stopBlockCrackEffect00
static int tolua_miniSandboxGameToLua_GameEffectManager_stopBlockCrackEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameEffectManager",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameEffectManager* self = (GameEffectManager*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'stopBlockCrackEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->stopBlockCrackEffect(*blockpos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'stopBlockCrackEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: changeBlockCrackEffectModel of class  GameEffectManager */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GameEffectManager_changeBlockCrackEffectModel00
static int tolua_miniSandboxGameToLua_GameEffectManager_changeBlockCrackEffectModel00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameEffectManager",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameEffectManager* self = (GameEffectManager*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  int process = ((int)  tolua_tonumber(tolua_S,3,0));
  long long actorid = (( long long)  tolua_tonumber(tolua_S,4,0));
  DirectionType dir = ((DirectionType) (int)  tolua_tonumber(tolua_S,5,DIR_NEG_X));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'changeBlockCrackEffectModel'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->changeBlockCrackEffectModel(*blockpos,process,actorid,dir);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'changeBlockCrackEffectModel'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: playPickItemEffect of class  GameEffectManager */
#ifndef TOLUA_DISABLE_tolua_miniSandboxGameToLua_GameEffectManager_playPickItemEffect00
static int tolua_miniSandboxGameToLua_GameEffectManager_playPickItemEffect00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"GameEffectManager",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"ClientActor",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ClientActor",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  GameEffectManager* self = (GameEffectManager*)  tolua_tousertype(tolua_S,1,0);
  ClientActor* picker = ((ClientActor*)  tolua_tousertype(tolua_S,2,0));
  ClientActor* item = ((ClientActor*)  tolua_tousertype(tolua_S,3,0));
  int yoffset = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'playPickItemEffect'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->playPickItemEffect(picker,item,yoffset);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'playPickItemEffect'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_miniSandboxGameToLua_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  tolua_function(tolua_S,"IsAlpha",tolua_miniSandboxGameToLua_IsAlpha00);
  tolua_function(tolua_S,"IsDigit",tolua_miniSandboxGameToLua_IsDigit00);
  tolua_module(tolua_S,"std",0);
  tolua_beginmodule(tolua_S,"std");
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_ClientActor__","std::vector<ClientActor*>","",tolua_collect_std__vector_ClientActor__);
   #else
   tolua_cclass(tolua_S,"vector_ClientActor__","std::vector<ClientActor*>","",NULL);
   #endif
   tolua_beginmodule(tolua_S,"vector_ClientActor__");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<ClientActor*>::iterator","",tolua_collect_std__vector_ClientActor____iterator);
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<ClientActor*>::iterator","",NULL);
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_ClientActor___iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_ClientActor___new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_ClientActor___new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_ClientActor___new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_ClientActor___delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_ClientActor___clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_ClientActor___size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientActor____geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_ClientActor____seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientActor____geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_ClientActor___push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_ClientActor___begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_ClientActor___end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_ClientActor___erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_ClientActor___insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_ClientPlayer__","std::vector<ClientPlayer*>","",tolua_collect_std__vector_ClientPlayer__);
   #else
   tolua_cclass(tolua_S,"vector_ClientPlayer__","std::vector<ClientPlayer*>","",NULL);
   #endif
   tolua_beginmodule(tolua_S,"vector_ClientPlayer__");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<ClientPlayer*>::iterator","",tolua_collect_std__vector_ClientPlayer____iterator);
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<ClientPlayer*>::iterator","",NULL);
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_ClientPlayer____seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientPlayer____geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_ClientPlayer___insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_ActorLiving__","std::vector<ActorLiving*>","",tolua_collect_std__vector_ActorLiving__);
   #else
   tolua_cclass(tolua_S,"vector_ActorLiving__","std::vector<ActorLiving*>","",NULL);
   #endif
   tolua_beginmodule(tolua_S,"vector_ActorLiving__");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<ActorLiving*>::iterator","",tolua_collect_std__vector_ActorLiving____iterator);
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<ActorLiving*>::iterator","",NULL);
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_ActorLiving___iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_ActorLiving___new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_ActorLiving___delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_ActorLiving___clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_ActorLiving___size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_ActorLiving____seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ActorLiving____geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_ActorLiving___push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_ActorLiving___begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_ActorLiving___end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_ActorLiving___erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_ActorLiving___insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_ClientItem__","std::vector<ClientItem*>","",tolua_collect_std__vector_ClientItem__);
   #else
   tolua_cclass(tolua_S,"vector_ClientItem__","std::vector<ClientItem*>","",NULL);
   #endif
   tolua_beginmodule(tolua_S,"vector_ClientItem__");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<ClientItem*>::iterator","",tolua_collect_std__vector_ClientItem____iterator);
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<ClientItem*>::iterator","",NULL);
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_ClientItem___iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_ClientItem___new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_ClientItem___new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_ClientItem___new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_ClientItem___delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_ClientItem___clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_ClientItem___size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientItem____geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_ClientItem____seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_ClientItem____geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_ClientItem___push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_ClientItem___begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_ClientItem___end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_ClientItem___erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_ClientItem___insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_Rainbow__Vector3f_","std::vector<Rainbow::Vector3f>","",tolua_collect_std__vector_Rainbow__Vector3f_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<Rainbow::Vector3f>, "vector_Rainbow__Vector3f_", "std::vector<Rainbow::Vector3f>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_Rainbow__Vector3f_","std::vector<Rainbow::Vector3f>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<Rainbow::Vector3f>, "vector_Rainbow__Vector3f_", "std::vector<Rainbow::Vector3f>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_Rainbow__Vector3f_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<Rainbow::Vector3f>::iterator","",tolua_collect_std__vector_Rainbow__Vector3f___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<Rainbow::Vector3f>::iterator, "iterator", "std::vector<Rainbow::Vector3f>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<Rainbow::Vector3f>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<Rainbow::Vector3f>::iterator, "iterator", "std::vector<Rainbow::Vector3f>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_Rainbow__Vector3f__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_WCoord_","std::vector<WCoord>","",tolua_collect_std__vector_WCoord_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<WCoord>, "vector_WCoord_", "std::vector<WCoord>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_WCoord_","std::vector<WCoord>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<WCoord>, "vector_WCoord_", "std::vector<WCoord>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_WCoord_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<WCoord>::iterator","",tolua_collect_std__vector_WCoord___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<WCoord>::iterator, "iterator", "std::vector<WCoord>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<WCoord>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<WCoord>::iterator, "iterator", "std::vector<WCoord>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_WCoord__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_WCoord__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_WCoord__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_WCoord__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_WCoord__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_WCoord__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_WCoord__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_WCoord___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_WCoord___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_WCoord___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_WCoord__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_WCoord__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_WCoord__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_WCoord__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_WCoord__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_char_","std::vector<char>","",tolua_collect_std__vector_char_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<char>, "vector_char_", "std::vector<char>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_char_","std::vector<char>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<char>, "vector_char_", "std::vector<char>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_char_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<char>::iterator","",tolua_collect_std__vector_char___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<char>::iterator, "iterator", "std::vector<char>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<char>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<char>::iterator, "iterator", "std::vector<char>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_char__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_char__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_char__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_char__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_char__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_char__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_char__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_char___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_char___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_char___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_char__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_char__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_char__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_char__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_char__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_double_","std::vector<double>","",tolua_collect_std__vector_double_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<double>, "vector_double_", "std::vector<double>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_double_","std::vector<double>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<double>, "vector_double_", "std::vector<double>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_double_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<double>::iterator","",tolua_collect_std__vector_double___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<double>::iterator, "iterator", "std::vector<double>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<double>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<double>::iterator, "iterator", "std::vector<double>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_double__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_double__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_double__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_double__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_double__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_double__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_double__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_double___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_double___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_double___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_double__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_double__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_double__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_double__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_double__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_float_","std::vector<float>","",tolua_collect_std__vector_float_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<float>, "vector_float_", "std::vector<float>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_float_","std::vector<float>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<float>, "vector_float_", "std::vector<float>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_float_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<float>::iterator","",tolua_collect_std__vector_float___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<float>::iterator, "iterator", "std::vector<float>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<float>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<float>::iterator, "iterator", "std::vector<float>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_float__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_float__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_float__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_float__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_float__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_float__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_float__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_float___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_float___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_float___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_float__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_float__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_float__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_float__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_float__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_int_","std::vector<int>","",tolua_collect_std__vector_int_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<int>, "vector_int_", "std::vector<int>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_int_","std::vector<int>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<int>, "vector_int_", "std::vector<int>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_int_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<int>::iterator","",tolua_collect_std__vector_int___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<int>::iterator, "iterator", "std::vector<int>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<int>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<int>::iterator, "iterator", "std::vector<int>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_int__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_int__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_int__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_int__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_int__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_int__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_int__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_int___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_int___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_int___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_int__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_int__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_int__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_int__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_int__insert00);
   tolua_endmodule(tolua_S);
   #ifdef __cplusplus
   tolua_cclass(tolua_S,"vector_std__string_","std::vector<std::string>","",tolua_collect_std__vector_std__string_);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<std::string>, "vector_std__string_", "std::vector<std::string>", "")
#endif
   #else
   tolua_cclass(tolua_S,"vector_std__string_","std::vector<std::string>","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
   SANDBOX_BIND_LUATYPE(std::vector<std::string>, "vector_std__string_", "std::vector<std::string>", "")
#endif
   #endif
   tolua_beginmodule(tolua_S,"vector_std__string_");
    #ifdef __cplusplus
    tolua_cclass(tolua_S,"iterator","std::vector<std::string>::iterator","",tolua_collect_std__vector_std__string___iterator);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<std::string>::iterator, "iterator", "std::vector<std::string>::iterator", "")
#endif
    #else
    tolua_cclass(tolua_S,"iterator","std::vector<std::string>::iterator","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
    SANDBOX_BIND_LUATYPE(std::vector<std::string>::iterator, "iterator", "std::vector<std::string>::iterator", "")
#endif
    #endif
    tolua_beginmodule(tolua_S,"iterator");
     tolua_function(tolua_S,".add",tolua_miniSandboxGameToLua_std_vector_std__string__iterator__add00);
    tolua_endmodule(tolua_S);
    tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_std_vector_std__string__new00);
    tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_std_vector_std__string__new00_local);
    tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_std_vector_std__string__new00_local);
    tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_std_vector_std__string__delete00);
    tolua_function(tolua_S,"clear",tolua_miniSandboxGameToLua_std_vector_std__string__clear00);
    tolua_function(tolua_S,"size",tolua_miniSandboxGameToLua_std_vector_std__string__size00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_std__string___geti00);
    tolua_function(tolua_S,".seti",tolua_miniSandboxGameToLua_std_vector_std__string___seti00);
    tolua_function(tolua_S,".geti",tolua_miniSandboxGameToLua_std_vector_std__string___geti01);
    tolua_function(tolua_S,"push_back",tolua_miniSandboxGameToLua_std_vector_std__string__push_back00);
    tolua_function(tolua_S,"begin",tolua_miniSandboxGameToLua_std_vector_std__string__begin00);
    tolua_function(tolua_S,"end_",tolua_miniSandboxGameToLua_std_vector_std__string__end00);
    tolua_function(tolua_S,"erase",tolua_miniSandboxGameToLua_std_vector_std__string__erase00);
    tolua_function(tolua_S,"insert",tolua_miniSandboxGameToLua_std_vector_std__string__insert00);
   tolua_endmodule(tolua_S);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"BackPack","BackPack","IBackPack",tolua_collect_BackPack);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(BackPack, "BackPack", "BackPack", "IBackPack")
#endif
  #else
  tolua_cclass(tolua_S,"BackPack","BackPack","IBackPack",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(BackPack, "BackPack", "BackPack", "IBackPack")
#endif
  #endif
  tolua_beginmodule(tolua_S,"BackPack");
   tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_BackPack_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_BackPack_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_BackPack_new00_local);
   tolua_function(tolua_S,"delete",tolua_miniSandboxGameToLua_BackPack_delete00);
   tolua_function(tolua_S,"validateItems",tolua_miniSandboxGameToLua_BackPack_validateItems00);
   tolua_function(tolua_S,"addItemWithPickUp_bySocGridCopyData",tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData00);
   tolua_function(tolua_S,"addItemWithPickUp_bySocGridCopyData",tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_bySocGridCopyData01);
   tolua_function(tolua_S,"addItemWithPickUp_byGridCopyData",tolua_miniSandboxGameToLua_BackPack_addItemWithPickUp_byGridCopyData00);
   tolua_function(tolua_S,"lootItem",tolua_miniSandboxGameToLua_BackPack_lootItem00);
   tolua_function(tolua_S,"sortPack",tolua_miniSandboxGameToLua_BackPack_sortPack00);
   tolua_function(tolua_S,"sortStorageBox",tolua_miniSandboxGameToLua_BackPack_sortStorageBox00);
   tolua_function(tolua_S,"doCrafting",tolua_miniSandboxGameToLua_BackPack_doCrafting00);
   tolua_function(tolua_S,"doPlayerPreDeductCraftMaterials",tolua_miniSandboxGameToLua_BackPack_doPlayerPreDeductCraftMaterials00);
   tolua_function(tolua_S,"doPlayerCraftFromWithhold",tolua_miniSandboxGameToLua_BackPack_doPlayerCraftFromWithhold00);
   tolua_function(tolua_S,"doPlayerReturnPreDeductedMaterialsByCraft",tolua_miniSandboxGameToLua_BackPack_doPlayerReturnPreDeductedMaterialsByCraft00);
   tolua_function(tolua_S,"clearEnchant",tolua_miniSandboxGameToLua_BackPack_clearEnchant00);
   tolua_function(tolua_S,"addItem_byGameInitItem",tolua_miniSandboxGameToLua_BackPack_addItem_byGameInitItem00);
   tolua_function(tolua_S,"addItem_byGridCopyData",tolua_miniSandboxGameToLua_BackPack_addItem_byGridCopyData00);
   tolua_function(tolua_S,"addItem_byGrid",tolua_miniSandboxGameToLua_BackPack_addItem_byGrid00);
   tolua_function(tolua_S,"clearRune",tolua_miniSandboxGameToLua_BackPack_clearRune00);
   tolua_function(tolua_S,"discardItem",tolua_miniSandboxGameToLua_BackPack_discardItem00);
   tolua_function(tolua_S,"enchant",tolua_miniSandboxGameToLua_BackPack_enchant00);
   tolua_function(tolua_S,"addRune",tolua_miniSandboxGameToLua_BackPack_addRune00);
   tolua_function(tolua_S,"replaceRune",tolua_miniSandboxGameToLua_BackPack_replaceRune00);
   tolua_function(tolua_S,"getRuneNum",tolua_miniSandboxGameToLua_BackPack_getRuneNum00);
   tolua_function(tolua_S,"getRuneItem",tolua_miniSandboxGameToLua_BackPack_getRuneItem00);
   tolua_function(tolua_S,"getGridItem",tolua_miniSandboxGameToLua_BackPack_getGridItem00);
   tolua_function(tolua_S,"getGridNum",tolua_miniSandboxGameToLua_BackPack_getGridNum00);
   tolua_function(tolua_S,"getGridDuration",tolua_miniSandboxGameToLua_BackPack_getGridDuration00);
   tolua_function(tolua_S,"getGridMaxDuration",tolua_miniSandboxGameToLua_BackPack_getGridMaxDuration00);
   tolua_function(tolua_S,"getGridEnchantNum",tolua_miniSandboxGameToLua_BackPack_getGridEnchantNum00);
   tolua_function(tolua_S,"getGridEnchantId",tolua_miniSandboxGameToLua_BackPack_getGridEnchantId00);
   tolua_function(tolua_S,"getGridEnchantColor",tolua_miniSandboxGameToLua_BackPack_getGridEnchantColor00);
   tolua_function(tolua_S,"getGridToolType",tolua_miniSandboxGameToLua_BackPack_getGridToolType00);
   tolua_function(tolua_S,"getGridEnough",tolua_miniSandboxGameToLua_BackPack_getGridEnough00);
   tolua_function(tolua_S,"getGridUserdata",tolua_miniSandboxGameToLua_BackPack_getGridUserdata00);
   tolua_function(tolua_S,"getGridUserdataStr",tolua_miniSandboxGameToLua_BackPack_getGridUserdataStr00);
   tolua_function(tolua_S,"getGridSidStr",tolua_miniSandboxGameToLua_BackPack_getGridSidStr00);
   tolua_function(tolua_S,"getModItemName",tolua_miniSandboxGameToLua_BackPack_getModItemName00);
   tolua_function(tolua_S,"getModItemDesc",tolua_miniSandboxGameToLua_BackPack_getModItemDesc00);
   tolua_function(tolua_S,"getModExtradata",tolua_miniSandboxGameToLua_BackPack_getModExtradata00);
   tolua_function(tolua_S,"getGridInfo",tolua_miniSandboxGameToLua_BackPack_getGridInfo00);
   tolua_function(tolua_S,"getGridJsonxxInfo",tolua_miniSandboxGameToLua_BackPack_getGridJsonxxInfo00);
   tolua_function(tolua_S,"setGridInfo",tolua_miniSandboxGameToLua_BackPack_setGridInfo00);
   tolua_function(tolua_S,"setGridJsonxxInfo",tolua_miniSandboxGameToLua_BackPack_setGridJsonxxInfo00);
   tolua_function(tolua_S,"getGridSortId",tolua_miniSandboxGameToLua_BackPack_getGridSortId00);
   tolua_function(tolua_S,"getGridCount",tolua_miniSandboxGameToLua_BackPack_getGridCount00);
   tolua_function(tolua_S,"getGridItemName",tolua_miniSandboxGameToLua_BackPack_getGridItemName00);
   tolua_function(tolua_S,"getGridMaxStack",tolua_miniSandboxGameToLua_BackPack_getGridMaxStack00);
   tolua_function(tolua_S,"setItem",tolua_miniSandboxGameToLua_BackPack_setItem00);
   tolua_function(tolua_S,"addItemToEquip",tolua_miniSandboxGameToLua_BackPack_addItemToEquip00);
   tolua_function(tolua_S,"removeItemFromEquip",tolua_miniSandboxGameToLua_BackPack_removeItemFromEquip00);
   tolua_function(tolua_S,"setItemWithoutLimit",tolua_miniSandboxGameToLua_BackPack_setItemWithoutLimit00);
   tolua_function(tolua_S,"doRepair",tolua_miniSandboxGameToLua_BackPack_doRepair00);
   tolua_function(tolua_S,"showRecipeProduct",tolua_miniSandboxGameToLua_BackPack_showRecipeProduct00);
   tolua_function(tolua_S,"updateProductContainer",tolua_miniSandboxGameToLua_BackPack_updateProductContainer00);
   tolua_function(tolua_S,"updateCookBookProductContainer",tolua_miniSandboxGameToLua_BackPack_updateCookBookProductContainer00);
   tolua_function(tolua_S,"updateCraftContainer",tolua_miniSandboxGameToLua_BackPack_updateCraftContainer00);
   tolua_function(tolua_S,"updateCookBookContainer",tolua_miniSandboxGameToLua_BackPack_updateCookBookContainer00);
   tolua_function(tolua_S,"addItem",tolua_miniSandboxGameToLua_BackPack_addItem00);
   tolua_function(tolua_S,"replaceItem_byGridCopyData",tolua_miniSandboxGameToLua_BackPack_replaceItem_byGridCopyData00);
   tolua_function(tolua_S,"replaceItemByNum",tolua_miniSandboxGameToLua_BackPack_replaceItemByNum00);
   tolua_function(tolua_S,"removeItem",tolua_miniSandboxGameToLua_BackPack_removeItem00);
   tolua_function(tolua_S,"removeItemInNormalPack",tolua_miniSandboxGameToLua_BackPack_removeItemInNormalPack00);
   tolua_function(tolua_S,"clearPackByType",tolua_miniSandboxGameToLua_BackPack_clearPackByType00);
   tolua_function(tolua_S,"clearPackByType",tolua_miniSandboxGameToLua_BackPack_clearPackByType01);
   tolua_function(tolua_S,"clearPack",tolua_miniSandboxGameToLua_BackPack_clearPack00);
   tolua_function(tolua_S,"clearPack",tolua_miniSandboxGameToLua_BackPack_clearPack01);
   tolua_function(tolua_S,"shiftMoveItem",tolua_miniSandboxGameToLua_BackPack_shiftMoveItem00);
   tolua_function(tolua_S,"moveItem",tolua_miniSandboxGameToLua_BackPack_moveItem00);
   tolua_function(tolua_S,"swapItem",tolua_miniSandboxGameToLua_BackPack_swapItem00);
   tolua_function(tolua_S,"canPutItem",tolua_miniSandboxGameToLua_BackPack_canPutItem00);
   tolua_function(tolua_S,"setCreateModeShortCut",tolua_miniSandboxGameToLua_BackPack_setCreateModeShortCut00);
   tolua_function(tolua_S,"enoughGridForItem",tolua_miniSandboxGameToLua_BackPack_enoughGridForItem00);
   tolua_function(tolua_S,"enoughGridForItemMaxNum",tolua_miniSandboxGameToLua_BackPack_enoughGridForItemMaxNum00);
   tolua_function(tolua_S,"getShorCutEmptyGridNum",tolua_miniSandboxGameToLua_BackPack_getShorCutEmptyGridNum00);
   tolua_function(tolua_S,"index2Grid",tolua_miniSandboxGameToLua_BackPack_index2Grid00);
   tolua_function(tolua_S,"getItemCountInNormalPack",tolua_miniSandboxGameToLua_BackPack_getItemCountInNormalPack00);
   tolua_function(tolua_S,"searchNormalPack",tolua_miniSandboxGameToLua_BackPack_searchNormalPack00);
   tolua_function(tolua_S,"getSameGroupItemCountInNormalPack",tolua_miniSandboxGameToLua_BackPack_getSameGroupItemCountInNormalPack00);
   tolua_function(tolua_S,"afterChangeGrid",tolua_miniSandboxGameToLua_BackPack_afterChangeGrid00);
   tolua_function(tolua_S,"isHomeLandGameMakerMode",tolua_miniSandboxGameToLua_BackPack_isHomeLandGameMakerMode00);
   tolua_function(tolua_S,"getShortcutStartIndex",tolua_miniSandboxGameToLua_BackPack_getShortcutStartIndex00);
   tolua_function(tolua_S,"setTmpShortcutMode",tolua_miniSandboxGameToLua_BackPack_setTmpShortcutMode00);
   tolua_function(tolua_S,"isTmpShortcutMode",tolua_miniSandboxGameToLua_BackPack_isTmpShortcutMode00);
   tolua_function(tolua_S,"getShortcutGridCount",tolua_miniSandboxGameToLua_BackPack_getShortcutGridCount00);
   tolua_function(tolua_S,"getEmptyBagIndex",tolua_miniSandboxGameToLua_BackPack_getEmptyBagIndex00);
   tolua_function(tolua_S,"getEmptyShortcutIndex",tolua_miniSandboxGameToLua_BackPack_getEmptyShortcutIndex00);
   tolua_function(tolua_S,"tryAddItem_byGrid",tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGrid00);
   tolua_function(tolua_S,"tryAddItem_byGridCopyData",tolua_miniSandboxGameToLua_BackPack_tryAddItem_byGridCopyData00);
   tolua_function(tolua_S,"getExtBackPackInfo",tolua_miniSandboxGameToLua_BackPack_getExtBackPackInfo00);
   tolua_function(tolua_S,"loadExtBackPackInfo",tolua_miniSandboxGameToLua_BackPack_loadExtBackPackInfo00);
   tolua_function(tolua_S,"moveItem",tolua_miniSandboxGameToLua_BackPack_moveItem01);
   tolua_function(tolua_S,"placeItem",tolua_miniSandboxGameToLua_BackPack_placeItem00);
   tolua_function(tolua_S,"getContainer",tolua_miniSandboxGameToLua_BackPack_getContainer00);
   tolua_function(tolua_S,"takeItemFrom",tolua_miniSandboxGameToLua_BackPack_takeItemFrom00);
   tolua_function(tolua_S,"addStorageItem",tolua_miniSandboxGameToLua_BackPack_addStorageItem00);
   tolua_function(tolua_S,"mendItem",tolua_miniSandboxGameToLua_BackPack_mendItem00);
   tolua_function(tolua_S,"findItemInNormalPack",tolua_miniSandboxGameToLua_BackPack_findItemInNormalPack00);
   tolua_function(tolua_S,"mergePack",tolua_miniSandboxGameToLua_BackPack_mergePack00);
   tolua_function(tolua_S,"mergeItem",tolua_miniSandboxGameToLua_BackPack_mergeItem00);
   tolua_function(tolua_S,"attachContainer",tolua_miniSandboxGameToLua_BackPack_attachContainer00);
   tolua_function(tolua_S,"detachContainer",tolua_miniSandboxGameToLua_BackPack_detachContainer00);
   tolua_function(tolua_S,"ItemChangeForTrigger",tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger00);
   tolua_function(tolua_S,"ItemChangeForTrigger",tolua_miniSandboxGameToLua_BackPack_ItemChangeForTrigger01);
   tolua_function(tolua_S,"copyCurShotcutToOldEdit",tolua_miniSandboxGameToLua_BackPack_copyCurShotcutToOldEdit00);
   tolua_function(tolua_S,"copyOldEditToCurShotcut",tolua_miniSandboxGameToLua_BackPack_copyOldEditToCurShotcut00);
   tolua_function(tolua_S,"insertItemsToCurShotcutEdit",tolua_miniSandboxGameToLua_BackPack_insertItemsToCurShotcutEdit00);
   tolua_function(tolua_S,"numberOfItemInShortcut",tolua_miniSandboxGameToLua_BackPack_numberOfItemInShortcut00);
   tolua_function(tolua_S,"getSpecialItemName",tolua_miniSandboxGameToLua_BackPack_getSpecialItemName00);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"CraftingQueue","CraftingQueue","",tolua_collect_CraftingQueue);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(CraftingQueue, "CraftingQueue", "CraftingQueue", "")
#endif
  #else
  tolua_cclass(tolua_S,"CraftingQueue","CraftingQueue","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(CraftingQueue, "CraftingQueue", "CraftingQueue", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"CraftingQueue");
   tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_CraftingQueue_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_CraftingQueue_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_CraftingQueue_new00_local);
   tolua_function(tolua_S,"getCountDown",tolua_miniSandboxGameToLua_CraftingQueue_getCountDown00);
   tolua_function(tolua_S,"getProgress",tolua_miniSandboxGameToLua_CraftingQueue_getProgress00);
   tolua_function(tolua_S,"getQueueSize",tolua_miniSandboxGameToLua_CraftingQueue_getQueueSize00);
   tolua_function(tolua_S,"getCraftId",tolua_miniSandboxGameToLua_CraftingQueue_getCraftId00);
   tolua_function(tolua_S,"getCraftCount",tolua_miniSandboxGameToLua_CraftingQueue_getCraftCount00);
   tolua_function(tolua_S,"getCraftTicks",tolua_miniSandboxGameToLua_CraftingQueue_getCraftTicks00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"DangerNightManager","DangerNightManager","SandboxMgrBase",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(DangerNightManager, "DangerNightManager", "DangerNightManager", "SandboxMgrBase")
#endif
  tolua_beginmodule(tolua_S,"DangerNightManager");
   tolua_variable(tolua_S,"__DangerNightManagerInterface__",tolua_get_DangerNightManager___DangerNightManagerInterface__,NULL);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"VoidMonsterInfo","VoidMonsterInfo","",tolua_collect_VoidMonsterInfo);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(VoidMonsterInfo, "VoidMonsterInfo", "VoidMonsterInfo", "")
#endif
  #else
  tolua_cclass(tolua_S,"VoidMonsterInfo","VoidMonsterInfo","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(VoidMonsterInfo, "VoidMonsterInfo", "VoidMonsterInfo", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"VoidMonsterInfo");
   tolua_variable(tolua_S,"monsterID",tolua_get_VoidMonsterInfo_monsterID,tolua_set_VoidMonsterInfo_monsterID);
   tolua_variable(tolua_S,"monsterCount",tolua_get_VoidMonsterInfo_monsterCount,tolua_set_VoidMonsterInfo_monsterCount);
   tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_VoidMonsterInfo_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_VoidMonsterInfo_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_VoidMonsterInfo_new00_local);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"VoidMonsterTeamInfo","VoidMonsterTeamInfo","",tolua_collect_VoidMonsterTeamInfo);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(VoidMonsterTeamInfo, "VoidMonsterTeamInfo", "VoidMonsterTeamInfo", "")
#endif
  #else
  tolua_cclass(tolua_S,"VoidMonsterTeamInfo","VoidMonsterTeamInfo","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(VoidMonsterTeamInfo, "VoidMonsterTeamInfo", "VoidMonsterTeamInfo", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"VoidMonsterTeamInfo");
   tolua_variable(tolua_S,"monsterInfos",tolua_get_VoidMonsterTeamInfo_monsterInfos,tolua_set_VoidMonsterTeamInfo_monsterInfos);
   tolua_variable(tolua_S,"teamName",tolua_get_VoidMonsterTeamInfo_teamName,tolua_set_VoidMonsterTeamInfo_teamName);
   tolua_variable(tolua_S,"proportion",tolua_get_VoidMonsterTeamInfo_proportion,tolua_set_VoidMonsterTeamInfo_proportion);
   tolua_variable(tolua_S,"fantomId",tolua_get_VoidMonsterTeamInfo_fantomId,tolua_set_VoidMonsterTeamInfo_fantomId);
   tolua_variable(tolua_S,"tick",tolua_get_VoidMonsterTeamInfo_tick,tolua_set_VoidMonsterTeamInfo_tick);
   tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_new00_local);
   tolua_function(tolua_S,"CreateMonsterInfo",tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_CreateMonsterInfo00);
   tolua_function(tolua_S,"GetMonsterInfo",tolua_miniSandboxGameToLua_VoidMonsterTeamInfo_GetMonsterInfo00);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"SiegeDifficultInfo","SiegeDifficultInfo","",tolua_collect_SiegeDifficultInfo);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SiegeDifficultInfo, "SiegeDifficultInfo", "SiegeDifficultInfo", "")
#endif
  #else
  tolua_cclass(tolua_S,"SiegeDifficultInfo","SiegeDifficultInfo","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SiegeDifficultInfo, "SiegeDifficultInfo", "SiegeDifficultInfo", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"SiegeDifficultInfo");
   tolua_variable(tolua_S,"monsterTeamInfos",tolua_get_SiegeDifficultInfo_monsterTeamInfos,tolua_set_SiegeDifficultInfo_monsterTeamInfos);
   tolua_variable(tolua_S,"difficultLevel",tolua_get_SiegeDifficultInfo_difficultLevel,tolua_set_SiegeDifficultInfo_difficultLevel);
   tolua_function(tolua_S,"new",tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxGameToLua_SiegeDifficultInfo_new00_local);
   tolua_function(tolua_S,"CreateMonsterTeamInfo",tolua_miniSandboxGameToLua_SiegeDifficultInfo_CreateMonsterTeamInfo00);
   tolua_function(tolua_S,"GetMonsterTeamInfo",tolua_miniSandboxGameToLua_SiegeDifficultInfo_GetMonsterTeamInfo00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"SummonMonsterSiegeMgr","SummonMonsterSiegeMgr","SummonMonsterSiegeMgrInterface",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SummonMonsterSiegeMgr, "SummonMonsterSiegeMgr", "SummonMonsterSiegeMgr", "SummonMonsterSiegeMgrInterface")
#endif
  tolua_beginmodule(tolua_S,"SummonMonsterSiegeMgr");
   tolua_function(tolua_S,"CreateSiegeDifficultInfo",tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_CreateSiegeDifficultInfo00);
   tolua_function(tolua_S,"GetSiegeDifficultInfo",tolua_miniSandboxGameToLua_SummonMonsterSiegeMgr_GetSiegeDifficultInfo00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"ActorEventListen","ActorEventListen","ObserverEventListenIns",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorEventListen, "ActorEventListen", "ActorEventListen", "ObserverEventListenIns")
#endif
  tolua_beginmodule(tolua_S,"ActorEventListen");
   tolua_function(tolua_S,"RegEvent",tolua_miniSandboxGameToLua_ActorEventListen_RegEvent00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"SandboxActorSubsystem","SandboxActorSubsystem","ISandboxActorSubsystem",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SandboxActorSubsystem, "SandboxActorSubsystem", "SandboxActorSubsystem", "ISandboxActorSubsystem")
#endif
  tolua_beginmodule(tolua_S,"SandboxActorSubsystem");
   tolua_function(tolua_S,"createProjectileWithShooter",tolua_miniSandboxGameToLua_SandboxActorSubsystem_createProjectileWithShooter00);
   tolua_function(tolua_S,"shootArrowAuto",tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootArrowAuto00);
   tolua_function(tolua_S,"shootProjectileAuto",tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootProjectileAuto00);
   tolua_function(tolua_S,"throwItemAuto",tolua_miniSandboxGameToLua_SandboxActorSubsystem_throwItemAuto00);
   tolua_function(tolua_S,"shootImpulseAuto",tolua_miniSandboxGameToLua_SandboxActorSubsystem_shootImpulseAuto00);
   tolua_function(tolua_S,"createActorFirework",tolua_miniSandboxGameToLua_SandboxActorSubsystem_createActorFirework00);
   tolua_function(tolua_S,"dragonFlowerAttack",tolua_miniSandboxGameToLua_SandboxActorSubsystem_dragonFlowerAttack00);
   tolua_function(tolua_S,"getAllPlayers",tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllPlayers00);
   tolua_function(tolua_S,"getAllNearPlayersUin",tolua_miniSandboxGameToLua_SandboxActorSubsystem_getAllNearPlayersUin00);
   tolua_function(tolua_S,"getModBlockMaterial",tolua_miniSandboxGameToLua_SandboxActorSubsystem_getModBlockMaterial00);
   tolua_variable(tolua_S,"__IEventExcuteEx__",tolua_get_SandboxActorSubsystem___IEventExcuteEx__,NULL);
  tolua_endmodule(tolua_S);
  tolua_function(tolua_S,"GetSandboxActorSubsystemIns",tolua_miniSandboxGameToLua_GetSandboxActorSubsystemIns00);
  tolua_function(tolua_S,"GetSandboxActorSubsystem",tolua_miniSandboxGameToLua_GetSandboxActorSubsystem00);
  tolua_cclass(tolua_S,"GameEffectManager","GameEffectManager","EffectManager",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(GameEffectManager, "GameEffectManager", "GameEffectManager", "EffectManager")
#endif
  tolua_beginmodule(tolua_S,"GameEffectManager");
   tolua_function(tolua_S,"playBlockCrackEffect",tolua_miniSandboxGameToLua_GameEffectManager_playBlockCrackEffect00);
   tolua_function(tolua_S,"stopBlockCrackEffect",tolua_miniSandboxGameToLua_GameEffectManager_stopBlockCrackEffect00);
   tolua_function(tolua_S,"changeBlockCrackEffectModel",tolua_miniSandboxGameToLua_GameEffectManager_changeBlockCrackEffectModel00);
   tolua_function(tolua_S,"playPickItemEffect",tolua_miniSandboxGameToLua_GameEffectManager_playPickItemEffect00);
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_miniSandboxGameToLua (lua_State* tolua_S) {
 return tolua_miniSandboxGameToLua_open(tolua_S);
};
#endif

