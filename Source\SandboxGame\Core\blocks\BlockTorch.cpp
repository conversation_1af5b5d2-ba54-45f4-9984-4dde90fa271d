
#include "BlockTorch.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "WorldProxy.h"

#include "Graphics/Texture.h"
//#include "OgreMaterial.h"
#include "special_blockid.h"
#include "CoreCommonDef.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "world.h"
#include "WorldRender.h"
#include "chunk.h"

const char* pTorchName = "particles/1017.ent";
const char* pIceTorchName = "prefab/particles/ice_fazhang_01.prefab";
IMPLEMENT_BLOCKMATERIAL(TorchMaterial)
//IMPLEMENT_BLOCKINSTANCE(TorchMaterial)

using namespace MINIW;

static bool HasOxygenSupplied(World *pworld, const WCoord &blockpos)
{
	for(int i=0; i<6; i++)
	{
		if(pworld->getBlockID(NeighborCoord(blockpos,i)) == BLOCK_PLANTSPACE_OXYGEN) return true;
	}
	return false;
}

WCoord calNewWCoord(World* pworld, const WCoord& blockpos)
{
	int dir = pworld->getBlockData(blockpos);
	int dxz = 32;
	int dy = 33;
	WCoord pos = blockpos * BLOCK_SIZE + WCoord(50, 50, 50);
	if (dir < 4)
	{
		pos.y += dy;
		if (dir == 0) pos.x -= dxz;
		else if (dir == 1) pos.x += dxz;
		else if (dir == 2) pos.z -= dxz;
		else if (dir == 3) pos.z += dxz;
	}
	return pos;
}

WCoord calNewIceWCoord(World* pworld, const WCoord& blockpos)
{
	int dir = pworld->getBlockData(blockpos);
	int dxz = 36;
	int dy = 26;
	WCoord pos = blockpos * BLOCK_SIZE + WCoord(46, 47, 50);
	if (dir < 4)
	{
		pos.y += dy;
		if (dir == 0) pos.x -= dxz;
		else if (dir == 1) pos.x += dxz;
		else if (dir == 2) pos.z -= dxz;
		else if (dir == 3) pos.z += dxz;
	}
	return pos;
}

void TorchMaterial::initGeomName()
{
	m_geomName = "torch";
}
void TorchMaterial::onPlayRandEffect(World* pworld, const WCoord& blockpos)
{
	if (!pworld->getEffectMgr()->isExistParticle(blockpos))
	{
		onPlayEffect(pworld, blockpos);
	}
}

void TorchMaterial::onPlayEffect(World* pworld, const WCoord& blockpos)
{
	static bool bUsePrefab = !(Rainbow::GetFileManager().IsFileExist(pTorchName));
	if (getBlockResID() == BLOCK_TORCH || m_BlockResID == BLOCK_SMALL_TORCH || ITEM_SOCTORCH == m_BlockResID)
	{
		WCoord pos = calNewWCoord(pworld, blockpos);
		WorldRenderer* world = pworld->getRender();
		auto pParticle = pworld->getEffectMgr()->playParticleEffectAsync(pTorchName, pos, 0, 0, 0, false);
		if (bUsePrefab)
		{
			pworld->getEffectMgr()->addBlockParticle(blockpos, pParticle);
		}
	}
	else if(m_BlockResID == BLOCK_ICE_TORCH)
	{
		WCoord pos = calNewIceWCoord(pworld, blockpos);
		WorldRenderer* world = pworld->getRender();
		auto pParticle = pworld->getEffectMgr()->playParticleEffectAsync(pIceTorchName, pos, 0, 0, 0, false);
		pworld->getEffectMgr()->addBlockParticle(blockpos, pParticle);
	}


}

void TorchMaterial::onStopEffect(World* pworld, const WCoord& blockpos)
{
	auto pParticle = pworld->getEffectMgr()->getParticle(blockpos);
	if (pParticle != nullptr)
	{		
		pworld->getEffectMgr()->stopParticleEffect(pTorchName, pParticle, false);
		pworld->getEffectMgr()->removeBlockParticle(blockpos, pParticle);
	}
	
}

void TorchMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	int dir = pworld->getBlockData(blockpos);
	assert(dir < 5);

	WCoord minpos, maxpos;
	WCoord origin = blockpos * BLOCK_SIZE;

	size_t meshindex = dir==DIR_NEG_Y? 1:0;
	getGeom()->getBoundBox(minpos, maxpos, meshindex, 1.0f, dir&3);
	coldetect->addObstacle(minpos+origin, maxpos+origin);
}

int TorchMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	if(face==DIR_NEG_Y || face==DIR_POS_Y)
	{
		if (canPlaceTorchOn(pworld->getWorldProxy(), DownCoord(blockpos))) return DIR_NEG_Y;
		else face = DIR_NEG_X;
	}

	for(int i=0; i<4; i++)
	{
		int dir = (face + i)&3;
		BlockMaterial *mtl = pworld->getBlockMaterial(NeighborCoord(blockpos,dir));
		if(mtl->isOpaqueCube() || mtl->BlockTypeId() == BlockType_Architecture)
		{
			return dir;
		}
	}

	return -1;
}

bool TorchMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	for(int i=0; i<4; i++)
	{
		BlockMaterial *mtl = pworld->getBlockMaterial(NeighborCoord(blockpos,i));
		if(mtl->getBlockResID()==BLOCK_UNLOAD || mtl->isOpaqueCube() || mtl->BlockTypeId() == BlockType_Architecture) return true;
	}

	return canPlaceTorchOn(pworld, blockpos+WCoord(0,-1,0));
}

void TorchMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	if(!checkDrop(pworld, blockpos))
	{
		if(pworld->getCurMapID() >= MAPID_MENGYANSTAR && getBlockResID()==BLOCK_TORCH && !HasOxygenSupplied(pworld, blockpos))
		{
			int blockdata = pworld->getBlockData(blockpos);
			pworld->setBlockAll(blockpos, BLOCK_TORCH_OFF, blockdata);
		}

		if (pworld->getCurMapID() >= MAPID_MENGYANSTAR && getBlockResID() == ITEM_SOCTORCH && !HasOxygenSupplied(pworld, blockpos))
		{
			int blockdata = pworld->getBlockData(blockpos);
			pworld->setBlockAll(blockpos, BLOCK_TORCH_OFF, blockdata);
		}
	}
}

bool TorchMaterial::checkDrop(World *pworld, const WCoord &blockpos)
{
	if(!canPutOntoPos(pworld->getWorldProxy(), blockpos))
	{
		if(pworld->getBlockID(blockpos) == getBlockResID())
		{
			dropBlockAsItem(pworld, blockpos);
			pworld->setBlockAir(blockpos);
		}
		return true;
	}
	else
	{
		int placedir = pworld->getBlockData(blockpos);
		bool drop = false;

		if(placedir < 4)
		{
			BlockMaterial *mtl = pworld->getBlockMaterial(NeighborCoord(blockpos,placedir));
			if(mtl->getBlockResID()!=BLOCK_UNLOAD && !mtl->isOpaqueCube() && mtl->BlockTypeId() != BlockType_Architecture)
			{
				drop = true;
			}
		}

		if (placedir == 4 && !canPlaceTorchOn(pworld->getWorldProxy(), blockpos + WCoord(0, -1, 0)))
		{
			drop = true;
		}

		if(drop)
		{
			dropBlockAsItem(pworld, blockpos);
			pworld->setBlockAir(blockpos);
			return true;
		}
		else
		{
			return false;
		}
	}
}

bool TorchMaterial::canPlaceTorchOn(WorldProxy *pworld, const WCoord &blockpos)
{
	if(pworld->doesBlockHaveSolidTopSurface(blockpos))
	{
		return true;
	}
	else
	{
		auto pmtl = pworld->getBlockMaterial(blockpos);
		if (pmtl->BlockTypeId() == BlockType_Architecture) return true;
		int blockid = pmtl->m_Def->ID;//pworld->getBlockID(blockpos);
		return blockid==BLOCK_FENCE || blockid==BLOCK_FENCE_HELL || blockid==BLOCK_COBBLE_WALL || blockid==BLOCK_MOSSY_WALL || blockid>=BLOCK_GLASS_START&&blockid<=BLOCK_GLASS_END || (blockid >= 1206 && blockid <= 1222);
	}
}

int TorchMaterial::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int dir = sectionData->getBlock(blockpos).getData();
	assert(dir < 5);
	if(dir < 4)
	{
		idbuf[0] = 0;
		dirbuf[0] = dir;
	}
	else 
	{
		idbuf[0] = 1;
		dirbuf[0] = DIR_NEG_Z;
	}

	return 1;
}

int TorchMaterial::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 1;
	dirbuf[0] = DIR_NEG_Z;

	return 1;
}

void TorchMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	if(getBlockResID() == BLOCK_TORCH || getBlockResID() == ITEM_SOCTORCH)
	{
		Chunk *pchunk = pworld->getChunk(blockpos);
		if(pchunk)
		{
			WCoord offset = blockpos - pchunk->m_Origin;
			pchunk->removeSearchBlock(offset.x, offset.y, offset.z, m_BlockResID);
		}
	}
}

void TorchMaterial::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	if(getBlockResID() == BLOCK_TORCH || getBlockResID() == ITEM_SOCTORCH)
	{
		Chunk *pchunk = pworld->getChunk(blockpos);
		if(pchunk)
		{
			WCoord offset = blockpos - pchunk->m_Origin;
			pchunk->addSearchBlock(offset.x, offset.y, offset.z, getBlockResID()); 
		}
	}
}

void TorchMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	ModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);

	if(pworld->getCurMapID() >= MAPID_MENGYANSTAR && m_BlockResID==BLOCK_TORCH && !HasOxygenSupplied(pworld, blockpos))
	{
		pworld->getEffectMgr()->playParticleEffectAsync("particles/item_817_extinguish.ent", BlockCenterCoord(blockpos), 20);

		int blockdata = pworld->getBlockData(blockpos);
		pworld->setBlockAll(blockpos, BLOCK_TORCH_OFF, blockdata);
	}

	if (pworld->getCurMapID() >= MAPID_MENGYANSTAR && m_BlockResID == ITEM_SOCTORCH && !HasOxygenSupplied(pworld, blockpos))
	{
		pworld->getEffectMgr()->playParticleEffectAsync("particles/item_817_extinguish.ent", BlockCenterCoord(blockpos), 20);

		int blockdata = pworld->getBlockData(blockpos);
		pworld->setBlockAll(blockpos, BLOCK_TORCH_OFF, blockdata);
	}
}

int TorchMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int curDir = blockdata;
	if (curDir > DIR_POS_Z)
		return blockdata;
	
	curDir = this->commonConvertDataByRotate(curDir, rotatetype);

	return curDir;
}


