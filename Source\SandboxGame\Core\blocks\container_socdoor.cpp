﻿#include "container_socdoor.h"

SocDoorContainer::SocDoorContainer()
{
	_lock.Reset();
}

SocDoorContainer::SocDoorContainer(const WCoord& blockpos, const int blockId) :ErosionContainer(blockpos, blockId)
{
	_lock.Reset();
}

SocDoorContainer::~SocDoorContainer()
{
}

flatbuffers::Offset<FBSave::ChunkContainer> SocDoorContainer::save(SAVE_BUFFER_BUILDER& builder)
{
	//auto basedata = saveContainerCommon(builder);
	auto basedata = ErosionContainer::saveContainerErosion(builder);

	auto lockdata = FBSave::CreateSocLock(builder,
		_lock.type,
		_lock.status,
		_lock.mainid,
		_lock.lockid,
		_lock.lockpassword,
		_lock.main_lockpassword,
		builder.CreateVector(_lock.mainids.data(), _lock.mainids.size())
		);
	auto actor = FBSave::Create<PERSON><PERSON>r<PERSON>oc<PERSON><PERSON>(builder, basedata, lockdata);

	return FBSave::Create<PERSON>hunk<PERSON>ontainer(builder, FBSave::ContainerUnion_Container<PERSON><PERSON><PERSON><PERSON>, actor.Union());
}

bool SocDoorContainer::load(const void* srcdata)
{
	auto src = reinterpret_cast<const FBSave::ContainerSocDoor*>(srcdata);
	if (!src)
		return false;
	//loadContainerCommon(src->basedata());
	ErosionContainer::load(src->basedata());

	auto lockdata = src->lock();
	_lock.type = lockdata->type();
	_lock.status = lockdata->status();
	_lock.mainid = lockdata->mainid();
	_lock.lockid = lockdata->lockid();
	_lock.lockpassword = lockdata->lockpassword();
	_lock.main_lockpassword = lockdata->main_lockpassword();

	for (size_t i = 0; i < lockdata->mainids()->size(); i++)
	{
		int uin = lockdata->mainids()->Get(i);
		_lock.mainids.push_back(uin);
	}

	return true;
}

void SocDoorContainer::dropItems()
{
	BlockMaterial* pmtl = getBlockMtl();
	if (!pmtl)
	{
		return;
	}
	//todo 看看要不要掉密码锁
	//if (m_hasKey)
	//{
	//	pmtl->doDropItem(m_World, m_BlockPos, ITEM_KEY, 1);
	//}
}
