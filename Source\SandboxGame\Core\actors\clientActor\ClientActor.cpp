
#include "ClientActor.h"
#include "LivingLocoMotion.h"
#include "ActorBody.h"
#include "IActorBody.h"
#include "ActorVision.h"
#include "GameNetManager.h"
#include "ClientActorProjectile.h"
#include "MpActorManager.h"
#include "ActorVehicleAssemble.h"
#include "GameMode.h"
#include "container_driverseat.h"
#include "ActorChunkPos.h"
#include "ActorUpdateFrequency.h"
#include "ActorBindVehicle.h"
#include "ToAttackTargetComponent.h"
#include "BindActorComponent.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "ModelComponent.h"
#include "ClientInfoProxy.h"
#include "PlayerCommand.h"
#include "ClientActorHelper.h"
#include "ThornBallComponent.h"
#include "Entity/OgreEntity.h"
#include "SandBoxManager.h"
#include "world.h"
#include "EffectManager.h"
#include "chunk.h"
#include "VehicleWorld.h"
#include "ClientPlayer.h"
#include "RenderSection.h"
#include "defdata.h"
#include "ChargeJumpComponent.h"
#include "ActorObjArray_generated.h"
#include "UgcComponentArray_generated.h"
#include "LuaCallHelper/LuaCallHelper.h"
#include "PlayerLocoMotion.h"
#include "proto_common.pb.h"
#include "proto_hc.pb.h"
#include "ChunkSave_generated.h"
#include "ModelItemMesh.h"
#include "PathEntity.h"
#include "SceneEditorMeshGen.h"
#include "SandboxClientActorNavigationpathComponent.h"
#include "ActorManagerInterface.h"
#include "BlockMaterialMgr.h"
#include "ActorHorse.h"
#include "InputInfo.h"

#include "Components/MeshRenderer.h"
#include "Core/GameObject.h"
#include "BlockMaterialMgr.h"
#include "Render/ShaderMaterial/MaterialInstance.h"
#include "Graphics/Shaders/ShaderPropertySheet.h"
#include "ModPackMgr.h"
#include "GunGridDataComponent.h"
#include "backpack.h"

#if OGRE_PLATFORM == OGRE_PLATFORM_ANDROID || PLATFORM_OHOS
#else
    #ifdef IWORLD_ROBOT_TEST
        #include "BehaviorManager.h"
    #endif
#endif//defined(ANDROID)

#include "ActorEventListen.h"
#include "SunHurtComponent.h"
#include "SoundComponent.h"
#include "TriggerComponent.h"
#include "ClientActorFuncWrapper.h"
#include "ActionAttrStateComponent.h"
#include "FireBurnComponent.h"
#include "FallComponent.h"
#include "AttackedComponent.h"
#include "DieComponent.h"
#include "EffectComponent.h"
#include "DropItemComponent.h"
#include "ActorInPortal.h"
#include "worldMesh/MiniCraftRenderer.h"
#include "SwarmComponent.h"
#include "PhysicsComponent.h"
#include "ScriptComponent.h"
#include "Mesh/LegacySkinMeshRenderer.h"
#include "ClientActorComponent.h"
#include "navigationpath.h"
#include "SandboxIdDef.h"
#include "SandboxLoadSave.h"
#include "SandboxCustomBuffer.h"
#include "base/stream/SandboxStreamBuffer.h"
#include "sync/SyncSerialize.h"
#include "ActorMovingLight.h"
#include "LuaInterfaceProxy.h"
#include "SandboxGameDef.h"
#include "SandboxCustomNotify.h"
#include "BlockScene.h"
#include "SandboxCustomBuffer.h"
#include "base/stream/SandboxStreamBuffer.h"
#include "sync/SyncSerialize.h"
#include "SpaceActorManager.h"
#include "Optick/optick.h"
#include "PlayerControl.h"
#include "ActorManager.h"
#include "ViewerComponentActor.h"
#include "CustomModelMgr.h"
#include "UgcAssetHeader.h"
#include "BlockMesh.h"
#include "ActorModelLerpComponent.h"

#ifdef BUILD_MINI_EDITOR_APP
#include "SandboxCfg.h"
#endif
#include "special_blockid.h"
#include "MsgStreamData.h"
#include "ClientFlyMob.h"
#include "actorComponents/ClientFlyComponent.h"
#include "locoMotionComponents/TrixenieLocomotion.h"
#include "locoMotionComponents/ActorLocoMotion.h"
#include "ClientItem.h"
#include "ICloudProxy.h"

#include "IActorAttrib.h"
#include "ActorInPortal.h"
#include "MsgStreamData.h"
#include "FishingComponent.h"
#include "PlayerAttrib.h"
#include "AssetPipeline/ImageConverter.h"
#include "UgcAssetMgr.h"
#include "UGCActorBatchManager.h"
#include "ImageMesh.h"
#include "container_backpack.h"
#include "GridContainer.h"
#include "EquipGridContainer.h"
#include "Diagnostics/Stacktrace.h"
#include "Debug/DebugMgr.h"
#include "SkillComponent.h"
#include "CameraManager.h"
#include "AIUpdateFrequency.h"
#include "Common/ActorBodySafeHandle.h"

//#include <chrono>
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

std::map<ACTORBODY_EFFECT, std::tuple<std::string, bool, int> > localBodyFxName =
{
	make_pair(BODYFX_FIRE, make_tuple("BUFF_FIRE_1", false, 0)),
	make_pair(BODYFX_PORTAL,make_tuple("portal_feedback", false, 0)),
	make_pair(BODYFX_ACCUMFIRE,make_tuple("35021", false, 0)),
	make_pair(BODYFX_DRAGONFIRE, make_tuple("35022", false, 0)),
	make_pair(BODYFX_DRAGONSUMMON, make_tuple("3503_call", false, 0)),
	make_pair(BODYFX_TAME_SUCCEED, make_tuple("InteractTure", false, 0)),

	make_pair(BODYFX_TAME_FAILED, make_tuple("InteractFalse", false, 0)),

	make_pair(BODYFX_TAME_FOOD, make_tuple("MobTreat", false, 0)),
	make_pair(BODYFX_TAME_NOFOOD, make_tuple("MobFull", false, 0)),
	make_pair(BODYFX_MILKING, make_tuple("mob_3401_2", false, 0)),

	make_pair(BODYFX_AI_ANGRY, make_tuple("mob_3401_1", false, 0)),

	make_pair(BODYFX_AI_NEEDREEDS, make_tuple("mob_3401_3", false, 0)),

	make_pair(BODYFX_FORBIDDEN, make_tuple("mob_3401_4", false, 0)),

	make_pair(BODYFX_FEAR, make_tuple("MobFear", false, 0)),

	make_pair(BODYFX_ROLECOLLECT, make_tuple("role_collect", false, 0)),
	make_pair(BODYFX_ROLEJUMP, make_tuple("role_jump", false, 0)),
	make_pair(BODYFX_DEADPROTECT, make_tuple("role_deadprotect", false, 0)),

	make_pair(BODYFX_DRAGONDIE0, make_tuple("3502_die", false, 0)),
	make_pair(BODYFX_DRAGONDIE1, make_tuple("3503_die", false, 0)),
	make_pair(BODYFX_DRAGONDIE2, make_tuple("3504_die", false, 0)),
	make_pair(BODYFX_HORSE_FLY, make_tuple("horse_3433_fly", false, 0)),
	make_pair(BODYFX_HORSE_BENTENG, make_tuple("horse_3438_fly", false, 0)),


	make_pair(BODYFX_DANCE, make_tuple("mob_3409_1", false, 0)),
	make_pair(BODYFX_TRANSPORT, make_tuple("item_9_2", false, 0)),
	make_pair(BODYFX_AI_SLEEP, make_tuple("mob_3402_1", false, 0)),


	make_pair(BODYFX_INTERACTION, make_tuple("mob_3120", false, 0)),
	make_pair(BODYFX_CONCEAL, make_tuple("3407_1", false, 0)),
	make_pair(BODYFX_SCORPION_CONCEAL, make_tuple("100076_1", false, 0)),
	make_pair(BODYFX_WEAPON_FIRE, make_tuple("31051_1", false, 0)),
	make_pair(BODYFX_DIZZY, make_tuple("mob_3102_2", false, 0)),
	make_pair(BODYFX_MAKETROUBLE, make_tuple("mob_3102_1", false, 0)),
	make_pair(BODYFX_AI_HUNGRY, make_tuple("mob_3416_1", false, 0)),


	make_pair(BODYFX_BALL_CHARGE, make_tuple("ball_shoot_charge", false, 0)),

	make_pair(BODYFX_BASKETBALL_CHARGE, make_tuple("ball_shoot_charge", false, 0)),
	make_pair(BODYFX_BALL_SHOOT_RELEASE, make_tuple("ball_shoot_release", false, 0)),
	make_pair(BASKETBALL_OBSTRUCT, make_tuple("item_hudun", false, 0)),

	make_pair(BODYFX_BASKETBALL_DRIBBLERUSH, make_tuple("item_chongci", false, 0)),
	make_pair(BASKETBALL_GRAB, make_tuple("item_lanqiuxuli", false, 0)),
	make_pair(BODYFX_AI_STAND_SLEEP, make_tuple("mob_3402_1", false, 0)),
	make_pair(BODYFX_MILKING_TOXIC, make_tuple("mob_3401_412", false, 0)),
};

unsigned int ClientActor::m_CurActorFrame = 0;
bool ClientActor::s_AABBUseHitBox = false;

long long ClientActor::mg_cur_id = 0;

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
bool ClientActor::s_SaveActorCommonForSyncFlag = false;
#endif // SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX


static WCoord s_Wcoord(0, -1, 0);
IMPLEMENT_SCENEOBJECTCLASS(ClientActor)
ReflexClassParam<ClientActor, Rainbow::Vector3f> ClientActor::R_LocomotionPos(0, "Position", "base", &ClientActor::LocomotionPosGet, &ClientActor::LocomotionPosSet, ReflexConfig::REG_LOCAL);
ReflexClassParam<ClientActor, bool> ClientActor::R_Visible(1, "Visible", "base", &ClientActor::IsVisible, &ClientActor::SetVisible);

Rainbow::AABB ClientActor::GetAABB()
{
	if (getObjType() == OBJ_TYPE_GAMEOBJECT)
	{
		if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
		{
			Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
			if (pMeshRenderer)
			{
				Rainbow::AABB box = pMeshRenderer->GetWorldBounds();
				return box;
			}
		}

		if (getLocoMotion())
		{
			auto pEntity = getEntity();
			if (pEntity && pEntity->GetMainModel())
			{
				return pEntity->GetMainModel()->GetWorldBounds();
			}
		}

		if (!(m_preAABB == Rainbow::AABB::zero))
		{
			return m_preAABB;
		}
	}
	

	if (getLocoMotion())
	{
		Rainbow::AABB box = Rainbow::AABB::zero;
		if (s_AABBUseHitBox)
		{
			getLocoMotion()->getHitCollideBox(box);
		}
		else
		{
			getLocoMotion()->getCollideBox(box);
		}
		return box;
	}

    auto trans = GetTransform();
    if (trans) {
        return Rainbow::AABB(trans->GetWorldPosition(), Vector3f(BLOCK_FSIZE / 2.0f));
    }
	//返回一个默认值
	return Rainbow::AABB(Vector3f(0.0f), Vector3f(BLOCK_FSIZE / 2.0f));
}

void ClientActor::PrepareAABB()
{
	Rainbow::AABB box = PrepareAABBInner();
	if (!(m_preAABB == box))
	{
		m_preAABB = box;
		if (m_pWorld && m_pWorld->getActorMgr())
		{
			m_pWorld->getActorMgr()->ToCastMgr()->CheckLargeGameObject(GetAncestor());
		}
	}

	return;
}

Rainbow::AABB ClientActor::PrepareAABBInner()
{
	Rainbow::AABB box = Rainbow::AABB::zero;

	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		return box;
	}

	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
		if(pMeshRenderer)
		{
			Rainbow::AABB box = pMeshRenderer->GetWorldBounds();
			return box;
		}
	}
	
	if (getLocoMotion())
	{
		getLocoMotion()->getHitCollideBox(box);
		auto pEntity = getEntity();
		auto pBody = getBody();
		if (pEntity && pEntity->GetMainModel())
		{
			box = pEntity->GetMainModel()->GetWorldBounds();
		}
		else if (pEntity && pEntity->GetBindObjectCount() > 0 && pBody && pBody->getModelType() == ModelType_Object && pBody->getExtraData() > 0)
		{
			auto bindobj = pEntity->GetBindObject(0);
			BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(bindobj);
			ImageMesh* pImageMesh = dynamic_cast<ImageMesh*>(bindobj);
			if (pBlockMesh != NULL && pBlockMesh->getMesh() && pBlockMesh->GetTransform())
			{
				Transform* pActorTrans = GetTransform();
				Transform* pTrans = pBlockMesh->GetTransform();

				if (pActorTrans && pTrans)
				{
					//直接拿WorldMatrix有时候Entity的Transform没更新, 改成根据Actor来算
					Matrix4x4f actorMat = pActorTrans->GetLocalToWorldMatrix();					
					Vector3f worldPos = pTrans->GetLocalPosition();
					Vector3f worldScale = pTrans->GetLocalScale();
					Quaternionf worldRot = pTrans->GetLocalRotation();
					Matrix4x4f worldMat = Matrix4x4f::identity;
					makeSRTMatrix(worldMat, worldScale, worldRot, worldPos);

					worldMat = actorMat * worldMat;

					//实体的中点
					Vector3f actorCenter(50.0f, 0.0f, 50.0f);
					//实体的中点加上Entity设置的局部位置
					Vector3f offset = (actorCenter + pTrans->GetLocalPosition());
					SectionMesh* pMesh = pBlockMesh->getMesh();
					AABB localAABB = pMesh->GetLocalAABB();
					Vector3f localMinPos = localAABB.CalculateMin() - offset;
					Vector3f localMaxPos = localAABB.CalculateMax() - offset;
					localAABB.FromMinMax(localMinPos, localMaxPos);

					AABB resultAABB;
					TransformAABB(localAABB, worldMat, resultAABB);
					box = resultAABB;
				}				
			}
			else if (pImageMesh != NULL)
			{
				box = pImageMesh->GetWorldBounds();

				Transform* pTransform = GetTransform();
				if (pTransform)
				{
					Vector3f localMinPos(-600, -600, 0);
					Vector3f localMaxPos(600, 600, 100);
					Matrix4x4f worldMat = pTransform->GetLocalToWorldMatrix();
					AABB localAABB;
					localAABB.FromMinMax(localMinPos, localMaxPos);

					AABB resultAABB;
					TransformAABB(localAABB, worldMat, resultAABB);
					box = resultAABB;
				}
			}
			else
			{
				ModelItemMesh* pModelItemMesh = dynamic_cast<ModelItemMesh*>(bindobj);
				if (pModelItemMesh != NULL && pModelItemMesh->GetModel())
				{
					box = pModelItemMesh->GetModel()->GetWorldBounds();
				}
			}
		}
		else if (pBody == NULL && getEffectComponent())//只有含有特效组件的实体
		{
			EffectComponent* pEffectComp = getEffectComponent();
			box = pEffectComp->GetEffectParticleAABB();
		}

		return box;
	}
	 
	auto trans = GetTransform();
	if (trans) {
		return Rainbow::AABB(trans->GetWorldPosition(), Vector3f(BLOCK_FSIZE / 2.0f));
	}
	//返回一个默认值
	return Rainbow::AABB(Vector3f(0.0f), Vector3f(BLOCK_FSIZE / 2.0f));
}

void ClientActor::GetAABBInfo(Rainbow::Vector3f& size, Rainbow::Vector3f& anchor)
{
	Rainbow::Vector3f position = GetWorldPosition();
	Rainbow::AABB box = GetAABB();
	Rainbow::Vector3f minPos = box.CalculateMin();
	Rainbow::Vector3f maxPos = box.CalculateMax();
	
	// 这是组合，对每个子节点单独计算AABB
	std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>> children = GetAllChildren();
	if (children.size() > 0)
	{
		for (unsigned int i = 0; i < children.size(); i++)
		{
			ClientActor* emptyActor = children[i]->ToCast<ClientActor>();
			if (emptyActor)
			{
				Rainbow::AABB tmpBox = emptyActor->GetAABB();
				Rainbow::Vector3f tmpMinPos = tmpBox.CalculateMin();
				Rainbow::Vector3f tmpMaxPos = tmpBox.CalculateMax();
				if (i == 0)
				{
					minPos = tmpMinPos;
					maxPos = tmpMaxPos;
				}
				else {
					minPos.x = std::min(tmpMinPos.x, minPos.x);
					minPos.y = std::min(tmpMinPos.y, minPos.y);
					minPos.z = std::min(tmpMinPos.z, minPos.z);

					maxPos.x = std::max(tmpMaxPos.x, maxPos.x);
					maxPos.y = std::max(tmpMaxPos.y, maxPos.y);
					maxPos.z = std::max(tmpMaxPos.z, maxPos.z);
				}
			}
		}
	}

	size = Vector3f(maxPos.x - minPos.x, maxPos.y - minPos.y, maxPos.z - minPos.z);
	float anchorX = (minPos.x + maxPos.x) / 2 - position.x;
	float anchorY = (minPos.y + maxPos.y) / 2 - position.y;
	float anchorZ = (minPos.z + maxPos.z) / 2 - position.z;
	anchor = Vector3f(anchorX, anchorY, anchorZ);

	auto pBody = getBody();
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)	return;

	auto pEntity = getEntity();
	Rainbow::Model* pModel = nullptr;

	if (pEntity && pEntity->GetMainModel())
	{
		pModel = pEntity->GetMainModel();
		std::vector< SceneEditorVertex> vertexs;
		std::vector<UInt16> indices;
		Vector3f centerOffset(0.0f, 0.0f, 0.0f);
		Vector3f minPos(0.0f, 0.0f, 0.0f);
		Vector3f maxPos(0.0f, 0.0f, 0.0f);
		Vector3f scale = GetLocalScale();

		bool res = SceneEditorMeshGen::GetInstance().GetModelShaderData(pModel, vertexs, indices, centerOffset, minPos, maxPos);
		if (!res)	return;

		Rainbow::Matrix4x4f bodyWorldMatrix = pBody->getWorldMatrix();
		for (unsigned int i = 0; i < vertexs.size(); i++)
		{
			Vector4f v4 = Vector4f(vertexs[i].pos, 1.0);
			v4 = bodyWorldMatrix.MultiplyVector4(v4);

			Vector3f temp = Vector3f(v4.x, v4.y, v4.z);

			if (i == 0)
			{
				minPos = temp;
				maxPos = temp;
			}
			else {
				minPos.x = std::min(temp.x, minPos.x);
				minPos.y = std::min(temp.y, minPos.y);
				minPos.z = std::min(temp.z, minPos.z);

				maxPos.x = std::max(temp.x, maxPos.x);
				maxPos.y = std::max(temp.y, maxPos.y);
				maxPos.z = std::max(temp.z, maxPos.z);
			}
		}

		size = Vector3f(maxPos.x - minPos.x, maxPos.y - minPos.y, maxPos.z - minPos.z);

		float anchorX = (minPos.x + maxPos.x) / 2 - position.x;
		float anchorY = (minPos.y + maxPos.y) / 2 - position.y;
		float anchorZ = (minPos.z + maxPos.z) / 2 - position.z;
		anchor = Vector3f(anchorX, anchorY, anchorZ);
	}
	else if (pEntity && pEntity->GetBindObjectCount() > 0 && pBody && pBody->getModelType() == ModelType_Object && pBody->getExtraData() > 0)
	{
		int extraData = pBody->getExtraData();
		if (extraData <= 0) return;

		int itemId = ClientItem::getRealModelItemId(extraData);
		const ItemDef* pItemdef = GetDefManagerProxy()->getItemDef(itemId);
		//基本是一个
		BaseItemMesh* pMesh = dynamic_cast<BaseItemMesh*>(pEntity->GetBindObject(0));
		if (pMesh == nullptr || pItemdef == nullptr)	return;

		Rainbow::Matrix4x4f meshWorldMatrix = pMesh->GetWorldMatrix();
		Rainbow::Matrix4x4f bodyWorldMatrix = pBody->getWorldMatrix();
		Rainbow::Vector3f meshPostion = pMesh->GetWorldPosition();
		Rainbow::Vector3f actorPosition = GetWorldPosition();

		if (pItemdef->MeshType != BLOCK_GEN_MESH && pItemdef->MeshType != 3)	return;

		//用于BlockItem
		BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(pMesh);
		if (pBlockMesh == nullptr)	return;

		std::vector<Vector3f> vertexs;
		vertexs.reserve(1024);

		//SectionMesh* pMesh = pBlockMesh->getMesh();
		SectionMesh* pSectionMesh = pBlockMesh->getMesh();
		for (int meshIndex = 0; meshIndex < pSectionMesh->GetSubMeshCount(); meshIndex++)
		{
			SectionSubMesh* pSectionSubMesh = pSectionMesh->GetSubMeshAt(meshIndex);
			for (int meshInfoIndex = 0; meshInfoIndex < pSectionSubMesh->GetMeshInfoCount(); meshInfoIndex++)
			{
				SectionSubMesh::MeshInfo* pMeshInfo = pSectionSubMesh->GetMeshInfo(meshInfoIndex);
				const IndexFormat& indexFormat = pMeshInfo->m_Mesh->GetIndexFormat();
				UInt32 indexSize = GetMeshIndexSize(indexFormat);
				//如果顶点的类型是32位的暂不处理
				if (indexSize != 2)
					continue;
				size_t indexCount = pMeshInfo->m_Mesh->GetTotalIndexCount();
				UInt16* indexDataBuf = (UInt16*)pMeshInfo->m_Mesh->GetIndexBuffer().data();
				size_t vertexCount = pMeshInfo->m_Mesh->GetVertexCount();
				StrideIterator<Vector3f> vertexBegin = pMeshInfo->m_Mesh->GetVertexBegin();
				for (int indiceIndex = 0; indiceIndex < indexCount; indiceIndex += 3)
				{
					const Vector3f& vert1 = *(vertexBegin + indexDataBuf[indiceIndex + 0]) - Vector3f(50.0f, 0, 50.0f);
					const Vector3f& vert2 = *(vertexBegin + indexDataBuf[indiceIndex + 1]) - Vector3f(50.0f, 0, 50.0f);
					const Vector3f& vert3 = *(vertexBegin + indexDataBuf[indiceIndex + 2]) - Vector3f(50.0f, 0, 50.0f);

					Vector4f temp1(vert1, 1.0);
					Vector4f temp2(vert2, 1.0);
					Vector4f temp3(vert3, 1.0);

					temp1 = bodyWorldMatrix.MultiplyVector4(temp1);
					temp2 = bodyWorldMatrix.MultiplyVector4(temp2);
					temp3 = bodyWorldMatrix.MultiplyVector4(temp3);

					vertexs.push_back(Vector3f(temp1.x, temp1.y, temp1.z));
					vertexs.push_back(Vector3f(temp2.x, temp2.y, temp2.z));
					vertexs.push_back(Vector3f(temp3.x, temp3.y, temp3.z));
				}
			}
		}

		for (unsigned int i = 0; i < vertexs.size(); i++)
		{
			Vector3f temp = vertexs[i];

			if (i == 0)
			{
				minPos = temp;
				maxPos = temp;
			}
			else {
				minPos.x = std::min(temp.x, minPos.x);
				minPos.y = std::min(temp.y, minPos.y);
				minPos.z = std::min(temp.z, minPos.z);

				maxPos.x = std::max(temp.x, maxPos.x);
				maxPos.y = std::max(temp.y, maxPos.y);
				maxPos.z = std::max(temp.z, maxPos.z);
			}
		}

		size = Vector3f(maxPos.x - minPos.x, maxPos.y - minPos.y, maxPos.z - minPos.z);

		float anchorX = (minPos.x + maxPos.x) / 2 - position.x;
		float anchorY = (minPos.y + maxPos.y) / 2 - position.y;
		float anchorZ = (minPos.z + maxPos.z) / 2 - position.z;
		anchor = Vector3f(anchorX, anchorY, anchorZ);
		return ;
	}

	return ;
}

Rainbow::Vector3f ClientActor::GetAABBCenter()
{
	Rainbow::AABB box = GetAABB();
	return box.GetCenter();
}

ClientActor::ClientActor() : ClientActor(false)
{
	m_pGridContainer = nullptr;
	m_pEquipGridContainer = nullptr;
	m_pSkillComponent = nullptr;
}

ClientActor::ClientActor(bool empty) :  m_bEmpty(empty), m_LiveTicks(0),
										m_pWorld(NULL), m_CurMapID(NULL_MAPID), m_NeedClearTicks(-1),
										m_ObjId(0LL), m_Body(NULL),
										m_Mass(0), m_BehitDisable(false),
										m_llMasterObjId(0), m_NetMoveFlag(MF_FULL),
										m_ServerPosCmp(0, 0, 0),
										m_ServerYawCmp(0), m_ServerPitchCmp(0), 
										m_bSectionIsDisplay(false),
										m_monsterDef(nullptr), 
										m_BeHurtTarget(0), m_BeHurtTimer(0), m_DoJumping(-1),
										m_PathHide(0), m_Flags(0), m_FlagsEx(0), m_AnimFlags(0),
										m_MotionChange(false), m_isChangePos(false),
										m_Invulnerable(false), m_nAiInvulnerableProb(-1),
										m_bReverse(false), m_bPause(false),
										m_iAttChangeFlag(0), m_IsFallGround(false),
										m_bStatic(false), m_Scale(Vector3f(1.0f)),
										m_pThornBallComponent(nullptr), m_pPhysicsComponent(nullptr),
										m_pComponentLocoMotion(nullptr), m_pComponentVision(nullptr),
										m_pComponentNaviPath(nullptr), m_pComponentAttrib(nullptr),
										m_pComponentFunc(nullptr), m_pComponentRidden(nullptr),
										m_pComponentFireBurn(nullptr), m_pComponentCarry(nullptr), m_pAttackedComponent(nullptr),
										m_pSwarmComponent(nullptr), m_pModelComponent(nullptr), m_pActorInPortal(nullptr),
										m_pActorBindVehicle(nullptr), m_pBindActorComponent(nullptr), 
										m_pActorUpdateFrequency(nullptr), m_pSoundComponent(nullptr),
										m_pChunkPosComponent(nullptr), m_pEffectComponent(nullptr), m_pActionAttrStateComponent(nullptr),
										m_pGameObject(nullptr), m_bCanBeInteracted(false), m_pScriptComponent(nullptr),
										m_pPBActorPhysicsCom(nullptr), m_pPBActorEffectCom(nullptr), m_pPBActorSoundCom(nullptr),
										m_IsInPrefabScene(false), m_bControlByScript(true), m_IsLoadedModel(true), m_bMoveable(0), m_pFindComp(nullptr), 
										m_pModelLerpComponent(nullptr), m_bParent(false), m_bDelayInit(false), m_bEnableBatch(true),
										m_LoginVisible(true), m_fromPB(false), m_CanCollide(true), m_bNoDieComponent(false), m_triggerRightCount(0)
{
	this->IncrementRef(); // 计数为1

	m_LastActorTick = -1;
	m_LastActorUpdate = -1;
	m_LastActorSelect = -1;
	m_SpaceActorHandle = nullptr;
	m_AsActorViewer = false;
	m_AsViewerRadius = 0;
	m_TurningSpeed = 30.0f;
	m_bUgcSelect = false;
	m_CollideBlockPos.clear();
	Init(); // 初始化

	if (!m_bEmpty)
	{
		CreateComponent<ActorLocoMotion>("ActorLocoMotion");
		CreateComponent<ActorAttrib>("ActorAttrib");
		CreateComponent<ActorChunkPos>("ActorChunkPos");
		//CreateComponent<RiddenComponent>("RiddenComponent");
		CreateComponent<AttackedComponent>("AttackedComponent");
		//CreateComponent<DieComponent>("DieComponent");
		CreateComponent<EffectComponent>("EffectComponent");
		CreateComponent<ActionAttrStateComponent>("ActionAttrStateComponent");
		CreateComponent<DropItemComponent>("DropItemComponent");
		cacheTriggerComponent(CreateComponent<TriggerComponent>("TriggerComponent"));
		//CreateComponent<ParticlesComponent>("ParticlesComponent");
		//CreateComponent<FallComponent>("FallComponent");

		m_pComponentFunc = CreateComponent<ClientActorFuncWrapper>("ClientActorFuncWrapper");
		CreateComponent<SoundComponent>("SoundComponent");
		CreateComponent<ChargeJumpComponent>("ChargeJumpComponent");
	}
	else
	{
		m_pGameObject = Rainbow::GameObject::Create();

#if PHYSICS_SCALE_SWITCH
		Rainbow::Transform* pTransform = GetTransform();
		if (pTransform)
		{
			pTransform->SetUseNonUniformScaleType(true);
		}
#endif
	}

	m_AccumulateDtime = (rand() % 101) / 100.0f * GAME_TICK_TIME;
	m_bIgnoreUpdateFrequencyCtrl = false;
	m_bXrayEffectEnable = false;
	m_pGridContainer = nullptr;
	m_pEquipGridContainer = nullptr;
	m_pSkillComponent = nullptr;
	m_pAIUpdateFrequency = nullptr;
	CreateEvent2();
}

void ClientActor::CreateEvent2()
{
	//创建组件
	typedef ListenerFunctionRef<ComponentType> Listener1;
	Listener1* listener1 = SANDBOX_NEW(Listener1, [&](ComponentType type) -> void {
		switch (type)
		{
		case ComponentType::COMPONENT_DIE:
			if (nullptr == this->GetComponent<DieComponent>())
				this->CreateComponent<DieComponent>("DieComponent");
			break;
		default:
			break;
		}
		});
	Event2().Subscribe("Actor_CreateComponent", listener1);

	//删除组件
	typedef ListenerFunctionRef<ComponentType> Listener2;
	Listener2* listener2 = SANDBOX_NEW(Listener2, [&](ComponentType type) -> void {
		MNSandbox::Component* component = nullptr;
		switch (type)
		{
		case ComponentType::COMPONENT_DIE:
			component = this->GetComponent<DieComponent>();
			if (component)
				this->RemoveComponent(component);
			this->SetNoDieComponent(true);
			break;
		default:
			break;
		}
		});
	Event2().Subscribe("Actor_RemoveComponent", listener2);

	//受playercontrol攻击
	typedef ListenerFunctionRef<> Listener3;
	Listener3* listener3 = SANDBOX_NEW(Listener3, [&]() -> void {
		g_pPlayerCtrl->attackActor(this);
		});
	Event2().Subscribe("PlayerCtr_attackActor", listener3);


	typedef ListenerFunctionRef<IClientActor*&> Listener4;
	Listener4* listener4 = SANDBOX_NEW(Listener4, [this](IClientActor*& player) -> void {
		if (this->isPlayer()) player = this;
		else player = this->getShootingActor();
		});
	Event2().Subscribe("Actor_getshootingActor", listener4);
}

void ClientActor::SetEmpty(bool empty)
{
	if (m_bEmpty == empty)
	{
		return;
	}

	m_bEmpty = empty;

	if (!m_bEmpty) //空对象转成实体对象
	{
		if (!m_pComponentLocoMotion)
		{
			CreateComponent<ActorLocoMotion>("ActorLocoMotion");
		}
		if (!m_pChunkPosComponent)
		{
			CreateComponent<ActorChunkPos>("ActorChunkPos");
		}
		if (m_pWorld && m_pWorld->getActorMgr())
		{
			m_pWorld->getActorMgr()->emptyActorChange(getObjId(), false);
		}
	}
	else //实体对象转空对象
	{
		if (m_pWorld && m_pWorld->getActorMgr())
		{
			m_pWorld->getActorMgr()->emptyActorChange(getObjId(), true);
		}
	}
}

Rainbow::MeshRenderer* ClientActor::GetMeshRender()
{
	if (!m_pModelComponent)
	{
		return nullptr;
	}

	return m_pModelComponent->GetMeshRender();
}

bool ClientActor::IsMeshRenderEnabled()
{
	Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
	if (!pMeshRenderer)
		return false;

	return pMeshRenderer->IsEnable();
}

void ClientActor::EnableMeshRender(bool bEnable)
{
	Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
	if (!pMeshRenderer)
		return;

	pMeshRenderer->SetEnable(bEnable);
}

bool ClientActor::IsBaseModelType(int modeltype, int extraData)
{
	if(!UGCActorBatchManager::GetInstance().IsActorBatchEnable())
		return false;
	//在预制场景中创建的直接走老的actorbody，不然截图会不生效
	if (m_IsInPrefabScene)
		return false;

	if (modeltype == ModelType_Base)
		return true;
	else if ((ModelType_Object == modeltype && extraData <= 0) || ModelType_UGCObj == modeltype)
		return true;

	return false;
}

void ClientActor::EnableBatch(bool bEnable)
{
	m_bEnableBatch = bEnable;
}

void ClientActor::SetLogicVisible(bool bVisible)
{
	m_LoginVisible = bVisible;

	Rainbow::MeshRenderer* meshrender = GetMeshRender();
	if (meshrender)
	{
		meshrender->SetEnable(bVisible);
	}
}

Rainbow::GameObject* ClientActor::GetMeshRenderGO() 
{
	if (m_pModelComponent)
	{
		return m_pModelComponent->GetMeshRenderGO();
	}
	return nullptr;
}

bool ClientActor::CanBatch()
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
		return false;

	if (!m_bStatic)
		return false;

	//if (m_bControlByScript)
	if(m_bMoveable != 0)	//暂时用这个标记, 会级联
		return false;

	if (!m_bEnableBatch)
		return false;

	if (!m_LoginVisible)
		return false;

	Rainbow::GameObject* go = GetMeshRenderGO();
	if (!go)
		return false;

	Rainbow::GameScene* scene = go->GetScene();
	if (!scene)
		return false;

	Rainbow::MeshRenderer *pMeshRenderer = GetMeshRender();
	if (!pMeshRenderer)
		return false;

	if (m_pModelComponent && !m_pModelComponent->IsLoadedModelMeshRenderer())
		return false;

	if (m_pModelComponent && m_pModelComponent->IsWaitModelLoadFinished())
		return false;

	//透明的材质不合批
	if (m_pModelComponent && m_pModelComponent->HasTransparentMatItemInfo())
		return false;

	if (!pMeshRenderer->GetSharedMesh())
		return false;

	const dynamic_array<Rainbow::SubMesh>& meshes = pMeshRenderer->GetSharedMesh()->GetSubMeshes();
	size_t count = meshes.size();
	if (count <= 0) {
		return false;
	}
	/*else if (count == 1 && meshes[0].vertexCount > 10000) {
		return false;
	}*/
	else if (count >= 1) {
		int sum = 0;
		for (auto& it : meshes)
		{
			sum += it.vertexCount;
		}
		
		if (sum >= 2000)
			return false;
	}
	
	return true;
}

std::string ClientActor::GetModelPath() 
{
	if (m_pModelComponent)
	{
		return m_pModelComponent->GetModelPath();
	}
	return "";
}

int ClientActor::GetModelType() 
{ 
	if (m_pModelComponent)
	{
		return m_pModelComponent->GetModelType();
	}

	return 0; 
}
int ClientActor::GetModelExtraData() 
{ 
	if (m_pModelComponent)
	{
		return m_pModelComponent->GetModelExtraData();
	}

	return 0;
}
void  ClientActor::rotateBodyTo(const WCoord& target, bool sync)
{
	ActorBody* body =getBody();
	if (body != NULL)
	{
		body->rotateBodyTo(target, sync);
	}
}

ClientActor::~ClientActor()
{
	setMovingLightEnable(false);
	if (m_pWorld && m_pWorld->getActorMgr() && m_pWorld->getActorMgr()->GetActorMGT())
	{
		m_pWorld->getActorMgr()->GetActorMGT()->RemoveNode(this);
	}
	if (m_SpaceActorHandle) {
		m_SpaceActorHandle->SetIsVaild(false);
		m_SpaceActorHandle = nullptr;
	}
	//LOG_INFO("Actor Delete ID:%lld,  Pointer:%p", getObjId(), this);
	//auto targetComponent = getToAttackTargetComponent();
	//if (targetComponent)
	//{
	//	targetComponent->setTarget(nullptr);
	//}
	setBeHurtTarget(NULL);
	setBeAtk(NULL);

	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		RidComp->setRidingActor_Base(NULL);
		RidComp->clearRiddenActor();
	}

	if (GetActorBodySafeHandle()->IsVaild(m_Body))
	{
		m_Body->setOwnerActorNull();
		m_Body->clearAnimFrameEvent(getObjId());
		ENG_DELETE(m_Body);
	}

	m_Body = NULL;

#ifdef IWORLD_SERVER_BUILD
	if (m_pWorld)
	{
		auto soundPlayerComp = GetComponent<SoundComponent>();
		if (soundPlayerComp)
			soundPlayerComp->onRelease();
	}
	m_pWorld = NULL;
#else	
	m_pWorld = NULL;

	//auto soundPlayerComp = getSoundComponent();
	//if (soundPlayerComp) soundPlayerComp->onRelease();	
#endif
#ifdef IWORLD_ROBOT_TEST
	if (BehaviorManager::getSingletonPtr())
		BehaviorManager::getSingleton().UnBindBehavior(this);
#endif

	if (m_pGameObject)
		Rainbow::GameObject::Destroy(m_pGameObject);

	ms_ID2Ptr.erase(this);

	//修复lua调用野指针导致的崩溃 //2021-10-20 codeby: lizhibao   2021-12-06注释代码：codeby:crane
	//tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "ClientActor");

	ENG_DELETE(m_pPBActorPhysicsCom);
	ENG_DELETE(m_pPBActorEffectCom);
	ENG_DELETE(m_pPBActorSoundCom);

#if PLATFORM_WIN
    if (Rainbow::GetDebugMgr().GetCheckActorRelease()) {
        InitStackTrace();
        void* stack[20];
        stack[0] = 0;
        GetStacktrace(stack, 20, 3);
        const int kBufferSize = 20 * 1024;	// kBufferSize = kFormatStringMaxLength
        char buffer[kBufferSize];
        GetReadableStackTrace(buffer, kBufferSize, stack, 20);
        WarningStringMsg("Actor abnormal release: %s", buffer);
        UninitStackTrace();
    }
#endif
	if (g_pPlayerCtrl)
	{
		if (g_pPlayerCtrl->m_PickResult.actor == this)
		{
			g_pPlayerCtrl->m_PickResult.actor = nullptr;
		}
	}

	if (m_childBin)
		free(m_childBin);
	if (m_scriptcomBin)
		free(m_scriptcomBin);
	if (m_ugcComponentBin)
		free(m_ugcComponentBin);
	if (m_modelcomBin)
		free(m_modelcomBin);
}

void ClientActor::Destroy()
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		Super::Destroy();
		return;
	}

	addRef();

	ActorManager *pActorMgr = nullptr;
	if (m_pWorld && m_pWorld->getActorMgr())
	{
		pActorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
	}

	ClientActor* pAncestor = GetAncestor();
	if (pAncestor && pActorMgr)
	{
		if (pAncestor == this)
		{
			pActorMgr->DeleteLargeGameObject(this);
		}
	}

	Super::Destroy();

	if (pActorMgr && pAncestor != this)
	{
		pActorMgr->CheckLargeGameObject(pAncestor);
	}

	release();

	return;
}

void ClientActor::OnDirty()
{
	Release(); // 释放

	Super::OnDirty();
}

void ClientActor::OnClearNotify()
{
	Super::OnClearNotify();
}
void ClientActor::SetObjId(long long objid)
{
	m_ObjId = objid; 
}

long long ClientActor::GenNextObjId()
{
	return mg_cur_id++;
}

void ClientActor::ResetObjId(long long id/*=0x100000000LL*/)
{
	mg_cur_id = id;
}

long long ClientActor::GetCurObjId()
{
	return mg_cur_id;
}
long long ClientActor::PackObjId(long long id)
{
	if (id >= 0x100000000LL)
		return (id - 0x100000000LL) * 2;
	return (id * 2) + 1;
}
long long ClientActor::UnpackObjId(long long id)
{
	if (id & 1)
	{
		return (id - 1) / 2;
	}
	return id / 2 + 0x100000000LL;
}

void ClientActor::enterWorld(World* pworld)
{
	assert(pworld != NULL);
	if (pworld == NULL)
	{
		return;
	}

	m_LiveTicks = 0;
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(0);
		{
			LoadActorEventListen();
		}
	}

	m_MotionChange = false;
	m_isChangePos = false;
	m_pWorld = pworld;
	m_CurMapID = pworld->getCurMapID();

	if (isPlayer() && g_WorldMgr)
		g_WorldMgr->signChangedToSync(getObjId(), BIS_MAP);
	auto updateFrequencyComp = getUpdateFrequencyCom();//by__Logo
	if (updateFrequencyComp) updateFrequencyComp->onEnterWorld(pworld);

	if (m_pGameObject && !m_pGameObject->IsInScene())
		m_pWorld->getScene()->AddGameObject(m_pGameObject);

	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->AddMeshRender();
	}
	
	if (getLocoMotion()) getLocoMotion()->onEnterWorld(pworld);
	if (getBody())
	{
		setBodyCull();
		getBody()->onEnterWorld(pworld);
	}

	if (getChunkPosComponent())//by__Logo
		getChunkPosComponent()->onEnterWorld();
	initBornBuff();
	long long objId = pworld->trackActor(this);

	m_pWorld->Event().Emit("OnActorEnter", SandboxContext(m_pWorld).SetData_RefEx(this)); // 替换成场景管理，节点维系actor的生命周期
	SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("OnActorEnter", SandboxContext(m_pWorld).SetData_RefEx(this));

	if (!m_SpaceActorHandle && NeedTickForever()) {
		m_SpaceActorHandle = getActorMgr()->GetSpaceManager()->SetForeverTick(this);
	}

	if (m_bEmpty)
	{
		OnEnterChildCull();
	}

	if (m_AsActorViewer) {
		auto compView = GetComponent<ViewerComponentActor>();
		if (!m_pViewerComponentActor) {
			m_pViewerComponentActor = CreateComponent<ViewerComponentActor>("ViewerComponentActor");
			m_pViewerComponentActor->SetRadius(m_AsViewerRadius);
		}
		m_pViewerComponentActor->EnterWorld();
	}

	if (m_ParentWID == -1) //prefab scene特殊处理
		m_ParentWID = 0;

	if (m_IsInPrefabScene && GetWorldManagerPtr() && GetWorldManagerPtr()->getPrefabSceneVisible() && g_pPlayerCtrl && getBody() && getBody()->getEntity())
	{
		Entity* pEntity = getBody()->getEntity();
		if (pEntity && g_pPlayerCtrl->getScene())
		{
			pEntity->DetachFromScene();
			pEntity->AttachToScene(g_pPlayerCtrl->getScene());
		}
	}

	Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
	if (pMeshRenderer)
	{
		bool isugcMode = m_pWorld && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->isUGCEditMode();
		if (isugcMode && !m_bUgcSelect && m_pWorld && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->GetHideAllChunks())
		{
			pMeshRenderer->SetEnable(false);
		}
	}
}

void ClientActor::leaveWorld(bool keep_inchunk)
{
	if (m_pWorld == NULL) return;

	//getBindActorCom()->leaveWorld(keep_inchunk);
	auto bindActorComp = m_pBindActorComponent;
	if (bindActorComp) bindActorComp->leaveWorld(keep_inchunk);

	if(m_pWorld == NULL) return;

	auto pScriptComponent = getScriptComponent();
	if (pScriptComponent)
	{
		pScriptComponent->OnLeaveWorld();
	}

	auto viewerCom = m_pViewerComponentActor;
	if (viewerCom)
		viewerCom->LeaveWorld(keep_inchunk);

	if (m_SpaceActorHandle && NeedTickForever()) {
		m_SpaceActorHandle->SetIsVaild(false);
		m_SpaceActorHandle = nullptr;
	}

	m_pWorld->Event().Emit("OnActorLeave", SandboxContext(m_pWorld).SetData_RefEx(this)); // 替换成场景管理，节点维系actor的生命周期
	//退出场景管理  David 2023/8/8

	m_pWorld->untrackActor(this);

	setBeHurtTarget(NULL);
	setBeAtk(nullptr);
	//if(m_RidingActor != 0) mountActor(NULL);

	auto RidComp = getRiddenComponent();
	if (RidComp)
	{
		RidComp->setRidingActor(NULL);
		RidComp->clearRiddenActor();
	}

	if(getBody()) getBody()->onLeaveWorld();
	if (getLocoMotion()) getLocoMotion()->leaveWorld();
	//m_pActorChunkPos->onLeaveWorld(keep_inchunk);
	//auto comp = GetComponent<ActorChunkPos>();//by__Logo
	if (m_pChunkPosComponent)
		m_pChunkPosComponent->onLeaveWorld(keep_inchunk);

	setMovingLightEnable(false);
	if(m_pGameObject && m_pGameObject->IsInScene())
		m_pGameObject->RemoveFromScene();

	if (m_pModelComponent)
	{
		m_pModelComponent->OnActorLeaveWorld();
	}
	
	if (getChunkPosComponent())//by__Logo
		getChunkPosComponent()->onLeaveWorld(keep_inchunk);
	
	if (IsObject() && GetWorldManagerPtr() && GetWorldManagerPtr()->m_removeGraphics)
	{
		GetWorldManagerPtr()->m_removeGraphics(getObjId());
	}

	m_pWorld = NULL;
}

int ClientActor::getNetSyncPeriod()
{
	return 1;
}

bool ClientActor::checkNeedClear()
{
	if (m_NeedClearTicks > 0) 
		m_NeedClearTicks--;

	if (m_NeedClearTicks == 0)
	{
		onClear();
		m_NeedClearTicks = -1;

		addRef();
		ActorManager* pActorMgr = nullptr;
		if (m_pWorld && m_pWorld->getActorMgr())
		{
			pActorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		}

		ClientActor* pAncestor = GetAncestor();
		if (pAncestor && pActorMgr)
		{
			if (pAncestor == this)
			{
				pActorMgr->DeleteLargeGameObject(this);
			}
		}

		m_pWorld->GetWorldScene()->UnbindLegacyActor(this);

		if (pActorMgr && pAncestor != this)
		{
			pActorMgr->CheckLargeGameObject(pAncestor);
		}

		release();

		//leaveWorld(false);	
		return true;
	}

	return false;
}
void ClientActor::onAfterTick()
{
	if (getChunkPosComponent())
	{
		getChunkPosComponent()->onAfterTick();
	}
}

void ClientActor::AddUgcComponent(void* fbs, bool& needLuaComponent)
{
	FBSave::UgcComponent* ugccomponent = static_cast<FBSave::UgcComponent*>(fbs);
	std::string name = ugccomponent->name()->str();
	ugccomponent->detail();

	jsonxx::Object obj;
	if (obj.parseBinary((unsigned char*)ugccomponent->detail()->data(), ugccomponent->detail()->size()))
	{
		ActorComponentBase* pComponent = (ActorComponentBase*)CreateComponentByFactory(name, name);
		if (pComponent)
		{
			pComponent->LoadComponentData(obj, true);
			needLuaComponent = pComponent->NeedLuaComponent();
		}
	}
}

//部分耗时操作可以放到匀帧tick中处理，可获得匀帧的效果
void ClientActor::DelayInitData(bool force)
{
	if (m_bDelayInit) return;

	if (!force && m_pWorld && m_pWorld->getActorMgr())
	{
		if (m_ParentWID == 0 && m_pWorld->getActorMgr()->GetGameObjDelay() > 6)
		{
			return;
		}

		m_pWorld->getActorMgr()->IncGameObjDelay();
	}

	OPTICK_EVENT();

	m_bDelayInit = true;

	// 先创建脚本组件，如果有需要
	string scriptComStr = "";
	if (m_scriptcomBin && m_scriptcomLen > 0)
	{
		scriptComStr.assign(m_scriptcomBin, m_scriptcomLen);
	}
	if (!m_pScriptComponent)
	{
		if (m_scriptcomBin)
		{
			if (!ScriptComponent::IsNoLuaComponent(scriptComStr.c_str()))
			{
				m_pScriptComponent = CreateComponent<ScriptComponent>("ScriptComponent");
			}
		}
		/*else if (m_pWorld && m_pWorld->GetWorldMgr() &&
			(m_pWorld->GetWorldMgr()->isUGCEditMode() || m_pWorld->GetWorldMgr()->isUGCEditBuildMode()))
		{
			m_pScriptComponent = CreateComponent<ScriptComponent>("ScriptComponent");
		}*/
	}

	if (m_modelcomBin)
	{
		auto modelcom = SureModelComponent();
		modelcom->UnSerializeAndLoadModelKV(m_modelcomBin, m_modelcomLen);
		modelcom->m_bModelDirty = false;

		free(m_modelcomBin);
		m_modelcomBin = nullptr;
	}

	if (m_ugcComponentBin)
	{
		flatbuffers::Verifier verifier((const uint8_t*)m_ugcComponentBin, m_ugcComponentLen);
		if (FBSave::VerifyUgcComponentArrayBuffer(verifier))
		{
			const FBSave::UgcComponentArray* ugcComponentArray = FBSave::GetUgcComponentArray(m_ugcComponentBin);
			if (ugcComponentArray)
			{
				bool needScriptCom = false;
				auto ugccomponents = ugcComponentArray->ugccomponents();
				for (unsigned int i = 0; i < ugccomponents->size(); i++)
				{
					auto ugccomponent = ugccomponents->Get(i);
					bool needLuaComponent = false;
					AddUgcComponent((void *)ugccomponent, needLuaComponent);
					needScriptCom |= needLuaComponent;
				}
				if (needScriptCom && !m_pScriptComponent)
				{
					m_pScriptComponent = CreateComponent<ScriptComponent>("ScriptComponent");
				}
			}
		}

		free(m_ugcComponentBin);
		m_ugcComponentBin = nullptr;
	}

	if (m_scriptcomBin)
	{
		if (m_pScriptComponent)
		{
			m_pScriptComponent->OnLoad(scriptComStr);
		}
		free(m_scriptcomBin);
		m_scriptcomBin = nullptr;
	}
	/*else if (m_scriptcomLen >= 0)
	{
		if (m_pScriptComponent)
		{
			string scriptComStr = "";
			m_pScriptComponent->OnLoad(scriptComStr);
		}
		m_scriptcomLen = -1;
	}*/

	//处理child actor
	if (m_childBin)
	{
		std::vector<ClientActor*> children;
		std::vector<ClientActor*> childrenFail;

		if (m_fromPB)
		{
			LoadFromPBChildren(m_childBin, m_childLen, children, childrenFail);
		}
		else
		{
			LoadChildren(m_childBin, m_childLen, children, childrenFail);
		}

		auto iter = childrenFail.begin();
		while (iter != childrenFail.end())
		{
			ClientActor* actor = *iter;
			actor->release();
			iter++;
		}

		iter = children.begin();
		while (iter != children.end())
		{
			ClientActor* actor = *iter;
			if (actor->getChunkPosComponent())
			{
				actor->RemoveComponent(actor->getChunkPosComponent());
			}
			actor->SetNodeParent(this, true);

			iter++;
		}

		free(m_childBin);
		m_childBin = nullptr;
	}

	if ((m_pPBActorPhysicsCom || m_pPBActorEffectCom || m_pPBActorSoundCom) && m_pWorld && m_pWorld->isRemoteMode())
	{
		if (m_pPBActorPhysicsCom)
		{
			if (!m_pPhysicsComponent)
			{
				m_pPhysicsComponent = CreateComponent<PhysicsComponent>("PhysicsComponent");
			}
			m_pPhysicsComponent->OnLoadFromPB(m_pPBActorPhysicsCom);
			ENG_DELETE(m_pPBActorPhysicsCom);
		}

		if (m_pPBActorEffectCom)
		{
			if (!m_pEffectComponent)
			{
				m_pEffectComponent = CreateComponent<EffectComponent>("EffectComponent");
			}
			m_pEffectComponent->OnLoadFromPB(m_pPBActorEffectCom);
			ENG_DELETE(m_pPBActorEffectCom);
		}

		if (m_pPBActorSoundCom)
		{
			if (!m_pSoundComponent)
			{
				m_pSoundComponent = CreateComponent<SoundComponent>("SoundComponent");
			}
			m_pSoundComponent->OnLoadFromPB(m_pPBActorSoundCom);
			ENG_DELETE(m_pPBActorSoundCom);
		}
	}

	if (m_pScriptComponent)
	{
		bool isugcMode = m_pWorld && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->isUGCEditMode();
		if (isugcMode && !m_bUgcSelect && m_pWorld && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->GetHideAllChunks())
		{
			m_pScriptComponent->SetIsEditHide(true);
		}
	}
}

void ClientActor::DelayInitDataRecursively()
{
	DelayInitData(true);
	//tick();
	if (GetChildrenCount() > 0 && m_pWorld && m_pWorld->getActorMgr())
	{
		GetChildrenList().for_each([&](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				child->DelayInitDataRecursively();
			}
		});
	}
}

void ClientActor::prepareTick()
{
	if (getLocoMotion())
		getLocoMotion()->prepareTick();

	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				ActorLocoMotion* locmove = child->getLocoMotion();
				if (locmove) locmove->prepareTick();

				child->prepareTick();
			}
			});
	}

	if (useNewLerpModel() && m_pModelLerpComponent)
		m_pModelLerpComponent->PreTick();
}

void ClientActor::tick()
{
	OPTICK_EVENT();
	Super::Tick(); // 临时方案，后期场景树完成后，会转接到场景树统一tick注册
	if(m_pWorld == NULL) return;

	int objType = getObjType();
	if (OBJ_TYPE_GAMEOBJECT == objType || OBJ_TYPE_MONSTER == objType || OBJ_TYPE_ROLE == objType)
	{
		if (!m_bDelayInit)
		{
			DelayInitData(OBJ_TYPE_ROLE == objType);
		}
	}

	UpdateOctree();

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
	{
		// 云服主机不调update, 所以技能updat放这里调用一下
		//技能update
		if (getSkillComponent())
		{
			getSkillComponent()->OnUpdate(GetWorldManagerPtr()->getTickDelta());
		}

		// 云服主机不调update, 所以OnTransformUpdate放这里调用一下
		OnTransformUpdate();
	}

	if (m_bStatic)
	{
		//实体Body tick 没有有意义的逻辑，先不调用
		if (!IsObject() && getBody()) getBody()->tick();

		if (m_pWorld->isRemoteMode() && IsObject())
		{
			if (m_pComponentLocoMotion)
			{
				m_pComponentLocoMotion->tick();
			}
		}

		if (GetChildrenCount() > 0)
		{
			GetChildrenList().for_each([](SandboxNode* t) -> void {
				ClientActor* child = dynamic_cast<ClientActor*>(t);
				if (child)
				{
					child->tick();
				}
			});
		}

		return;
	}

	if(m_pWorld->onServer())
	{
		UpdateFireBurning();
	}
	auto navigator = getNavigator();
	auto functionWrapper = getFuncWrapper();
	
	if (navigator && functionWrapper && !functionWrapper->getSimulateFly())
	{
		//解除一下反向依赖
		bool player = false;
		if (objType == OBJ_TYPE_ROLE)
		{
			player = true;
		}
		if ((!player || !navigator->noPath()) && (objType != OBJ_TYPE_PIRATE_SHIP))
		{
			navigator->clearMoveForward();
		}
	}
	ActorLocoMotion *locoMotion = getLocoMotion();
	if(locoMotion)
	{
		locoMotion->preMove();
		preMoveTick();
		locoMotion->tick();
		afterMoveTick();
		locoMotion->doBlockCollision();
		locoMotion->doPickThrough();
	}

	if(getAttrib()) getAttrib()->tick();
	if (getBody()) getBody()->tick();
	if(getVision()) getVision()->clearAICanSeeCache();
	
	if(m_BeHurtTarget != 0)
	{
		ClientActor *target = getBeHurtTarget();
		if(target==NULL || target->isDead() || target->needClear()) m_BeHurtTarget = 0;
	}
	if(navigator && functionWrapper && !functionWrapper->getSimulateFly())
	{
		if (navigator->noPath())
		{
			navigator->clearPathEntity();
			setCanControl(true);
		}	
		//PROFINY_NAMED_SCOPE("update navigation")	
		navigator->tick();
	}
	auto pNavigationPathComponent = this->GetComponent<ClientActorNavigationPathComponent>();
	if (pNavigationPathComponent)
	{
		if(!pNavigationPathComponent->noPath())
			pNavigationPathComponent->Tick();
	}
	if (locoMotion)  //navigator->tick()里会清除m_isJumping状态，所以这段逻辑放在其后面执行
	{
		if (functionWrapper && !functionWrapper->getSimulateFly())
		{
			if (m_DoJumping > 0)
			{
				m_DoJumping = 0;

				if (objType != OBJ_TYPE_DROPITEM && objType != OBJ_TYPE_EXPORB && objType != OBJ_TYPE_GAMEOBJECT)
				{
					locoMotion->setJumping(true);
				}
			}
			else if (m_DoJumping == 0)
			{
				m_DoJumping = -1;

				if (objType != OBJ_TYPE_DROPITEM && objType != OBJ_TYPE_EXPORB && objType != OBJ_TYPE_GAMEOBJECT)
				{
					locoMotion->setJumping(false);
				}
			}
		}
	}

	//if (objType != OBJ_TYPE_DROPITEM && objType != OBJ_TYPE_EXPORB && objType != OBJ_TYPE_GAMEOBJECT)
	//{
	//	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
	//	if (pLivingAttrib && pLivingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE))
	//	{
	//		if (getFlying())
	//		{
	//			ClientPlayer *player = dynamic_cast<ClientPlayer *>(this);
	//			if (player)
	//				player->setFlyingAndSync(false);
	//			else
	//				setFlying(false);
	//		}
	//	}
	//}

	auto thornComponent = getThornBallComponent();
	if (thornComponent)
	{
		thornComponent->onTick();
	}

	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				child->tick();
			}
		});
	}
}

void ClientActor::tickForFlyObject()
{
	ActorBase::OnTick();

	if (m_pWorld == NULL) return;

	int objType = getObjType();
	if (OBJ_TYPE_GAMEOBJECT == objType || OBJ_TYPE_MONSTER == objType || OBJ_TYPE_FLYMONSTER == objType)
	{
		if (!m_bDelayInit)
		{
			DelayInitData(false);
		}
	}
}

void ClientActor::tickForAquaticObject()
{
	ActorBase::OnTick();

	if (m_pWorld == NULL) return;

	int objType = getObjType();
	if (OBJ_TYPE_AQUATICMONSTER == objType)
	{
		if (!m_bDelayInit)
		{
			DelayInitData(false);
		}
	}
}

void ClientActor::CheckChildCull(bool bCull)
{
	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([&](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				if (child->getBody())
				{
					child->getBody()->SetForceCull(bCull);
				}
			}
		});
	}
}

void ClientActor::OnEnterChildCull()
{
	if (getBody()) //子节点进入地图时强制渲染，且跟随父节点cull
	{
		getBody()->SetForceCull(false);
	}
	return;
}

void ClientActor::SetOutline(UInt32 value)
{
	if (IsMeshRenderEnabled())
	{
		Rainbow::MeshRenderer* meshrender = GetMeshRender();
		if (meshrender)
		{
			meshrender->SetOutline(value);
		}
		return;
	}

	ActorBody* body = getBody();
	if (!body)
		return;

	Rainbow::Model* model = body->getModel();
	if (model)
	{
		model->SetOutline(value);
	}
	else
	{
		Rainbow::Entity* entity = getEntity();
		if (!entity)
			return;

		int count = entity->GetBindObjectCount();
		if (count <= 0)
			return;

		for (int i = 0; i < count; i++)
		{
			MovableObject* obj = entity->GetBindObject(i);
			if (obj) {
				BaseItemMesh* itemmesh = dynamic_cast<BaseItemMesh*>(obj);
				if (itemmesh) {
					ModelItemMesh* pItemMesh = dynamic_cast<ModelItemMesh*>(itemmesh);
					if (pItemMesh)
					{
						Model* pModel = pItemMesh->GetModel();
						if (pModel)
							pModel->SetOutline(value);
					}
					else
					{
						itemmesh->SetOutline(value);
					}
				}
			}
		}
	}
}

void ClientActor::onEnterSection(Section* section)
{
	updateSectionIsDisplay(section);
}

void ClientActor::onLeaveSection(Section* section)
{
	updateSectionIsDisplay(section);
}

void ClientActor::updateSectionIsDisplay(Section* section)
{
	//Assert(section != nullptr);
	//check this section is on camera frustum
	RenderSection* renderSection = nullptr;
	if (section)
	{
		renderSection = section->GetRenderSection();
	}
	
	if (needCheckVisible()) 
	{
		//AABB actorBounds = GetAABB();
		m_bSectionIsDisplay = (renderSection != nullptr && (renderSection->IsVisiable() || section->isEmpty())) || (renderSection == nullptr);
		if (getObjType() == OBJ_TYPE_GAMEOBJECT && !(m_mergeAABB == Rainbow::AABB::zero))
		{
			m_bSectionIsDisplay = GetMiniCraftRenderer().IntersectActorAABBCameraFrustum(renderSection, m_mergeAABB);
		}
		
		//if (m_bSectionIsDisplay)
		//{
			/*
			* 这段逻辑放置到GetAABB里面去了
			auto pEntity = getEntity();
			auto pBody = getBody();
			if (pEntity && pEntity->GetMainModel() )
			{
				actorBounds = pEntity->GetMainModel()->GetWorldBounds();
			}
			*/
			//bool sectionIntersection = GetMiniCraftRenderer().IntersectActorAABBCameraFrustum(renderSection, actorBounds);
			//m_bSectionIsDisplay &= sectionIntersection;
		//}
	}
	else 
	{
		m_bSectionIsDisplay = true;
	}
	setBodyCull();
}

void ClientActor::setBodyCull()
{
	bool isugcMode = m_pWorld && m_pWorld->GetWorldMgr() && m_pWorld->GetWorldMgr()->isUGCEditMode();
	bool isCull = !m_bSectionIsDisplay;
	if (isugcMode && m_bUgcSelect)
	{
		isCull = false;
	}

	if (m_Body != nullptr) 
	{
		m_Body->SetForceCull(isCull);
	}

	Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
	if (isCull && pMeshRenderer)
	{
		pMeshRenderer->SetEnable(!isCull);
	}
}
bool ClientActor::skillPlayAnim(int tpsanimid, int fpsanimid, int isLoop, int playLayer)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		if (!actorbody->hasSeqPlaying(tpsanimid))
		{

		/*actorbody->setActTriggerID(tpsanimid);*/
		actorbody->skillplayAnimBySeqId(tpsanimid, isLoop, playLayer);
		actorbody->setAblePlayOtherAnim(true);
		}
		//同步到客机
		//notifyPlayAnim2Tracking(seq, include_me, seq1, isLoop);
		if (m_pWorld && !m_pWorld->isRemoteMode())
		{

			//ANIM_MODE_LOOP = 0,//循环播放
			//	ANIM_MODE_ONCE, //播放一次
			//	ANIM_MODE_ONCE_STOP, //播放一次完毕，停在末尾

			jsonxx::Object context;
			char objid_str[128];
			sprintf(objid_str, "%lld", getObjId());
			context << "objid" << objid_str;
			context << "tpsanimid" << tpsanimid;
			context << "fpsanimid" << fpsanimid;
			context << "loop" << isLoop;
			context << "playLayer" << playLayer;
			SandBoxManager::getSingleton().sendBroadCast("PB_SKILLPLAYANIM_HC", context.bin(), context.binLen());
		}
		return true;
	}
	return false;
}
bool ClientActor::skillStopAnim(int tpsanimid, int fpsanimid, bool isReset)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{

		actorbody->skillStopAnimBySeqId(tpsanimid, isReset);
		actorbody->setAblePlayOtherAnim(true);
		if (m_pWorld && !m_pWorld->isRemoteMode())
		{

			//ANIM_MODE_LOOP = 0,//循环播放
			//	ANIM_MODE_ONCE, //播放一次
			//	ANIM_MODE_ONCE_STOP, //播放一次完毕，停在末尾

			jsonxx::Object context;
			char objid_str[128];
			sprintf(objid_str, "%lld", getObjId());
			context << "objid" << objid_str;
			context << "tpsanimid" << tpsanimid;
			context << "fpsanimid" << fpsanimid;
			context << "isReset" << isReset;
			SandBoxManager::getSingleton().sendBroadCast("PB_SKILLSTOPANIM_HC", context.bin(), context.binLen());
		}
		return true;
	}
	return false;
}
void ClientActor::SetFirstSearch(bool firstSearch)
{
	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
	if (pLivingAttrib) {
		pLivingAttrib->setFirstSearch(firstSearch);
	}
}

bool ClientActor::GetFirstSearch()
{
	LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
	if (pLivingAttrib) {
		return	pLivingAttrib->getFirstSearch();
	}
	return false;
}

bool ClientActor::GetGunInfo(int& gunQuality, int& gunLevel, int& featureCount)
{
	GunGridDataComponent* comp = NULL;
	if (getObjType() == OBJ_TYPE_ROLE)
	{
		ClientPlayer* player = static_cast<ClientPlayer*>(this);
		int shortcutIndex = player->getCurShortcut() + player->getShortcutStartIndex();
		BackPack* pack = player->getBackPack();
		if (!pack)
		{
			return false;
		}
		BackPackGrid* grid = pack->index2Grid(shortcutIndex);
		if (!grid)
		{
			return false;
		}

		comp = dynamic_cast<GunGridDataComponent*>(grid->getGunDataComponent());
	}
	else
	{
		BackPackGrid* pweapon = getEquipGrid(EQUIP_WEAPON);
		if (pweapon)
		{
			comp = dynamic_cast<GunGridDataComponent*>(pweapon->getGunDataComponent());
		}
	}

	if (comp)
	{
		featureCount = comp->GetGunFeatureQualityCount();
		gunLevel = comp->GetCurLevel();
		ItemDef* itemDef = GetDefManagerProxy()->getItemDef(comp->getGunId());
		if (itemDef)
		{
			gunQuality = itemDef->QualityLevel;
		}
		else
		{
			gunQuality = 0;
		}

		return true;
	}

	return false;
}

bool ClientActor::HasWeaponPlaying(int animid)
{
	ActorBody* actorbody = getBody();
	return (actorbody && actorbody->hasWeaponPlaying(animid)) ? true : false;
}

void ClientActor::PlayWeaponAnim(int animid, int loop, float speed)
{
	if (!getBody() || !m_pWorld)
		return;

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", getObjId());
	context << "objid" << objid_str;
	context << "animid" << animid;
	context << "loop" << loop;
	context << "speed" << speed;

	if (m_pWorld->isRemoteMode())
	{
		SandBoxManager::getSingleton().sendBroadCast("PB_PLAYWEAPONANIM_CH", context.bin(), context.binLen());
	}
	else
	{
		getBody()->playWeaponAnim(animid, loop, speed);
		SandBoxManager::getSingleton().sendBroadCast("PB_PLAYWEAPONANIM_HC", context.bin(), context.binLen());
	}
}

void ClientActor::StopWeaponAnim(int animid)
{
	if (!getBody() || !m_pWorld)
		return;

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", getObjId());
	context << "objid" << objid_str;
	context << "animid" << animid;

	if (m_pWorld->isRemoteMode())
	{
		SandBoxManager::getSingleton().sendBroadCast("PB_STOPWEAPONANIM_CH", context.bin(), context.binLen());
	}
	else
	{
		getBody()->stopWeaponAnim(animid);
		SandBoxManager::getSingleton().sendBroadCast("PB_STOPWEAPONANIM_HC", context.bin(), context.binLen());
	}
}

void ClientActor::PlayWeaponMotion(const char* name, bool reset, int mclass, float scale)
{
	if (!getBody() || !m_pWorld)
		return;

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", getObjId());
	context << "objid" << objid_str;
	context << "name" << name;
	context << "reset" << reset;
	context << "mclass" << mclass;
	context << "scale" << scale;

	if (m_pWorld->isRemoteMode())
	{
		SandBoxManager::getSingleton().sendBroadCast("PB_PLAYWEAPONMOTION_CH", context.bin(), context.binLen());
	}
	else
	{
		getBody()->playWeaponMotion(name, reset, mclass, scale);
		SandBoxManager::getSingleton().sendBroadCast("PB_PLAYWEAPONMOTION_HC", context.bin(), context.binLen());
	}
}

void ClientActor::StopWeaponMotion(int mclass)
{
	if (!getBody() || !m_pWorld)
		return;

	jsonxx::Object context;
	char objid_str[128];
	sprintf(objid_str, "%lld", getObjId());
	context << "objid" << objid_str;
	context << "mclass" << mclass;

	if (m_pWorld->isRemoteMode())
	{
		SandBoxManager::getSingleton().sendBroadCast("PB_STOPWEAPONMOTION_CH", context.bin(), context.binLen());
	}
	else
	{
		getBody()->stopWeaponMotion(mclass);
		SandBoxManager::getSingleton().sendBroadCast("PB_STOPWEAPONMOTION_HC", context.bin(), context.binLen());
	}
}

bool ClientActor::skillPlayToolAnim(int animid)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "animid" << animid;
		SandBoxManager::getSingleton().sendBroadCast("PB_SKILLPLAYTOOLANIM_HC", context.bin(), context.binLen());
	}
	return true;
}

bool  ClientActor::skillStopToolAnim(int animid)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		jsonxx::Object context;
		char objid_str[128];
		sprintf(objid_str, "%lld", getObjId());
		context << "objid" << objid_str;
		context << "animid" << animid;
		SandBoxManager::getSingleton().sendBroadCast("PB_SKILLSTOPTOOLANIM_HC", context.bin(), context.binLen());
	}
	return true;
}

bool  ClientActor::SetLocoMotionType(int locotype, bool isNo)
{
	return true;
}

bool ClientActor::PlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->playMotion(path, loopPlayTime, OffsetPosition, rote, scale, isLoop, motion_class);
		return true;
	}
	return false;
}
bool ClientActor::HasPlayingEffect(const char* path)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		return actorbody->hasPlayingMotion(path);
	}
	return false;
}


bool ClientActor::skillPlayBodyEffect(const char* path, float loopPlayTime, const Rainbow::Vector3f& OffsetPosition, const Rainbow::Vector3f& rote, const Rainbow::Vector3f& scale, bool isLoop, int motion_class)
{
	if (m_pEffectComponent)
	{
		return m_pEffectComponent->skillPlayBodyEffect(path, loopPlayTime, OffsetPosition, rote, scale, isLoop, motion_class);
	}
	
	return false;
}
bool ClientActor::skillStopBodyEffect(const char* path)
{
		auto effectComponent = getEffectComponent();
		if (effectComponent)
		{
			effectComponent->stopBodyEffect(path);
			return true;
		}
	return false;
}

void ClientActor::SetIsEdit(bool isEdit)
{
	m_bUgcSelect = isEdit;
}

bool ClientActor::HasMeshRender()
{
	if (m_Body || (m_pModelComponent && m_pModelComponent->GetMeshRender()))
		return true;

	return false;
}

void ClientActor::update(float dtime)
{
	OPTICK_EVENT();

	float retdt = 0.0f;
	auto updateFrequencyComp = getUpdateFrequencyCom();
	if (updateFrequencyComp && !m_bIgnoreUpdateFrequencyCtrl && !updateFrequencyComp->checkNeedUpdate(dtime, retdt))
	{
		return;
	}

	if (m_bIgnoreUpdateFrequencyCtrl)
		retdt = dtime;

	//ActorLocoMotion *locoMotion = getLocoMotion();

	bool isremote = m_pWorld && m_pWorld->isRemoteMode();
	OnTransformUpdate();
	if (m_pComponentLocoMotion && (!m_bStatic || (IsObject() && isremote)))
	{
		m_pComponentLocoMotion->update(retdt);
	}

	if (m_pModelLerpComponent && useNewLerpModel())
		m_pModelLerpComponent->update(dtime);

	ActorBody *body = getBody();
	if (body)
	{
		body->update(retdt);
		if ((isSleeping() || isRestInBed()) && body->isLookAt())  //睡觉状态头就不要乱转了
		{
			//ActorLocoMotion *loc = getLocoMotion();
			if (m_pComponentLocoMotion)
			{
				body->resetPos();
			}
		}
	}
	else
	{
		if (IsObject() && GetWorldManagerPtr() && GetWorldManagerPtr()->m_updategraphicsvec)
		{
			GetWorldManagerPtr()->m_updategraphicsvec(dtime, this, GetWorldPosition());
		}
	}

	Rainbow::GameObject* pGameObject = GetMeshRenderGO();
	if (IsObject() && pGameObject)
	{
		if (pGameObject->GetTransform() && !useNewLerpModel())
		{
			pGameObject->GetTransform()->SetLocalPosition(GetWorldPosition());
			pGameObject->GetTransform()->SetLocalRotation(GetWorldRotation());
		}
	}

	/*if (m_pModelTransformLerpComponent)
		m_pModelTransformLerpComponent->update(retdt);*/

	//客机，实体父节点如果不被运动器驱动运动时才需要平滑
	if (IsObject() && isremote && m_ParentWID == 0 && !IsControlByScript())
	{
		// 更新transform
		auto pTransform = GetTransform();
		if (pTransform)
		{
			if (m_pComponentLocoMotion)
			{
				pTransform->SetWorldPosition(m_pComponentLocoMotion->m_UpdatePos.toVector3());
				pTransform->SetWorldRotation(m_pComponentLocoMotion->m_UpdateRot);
			}
		}
	}

	if (IsObject())
		UpdateGameObjectTransform();

	if (m_pSoundComponent)
	{
		m_pSoundComponent->onUpdate(retdt);
	}

	if (updateFrequencyComp)
	{
		updateFrequencyComp->finishUpdate();
	}

#ifdef ENABLE_PLAYER_CMD_COMMAND
	if (ms_enableDebugWrapBpx)
	{
		gmDrawWrapBox();
	}
#endif

	//更新场景管理  David 2023/8/8
	//最好的方式是通過Transform的变更来通知更新，目前暂时放这里
	//actor没添加到场景前，m_pWorld为null
	/*auto newPos = this->getPosition();
	if (m_pWorld && m_LastActorUpdatePos != newPos)
	{
		m_pWorld->getActorMgr()->GetActorMGT()->UpdateNode(this);
		m_LastActorUpdatePos = newPos;
	}*/

	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([dtime](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				child->update(dtime);
			}
		});
	}
#ifndef IWORLD_SERVER_BUILD
	//技能更新
	if (getSkillComponent())
	{
		getSkillComponent()->OnUpdate(retdt);
	}
#endif
}  

void ClientActor::UpdateOctree() {
    //更新场景管理  David 2023/8/8
    //actor没添加到场景前，m_pWorld为null
    auto newPos = this->getPosition();
    if (m_pWorld && m_LastActorUpdatePos != newPos) {
		PrepareAABB();
        m_pWorld->getActorMgr()->GetActorMGT()->UpdateNode(this);
        m_LastActorUpdatePos = newPos;
		if (m_bUgcSelect)
		{
			SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("UpdateSelectActorBoundbox");
		}
    }
}

void ClientActor::setMotionChange(const Rainbow::Vector3f &motion, bool addmotion, bool changepos, bool sync_pos)
{
	m_MotionChange = true;
	m_isChangePos = changepos;

	//float oldFaceYaw;
	WCoord oldPos;

	if (getLocoMotion())
	{
		if (addmotion)
			getLocoMotion()->addMotion(motion.x, motion.y, motion.z);
		else
			getLocoMotion()->m_Motion = motion;

		if (m_isChangePos)
			getLocoMotion()->doMoveStep(motion);
	}
}

void ClientActor::setxyzMotionChange(float x, float y, float z, bool addmotion, bool changepos)
{
	setMotionChange(Rainbow::Vector3f(x, y, z), addmotion, changepos);
}

void ClientActor::getBodySize(float& width, float& height)
{
	if (getLocoMotion())
	{
		width = float(getLocoMotion()->m_BoundSize);
		height = float(getLocoMotion()->m_BoundSize);
	}
	else
	{
		width = 0.0f;
		height = 0.0f;
	}
}

bool ClientActor::haveBags()
{
	MobAttrib* attrib = static_cast<MobAttrib*>(getAttrib());
	auto bag = attrib->getBags();
	if (bag || m_pGridContainer)
	{
		return true;
	}
	return false;
}
int ClientActor::getBagsCount()
{
	if (m_pGridContainer)
	{
		return m_pGridContainer->getGridCount();
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->getGridCount();
		}
	}
	return 0;
}

BackPackGrid* ClientActor::getBagsGrid(int index)
{
	

	if (m_pGridContainer)
	{
		return m_pGridContainer->index2Grid(index);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->index2Grid(index);
		}
	}
	return nullptr;
}

BackPackGrid* ClientActor::getEquipGrid(EQUIP_SLOT_TYPE t)
{
	if (m_pEquipGridContainer)
	{
		return m_pEquipGridContainer->index2Grid(t);
	}
	else
	{
		auto living = dynamic_cast<ActorLiving*>(this);
		auto livingAttr = living != NULL ? living->getLivingAttrib() : NULL;
		if (livingAttr)
		{
			return livingAttr->getEquipGrid(t);
		}
	}
	return nullptr;
}

BackPackGrid* ClientActor::getEquipGridWithType(EQUIP_SLOT_TYPE t)
{
	if (m_pEquipGridContainer)
	{
		return m_pEquipGridContainer->index2Grid(t);
	}
	else
	{
		auto living = dynamic_cast<ActorLiving*>(this);
		auto livingAttr = living != NULL ? living->getLivingAttrib() : NULL;
		if (livingAttr)
		{
			return livingAttr->getEquipGridWithType(t);
		}
	}
	return nullptr;
}

int ClientActor::getItemCount(int itemid)
{
	if (m_pGridContainer)
	{
		return m_pGridContainer->getItemCount(itemid);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->getItemCount(itemid);
		}
	}
	return 0;
}
BackPackGrid* ClientActor::getGridByItemID(int itemid)
{
	PackContainer* bags = getBags();
	if (bags)
	{
		return bags->getGridByItemID(itemid);
	}
	return NULL;
}
int ClientActor::addItemByGridCopyData(const GridCopyData* data)
{

	if (m_pGridContainer)
	{
		return m_pGridContainer->addItembyGridCopyData(*data);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->addItem_byGridCopyData(*data);
		}
	}
	return 0;
}

int ClientActor::addItemByGrid(const BackPackGrid* data)
{
	if (m_pGridContainer)
	{
		return m_pGridContainer->addItemByGrid(data);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->addItem_byGrid(data);
		}
	}
	return 0;
}
bool ClientActor::checkBagPutItem(int resid, int num)
{
	if (m_pGridContainer)
	{
		return m_pGridContainer->checkPutItem(resid, num);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			return bags->checkPutItem(resid, num);
		}
	}
	return false;
}
void ClientActor::applyEquips(EQUIP_SLOT_TYPE t)
{
	if (m_pEquipGridContainer)
	{
		//EQUIP_WEAPON
		m_pEquipGridContainer->equip(t);
	}
	else
	{
		auto living = dynamic_cast<ActorLiving*>(this);
		auto livingAttr = living != NULL ? living->getLivingAttrib() : NULL;
		if (livingAttr && getBody())
		{
			livingAttr->applyEquips(getBody(), EQUIP_WEAPON);
		}
	}

	
}
void ClientActor::removeBagItemByCount(int itemid, int num)
{
	if (m_pGridContainer)
	{
		m_pGridContainer->removeItemByCount(itemid, num);
	}
	else
	{
		PackContainer* bags = getBags();
		if (bags)
		{
			bags->removeItemByCount(itemid, num);
		}
	}
}

bool ClientActor::isUnsighted()
{
	if (getAttrib())
	{
		return getAttrib()->isUnsighted();
	}
	return false;
}
void ClientActor::setUnsighted(bool isUnsighted)
{
	if (getAttrib())
	{
		return getAttrib()->setUnsighted(isUnsighted);
	}
}
bool ClientActor::isDead()
{
	if (getAttrib()) return getAttrib()->isDead();
	else return false;
}

bool ClientActor::isOffLine()
{
	if (getAttrib()) return getAttrib()->getOffLine();
	else return false;
}

bool ClientActor::isInWater()
{
	return getLocoMotion() ? getLocoMotion()->m_InWater : false;
}

bool ClientActor::HandleLavaMovement()
{
	return getLocoMotion() ? getLocoMotion()->handleLavaMovement() : false;
}

bool ClientActor::isInLava()
{
	return getLocoMotion() ? getLocoMotion()->m_InLava : false;
}

double ClientActor::getSquareDistToPos(double x, double y, double z)
{
	if (getLocoMotion())
	{
		WCoord pos = getLocoMotion()->getPosition();
		double vecx = pos.x - x;
		double vecy = pos.y - y;
		double vecz = pos.z - z;
		double v = vecx * vecx + vecy * vecy + vecz * vecz;
		return v;
	}
	return DBL_MAX;
}

void ClientActor::getMergeCollideBox(Rainbow::AABB& boxMerge, Rainbow::AABB& box)
{
	Vector3f min1 = boxMerge.CalculateMin();
	Vector3f max1 = boxMerge.CalculateMax();
	Vector3f min2 = box.CalculateMin();
	Vector3f max2 = box.CalculateMax();
	Vector3f mergeMin = {min1.x < min2.x ? min1.x:min2.x, min1.y < min2.y ? min1.y:min2.y, min1.z < min2.z ? min1.z:min2.z};
	Vector3f mergeMax = { max1.x > max2.x ? max1.x:max2.x, max1.y > max2.y ? max1.y:max2.y, max1.z > max2.z ? max1.z:max2.z};

	boxMerge.SetCenterAndExtent({(mergeMin.x+mergeMax.x)/2, (mergeMin.y+mergeMax.y)/2, (mergeMin.z+mergeMax.z)/2},
									{(mergeMax.x-mergeMin.x)/2, (mergeMax.y-mergeMin.y)/2, (mergeMax.z-mergeMin.z)/2 });

	return;
}

void ClientActor::getCollideBox(CollideAABB& box)
{
	if (IsPhysics())
	{
		getLocoMotion()->getHitCollideBox(box);
	}
	else if (getLocoMotion())
	{
		getLocoMotion()->getCollideBox(box);
	}
	else
	{
		box.dim = WCoord(0, 0, 0);
		box.pos = WCoord(0, 0, 0);
	}
}

void ClientActor::getMultiCollideBox(std::vector<CollideAABB>& boxs)
{
	if (getLocoMotion())
	{
		getLocoMotion()->getCollideBoxs(boxs);
	}
	else
	{
		CollideAABB box;
		getCollideBox(box);
		boxs.push_back(box);
	}
}

void ClientActor::getBoundingBoxLocal(Rainbow::BoxSphereBound& bound)
{
	if (getLocoMotion())
	{
		getLocoMotion()->getBoundingBoxLocal(bound);
	}
	else
	{
		bound.m_Center = Vector3f(0.0f);
		bound.m_Extent = Vector3f(0.0f);
		bound.m_Radius = 0.0f;
	}
}

void ClientActor::getHitCollideBox(CollideAABB& box)
{
	if (getLocoMotion())
	{
		getLocoMotion()->getHitCollideBox(box);
	}
	else
	{
		box.dim = WCoord(0, 0, 0);
		box.pos = WCoord(0, 0, 0);
	}
}

void ClientActor::getViewBox(CollideAABB &box)
{
	if (getLocoMotion())
	{
		getLocoMotion()->getCollideBox(box);
	}
	else
	{
		box.dim = WCoord(0, 0, 0);
		box.pos = WCoord(0, 0, 0);
	}
}

double ClientActor::getSquareDistToActor(ClientActor *pActor)
{
	if (pActor == NULL || m_CurMapID != pActor->m_CurMapID || getLocoMotion() == NULL || pActor->getLocoMotion() == NULL)
	{
		return 9999999999.0;
	}

	WCoord pos = getLocoMotion()->getPosition();
	WCoord pos1 = pActor->getLocoMotion()->getPosition();
	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(pActor);
	if (vehicle)
	{
		int x = -1, y = -1, z = -1;
		vehicle->intersect(this, x, y, z);
		if (x >= 0 && y >= 0 && z >= 0)
		{
			WCoord blockpos = WCoord(x, y, z);
			pos1 = vehicle->convertWcoord(blockpos);
		}
	}

	double vecx = pos.x - pos1.x;
	double vecy = pos.y - pos1.y;
	double vecz = pos.z - pos1.z;

	return vecx * vecx + vecy * vecy + vecz * vecz;
}

int ClientActor::getEyeHeight()
{
	return getLocoMotion() ? getLocoMotion()->m_BoundHeight * 80 / 100 : 0;
}

void ClientActor::SyncPosition(const WCoord &pos, int yaw, int pitch, float angle/*=30.0f*/)
{
	//getLocoMotion()->gotoPosition(pos, yaw, pitch);
	//m_TickPosition.beginTick(pos);
	if (getLocoMotion())
	{
		getLocoMotion()->m_RotateYaw = (float)yaw;
		getLocoMotion()->m_RotationPitch = (float)pitch;
	}

	//setLookAt((float)yaw, (float)pitch, angle, angle);
	if (!isStopBodyRotation())
	{
		if (getBody()) {
			getBody()->setRotate((float)yaw, (float)pitch, 0);
		}
	}

	if (GetWorldManagerPtr()->getWorldId() == SELECTROLEWORLDID && getBody())
	{
		getBody()->setPosition(pos.x, pos.y, pos.z);
	}
	//	getLocoMotion()->setPosition(pos.x, pos.y, pos.z);
	//	getLocoMotion()->gotoPosition(pos, yaw, pitch);
}

void ClientActor::setPosition(int x, int y, int z)
{
	Rainbow::GameObject *pGameObject = GetMeshRenderGO();
	if (pGameObject && !useNewLerpModel())
	{
		Rainbow::Transform* transform = pGameObject->GetTransform();
		if (transform) {
			Rainbow::Vector3f position(x, y, z);
			transform->SetLocalPosition(position);
		}
	}

	if (getLocoMotion())
	{
		getLocoMotion()->setPosition(x, y, z);
	}

	PrepareAABB();

	// 	getLocoMotion()->m_Position = WCoord(x, y, z);
	OnAttributeChanged(this, &R_LocomotionPos);

	if (m_bUgcSelect)
	{
		SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("UpdateSelectActorBoundbox");
	}
}


WCoord& ClientActor::getPosition()
{
	if (getLocoMotion())
	{
		return getLocoMotion()->getPosition();
	}

	return s_Wcoord;
}

bool ClientActor::GetScreenPosition(int& x, int& y, bool inbox) // 获取对象在屏幕中的2D位置 只在客机生效
{
#ifndef IWORLD_SERVER_BUILD
	x = -1;
	y = -1;
	Vector3f result(0, 0, 0);
	Camera* camera = CameraManager::GetInstance().getEngineCamera();
	if (camera)
	{
		Vector3f vec3 = getPosition().toVector3();
		if (inbox && getLocoMotion())
		{
			vec3.y += getLocoMotion()->m_BoundHeight;
		}
		result = camera->WorldToScreenPoint(vec3);
		x = result.x;
		y = result.y;
		return true;
	}
#endif
	return false;
}

void ClientActor::setPosition(const WCoord &pos)
{
	setPosition(pos.x, pos.y, pos.z);
	//if(m_LocoMotion)
	//{
	//	m_LocoMotion->setPosition(pos.x, pos.y, pos.z);
	//}
}

void ClientActor::SetMoveDir(float x, float y, float z)
{
	if (getLocoMotion())
	{
		getLocoMotion()->setMoveDir(Vector3f(x, y, z));
	}
}

void ClientActor::moveToPosition(const WCoord &pos, float yaw, float pitch, int interpol_ticks)
{
	if (getLocoMotion() == NULL) return;
	getLocoMotion()->m_RotateYaw = yaw;
	getLocoMotion()->m_RotationPitch = pitch;

	if (IsObject() && m_pWorld && m_pWorld->isRemoteMode())
	{
		getLocoMotion()->m_PosRotationIncrements = interpol_ticks;
		getLocoMotion()->m_ServerPos = pos;
	}
	else
	{
		getLocoMotion()->setPosition(pos);
	}
}

void ClientActor::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
	if (getLocoMotion() == NULL) return;
	
	if (IsObject() && m_pWorld && m_pWorld->isRemoteMode())
	{
		getLocoMotion()->m_PosRotationIncrements = interpol_ticks;
		getLocoMotion()->m_ServerPos = pos;
		getLocoMotion()->m_ServerRot = rot;
	}
	else
	{
		getLocoMotion()->setPosition(pos);
		Rainbow::Vector3f euler = QuaternionToEulerAngle(rot);//rot.EulerAngle();
		// 这里貌似写反了，老代码暂时没有动了。m_RotateYaw = euler.y
		getLocoMotion()->m_RotateYaw = euler.x;
		getLocoMotion()->m_RotationPitch = euler.y;
	}
}

int ClientActor::getCurPlaceDir()
{
	float yaw = getLocoMotion() ? getLocoMotion()->m_RotateYaw : 0;

	int dir = int((yaw + 180.0f) / 90.0f + 0.5f) & 3;
	return dir == 0 ? DIR_NEG_Z : (dir == 1 ? DIR_NEG_X : (dir == 2 ? DIR_POS_Z : (dir == 3 ? DIR_POS_X : DIR_NEG_Y)));
}

void ClientActor::setFaceDir(float x, float y, float z)
{
	Vector3f dir;
	dir.x = x;
	dir.y = y;
	dir.z = z;
	float yaw = 0;
	float pitch = 0;
	Direction2PitchYaw(&yaw, &pitch, dir);
	setFaceYaw(yaw);
	setFacePitch(pitch);
}

void ClientActor::getFaceDir(float &x, float &y, float &z)
{
	if (getLocoMotion() == NULL)
	{
		return;
	}

	Vector3f dir;
	PitchYaw2Direction(dir, getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch);
	x = dir.x;
	y = dir.y;
	z = dir.z;
}

void ClientActor::setFaceYaw(float yaw, bool needSync/* = false*/)
{
	//SandboxSceneComponent* comp = FindCompChild("ActorLocoMotion");
	if (getLocoMotion() == NULL)
	{
		return;
	}

	getLocoMotion()->m_RotateYaw = yaw;
	if (getBody())
	{
		setLookAt(getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch, 360.0f, 360.0f);
	}
}

float ClientActor::getFaceYaw()
{
	if (getLocoMotion())
	{
		return getLocoMotion()->m_RotateYaw;
	}

	return 0.0f;
}

void ClientActor::setFacePitch(float pitch)
{
	if (getLocoMotion() == NULL)
	{
		return;
	}

	getLocoMotion()->m_RotationPitch = pitch;
	setLookAt(getLocoMotion()->m_RotateYaw, getLocoMotion()->m_RotationPitch, 360.0f, 360.0f);
}

float ClientActor::getFacePitch()
{
	if (getLocoMotion() == NULL)
	{
		return 0.0f;
	}

	return getLocoMotion()->m_RotationPitch;
}

float ClientActor::getNavigationFacePitch()
{
	if (getLocoMotion() == NULL)
	{
		return 0.0f;
	}

	return getLocoMotion()->m_NavigationRotationPitch;
}

void ClientActor::getPosition(int &x, int &y, int &z)
{
	if (getLocoMotion() == NULL)
	{
		return;
	}

	x = getLocoMotion()->m_Position.x;
	y = getLocoMotion()->m_Position.y;
	z = getLocoMotion()->m_Position.z;
}

void ClientActor::leapTarget(WCoord &pos, float motionY)
{
	if (getLocoMotion() == NULL) return;
	WCoord dp = pos - getLocoMotion()->getPosition();
	dp.y = 0;
	float dplen = dp.length();
	if (dplen == 0) dplen = 1.0f;

	getLocoMotion()->m_Motion.x += dp.x / dplen * 50.0f * 0.8f + getLocoMotion()->m_Motion.x*0.2f;
	getLocoMotion()->m_Motion.z += dp.z / dplen * 50.0f * 0.8f + getLocoMotion()->m_Motion.z*0.2f;
	getLocoMotion()->m_Motion.y = motionY;
	ActorLocoMotion::CheckMotionValid(getLocoMotion()->m_Motion);
}

extern float UpdateRotation(float from, float to, float range);
void ClientActor::faceActor(ClientActor *target, float yawpseed, float pitchspeed)
{
	if (target == NULL || getLocoMotion() == NULL)
	{
		return;
	}

	WCoord dp = target->getPosition() - getPosition();

	ActorLiving *living = dynamic_cast<ActorLiving *>(target);
	if (living)
	{
		dp.y += living->getEyeHeight() - getEyeHeight();
	}
	else
	{
		if (target->getLocoMotion())
		{
			dp.y += target->getLocoMotion()->m_BoundHeight / 2 - getEyeHeight();
		}
	}

	Vector3f dir = dp.toVector3();
	float yaw, pitch;
	Direction2PitchYaw(&yaw, &pitch, dir);

	getLocoMotion()->m_RotationPitch = UpdateRotation(getLocoMotion()->m_RotationPitch, pitch, pitchspeed);
	getLocoMotion()->m_RotateYaw = UpdateRotation(getLocoMotion()->m_RotateYaw, yaw, yawpseed);
}

void ClientActor::faceWorldPos(WCoord pos, float yawpseed, float pitchspeed)
{
	if (getLocoMotion() == NULL)
	{
		return;
	}

	WCoord dp = pos - getPosition();

	Vector3f dir = dp.toVector3();
	float yaw, pitch;
	Direction2PitchYaw(&yaw, &pitch, dir);

	getLocoMotion()->m_RotationPitch = UpdateRotation(getLocoMotion()->m_RotationPitch, pitch, pitchspeed);
	getLocoMotion()->m_RotateYaw = UpdateRotation(getLocoMotion()->m_RotateYaw, yaw, yawpseed);
}

WCoord ClientActor::getEyePosition()
{
	if (getLocoMotion() == NULL)
	{
		return WCoord(0, -1, 0);
	}

	return getLocoMotion()->getPosition() + WCoord(0, getEyeHeight(), 0);
}

WCoord ClientActor::getChestPosition()
{
	if (getLocoMotion() == NULL)
	{
		return WCoord(0, -1, 0);
	}

	return getLocoMotion()->getPosition() + WCoord(0, getChestHeight(), 0);
}

void  ClientActor::showNickName(bool bshow)
{
	if (getBody())
	{
		getBody()->setVisibleDispayName(bshow);
	}
}
void ClientActor::setNickName(char*nickname)
{
	ActorLiving * actor = dynamic_cast<ActorLiving*>(this);
	if (actor)
	{
		int objType = getObjType();
		ClientPlayer* player = nullptr;
		if (objType == OBJ_TYPE_ROLE)
		{
			player = static_cast<ClientPlayer*>(this);
			getBody()->setDispayName(nickname, actor->getTeam(), 0, player->m_BPTitle.c_str());
		}
		else
		{
			ClientMob * mob = ToCast<ClientMob>();
			if (mob)
			{
				mob->setDisplayName(nickname);
			}
			else
			{
				getBody()->setDispayName(nickname, actor->getTeam());
			}
		}
	}
}

void ClientActor::addIdleAnimal(int animal, int prop)
{
	if (getBody())
	{
		getBody()->addIdleAnimal(animal, prop);
	}
}

void ClientActor::rotateBodyToActor(ClientActor *pActor)
{
	if (getBody() && pActor)
	{
		getBody()->rotateBodyTo(pActor->getPosition());
	}
}


void ClientActor::setLookAt(ClientActor *pActor, float deltaLookYaw, float deltaLookPitch)
{
	if (pActor)
	{
		WCoord eyeposition = pActor->getEyePosition();
		setLookAt(eyeposition.x, eyeposition.y, eyeposition.z, deltaLookYaw, deltaLookPitch);
	}
}

void ClientActor::setLookAt(int x, int y, int z, float deltaLookYaw, float deltaLookPitch)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setisLookAt(true);
		WCoord target(x, y, z);
		WCoord dp = target - getEyePosition();
		float yaw	= 0.0f;
		float pitch = 0.0f;
		Direction2PitchYaw(&yaw, &pitch, dp.toVector3());

		getBody()->setLookTargetYaw(yaw);
		getBody()->setLookTargetPitch(pitch);
		getBody()->setDeltaLookPitch(deltaLookPitch);
		getBody()->setDeltaLookYaw(deltaLookYaw);
	}
}

void ClientActor::setLookAt(float yaw, float pitch, float deltaLookYaw, float deltaLookPitch)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setisLookAt(true);
		getBody()->setLookTargetYaw(yaw);
		getBody()->setLookTargetPitch(pitch);
		getBody()->setDeltaLookPitch(deltaLookPitch);
		getBody()->setDeltaLookYaw(deltaLookYaw);
	}
}

void ClientActor::setHeadLookAt(float yaw, float pitch, float deltayaw, float deltapitch)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setLookTargetYaw(yaw);
		actorbody->setLookTargetPitch(pitch);
		actorbody->setDeltaLookPitch(deltapitch);
		actorbody->setDeltaLookYaw(deltayaw);
	}
}

void ClientActor::keepLookAt(const ClientActor& pActor, float deltaYaw, float deltaPitch)
{
	if (getBody())
	{
		const WCoord& eyeposition = const_cast<ClientActor&>(pActor).getEyePosition();
		keepLookAt(eyeposition.x, eyeposition.y, eyeposition.z, deltaYaw, deltaPitch);
	}
}

void ClientActor::keepLookAt(int x, int y, int z, float deltaYaw, float deltaPitch)
{
	if (getBody())
	{
		WCoord target(x, y, z);
		WCoord dp = target - getEyePosition();
		float yaw = 0.0f;
		float pitch = 0.0f;
		Direction2PitchYaw(&yaw, &pitch, dp.toVector3());
		keepLookAt(yaw, pitch, deltaYaw, deltaPitch);
	}
}

void ClientActor::keepLookAt(float yaw, float pitch, float deltaYaw, float deltaPitch)
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setisLookAt(true);
		actorbody->setkeeplook(true);
		actorbody->setLookTargetYaw(yaw);
		actorbody->setLookTargetPitch(pitch);
		actorbody->setDeltaLookYaw(deltaYaw);
		actorbody->setDeltaLookPitch(deltaPitch);
	}
}

void ClientActor::keepLookAtByDirection(float x, float y, float z, float deltaYaw, float deltaPitch)
{
	if (getBody())
	{
		Rainbow::Vector3f dir(x, y, z);
		float yaw = 0.0f;
		float pitch = 0.0f;
		Direction2PitchYaw(&yaw, &pitch, dir);
		keepLookAt(yaw, pitch, deltaYaw, deltaPitch);
	}
}

void ClientActor::stopLookAt()
{
	ActorBody* actorbody = getBody();
	if (actorbody)
	{
		actorbody->setkeeplook(false);
		actorbody->resetPos();
	}
}

void ClientActor::setHeadIconByPath(const char* imageResPath, const char* imageResUVName, int imageWidth /*= 0*/, int imageHeight /*= 0*/,bool isSync/*=true*/)
{
	if (m_Body)
	{
		m_Body->setHeadIconByPath(imageResPath, imageResUVName, imageWidth, imageHeight, isSync);
	}
}

void ClientActor::bePushWithPlayer(ClientActor* player)
{

}

void ClientActor::initBornBuff()
{
	if (m_monsterDef)
	{
		if (m_monsterDef->BornBuff)
		{
			int buff = m_monsterDef->BornBuff;
			int buffId = buff / 1000;
			int buffLv = buff % 1000;
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
			if (pLivingAttrib)
			{
				pLivingAttrib->addBuffOnLoad(buffId, buffLv, 0);
			}
		}
	}
}

void ClientActor::onCollideWithPlayer(ClientActor* player)
{
	if (player == NULL)
	{
		return;
	}
	if (GCheckClientActorToClientPlayer(player))
	{
		//if (IsPhysics())
		{
			if (m_pPhysicsComponent)
			{
				PlayerLocoMotion* pPlayerLoco = static_cast<PlayerLocoMotion*>(player->getLocoMotion());
				if (pPlayerLoco->getPhysType() == RolePhysType::PHYS_ROLECONTROLLER)
					m_pPhysicsComponent->OnCollisionEvent(PhxEvent_CollidePlayer, player);
			}
		}
		player->onCollideWithActor(this);
	}
}

void ClientActor::onCollideWithActor(ClientActor* pActor)
{
}

void ClientActor::collideWithActor(ClientActor *actor)
{
	if (actor == NULL)
	{
		return;
	}

	if (isSleeping() || actor->isSleeping())
	{
		return;
	}
	//坐骑不必跟乘客一直触发碰撞
	auto RidComp = getRiddenComponent();
	if (RidComp && (RidComp->checkRidingByActorObjId(actor->getObjId()) || RidComp->checkRiddenByActorObjId(actor->getObjId(), true)))
	{
		return;
	}

	static std::function<bool(ActorLocoMotion*)> func = [&](ActorLocoMotion *loco)
	{
		if (loco && loco->getJumping())
		{
 			return true;
		}
		return false;
	};
 	if (func(actor->getLocoMotion()) || func(getLocoMotion()))
	{
		return ;
	}
	
	do 
	{
		if (actor->getObjType() == OBJ_TYPE_ROLE)
		{
			if (GCheckClientActorToClientPlayer(actor))
			{
				if(applyActorElasticCollision(actor))
					break;
			}
		}
		if (getObjType() == OBJ_TYPE_ROLE)
		{
			if (GCheckClientActorToClientPlayer(this))
			{
				if(applyActorObstructCollision(actor, this))
					break;
			}
		}
		actor->applyActorCollision(this);
	} while( false );
	auto triggerComponent = getTriggerComponent();
	if (triggerComponent)
	{
		triggerComponent->checkCollideOnTrigger(actor);
	}
	unsigned int targetobj = actor->getObjId()&0xffffffff;
	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_ACTOR_COLLIDE, 0, getObjId()&0xffffffff, (char*)&targetobj, sizeof(targetobj));
}

bool ClientActor::applyActorElasticCollision(ClientActor *actor)
{
	if(actor == NULL || getLocoMotion() == NULL || !getCanCollide()) 
	{
		return false;
	}
		
	int mass1 = getMass();
	int mass2 = actor->getMass();
	float x = 0.0f;
	float y = 0.0f;
	float z = 0.0f;
	if ((mass1 + mass2) != 0)
	{
		x = ((mass1 - mass2)*getLocoMotion()->m_Motion.x + 2 * mass2*actor->getLocoMotion()->m_Motion.x) / (mass1 + mass2);
		y = ((mass1 - mass2)*getLocoMotion()->m_Motion.y + 2 * mass2*actor->getLocoMotion()->m_Motion.y) / (mass1 + mass2);
		z = ((mass1 - mass2)*getLocoMotion()->m_Motion.z + 2 * mass2*actor->getLocoMotion()->m_Motion.z) / (mass1 + mass2);
	}
	
	getLocoMotion()->addMotion(x*0.5f, y*0.5f, z*0.5f);
	return true;
}

void ClientActor::applyActorCollision(ClientActor* actor)
{
	auto RidComp = getRiddenComponent();
	if (actor && /*actor->m_RidingActor!=getObjId()*/((!RidComp) || (RidComp && !RidComp->checkRidingByActorObjId(getObjId()) && !RidComp->checkRiddenByActorObjId(getObjId()))) && actor->getLocoMotion() && getLocoMotion())
	{
		WCoord dpos = actor->getLocoMotion()->getPosition() - getLocoMotion()->getPosition();
		ActorVehicleAssemble* vehicle = NULL;
		if (actor->getObjType() == OBJ_TYPE_VEHICLE)
		{
			vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		}

		if (vehicle)
		{
			dpos = vehicle->getMassCenterWcoord() - getLocoMotion()->getPosition();
		}

		float x = float(dpos.x) / BLOCK_FSIZE;
		float z = float(dpos.z) / BLOCK_FSIZE;

		float max_xz = Max(Abs(x), Abs(z));
		if (max_xz > 0)
		{
			float r = Sqrt(max_xz);
			x = x / r;
			z = z / r;
			float inv_r = 1.0f / r;

			if (inv_r > 1.0f) inv_r = 1.0f;

			x *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);
			z *= inv_r * 5.0f * (1.0f - ACTOR_COLLIDE_DEC);

			//如果是载具和生物相撞，将碰撞效果做的更加明显
			if (vehicle) {
				int speedVariable = (int)(vehicle->getCurSpeedShow() / 5.0f);
				x *= speedVariable;
				z *= speedVariable;
			}
			//LOG_INFO("vehicle mass: %d   actor mass: %d   x: %f   z: %f", actor->getMass(), getMass(), x, z);

			if (0.2f * getMass() < actor->getMass() && getCanCollide())
			{
				getLocoMotion()->m_CollideMoveTicks = 3;
				getLocoMotion()->addMotion(-x, 0, -z);
			}

			//只要是车和生物发生碰撞，也都加上碰撞效果
			if (((getMass() > 0.2f * actor->getMass()) || vehicle)&& actor->getCanCollide())
			{
				actor->getLocoMotion()->addMotion(x, 0, z);
			}
		}
	}
}

bool ClientActor::applyActorObstructCollision(ClientActor* actor, ClientActor* player)
{
	if (actor == NULL || actor->getLocoMotion() == NULL || this->getLocoMotion() == NULL)
	{
		return false;
	}

	if (GCheckClientActorToActorBasketBall(actor) ||
		GCheckClientActorToActorBall(actor))
	{
		actor->bePushWithPlayer(player);
	}
	else
	{
		int mass1 = getMass();
		int mass2 = actor->getMass();
		float x = 0.0f;
		float y = 0.0f;
		float z = 0.0f;
		if ((mass1 + mass2) != 0)
		{
			x = ((mass1 - mass2) * actor->getLocoMotion()->m_Motion.x + 2 * mass1 * this->getLocoMotion()->m_Motion.x) / (mass1 + mass2);
			y = ((mass1 - mass2) * actor->getLocoMotion()->m_Motion.y + 2 * mass1 * this->getLocoMotion()->m_Motion.y) / (mass1 + mass2);
			z = ((mass1 - mass2) * actor->getLocoMotion()->m_Motion.z + 2 * mass1 * this->getLocoMotion()->m_Motion.z) / (mass1 + mass2);
		}
		if (actor->getLocoMotion() && actor->getCanCollide())
		{
			actor->getLocoMotion()->addMotion(x, 0, z);
		}
	}
	return true;
}

bool ClientActor::AttackedFromType(ATTACK_TYPE atktype, float atkpoints, ClientActor *attacker, bool bhit /*= false*/)
{
	OneAttackData atkdata;
	//memset(&atkdata, 0, sizeof(atkdata));
	atkdata.damage_armor = true;
	// 新伤害计算系统 code-by:liya
	if (GetLuaInterfaceProxy().get_lua_const()->isOpenNewHpdecCalculate && ((atktype >= ATTACK_PUNCH && atktype <= MAX_MAGIC_ATTACK) || atktype == PHYSICS_ATTACK))
	{
		const int index = AtkType2ArmorIndex(atktype);
		atkdata.atkTypeNew = (1 << index);
		if (atktype == ATTACK_EXPLODE)
		{
			atkdata.explodePoints[0] = atkpoints;
		}
		else
		{
			atkdata.atkPointsNew[index] = atkpoints;
		}
	}
	else
	{
		atkdata.atktype = atktype;
		atkdata.atkpoints = atkpoints;
	}
	atkdata.fromplayer = dynamic_cast<ClientPlayer *>(attacker);
	atkdata.triggerhit = bhit;
	return attackedFrom(atkdata, attacker);
}
bool ClientActor::attackedFrom(OneAttackData &atkdata, ClientActor *attacker)
{
	if (dynamic_cast<ClientActorProjectile*>(this))
	{
		return false;
	}

	//if (m_Attrib)
	//	return m_Attrib->attackedFrom(atkdata, attacker);
	//else return false;

	return true;
}

void ClientActor::setNeedClear(int delay_ticks)
{
	assert(delay_ticks >= 0);

	if(m_NeedClearTicks < 0)
	{
		m_NeedClearTicks = delay_ticks;

		if (m_IsInPrefabScene && m_NeedClearTicks == 0) //在prefab scene中如果延迟释放，由于scene先释放会导致gameobject的transform等出现各种问题
		{
			Destroy();
		}
	}
}

ActorManager* ClientActor::getActorMgr()
{
	if(getWorld())
	{
		return static_cast<ActorManager*>(getWorld()->getActorMgr());
	}
	
	return NULL;
}

bool ClientActor::castShadow()
{
	if (IsObject() && !IsEmpty())
	{
		return true;
	}

	return false;
}

float ClientActor::getBrightness(float t)
{
	WCoord pos = getEyePosition();
	if(m_pWorld)
	{
		return m_pWorld->getBlockBright(CoordDivBlock(pos));
	}
	
	return 0.0f;
}

void ClientActor::setBeHurtTarget(ClientActor *pActor)
{
	m_BeHurtTimer = m_LiveTicks;
	if(pActor) m_BeHurtTarget = pActor->getObjId();
	else m_BeHurtTarget = 0;
}

ClientActor *ClientActor::getBeHurtTarget()
{
	if(m_BeHurtTarget == 0 || m_pWorld == NULL) 
	{
		return NULL;
	}

	return m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_BeHurtTarget);
}
void ClientActor::setBeAtk(ClientActor* pActor)
{
	m_BeAtkTime = m_LiveTicks;
	if (pActor)
	{
		WORLD_ID newobjid = pActor->getObjId();
		if (m_BeAtkTarget!=newobjid)
		{
			m_BeAtkTarget = newobjid;
			SandboxEventDispatcherManager::GetGlobalInstance().Emit("AIFunctionMgr_ClientMob_BeAtk",
				SandboxContext(nullptr).
				SetData_Number("objid", getObjId()).
				SetData_Number("beTarget", newobjid)
			);
		}
	}
	else m_BeAtkTarget = 0;
}
ClientActor* ClientActor::getBeAtk()
{
	if (m_BeAtkTarget == 0 || m_pWorld == NULL)
	{
		return NULL;
	}

	return m_pWorld->getActorMgr()->ToCastMgr()->findActorByWID(m_BeAtkTarget);
}


float ClientActor::getVerticalFaceSpeed()
{
	return 40.0f;
}

bool ClientActor::canBePushed()
{
	return false;
}

void ClientActor::jumpOnce()
{
	m_DoJumping = 1;
}

void ClientActor::syncJump()
{
	PB_PlayerCommonSetHC playerJumpHC;
	playerJumpHC.set_uin((int)getObjId());
	
	if(m_pWorld)
	{
		if (!m_pWorld->isRemoteMode()) {
			GameNetManager::getInstance()->sendBroadCast(PB_PLAYER_JUMP_HC, playerJumpHC);
		}else {
			GameNetManager::getInstance()->sendToHost(PB_PLAYER_JUMP_CH, playerJumpHC);
		}
	}	
}

void ClientActor::kill()
{
#ifdef BUILD_MINI_EDITOR_APP
	//工具编辑模式下，屏蔽死亡逻辑
	auto editorRunMode = MNSandbox::SandBoxCfg::GetInstancePtr()->getRun();
	if (editorRunMode == MNSandbox::RunMode::editor_edit)
	{
		return;
	}
#endif //
	if (nullptr != SureDieComponent())
	{
		//if(getAttrib()) getAttrib()->addHP(-1000000.0f);
		if (getAttrib())
		{
			getAttrib()->setHpForTrigger(-1.0f);
		}
	}
}

DieComponent* ClientActor::SureDieComponent()
{
	auto dieComp = GetComponent<DieComponent>();
	if (!dieComp && !m_bNoDieComponent)
	{
		dieComp = CreateComponent<DieComponent>("DieComponent");
	}

	return dieComp;
}

void ClientActor::playAnim(int seq, bool include_me /* = false */, int loop /*= -1*/)
{
	if (getBody())
	{
		getBody()->playAnim(seq, loop);
		NotifyPlayAnim2Tracking(seq, include_me);
	}
}

void ClientActor::NotifyPlayAnim2Tracking(int anim, bool include_me /* = false */)
{
	if (m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_ActorAnimHC actorAnimHC;
		actorAnimHC.set_actorid(getObjId());
		actorAnimHC.set_anim(anim);
		actorAnimHC.set_anim1(127);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_ANIM_HC, actorAnimHC, this, include_me, UNRELIABLE_SEQUENCED);
	}
}

void ClientActor::AddSkillCom()
{
	if (!m_pSkillComponent)
	{
		CreateComponent<SkillComponent>("SkillComponent");
	}
}

void ClientActor::activeSkill(const Rainbow::FixedString& id, long long targerid)
{
	if (id.empty())
		return;
	if (!m_pSkillComponent)
	{
		AddSkillCom();
	}
	if (m_pSkillComponent)
	{
		if (m_pSkillComponent->haveSkill(id))
			return;

		m_pSkillComponent->addSkill(id);
		if (targerid > 0)
		{
			m_pSkillComponent->SetTarger(id, targerid);
		}
	}
}

bool ClientActor::playAnimById(int id, int inputloopmode , int playLayer)
{
	if (getBody())
	{
		getBody()->playAnimBySeqId(id, inputloopmode, playLayer);
		//if (id == 100103)
			//getBody()->setCurAnim(SEQ_SITCHAIR, 0);

		if (m_pWorld && !m_pWorld->isRemoteMode())
		{
			PB_ActorPlayAnimByIdHC actorPlayAnimByIdHC;
			actorPlayAnimByIdHC.set_actorid(getObjId());
			actorPlayAnimByIdHC.set_animid(id);
			actorPlayAnimByIdHC.set_loopmode(inputloopmode);
			actorPlayAnimByIdHC.set_layer(playLayer);
			actorPlayAnimByIdHC.set_crossfade(-1.0f);
			m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_PLAY_ANIM_BY_ID_HC, actorPlayAnimByIdHC, this);
		}
		return true;
	}

	return false;
}

bool ClientActor::playAnimByIdNew(int id, int inputloopmode, int playLayer, float crossfade)
{
	if (getBody())
	{
		if (getEntity() && getEntity()->GetMainModel())
		{
			auto model = getEntity()->GetMainModel();
			if (model && model->HasAnim(id))
			{
				model->PlayAnim(id, 1, 1, inputloopmode, playLayer, crossfade);
			}
		}

		if (m_pWorld && !m_pWorld->isRemoteMode())
		{
			PB_ActorPlayAnimByIdHC actorPlayAnimByIdHC;
			actorPlayAnimByIdHC.set_actorid(getObjId());
			actorPlayAnimByIdHC.set_animid(id);
			actorPlayAnimByIdHC.set_loopmode(inputloopmode);
			actorPlayAnimByIdHC.set_layer(playLayer);
			actorPlayAnimByIdHC.set_crossfade(crossfade);
			m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_PLAYANIM_NEW_CH, actorPlayAnimByIdHC, this);
		}
		return true;
	}
	return false;
}

int ClientActor::getObjType() const
{
	return OBJ_TYPE_GAMEOBJECT;
}

bool ClientActor::isBurning()
{
	return false;
	//return getAttrib()->m_immuneToFire==0 && getAttrib()->m_Fire>0;
}

void ClientActor::UpdateFireBurning()
{
	if (isDead()) return;
	if (getAttrib() == NULL || getAttrib()->immuneToFire()) return;
	if (getLocoMotion() == NULL) return;
	if (getWorld() == NULL) return;

	ActorVehicleAssemble* vehicle = nullptr;
	
	if (getObjType() == OBJ_TYPE_VEHICLE)
	{
		vehicle = dynamic_cast<ActorVehicleAssemble*>(this);
	}

	int blockid = 0;
	bool burning = false;
	if (vehicle)
	{
		if (vehicle->isBurning()) burning = true;
	}
	else
	{
		CollideAABB box;
		getLocoMotion()->getCollideBox(box);
		box.expand(-1, -1, -1);
		if (getWorld()->isAnyBurning(box.minPos(), box.maxPos(), blockid)) burning = true;
	}

	if (burning)
	{
		auto component = getAttackedComponent();
		if (component)
		{
			component->attackedFromType_Base(ATTACK_FIRE, 1.0f * GetLuaInterfaceProxy().get_lua_const()->default_shanghai_beilv); //modify by null, 默认伤害倍率
		}

		bool iswet = isWet();
		if (!iswet)
		{
			auto fireBurnComponent = getFireBurnComponent();
			if (fireBurnComponent)
			{
				//if (blockid == BLOCK_BONFIRE)
				//	fireBurnComponent->setFire(100, 1, 60);//3*20  3秒
				//else
				fireBurnComponent->setFire(100, 1);
			}
		}
		else if (GenRandomInt(20) == 0)
		{
			auto sound = getSoundComponent();
			if (sound)
			{
				sound->playSound("misc.fizz", 0.7f, 1.6f + (GenRandomFloat() - GenRandomFloat()) * 0.4f);
			}
		}
	}
}

bool ClientActor::isWet()
{
	if (getLocoMotion() == NULL || getWorld() == NULL)
	{
		return false;
	}

	if (getLocoMotion()->m_InWater)
	{
		return true;
	}

	WCoord pos = getPosition();
	if (getWorld()->getBlockRaining(CoordDivBlock(pos)))
	{
		return true;
	}

	if (getWorld()->getBlockRaining(CoordDivBlock(pos + WCoord(0, getLocoMotion()->m_BoundHeight, 0))))
	{
		return true;
	}

	return false;
}

bool ClientActor::interact(ClientActor* pPlayer, bool onshift, bool isMobile)
{
	//if (IsObject())
	{
		auto pScriptComponent = getScriptComponent();
		if (pScriptComponent && pPlayer)
		{
			return pScriptComponent->OnInteract(pPlayer->getObjId());
		}
	}

	return false;
}

bool ClientActor::rightClickUpInteract(ClientActor* pPlayer)
{
	//if (IsObject())
	{
		auto pScriptComponent = getScriptComponent();
		if (pScriptComponent )
		{
			return pScriptComponent->OnRMouseUpEvent(pPlayer->getObjId());
		}
	}

	return false;
}

bool ClientActor::mouseEventTrigger(game::ch::PCMouseKeyType keyType, game::ch::PCMouseEventType eventType)
{
	//if (IsObject())
	{

		if (keyType == game::ch::PCMouseKeyType::right &&
			eventType == game::ch::PCMouseEventType::down)
		{
			// 每次按下时重置
			m_triggerRightCount = 0;
		}

		if (keyType == game::ch::PCMouseKeyType::right &&
			eventType == game::ch::PCMouseEventType::up)
		{
			if ( m_triggerRightCount == 0 )
			{
				// 记录当前通知过了
				m_triggerRightCount++;
			}
			else {
				// 已经通知过了，不再通知
				return false;
			}
		}

		auto pScriptComponent = getScriptComponent();
		if (pScriptComponent)
		{
			return pScriptComponent->OnMouseEvent(getObjId(), keyType, eventType);
		}
	}

	return false;
}

int ClientActor::GetPathHideRange()
{
	auto targetComponent = getToAttackTargetComponent();
	if(targetComponent && !targetComponent->hasTarget())
	{
		return 3;
	}
	else
	{
		if(getAttrib() == NULL)
		{
			return -1;
		}

		if (1 == m_PathHide) //jj怪
		{
			int ret = int(getAttrib()->getHP() - getAttrib()->getMaxHP()*0.33f);
			ret -= 8;
			if (ret < 0)
			{
				ret = 0;
			}

			ret += 3;

			return ret;
		}
		else
		{
			return  3+int(getAttrib()->getHP())-1;
		}
	}
}

bool ClientActor::IsPotionApplicable(int buffid)
{
	return true;
}

bool ClientActor::getOnRailState(WCoord &railknot, int &outindex, float &t, int &flags)
{
	return false;
}

void ClientActor::setDodge(int dodge)
{
	m_nAiInvulnerableProb = Clamp(dodge, 0, 100);
	addAttChangeFlag(2);
}

bool ClientActor::findNearestCustomBlock(WCoord& blockpos, int blockRange, bool(*valid)(ClientActor*, WCoord&, int, std::string) /* = NULL */, std::string extend/* ="" */, bool randomsort)
{
	if (getLocoMotion() == NULL || getWorld() == NULL)
	{
		return false;
	}
	WCoord center = CoordDivBlock(getLocoMotion()->getPosition());
	WCoord minpos = center - WCoord(blockRange, blockRange, blockRange);
	WCoord maxpos = center + WCoord(blockRange, blockRange, blockRange);
	//找到所有的方块
	int minx = BlockDivSection(minpos.x);
	int maxx = BlockDivSection(maxpos.x);
	int minz = BlockDivSection(minpos.z);
	int maxz = BlockDivSection(maxpos.z);
	std::vector<CHUNK_INDEX>indices;
	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			indices.push_back(ChunkIndex(x, z));
		}
	}
	if (randomsort)
		random_shuffle(indices.begin(), indices.end());
	bool findblock = false;
	int mindist = blockRange * blockRange;
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk* pchunk = m_pWorld->getChunk(indices[i]);
		if (pchunk == NULL) continue;
		int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM;
		if (maxy > maxpos.y + 1) maxy = maxpos.y + 1;
		int miny = 0;
		if (miny < minpos.y) miny = minpos.y;
		//typedef MINIW::HashTable<CHUNK_INDEX, ChunkViewerList*, ChunkIndexHashCoder> ChunkHashTable;
		//ChunkHashTable temp = m_pWorld->getChunkHashTable();
		for (int y = miny; y < maxy; y++)
		{
			for (int z = 0; z < CHUNK_BLOCK_Z; z++)
			{
				for (int x = 0; x < CHUNK_BLOCK_X; x++)
				{
					int blockid = pchunk->getBlockID(x, y, z);
					BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
					if (def)
					{
						if (def->Type && (def->Type == "custombasic" || def->Type == "fullycustomblock"))
						{
							WCoord pos = pchunk->m_Origin + WCoord(x, y, z);
							m_pWorld->setBlockAir(pos);
						}
					}
				}
			}
		}
		//if (findblock) break;
	}

	return findblock;
}

bool ClientActor::findNearestSPBlock(WCoord& blockpos, int blockRange, int SPid, bool(*valid)(ClientActor*, WCoord&, int, std::string) /* = NULL */, std::string extend/* ="" */, bool randomsort)
{
	if (getLocoMotion() == NULL || getWorld() == NULL)
	{
		return false;
	}

	WCoord center = CoordDivBlock(getLocoMotion()->getPosition());
	WCoord minpos = center - WCoord(blockRange, blockRange, blockRange);
	WCoord maxpos = center + WCoord(blockRange, blockRange, blockRange);
	//找到所有的方块
	int minx = BlockDivSection(minpos.x);
	int maxx = BlockDivSection(maxpos.x);
	int minz = BlockDivSection(minpos.z);
	int maxz = BlockDivSection(maxpos.z);
	std::vector<CHUNK_INDEX>indices;
	for (int x = minx; x <= maxx; x++)
	{
		for (int z = minz; z <= maxz; z++)
		{
			indices.push_back(ChunkIndex(x, z));
		}
	}
	if (randomsort)
		random_shuffle(indices.begin(), indices.end());
	bool findblock = false;
	int mindist = blockRange * blockRange;
	for (size_t i = 0; i < indices.size(); i++)
	{
		Chunk* pchunk = m_pWorld->getChunk(indices[i]);
		if (pchunk == NULL) continue;
		int maxy = pchunk->getTopFilledSegment() + SECTION_BLOCK_DIM;
		if (maxy > maxpos.y + 1) maxy = maxpos.y + 1;
		int miny = 0;
		if (miny < minpos.y) miny = minpos.y;
		//typedef MINIW::HashTable<CHUNK_INDEX, ChunkViewerList*, ChunkIndexHashCoder> ChunkHashTable;
		//ChunkHashTable temp = m_pWorld->getChunkHashTable();
		for (int y = miny; y < maxy; y++)
		{
			for (int z = 0; z < CHUNK_BLOCK_Z; z++)
			{
				for (int x = 0; x < CHUNK_BLOCK_X; x++)
				{
					int blockid = pchunk->getBlockID(x, y, z);
					if (blockid == SPid)
					{
						WCoord pos = pchunk->m_Origin + WCoord(x, y, z);
						m_pWorld->setBlockAir(pos);
					}
				}
			}
		}
		//if (findblock) break;
	}

	return findblock;
}


void ClientActor::setReverse(bool reverse)
{
	m_bReverse =  reverse;
	if(m_pWorld && !m_pWorld->isRemoteMode())
	{
		PB_ActorReverseHC actorReverseHC;
		actorReverseHC.set_actorid(getObjId());
		actorReverseHC.set_reverse(reverse);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_ACTOR_REVERSE_HC, actorReverseHC, this, true);
	}
}
void ClientActor::saveActorCommon(jsonxx::Object &obj)
{
	obj<<"common"<<jsonxx::Object();
	jsonxx::Object &obj_ = (jsonxx::Object &)obj.get<jsonxx::Object>("common");
	ActorLocoMotion *loc = getLocoMotion();
	obj_<<"pos"<<(WCoordToCoord3Json(loc ? loc->m_Position : WCoord(0, -1, 0)));
	obj_<<"mot"<<(Vector3ToVec3Json(loc ? Rainbow::Vector3f(loc->m_Motion.x, loc->m_Motion.y, loc->m_Motion.z) : Rainbow::Vector3f(0.0f)));

	flatbuffers::Offset<FBSave::ActorAttInfo> attinfo;
	bool bNeedSave = false;
	int8_t firstSearch = 0;
	if (g_WorldMgr && g_WorldMgr->m_RuleMgr && g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		ActorAttrib* pActorAttrib = getAttrib();
		if (pActorAttrib) {
			float maxhp = -1.0f;
			if (hasAttChanged(1)) {
				maxhp = pActorAttrib->getMaxHP();
				bNeedSave = true;
			}

			float hprecover = -1.0f;
			if (pActorAttrib->getHPRecover() > 0.0f) {
				hprecover = pActorAttrib->getHPRecover();
				bNeedSave = true;
			}

			float walkspeed = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Walk_Speed) > 0.0f) {
				walkspeed = pActorAttrib->getSpeedAtt(Actor_Walk_Speed);
				bNeedSave = true;
			}

			float swimspeed = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Swim_Speed) > 0.0f) {
				swimspeed = pActorAttrib->getSpeedAtt(Actor_Swim_Speed);
				bNeedSave = true;
			}

			float jumppower = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Jump_Speed) > 0.0f) {
				jumppower = pActorAttrib->getSpeedAtt(Actor_Jump_Speed);
				bNeedSave = true;
			}

			int attacktype = pActorAttrib->getAttackType(-1);
			if (attacktype >= 0) {
				bNeedSave = true;
			}

			float immunetype = 0;
			if (pActorAttrib->getImmuneType() > 0) {
				immunetype = (float)pActorAttrib->getImmuneType();
				bNeedSave = true;
			}

			float punchattack = -1.0f;
			float rangeattack = -1.0f;
			float punchdefense = -1.0f;
			float rangedefense = -1.0f;
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
			if (pLivingAttrib) {
				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, true) > 0.0f) {
					punchattack = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, true);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, true) > 0.0f) {
					rangeattack = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, true);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, false) > 0.0f) {
					punchdefense = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, false);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, false) > 0.0f) {
					rangedefense = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, false);
					bNeedSave = true;
				}
				if (pLivingAttrib->getFirstSearch())
				{
					firstSearch = 1;
				}
			}

			int dodge = -1;
			if (hasAttChanged(2)) {
				dodge = getAiInvulnerableProb();
				bNeedSave = true;
			}

			auto ActionAttrStateComp = getActionAttrStateComponent();
			if (ActionAttrStateComp && ActionAttrStateComp->getAllActionAttrState() != ENABLE_INITVALUE) {
				bNeedSave = true;
			}

			if (bNeedSave)
			{
				auto ActionAttrState = ActionAttrStateComp ? ActionAttrStateComp->getAllActionAttrState() : 0;
				obj<<"attinfo"<<jsonxx::Array();
				jsonxx::Array &obj_info = (jsonxx::Array &)obj.get<jsonxx::Array>("attinfo");
				obj_info<<maxhp<<hprecover<<walkspeed<<swimspeed
					<<jumppower<<attacktype<<immunetype
					<<punchattack<<rangeattack<<punchdefense
					<<rangedefense<<dodge<< ActionAttrState;
				obj << "firstsearch" << firstSearch;
			}
		}
	}
	jsonxx::Array sawtooth_;
	
	auto thornComponent = getThornBallComponent();
	if (thornComponent)
	{
		for (int i = 0; i < thornComponent->getThornAnchorNum(); i++)
		{
			auto info = thornComponent->getThornAnchorAt(i);
			sawtooth_ << jsonxx::Array();
			jsonxx::Array& thornInfo = (jsonxx::Array&)sawtooth_.get<jsonxx::Array>(0);
			thornInfo <<info.anchorId;
			thornInfo <<(Vector3ToVec3Json(Rainbow::Vector3f(info.pos.x, info.pos.y, info.pos.z)));
		}
	}
	obj<<"infomain"<<jsonxx::Array();
	jsonxx::Array &obj_main = (jsonxx::Array &)obj.get<jsonxx::Array>("infomain");
	auto functionWrapper = m_pComponentFunc;
	float FallDistance = functionWrapper ? functionWrapper->getFallDistance():0.0;
	obj_main<<m_ObjId<<(loc ? loc->m_RotateYaw : 0.0f)<<(loc ? loc->m_RotationPitch : 0.0f)<< FallDistance <<m_Flags<<m_FlagsEx<<m_LiveTicks<<m_llMasterObjId<<sawtooth_;
}


bool ClientActor::loadActorCommon(const jsonxx::Object &obj)
{
	if(getLocoMotion() == NULL) return false;
	const jsonxx::Array& infomain = obj.get<jsonxx::Array>("infomain");
	const jsonxx::Object& common = obj.get<jsonxx::Object>("common");
	SetObjId(infomain.get<jsonxx::Number>(0));
	//m_ObjId = infomain.get<jsonxx::Number>(0);
	WCoord pos = Coord3ToWCoordJson(common.get<jsonxx::Array>("pos"));
	getLocoMotion()->gotoPosition(pos, infomain.get<jsonxx::Number>(1), infomain.get<jsonxx::Number>(2));

	auto functionWrapper = m_pComponentFunc;
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(infomain.get<jsonxx::Number>(3));
	}

	getLocoMotion()->m_Motion = Vec3ToVector3Json(common.get<jsonxx::Array>("mot"));
	resetAllFlags(infomain.get<jsonxx::Number>(4));
	m_LiveTicks = infomain.get<jsonxx::Number>(5);
	m_llMasterObjId = infomain.get<jsonxx::Number>(6);

	if (g_WorldMgr && g_WorldMgr->m_RuleMgr && g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		if (obj.has<jsonxx::Array>("attinfo")) {
			const jsonxx::Array& attinfo = obj.get<jsonxx::Array>("attinfo");
			ActorAttrib* pActorAttrib = getAttrib();
			if (pActorAttrib) 
			{
				if (attinfo.get<jsonxx::Number>(0) > 0.0f)
					pActorAttrib->setMaxHP(attinfo.get<jsonxx::Number>(0));

				if (!(attinfo.get<jsonxx::Number>(1) < 0.0f))
					pActorAttrib->setHPRecover(attinfo.get<jsonxx::Number>(1));

				if (!(attinfo.get<jsonxx::Number>(2) < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Walk_Speed, attinfo.get<jsonxx::Number>(2));

				if (!(attinfo.get<jsonxx::Number>(3)  < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Swim_Speed, attinfo.get<jsonxx::Number>(3) );

				if (!(attinfo.get<jsonxx::Number>(4) < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Jump_Speed, attinfo.get<jsonxx::Number>(4));

				if (attinfo.get<jsonxx::Number>(5) >= 0)
					pActorAttrib->setAttackType(attinfo.get<jsonxx::Number>(5));

				if (attinfo.get<jsonxx::Number>(6) > 0) {
					pActorAttrib->resetImmuneType();
					pActorAttrib->setImmuneType(attinfo.get<jsonxx::Number>(6), true);
				}
			}

			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
			if (pLivingAttrib) {
				if (!(attinfo.get<jsonxx::Number>(7) < 0.0f))
					pLivingAttrib->setAttackBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo.get<jsonxx::Number>(7));

				if (!(attinfo.get<jsonxx::Number>(8) < 0.0f))
					pLivingAttrib->setAttackBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo.get<jsonxx::Number>(8));

				if (!(attinfo.get<jsonxx::Number>(9) < 0.0f))
					pLivingAttrib->setArmorBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo.get<jsonxx::Number>(9));

				if (!(attinfo.get<jsonxx::Number>(10) < 0.0f))
					pLivingAttrib->setArmorBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo.get<jsonxx::Number>(10));
				if (obj.has<jsonxx::Number>("firstsearch") && obj.get<jsonxx::Number>("firstsearch") > 0.0f)
				{
					pLivingAttrib->setFirstSearch(true);
				}
				else
				{
					pLivingAttrib->setFirstSearch(false);
				}
			}

			if (attinfo.get<jsonxx::Number>(11) >= 0)
				setAiInvulnerableProb(attinfo.get<jsonxx::Number>(11));

			if (attinfo.get<jsonxx::Number>(12) != ENABLE_INITVALUE)
			{
				auto ActionAttrStateComp = getActionAttrStateComponent();
				if (ActionAttrStateComp)
				{
					ActionAttrStateComp->setActionAttrState(attinfo.get<jsonxx::Number>(12), true);
				}
			}

		}
	}
	m_ServerPosCmp = getLocoMotion()->getPosition();
	m_ServerYawCmp = (int)(getLocoMotion()->m_RotateYaw + 360);
	m_ServerPitchCmp = (int)(getLocoMotion()->m_RotationPitch + 360);
	ActorLocoMotion::CheckMotionValid(getLocoMotion()->m_Motion);
	if (infomain.has<jsonxx::Array>(7))
	{
		auto thornComponent = sureThornBallComponent();
		if (thornComponent)
		{
			auto sawtooth_ = infomain.get<jsonxx::Array>(7);
			size_t  num = sawtooth_.size();
			for (int i = 0; i < num; i++)
			{
				auto arr = sawtooth_.get<jsonxx::Array>(i);
				auto id = arr.get<jsonxx::Number>(0);
				auto pos = Vec3ToVector3Json(arr.get<jsonxx::Array>(1));
				thornComponent->setThornAnchorId(id, pos);
			}
			thornComponent->createThornBall();
		}
	}
	return true;
}


int ClientActor::savePBActorCommon(game::common::PB_ActorCommon *actorCommon)
{
	PB_Vector3 *pos = actorCommon->mutable_pos();
	PB_Vector3 *motion = actorCommon->mutable_motion();
	ActorLocoMotion* loc = getLocoMotion();
	if (loc)
	{
		pos->set_x(loc->m_Position.x);
		pos->set_y(loc->m_Position.y);
		pos->set_z(loc->m_Position.z);
		motion->set_x(loc->m_Motion.x * 1000);
		motion->set_y(loc->m_Motion.y * 1000);
		motion->set_z(loc->m_Motion.z * 1000);
		actorCommon->set_yaw(loc->m_RotateYaw * 1000);
		actorCommon->set_pitch(loc->m_RotationPitch * 1000);
	}
	auto pTransform = GetTransform();
	if (pTransform && pTransform->GetParent())
	{
		auto rot = GetLocalRotationEuler();
		actorCommon->set_yaw(rot.y * 1000);
		actorCommon->set_pitch(rot.x * 1000);

		auto position = GetLocalPosition();
		pos->set_x(position.x);
		pos->set_y(position.y);
		pos->set_z(position.z);
	}
	
	actorCommon->set_wid(m_ObjId);
	auto functionWrapper = getFuncWrapper();
	actorCommon->set_falldist(functionWrapper ? functionWrapper->getFallDistance() : 0);
	actorCommon->set_flags(m_Flags);
	actorCommon->set_flagsex(m_FlagsEx);
	actorCommon->set_liveticks(m_LiveTicks);
	// TODO: attinfo
	actorCommon->set_masterobjid(m_llMasterObjId);
	ActorLiving *living = dynamic_cast<ActorLiving *>(this);
	if (living)
		actorCommon->set_teamid(living->getTeam());
	
	auto sawtooth_ = actorCommon->mutable_sawtooth();
	auto thornComponent = getThornBallComponent();
	if (thornComponent)
	{
		for (size_t i = 0; i < thornComponent->getThornAnchorNum(); i++)
		{
			auto info = thornComponent->getThornAnchorAt(i);
			PB_SawtoothInfo* roleinfodata = sawtooth_->Add();
			roleinfodata->set_sawtoothid(info.anchorId);
			PB_Vector3* vec = roleinfodata->mutable_pos();
			vec->set_x(info.pos.x);
			vec->set_y(info.pos.y);
			vec->set_z(info.pos.z);
		}
	}
	//同步生物不可攻击等属性
	PB_ActorAttInfo* attrinfo = actorCommon->mutable_attinfo();
	auto ActionAttrStateComp = getActionAttrStateComponent();
	if (ActionAttrStateComp && ActionAttrStateComp->getAllActionAttrState() != ENABLE_INITVALUE) {
		attrinfo->set_settingatt(ActionAttrStateComp->getAllActionAttrState());
	}
	//added by dongjianan 2023.8.15
	//desc:对子节点的网络序列化处理
	//if (actorCommon->has_sandboxnodes())

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if (NeedSupportExSandboxsInfo())
	{
		AutoRef<Stream> OutStream = SANDBOX_NEW(StreamBuffer);
		if (SerializeForSyncToStream(OutStream))
		{
			AutoRef<CustomBuffer> CustomBuffer;
			if (OutStream->SaveToBinary(CustomBuffer, true))
			{
				actorCommon->set_sandboxnodes(CustomBuffer->Data<const char>(), CustomBuffer->Size());
			}
		}
	}
#endif // SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	return 0;
}

#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
void ClientActor::filterAllSyncNodes(std::vector<MNSandbox::AutoRef<MNSandbox::SandboxNode>>& out) const
{
	//out.clear();
	//MNSandbox::AutoRef<MNSandbox::SandboxNode> self((SandboxNode*)this);
	//out.push_back(self);
	GetChildrenList().for_each([&out](SandboxNode* t) -> void {
		if (t->IsSyncSendable())
			out.push_back(t);
	});

	int index = 0;
	while (index < out.size())
	{
		out[index]->GetChildrenList().for_each([&out](SandboxNode* t) -> void {
			if (t->IsSyncSendable())
				out.push_back(t);
		});
		index++;
	};
}

bool ClientActor::NeedSupportExSandboxsInfo() const
{
	if (MNSandbox::Config::GetSingleton().IsSandboxMode()) // studio 制作的地图，不需要actor 的拓展
		return false;
	if (GetChildrenCount() > 0) //!!! 有子节点扩展的  才认为需要存档新节点部分   (避免无效冗余节点信息)
	{
		if (m_bParent) //对象组件的父节点
		{
			return false;
		}
		else
		{
			return true;
		}
	}
	return false;
}

bool ClientActor::SerializeForSyncToStream(MNSandbox::AutoRef<MNSandbox::Stream>& buffer) const
{
	{//self_node
		MNSandbox::AutoRef<MNSandbox::SandboxNode> self((SandboxNode*)this);

		long long nid = GetNodeid();
		bool ret = buffer->WriteNumber<long long>(nid);
		SANDBOX_ASSERT(ret != 0);

		size_t len = 0;
		size_t offset = buffer->GetOffset();
		buffer->WriteLengthReserved<unsigned>();

		if (!SyncSerialize::SerializeReflexAttr(self, buffer, len))
			return false;

		if (!SyncSerialize::SerializeCustomAttr(self, buffer, len))
			return false;

		ret = buffer->WriteLengthOffset<unsigned>(offset, len);
		SANDBOX_ASSERT(ret != 0);
	}

	//all sub nodes
	std::vector<AutoRef<SandboxNode>> allnodes;
	//1.filter all sub nodes
	filterAllSyncNodes(allnodes);//without self


	std::vector<AutoRef<SandboxNode>> allparents;
	allparents.resize(allnodes.size());
	for (size_t i = 0; i < allnodes.size(); i++) {
		allparents[i] = allnodes[i]->GetParent();
	}

	if (buffer->WriteNumber<unsigned>(allnodes.size()) == 0)
		return false;

	if (!SyncSerialize::SerializeNodeIds(allnodes, buffer, allparents)) { //
		SANDBOX_ASSERT(false);
		return false;
	}
	
	if (!SyncSerialize::SerializeNodesInfo(allnodes, buffer)) { //
		SANDBOX_ASSERT(false);
		return false;
	}

	return true;
}

bool ClientActor::UnserializeForSyncFromStream(MNSandbox::AutoRef<MNSandbox::Stream>& buffer)
{
	{//self node
		//SDBSTREAM_LINE(buffer);
		long long nid = 0;
		buffer->ReadNumber<long long>(nid);

		unsigned len = 0;
		buffer->ReadLength<unsigned>(len);

		MNSandbox::AutoRef<MNSandbox::SandboxNode> self((SandboxNode*)this);
		if (!SyncSerialize::UnserializeReflexAttr(buffer, self)) {
			SANDBOX_ASSERT(false);
			return false;

		}
		if (!SyncSerialize::UnserializeCustomAttr(buffer, self)) {
			SANDBOX_ASSERT(false);
			return false;
		}

		SetNodeid(nid);
		//UnbindNodeID();
		if (GetSceneManagerPtr())
			GetSceneManagerPtr()->UnbindNodeID(this); // 绑定到全局	
		BindNodeID();
	}

	std::vector<AutoRef<SandboxNode>> allnodes;
	std::vector<AutoRef<SandboxNode>> allparents;

	unsigned size = 0;
	if (!buffer->ReadNumber<unsigned>(size))
	{
		SANDBOX_ASSERT(false);
		return false;
	}

	allnodes.resize(size);
	allparents.resize(size);


	std::vector<long long>   parent_ids;
	parent_ids.resize(size);
	if (!SyncSerialize::UnserializeNodeIds(buffer, allnodes, parent_ids))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	SyncMsg::MsgStreamData::FindParents(allnodes, parent_ids, allparents);
	//bind node to sceneManager//BindNodeID
	for (size_t i = 0; i < allnodes.size(); i++) {
		if (!allnodes[i]){
			SANDBOX_ASSERT(false);
			continue;
		}
		allnodes[i]->BindNodeID();
	}

	if (!SyncSerialize::UnserializeNodesInfo(buffer, size))
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	//set parents
	SandboxNode* instance = nullptr;
	for (size_t i = 0; i < allnodes.size(); i++) {
		instance = allnodes[i].get();
		if (instance && instance->IsSyncReceiveable()) {
			instance->LoadReflexStart();
			instance->SetParent(allparents[i]);
			instance->LoadReflexEnd();
		}
	}
	return true;
}

#endif // SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX

int ClientActor::loadPBActorCommon(const game::common::PB_ActorCommon &actorCommon)
{
	OPTICK_EVENT();
	/* 一键云服为实现精细控制将此处逻辑实现复制了一份在ClientPlayer下, 如果此处变动请注意是否需要同步 2021.12.24 by 黄林 */
	if (getLocoMotion() == NULL) return -10;
	if (!actorCommon.has_motion()) return -20;
	SetObjId(actorCommon.wid());
	//m_ObjId = actorCommon.wid();
	const PB_Vector3 &pbpos = actorCommon.pos();
	const WCoord pos(pbpos.x(), pbpos.y(), pbpos.z());
	getLocoMotion()->gotoPosition(pos, actorCommon.yaw() / 1000.0, actorCommon.pitch() / 1000.0);
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(actorCommon.falldist());
	}
	const PB_Vector3 &motion = actorCommon.motion();
	getLocoMotion()->m_Motion = Vector3f(motion.x() / 1000.0, motion.y() / 1000.0, motion.z() / 1000.0);
	resetAllFlags(actorCommon.flags());
	m_LiveTicks = actorCommon.liveticks();
	m_llMasterObjId = actorCommon.masterobjid();

	m_ServerPosCmp = getLocoMotion()->getPosition();
	m_ServerYawCmp = (int)(getLocoMotion()->m_RotateYaw + 360);
	m_ServerPitchCmp = (int)(getLocoMotion()->m_RotationPitch + 360);
	ActorLocoMotion::CheckMotionValid(getLocoMotion()->m_Motion);
	if (actorCommon.sawtooth_size() > 0)
	{
		auto thornComponent = sureThornBallComponent();
		if (thornComponent)
		{
			auto sawtooh = actorCommon.sawtooth();
			for (int i = 0; i < sawtooh.size(); i++)
			{
				int id = sawtooh.Get(i).sawtoothid();
				const PB_Vector3& pos = sawtooh.Get(i).pos();
				thornComponent->setThornAnchorId(id, Rainbow::Vector3f(pos.x(), pos.y(), pos.z()));
			}
			thornComponent->createThornBall();
		}
	}
	//同步生物不可攻击等属性
	const PB_ActorAttInfo& attrinfo = actorCommon.attinfo();
	if (attrinfo.has_settingatt() && attrinfo.settingatt() != ENABLE_INITVALUE)
	{
		auto ActionAttrStateComp = getActionAttrStateComponent();
		if (ActionAttrStateComp ) {
			ActionAttrStateComp->setAllActionAttrState(attrinfo.settingatt());
		}
	}

	ActorLiving *living = dynamic_cast<ActorLiving *>(this);
	if (living)
		living->setTeam(actorCommon.teamid());
	//added by dongjianan 2023.8.14
	//desc:对子节点的网络反序列化处理
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if (actorCommon.has_sandboxnodes())
	{
		const std::string sandboxdata = actorCommon.sandboxnodes();
		AutoRef<Stream> InStream = SANDBOX_NEW(StreamBuffer);
		if (InStream->LoadFromBinary(CustomBuffer::CreateStatic((void*)sandboxdata.c_str(), sandboxdata.size())) == SANDBOXERR::OK)
		{
			//SANDBOXERR result = ParseFromStream(InStream);
			if (!UnserializeForSyncFromStream(InStream))
			{
				SANDBOX_WARNINGEX(false, ToString("net parse sandboxnodes failed!"));
			}
		}
	}
#endif //SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	return 0;
}
flatbuffers::Offset<FBSave::ActorCommon> ClientActor::saveActorCommon(SAVE_BUFFER_BUILDER &builder)
{
	ActorLocoMotion *loc = getLocoMotion();
	auto pos = loc ? WCoordToCoord3(loc->m_Position) : FBSave::Coord3(0 , -1, 0);
	auto motion = loc? FBSave::Vec3(loc->m_Motion.x, loc->m_Motion.y, loc->m_Motion.z): FBSave::Vec3(0, 0, 0);

	flatbuffers::Offset<FBSave::ActorAttInfo> attinfo;
	bool bTrySave = true;
#ifdef IWORLD_SERVER_BUILD
	if (isPlayer()) {
		// 一键云服模式下的[玩家actor]使用 ArchiveManager::getNeedSyncArchive 来判断是否保存
		SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
		if (sandboxResult.IsExecSuccessed())
		{
			bTrySave = sandboxResult.GetData_Bool();
		}
	}
#endif
	bool bNeedSave = false;
	int8_t firstSearch = 0;
	if (bTrySave) {
		ActorAttrib* pActorAttrib = getAttrib();
		if (pActorAttrib) {
			float maxhp = -1.0f;
			if (hasAttChanged(1)) {
				maxhp = pActorAttrib->getMaxHP();
				bNeedSave = true;
			}

			float hprecover = -1.0f;
			if (pActorAttrib->getHPRecover() > 0.0f) {
				hprecover = pActorAttrib->getHPRecover();
				bNeedSave = true;
			}

			float walkspeed = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Walk_Speed) > 0.0f) {
				walkspeed = pActorAttrib->getSpeedAtt(Actor_Walk_Speed);
				bNeedSave = true;
			}

			float swimspeed = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Swim_Speed) > 0.0f) {
				swimspeed = pActorAttrib->getSpeedAtt(Actor_Swim_Speed);
				bNeedSave = true;
			}

			float jumppower = -1.0f;
			if (pActorAttrib->getSpeedAtt(Actor_Jump_Speed) > 0.0f) {
				jumppower = pActorAttrib->getSpeedAtt(Actor_Jump_Speed);
				bNeedSave = true;
			}

			int attacktype = pActorAttrib->getAttackType(-1);
			if (attacktype >= 0) {
				bNeedSave = true;
			}

			float immunetype = 0;
			if (pActorAttrib->getImmuneType() > 0) {
				immunetype = (float)pActorAttrib->getImmuneType();
				bNeedSave = true;
			}

			float punchattack = -1.0f;
			float rangeattack = -1.0f;
			float punchdefense = -1.0f;
			float rangedefense = -1.0f;
			int8_t Unsighted = 0;
			if(pActorAttrib->isUnsighted())
			{
				Unsighted = 1;
			}
			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
			if (pLivingAttrib) {
				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, true) > 0.0f) {
					punchattack = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, true);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, true) > 0.0f) {
					rangeattack = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, true);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, false) > 0.0f) {
					punchdefense = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_PUNCH, false);
					bNeedSave = true;
				}

				if (pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, false) > 0.0f) {
					rangedefense = pLivingAttrib->getAttackAndDefenseBase(ATTACK_TYPE::ATTACK_RANGE, false);
					bNeedSave = true;
				}
				if (pLivingAttrib->getFirstSearch())
				{
					firstSearch = 1;
				}
			}

			int dodge = -1;
			if (hasAttChanged(2)) {
				dodge = getAiInvulnerableProb();
				bNeedSave = true;
			}

			auto ActionAttrStateComp = getActionAttrStateComponent();
			if (ActionAttrStateComp && ActionAttrStateComp->getAllActionAttrState() != ENABLE_INITVALUE) {
				bNeedSave = true;
			}

			if (bNeedSave) {
				auto ActionAttrState = ActionAttrStateComp ? ActionAttrStateComp->getAllActionAttrState() : 0;
				attinfo = FBSave::CreateActorAttInfo(
					builder, 
					maxhp, 
					hprecover, 
					walkspeed, 
					swimspeed, 
					jumppower, 
					punchattack, 
					rangeattack,
					punchdefense, 
					rangedefense, 
					dodge, 
					attacktype, 
					(int32_t)immunetype, 
					ActionAttrState,
					Unsighted);
			}
		}
	}
	
	std::vector<flatbuffers::Offset<FBSave::SawtoothInfo>> sawtoothDatas;
	auto thornComponent = getThornBallComponent();
	if (thornComponent)
	{
		for (int i = 0; i < thornComponent->getThornAnchorNum(); ++i)
		{
			auto info = thornComponent->getThornAnchorAt(i);
			auto temp = FBSave::Vec3(info.pos.x, info.pos.y, info.pos.z);
			sawtoothDatas.push_back(FBSave::CreateSawtoothInfo(builder, info.anchorId, &temp));
		}
	}
	auto sawtoothInfo = builder.CreateVector(sawtoothDatas);
	auto functionWrapper = m_pComponentFunc;
	float FallDistance = functionWrapper ? functionWrapper->getFallDistance() : 0.0;

	//added by dongjianan 2023.8.11
	//desc:对子节点的序列化处理
	flatbuffers::Offset<flatbuffers::String> Data = 0;
	
	//!!! 有子节点扩展的  才认为需要存档新节点部分   (避免无效冗余节点信息)
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if(NeedSupportExSandboxsInfo()) {

		AutoRef<Stream> OutStream = SANDBOX_NEW(StreamBuffer);
		bool serializeSuccess = false;
		if (s_SaveActorCommonForSyncFlag) {
			serializeSuccess = SerializeForSyncToStream(OutStream);
		}
		else {
			serializeSuccess = SerializeToStream(OutStream);
		}
		if (serializeSuccess)
		{
			AutoRef<CustomBuffer> CustomBuffer;
			FlagsInt& streamHeader = OutStream->GetHeadFlag();//标记位区分 存档/同步
			if (s_SaveActorCommonForSyncFlag) {
				streamHeader.SetFlag((unsigned)MNSandbox::Stream::SAVEFLAG::ACTOR_SYNC, true);//flag for sync
			}
			else {
				streamHeader.SetFlag((unsigned)MNSandbox::Stream::SAVEFLAG::ACTOR_SYNC, false);//flag for archive
			}
			if (OutStream->SaveToBinary(CustomBuffer, true))
			{
				Data = builder.CreateString(CustomBuffer->Data<const char>(), CustomBuffer->Size());
			}
		}
	}
#endif//SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	auto RotatorYaw = loc ? loc->m_RotateYaw : 0.f;
	auto RotatorPitch = loc ? loc->m_RotationPitch : 0.f;

	auto pTransform = GetTransform();
	if (pTransform && pTransform->GetParent())
	{
		auto rot = GetLocalRotationEuler();
		RotatorYaw = rot.y;
		RotatorPitch = rot.x;

		auto position = GetLocalPosition();
		pos = FBSave::Coord3(position.x, position.y, position.z);
	}

	return FBSave::CreateActorCommon(builder, m_ObjId, &pos, &motion, RotatorYaw, RotatorPitch, FallDistance, m_Flags, m_FlagsEx, m_LiveTicks, bNeedSave ? attinfo : 0,m_llMasterObjId, sawtoothInfo, Data, firstSearch);
}

extern void CheckMotionValid(Vector3f &motion);
bool ClientActor::loadActorCommon(const FBSave::ActorCommon *srcdata)
{
	if(srcdata == NULL) return false;
	if(getLocoMotion() == NULL) return false;
	if(srcdata->motion() == NULL) return false;

	SetObjId(srcdata->wid());
	
	WCoord pos = Coord3ToWCoord(srcdata->pos());
	getLocoMotion()->gotoPosition(pos, srcdata->yaw(), srcdata->pitch());
	auto functionWrapper = m_pComponentFunc;
	if (functionWrapper)
	{
		functionWrapper->setFallDistance(srcdata->falldist());
	}
	getLocoMotion()->m_Motion = Vector3f(srcdata->motion()->x(), srcdata->motion()->y(), srcdata->motion()->z());
	resetAllFlags(srcdata->flags());
	m_LiveTicks = srcdata->liveticks();
	m_llMasterObjId = srcdata->masterobjid();
	//if (g_WorldMgr && g_WorldMgr->m_RuleMgr && g_WorldMgr->m_RuleMgr->getRuleOptionVal(GMRULE_SAVEMODE) != 1) {
		auto attinfo = srcdata->attinfo();
		if (attinfo) {
			ActorAttrib* pActorAttrib = getAttrib();
			if (pActorAttrib) {
				if (attinfo->maxhp() > 0.0f)
					pActorAttrib->setMaxHP(attinfo->maxhp());

				if (!(attinfo->hprecover() < 0.0f))
					pActorAttrib->setHPRecover(attinfo->hprecover());

				if (!(attinfo->walkspeed() < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Walk_Speed, attinfo->walkspeed());

				if (!(attinfo->swimspeed() < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Swim_Speed, attinfo->swimspeed());

				if (!(attinfo->jumppower() < 0.0f))
					pActorAttrib->setSpeedAtt(Actor_Jump_Speed, attinfo->jumppower());

				if (attinfo->attacktype() >= 0)
					pActorAttrib->setAttackType(attinfo->attacktype());

				if (attinfo->immunetype() > 0) {
					pActorAttrib->resetImmuneType();
					pActorAttrib->setImmuneType(attinfo->immunetype(), true);
				}
				if (attinfo->unsighted()> 0)
				{
					pActorAttrib->setUnsighted(true);
				}else
				{
					pActorAttrib->setUnsighted(false);
				}
			}

			LivingAttrib* pLivingAttrib = dynamic_cast<LivingAttrib*>(getAttrib());
			if (pLivingAttrib) {
				if (!(attinfo->punchattack() < 0.0f))
					pLivingAttrib->setAttackBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo->punchattack());

				if (!(attinfo->rangeattack() < 0.0f))
					pLivingAttrib->setAttackBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo->rangeattack());

				if (!(attinfo->punchdefense() < 0.0f))
					pLivingAttrib->setArmorBaseLua(ATTACK_TYPE::ATTACK_PUNCH, attinfo->punchdefense());

				if (!(attinfo->rangedefense() < 0.0f))
					pLivingAttrib->setArmorBaseLua(ATTACK_TYPE::ATTACK_RANGE, attinfo->rangedefense());

				if (srcdata->firstsearch() > 0)
				{
					pLivingAttrib->setFirstSearch(true);
				}
				else
				{
					pLivingAttrib->setFirstSearch(false);
				}
			}

			if (attinfo->dodge() >= 0)
				setAiInvulnerableProb(attinfo->dodge());

			if (attinfo->settingatt() != ENABLE_INITVALUE)
			{
				auto ActionAttrStateComp = getActionAttrStateComponent();
				if (ActionAttrStateComp)
				{
					ActionAttrStateComp->setAllActionAttrState(attinfo->settingatt());// 存档用的是getAllActionAttrState 恢复存档
				}
			}

		}
	//}
	m_ServerPosCmp = getLocoMotion()->getPosition();
	m_ServerYawCmp = (int)(getLocoMotion()->m_RotateYaw + 360);
	m_ServerPitchCmp = (int)(getLocoMotion()->m_RotationPitch + 360);
	ActorLocoMotion::CheckMotionValid(getLocoMotion()->m_Motion);
	
	auto sawtooth = srcdata->sawtooth();
	if (sawtooth && sawtooth->size() > 0)
	{
		auto thornComponent = sureThornBallComponent();
		if (thornComponent)
		{
			for (int i = 0; i < sawtooth->size(); i++)
			{
				int id = sawtooth->Get(i)->sawtoothid();
				auto pos = sawtooth->Get(i)->pos();
				thornComponent->setThornAnchorId(id, Rainbow::Vector3f(pos ? pos->x() : 0, pos ? pos->y() : 0, pos ? pos->z() : 0));
			}
			thornComponent->createThornBall();
		}
	}

	//added by dongjianan 2023.8.11
	//desc:对子节点的反序列化处理
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	if (const flatbuffers::String* Data = srcdata->sandboxnodes())
	{
		AutoRef<Stream> InStream = SANDBOX_NEW(StreamBuffer);
		if (InStream->LoadFromBinary(CustomBuffer::CreateStatic((void*)Data->c_str(), Data->size())) == SANDBOXERR::OK)
		{		
			FlagsInt& streamHeader = InStream->GetHeadFlag();//标记位区分 存档/同步
			if (streamHeader.CheckFlag((unsigned)MNSandbox::Stream::SAVEFLAG::ACTOR_SYNC)) {//同步
				return UnserializeForSyncFromStream(InStream);
			}
			else {
				SANDBOXERR result = ParseFromStream(InStream);
				if (result != SANDBOXERR::OK)
				{
					SANDBOX_WARNINGEX(false, ToString("load sandboxnodes failed! errorid=", (int)result));
					return false;
				}
			}
		}
	}
#endif //SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	return true;
}

int ClientActor::getMass()
{
	return 60000;
}

void ClientActor::CreateNavPath()
{
	if (!getNavigator())
		CreateComponent<NavigationPath>("NavigationPath");
}

void ClientActor::tryMoveToActor(long long targetobj, float speed)
{
	ClientActor *pTarget = getActorMgr()->findActorByWID(targetobj);
	if (pTarget && getNavigator())
	{
		getNavigator()->tryMoveTo(pTarget, speed, 10000);	// 默认寻路100格
	}
}

void ClientActor::tryMoveToPos(int x, int y, int z, float speed)
{
	if (getNavigator())
	{
		getNavigator()->tryMoveTo(x, y, z, speed, 10000);	// 默认寻路100格
	}
}

void ClientActor::tryMoveTo(int x, int y, int z, float speed, bool canControl, bool needSync/* = false*/, bool showtip)
{
	if (!getNavigator())
		CreateNavPath();

	bool  bshow = showtip;
	if (this->getObjType() != OBJ_TYPE_ROLE)
	{
		bshow = false;
	}  //非玩家生物不显示提示

	if (getNavigator()->tryMoveTo(x, y, z, speed, -1, -1, bshow))
	{
		setCanControl(canControl);
	}
}

bool ClientActor::TryGetPathToPos(int x, int y, int z, int boundSize)
{
	WCoord curDim = CoordDivBlock(getPosition());
	WCoord tagertPos = WCoord(x, y, z);
	WCoord dim = CoordDivBlock(tagertPos);
	int curDist = curDim.squareDistanceTo(dim);
	//已在目标位置
	if (curDist <= boundSize * boundSize + 2)
	{
		return true;
	}

	if (!getNavigator())
	{
		CreateNavPath();
	}
		
	if (canNavigation())
	{
		// 默认寻路100格
		PathEntity* pPathEntity = getNavigator()->getPathTo(x, y, z, 10000);
		if (pPathEntity && pPathEntity->getFinalPathPoint())
		{
			PathPoint* point = pPathEntity->getFinalPathPoint();
			WCoord fPos = point->block;
			int dist = dim.squareDistanceTo(fPos);
			if (dist <= (boundSize * boundSize) + 2)
			{
				ENG_DELETE(pPathEntity);
				return true;
			}
		}


		ENG_DELETE(pPathEntity);
	}

	return false;
}

bool ClientActor::IsAbleGetPath(int x, int y, int z, int boundSize)//去除在受重力等情况下，是否能够寻找到 x y z点路线
{
	WCoord curDim = CoordDivBlock(getPosition());
	WCoord tagertPos = WCoord(x, y, z);
	WCoord dim = CoordDivBlock(tagertPos);
	int bount = getLocoMotion()->m_BoundSize / BLOCK_SIZE;
	if ((bount - 2) > 0)
	{
		boundSize = bount;
	}
	else
	{
		boundSize = 2;
	}
	int curDist = curDim.squareDistanceTo(dim);
	//已在目标位置
	if (curDist <= boundSize * boundSize)
	{
		return true;
	}

	if (!getNavigator())
	{
		CreateNavPath();
	}
	//LARGE_INTEGER frequency;
	//QueryPerformanceFrequency(&frequency);

	//// 获取当前性能计数器的值
	//LARGE_INTEGER start;
	//QueryPerformanceCounter(&start);
	// 默认寻路100格
	PathEntity* pPathEntity = getNavigator()->getActorGetPathTo(x, y, z, 10000);
	if (pPathEntity && pPathEntity->getFinalPathPoint())
	{
		PathPoint* point = pPathEntity->getFinalPathPoint();
		WCoord fPos = point->block;
		int dist = dim.squareDistanceTo(fPos);
		 
		if (dist <= (boundSize * boundSize))
		{
			ENG_DELETE(pPathEntity);
			//LARGE_INTEGER end;
			//QueryPerformanceCounter(&end);

			//// 计算经过的时间，单位为微秒
			//double elapsed_microseconds = double(end.QuadPart - start.QuadPart) * 1000000.0 / frequency.QuadPart;
			//LOG_PRIVATE("hx IsAbleGetPath lent %lf", elapsed_microseconds);
			return true;
		}
	}

	ENG_DELETE(pPathEntity);
	//LARGE_INTEGER end;
	//QueryPerformanceCounter(&end);

	//// 计算经过的时间，单位为微秒
	//double elapsed_microseconds = double(end.QuadPart - start.QuadPart) * 1000000.0 / frequency.QuadPart;

	//// 输出经过的时间
	//LOG_PRIVATE("hx IsAbleGetPath lent %lf", elapsed_microseconds);
	return false;
}


void ClientActor::SetAIJumping(bool b)
{
	if (getLocoMotion())
	{
		getLocoMotion()->setAIJumping(b);
	}
}

bool ClientActor::isExistInCollide(const WCoord& blockpos)
{
	//for (int i = 0; i< (int)m_CollideBlockPos.size(); i++)
	//{
	//	if (m_CollideBlockPos[i] == blockpos)
	//	{
	//		return true;
	//	}
	//}
	//return false;

	auto it = m_CollideBlockPos.find(blockpos);
	return (it != m_CollideBlockPos.end());
}

void ClientActor::setCollideBlockState(const WCoord& blockpos, int blockid)
{
	m_CollideBlockPos.emplace(blockpos);
	//m_CollideBlockPos.push_back(blockpos);
	//m_CollideBlockID.push_back(blockid);
}

void ClientActor::clearCollideBlock()
{
	m_CollideBlockPos.clear();
	//m_CollideBlockID.clear();
}

void ClientActor::setFallGround(bool b)
{
	m_IsFallGround = b;
}

bool ClientActor::isVehicleController()
{
	bool isController = false;
	auto ridComp = getRiddenComponent();
	if (!ridComp) return false;
	ClientActor* actor = ridComp->getRidingActor();
	ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
	if (vehicle)
	{
		WORLD_ID id = vehicle->getRiddenByActorID();
		if (id > 0 && id == this->m_ObjId)
		{
			isController = true;
		}
		else
		{
			WCoord pos = vehicle->getRiddenBindSeatPos(this);
			if (vehicle->getVehicleWorld() && vehicle->getVehicleWorld()->getContainerMgr())
			{
				ContainerDriverSeat* container = dynamic_cast<ContainerDriverSeat*>(vehicle->getVehicleWorld()->getContainerMgr()->getContainer(pos));
				if (container && container->m_BindKeyData.size() > 1)
				{
					isController = true;
				}
			}
		}

	}

	return isController;
}


bool ClientActor::getFallGround()
{
	return m_IsFallGround;
}

void ClientActor::playEffect(ACTORBODY_EFFECT fx)
{
	if (nullptr == getBody() || !getBody()->getEntity())
	{
		return;
	}

	if (fx == BODYFX_HURT || fx == BODYFX_EXPLODE_HURT)
	{
		ClientMob* mob = nullptr;
		if (getObjType() == OBJ_TYPE_MONSTER)
		{
			mob = static_cast<ClientMob*>(this);
		}

		if (mob &&
			ModPackMgr::GetInstancePtr()->IsModBeHit(mob->getMonsterId()))
		{
			mob->PlayModBeHit(fx == BODYFX_EXPLODE_HURT);
		}
		else
		{
#ifdef IWORLD_SERVER_BUILD
			playAnim(SEQ_BEHIT);
#else
			ColourValue color(0.5f, 0, 0);
			getBody()->getEntity()->SetOverlayColor(&color);
			if (!m_BehitDisable)
			{
				playAnim(SEQ_BEHIT);
			}
#endif
			getBody()->setHurtTick(10);
		}
	}
	else if (fx == TOOLFX_JETPACK2)
	{
		if (getBody()->getDorsumEntity()!= NULL)
		{
			getBody()->getDorsumEntity()->PlayMotion("item_12253_1");
			getWorld()->getEffectMgr()->playSound(getPosition(), "misc.jetpack_2", 1.0f, 1.5f);
		}
	}
	else if (fx == BODYFX_ENCH_FALL)
	{
		getBody()->getEntity()->PlayMotion("ench_fall", false);
		if (getWorld())
		{
			getWorld()->getEffectMgr()->playSound(getPosition(), "misc.ench_fall", 1.0f, 1.5f);
		}
		else if (fx == BODYFX_DISAPPEAR)
		{
			getBody()->getEntity()->PlayMotion("monster_escape_1");
			getBody()->playAnim(SEQ_DISAPPEAR);
		}
	}
	else
	{
		auto item = localBodyFxName.find(fx);
		if( item != localBodyFxName.end())
		{
			auto& tuleValue = item->second;
			getBody()->playEffect(std::get<0>(tuleValue).c_str(), std::get<1>(tuleValue), std::get<2>(tuleValue));
		}
	}
}

void ClientActor::stopEffect(ACTORBODY_EFFECT fx)
{
	if (fx == BODYFX_HURT)
	{
		if (getBody()->getEntity())
		{
			getBody()->getEntity()->SetOverlayColor(nullptr);
		}
#ifndef IWORLD_SERVER_BUILD
		if (getBody()->getModel() && getBody()->getBodyColor() != 0)
		{
			getBody()->applyBodyColor(getBody()->getBodyColor(), false);
		}
#endif
	}
	if (nullptr == getBody()->getEntity())
	{
		return;
	}
	if (fx == TOOLFX_JETPACK2)
	{
		if (getBody()->getDorsumEntity() != NULL)
		{
			getBody()->getDorsumEntity()->StopMotion("item_12253_1");
			getWorld()->getEffectMgr()->playSound(getPosition(), "misc.jetpack_3", 1.0f, 1.5f);
		}
	}
	else
	{
		auto item = localBodyFxName.find(fx);
		if (item != localBodyFxName.end())
		{
			auto& tuleValue = item->second;
			getBody()->getEntity()->StopMotion(std::get<0>(tuleValue).c_str());
		}
	}
}

void ClientActor::changeBaseModel(std::string& strModelId, float fScale)
{
	return;
}

ClientActor* ClientActor::clone()
{
	return NULL;
}

void ClientActor::getsyncData(jsonxx::Object &data)
{
	ActorAttrib* actorAttr = getAttrib();
	if (actorAttr)
	{
		data<<"hp"<<actorAttr->getHP();
		data<<"behurttarget"<<m_BeHurtTarget;
	}
	data<<"reverse"<<getReverse();
	ActorBody *body = getBody();
	if (body)
	{
		data<<"anim"<<body->getCurAnim(0);
		data<<"anim1"<<body->getCurAnim(1);
		data<<"actid"<<body->getActID();
		if (getCustomScale() > 0.0001)
		{
			data<<"customscale"<<getCustomScale();
		}
		data<<"actidtrugger"<<body->getActTriggerID();
		data << "animSeq" << body->getAnimSeq();
		data << "seatact" << body->isSideAct();
	}

	ActorLocoMotion *locmove = getLocoMotion();
	WCoord curpos = locmove ? locmove->getPosition() : WCoord(0, 0, 0);
	unsigned int curpitch, curyaw;
	Rainbow::Quaternionf curquat;
	bool rot_enough = false;

	bool full_rot = locmove ? locmove->needFullRotation() : false;
	if (locmove)
	{
		if(full_rot)
		{
			locmove->getRotation(curquat);
			data<<"pitch"<<10000;
			data<<"yaw"<<curquat.ToUInt32();
		}
		else
		{
			curpitch = AngleFloat2Char(locmove->m_RotationPitch);
			curyaw = AngleFloat2Char(locmove->m_RotateYaw);
			data<<"pitch"<<curpitch;
			data<<"yaw"<<curyaw;
		}
		data<<"changeflags"<<(locmove->m_OnGround ? 8 : 0);
		data<<"mapid"<<(getCurMapID() + 1);
		data<<"x"<<curpos.x;
		data<<"y"<<curpos.y;
		data<<"z"<<curpos.z;
	}
}

void ClientActor::setsyncData(jsonxx::Object &data)
{
	ActorBody *body = getBody();
	if(body)
	{
		if (isBoomerangItem(GetItemId()))
		{
			ClientActorProjectile *projectile = static_cast<ClientActorProjectile *>(this);
			if (projectile)
			{
				if (data.has<jsonxx::Number>("anim"))
				{
					projectile->stopEntityModelAnim();
					projectile->playEntityModelAnim(data.get<jsonxx::Number>("anim"), 0);
				}
			}
			return;
		}
		if ((data.has<jsonxx::Number>("actid") && data.get<jsonxx::Number>("actid") != body->getActID() && body->getActID() > 0)
			|| (data.has<jsonxx::Number>("actidtrugger") && data.get<jsonxx::Number>("actidtrugger") != body->getActTriggerID() && body->getActTriggerID() > 0)
			|| ( data.has<jsonxx::Number>("animSeq") && data.get<jsonxx::Number>("animSeq") != body->getAnimSeq()))
		{
			body->setCurAnim(-1, 0);
			stopMotion(30000);
			body->stopAnim(SEQ_PLAY_ACT);
		}
		if (data.has<jsonxx::Number>("actid") && data.get<jsonxx::Number>("actid") >= 0 && data.get<jsonxx::Number>("actid") != body->getActID())
		{
			auto def = GetDefManagerProxy()->getPlayActDef(data.get<jsonxx::Number>("actid"));
			if (def)
			{
				//20210929 codeby:chenwei 新增装扮互动判定分支
				if (def->SkinID > 0 && def->SkinID2)//20210921 codeby:chenwei 播放装扮互动特效
				{
					body->playSkinActMotion(def->ID, 30000);
				}
				else
				{
					stopMotion(30000);
					playMotion(def->Effect.c_str(), 30000);
				}
			}
		}
		else if (data.has<jsonxx::Number>("actidtrugger") && data.get<jsonxx::Number>("actidtrugger") >= 0 && data.get<jsonxx::Number>("actidtrugger") != body->getActTriggerID())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(data.get<jsonxx::Number>("actidtrugger"));
			if (def)
			{
				stopMotion(30000);
				playMotion(def->Effect.c_str(), 30000);
			}
		}
		if (data.has<jsonxx::Boolean>("seatact"))
		{
			body->setSideAct(data.get<jsonxx::Boolean>("seatact"));
		}
		if (data.has<jsonxx::Number>("actid") )
		{
			body->setAct(data.get<jsonxx::Number>("actid") );
		}
		if (data.has<jsonxx::Number>("actidtrugger"))
		{
			body->setActTrigger(data.get<jsonxx::Number>("actidtrugger"));
		}
		if (data.has<jsonxx::Number>("animSeq"))
		{
			body->setAnimSeq(data.get<jsonxx::Number>("animSeq"));
		}
		if (data.has<jsonxx::Number>("customscale"))
		{
			setCustomScale(data.get<jsonxx::Number>("customscale"));
		}
		if (data.has<jsonxx::Number>("anim1") && data.get<jsonxx::Number>("anim1") == 127)
		{
			if (data.has<jsonxx::Number>("anim"))
				body->playAnim(data.get<jsonxx::Number>("anim"));
		}
		else
		{
			if (data.has<jsonxx::Number>("anim")) 
				body->setCurAnim(data.get<jsonxx::Number>("anim"), 0);
			if (data.has<jsonxx::Number>("anim1"))
				body->setCurAnim(data.get<jsonxx::Number>("anim1"), 1);
		}
		if (data.has<jsonxx::Number>("anim"))
		{
			int anim = data.get<jsonxx::Number>("anim");//客机看主机 魔炎变形还原 名字高度;
			if (anim == SEQ_RE_SHAPE_SHIFT && body->getIsNeedRecoverShapeHeight())
			{
				body->revoverShapeHeight();
			}
		}
	}
	bool change = false;
	WCoord pos = m_ServerPosCmp;
	unsigned int yaw = m_ServerYawCmp;
	unsigned int pitch = m_ServerPitchCmp;

	if (data.has<jsonxx::Number>("yaw"))
	{
		if(getLocoMotion() && getLocoMotion()->needFullRotation())
		{
			yaw = data.get<jsonxx::Number>("yaw");
		}
		else
		{
			yaw = AngleChar2Float(data.get<jsonxx::Number>("yaw"));
		}
		change = true;
	}
	if (data.has<jsonxx::Number>("pitch"))
	{
		if(getLocoMotion() && getLocoMotion()->needFullRotation())
		{
			pitch = data.get<jsonxx::Number>("pitch");
		}
		else
		{
			pitch = AngleChar2Float(data.get<jsonxx::Number>("pitch"));
		}
		change = true;
	}
	if (data.has<jsonxx::Number>("x"))
	{
		pos.x = data.get<jsonxx::Number>("x");
		change = true;
	}
	if (data.has<jsonxx::Number>("y"))
	{
		pos.y = data.get<jsonxx::Number>("y");
		change = true;
	}
	if (data.has<jsonxx::Number>("z"))
	{
		pos.z = data.get<jsonxx::Number>("z");
		change = true;
	}
	if (change)
	{
		m_ServerPosCmp = pos;
		m_ServerYawCmp = yaw;
		m_ServerPitchCmp = pitch;
		if(getLocoMotion() && getLocoMotion()->needFullRotation())
		{
			Rainbow::Quaternionf rot;
			//rot.decompressFromInt(yaw);
			rot.FromUInt32(yaw);
			moveToPosition(pos, rot, 3);
		}
		else
		{
			moveToPosition(pos, yaw, pitch, 3);
		}
	}
	if (data.has<jsonxx::Number>("changeflags"))
	{
		int changeflags = data.get<jsonxx::Number>("changeflags");
		bool onground = (changeflags & 8) != 0;
		ActorLocoMotion *locmove = getLocoMotion();
		if (locmove) locmove->setOnGround(onground);
	}
	if (data.has<jsonxx::Number>("behurttarget"))
	{
		m_BeHurtTarget = data.get<jsonxx::Number>("behurttarget");
	}
	if (data.has<jsonxx::Number>("hp"))
	{
		float dhp = data.get<jsonxx::Number>("hp") - getAttrib()->getHP();
		if (data.get<jsonxx::Number>("hp") == 0) dhp -= 0.1f; //避免浮点误差: dhp + curhp != attr.HP
		getAttrib()->addHP(dhp);
	}
	if (data.has<jsonxx::Number>("reverse"))
	{
		m_bReverse = data.get<jsonxx::Number>("reverse");
	}
}

flatbuffers::Offset<FBSave::SectionActor> ClientActor::saveSectionActor(SAVE_BUFFER_BUILDER &builder, FBSave::SectionActorUnion actor_type, flatbuffers::Offset<void> actor)
{
	return FBSave::CreateSectionActor(builder, actor_type, actor, m_productName.length() > 0 ? builder.CreateString(m_productName) : 0);
}

flatbuffers::Offset<FBSave::ActorObj> ClientActor::SaveActor(SAVE_BUFFER_BUILDER& builder)
{
	OPTICK_EVENT();

	auto basedata = saveActorCommon(builder);
	auto RotatorRoll = m_pComponentLocoMotion ? m_pComponentLocoMotion->m_RotateRoll : 0.f;

	auto pTransform = GetTransform();
	if (pTransform && pTransform->GetParent())
	{
		auto rot = GetLocalRotationEuler();
		RotatorRoll = rot.z;
	}

	string modelPath;
	int modelType = 0;
	int extradata = 0;
	auto localScale = GetLocalScale();
	auto scale = FBSave::Vec3(localScale.x, localScale.y, localScale.z);
	if (m_Body)
	{
		modelPath = m_Body->getModelPath();
		modelType = m_Body->getModelType();
		extradata = m_Body->getExtraData();
	}
	else if(GetMeshRender())
	{
		ModelParamData paramData;
		m_pModelComponent->GetModelParam(paramData);
		modelPath = paramData.modelPath;
		modelType = paramData.type;
		extradata = paramData.extraData;
	}

	string scriptComponent = "";
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> childrenOffset = 0;
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> ugccomponentsOffset = 0;
	SaveObjectChildAndComponent(builder, scriptComponent, ugccomponentsOffset, childrenOffset);
	
	flatbuffers::Offset<flatbuffers::Vector<int8_t>> modelOffset = 0;
	if (!m_bDelayInit)
	{
		if (m_modelcomBin)
		{
			modelOffset = builder.CreateVector((int8_t*)m_modelcomBin, m_modelcomLen);
		}
	}
	else
	{
		/*jsonxx::Object jsonObj;
		if (m_pModelComponent)
		{
			//m_pModelComponent->SerializeModelKV(nullptr, jsonObj);
			//m_pModelComponent->CreateComponentData(jsonObj);
		}
		if (jsonObj.binLen() == 0)
		{
			modelOffset = 0;
		}
		else
		{
			modelOffset = builder.CreateVector((int8_t*)jsonObj.bin(), jsonObj.binLen());
		}*/
	}
	
	long long parentWID = m_ParentWID;
	if (m_ParentWID == 0 && m_pWorld && m_pWorld->GetWorldMgr() &&
		(m_pWorld->GetWorldMgr()->isUGCEditMode() || m_pWorld->GetWorldMgr()->isUGCEditBuildMode()) &&
		m_IsInPrefabScene) //区分是否在prefab scene，这个值默认为false
	{
		parentWID = -1;
	}

	return FBSave::CreateActorObj(builder, basedata, builder.CreateString(modelPath), modelType, extradata, &scale, m_bCanBeInteracted, 
									builder.CreateString(scriptComponent), m_bParent, parentWID, RotatorRoll,
									childrenOffset, modelOffset, ugccomponentsOffset);
}

void ClientActor::SaveObjectChildAndComponent(SAVE_BUFFER_BUILDER& builder, std::string& scriptComponent, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& ugccomponentsOffset, flatbuffers::Offset<flatbuffers::Vector<int8_t>>& childrenOffset)
{
	if (!m_bDelayInit && m_scriptcomBin)
	{
		scriptComponent.assign(m_scriptcomBin, m_scriptcomLen);
	}
	else
	{
		ScriptComponent* pScriptComponent = getScriptComponent();
		if (pScriptComponent)
		{
			pScriptComponent->OnSave(scriptComponent);
			if (scriptComponent.empty())
			{
				scriptComponent = "";
			}
		}
	}

	if (!m_bDelayInit && m_childBin)
	{
		childrenOffset = builder.CreateVector((int8_t*)m_childBin, m_childLen);
	}
	else
	{
		SAVE_BUFFER_BUILDER childrenBuilder;
		if (GetChildrenCount() > 0)
		{
			std::vector<flatbuffers::Offset<FBSave::ActorObj>> actorArray;
			actorArray.clear();
			GetChildrenList().for_each([&](SandboxNode* t) -> void {
				ClientActor* child = dynamic_cast<ClientActor*>(t);
				if (child)
				{
					actorArray.push_back(child->SaveActor(childrenBuilder));
				}
				});
			auto array = FBSave::CreateActorObjArray(childrenBuilder, childrenBuilder.CreateVector(actorArray));
			childrenBuilder.Finish(array);
		}
		if (childrenBuilder.GetSize() > 0)
		{
			childrenOffset = builder.CreateVector((int8_t*)childrenBuilder.GetBufferPointer(), childrenBuilder.GetSize());
		}
		else
		{
			childrenOffset = 0;
		}
	}

	if (!m_bDelayInit && m_ugcComponentBin)
	{
		ugccomponentsOffset = builder.CreateVector((int8_t*)m_ugcComponentBin, m_ugcComponentLen);
	}
	else
	{
		SAVE_BUFFER_BUILDER ugccomponentsBuilder;
		auto& comps = GetAllComponents();
		std::vector<flatbuffers::Offset<FBSave::UgcComponent>> ugccomponentsArray;
		ugccomponentsArray.clear();
		comps.for_each([&ugccomponentsArray, &ugccomponentsBuilder](::MNSandbox::Component* comp) -> void {
			ActorComponentBase* compBase = comp->ToCast<ActorComponentBase>();
			const char* sCmpName = compBase ? compBase->GetUgcComponentFactoryName() : nullptr;
			if (sCmpName)
			{
				jsonxx::Object* pComponentData = compBase->GetComponentData();
				if (pComponentData)
				{
					ugccomponentsArray.push_back(FBSave::CreateUgcComponent(ugccomponentsBuilder, ugccomponentsBuilder.CreateString(sCmpName), ugccomponentsBuilder.CreateVector((int8_t*)pComponentData->bin(), pComponentData->binLen())));
				}
				else
				{
					jsonxx::Object componentData;
					compBase->CreateComponentData(componentData);
					ugccomponentsArray.push_back(FBSave::CreateUgcComponent(ugccomponentsBuilder, ugccomponentsBuilder.CreateString(sCmpName), ugccomponentsBuilder.CreateVector((int8_t*)componentData.bin(), componentData.binLen())));
				}
			}
			});
		auto array = FBSave::CreateUgcComponentArray(ugccomponentsBuilder, ugccomponentsBuilder.CreateVector(ugccomponentsArray));
		ugccomponentsBuilder.Finish(array);

		if (ugccomponentsBuilder.GetSize() > 0)
		{
			ugccomponentsOffset = builder.CreateVector((int8_t*)ugccomponentsBuilder.GetBufferPointer(), ugccomponentsBuilder.GetSize());
		}
		else
		{
			ugccomponentsOffset = 0;
		}
	}

	return;
}

flatbuffers::Offset<FBSave::SectionActor> ClientActor::save(SAVE_BUFFER_BUILDER& builder)
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		return 0;
	}

	flatbuffers::Offset<FBSave::ActorObj> actor = SaveActor(builder);
	
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorObj, actor.Union());
}

bool ClientActor::load(const void* srcdata, int version)
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		return true;
	}

	auto src = reinterpret_cast<const FBSave::ActorObj*>(srcdata);

	return LoadActor(src);
}

void ClientActor::legacyLoad(bool &find,bool& ret , const FBSave::SectionActor* s, int version)
{
	find = false;
	ret = false;
	if (s->actor_type() == FBSave::SectionActorUnion::SectionActorUnion_ActorMob)
	{
		ClientFlyMob* pFlyMob = dynamic_cast<ClientFlyMob*>(this);
		auto pClientFlyComponent = this->getClientFlyComponent();
		if (pFlyMob)
		{
			find = true;
			ret = pFlyMob->loadFromClientMobFB(s->actor(), version);
		}
		else if (pClientFlyComponent)
		{
			find = true;
			ret = pClientFlyComponent->loadFromClientMobFB(s->actor(), version);
		}
	}
}

bool ClientActor::LoadActor(const FBSave::ActorObj * actorObj, const ClientActor* pParentActor)
{
	if (actorObj == NULL)
	{
		return false;
	}

	OPTICK_EVENT();

	InitInstance();
	CreateComponent<ActorLocoMotion>("ActorLocoMotion");
	CreateComponent<ActorChunkPos>("ActorChunkPos");

	loadActorCommon(actorObj->basedata());
	//int modelType = actorObj->modeltype();
	//int extraData = actorObj->extradata();

	if (actorObj->basedata() == NULL)
		return false;
	m_ParentWID = actorObj->parentid();

	auto pos = actorObj->basedata()->pos();
	if (pos)
	{
		SetLocalPostion(pos->x(), pos->y(), pos->z());
	}
	
	if (m_ParentWID == -1) //prefab scene特殊处理
	{
		m_IsInPrefabScene = true;
	}
	else if(pParentActor && pParentActor->m_IsInPrefabScene)
	{
		//如果祖先是在预制里面创建的那么孩子也应该是
		m_IsInPrefabScene = true;
	}

	/* 由ModelComponent负责模型，这里不用加载了
	if (actorObj->modelpath())
	{
		InitActor(modelType, actorObj->modelpath()->str(), extraData);
	}*/

	auto scale = actorObj->scale();
	if (scale)
	{
		SetLocalScale(scale->x(), scale->y(), scale->z());
	}
	SetCanBeInteracted(actorObj->interacted());
	m_bParent = actorObj->isparent();
	
	if (m_ParentWID == -1)
	{
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->getPrefabSceneVisible() && g_pPlayerCtrl)
		{
			//enter world 时重新attach到prefab scene			
		}
		else
		{
			return false;
		}
	}

	if (m_pComponentLocoMotion)
	{
		WCoord& pos = m_pComponentLocoMotion->getPosition();
		SetWorldPosition(pos.x, pos.y, pos.z); //有物理可以设置物理的位置
		SetLocalRotationEuler(m_pComponentLocoMotion->m_RotationPitch, m_pComponentLocoMotion->m_RotateYaw, actorObj->roll());
	}

	//这里只是为了兼容以前lua中存model组件数据
	if (actorObj->modelcomponent() && actorObj->modelcomponent()->size() > 0) 
	{
		m_modelcomLen = actorObj->modelcomponent()->size();
		m_modelcomBin = (char*)malloc(m_modelcomLen);
		if (m_modelcomBin) memcpy(m_modelcomBin, actorObj->modelcomponent()->data(), m_modelcomLen);
	}

	LoadObjectChildAndComponent(actorObj->scriptcomponent(), actorObj->ugccomponents(),	actorObj->children());

	if (getBody() && getLocoMotion())
	{
		WCoord& coord = getLocoMotion()->getPosition();
		Vector3f pos = coord.toVector3();
		getBody()->setPosition(pos);
	}
	return true;
}

void ClientActor::LoadObjectChildAndComponent(const flatbuffers::String* scriptcomponent, const flatbuffers::Vector<int8_t>* ugccomponents, const flatbuffers::Vector<int8_t>* children)
{
	m_bEmpty = false;

	m_scriptcomLen = 0;
	if (scriptcomponent)
	{
		m_scriptcomLen = scriptcomponent->size();
		if (m_scriptcomLen > 0)
		{
			m_scriptcomBin = (char*)malloc(m_scriptcomLen);
			if (m_scriptcomBin) memcpy(m_scriptcomBin, scriptcomponent->data(), m_scriptcomLen);
		}
	}

	if (ugccomponents && ugccomponents->size() > 0)
	{
		m_ugcComponentLen = ugccomponents->size();
		m_ugcComponentBin = (char*)malloc(m_ugcComponentLen);
		if (m_ugcComponentBin) memcpy(m_ugcComponentBin, ugccomponents->data(), m_ugcComponentLen);
	}

	if (children && children->size() > 0)
	{
		m_childLen = children->size();
		m_childBin = (char*)malloc(m_childLen);
		if (m_childBin) memcpy(m_childBin, children->data(), m_childLen);
	}
}

bool ClientActor::LoadChildren(char* buf, int bufLen, std::vector<ClientActor*> & children, std::vector<ClientActor*>& childrenFail)
{
	flatbuffers::Verifier verifier((const uint8_t*)buf, bufLen);
	if (!FBSave::VerifyActorObjArrayBuffer(verifier))
	{
		return false;
	}

	const FBSave::ActorObjArray* actorArray = FBSave::GetActorObjArray(buf);
	auto actors = actorArray->actors();
	if (actors)
	{
		for (unsigned int i = 0; i < actors->size(); i++)
		{
			auto child = actors->Get(i);
			
			ClientActor* actor = SANDBOX_NEW(ClientActor, true);
			if (!actor) continue;
			if (actor->LoadActor(child, this))
			{
				children.push_back(actor);
			}
			else
			{
				childrenFail.push_back(actor);
			}
		}
	}

	return true;
}

void ClientActor::setBeHurtTargetID(WORLD_ID n)
{
	m_BeHurtTarget = n;
}

WORLD_ID ClientActor::getBeHurtTargetID()
{
	return m_BeHurtTarget;
}

bool ClientActor::IsTriggerProjectile() // 是否是投掷物
{
	auto objectType = getObjType();
	return ClientActorHelper::isTriggerProjectileType(objectType);
}
bool ClientActor::IsTriggerCreature() // 是否是触发器生物(包含NPC，怪物，动物)
{
	return ClientActorHelper::isTriggerCreatureType(getObjType());
}

Rainbow::Entity* ClientActor::getEntity()
{
	if (m_Body)
	{
		return m_Body->getEntity();
	}

	return NULL;
}
Rainbow::IActorBody* ClientActor::GetIBody()
{
	return getBody();
}
ActorBody *ClientActor::getBody()
{
	return m_Body;
}
ActorLocoMotion *ClientActor::getLocoMotion()
{
	return m_pComponentLocoMotion;
}
const ActorLocoMotion* ClientActor::getLocoMotion() const
{
	return m_pComponentLocoMotion;
}
ActorVision *ClientActor::getVision()
{
	return m_pComponentVision;
}

NavigationPath *ClientActor::getNavigator()
{
	return m_pComponentNaviPath;
}

ActorAttrib *ClientActor::getAttrib()
{
	return m_pComponentAttrib;
}

ClientActorFuncWrapper *ClientActor::getFuncWrapper()
{
	return m_pComponentFunc;
}

IClientActorFuncWrapper* ClientActor::getActorFuncWrapper()
{
	return m_pComponentFunc;
}

RiddenComponent *ClientActor::getRiddenComponent()
{
	return m_pComponentRidden;
}

MNSandbox::Component* ClientActor::getActorAttackedComponent()
{
	return m_pAttackedComponent;
}

AttackedComponent* ClientActor::getAttackedComponent()
{
	return m_pAttackedComponent;
}

FireBurnComponent* ClientActor::getFireBurnComponent()
{
	return m_pComponentFireBurn;
}

RiddenComponent* ClientActor::sureRiddenComponent()
{
	if (!m_pComponentRidden)
	{
		CreateComponent<RiddenComponent>("RiddenComponent");
	}

	return m_pComponentRidden;
}

FireBurnComponent* ClientActor::sureFireBurnComponent()
{
	if (!m_pComponentFireBurn)
	{
		CreateComponent<FireBurnComponent>("FireBurnComponent");
	}

	return m_pComponentFireBurn;
}

CarryComponent* ClientActor::sureCarryComponent()
{
	if (!m_pComponentCarry)
	{
		m_pComponentCarry = CreateComponent<CarryComponent>("CarryComponent");
	}
	return m_pComponentCarry;
}

SwarmComponent* ClientActor::getSwarmComponent() {
	return m_pSwarmComponent;
}

ModelComponent* ClientActor::SureModelComponent()
{
	if (!m_pModelComponent)
	{
		m_pModelComponent = CreateComponent<ModelComponent>("Model");
	}

	return m_pModelComponent;
}
FishingComponent* ClientActor::getFishingComponent()
{
	return nullptr;
}
bool ClientActor::IsOnViewFrustum()
{
	if (getEntity() && getEntity()->GetMainModel())
	{
		AABB actorBounds = this->m_Body->getEntity()->GetMainModel()->GetWorldBounds();
		return GetMiniCraftRenderer().IntersectAABBCameraFrustum(actorBounds);
	}
	return false;
}
void ClientActor::Pause() {
	m_bPause = true;
	// 自身暂停，待确认
	/*
	ActorBody* body = getBody();
	if (body)
	{
		body->SetIsEdit(true);
	}
	//动画停止
	auto temperatureComponent = getTemperatureComponent();
	if (temperatureComponent)
	{
		temperatureComponent->PauseCurrentFrame(true);
	}

	LivingLocoMotion* pLivingLoc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (pLivingLoc && (pLivingLoc->getLocoMotionType() == MoveAbilityType::AquaticLoc || pLivingLoc->getLocoMotionType() == MoveAbilityType::FlyLoc))
	{
		pLivingLoc->m_HasTarget = false;
		pLivingLoc->setBehaviorOff(BehaviorType::Pursuit);
		pLivingLoc->setBehaviorOff(BehaviorType::Wander);
		// 让蝴蝶悬停在空中 or 让鱼停止
		pLivingLoc->m_SpeedMultiple = 0.0f;
	}

	TrixenieLocomotion* trixenieLoc = dynamic_cast<TrixenieLocomotion*>(getLocoMotion());
	if (trixenieLoc)
	{
		// 让三栖生物停止
		trixenieLoc->setCurMoveType(Land_Loc);
	}

	NavigationPath* pNavigationPath = getNavigator();
	if (pNavigationPath)
	{
		pNavigationPath->setPath(NULL, -1);
	}
	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setCanMove(false);
	}
	ClientMob* mob = ToCast<ClientMob>();
	if (mob)
	{
		mob->setAIActive(false);
		mob->setImmuneAttackType(ATTACK_ALL);
	}
	*/

	// 脚本暂停
	//LuaCallHelper::GetInstance().MFuncCall(LuaCallHelper::UGCLuaRT, "OnGameObjectEvent", getObjId(), (int)Resgist_OnPause);
	//Fire((int)Resgist_OnPause);

	// 组合的子节点暂停
	GetChildrenList().for_each([](SandboxNode* t) -> void {
		ClientActor* actor = t->ToCast<ClientActor>();
		if (actor) {
			actor->Pause();
		}
	});
}

void ClientActor::Resume() {
	m_bPause = false;
	/*
	LivingLocoMotion* pLivingLoc = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (pLivingLoc && (pLivingLoc->getLocoMotionType() == MoveAbilityType::AquaticLoc || pLivingLoc->getLocoMotionType() == MoveAbilityType::FlyLoc))
	{
		pLivingLoc->m_HasTarget = false;
		pLivingLoc->setBehaviorOn(BehaviorType::Pursuit);
		pLivingLoc->setBehaviorOn(BehaviorType::Wander);
		pLivingLoc->m_SpeedMultiple = 1.0f;
	}

	ActorBody* body = getBody();
	if (body)
	{
		body->SetIsEdit(false);
	}

	auto temperatureComponent = getTemperatureComponent();
	if (temperatureComponent)
	{
		temperatureComponent->PauseCurrentFrame(false);
	}

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		functionWrapper->setCanMove(true);
	}

	ClientMob* mob = ToCast<ClientMob>(); 
	if (mob)
	{
		mob->setAIActive(true);
	}
	*/

	GetChildrenList().for_each([](SandboxNode* t) -> void {
		ClientActor* actor = t->ToCast<ClientActor>();
		if (actor) {
			actor->Resume();
		}
	});
}

ActorChunkPos* ClientActor::getChunkPosComponent() {
	return m_pChunkPosComponent;
}

void ClientActor::BindLocoMotion(MNSandbox::SceneComponent* pComponent)
{
	if (pComponent && m_pComponentLocoMotion)
	{
		RemoveComponent(m_pComponentLocoMotion);
	}
	m_pComponentLocoMotion = dynamic_cast<ActorLocoMotion*>(pComponent);

	// gcAssetPrefab::Instantiate 还原实体lua 中 transform 部件的旋转时 m_pComponentLocoMotion 还没有创建， 导致
	// ActorBody::updateForModelView 更新不到旋转， 这里设置一下。
	auto pTransform = GetTransform();
	if (pTransform && m_pComponentLocoMotion)
	{
		auto rot = pTransform->GetWorldRotation();
		Vector3f euler = Rainbow::QuaternionToEulerAngle(rot);
		m_pComponentLocoMotion->m_RotationPitch = euler.x;//UpdateRotation(pLoco->m_RotationPitch, euler.x, 180.0f);
		m_pComponentLocoMotion->m_RotateYaw = euler.y;// UpdateRotation(pLoco->m_RotateYaw, euler.y, 180.0f);
		m_pComponentLocoMotion->m_RotateRoll = euler.z;// UpdateRotation(pLoco->m_RotateRoll, euler.z, 180.0f);
		m_pComponentLocoMotion->m_RotateQuat = rot;

		ResetHitBox();
	}
}
void ClientActor::BindVision(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentVision = dynamic_cast<ActorVision*>(pComponent);
}
void ClientActor::BindNavigationPath(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentNaviPath = dynamic_cast<NavigationPath*>(pComponent);
}
void ClientActor::BindAttrib(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentAttrib = dynamic_cast<ActorAttrib*>(pComponent);
}
void ClientActor::BindFuncWrapper(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentFunc = dynamic_cast<ClientActorFuncWrapper*>(pComponent);
}
void ClientActor::BindRiddenComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentRidden = dynamic_cast<RiddenComponent*>(pComponent);
}

void ClientActor::BindFireBurnComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentFireBurn = dynamic_cast<FireBurnComponent*>(pComponent);
}
void ClientActor::BindCarryComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pComponentCarry = dynamic_cast<CarryComponent*>(pComponent);
}

void ClientActor::BindSwarmComponent(MNSandbox::SceneComponent* pComponent) {
	m_pSwarmComponent = dynamic_cast<SwarmComponent*>(pComponent);
}

void ClientActor::BindSoundComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pSoundComponent = dynamic_cast<SoundComponent*>(pComponent);
}

void ClientActor::BindChunkPosComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pChunkPosComponent = dynamic_cast<ActorChunkPos*>(pComponent);
}

void ClientActor::BindAttackedComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pAttackedComponent = dynamic_cast<AttackedComponent*>(pComponent);
}

void ClientActor::BindEffectComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pEffectComponent = dynamic_cast<EffectComponent*>(pComponent);
}

void ClientActor::BindSkillComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pSkillComponent = dynamic_cast<SkillComponent*>(pComponent);
}

void ClientActor::BindActionAttrStateComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pActionAttrStateComponent = dynamic_cast<ActionAttrStateComponent*>(pComponent);
}

void ClientActor::BindPhysicsComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pPhysicsComponent = dynamic_cast<PhysicsComponent*>(pComponent);
}

void ClientActor::BindChargeJumpComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pChargeJumpComponent = dynamic_cast<ChargeJumpComponent*>(pComponent);
}

void ClientActor::BindScriptComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pScriptComponent = dynamic_cast<ScriptComponent*>(pComponent);
}

void ClientActor::BindModelComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pModelComponent = dynamic_cast<ModelComponent*>(pComponent);
}

void ClientActor::BindActorModelLerpComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pModelLerpComponent = dynamic_cast<ActorModelLerpComponent*>(pComponent);
}

void ClientActor::BindActorGridComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pGridContainer = dynamic_cast<GridContainer*>(pComponent);
}
void ClientActor::BindActorEquiptComponent(MNSandbox::SceneComponent* pComponent)
{
	m_pEquipGridContainer = dynamic_cast<EquipGridContainer*>(pComponent);
}

ActorInPortal*  ClientActor::sureActorInPortal()
{
	if (!m_pActorInPortal){
		m_pActorInPortal = CreateComponent<ActorInPortal>("ActorInPortal");
	}
	return m_pActorInPortal;
}

ActorInPortal* ClientActor::getActorInPortal()
{
	return m_pActorInPortal;
}

ActorBindVehicle* ClientActor::getActorBindVehicle()
{
	if (!m_pActorBindVehicle) {
		m_pActorBindVehicle = CreateComponent<ActorBindVehicle>("ActorBindVehicle");
	}
	return m_pActorBindVehicle;
}
BindActorComponent* ClientActor::getBindActorCom()
{
	if (!m_pBindActorComponent) {
		m_pBindActorComponent = CreateComponent<BindActorComponent>("BindActorComponent");
	}
	return m_pBindActorComponent;
}
ActorUpdateFrequency* ClientActor::getUpdateFrequencyCom()
{
	if (!m_pActorUpdateFrequency) {
		m_pActorUpdateFrequency = CreateComponent<ActorUpdateFrequency>("ActorUpdateFrequency");
	}
	return m_pActorUpdateFrequency;
}

AIUpdateFrequency* ClientActor::getAIUpdateFrequencyCom()
{
	if (!m_pAIUpdateFrequency) {
		m_pAIUpdateFrequency = CreateComponent<AIUpdateFrequency>("AIUpdateFrequency");
	}
	return m_pAIUpdateFrequency;
}


void ClientActor::SaveToPBActorObj(const void* actorObj)
{
	if (!m_bDelayInit)
	{
		DelayInitDataRecursively(); //必须完成数据全部的初始化
	}

	PB_ActorObj* pbActorObj = (PB_ActorObj*)actorObj;
	PB_ActorCommon* actorCommon = pbActorObj->mutable_basedata();
	savePBActorCommon(actorCommon);

	pbActorObj->set_interacted(canBeCollidedWith());
	string modelPath;
	int modelType = 0;
	int extradata = 0;
	if (m_Body)
	{
		modelPath = m_Body->getModelPath();
		modelType = m_Body->getModelType();
		extradata = m_Body->getExtraData();
	}

	pbActorObj->set_modelpath(modelPath);
	pbActorObj->set_modeltype(modelType);
	pbActorObj->set_extradata(extradata);

	PB_Vector3f* scale = pbActorObj->mutable_scale();
	auto localScale = GetLocalScale();
	scale->set_x(localScale.x);
	scale->set_y(localScale.y);
	scale->set_z(localScale.z);

	
	ScriptComponent* pScriptComponent = getScriptComponent();
	if (pScriptComponent)
	{
		string scriptComponent;
		pScriptComponent->OnSave(scriptComponent, true);

		if (scriptComponent != "")
		{
			pbActorObj->set_script(scriptComponent);
		}
	}
	
	float roll = m_pComponentLocoMotion ? m_pComponentLocoMotion->m_RotateRoll : 0.f;
	auto pTransform = GetTransform();
	if (pTransform && pTransform->GetParent())
	{
		auto rot = GetLocalRotationEuler();
		roll = rot.z;
	}
	pbActorObj->set_roll(roll);

	if (m_pModelComponent)
	{
		string modelComponent;
		m_pModelComponent->SerializeModelKV(&modelComponent);
		pbActorObj->set_modelcomponent(modelComponent);
	}
	
	auto pPhysicsCom = getPhysicsComponent();
	if (pPhysicsCom)
	{
		game::common::PB_ActorPhysicsCom* pbPhysicsCom = pbActorObj->mutable_physicscom();
		pPhysicsCom->OnSavePB(pbPhysicsCom);
	}

	auto pEffectCom = getEffectComponent();
	if (pEffectCom)
	{
		game::common::PB_EffectComParticleInfo* pbEffectCom = pbActorObj->mutable_effectcomptclinfo();
		pEffectCom->OnSavePB(pbEffectCom);
	}

	auto pSoundCom = getSoundComponent();
	if (pSoundCom)
	{
		game::common::PB_SoundComInfo* pbSoundCom = pbActorObj->mutable_soundcominfo();
		pSoundCom->OnSavePB(pbSoundCom);
	}

	pbActorObj->set_nodeid(GetNodeid());
	pbActorObj->set_isparent(m_bParent);
	pbActorObj->set_parentwid(m_ParentWID);
	if (GetChildrenCount() > 0)
	{
		PB_ActorObjArray children;
		GetChildrenList().for_each([&](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				PB_ActorObj* childActor = children.add_child();
				child->SaveToPBActorObj(childActor);
			}
		});

		if (children.child_size() > 0)
		{
			std::string childrenStr;
			children.SerializeToString(&childrenStr);
			pbActorObj->set_children(childrenStr.data(), childrenStr.size());
			
		}
	}
}

//todo 以后ClientMob可以挂更多组件和actor子节点，可仿照SaveObjectChildAndComponent修改此函数
void ClientActor::SaveToPBChildAndComponent(std::string& modelcomponent)
{
	if (!m_bDelayInit)
	{
		DelayInitDataRecursively(); //必须完成数据全部的初始化
	}

	if (m_pModelComponent)
	{
		m_pModelComponent->SerializeModelKV(&modelcomponent);
	}

	return;
}

void ClientActor::LoadFromPBChildAndComponent(const std::string* modelcomponent)
{
	if (modelcomponent)
	{
		m_modelcomLen = modelcomponent->size();
		if (m_modelcomLen > 0)
		{
			m_modelcomBin = (char*)malloc(m_modelcomLen);
			if (m_modelcomBin) memcpy(m_modelcomBin, modelcomponent->data(), m_modelcomLen);
		}
	}
}

int ClientActor::saveToPB(game::hc::PB_GeneralEnterAOIHC *pb)
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT || m_ParentWID != 0)
	{
		return -1;
	}

	PB_ActorObj* actorObj = pb->mutable_actorobj();
	SaveToPBActorObj(actorObj);
	return 0;
}

void ClientActor::SendActorObjMsg(const char* script)
{
	if (!m_pWorld || !m_pWorld->getMpActorMgr())
		return;

	PB_GeneralEnterAOIHC pb;
	pb.set_objid(getObjId());
	PB_ActorObj* pbActorObj = pb.mutable_actorobj();
	PB_ActorCommon* actorCommon = pbActorObj->mutable_basedata();
	PB_Vector3* pos = actorCommon->mutable_pos();
	ActorLocoMotion* loc = getLocoMotion();
	auto pTransform = GetTransform();
	float roll = m_pComponentLocoMotion ? m_pComponentLocoMotion->m_RotateRoll : 0.f;
	if (pTransform && pTransform->GetParent())
	{
		auto rot = GetLocalRotationEuler();
		actorCommon->set_yaw(rot.y * 1000);
		actorCommon->set_pitch(rot.x * 1000);
		auto position = GetLocalPosition();
		pos->set_x(position.x);
		pos->set_y(position.y);
		pos->set_z(position.z);
		roll = rot.z;
	}
	else if (loc)
	{
		pos->set_x(loc->m_Position.x);
		pos->set_y(loc->m_Position.y);
		pos->set_z(loc->m_Position.z);
		actorCommon->set_yaw(loc->m_RotateYaw * 1000);
		actorCommon->set_pitch(loc->m_RotationPitch * 1000);
	}

	pbActorObj->set_roll(roll);

	PB_Vector3f* scale = pbActorObj->mutable_scale();
	auto localScale = GetLocalScale();
	scale->set_x(localScale.x);
	scale->set_y(localScale.y);
	scale->set_z(localScale.z);

	pbActorObj->set_script(script);
	m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_SEND_OBJACTOR_MSG, pb, this);
}

void ClientActor::LoadFromPBActorObjSimple(const void* actorObj)
{
	PB_ActorObj* pbActorObj = (PB_ActorObj*)actorObj;
	const PB_ActorCommon& actorCommon = pbActorObj->basedata();
	const PB_Vector3& pbpos = actorCommon.pos();
	const WCoord pos(pbpos.x(), pbpos.y(), pbpos.z());
	getLocoMotion()->gotoPosition(pos, actorCommon.yaw() / 1000.0, actorCommon.pitch() / 1000.0);

	SetLocalPostion(pbpos.x(), pbpos.y(), pbpos.z());

	const PB_Vector3f& vec3 = pbActorObj->scale();
	SetLocalScale(vec3.x(), vec3.y(), vec3.z());

	if (m_pComponentLocoMotion)
	{
		WCoord& pos = m_pComponentLocoMotion->getPosition();
		SetWorldPosition(pos.x, pos.y, pos.z); //有物理可以设置物理的位置
		SetLocalRotationEuler(m_pComponentLocoMotion->m_RotationPitch, m_pComponentLocoMotion->m_RotateYaw, pbActorObj->roll());

		m_pComponentLocoMotion->m_ServerPos = m_pComponentLocoMotion->getPosition();
		m_pComponentLocoMotion->m_ServerRot = m_pComponentLocoMotion->m_RotateQuat;
	}

	if (pbActorObj->has_script())
	{
		m_scriptcomLen = pbActorObj->script().size();
		if (m_scriptcomLen > 0 && m_pScriptComponent)
		{
			string scriptComStr = "";
			scriptComStr.assign(pbActorObj->script().data(), m_scriptcomLen);
			m_pScriptComponent->OnClientSyncActionGroup(scriptComStr);
		}
	}
}

bool ClientActor::LoadFromPBActorObj(const void* actorObj)
{
	PB_ActorObj* pbActorObj = (PB_ActorObj*)actorObj;
	InitInstance();
	CreateComponent<ActorLocoMotion>("ActorLocoMotion");
	CreateComponent<ActorChunkPos>("ActorChunkPos");

	const PB_ActorCommon& actorCommon = pbActorObj->basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;

	SetNodeid(pbActorObj->nodeid());
	m_bParent = pbActorObj->isparent();
	m_ParentWID = pbActorObj->parentwid();

	if (actorCommon.has_pos())
	{
		auto pos = actorCommon.pos();
		SetLocalPostion(pos.x(), pos.y(), pos.z());
	}
	
	if (pbActorObj->modelpath().size() > 0)
	{
		InitActor(pbActorObj->modeltype(), pbActorObj->modelpath(), pbActorObj->extradata());
	}

	const PB_Vector3f& vec3 = pbActorObj->scale();
	SetLocalScale(vec3.x(), vec3.y(), vec3.z());

	SetCanBeInteracted(pbActorObj->interacted());

	m_bEmpty = false; //客机不分父子节点
	if (m_pComponentLocoMotion)
	{
		WCoord& pos = m_pComponentLocoMotion->getPosition();
		SetWorldPosition(pos.x, pos.y, pos.z); //有物理可以设置物理的位置
		SetLocalRotationEuler(m_pComponentLocoMotion->m_RotationPitch, m_pComponentLocoMotion->m_RotateYaw, pbActorObj->roll());
		
		m_pComponentLocoMotion->m_ServerPos = m_pComponentLocoMotion->getPosition();
		m_pComponentLocoMotion->m_ServerRot = m_pComponentLocoMotion->m_RotateQuat;
	}
	if (pbActorObj->has_modelcomponent())
	{
		m_modelcomLen = pbActorObj->modelcomponent().size();
		if (m_modelcomLen > 0)
		{
			m_modelcomBin = (char*)malloc(m_modelcomLen);
			if (m_modelcomBin) memcpy(m_modelcomBin, pbActorObj->modelcomponent().data(), m_modelcomLen);
		}
	}
	if (pbActorObj->has_physicscom())
	{
		ENG_DELETE(m_pPBActorPhysicsCom);
		m_pPBActorPhysicsCom = ENG_NEW(game::common::PB_ActorPhysicsCom)(*pbActorObj->mutable_physicscom());
	}

	if (pbActorObj->has_effectcomptclinfo())
	{
		ENG_DELETE(m_pPBActorEffectCom);
		m_pPBActorEffectCom = ENG_NEW(game::common::PB_EffectComParticleInfo)(*pbActorObj->mutable_effectcomptclinfo());
	}

	if (pbActorObj->has_soundcominfo())
	{
		ENG_DELETE(m_pPBActorSoundCom);
		m_pPBActorSoundCom = ENG_NEW(game::common::PB_SoundComInfo)(*pbActorObj->mutable_soundcominfo());
	}

	if (pbActorObj->has_children())
	{
		m_childLen = pbActorObj->children().size();
		if (m_childLen > 0)
		{
			m_fromPB = true;
			m_childBin = (char*)malloc(m_childLen);
			if (m_childBin) memcpy(m_childBin, pbActorObj->children().data(), m_childLen);
		}
	}

	if (pbActorObj->has_script())
	{
		m_scriptcomLen = pbActorObj->script().size();
		if (m_scriptcomLen > 0)
		{
			m_scriptcomBin = (char*)malloc(m_scriptcomLen);
			if (m_scriptcomBin) memcpy(m_scriptcomBin, pbActorObj->script().data(), m_scriptcomLen);
		}
	}

	return true;
}

//此函数目前没用，联机子节点独立处理
bool ClientActor::LoadFromPBChildren(char* buff, int buffLen, std::vector<ClientActor*> & childrenVec, std::vector<ClientActor*>& childrenVecFail)
{
	PB_ActorObjArray childrenArray;
	childrenArray.ParseFromArray(buff, buffLen);
	for (int i = 0; i < childrenArray.child_size(); i++)
	{
		const PB_ActorObj& actorObj = childrenArray.child(i);
		ClientActor* actor = SANDBOX_NEW(ClientActor, true);
		if (!actor) continue;
		if (actor->LoadFromPBActorObj(&actorObj))
		{
			childrenVec.push_back(actor);
		}
		else
		{
			childrenVecFail.push_back(actor);
		}
	}

	return true;
}

int ClientActor::LoadFromPB(const game::hc::PB_GeneralEnterAOIHC &pb)
{
	if (getObjType() != OBJ_TYPE_GAMEOBJECT)
	{
		return -1;
	}

	const PB_ActorObj& actorObj = pb.actorobj();
	LoadFromPBActorObj(&actorObj);

	return 0;
}

bool ClientActor::playMotion(const char *name, int motion_class, bool force_play, float loopPlayTime)
{
	if (name == NULL || !getBody() || getBody()->getEntity() == NULL)
		return false;
	getBody()->getEntity()->PlayMotion(name, false, motion_class, loopPlayTime);
	SharePtr<ModelMotion> pmotion = getBody()->getEntity()->FindMotion(name);
	if (pmotion && loopPlayTime > 0)
	{
		pmotion->m_loop = loopPlayTime > 0 ? ML_LOOP : ML_ONCE;
		pmotion->m_fLoopStopTime = loopPlayTime;
		getBody()->getEntity()->SetMotionLoopTime(name, loopPlayTime);
	}
	return true;
}

void ClientActor::stopMotion(const char *name)
{
	if (name != NULL && getBody())
	{
		if (getBody()->getEntity()) 
			getBody()->getEntity()->StopMotion(name);
	}
}

void ClientActor::stopMotion(int motion_class)
{
	if (getBody() && getBody()->getEntity())
		getBody()->getEntity()->StopMotion(motion_class);
}
std::string ClientActor::getActorName()
{
	if (m_monsterDef)
	{
		return m_monsterDef->Name.c_str();
	}
	return "";
}

void ClientActor::LoadActorEventListen()
{
	
}

void ClientActor::syncAttr(int attrtype, float val)
{
	if (m_pWorld && !m_pWorld->isRemoteMode() && attrtype == ATTRT_MAX_HP) //生物暂时只同步最大血量 by：jeff
	{
		jsonxx::Object object;
		object << "objid" << this->getObjId();
		object << "attrtype" << attrtype;
		object << "val" << val;
		//相关逻辑在LuaMsgHandle.lua中处理
		GetSandBoxManager().sendBroadCast("ACTOR_SET_ATTR_TOTRACKINGPLAYERS", object.bin(), object.binLen());
	}
}

void ClientActor::setRoleSkinId(const int id)
{
	/*RoleSkinDef* p = GetDefManagerProxy()->getRoleSkinDef(id);
	if (p)
	{
		m_searchName = p->Name;
	}*/
}

void ClientActor::setMonsterId(const int id)
{
	m_monsterDef = GetDefManagerProxy()->getMonsterDef(id);
	/*if (m_monsterDef)
	{
		m_searchName = m_monsterDef->Name.c_str();
	}*/
}

void ClientActor::setItemId(const int id)
{
	/*ItemDef* p = GetDefManagerProxy()->getItemDef(id);
	if (p)
	{
		m_searchName = p->Name.c_str();
	}*/
}

void ClientActor::setBlockId(const int id)
{
	/*BlockDef* p = GetDefManagerProxy()->getBlockDef(id);
	if (p)
	{
		m_searchName = p->Name.c_str();
	}*/
}

void ClientActor::SetBindChunk(bool toggle)
{
	auto pActorChunkPos = m_pChunkPosComponent;
	if(!m_pChunkPosComponent && toggle)
	{
		pActorChunkPos = m_pChunkPosComponent = CreateComponent<ActorChunkPos>("ActorChunkPos");
	}
	// auto pActorChunkPos = CreateComponent<ActorChunkPos>("ActorChunkPos");
	if (toggle) {//open
		SetSerializableInChunk(true);//走老的跟随chunk的序列化
		//SetSerializable(false);
		if (m_pWorld && pActorChunkPos)
		{
			pActorChunkPos->onEnterWorld();
		}
	}
	else {//close
		SetSerializableInChunk(false);//走场景树的序列化
		//SetSerializable(true);
		if (m_pWorld && pActorChunkPos)
		{
			pActorChunkPos->onLeaveWorld(false);
			RemoveComponent(GetComponentByName("ActorChunkPos"));
			m_pChunkPosComponent = nullptr;
		}
	}
	//Super::SetBindChunk(toggle);
}

void ClientActor::setMovingLightEnable(bool value)
{
	//todo 如果有地方设置true，打开下面代码
	/*if (!value && m_pActorMovingLight == nullptr) return;
	if (m_pActorMovingLight == nullptr)
	{
		m_pActorMovingLight = this->CreateComponent<ActorMovingLight>("ActorMovingLight");
	}
	m_pActorMovingLight->SetEnable(value);*/
}

bool ClientActor::LocomotionPosGet(Rainbow::Vector3f& value) const
{
	value = m_pComponentLocoMotion ? m_pComponentLocoMotion->getPosition().toVector3() : Rainbow::Vector3f::zero;
	return true;
}
void ClientActor::LocomotionPosSet(const Rainbow::Vector3f& value)
{
	if (getLocoMotion())
	{
		getLocoMotion()->setPosition(value.x, value.y, value.z);
	}
}



void ClientActor::SetVisible(const bool& value)
{
	if (value == IsVisible())
	{
		return;
	}
	Super::SetVisible(value);
	if (m_Body)
	{
		m_Body->show(value);
	}
	OnAttributeChanged(this, &R_Visible);

	//auto children = this->GetAllChildren();
	//for (auto child : children)
	//{
	//	if (child->IsKindOf<ClientActor>())
	//	{
	//		auto actor = child->ToCast<ClientActor>();
	//		if (actor)
	//		{
	//			actor->SetVisible(value);
	//		}
	//	}
	//}
}
Rainbow::Vector3f ClientActor::GetlocalAABBExtent()
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		Vector3f center = GetWorldPosition();
		Vector3f extent(50.0f, 50.0f, 50.0f);
		Rainbow::AABB box;
		box.SetCenterAndExtent(center, extent);

		Rainbow::MeshRenderer* pMeshRenderer = GetMeshRender();
		if (pMeshRenderer)
		{
			Rainbow::SharePtr< Rainbow::Mesh> mesh = pMeshRenderer->GetSharedMesh();
			if (mesh)
			{
				box = mesh->GetLocalAABB();
			}
		}

		return box.GetExtent();
	}

	if (getLocoMotion())
	{
		BoxSphereBound localBound;
		Rainbow::AABB box;
		if (getObjType() == OBJ_TYPE_GAMEOBJECT)
		{
			getLocoMotion()->getHitCollideBox(box);
			auto pEntity = getEntity();
			auto pBody = getBody();
			if (pEntity && pEntity->GetMainModel())
			{
				pEntity->GetMainModel()->getLocalBounds(localBound);
				box.FromMinMax(localBound.getMin(), localBound.getMax());
			}
			else if (pEntity && pEntity->GetBindObjectCount() > 0 && pBody && pBody->getModelType() == ModelType_Object && pBody->getExtraData() > 0)
			{
				auto bindobj = pEntity->GetBindObject(0);
				BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(bindobj);
				if (pBlockMesh != NULL)
				{
					Vector3f worldPos = pEntity->GetWorldPosition();
					SectionMesh* pMesh = pBlockMesh->getMesh();
					AABB box = pMesh->GetLocalAABB();
				}
				else
				{
					ModelItemMesh* pModelItemMesh = dynamic_cast<ModelItemMesh*>(bindobj);
					if (pModelItemMesh != NULL)
					{
						pModelItemMesh->GetModel()->getLocalBounds(localBound);
						box.FromMinMax(localBound.getMin(), localBound.getMax());
					}
				}
			}
			else if (pBody == NULL && getEffectComponent())//只有含有特效组件的实体
			{
				EffectComponent* pEffectComp = getEffectComponent();
				box.SetCenterAndExtent(Vector3f(0.0f, 0.0f, 0.0f), Vector3f(50.0f, 50.0f, 50.0f));
			}
		}
		else
		{
			Vector3f(BLOCK_FSIZE / 2.0f);
		}
		return box.GetExtent();
	}
	return Vector3f(BLOCK_FSIZE / 2.0f);
}

void ClientActor::SetIgnoreUpdateFrequencyCtrlInner(bool b, int flag)
{
	//设置此ClientActor是否走AOI那套逻辑调用update和tick
	m_bIgnoreUpdateFrequencyCtrl = b;

	if (g_WorldMgr && !IsEmpty())  //&& g_WorldMgr->getNewActorMoveLerpSwtich()
	{
		if (!m_SpaceActorHandle && b)
		{
			m_SpaceActorHandle = getActorMgr()->GetSpaceManager()->SetForeverTick(this);
		}
		else if (m_SpaceActorHandle && !b)
		{
			m_SpaceActorHandle->SetIsVaild(false);
			m_SpaceActorHandle = nullptr;
		}
	}

	if (flag == 1) //向上 
	{
		ClientActor* parent = dynamic_cast<ClientActor*>(GetParent());
		if (parent)
		{
			parent->SetIgnoreUpdateFrequencyCtrlInner(b, 1);
		}
	}
	else if (flag == 2) //向下
	{
		if (GetChildrenCount() > 0)
		{
			GetChildrenList().for_each([b](SandboxNode* t) -> void {
				ClientActor* child = dynamic_cast<ClientActor*>(t);
				if (child)
				{
					child->SetIgnoreUpdateFrequencyCtrlInner(b, 2);
				}
				});
		}
	}
}

void ClientActor::SetIgnoreUpdateFrequencyCtrl(bool b)
{
	SetIgnoreUpdateFrequencyCtrlInner(b, 0);

	//向上设父节点 
	ClientActor *parent = dynamic_cast<ClientActor*>(GetParent());
	if (parent)
	{
		parent->SetIgnoreUpdateFrequencyCtrlInner(b, 1);
	}

	//向下设
	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([b](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				child->SetIgnoreUpdateFrequencyCtrlInner(b, 2);
			}
			});
	}
}

void ClientActor::SetCanBeInteracted(bool b)
{
	m_bCanBeInteracted = b;

	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([b](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				child->SetCanBeInteracted(b);
			}
			});
	}
}

void ClientActor::SetSelfMoveable(bool isMoveable)
{
	if (isMoveable)
		m_bMoveable = m_bMoveable | 0x01;
	else
		m_bMoveable = m_bMoveable & 0xFE;

	SetAncestorMoveable(isMoveable);
}

void ClientActor::SetAncestorMoveable(bool isAncestorMoveable)
{
	if (GetChildrenCount() == 0)
		return;

	GetChildrenList().for_each([isAncestorMoveable](SandboxNode* t) -> void {
		ClientActor* child = dynamic_cast<ClientActor*>(t);
		if (!child)
			return;
		if (isAncestorMoveable)
			child->m_bMoveable = child->m_bMoveable | 0x02;
		else
			child->m_bMoveable = child->m_bMoveable & 0xFD;
		//如果自己是个运动器就不用再去通知子节点了
		if (child->IsSelfMoveable())
			return;
		child->SetAncestorMoveable(isAncestorMoveable);
	});
}

bool ClientActor::IsSelfMoveable()
{
	return (m_bMoveable & 0x01) == 1;
}

bool ClientActor::IsAncestorMoveable()
{
	return (m_bMoveable & 0x02) == 2;
}

bool ClientActor::IsMoveable()
{
	return (m_bMoveable & 0x03) != 0;
}

void ClientActor::BindToScene(MNSandbox::Scene* scene)
{
	// ClientActor 处理
	World* world = MNSandbox::GetWorldByScene(scene);
	ActorManager* actorMgr = world ? static_cast<ActorManager*>(world->getActorMgr()) : nullptr;
	if (actorMgr)
	{
		actorMgr->InsertLiveActors(this);
	}
	else
	{
		assert(false && "ClientActorManager is nil!");
	}

	Super::BindToScene(scene);
}
void ClientActor::UnbindFromScene(MNSandbox::Scene* scene)
{
	Super::UnbindFromScene(scene);

	// ClientActor 处理
	World* world = MNSandbox::GetWorldByScene(scene);
	ActorManager* actorMgr = world ? static_cast<ActorManager*>(world->getActorMgr()) : nullptr;
	if (actorMgr)
	{
		actorMgr->EraseLiveActors(this, scene->IsKeepInChunk());
	}
	else
	{
		assert(false);
	}
}

ThornBallComponent* ClientActor::sureThornBallComponent()
{
	if (!m_pThornBallComponent)
	{
		m_pThornBallComponent = CreateComponent<ThornBallComponent>("ThornBallComponent");
	}
	
	return m_pThornBallComponent;
}

ThornBallComponent* ClientActor::getThornBallComponent()
{
	return m_pThornBallComponent;
}

Rainbow::GameObject* ClientActor::GetGameObject()
{
	return m_pGameObject;
}

Rainbow::Transform* ClientActor::GetTransform()
{
	if (!m_pGameObject)
		return nullptr;

	return m_pGameObject->GetTransform();
}

void ClientActor::InitActor(int iModelType, const std::string& sModelPath, int extraData)
{
	if (IsBaseModelType(iModelType, extraData) && m_pModelComponent)
	{
		m_pModelComponent->InitMeshRenderParam(iModelType, sModelPath, extraData);
	}
	else
	{
		NewActorBody(iModelType, sModelPath, extraData);
	}
}

void ClientActor::SetModel(int iModelType, const std::string& sModelPath, int iExtraData, bool saveMesh)
{
	if (IsBaseModelType(iModelType, iExtraData) && m_pModelComponent)
	{
		m_pModelComponent->SetModelMeshRenderer(iModelType, sModelPath, iExtraData, saveMesh);
	}
	else
	{
		SetModelActorBody(iModelType, sModelPath, iExtraData, saveMesh);
	}
}

void ClientActor::SetModelActorBody(int iModelType, const std::string& sModelPath, int iExtraData /* = -1 */, bool saveMesh /* = false */)
{
	if (m_pModelComponent)
	{
		m_pModelComponent->RemoveMeshRender();
	}

	if (!m_Body) {
		m_Body = ENG_NEW(ActorBody)(this);
		Rainbow::FixedString modelPath(sModelPath.c_str());
		m_Body->setModelPath(modelPath);
		if (m_pWorld && m_Body) {
			m_Body->onEnterWorld(m_pWorld);
		}
	}

	if (!m_Body || (sModelPath.empty() && iExtraData < 0))
		return;

	if (m_pModelComponent)
		m_pModelComponent->InitMeshRenderParam(iModelType, sModelPath, iExtraData);

	InitActorBody(m_Body, iModelType, sModelPath, iExtraData, saveMesh);

	auto pEntity = getEntity();
	if (pEntity)
	{
		pEntity->SetScale(m_Scale);

		ActorLocoMotion* pLoco = getLocoMotion();
		if (pLoco)
		{
			WCoord& coord = pLoco->getPosition();
			Vector3f pos = coord.toVector3();
			pEntity->SetPosition(pos);
		}
	}
}

void ClientActor::RemoveActorBody(bool removeModelComp /* = true */)
{
	if (GetActorBodySafeHandle()->IsVaild(m_Body))
		ENG_DELETE(m_Body);
	m_Body = NULL;

	if (removeModelComp && m_pModelComponent)
	{
		m_pModelComponent->RemoveMeshRender();
		DestroyComponent(m_pModelComponent);
	}
}

void ClientActor::SetModelTexture(const char* sType, const char* sTextPath, int nSubMeshIndex /* = -1 */, bool forceMainTex /* = false */, bool isCubeMap /* = false */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelTextureMeshRenderer(sType, sTextPath, nSubMeshIndex, forceMainTex, isCubeMap);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomTexture(sType, sTextPath, nSubMeshIndex, forceMainTex, isCubeMap);
	}
}

void ClientActor::SetOverlayColor(float r, float g, float b)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetOverlayColorMeshRenderer(r, g, b);
	}
	else
	{
		Entity* pEntity = getEntity();
		if (pEntity)
		{
			ColourValue value(r, g, b);
			pEntity->SetOverlayColor(&value);
		}
	}
}

void ClientActor::SetModelMatVector(const char* sType, const Rainbow::Vector4f &vec4, int nSubMeshIndex /* = -1 */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatVectorMeshRenderer(sType, vec4, nSubMeshIndex);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomMatVector(sType, vec4, nSubMeshIndex);
	}
}

void ClientActor::SetModelMatFloat(const char* sType, float val, int nSubMeshIndex /* = -1 */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatFloatMeshRenderer(sType, val, nSubMeshIndex);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomMatFloat(sType, val, nSubMeshIndex);
	}
}

void ClientActor::SetModelMatKeyword(const char* sType, bool flag)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatKeywordMeshRenderer(sType, flag);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomMatKeyword(sType, flag);
	}
}

//设置材质的裁剪模式
void ClientActor::SetModelMatCullMode(int type)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatCullModeMeshRenderer(type);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomMatCullMode(type);
	}
}

void ClientActor::SetModelMatAutoTiling(bool flag, int type, int nSubMeshIndex /* = -1 */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatAutoTilingMeshRenderer(flag, type, nSubMeshIndex);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->SetCustomMatAutoTiling(flag, type, nSubMeshIndex);
	}
}

void ClientActor::UpdateAutoTiling()
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->UpdateAutoTilingMeshRenderer(m_Scale);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->updateAutoTiling(m_Scale);
	}
}

void ClientActor::UpdateModelMatLightData()
{
	if (m_pModelComponent && IsBaseModelType(m_pModelComponent->GetModelType(), m_pModelComponent->GetModelExtraData()))
	{
		Rainbow::MeshRenderer* render = GetMeshRender();
		if (!render)
			return;

		int nCount = render->GetMaterialCount();
		for (int i = 0; i < nCount; i++)
		{
			MaterialInstance* mat = render->GetMaterials()[i].CastTo<MaterialInstance>().Get();
			if (mat)
			{
				Vector4f lightparam(1, 0, 0, 0);
				mat->SetVector("g_InstanceData", lightparam);
				mat->SetVector("g_InstanceAmbient", Vector4f::zero);
			}
		}
	}
	else if (getBody() && getBody()->getEntity()) //设置材质明暗
	{
		Vector4f lightparam(1, 0, 0, 0);
		getBody()->getEntity()->SetInstanceData(lightparam);
		getBody()->getEntity()->SetInstanceAmbient(ColourValue::ZERO, true);
	}
}


void ClientActor::SetModelMatTransparent(float val)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->SetModelMatTransparentMeshRenderer(val);
	}
	else
	{
		if (!m_Body)
			return;

		m_Body->setCustomMatTransparent(val);
	}
}

bool ClientActor::IsLoadModelFinished()
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		return m_pModelComponent->IsLoadedModelMeshRenderer();
	}
	else
	{
		auto body = getBody();
		if (!body)
			return false;

		return body->isLoadModelFinished();
	}
}

void ClientActor::SetModelMaterial(const std::string& sPath, bool transparencyShadowFlag /* = false */, int nSubMeshIndex /* = -1 */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->updateMaterial(sPath, transparencyShadowFlag, nSubMeshIndex);
	}
	else
	{
		auto body = getBody();
		if (!body)
			return;

		body->updateMaterial(sPath, transparencyShadowFlag, nSubMeshIndex);
	}
}

void ClientActor::SetModelMaterial(int matType, bool transparencyShadowFlag /* = false */, int nSubMeshIndex /* = -1 */)
{
	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		m_pModelComponent->updateMaterial(matType, transparencyShadowFlag, nSubMeshIndex);
	}
	else
	{
		auto body = getBody();
		if (!body)
			return;

		body->updateMaterial((ACTOR_CUSTOM_MAT_TYPE)matType, transparencyShadowFlag, nSubMeshIndex);
	}
}

ActorBody* ClientActor::NewActorBody(int iModelType, const std::string& sModelPath, int iExtraData)
{
	if (sModelPath.empty())
		return nullptr;
	if (GetActorBodySafeHandle()->IsVaild(m_Body))
		ENG_DELETE(m_Body);

	m_Body = ENG_NEW(ActorBody)(this);
	InitActorBody(m_Body, iModelType, sModelPath, iExtraData);

	return m_Body;
}

void ClientActor::InitActorBody(ActorBody* body, int iModelType, const std::string& sModelPath, int iExtraData, bool saveMesh)
{
	if (!body)
		return;

	if (!sModelPath.empty())
	{
		Rainbow::FixedString modelPath(sModelPath.c_str());
		body->setModelPath(modelPath);
	}
	body->setModelType(iModelType);
	body->setExtraData(iExtraData);

	body->setControlRotation(false);
	body->setInterpRotation(false);

	if (iModelType == ModelType_Base)
	{
		body->initBaseModel(iExtraData);
	}
	else if (iModelType == ModelType_Custom)
	{
		// 微缩方块/道具
		CustomModel* csmodel = CustomModelMgr::GetInstancePtr()->getCustomModel(MAP_MODEL_CLASS, sModelPath);
		if (!csmodel)
		{
			csmodel = CustomModelMgr::GetInstancePtr()->getCustomModel(RES_MODEL_CLASS, sModelPath);
		}
		if (csmodel)
		{
			body->initCustomModel(sModelPath, 1.0f);
		}
		else
		{
			body->initCustomActor(DEFAUT_MODEL, sModelPath);
		}
	}
	else if (iModelType == ModelType_FullyCustom)
	{
		body->initFullyCustomActor(MAP_MODEL_CLASS, NULL, sModelPath, false);
	}
	else if (iModelType == ModelType_Object)
	{
		if (iExtraData > 0)
		{
			body->initBlockModel(iExtraData, 1.0, true);

			if (getBody() && getBody()->getEntity() && getBody()->getEntity()->GetTransform() && getBody()->getEntity()->GetBindObjectCount() > 0)
			{
				auto bindobj = getBody()->getEntity()->GetBindObject(0);
				BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(bindobj);
				if (pBlockMesh)
				{
					SectionMesh* pMesh = pBlockMesh->getMesh();
					if (pMesh)
					{
						AABB blockBounds = pMesh->GetLocalAABB();
						Vector3f max = blockBounds.CalculateMax();
						Vector3f min = blockBounds.CalculateMin();
						Vector3f contentSize = max - min;
						float maxScale = math::max(math::max(m_Scale.x, m_Scale.y), m_Scale.z);
						float maxSize = math::max(math::max(contentSize.x, contentSize.y), contentSize.z);
						pBlockMesh->SetBoundSize(maxScale * maxSize);
					}
				}
			}
		}
		else
		{
			body->initUgcModel(sModelPath, UgcAssetType::OBJ);
		}
	}
	//else if (iModelType == MONSTER_IMPORT_MODEL)
	//{
	//	body->initOffcialModel(sModelPath);
	//}
	else if (iModelType == ModelType_UGCObj)
	{
		body->initUgcModel(sModelPath, UgcAssetType::OBJ);
	}
	else if (iModelType == ModelType_UGCOmod)
	{
		body->initUgcModel(sModelPath, UgcAssetType::OMOD);
	}
	else if (iModelType == ModelType_UGCEnginePrefab)
	{
		body->initUgcModel(sModelPath, UgcAssetType::ENGINE_PREFAB);
	}
	else
	{
		const char* pModeName = sModelPath.c_str();
		if (pModeName && pModeName[0] != 0)
		{
			if (pModeName[0] == 'p')
			{
				char path2[256];
				sprintf(path2, "%s", pModeName + 1);
				int model = atoi(path2);
				int playerindex = ComposePlayerIndex(model, 0, 0);
				body->initPlayer(playerindex);
			}
			else
			{
				// 加载player12素体
				if (iExtraData == PLAYER12_EXTRADATA)
				{
					body->initMonster(pModeName, 1.0f, true, "", "", false);
					if (pModeName && pModeName[0] != 0)
					{
						if (!body->isAvatarModel()) {
							body->setCustomSkins("initPlayer CustomSkins");
						}
						//Avatar模型，用默认裸模加上默认部件
						body->setBodyType(3);
						//body->addDefaultAvatar();
						//body->addAnimModel(2);
					}
				}
				else if (iExtraData == -998) //npc
				{
					body->initMonster(pModeName, 1.0f, true, "", "", true);
				}
				else
				{
					body->initMonster(pModeName, 1.0f, false, nullptr, nullptr, true);
				}
			}
		}
	}

	ResetActorCollider(saveMesh);
}

void ClientActor::ResetHitBox()
{
	auto body = getBody();
	if (!body)
		return;

	auto model = body->getModel();
	if (!model)
		return;

	if (m_pModelComponent && m_pModelComponent->IsSkeletonModel())
		return;

	auto pLocoMotion = getLocoMotion();
	if (pLocoMotion)
	{
		BoxSphereBound b;
		model->getLocalBounds(b);
		Vector3f box = b.getBox().getExtension() * 2.0f * m_Scale;
		pLocoMotion->setAttackBound((int)(box.y), (int)(box.x), (int)(box.z));
	}

	if (m_pWorld && m_pWorld->getActorMgr() && m_pWorld->getActorMgr()->GetActorMGT())
	{
		m_pWorld->getActorMgr()->GetActorMGT()->UpdateNode(this);
	}
}

void ClientActor::ResetShapeSize()
{
	if (!m_pPhysicsComponent)
		return;
	m_pPhysicsComponent->ResetShapeSize(m_Scale);
}

void ClientActor::UpdateChildrenWorldScale()
{
	auto children = this->GetAllChildren();
	for (auto child : children)
	{
		if (child->IsKindOf<ClientActor>())
		{
			auto actor = child->ToCast<ClientActor>();
			if (actor)
			{
				actor->m_Scale = actor->m_LocalScale * m_Scale;
				actor->UpdateLocalPosition();
				actor->ResetShapeSize();
				actor->ResetEffectComScale();
				actor->UpdateAutoTiling();
				actor->UpdateChildrenWorldScale();
			}
		}
	}
}

void ClientActor::ResetEffectComScale()
{
	// 更新特效组件的缩放
	auto effectCom = getEffectComponent();
	if (effectCom)
	{
		effectCom->ResetEffectParticleScale();
	}
}

bool ClientActor::IsPhysics()
{
	return m_pPhysicsComponent && m_pPhysicsComponent->IsMoveType(LPT_Rigidbody);
}

void ClientActor::GetPlatformPlayers(std::vector<long long>& vPlayers)
{
	if (!m_pPhysicsComponent)
		return;

	vPlayers = m_pPhysicsComponent->GetPlayers();
}

void ClientActor::SetIsParent(bool flag)
{
	m_bParent = flag;
}

void ClientActor::SetParentWID(long long wid)
{
	if (m_ParentWID == wid)
	{
		return;
	}

	m_ParentWID = wid; 
}

bool ClientActor::SetNodeParentReal(ClientActor* parent, bool bIsLoad)
{
	bool bRet = false;
	if (parent)
	{
		bRet = SetParent(parent);
	}
	else
	{
		if (!m_pWorld || !m_pWorld->GetWorldScene())
			return false;

		auto pScene = m_pWorld->GetWorldScene();
		if (!pScene)
			return false;

		bRet = pScene->BindSceneObj(this, pScene->GetLegacyActorRoot());
	}

	auto pTransform = GetTransform();
	if (bRet && pTransform)
	{
		if (!parent)
		{
			auto pParentTransform = pTransform->GetParent();
			Rainbow::Vector3f pos = GetLocalPosition();// pTransform->GetLocalPosition();
			auto rotation = pTransform->GetWorldRotation();
			auto scale = GetWorldScale();

			if (pParentTransform)
			{
				pos = pParentTransform->TransformPoint(pTransform->GetLocalPosition());
			}
			pTransform->SetParent(nullptr);

			SetWorldPosition(pos.x, pos.y, pos.z);
			SetWorldRotation(rotation);
			SetWorldScale(scale);
		}
		else
		{
			auto pParentTransform = parent->GetTransform();
			if (pParentTransform)
			{
				Rainbow::Quaternionf rotation = pTransform->GetLocalRotation();
				auto pos = GetLocalPosition();// pTransform->GetLocalPosition();

				if (!bIsLoad)
				{
					pos = pTransform->GetWorldPosition();
					rotation = pTransform->GetWorldRotation();
				}

				pTransform->SetParent(pParentTransform);

				if (bIsLoad)
				{
					SetLocalPostion(pos);
					SetLocalRotation(rotation);
					SetLocalScale(m_Scale);
				}
				else
				{
					pos = pParentTransform->InverseTransformPoint(pos);
					Rainbow::Vector3f parentWorldScale;
					if (GetParentActorWorldScale(parentWorldScale))
					{
						pos /= parentWorldScale;
					}

					SetLocalPostion(pos);
					//SetLocalRotation(rotation * pParentTransform->GetWorldRotation().Inverse());
					SetLocalRotation(pParentTransform->GetWorldRotation().Inverse() * rotation);
					SetLocalScale(m_Scale / parent->GetWorldScale());
				}
				
				ResetActorCollider();
				UpdateAutoTiling();
			}
		}
	}

	OnTransformUpdate();

	if (bRet)
	{
		SetEmpty(parent != nullptr);
	}

	return bRet;
}

void ClientActor::BindModelParent()
{
	/*ClientActor* pParent = dynamic_cast<ClientActor*>(GetParent());
	if (pParent && useNewLerpModel())
	{
		Rainbow::Transform* pModelTransform = nullptr;
		Rainbow::Transform* pParentModelTransform = nullptr;
		if (GetMeshRenderGO() && pParent->GetMeshRenderGO())
		{
			pModelTransform = GetMeshRenderGO()->GetTransform();
			if (pModelTransform && pModelTransform->GetParent())
				return;

			pParentModelTransform = pParent->GetMeshRenderGO()->GetTransform();
		}
		if (pModelTransform && pParentModelTransform)
		{			
			auto worldPos = pModelTransform->GetWorldPosition();
			Rainbow::Vector3f localPosOri = pParentModelTransform->InverseTransformPoint(worldPos);

			pModelTransform->SetParent(pParentModelTransform);

			auto vParentScale = pParentModelTransform->GetLocalScale();
			auto vScale = pModelTransform->GetLocalScale();
			pModelTransform->SetLocalScale(vScale / vParentScale);
			pModelTransform->SetLocalPosition(localPosOri);
		}
	}*/
}

ClientActor* ClientActor::GetAncestor()
{
	if (!GetParent())
	{
		return this;
	}

	ClientActor* ancestor = dynamic_cast<ClientActor*>(GetParent());
	if (!ancestor) return this;

	while (true)
	{
		if (ancestor->GetParent())
		{
			ClientActor* parentActor = dynamic_cast<ClientActor*>(ancestor->GetParent());
			if (!parentActor)
			{
				break;
			}
			else
			{
				ancestor = parentActor;
			}
		}
		else
		{
			break;
		}
	}

	return ancestor;
}

Rainbow::AABB ClientActor::GetMergeAABB()
{
	Rainbow::AABB mergeAABB = GetAABB();

	if (GetChildrenCount() > 0)
	{
		GetChildrenList().for_each([&mergeAABB](SandboxNode* t) -> void {
			ClientActor* child = dynamic_cast<ClientActor*>(t);
			if (child)
			{
				Rainbow::AABB aabbMerge = child->GetMergeAABB();
				getMergeCollideBox(mergeAABB, aabbMerge);
			}
			});
	}

	return mergeAABB;
}

ClientActor* ClientActor::Instantiate(const jsonxx::Object& obj)
{
	World* pWorld = MNSandbox::GetWorldByCurrentScene();
	if (!pWorld)
		return nullptr;

	ClientActor* pActor = SANDBOX_NEW(ClientActor, true);
	pActor->InitInstance();
	pActor->setWorld(pWorld);
	pWorld->getActorMgr()->ToCastMgr()->InsertLiveActors(pActor);

	pActor->InitActor(ModelType_Base, "", MeshType_Cube);
	pActor->SetCanBeInteracted(true);
	if (obj.has<jsonxx::String>("name")) {
		//std::string strName = obj.get<jsonxx::String>("name");
		pActor->SetName(obj.get<jsonxx::String>("name"));
	}

	ScriptCmpData stData;
	const jsonxx::Array& vComponents = obj.get<jsonxx::Array>("components");
	for (size_t i = 0; i < vComponents.size(); i++)
	{
		const jsonxx::Object& cmpObj = vComponents.get<jsonxx::Object>(i);
		if (cmpObj.has<jsonxx::String>("name"))
		{
			const std::string& sCmpName = cmpObj.get<jsonxx::String>("name");
			ActorComponentBase* pComponent = (ActorComponentBase*)pActor->CreateComponentByFactory(sCmpName, sCmpName);
			if (pComponent)
			{
				pComponent->LoadComponentData(cmpObj, false);

				jsonxx::Object obj;
				obj << "name" << sCmpName;
				stData.vObj.emplace_back(obj);
			}
			else
			{
				stData.vObj.emplace_back(cmpObj);
			}
		}
	}

	//加脚本组件
	if (stData.vObj.size() > 0)
	{
		auto pScriptComponent = pActor->CreateComponent<ScriptComponent>("ScriptComponent");
		if (pScriptComponent)
		{
			pScriptComponent->AddScriptComponent(stData);
		}
	}

	return pActor;
}

bool ClientActor::SetNodeParent(ClientActor* parent, bool bIsLoad)
{
	bool bRet = SetNodeParentReal(parent, bIsLoad);

	if (bRet)
	{
		if (parent)
		{
			parent->SetIsParent(true);
			SetParentWID(parent->getObjId());

			if (parent->IsMoveable())
				m_bMoveable = m_bMoveable | 0x02;
			else
				m_bMoveable = m_bMoveable & 0xFD;
			SetAncestorMoveable(parent->IsMoveable());
		}
		else
		{
			SetParentWID(0);
			//父节点为空了，先设置自己的，然后设置子节点的
			m_bMoveable = m_bMoveable & 0xFD;
			SetAncestorMoveable(false);
		}
	}

	return bRet;
}

static void WorldToLocalScale(ClientActor* actor, Rainbow::Vector3f& scale)
{
	if (!actor)
		return;

	ClientActor* pParent = dynamic_cast<ClientActor*>(actor->GetParent());
	if (!pParent)
		return;

	Rainbow::Vector3f worldscale = pParent->GetLocalScale();
	scale /= worldscale;

	WorldToLocalScale(pParent, scale);
}

static void LocalToWorldScale(ClientActor* actor, Rainbow::Vector3f& scale)
{
	if (!actor)
		return;

	ClientActor* pParent = dynamic_cast<ClientActor*>(actor->GetParent());
	if (!pParent)
		return;

	Rainbow::Vector3f worldscale = pParent->GetLocalScale();
	scale *= worldscale;

	LocalToWorldScale(pParent, scale);
}
void ClientActor::OnTransformUpdate()
{
	if (isPlayer())
		return;

	auto pTransform = GetTransform();
	if (!pTransform)
		return;

	if (!pTransform->GetParent() && !IsPhysics()) //有父节点的 需要反向同步locomotion
		return;

	auto pLocoMotion = getLocoMotion();
	if (!pLocoMotion)
		return;

	OPTICK_EVENT();

	//位置
	pLocoMotion->m_Position = pTransform->GetWorldPosition();

	//旋转
	pLocoMotion->m_RotateQuat = pTransform->GetWorldRotation();
	Vector3f euler = Rainbow::QuaternionToEulerAngle(pLocoMotion->m_RotateQuat);
	pLocoMotion->m_RotationPitch = euler.x;//UpdateRotation(pLoco->m_RotationPitch, euler.x, 180.0f);
	pLocoMotion->m_RotateYaw = euler.y;// UpdateRotation(pLoco->m_RotateYaw, euler.y, 180.0f);
	pLocoMotion->m_RotateRoll = euler.z;// UpdateRotation(pLoco->m_RotateRoll, euler.z, 180.0f);

	ClientActor* pParent = dynamic_cast<ClientActor*>(GetParent());
	if (pParent)
	{
		Rainbow::Vector3f parentWorldScale = pParent->GetWorldScale();
		m_Scale = parentWorldScale * m_LocalScale;

		auto pEntity = getEntity();
		if (pEntity)
			pEntity->SetScale(m_Scale);

		Rainbow::GameObject* pMeshRenderGO = GetMeshRenderGO();
		if (pMeshRenderGO && pMeshRenderGO->GetTransform() && !useNewLerpModel())
		{
			pMeshRenderGO->GetTransform()->SetLocalScale(m_Scale);
		}
	}
	
}

void ClientActor::SetWorldPosition(float x, float y, float z, bool igoreLerp/* =false */)
{
	m_LocalPosition.x = x;
	m_LocalPosition.y = y;
	m_LocalPosition.z = z;

	setPosition(x, y, z);
	auto pTransform = GetTransform();
	//bool needRealTimeUpdatePos = g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich() && (g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN) && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的
	bool needRealTimeUpdatePos = useNewLerpModel() && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的

	if (pTransform)
	{

		/*if (m_pModelLerpComponent && !igoreLerp)
		{
			m_pModelLerpComponent->InitData(PlatformMoveType::STRAIGHT_LINE);
			m_pModelLerpComponent->SetLerpPosData(GetTransform()->GetWorldPosition());
		}*/

		pTransform->SetWorldPosition(Vector3f(x, y, z));

		Rainbow::Vector3f parentWorldScale;
		if (GetParentActorWorldScale(parentWorldScale))
		{
			m_LocalPosition = pTransform->GetLocalPosition();
			m_LocalPosition /= parentWorldScale;
		}

		if (needRealTimeUpdatePos)
			GetTransform()->ForceUpdatePhysicDirtyFlag();  //更新物理位置
	}
}

const Rainbow::Vector3f ClientActor::GetWorldPosition()
{
	auto pTransform = GetTransform();
	if (pTransform)
		return pTransform->GetWorldPosition();

	return getPosition().toVector3();
}

void ClientActor::SetLocalPostion(const Rainbow::Vector3f& pos)
{
	SetLocalPostion(pos.x, pos.y, pos.z);
}

void ClientActor::SetLocalPostion(float x, float y, float z)
{
	m_LocalPosition.x = x;
	m_LocalPosition.y = y;
	m_LocalPosition.z = z;

	UpdateLocalPosition();
}

void ClientActor::UpdateLocalPosition()
{
	Vector3f pos = m_LocalPosition;

	/*
	//因为transform中的scale被始终置成1了, 为了保持整体缩放相关逻辑都要补上
	Rainbow::Vector3f parentWorldScale;
	if (GetParentActorWorldScale(parentWorldScale))
	{
		pos *= parentWorldScale;
	}*/

	auto pTransform = GetTransform();
	//bool needRealTimeUpdatePos = g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich() && (g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN) && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的
	bool needRealTimeUpdatePos = useNewLerpModel() && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的

	if (pTransform)
	{
		/*if (m_pModelLerpComponent)
		{
			m_pModelLerpComponent->InitData(PlatformMoveType::STRAIGHT_LINE);
			m_pModelLerpComponent->SetLerpPosData(pTransform->GetWorldPosition());
		}*/

		pTransform->SetLocalPosition(pos);

		if (needRealTimeUpdatePos)
			GetTransform()->ForceUpdatePhysicDirtyFlag();  //更新物理位置

		if (pTransform->GetParent())
		{
			//locomotion需要的是世界坐标
			pos = pTransform->GetParent()->TransformPoint(pos);
		}

	}

	setPosition(pos.x, pos.y, pos.z);
}

const Rainbow::Vector3f ClientActor::GetLocalPosition()
{
	/*auto pTransform = GetTransform();
	if (pTransform)
	{
		return pTransform->GetLocalPosition();
	}

	return getPosition().toVector3();*/
	return m_LocalPosition;
}

void ClientActor::SetLocalPostionFromParentAxis(float x, float y, float z)
{
	auto pTransform = GetTransform();
	if (pTransform)
	{
		pTransform->SetLocalPosition(Vector3f(x, y, z));
		if (pTransform->GetParent())
		{
			auto worldPos = pTransform->GetWorldPosition();
			x = worldPos.x;
			y = worldPos.y;
			z = worldPos.z;
		}
	}

	setPosition(x, y, z);
}

const Rainbow::Vector3f ClientActor::GetLocalPositionFromParentAxis()
{
	auto pTransform = GetTransform();
	if (pTransform)
	{
		return pTransform->GetLocalPosition();
	}

	return getPosition().toVector3();
}

void ClientActor::SetLocalRotation(const Rainbow::Quaternionf& rotation)
{
	auto pTransform = GetTransform();
	auto rot = rotation;
	if (pTransform)
	{
		pTransform->SetLocalRotation(rotation);
		if (pTransform->GetParent())
		{
			rot = rotation * pTransform->GetParent()->GetWorldRotation();
		}
	}

	Rainbow::GameObject* pMeshRenderGO = GetMeshRenderGO();
	if (pMeshRenderGO && pMeshRenderGO->GetTransform())
	{
		pMeshRenderGO->GetTransform()->SetLocalRotation(rot);
	}

	auto pLoco = getLocoMotion();
	if (pLoco)
	{
		Vector3f euler = Rainbow::QuaternionToEulerAngle(rot);
		pLoco->m_RotationPitch = euler.x;//UpdateRotation(pLoco->m_RotationPitch, euler.x, 180.0f);
		pLoco->m_RotateYaw = euler.y;// UpdateRotation(pLoco->m_RotateYaw, euler.y, 180.0f);
		pLoco->m_RotateRoll = euler.z;// UpdateRotation(pLoco->m_RotateRoll, euler.z, 180.0f);
		pLoco->m_RotateQuat = rot;
	}
}

void ClientActor::SetLocalRotationEuler(float x, float y, float z)
{
	SetLocalRotation(Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(x, y, z)));
}

const Rainbow::Quaternionf ClientActor::GetLocalRotation()
{
	auto pTransform = GetTransform();
	if (pTransform)
	{
		return pTransform->GetLocalRotation();
	}

	if (!getLocoMotion())
		return Rainbow::Quaternionf::identity;

	return getLocoMotion()->m_RotateQuat;
}

Rainbow::Vector3f ClientActor::GetLocalRotationEuler()
{
	return Rainbow::QuaternionToEulerAngle(GetLocalRotation());
}

void ClientActor::SetWorldRotation(const Rainbow::Quaternionf& rotation)
{
	//bool needRealTimeUpdatePos = g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich() && (g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN) && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的
	bool needRealTimeUpdatePos = useNewLerpModel() && m_bIgnoreUpdateFrequencyCtrl; //需要实时更新位置的

	auto pTransform = GetTransform();
	if (pTransform)
	{
		pTransform->SetWorldRotation(rotation);

		if (needRealTimeUpdatePos)
			GetTransform()->ForceUpdatePhysicDirtyFlag();  //更新物理位置
	}

	//if (m_pMeshRenderGO)
	//{
	//	Rainbow::Transform* transform = m_pMeshRenderGO->GetTransform();
	//	if (transform)
	//		transform->SetLocalRotation(rotation);
	//}

	auto pLoco = getLocoMotion();
	if (pLoco)
	{
		Vector3f euler = Rainbow::QuaternionToEulerAngle(rotation);
		pLoco->m_RotationPitch = euler.x;//UpdateRotation(pLoco->m_RotationPitch, euler.x, 180.0f);
		pLoco->m_RotateYaw = euler.y;// UpdateRotation(pLoco->m_RotateYaw, euler.y, 180.0f);
		pLoco->m_RotateRoll = euler.z;// UpdateRotation(pLoco->m_RotateRoll, euler.z, 180.0f);
		pLoco->m_RotateQuat = rotation;
	}
}

void ClientActor::SetWorldRotationEuler(float x, float y, float z)
{
	SetWorldRotation(Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(x, y, z)));
}

const Rainbow::Quaternionf ClientActor::GetWorldRotation()
{
	auto pTransfrom = GetTransform();
	if (pTransfrom)
	{
		return pTransfrom->GetWorldRotation();
	}

	if (!getLocoMotion())
		return Rainbow::Quaternionf::identity;

	return getLocoMotion()->m_RotateQuat;
}

Rainbow::Vector3f ClientActor::GetWorldRotationEuler()
{
	return Rainbow::QuaternionToEulerAngle(GetWorldRotation());
}

void ClientActor::SetLocalScale(const Rainbow::Vector3f& scale)
{
	SetLocalScale(scale.x, scale.y, scale.z);
}

void ClientActor::SetLocalScale(float x, float y, float z)
{
	m_LocalScale.x = x;
	m_LocalScale.y = y;
	m_LocalScale.z = z;
	m_Scale = m_LocalScale;
	
#if PHYSICS_SCALE_SWITCH
	//LocalToWorldScale(this, m_Scale);
	Rainbow::Transform* pTransform = GetTransform();
	if (pTransform)
	{
		pTransform->SetLocalScale(m_LocalScale);

		if (pTransform->GetParent())
		{
			Rainbow::Vector3f parentWorldScale = pTransform->GetParent()->GetWorldScale();
			m_Scale *= parentWorldScale;
		}
	}
#else
	LocalToWorldScale(this, m_Scale);
#endif

	Rainbow::GameObject* pMeshRenderGO = GetMeshRenderGO();
	if (pMeshRenderGO)
	{
		Rainbow::Transform* transform = pMeshRenderGO->GetTransform();
		if (transform) {
			transform->SetLocalScale(m_Scale);
		}
	}
	else
	{
		auto pEntity = getEntity();
		if (pEntity)
			pEntity->SetScale(m_Scale);
	}

#if PHYSICS_SCALE_SWITCH
	ResetHitBox();
	ResetEffectComScale();
	PrepareAABB();
#else
	ResetHitBox();
	ResetShapeSize();
	ResetEffectComScale();
	UpdateChildrenWorldScale();
	PrepareAABB();
#endif
}

const Rainbow::Vector3f ClientActor::GetLocalScale()
{
	return m_LocalScale;
}

void ClientActor::SetWorldScale(const Rainbow::Vector3f& scale)
{
	SetWorldScale(scale.x, scale.y, scale.z);
}

void ClientActor::SetWorldScale(float x, float y, float z)
{
	m_Scale.x = x;
	m_Scale.y = y;
	m_Scale.z = z;
	m_LocalScale = m_Scale;
	
#if PHYSICS_SCALE_SWITCH
	//WorldToLocalScale(this, m_LocalScale);
	Rainbow::Transform* pTransform = GetTransform();
	if (pTransform)
	{
		pTransform->SetWorldScale(m_Scale);

		if (pTransform->GetParent())
		{
			Rainbow::Vector3f parentWorldScale = pTransform->GetParent()->GetWorldScale();
			m_LocalScale /= parentWorldScale;
		}
	}
#else
	WorldToLocalScale(this, m_LocalScale);
#endif

	Rainbow::GameObject* pMeshRenderGO = GetMeshRenderGO();
	if (pMeshRenderGO)
	{
		Rainbow::Transform* transform = pMeshRenderGO->GetTransform();
		if (transform) {
			transform->SetLocalScale(m_Scale);
		}
	}
	else
	{
		auto pEntity = getEntity();
		if (pEntity)
			pEntity->SetScale(m_Scale);
	}

#if PHYSICS_SCALE_SWITCH
	ResetHitBox();
	ResetEffectComScale();
	PrepareAABB();
#else
	ResetHitBox();
	ResetShapeSize();
	ResetEffectComScale();
	UpdateChildrenWorldScale();
	PrepareAABB();
#endif
}

const Rainbow::Vector3f& ClientActor::GetWorldScale()
{
	return m_Scale;
}

bool ClientActor::GetParentActorWorldScale(Rainbow::Vector3f& worldscale)
{
	worldscale = Rainbow::Vector3f::one;
	ClientActor* pParent = dynamic_cast<ClientActor*>(GetParent());
	if (pParent)
	{
		worldscale = pParent->GetWorldScale();
		return true;
	}

	return false;
}

void ClientActor::SetRotationAroundPosition(float pitch, float yaw, float roll, int posx, int posy, int posz)
{
	Rainbow::Vector3f pos = GetWorldPosition();
	Rainbow::Quaternionf rotate = GetWorldRotation();
	/*if (m_pModelLerpComponent)
	{
		m_pModelLerpComponent->InitData(PlatformMoveType::AROUND_POINT);
		m_pModelLerpComponent->SetLerpPosData(pos);
		m_pModelLerpComponent->SetLerpRotData(pitch, yaw, roll, Rainbow::Vector3f(posx, posy, posz), rotate);
	}*/

	if (CalRotAndPosByAroundMove(pitch, yaw, roll, Rainbow::Vector3f(posx, posy, posz), rotate, pos))
	{
		SetWorldPosition(pos.x, pos.y, pos.z, true);
		SetWorldRotation(rotate);
	}
}

void ClientActor::SetLocalAroundPosition(float pitch, float yaw, float roll, int posx, int posy, int posz)
{
	Rainbow::Vector3f pos = GetWorldPosition();
	Rainbow::Quaternionf rotate = GetWorldRotation();
	/*if (m_pModelLerpComponent)
	{
		m_pModelLerpComponent->InitData(PlatformMoveType::LOCAL_AROUND_POINT);
		m_pModelLerpComponent->SetLerpPosData(pos);
		m_pModelLerpComponent->SetLerpRotData(pitch, yaw, roll, Rainbow::Vector3f(posx, posy, posz), rotate);
	}*/
	
	if (CalLocalRotAndPosByAroundMove(pitch, yaw, roll, Rainbow::Vector3f(posx, posy, posz), rotate, pos))
	{
		SetWorldPosition(pos.x, pos.y, pos.z, true);
		SetWorldRotation(rotate);
	}
}

bool ClientActor::useNewLerpModel()
{
	auto pPhysicsComponent = getPhysicsComponent();
	if (!pPhysicsComponent || !pPhysicsComponent->IsMoveType(LPT_Platform) || !g_WorldMgr || (g_WorldMgr->getGameMode() != OWTYPE_GAMEMAKER_RUN))
		return false;

	bool ret = ClientActor::isNewLerpModel();

	if (ret)
	{
		if (!m_pModelLerpComponent)
			CreateComponent<ActorModelLerpComponent>("ActorModelLerpComponent");
	}
	else
	{
		if (m_pModelLerpComponent)
			DestroyComponent(m_pModelLerpComponent);
	}

	return ret;
}

bool ClientActor::isNewLerpModel()
{
	return g_WorldMgr && g_WorldMgr->getNewActorMoveLerpSwtich();
}

bool ClientActor::CalLocalRotAndPosByAroundMove(float pitch, float yaw, float roll, const Rainbow::Vector3f posrelatively, Rainbow::Quaternionf &retrot, Rainbow::Vector3f &retpos)
{
	auto pTransfrom = GetTransform();
	if (!pTransfrom)
		return false;

	if (pitch == 0 && yaw == 0 && roll == 0)
		return false;

	Rainbow::Vector3f anchorPos = pTransfrom->TransformPoint(posrelatively);

	Quaternionf rotate = Quaternionf::identity;

	Rainbow::Matrix3x3f transform;
	Rainbow::Matrix3x3f matrix;
	Rainbow::Vector3f pos;
	Vector3f direction;

	if (roll != 0)
	{
		QuaternionfToMatrix(retrot, transform);
		direction = Rainbow::Normalize(transform.GetColumn(2));
		rotate = AxisAngleToQuaternionf(direction, Deg2Rad(roll));
		retrot = rotate * retrot;
		QuaternionfToMatrix(rotate, matrix);

		pos = retpos - anchorPos;
		retpos = matrix.MultiplyPoint3(pos) + anchorPos;
	}

	if (yaw != 0)
	{
		QuaternionfToMatrix(retrot, transform);

		direction = Rainbow::Normalize(transform.GetColumn(1));
		rotate = AxisAngleToQuaternionf(direction, Deg2Rad(yaw));
		retrot = rotate * retrot;

		QuaternionfToMatrix(rotate, matrix);
		pos = retpos - anchorPos;
		retpos = matrix.MultiplyPoint3(pos) + anchorPos;
	}

	if (pitch != 0)
	{
		QuaternionfToMatrix(retrot, transform);

		direction = Rainbow::Normalize(transform.GetColumn(0));
		rotate = AxisAngleToQuaternionf(direction, Deg2Rad(pitch));
		retrot = rotate * retrot;

		QuaternionfToMatrix(rotate, matrix);
		pos = retpos - anchorPos;
		retpos = matrix.MultiplyPoint3(pos) + anchorPos;
	}

	return true;
}

bool ClientActor::CalRotAndPosByAroundMove(float pitch, float yaw, float roll, const Rainbow::Vector3f posrelatively, Rainbow::Quaternionf& retrot, Rainbow::Vector3f& retpos)
{
	auto pTransfrom = GetTransform();
	if (!pTransfrom)
		return false;

	Rainbow::Vector3f anchorPos = pTransfrom->TransformPoint(posrelatively);
	Rainbow::Vector3f diffPos(retpos.x - anchorPos.x, retpos.y - anchorPos.y, retpos.z - anchorPos.z);

	Rainbow::Matrix4x4f mat_diff;
	Rainbow::makeSRTMatrix(mat_diff, Rainbow::Vector3f::one, retrot, diffPos);

	Rainbow::Matrix4x4f mat_operate;
	Rainbow::Quaternionf rot_operate = Rainbow::AngleEulerToQuaternionf(Rainbow::Vector3f(pitch, yaw, roll));
	Rainbow::makeSRTMatrix(mat_operate, Rainbow::Vector3f::one, rot_operate, Rainbow::Vector3f::zero);

	Rainbow::Matrix4x4f mat_ret = Matrix4x4Mul(mat_diff, mat_operate);
	auto pos = mat_ret.GetPosition();

	retpos = Rainbow::Vector3f(anchorPos.x + pos.x, anchorPos.y + pos.y, anchorPos.z + pos.z);
	retrot = mat_ret.GetRotation();

	return true;
}

const Rainbow::Vector3f ClientActor::InverseTransformPoint(float posx, float posy, float posz)
{
	auto pTransfrom = GetTransform();
	if (!pTransfrom)
	{
		return Rainbow::Vector3f(posx, posy, posz);
	}

	Rainbow::Vector3f anchorPos = pTransfrom->InverseTransformPoint(Rainbow::Vector3f(posx, posy, posz));
	return anchorPos;
}

void ClientActor::FaceWorldPos(const Rainbow::Vector3f& pos)
{
	Rainbow::Vector3f dir = pos - getPosition().toVector3();
	if (dir.Length() <= Rainbow::kEpsilon)
		return;

	Rainbow::Quaternionf quat;
	setRotateArc(Vector3f(0, 0, -1.0f), dir.GetNormalized(), quat);
	SetLocalRotation(quat);
}

void ClientActor::UpdateGameObjectTransform()
{
	//if (m_pPhysicsComponent && g_WorldMgr && !g_WorldMgr->getNewActorMoveLerpSwtich() && (g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN) && !useNewLerpModel())
	if (m_pPhysicsComponent && g_WorldMgr && (g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER_RUN) && !useNewLerpModel())
	{
		auto pMgr = getActorMgr();
		if (pMgr)
		{
			ClientPlayer* pPlayer = nullptr;
			std::vector<long long>& vPlayers = m_pPhysicsComponent->GetPlayers();

			for (unsigned int i = 0; i < vPlayers.size(); i++)
			{
				pPlayer = pMgr->findPlayerByUin(vPlayers[i]);
				if (pPlayer)
				{
					auto pPlayerTransform = pPlayer->GetTransform();
					PlayerLocoMotion* pLoco = static_cast<PlayerLocoMotion*>(pPlayer->getLocoMotion());
					if (pPlayerTransform && pLoco)
					{
						pPlayer->getEntity()->SetPosition(pPlayerTransform->GetWorldPosition() - Rainbow::Vector3f(0.0f, pLoco->m_BoundHeight / 2.0f - (pLoco->getRigidDynamicActor() ? 2.0f : 0.0f), 0.0f));
					}
				}
			}
		}
	}
}

void ClientActor::GetModelWorldBox(Rainbow::AABB& box)
{
	Vector3f center = GetWorldPosition();
	Vector3f extent(50.0f, 50.0f, 50.0f);
	box.SetCenterAndExtent(center, extent);

	if (m_pModelComponent && m_pModelComponent->IsBaseModelType())
	{
		Rainbow::MeshRenderer *pMeshRenderer = GetMeshRender();
		if (pMeshRenderer)
		{
			box = pMeshRenderer->GetWorldBounds();
		}

		return;
	}
	auto body = getBody();
	if (!body)
		return;

	WCoord min;
	WCoord max;
	WCoord dim;

	auto model = body->getModel();
	if (!model )
	{
		Rainbow::Entity* entity = body->getEntity();
		if (!entity)
			return;

		if (entity->GetBindObjectCount() > 0)
		{
			auto bindobj = entity->GetBindObject(0);
			ModelItemMesh* pModelMesh = dynamic_cast<ModelItemMesh*>(bindobj);
			if (pModelMesh)
			{
				model = pModelMesh->GetModel();
			}
		}

	}

	if (!model)
	{

		Rainbow::Entity* entity = body->getEntity();
		if (!entity)
			return;

		int count = entity->GetBindObjectCount();
		if (count <= 0)
			return;

		auto bindobj = entity->GetBindObject(0);
		BlockMesh* pBlockMesh = dynamic_cast<BlockMesh*>(bindobj);
		if (pBlockMesh != NULL)
		{
			SectionMesh* pMesh = pBlockMesh->getMesh();
			AABB blockBounds = pMesh->GetLocalAABB();
			//这里写死0，0的原因是放置实体的时候中心点是（0，50，0），而方块的的中心点是（50，50，50），使用方块中心点去AABB会偏移
			blockBounds.m_Center.x = 0;
			blockBounds.m_Center.z = 0;
			auto matrix = entity->GetWorldMatrix();
			TransformAABB(blockBounds, matrix, box);
		}
		else
		{
			auto trans = entity->GetTransform()->GetWorldTransform();
			AABB oriBound;
			center = Vector3f(0, 50, 0);
			extent = Vector3f(50, 50, 50);
			oriBound.SetCenterAndExtent(center, extent);
			TransformAABB(oriBound, trans, box);
		}

		return;
	}

	box = model->GetWorldBounds();
	return;
}


void ClientActor::ResetActorCollider(bool saveMesh)
{
	if (m_pModelComponent && m_pModelComponent->IsSkeletonModel())
		return;

	ResetHitBox();
	if (m_pPhysicsComponent)
	{
		m_pPhysicsComponent->ResetCollider(-1, saveMesh);
	}
}

//设置实体是否被运动器驱动
void ClientActor::SetControlByScript(bool b)
{
	m_bControlByScript = b;
}

//实体是否被运动器驱动
bool ClientActor::IsControlByScript()
{
	return m_bControlByScript;
}

void ClientActor::SetAsActorViewer(int r)
{
	m_AsActorViewer = true;
	m_AsViewerRadius = r;
	if (IsInWorld()) {
		if (!m_pViewerComponentActor) {
			m_pViewerComponentActor = CreateComponent<ViewerComponentActor>("ViewerComponentActor");
			m_pViewerComponentActor->SetRadius(r);
		}
		m_pViewerComponentActor->EnterWorld();
	}
}

int ClientActor::GetAsActorViewerDist()
{
	return m_AsViewerRadius;
}

/********** 判断野指针相关逻辑 start *******************/
std::unordered_map< ClientActor*,int> ClientActor::ms_ID2Ptr;

bool ClientActor::IsValid(ClientActor* pActor)
{
	return ms_ID2Ptr.find(pActor) != ms_ID2Ptr.end();
}
void ClientActor::ClearPtr2IDCache()
{
	ms_ID2Ptr.clear();
}
int ClientActor::genInstanceID()
{
	static int s_AddID = 0;
	return ++s_AddID;
}
/********** 判断野指针相关逻辑 end *******************/
ActorManagerInterface* ClientActor::GetActorMgrInterface()
{ 
	return getActorMgr(); 
}
bool ClientActor::CanNavigate()
{
	auto functionWrapper = getFuncWrapper();
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ClientActor* ator = RidComp->getRidingActor();
		if (ator)
		{
			auto functionWrapper = ator->getFuncWrapper();
			bool isCanSwimming = functionWrapper ? functionWrapper->getCanSwimming() : false;
			return ((ator->getLocoMotion() && ator->getLocoMotion()->m_OnGround) || (isCanSwimming && (ator->isInWater() || ator->HandleLavaMovement()))) && (functionWrapper && functionWrapper->getCanMove());
		}

		return false;
	}
	else
	{
		auto functionWrapper = getFuncWrapper();
		bool isCanSwimming = functionWrapper ? functionWrapper->getCanSwimming() : false;
		return ((getLocoMotion() && getLocoMotion()->m_OnGround) || (isCanSwimming && (isInWater() || HandleLavaMovement()))) && functionWrapper && (functionWrapper->getCanMove() || getClimbing());
	}
}

void ClientActor::ClearMoveForward()
{
	assert((dynamic_cast<LivingLocoMotion*>(getLocoMotion()) != NULL || dynamic_cast<TrixenieLocomotion*>(getLocoMotion()) != NULL));
	LivingLocoMotion* loco = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	if (loco)
	{
		loco->setMoveForward(0);
	}
	else
	{
		TrixenieLocomotion* tloco = dynamic_cast<TrixenieLocomotion*>(getLocoMotion());
		if (tloco)
		{
			tloco->setMoveForward(0);
		}
	}
}

WCoord ClientActor::GetActorPosition()
{
	WCoord pos = getLocoMotion()->getPosition();
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ClientActor* riding = RidComp->getRidingActor();
		if (riding)
		{
			pos = riding->getPosition();
		}
	}
	return pos;
}

int ClientActor::GetPathableYPos()
{
	World* pworld = getWorld();
	if (pworld == NULL)
	{
		return -BLOCK_SIZE;
	}
	ActorLocoMotion* loc = NULL;
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ClientActor* riding = RidComp->getRidingActor();
		if (riding)
		{
			loc = riding->getLocoMotion();
		}
	}

	if (!loc)
	{
		loc = getLocoMotion();
	}

	if (loc == NULL)
	{
		return -BLOCK_SIZE;
	}

	auto functionWrapper = getFuncWrapper();
	if (isInWater() && functionWrapper && functionWrapper->getCanSwimming())
	{
		WCoord pos = loc->getPosition();
		int y = loc->getBoundMinY() / BLOCK_SIZE;
		int x = CoordDivBlock(pos.x);
		int z = CoordDivBlock(pos.z);
		int blockid = pworld->getBlockID(x, y, z);
		int i = 0;

		do
		{
			if (!BlockMaterialMgr::isWater(blockid)/*blockid!=BLOCK_FLOW_WATER && blockid!=BLOCK_STILL_WATER*/)
			{
				return y * BLOCK_SIZE;
			}

			y++;
			blockid = pworld->getBlockID(x, y, z);
			i++;
		} while (i <= 16);

		return (loc->getBoundMinY() / BLOCK_SIZE) * BLOCK_SIZE;
	}
	else
	{
		return ((loc->getBoundMinY() + BLOCK_SIZE / 2) / BLOCK_SIZE) * BLOCK_SIZE;
	}
}

bool ClientActor::HasLivingLocoMotion()
{
	LivingLocoMotion* loco = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
	return loco ? true : false;
}
bool ClientActor::HasTrixenieLocomotion()
{
	TrixenieLocomotion* tloco = dynamic_cast<TrixenieLocomotion*>(getLocoMotion());
	return tloco ? true : false;
}

bool ClientActor::CanAttackByItemSkill(int skillId, IClientActor* player)
{
	return canAttackByItemSkill(skillId, dynamic_cast<ClientPlayer*>(player));
}
void ClientActor::HandleLocomotionForNavigation(int type, bool noPath)
{
	if (1 == type)
	{
		LivingLocoMotion* loco = dynamic_cast<LivingLocoMotion*>(getLocoMotion());
		//loco->setMoveForward(0);
		loco->clearTarget();
		auto RidComp = getRiddenComponent();
		if (!RidComp)
			return;
		ActorHorse* riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor()); // 坐骑
		if (!isPlayer() && !loco->m_InWater && !(RidComp && RidComp->isRidden()))
		{
			if (!riding)
			{
				loco->setJumping(false);
			}
		}
		if (!noPath && !riding)
		{
			ClientPlayer* player = dynamic_cast<ClientPlayer*>(this);
			if (player && player->isNewMoveSyncSwitchOn())
			{
				player->removeMoveStatus(IMT_Jump);
			}
		}
	}
	else if ( 2== type)
	{
		TrixenieLocomotion* tloco = getLocoMotion()->ToCast<TrixenieLocomotion>();
		//tloco->setMoveForward(0);
		tloco->clearTarget();
		auto RidComp = getRiddenComponent();
		if (!tloco->m_InWater && !(RidComp && RidComp->isRidden()))
		{
			tloco->setJumping(false);
		}
	}
}

void ClientActor::SetJumpToTarget(const WCoord& target)
{
	LivingLocoMotion* loco = dynamic_cast<LivingLocoMotion*>(getLocoMotion());

	if (loco)
	{
		loco->setJumpToTarget(target);
	}
	else
	{
		TrixenieLocomotion* tloco = dynamic_cast<TrixenieLocomotion*>(getLocoMotion());

		if (tloco)
		{
			tloco->setJumpToTarget(target);
		}
	}
}

void ClientActor::SetTarget(const WCoord& target, float speed)
{
	LivingLocoMotion* loco = dynamic_cast<LivingLocoMotion*>(getLocoMotion());

	if (loco)
	{
		loco->setTarget(target, speed);
	}
	else
	{
		TrixenieLocomotion* tloco = dynamic_cast<TrixenieLocomotion*>(getLocoMotion());

		if (tloco)
		{
			tloco->setTarget(target, speed);
		}
	}
}
IActorLocoMotion* ClientActor::getILocoMotion()
{
	return getLocoMotion();
}

bool ClientActor::HasPhysActor()
{
	PhysicsLocoMotion* loc = dynamic_cast<PhysicsLocoMotion*>(getLocoMotion());
	if (loc && loc->m_hasPhysActor)
		return true;
	else
		return false;
}
MNSandbox::Component* ClientActor::getActorSoundComponent()
{
	return m_pSoundComponent;
}
MNSandbox::Component* ClientActor::getLocoMotionComponent()
{
	return getLocoMotion();
}
MNSandbox::Component* ClientActor::getAttribComponent()
{
	return getAttrib();
}
IActorAttrib* ClientActor::GetIActorAttrib()
{
	return getAttrib();
}
IClientActor* ClientActor::IClone()
{
	return clone();
}

IClientPlayer* ClientActor::CastToPlayer()
{
	return dynamic_cast<ClientPlayer*>(this);
}

MNSandbox::Component* ClientActor::getActorComponent(ComponentType type)
{
	MNSandbox::Component* component = nullptr;
	switch (type)
	{
	case ComponentType::COMPONENT_ACTOR_LIVING_ATTRIB:
		component = getLivingAttrib();
		break;
	case ComponentType::COMPONENT_RIDDEN:
		component = getRiddenComponent();
		break;
	case ComponentType::COMPONENT_FISHING:
		component = getFishingComponent();
		break;
	case ComponentType::COMPONENT_TO_ATTACK_TARGET:
		component = GetComponent<ToAttackTargetComponent>();
		break;
	case ComponentType::COMPONENT_PLAYER_ATTRIB:
		component = getPlayerAttrib();
		break;
	case ComponentType::COMPONENT_ACTOR_IN_PORTAL:
		component = getActorInPortal();
		break;
	case ComponentType::COMPONENT_PHYSICS:
		component = getPhysicsComponent();
		break;
	case ComponentType::COMPONENT_SOUND:
		component = getSoundComponent();
		break;
	case ComponentType::COMPONENT_ATTACK:
		component = getAttackedComponent();
		break;
	case ComponentType::COMPONENT_THORN_BALL:
		component = getThornBallComponent();
		break;
	case ComponentType::COMPONENT_EFFECT:
		component = getEffectComponent();
		break;
	case ComponentType::COMPONENT_BIND_ACTOR:
		component = getBindActorCom();
		break;
	default:
		break;
	}
	return component;
}

bool ClientActor::IsClientActorProjectile()
{
	return dynamic_cast<ClientActorProjectile*>(this) ? true : false;
}
bool ClientActor::IsActorVehicleAssemble()
{
	return dynamic_cast<ActorVehicleAssemble*>(this) ? true : false;
}
bool ClientActor::AttackedFrom(OneAttackData& atkdata, IClientActor* inputattacker)
{
	if (inputattacker)
	{
		return attackedFrom(atkdata, static_cast<ClientActor*>(inputattacker));
	}
	else
		return attackedFrom(atkdata, nullptr);
}

void* ClientActor::saveToBuffer(size_t& buflen)
{
	flatbuffers::FlatBufferBuilder builder;
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	s_SaveActorCommonForSyncFlag = true;//打个标记 用于 saveActorCommon 区分存档还是同步
#endif
	auto s = saveToNet(builder);
#ifdef SURVIVAL_ACTOR_SUPPORT_SANDBOX_NODEEX
	s_SaveActorCommonForSyncFlag = false;
#endif
	builder.Finish(s);

	unsigned char* src = builder.GetBufferPointer();
	size_t srclen = builder.GetSize();

	void* buffer = malloc(srclen);
	memcpy(buffer, src, srclen);

	buflen = srclen;
	return buffer;
}

ClientActor* ClientActor::createFromBuffer(const void* buffer, int version)
{
	const FBSave::SectionActor* s = flatbuffers::GetRoot<FBSave::SectionActor>(buffer);
	if (!s)
		return NULL;
	ClientActor* obj = MNSandbox::ToCast<ClientActor>(ActorBase::createActorFromFB(s));//CreateActorFromUnionType(s->actor_type());
	assert(obj);
	if (s == NULL) return NULL;
	if (obj && obj->loadFromNet(s->actor(), version))
	{
		return obj;
	}
	else
	{
		if (obj) obj->release();
		return NULL;
	}
}

flatbuffers::Offset<FBSave::SectionActor> ClientActor::saveToNet(SAVE_BUFFER_BUILDER& builder)
{
	return save(builder);
}

bool ClientActor::loadFromNet(const void* srcdata, int version)
{
	return load(srcdata, version);
}


#ifdef ENABLE_PLAYER_CMD_COMMAND
#include "CombatCheckExtend.h"
bool ClientActor::ms_enableDebugWrapBpx = false;
//gm 命令绘制生物包围盒.
void ClientActor::gmDrawWrapBox()
{
	CombatCheckExtend::drawActorBox(this, m_selfAtkBoxInfo);
}
#endif

bool ClientActor::isRun()
{
	ActorLocoMotion* pLocoMotion = getLocoMotion();
	if (pLocoMotion)
		return pLocoMotion->m_InRun || getRun();
	else
		return getRun();
}

std::string ClientActor::GetComponentFromC()
{
	std::string ret = "";

	if (!m_bDelayInit)
	{
		DelayInitData(true);
		if (m_pScriptComponent)
		{
			return ret;
		}
	}

	bool actorNeedComp = false;
	int objType = getObjType();
	if (objType == OBJ_TYPE_ROLE || objType == OBJ_TYPE_MONSTER || objType == OBJ_TYPE_GAMEOBJECT)
	{
		actorNeedComp = true;
	}

	//拼接lua组件所需字符串, "{\"c\":[[\"Transform\",1,{}],[\"Model\",1,{}],[\"Physics\",1,{}],[\"Effect\",1,{}],[\"Sound\",1,{}]],\"v\":2}"
	if (actorNeedComp)
	{
		bool hasModel = getModelComponent() != nullptr;
		bool hasPhysics = getPhysicsComponent() != nullptr;
		bool hasEffect = getEffectComponent() != nullptr;
		bool hasSound = getSoundComponent() != nullptr;

		if (!m_pScriptComponent)
		{
			m_pScriptComponent = CreateComponent<ScriptComponent>("ScriptComponent", false);
		}

		ret = "{\"c\":[[\"Transform\",1,{}]";

		if (hasModel)
		{
			ret.append(",[\"Model\",1,{}]");
		}

		if (hasPhysics)
		{
			ret.append(",[\"Physics\",1,{}]");
		}

		if (hasEffect)
		{
			ret.append(",[\"Effect\",1,{}]");
		}

		if (hasSound)
		{
			ret.append(",[\"Sound\",1,{}]");
		}

		ret += "], \"v\":2}";
	}

	return ret;
}

void ClientActor::TriggerScriptComponent(const std::vector<int>& enterList)
{
	auto pScriptComponent = getScriptComponent();
	if (pScriptComponent)
	{
		std::for_each(enterList.begin(), enterList.end(), [pScriptComponent](int nUin)
			{
				pScriptComponent->OnEvent((unsigned int)CE_OnPlayerEnterAOI, true, nUin);
			});
	}
}

