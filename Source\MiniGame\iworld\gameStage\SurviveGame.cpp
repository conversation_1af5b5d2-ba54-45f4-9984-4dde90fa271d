#include "SurviveGame.h"
#include "ClientActor.h"
#include "ClientGame.h"
#include "Input/OgreInputManager.h"
#include "PlayerControl.h"
#include "world.h"
#include "WorldRender.h"
#include "backpack.h"
#include "GameCamera.h"
#include "BlockScene.h"
#include "MiniDeveloper/Resource/BluePrint/UGCBluePrint.h"
#include "MiniDeveloper/Resource/BluePrint/UGCBluePrintMgr.h"

#include "ui_scriptfunc.h"
#include "Platforms/PlatformInterface.h"
#include "OgreStringUtil.h"
//#include "OgreSceneManager.h"
//#include "OgreRoot.h"
#include "ClientActorManager.h"
#include "WorldMeta.h"
#include "ActorAttrib.h"
#include "LivingLocoMotion.h"
#include "EffectManager.h"
#include "EffectParticle.h"
#include "container_world.h"
#include "ChunkGenerator.h"
#include "GameEvent.h"
#include "block_tickmgr.h"
#include "voxelmodel.h"
#include "special_blockid.h"
#include "BlockPortal.h"
#include "container_signs.h"
#include "CustomModelMgr.h"
#include "IRecordInterface.h"
#include "ClientGameManager.h"
//#include "TriggerScriptMgr.h"
#include "FullyCustomModelMgr.h"
#include "ImportCustomModelMgr.h"
#include "ResourceCenter.h"
#include "PlanarReflectionEffect.h"
#include "ActorExpOrb.h"
#include "ClientActorArrow.h"
#include "ActorBomb.h"
#include "ActorFallingSand.h"
#include "ActorFlyingBlock.h"
#include "ActorDragon.h"
#include "ActorEarthCoreMan.h"
#include "ActorBody.h"
#include "Components/Base/ModelComponent.h"

#include "chunk.h"
#include "Graphics/Texture.h"
#include "DebugDataMgr.h"
#include "ActorCSProto.h"
#include "ClientItem.h"
#include "DefManagerProxy.h"
#include "navigationpath.h"
#include "ClientMob.h"
#include "TouchControlLua.h"
#include "MinimapRenderer.h"
#include "WorldManager.h"
#include "GameMode.h"
#include "CameraModel.h"
#include "Entity/OgreEntity.h"
#include "OgreTimer.h"
#include "Sound/OgreSoundSystem.h"
#include "OgreUtils.h"
//#include "Profiny.h"

#include "GameNetManager.h"
#include "CameraManager.h"

#include "PCControlLua.h"
#include "proto_define.h"
#include "proto_common.h"
#include "ObserverEventManager.h"
#include "CustomModelPacking.h"
#include "CustomMotionMgr.h"

#include "File/DirVisitor.h"

#include "StarStationTransferMgr.h"

//#include "TaskSubSystem.h"
#include "RoomClient.h"
#include "WorldSceneManager.h"
#include "SandboxEventDispatcherManager.h"

#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientInfoProxy.h"
#include "GameInfoProxy.h"
#include "Core/GameEngine.h"
#include "Sound/MusicManager.h"
#include "Graphics/ScreenManager.h"
#include "PlayerControlLua.h"
#include "PlayerAttrib.h"

#include "GameSuviveNetHandler.h"
#include "GameSuviveLoadHandler.h"
#include "GameSuviveUiHandler.h"
#include "Entity/OgreEntityMotionData.h"
#include "ClientApp.h"
#include "Event/MiniGlobalEvent.h"

#include "BlockMaterialMgr.h"
#include "RiddenComponent.h"
#include "IWorldConfigProxy.h"


#ifdef BUILD_MINI_EDITOR_APP
#include "EditorControl.h"
#include "EditActorRender.h"
#include "sandboxConfig/SandboxCfg.h"
#include "EditorCbeScene.h"
#include "EditorCmScene.h"
#include "EditorBluePrintScene.h"
#include "EditorBlockSystem/Extend/NewBluePrintScene.h"
#ifdef STUDIO_LINK_EDITOR
#include "EditorStatusUtils.h"
#endif //STUDIO_LINK_EDITOR
#endif //BUILD_MINI_EDITOR_APP

//#include "SandboxCoreUIService.h"
#include "SandboxGameSetNode.h"
#include "SandboxGlobalNotify.h"
#include "display/SandboxRenderSetting.h"
#include "ClientGameReport.h"
#include "TeamSetterComponent.h"
#include "TemperatureManager.h"
#include "SandboxCore.h"
#include "SandboxCameraObject.h"
#include "Optick/optick.h"
#include "GameConfiguration.h"
#include "GameConfigMgr.h"
#include "ClientGameReport.h"
#include "ClientGameStatistics.h"
#include "Render/LOD/LODGroupManager.h"
#include "ZmqProxy.h"
#include "SandboxActorSubsystem.h"
#include "Utils/thinkingdata/GameAnalytics.h"

#if USE_MEMORY_PROFILER
#include "Debug/DebugMgr.h"
#endif
#include <ClientAccount.h>

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;
extern EffectParticle *s_Effect;
static PlayerBriefInfo myBriefInfo;
const int LOAD_COMPLETE = 80;

//#include "OgreEntityMotionData.h"

// 记录SurviveGame的生命周期
// 有的错误的客户端逻辑会在OnInputEvent()中析构掉SurviveGame，造成不可预知的错误（比如m_PlayerCtrl成为野指针）。
// 该静态变量对此加以保护
static bool g_ClientGameLive = false;

static float s_bright = 0;
EntityMotionData *IMPLEMENT_RTTI_pdata;
EXPORT_SANDBOXGAME extern int g_all_projectile;

static void IMPLEMENT_RTTI_Init() //用来确定各个模块文件的IMPLEMENT_RTTI被调用， 需要吗？？？
{
	IMPLEMENT_RTTI_pdata = ENG_NEW(EntityMotionData)();
	IMPLEMENT_RTTI_pdata->Release();
}

static int CompassDistToScreen(int d)
{
	const float R = BLOCK_FSIZE;
	return int(d / R);
}

static void AddInitSurviveItems(ClientPlayer *player)
{
	BackPack *pack = player->getBackPack();
	if (pack == NULL) return;
	int n = player->getBody()->getModelID() - 1;

#ifndef IWORLD_SERVER_BUILD
	assert(n >= 0 && n < 10);
#endif

	if (n >= 0 && n < 10)
	{
		std::vector<CharacterDef> &charDefTable = GetDefManagerProxy()->getCharDefTable();
		const CharacterDef &def = charDefTable[n];

		for (int i = 0; i < MAX_CHARINIT_ITEMS; i++)
		{
			int itemId = def.ItemID[i];
			int itemNum = def.ItemNum[i];
			int itemEquip = def.ItemEquip[i];
			int equiPart = def.EquipPart[i];
			if (itemId > 0 && itemNum > 0)
			{
				if (itemEquip == 1)
				{
					pack->addItemToEquip(itemId, equiPart, 1);
				}
				else
				{
					pack->addItem(itemId, itemNum);
				}
			}
		}
	}
	else
	{
		LOG_INFO("AddInitSurviveItems n>=0 && n<10");
	}
}

static void AddInitEnchants(BackPack *pack, int itemid, int itemnum, int id1 = 0, int lv1 = 0, int id2 = 0, int lv2 = 0, int id3 = 0, int lv3 = 0)
{
	int s_InitEnchants[16];
	int count = 0;
	if (id1 > 0)
	{
		s_InitEnchants[count++] = id1 * 100 + lv1;
	}
	if (id2 > 0)
	{
		s_InitEnchants[count++] = id2 * 100 + lv2;
	}
	if (id3 > 0)
	{
		s_InitEnchants[count++] = id3 * 100 + lv3;
	}

	for (int i = 0; i < itemnum; i++)
	{
		//		pack->addItemWithPickUp(itemid, 1, -1, count, s_InitEnchants);
		int gidx = pack->getEmptyBagIndex();
		if (gidx < 0) gidx = pack->getEmptyShortcutIndex();
		if (gidx < 0) return;

		int dur = -1;
		auto def = GetDefManagerProxy()->getToolDef(itemid);
		if (def) dur = def->Duration;

		//pack->replaceItem(gidx, itemid, 1, dur);
		GridCopyData data;
		data.resid = itemid;
		data.num = 1;
		data.duration = dur;
		pack->replaceItem_byGridCopyData(data, gidx);
		for (int i = 0; i < count; ++i) pack->enchant(gidx, s_InitEnchants[i]);
	}
}

static void AddInitSurviveItems2(ClientPlayer *player)
{
	BackPack *pack = player->getBackPack();
	//pack->addItem(11001, 1);

	AddInitEnchants(pack, 11005, 1, 15, 5, 25, 5);
	AddInitEnchants(pack, 11015, 3, 15, 5, 25, 5);
	AddInitEnchants(pack, 11055, 1);
	AddInitEnchants(pack, 12005, 2, 6, 5, 15, 5);
	AddInitEnchants(pack, 12050, 3, 13, 5, 14, 5, 15, 5);
	AddInitEnchants(pack, 12241, 1, 19, 5, 15, 5);
	AddInitEnchants(pack, 12242, 1, 19, 5, 15, 5);
	AddInitEnchants(pack, 12243, 1, 19, 5, 15, 5);
	AddInitEnchants(pack, 12244, 1, 19, 5, 15, 5);

	pack->addItem(104, 256);
	pack->addItem(200, 192);
	pack->addItem(8, 64);
	pack->addItem(12550, 64);
	pack->addItem(12556, 64);
	pack->addItem(12557, 64);
	pack->addItem(12555, 64);
	pack->addItem(12558, 64);
	pack->addItem(12559, 64);
	pack->addItem(12560, 64);
}

// todo: 临时添加变量，解决在SurviveGame::OnInputEvent()里自己销毁自己而产生的问题
bool g_isSurviveGameNull = false;

SurviveGame::SurviveGame() : 
m_OpenCmd(false),
m_bHaveJudge(false),
m_TickCount(0)
{
	ClientActor::ClearPtr2IDCache();
#ifndef IWORLD_SERVER_BUILD
	IMPLEMENT_RTTI_Init();
	ScriptVM::game()->callFunction("CheckLoadEnterRoomToc", NULL);
#ifdef IWORLD_DEV_BUILD
	m_OpenCmd = true;
#endif

#ifdef _WIN32
	if(!m_OpenCmd)
		m_OpenCmd = GetClientApp().checkIsGMWhiteListMember(); //优先检查白名单
#endif

#endif
	m_isInSetting = false;

	g_isSurviveGameNull = false;

	g_ClientGameLive = true;
}

SurviveGame::~SurviveGame()
{
	g_ClientGameLive = false;

	if (m_PlayerCtrl)
	{
		m_PlayerCtrl->release();
		m_PlayerCtrl = NULL;
	}
	//ClientGameManager::clearCurGame中有调用unload这里的unload重复了 code-by:hanyunqiang
	//unload();
	tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "SurviveGame");
	g_isSurviveGameNull = true;
	ClientActor::ClearPtr2IDCache();
}

const char *SurviveGame::getName()
{
	return "SurviveGame";
}

const char *SurviveGame::getTypeName()
{
	return "SurviveGame";
}

//extern std::string hoverEffectClass;
//extern std::string hoverClientActorClass;
//extern int effectTickCount;
//extern int hoverClientActorDistance;
int SurviveGame::getDebugInfo(char *buffer, int buflen)
{
	ClientStatus status;
	float yaw = .0f;
	float pitch = .0f;

	memset(&status, 0, sizeof(status));
	if (m_PlayerCtrl)
	{
		m_PlayerCtrl->getClientStatus(status);
		yaw = m_PlayerCtrl->getLocoMotion()->m_RotateYaw;
		pitch = m_PlayerCtrl->getLocoMotion()->m_RotationPitch;
	}

	assert(m_PlayerCtrl);
	World *pworld = m_PlayerCtrl->getWorld();
	float daytime = pworld->getHours();
	int hour = int(daytime);

	const char *biomename = "";
	int biomeid = 0;
	const char *airlandbiomename = "Empty";
	int airlandbiomeid = 0; 
	int posHeight = 0;
	float ConfigminHeight = 0;
	float ConfigmaxHeight = 0;
	Chunk *pchunk = pworld->getChunk(status.curpos);
	if (pchunk)
	{
		const BiomeDef *biome = pchunk->getBiome(status.curpos.x - pchunk->m_Origin.x, status.curpos.z - pchunk->m_Origin.z);
		if (biome) {
			biomename = biome->Name.c_str();
			biomeid = biome->ID;
		}
		posHeight = pchunk->getTopHeight(status.curpos.x - pchunk->m_Origin.x, status.curpos.z - pchunk->m_Origin.z);

		ConfigminHeight = biome->MinHeight;
		ConfigmaxHeight = biome->MaxHeight;
		const BiomeDef *airlandbiome = pchunk->getAirLandBiome(status.curpos.x - pchunk->m_Origin.x, status.curpos.z - pchunk->m_Origin.z);
		if (airlandbiome && airlandbiome->ID != 0) {
			airlandbiomename = airlandbiome->Name.c_str();
			airlandbiomeid = airlandbiome->ID;
		}
	}

	BlockScene *pscene = pworld->getScene();
	Rainbow::Vector3f vel = status.motion / (GAME_TICK_TIME*100.0f);
	int bufpos = 0;
	bufpos += snprintf(buffer + bufpos, buflen - bufpos,
		"  |Yaw:%.4f|Pitch:%.4f|SEC:%d|%d|%d|FOV:%.2f| BIOME:%s[%d] | REAL_HEIGHT:%d | CONFIG_HEIGHT:%.4f/%.4f |  ",
		yaw, pitch,
		BlockDivSection(status.curpos.x), BlockDivSection(status.curpos.y), BlockDivSection(status.curpos.z),
		m_PlayerCtrl->getCamera()->getFov(), biomename, biomeid, posHeight, ConfigminHeight, ConfigmaxHeight);
	//bufpos += snprintf(buffer + bufpos, buflen - bufpos, 
	//	"POS:[%4d|%4d|%4d]|Yaw:%.4f|Pitch:%.4f|SEC:%d|%d|%d|FOV:%.2f|BIOME:%s[%d]|AIRLAND_BIOME:%s[%d] ",
	//	status.curpos.x, status.curpos.y, status.curpos.z,
	//	yaw, pitch,
	//	BlockDivSection(status.curpos.x), BlockDivSection(status.curpos.y), BlockDivSection(status.curpos.z),
	//	m_PlayerCtrl->getCamera()->getFov(), biomename, biomeid, airlandbiomename, airlandbiomeid);
	bufpos += snprintf(buffer + bufpos, buflen - bufpos,
		"/n  |TIME:%2d:%2d|SECTION:%d/%d|OBJ:%d/%d | ", hour, int((daytime - hour) * 60),
		pscene->m_FrameRenderSections, pscene->m_FrameSections, pscene->m_FrameRenderObjs, pscene->m_FrameObjs);

	int sunlt = pworld->getBlockSunIllum(status.curpos);
	int blocklt = pworld->getBlockLightByType(1, status.curpos);
	int lt = pworld->getBlockLightValue(status.curpos);
	bufpos += snprintf(buffer + bufpos, buflen - bufpos, " | Light:%d/%d/%d, | ", sunlt, blocklt, lt);
	//bufpos += snprintf(buffer + bufpos, buflen - bufpos, "TIME:%2d:%2d|SECTION:%d/%d|OBJ:%d/%d", hour, int((daytime - hour) * 60), pscene->m_FrameRenderSections,
	//	pscene->m_FrameSections, pscene->m_FrameRenderObjs, pscene->m_FrameObjs);

	//int sunlt = pworld->getBlockSunIllum(status.curpos);
	//int blocklt = pworld->getBlockLightByType(1, status.curpos);
	//int lt = pworld->getBlockLightValue(status.curpos);
	//bufpos += snprintf(buffer + bufpos, buflen - bufpos, "Light:%d/%d/%d, ", sunlt, blocklt, lt);

	//auto octreeMemSize = pworld->getActorMgr()->ToCastMgr()->GetActorMGT()->GetMemorySize();
	//auto octreeElementsCount = pworld->getActorMgr()->ToCastMgr()->GetActorMGT()->GetElementsCount();
	//auto octreeNodes = pworld->getActorMgr()->ToCastMgr()->GetActorMGT()->GetTreeNodes();
	//auto eoctreeMemSize = pworld->getEffectMgr()->GetEffectMGT()->GetMemorySize();
	//auto eoctreeElementsCount = pworld->getEffectMgr()->GetEffectMGT()->GetElementsCount();
	//auto eoctreeNodes = pworld->getEffectMgr()->GetEffectMGT()->GetTreeNodes();
	//bufpos += snprintf(buffer + bufpos, buflen - bufpos, "AO:%d/%d/%d, EO:%d/%d/%d, ", 
	//	octreeNodes, octreeElementsCount, octreeMemSize/1024,
	//	eoctreeNodes, eoctreeElementsCount, eoctreeMemSize / 1024);
	 
	//if (!hoverClientActorClass.empty()) {
	//	bufpos += snprintf(buffer + bufpos, buflen - bufpos, "[%s] dis:%d, ", hoverClientActorClass.c_str(), hoverClientActorDistance);
	//}
	//if (!hoverEffectClass.empty()) {
	//	bufpos += snprintf(buffer + bufpos, buflen - bufpos, "[%s], ", hoverEffectClass.c_str());
	//}

	PlayerAttrib *attr = m_PlayerCtrl->getPlayerAttrib();
	int score = 0;
	MNSandbox::GetGlobalEvent().Emit<int&, long long>("ClientAccountMgr_getOWScore", score, m_PlayerCtrl->getOWID());
	const float maxHP = attr->getMaxHP();
	const float overflowHP = attr->getOverflowHP();
	const float maxStrength = attr->getMaxStrength();
	const float overflowStrength = attr->getOverflowStrength();
	const int waterpress = attr->getWaterPressure();
	const int waterDepth = attr->m_WaterDepth;
	const int lowerwaterpress = attr->getLowerBodyWaterPressuer();
	const int lowerwaterDepth = attr->m_LowerWaterDepth;
	const float posTemp = attr->getFinalPosTemperature();
	const float temp = attr->getTemperature();

	bufpos += snprintf(buffer + bufpos, buflen - bufpos,
		" | WaterPress:%d/%d(%d/%d) | Temp: %4.3f | pTemp: %4.3f",
		waterpress,
		lowerwaterpress,
		waterDepth,
		lowerwaterDepth,

		temp,
		posTemp
	);

	//bufpos += snprintf(buffer + bufpos, buflen - bufpos,
	//	"\nHP: %4.3f/%d(%d+%d) | Strength: %4.3f/%d(%d+%d)(%s) | Hunger: %d/%d | WaterPress:%d/%d(%d/%d) | Score:%d | Proj:%d | Temp: %4.3f | pTemp: %4.3f",
	//	attr->getHP(),
	//	int(maxHP),
	//	int(maxHP),
	//	int(overflowHP),

	//	attr->getStrength(),
	//	int(maxStrength + overflowStrength),
	//	int(maxStrength),
	//	int(overflowStrength),
	//	attr->useCompatibleStrength() && attr->strengthFoodShowState() != SFS_Empty ? "yes" : "no",

	//	attr->getFoodLevel(),
	//	attr->getFoodMaxLevel(),

	//	waterpress,
	//	lowerwaterpress,
	//	waterDepth,
	//	lowerwaterDepth,

	//	score,
	//	g_all_projectile,
	//	temp,
	//	posTemp
	//);

	if (m_PlayerCtrl->getCurOperate() == PLAYEROP_DIG)
	{
		bufpos += snprintf(buffer + bufpos, buflen - bufpos, "\nCurDigTick: %d, TotalDigTick: %d", m_PlayerCtrl->getOperateTicks(), m_PlayerCtrl->getOperateTotalTicks());
	}

	WCoord bosspos;
	if (m_PlayerCtrl->getWorld()->getChunkProvider()->getBossInfo(bosspos))
	{
		bufpos += snprintf(buffer + bufpos, buflen - bufpos, "\nBOSS: %d,%d,%d ", bosspos.x, bosspos.y, bosspos.z);
	}
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->getTemperatureMgr() && static_cast<TemperatureManager*>(GetWorldManagerPtr()->getTemperatureMgr())->m_ShowData)
	{
		TemperatureManager* tempMgr = static_cast<TemperatureManager*>(GetWorldManagerPtr()->getTemperatureMgr());
		World* world = g_pPlayerCtrl->getWorld();
		WCoord pos = CoordDivBlock(g_pPlayerCtrl->getPosition());
		const float eTemp = tempMgr->GetEnviromentTemperature(world, pos);
		const float bTemp = tempMgr->GetBiomeTemperature(world, pos);
		const float hTemp = tempMgr->GetHeightTemperature(world, pos);
		const float wTemp = tempMgr->GetWeatherTemperature(world, pos);
		const float sTemp = tempMgr->GetSourceTemperature(world, pos);
		//const float aTemp = tempMgr->GetAreaSourceTemperature(world, pos);

		bufpos += snprintf(buffer + bufpos, buflen - bufpos, "\nTemperature= Env:%4.3f | Biome:%4.3f | H:%4.3f | Weather:%4.3f | Source:%4.3f ",
			eTemp, bTemp, hTemp, wTemp, sTemp);
	}

	return bufpos;
}

void SurviveGame::applayGameSetData(bool viewchange /* = false */)
{
	//if (true)
	//{//小镇地图摄像机逻辑有问题测试时先手动注释
	//	return;
	//}
	GetIWorldConfigProxy()->SetSoundSystem();
#ifndef IWORLD_SERVER_BUILD
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("Fmod_setFmodSoundSystem");
#endif
	//XMLNode node = Root::getSingleton().m_Config.getRootNode().getChild("GameData");

	XMLNode node = GetIWorldConfigProxy()->getRootNode().getChild("GameData");
	if (!node.isNull())
	{
		XMLNode child = node.getChild("Settinig");
		if (!child.isNull())
		{
			int mode = child.attribToInt("view") - 1;
			if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode())
			{
				int defcam = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA);
				if (/*8 == defcam || */9 == defcam)
				{
					mode = CAMERA_TPS_BACK_2;
				}
				else if (defcam >= 3 && defcam <= 5)
					mode = defcam - 3;
				else if (6 == defcam || 7 == defcam)
				{
					mode = defcam - 2;
				}
				else if (/*10 == defcam || */11 == defcam)
				{
					mode = defcam - 6;
				}
			}
			if (g_pPlayerCtrl)
			{
				/*if ((g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_FOOTBALLER || g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_BASKETBALLER || g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL) && mode < 4 && !viewchange)
				{
					if (g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
					{
						g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
					}
					else if (g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
					{
						g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK_2);
					}
					else if (g_pPlayerCtrl->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
					{
						g_pPlayerCtrl->setViewMode(CameraControlMode::CAMERA_TPS_BACK);
					}
				}
				else */if (viewchange)
				{
					g_pPlayerCtrl->setViewMode(mode);
				}
				
				if (GetClientInfoProxy()->isMobile())
				{
					TouchControl::GetInstance().setSensitivity(child.attribToInt("sensitivity"));
					TouchControl::GetInstance().setReversalY(child.attribToInt("reversalY"));
					TouchControl::GetInstance().setSightModel(child.attribToInt("sight"));

					if (child.hasAttrib("rocker"))
						TouchControl::GetInstance().setRockerControl(child.attribToInt("rocker"));
				}
				else
				{
					if (PCControl::GetInstancePtr()) {
						PCControl::GetInstance().setSensitivity(child.attribToInt("sensitivity"));
						PCControl::GetInstance().setReversalY(child.attribToInt("reversalY"));
						//PCControl::getSingleton().setSightModel(child.attribToInt("sight"));	
					}
				}
			}
#ifdef BUILD_MINI_EDITOR_APP
			ClientPlayer::SetViewRangeSetting(child.attribToInt("view_distance") * 2);
#endif
			MNSandbox::Core::SystemSetChange();
			//m_PlayerCtrl->getWorld()->getEffectMgr()
			// float brightness = child.attribToInt("screenbright") / 100.0f;
			// if (brightness != s_bright)
			// {
			// 	s_bright = brightness;
			// 	SetScreenBrightness(brightness);
			// }
			//if (child.hasAttrib("treeshape"))
			//{
			//	if (child.attribToInt("treeshape") == 0)
			//		BlockMaterialMgr::m_LeafUseCube = true;
			//	else
			//		BlockMaterialMgr::m_LeafUseCube = false;
			//}

#ifdef IWORLD_REALTIME_SHADOW
			bool oldreflect = SceneManager::getSingleton().isReflectmapEnable();
			SceneManager::getSingleton().setShadowmap(child.hasAttrib("shadow") && child.attribToInt("shadow") > 0);
			if (!isInGame() || !m_hasBegan) SceneManager::getSingleton().setReflectmap(child.hasAttrib("reflect") && child.attribToInt("reflect") > 0);
			/*
			if(isInGame() && m_hasBegan && oldreflect != SceneManager::getSingleton().isReflectmapEnable())
			{
				g_BlockMtlMgr.resetMaterial(BLOCK_FLOW_WATER);
				g_BlockMtlMgr.resetMaterial(BLOCK_STILL_WATER);

				if(m_PlayerCtrl && m_PlayerCtrl->getWorld())
				{
					WCoord center = CoordDivBlock(m_PlayerCtrl->getPosition());
					int r = 16*4;
					m_PlayerCtrl->getWorld()->markBlockForUpdate(WCoord(center.x-r, 0, center.z-r), WCoord(center.z+r, 255, center.z+r), false);
				}
			}*/
#endif
			//老版本的画面配置
			//ClientPlayer::SetViewRangeSetting(child.attribToInt("view_distance") * 2); //ClientPlayer::m_ViewRangeSetting = child.attribToInt("view_distance") * 2;
			//default value 1 medium level
			//int graphicQuality = 1;
			//if (child.hasAttrib("graphic_setting")) 
			//{
			//	int curvalue = child.attribToInt("graphic_setting") - 1;
			//	if (curvalue >= 0 && curvalue < 3) 
			//	{
			//		graphicQuality = curvalue;
			//	}
			//}
			//SetSandboxGraphicQuality(graphicQuality, false);
			//if (child.hasAttrib("fog")) 
			//{
			//	int value = child.attribToInt("fog");
			//	if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame() && WorldRenderer::m_CurFogIndex > 0)// studio设置
			//	{
			//		value = WorldRenderer::m_CurFogIndex;
			//	}
			//	else
			//		WorldRenderer::m_CurFogIndex = value;
			//	Rainbow::FogType fogType = value == 0 ? Rainbow::FogType::kFogTypeDisable : Rainbow::FogType::kFogTypeLinear;
			//	GetRenderSetting().GetFogConfig().m_FogType = (fogType);
			//}
			//else 
			//{
			//	GetRenderSetting().GetFogConfig().m_FogType = Rainbow::FogType::kFogTypeDisable;
			//}
			//int sandboxShadow = -1;
			//int sandboxWaterRelect = -1;
			//GlobalNotify::GetInstance().GetShadowAndWaterReflected.Emit(sandboxShadow, sandboxWaterRelect);
			//if (child.hasAttrib("reflect"))
			//{
			//	int value = child.attribToInt("reflect");
			//	value = sandboxWaterRelect < 0 ? value : sandboxWaterRelect;
			//	GetPlanarReflection().SetEffectEnable(value != 0);
			//}
			//else 
			//{
			//	GetPlanarReflection().SetEffectEnable(false);
			//}
			//if (child.hasAttrib("shadow")) 
			//{
			//	int value = child.attribToInt("shadow");
			//	value = sandboxShadow < 0 ? value : sandboxShadow;
			//	bool isOpen = value == 1;
			//	bool preOpen = WorldRenderer::m_CurShadowOpen;
			//	WorldRenderer::m_CurShadowOpen = isOpen;
			//	WorldRenderer::SetShadowEnable(isOpen);
			//}
			//else 
			//{
			//	WorldRenderer::SetShadowEnable(WorldRenderer::m_CurShadowOpen);
			//}
			//if (child.hasAttrib("high_skyplane"))
			//{
			//	GetSandboxRenderSetting().SetSkyboxLevel((SkyboxLevel)child.attribToInt("high_skyplane"));
			//}
			//if (child.hasAttrib("caustics"))
			//{
			//	GetSandboxRenderSetting().SetCausticsEnable(child.attribToInt("caustics") == 1);
			//}
			//else 
			//{
			//	GetSandboxRenderSetting().SetCausticsEnable(false);
			//}
			//if (child.hasAttrib("godray"))
			//{
			//	if (g_WorldMgr && !g_WorldMgr->isNewSandboxNodeGame())
			//		GetSandboxRenderSetting().SetGodrayEnable(child.attribToInt("godray") == 1);
			//}
			//else 
			//{
			//	if (g_WorldMgr && !g_WorldMgr->isNewSandboxNodeGame())
			//		GetSandboxRenderSetting().SetGodrayEnable(false);
			//}
			//if (child.hasAttrib("plantflutter"))
			//{
			//	GetSandboxRenderSetting().SetGrassAnimationEnable(child.attribToInt("plantflutter") == 1);
			//}
			//else 
			//{
			//	GetSandboxRenderSetting().SetGrassAnimationEnable(false);
			//}
		}
	}
	//将游戏图像设置映射到SandBox Setting



	using namespace Rainbow::Setting;
	GameConfigMgr* gameCfgMgr = GameConfigMgr::GetInstance();
#ifndef BUILD_MINI_EDITOR_APP
	ClientPlayer::SetViewRangeSetting(gameCfgMgr->GetVisionDistance());
	MNSandbox::Core::SystemSetChange();
#endif
	int sandboxShadow = -1;
	int sandboxWaterRelect = -1;
	GlobalNotify::GetInstance().GetShadowAndWaterReflected.Emit(sandboxShadow, sandboxWaterRelect);

	SetSandboxGraphicQuality(gameCfgMgr->GetGraphicsQuality(), false);

	gameCfgMgr->ApplyLOD();
	
	//水面焦散
	int waterSurfaceCaustics =(int)gameCfgMgr->GetWaterSurfaceCaustics();
	GetSandboxRenderSetting().SetCausticsEnable(waterSurfaceCaustics>0);

	//动态天空
	int skyLevel = (int)gameCfgMgr->GetDynamicSkyLevel();
#ifdef BUILD_MINI_EDITOR_APP
	skyLevel = 2;
#endif
	GetSandboxRenderSetting().SetSkyboxLevel((SkyboxLevel)skyLevel);
	//动态植被
	int dynamicVegatation = (int)gameCfgMgr->GetDynamicVegetation();
	GetSandboxRenderSetting().SetGrassAnimationEnable(dynamicVegatation > 0);
	
	//兼容studio,如果在studio里有成功重载配置文件就使用配置文件的开关
	bool useCfg = true;
#ifdef BUILD_MINI_EDITOR_APP
	useCfg = gameCfgMgr->GetIsStudioCfg();
#endif

	if (useCfg)
	{
		//实时阴影
		int realTimeShadow = (int)gameCfgMgr->GetRealTimeShadows();
		bool shadowOpen = realTimeShadow > 0;
		WorldRenderer::m_CurShadowOpen = shadowOpen;
		WorldRenderer::SetShadowEnable(shadowOpen);

		//阴影配置新沙盒并且有配置重载的情况
		if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame() && gameCfgMgr->GetIsStudioCfg())
		{
			auto setting = Rainbow::GetSandboxRenderSettingPtr();
			if (setting)
			{
				auto shadowCfg = gameCfgMgr->GetShadowCfg();
				if (shadowCfg.m_nDistance > 0 && shadowCfg.m_nCascadeLevel > 0)
				{
					setting->SetShadowDistance(shadowCfg.m_nDistance);
					setting->SetShadowNumCascade(shadowCfg.m_nCascadeLevel);
					GetGraphicsQualitySetting().OnSceneRendererTextureDirty();
				}
			}
		}


		//水面反射
		int waterReflect = (int)gameCfgMgr->GetWaterReflection();
		bool isUGCCustomSky = false;
		if (m_PlayerCtrl)
		{
			World* pWorld = m_PlayerCtrl->getWorld();
			if (pWorld && pWorld->getRender())
			{
				WorldRenderer *pRender = pWorld->getRender();
				isUGCCustomSky = pRender->getIsUGCCustomSky();
			}
		}

		if (!isUGCCustomSky)
		{
			GetPlanarReflection().SetEffectEnable(waterReflect > 0);
		}

		//抗锯齿
		int antAliasing = (int)gameCfgMgr->GetAntiAliasing();
		GetSandboxRenderSetting().SetAntialiasingEnable(antAliasing > 0);
		//滤镜
		int lut = gameCfgMgr->GetLutFilter();
		GetSandboxRenderSetting().SetLUTEnable(lut > 0);

		//Bloom
		int bloom = (int)gameCfgMgr->GetBloom();
		GetSandboxRenderSetting().SetBloomEnable(bloom > 0);
		////HDR
		int hdr = (int)gameCfgMgr->GetHDR();
		GetSandboxRenderSetting().SetPostporcessEnable(hdr > 0);

		//雾效
		int fogEffect = (int)gameCfgMgr->GetFogEffect();
		WorldRenderer::m_CurFogIndex = fogEffect;
		GetRenderSetting().GetFogConfig().m_FogType = (FogType)fogEffect;

		//体积光
		GetSandboxRenderSetting().SetGodrayEnable(gameCfgMgr->GetVolumetricLights() > 0);
	}
	else
	{
		//实时阴影
		int realTimeShadow = sandboxShadow;
		bool shadowOpen = realTimeShadow > 0;
		WorldRenderer::m_CurShadowOpen = shadowOpen;
		WorldRenderer::SetShadowEnable(shadowOpen);

		//水面反射
		int waterReflect = sandboxWaterRelect;
		GetPlanarReflection().SetEffectEnable(waterReflect > 0);
		
		//雾效
		int fogEffect = (int)gameCfgMgr->GetFogEffect();
		if (g_WorldMgr && g_WorldMgr->isNewSandboxNodeGame() && WorldRenderer::m_CurFogIndex > 0)// studio设置
		{
			fogEffect = Rainbow::FogType::kFogTypeLinear;
		}
		GetRenderSetting().GetFogConfig().m_FogType = (FogType)fogEffect;
	}
	////SSAO
	//gameCfgMgr->GetSSAO();
	////景深
	//gameCfgMgr->GetDepthOfield();
	////贴图质量
	//gameCfgMgr->GetTextureQuality();
	////模型质量
	//gameCfgMgr->GetModelQuality();
	////特效质量
	//gameCfgMgr->GetEffectQuality();
	////材质质量
	//gameCfgMgr->GetMaterialQuality();
	////视野配置
	//gameCfgMgr->GetVisionCfg();
	////分辨率
	//gameCfgMgr->GetResolutionLevel();
	////帧率
	//gameCfgMgr->GetLimitFrameRate();
	
	//Root::getSingleton().saveFile();
}

void SurviveGame::applyScreenBright()
{
	XMLNode node = GetIWorldConfigProxy()->getRootNode().getChild("GameData");
	if(!node.isNull())
	{
		XMLNode child = node.getChild("Settinig");
		if (!child.isNull())
		{
			float brightness = child.attribToInt("screenbright") / 100.0f;
			if (brightness != s_bright)
			{
				s_bright = brightness;
				SetScreenBrightness(brightness);
			}
		}
	}
}

bool SurviveGame::load()
{
	ClientGame::load();

	GetGameInfoProxy()->SetMinimapRenderMode(0);
	g_BlockMtlMgr.clearOnEndGame();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("MapMarkingMgr_clear", SandboxContext(nullptr));
	m_TickCount = 0;

	return true;
}

void SurviveGame::setupPlayerAndVM()
{
	LOG_INFO("setupPlayerAndVM0");
	m_PlayerCtrl = createPlayerControl();
	m_PlayerCtrl->setEnterTs(time(0));
	bool isAccountData = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("ClientAccountMgr_isAccountData", isAccountData);
	if (isAccountData) return;
	if (m_PlayerCtrl == NULL) return;
	const ROLEINFO &roleinfo = GetClientInfoProxy()->getAccountInfo()->RoleInfo;
	int rolemodel = roleinfo.Model;
	//if (rolemodel == 0) rolemodel = 2;//修改默认模型为卡卡ID为2，之前是酋长ID是1，和存档界面保持一致 fixby：hexianggui
	//int geniuslv = GetClientInfoProxy()->getGenuisLv(rolemodel);
	//if (geniuslv < 0) geniuslv = 0;

	int playerindex = ComposePlayerIndex(rolemodel, 0, roleinfo.SkinID);
	int nindex = -1;
	MNSandbox::GetGlobalEvent().Emit<int&>("ClientAccountMgr_playerindex", nindex);
	if (nindex >= 0)
	{
		playerindex = nindex;
		GetClientInfoProxy()->getAccountInfo()->RoleInfo.Model = playerindex;
	}

	m_PlayerCtrl->init(GetClientInfoProxy()->getUin(), roleinfo.NickName, playerindex, roleinfo.CustomSkin);

	m_PlayerCtrl->setVipInfo(GetClientInfoProxy()->getAccountVipInfo());

	int w, h = 0;
	GetClientInfoProxy()->getClientWindowSize(w, h);
	m_PlayerCtrl->getCamera()->setScreenSize(w, h);

	MINIW::ScriptVM *scriptvm = MINIW::ScriptVM::game();
	scriptvm->setUserTypePointer("ClientBackpack", "BackPack", m_PlayerCtrl->getBackPack());
	scriptvm->setUserTypePointer("ClientCraftingQueue", "CraftingQueue", m_PlayerCtrl->getCraftingQueue());
	scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", m_PlayerCtrl->getAttrib());
	scriptvm->setUserTypePointer("CurMainPlayer", "PlayerControl", m_PlayerCtrl);
	LOG_INFO("setupPlayerAndVM1");
}

//#define DEBUG_WORLDGEN
#ifdef DEBUG_WORLDGEN
#include "OgreMD5.h"

void GetChunkData(unsigned short *buffer, Chunk *pchunk)
{
	int count = 0;
	for (int y = 0; y < 128; y++)
	{
		for (int z = 0; z < 16; z++)
		{
			for (int x = 0; x < 16; x++)
			{
				buffer[count++] = (unsigned short)pchunk->getBlock(x, y, z)->getAll();
			}
		}
	}
}
void TestChunkGen()
{
	FILE *fp = fopen("log/chunkgenxyz.txt", "wt");
	World *pworld = GetWorldManagerPtr()->getOrCreateWorld(0);
	int cx = BlockDivSection(-223);
	int cz = BlockDivSection(242);
	int r = 16;

	for (int x = cx - r; x <= cx + r; x++)
	{
		for (int z = cz - r; z <= cz + r; z++)
		{
			pworld->syncLoadChunk(x, z);
			//pworld->getChunkProvider()->createChunkData(chunkdata, biomes, x, z);
		}
	}

	unsigned short buffer[16 * 16 * 128];
	char tmp[16];
	char md5[33];
	for (int x = cx - r; x <= cx + r; x++)
	{
		for (int z = cz - r; z <= cz + r; z++)
		{
			Chunk *pchunk = pworld->getChunkBySCoord(x, z);
			GetChunkData(buffer, pchunk);

			MINIW::Md5Calc(tmp, (const char *)buffer, sizeof(buffer));
			MINIW::Md5ToHex(md5, tmp);

			fprintf(fp, "%d-%d: %s\n", x, z, md5);
		}
	}
	fclose(fp);
}
#endif

bool SurviveGame::initSurviveGame()
{	
	if (!m_uiHandler->isResInit())
	{
	}
	else if (m_WorldMgr == NULL)
	{
		WorldDesc *desc = GetClientInfoProxy()->getCurWorldDesc();
		if (desc == NULL) {
			LOG_INFO("SurviveGame::updateLoad desc is NULL");
			return false;//return -1;
		}

		m_WorldMgr = ENG_NEW(WorldManager)(desc);
		SetWorldManagerPtr(m_WorldMgr);
		//插件库使用的下载资源的信息先加载进来，用于判断资源能否加载
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_loadMapUseDownloadRes", SandboxContext(nullptr)
			.SetData_Number("_specialType", desc->_specialType)
			.SetData_Number("worldid", desc->worldid));

		//先将资源文件夹信息初始化，后面再做资源的动态加载
		if (ResourceCenter::GetInstancePtr())
		{
			ResourceCenter::GetInstancePtr()->initMapLibResourceManifest(desc->worldid, desc->_specialType);
		}

		MINIW::ScriptVM::game()->setUserTypePointer("WorldMgr", "WorldManager", m_WorldMgr);
		MINIW::ScriptVM::game()->callFunction("initWorldMgr", "");
		m_WorldMgr->onInit();

		MINIW::ScriptVM::game()->callFunction("ApplySurviveGameConfig", "");

		MINIW::ScriptVM::game()->callFunction("ClientShowWaterMark", "");
		desc->ForceOpentype = 0;

#ifdef DEBUG_WORLDGEN
		TestChunkGen();
#endif
	}
	else if (m_PlayerCtrl == NULL)
	{
		setupPlayerAndVM();
	}
	else
	{
		return true;
	}

	return false;
}


void SurviveGame::prepareTick()
{
	OPTICK_EVENT();
	++ m_TickCount;
	if (m_WorldMgr) m_WorldMgr->prepareTick();
}

void SurviveGame::suviveGameUnload(GAME_RELOAD_TYPE reloadtype)
{
	bool isTypeGameSurvive = (strcmp(getTypeName(), "SurviveGame") == 0);
	if (isTypeGameSurvive)
	{
		MNSandbox::GetGlobalEvent().Emit<>("AchievementManager_clear");
	}
	/*if (TaskSubSystem::GetTaskSubSystem())
		{
			TaskSubSystem::GetTaskSubSystem()->Clear();
		}*/
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr);
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_Clear", sContext);
	}
}

void SurviveGame::unload(GAME_RELOAD_TYPE reloadtype/* =NO_RELOAD */)
{
	ENTRYMAPCOST_STEP("Begin SurviveGame::unload");

	long long mapId = GetWorldManagerPtr() ? GetWorldManagerPtr()->getWorldId() : 0;
	int64_t ts = GetWorldManagerPtr() ? GetWorldManagerPtr()->getCurrentTimeStamp() : 0;
	std::map<std::string, GameAnalytics::Value> eventData{
		{"room_id", GameAnalytics::Value(m_serverRoomId)},
		{"map_id", GameAnalytics::Value(std::to_string(mapId))},
		{"ts", GameAnalytics::Value(ts)},
		{"duration", GameAnalytics::Value(ts - m_BeginGameTs)}
	};
	GameAnalytics::TrackEvent("game_session_end", eventData);	

	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GE_SURVIVE_GAME_UNLOAD", MNSandbox::SandboxContext());

	GlobalNotify::GetInstance().NotifyGameToClose();

	// 先切断事件输入，后释放对象
	if (m_PlayerCtrl)
	{
		m_PlayerCtrl->release();
		m_PlayerCtrl = NULL;
	}
	g_pPlayerCtrl = NULL;
	SetIPlayerControl(nullptr);
	ModelComponent::ClearAsyncTask();

	ClientGame::unload(reloadtype);

	clearScript();
	suviveGameUnload(reloadtype);

#ifdef BUILD_MINI_EDITOR_APP
	if (editorControl)
	{
		editorControl->clearResSelection();
		SANDBOX_DELETE(editorControl);
	}
#endif //BUILD_MINI_EDITOR_APP

	ENG_DELETE(m_WorldMgr);
	SetWorldManagerPtr(NULL);

	bool ishost = GetClientInfoProxy()->getMultiPlayer()&GAME_NET_MP_GAME_HOST;
	if (reloadtype == NO_RELOAD) {
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("ModManager_unLoadCurMods", SandboxContext(nullptr).SetData_Bool("isMods", ishost));
	}
	g_BlockMtlMgr.clearOnEndGame();
	GetMusicManager().ReleaseRes();
#ifndef IWORLD_SERVER_BUILD
	GetClientInfoProxy()->FmodSoundSystemEXReleaseRes();
#endif
	if (GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false))
	{
		GAMERECORD_INTERFACE_EXEC(recordStop(), (void)0);
	}

	ENTRYMAPCOST_STEP("End SurviveGame::unload");

#if USE_MEMORY_PROFILER
	GetDebugMgr().CleanObjectInstacneDestroyed();
#endif
}

/**
 * 将清理lua脚本中的变量状态抽离成函数, 客户端与云服都能调用
 * 云服无法进入m_uiHandler->unload()导致下列代码无法执行
 * <AUTHOR>
 * @date 2022.03.10
 */ 
void SurviveGame::clearScript()
{
	MINIW::ScriptVM *scriptvm = MINIW::ScriptVM::game();
	if (scriptvm)
	{
		scriptvm->setUserTypePointer("CurMainPlayer", "PlayerControl", NULL);
		scriptvm->setUserTypePointer("ClientBackpack", "BackPack", NULL);
		scriptvm->setUserTypePointer("ClientCraftingQueue", "CraftingQueue", NULL);
		scriptvm->setUserTypePointer("MainPlayerAttrib", "PlayerAttrib", NULL);
		scriptvm->callFunction("onWorldMgrRelease", "");
		scriptvm->callFunction("clearWorldMgr", "");
		scriptvm->setUserTypePointer("WorldMgr", "WorldManager", NULL);
		scriptvm->callFunction("clearDevWorld", "");
		GetSandboxActorSubsystem()->HandleLuaCurWorld(scriptvm, nullptr);
	}
}
void SurviveGame::updateMinimap()
{
	GameSuviveUiHandler* surviveUiHandler = dynamic_cast<GameSuviveUiHandler*>(m_uiHandler);
	if (surviveUiHandler)
	{
		surviveUiHandler->updateMinimap();
	}
}

void SurviveGame::world2RadarPos(int &x, int &z)
{
	int radarsteer = GetIWorldConfigProxy()->getGameData("radarSteering");

	WCoord dp = WCoord(x, 0, z) - m_PlayerCtrl->getPosition();
	float angle = 0;
	if (radarsteer == 0) angle = m_PlayerCtrl->getLocoMotion()->m_RotateYaw + 180;

	GameSuviveUiHandler::CompassPointToScreen(dp, x, z, angle, false);
}

int SurviveGame::world2RadarDist(int d)
{
	return CompassDistToScreen(d);
}

void SurviveGame::setCameraDepth(float n, float f)
{
#ifndef DEDICATED_SERVER
	CameraManager::GetInstance().getEngineCamera()->SetNear(n);
	CameraManager::GetInstance().getEngineCamera()->SetDepth(f);
#endif
}

void SurviveGame::updateRadarmap()
{
	GameSuviveUiHandler* surviveUiHandler = dynamic_cast<GameSuviveUiHandler*>(m_uiHandler);
	if (surviveUiHandler)
	{
		surviveUiHandler->updateRadarmap();
	}
}

void SurviveGame::tick()
{
	SANDBOXPROFILING_FUNC("SurviveGame::tick");
	if (m_WorldMgr)
	{
		m_WorldMgr->tick();
	}
	m_uiHandler->tick();

#ifdef BUILD_MINI_EDITOR_APP
	if (editorControl)
	{
		editorControl->tick();
	}
#endif //BUILD_MINI_EDITOR_APP
}

void SurviveGame::update(float dtime)
{
	//PROFINY_NAMED_SCOPE("SurviveGame::Update")

	if (m_WorldMgr)
	{
		m_WorldMgr->update(dtime);
	}
		
#ifdef BUILD_MINI_EDITOR_APP
	if (editorControl)
	{
		editorControl->update(dtime);
	}
	UInt32 dtick = TimeToTick(dtime);

	if (g_WorldMgr && g_WorldMgr->m_pEditorCbeScene && g_WorldMgr->m_pEditorCbeScene->isEnabled())
	{
		g_WorldMgr->m_pEditorCbeScene->update(dtick);
	}

	if (g_WorldMgr && g_WorldMgr->m_pEditorCmScene && g_WorldMgr->m_pEditorCmScene->isEnabled())
	{
		g_WorldMgr->m_pEditorCmScene->update(dtick);
	}

	if (g_WorldMgr && g_WorldMgr->m_pEditorBluePrintScene && g_WorldMgr->m_pEditorBluePrintScene->isEnabled())
	{
		g_WorldMgr->m_pEditorBluePrintScene->update(dtick);
	}
#endif //BUILD_MINI_EDITOR_APP
}

bool SurviveGame::isInGame()
{
	return true;
}

int SurviveGame::getGameStage()
{
	if (GetWorldManagerPtr() == NULL) return 0;
	if (GetWorldManagerPtr()->isGameMakerRunMode())
	{
		return (int)GetWorldManagerPtr()->m_RuleMgr->getGameStage();
	}

	return 0;
}

void SurviveGame::renderUI(bool isHide)
{
	if (!GetCoreUiIsHideUiCursor())
	{
		m_uiHandler->renderUI(isHide);
	}
	
	// 沙盒相关render UI
	GlobalNotify::GetInstance().m_RenderUI.Emit();

#ifdef BUILD_MINI_EDITOR_APP
	if (editorControl)
	{
		editorControl->renderUI();
	}
#endif //BUILD_MINI_EDITOR_APP
}

void SurviveGame::beginGame()
{

	ENTRYMAPCOST_STEP("SurviveGame::beginGame");

	LOG_INFO("SurviveGame beginGame0");

	long long mapId = GetWorldManagerPtr() ? GetWorldManagerPtr()->getWorldId() : 0;
	int64_t ts = GetWorldManagerPtr() ? GetWorldManagerPtr()->getCurrentTimeStamp() : 0;
	m_BeginGameTs = ts;
	std::map<std::string, GameAnalytics::Value> eventData{
		{"room_id", GameAnalytics::Value(m_serverRoomId)},
		{"map_id", GameAnalytics::Value(std::to_string(mapId))},
		{"ts", GameAnalytics::Value(ts)}
	};
	GameAnalytics::TrackEvent("game_session_start", eventData);

	GetClientGameJankReport().Start(mapId);
	GetClientGameJankReport().PrepareFirstReport();
	GlobalCallbacks::Get().endFrameTick.Register(&ClientGameJankReport::OnTick);

	GetClientGameStatistics().Start(mapId);
	GlobalCallbacks::Get().endFrameTick.Register(&ClientGameStatistics::OnTick);

	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode() && m_PlayerCtrl && GetWorldManagerPtr()->m_RuleMgr)
	{
		LOG_INFO("GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal");
		int defcam = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_CAMERA);
		//if (defcam < 3 || 8 == defcam)
		//{
		//	if (m_PlayerCtrl->getOPWay() == PLAYEROP_WAY_FOOTBALLER)
		//	{
		//		defcam = CameraControlMode::CAMERA_TPS_BACK;
		//	}
		//	else if (m_PlayerCtrl->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
		//	{
		//		defcam = CameraControlMode::CAMERA_TPS_BACK_2;
		//	}
		//	else if (m_PlayerCtrl->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
		//	{
		//		defcam = CameraControlMode::CAMERA_TPS_BACK;
		//	}
		//}
		//LOG_INFO("GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal2");
		//if ((defcam >= 0 && defcam < 3) || 8 == defcam || 9 == defcam)
		//{
		//	if (g_pPlayerCtrl && g_pPlayerCtrl->getOPWay() != PLAYEROP_WAY_FOOTBALLER && g_pPlayerCtrl->getOPWay() != PLAYEROP_WAY_BASKETBALLER && g_pPlayerCtrl->getOPWay() != PLAYEROP_WAY_PUSHSNOWBALL)
		//	{
		//		if (8 == defcam || 9 == defcam)
		//		{
		//			defcam = CAMERA_TPS_BACK_2;
		//		}
		//		m_PlayerCtrl->setViewMode(defcam);
		//	}
		//}
		//else if (defcam >= 3 && defcam <= 5)
		//{
		//	m_PlayerCtrl->setViewMode(defcam - 3);
		//}
		//else if (6 == defcam || 7 == defcam)
		//{
		//	m_PlayerCtrl->setViewMode(defcam - 2);
		//}
		//m_PlayerCtrl->setViewMode(defcam);
	}
	LOG_INFO("SurviveGame beginGame1");
	if (m_PlayerCtrl && m_PlayerCtrl->isDead())
	{
		m_PlayerCtrl->onDie();
	}
	LOG_INFO("SurviveGame beginGame2");
	if (m_PlayerCtrl)
	{
		LOG_INFO("SurviveGame GetClientInfoProxy()->setRenderCamera");
		GetClientInfoProxy()->setRenderCamera(CameraManager::GetInstance().getEngineCamera());
		//GetClientInfoProxy()->setRenderCamera(m_PlayerCtrl->getCamera()->getEngineCamera());
		if (m_PlayerCtrl->getWorld() != NULL) GetClientInfoProxy()->setRenderContent(m_PlayerCtrl->getWorld()->getScene());
	}
	GetGameEventQue().postBackpackChange(-1);

	if (m_PlayerCtrl) GetGameEventQue().postEnterWater(m_PlayerCtrl->m_DisplayOxygen >= 0);

	if (m_WorldMgr == NULL) return;
	GameMode *gmaker = static_cast<GameMode*>(m_WorldMgr->m_RuleMgr);

	bool isplayer = false;
	MNSandbox::GetGlobalEvent().Emit<bool&>("RoomManager_CurMultiPlayer", isplayer);
	if (gmaker && m_WorldMgr->isGameMakerRunMode() && gmaker->getGameStage() == CGAME_STAGE_PREPARE)
	{
		bool newplayerflag = m_PlayerCtrl ? m_PlayerCtrl->isNewPlayer() : true;
		// 云服主机 跳过开局介绍和队伍选择
		if (GetWorldManagerPtr() && GetWorldManagerPtr()->IsRentServerHost())
		{
			newplayerflag = false;
		}
		else
		{
			this->checkGameLeader(0, ROOM_ACTION_INIT);
		}
		GameMode *setterMgr = static_cast<GameMode*>(m_WorldMgr->getBaseSettingManager());
		if (newplayerflag && setterMgr && setterMgr->getNeedShowIntroFrame() && !m_WorldMgr->isRecordMode()) // 开局介绍
		{
			gmaker->setCustomGameStage(CGAME_STAGE_SHOWINTROS);
		}
		else if (newplayerflag && setterMgr && setterMgr->getNeedShowSelTeamFrame() && !m_WorldMgr->isRecordMode()) // 队伍选择
		{
			gmaker->setCustomGameStage(CGAME_STAGE_SELECTTEAM);
		}
		else
		{
#ifndef DEDICATED_SERVER

			if (gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 2)	//直接开启 没有倒计时
			{
				gmaker->onGameStart();
			}
			else if (gmaker->getRuleOptionVal(GMRULE_STARTMODE) == 1) //自动开启
			{
				// 开局需求人数不大于1
				if (!(isplayer && gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS) > 1))
				{
					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
				}
			}
			else //房主开启
			{
				// 单机房主自动开启
				if (!isplayer || MNSandbox::Config::GetSingleton().IsSandboxMode())
				{
					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
				}
				else if (Config::GetSingleton().IsSandboxMode())
				{
					// 如果是沙盒模式，自动开始游戏
					gmaker->setCustomGameStage(CGAME_STAGE_COUNTDOWN);
				}
#ifdef IWORLD_SERVER_BUILD
				if (ROOM_SERVER_OFFICIAL == GetClientInfoProxy()->getRoomHostType())
					GameEventQue::GetInstance().postSimpleEvent("GIE_WAITHOST_STARTGAME");
#endif
			}
#endif
		}
		LOG_INFO("gmaker->getRuleOptionVal(GMRULE_STARTMODE) end");
	}

	if (m_WorldMgr->isGameMakerRunMode() && m_WorldMgr->isRemote())
	{
		gmaker->callEventScript(WES_GAME_START_CLIENT);
		gmaker->RemoteReady();
	}

	// 云服服务器不在这里触发, 包括一键云服与租赁服
	#ifndef IWORLD_SERVER_BUILD
	//观察者事件接口
	//对应的功能：任意玩家进入游戏时	
	if(!m_WorldMgr->isRemote())
	{
		//玩法运行模式||主机`
		ObserverEvent_Player obevent(getHostUin());
		GetObserverEventManager().OnTriggerEvent("Game.AnyPlayer.EnterGame", &obevent);
	}
	#endif

	if (m_WorldMgr->isSurviveMode() && !isplayer)
	{
		WorldMapData *mapdata = m_WorldMgr->getMapData(1, false);
		if (mapdata && !mapdata->bosses.empty() && mapdata->bosses[0].hp <= 0)
		{
			m_WorldMgr->addUnlockItem(1);
			int state = 0;
			MNSandbox::GetGlobalEvent().Emit<int&,int,int>("AchievementManager_getAchievementState", state,m_PlayerCtrl->getObjId(), 1024);
			if (m_PlayerCtrl && state == 2)
			{
				const MonsterDef *mondef = GetDefManagerProxy()->getMonsterDef(3502);
				LOG_INFO(" GetDefManagerProxy()->getMonsterDef(3502)");
				m_PlayerCtrl->addAchievement(3, ACHIEVEMENT_KILLMOB, mondef->ID);
				m_PlayerCtrl->updateTaskSysProcess(TASKSYS_KILL_MOB, mondef->ID);
				m_PlayerCtrl->addOWScore(mondef->KillScore);
			}
		}
	}
	LOG_INFO("GetClientAccountMgr().getRoominfo()");
	ROOMINFO roominfo;
	MNSandbox::GetGlobalEvent().Emit<ROOMINFO &>("RoomManager_getRoominfo", roominfo);
	if ((GetClientInfoProxy()->getMultiPlayer() & GAME_NET_MP_GAME_HOST))
	{
		RoomClient *roomclient = GetGameNetManagerPtr()->getRoomClient();
		if (roomclient && roomclient->isLocked())
		{
			if (!roomclient->isLockedBeacuseOfNotAllowJoin())  //code-by dengpeng
			{
				roomclient->setLocked(false);
			}
			roomclient->updateRoomFlags(GetClientInfoProxy()->getUin(), gmaker->getGameStage());
		}
		LOG_INFO("gmaker && gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS)");
		if (gmaker && gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS) > roominfo.MaxPlayerCount) {
			GetGameEventQue().postInfoTips(6091, (int)gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS));
			LOG_INFO("postInfoTips(6091, gmaker->getRuleOptionVal(GMRULE_STARTPLAYERS)");
		}
	}

	if (g_pPlayerCtrl != nullptr)
	{
		if (GetClientInfoProxy()->isPC())
		{
			if (GetWorldManagerPtr() && !GetWorldManagerPtr()->isNewSandboxNodeGame())
			{
				g_pPlayerCtrl->setSightMode(true);
			}
		}
		g_pPlayerCtrl->changeViewModeByMsg();
	}

#ifdef BUILD_MINI_EDITOR_APP
	GetClientInfoProxy()->setRenderCamera(GetCurrentSandboxCamera());
	editorControl = SANDBOX_NEW(EditorControl, g_pPlayerCtrl, GetClientInfoProxy()->getHwnd());
#endif //BUILD_MINI_EDITOR_APP

	//应用Camera设置，用于处理CameraObject设置被覆盖的问题
	auto CameraObject = GetCurrentSandboxCameraObj();
	if (CameraObject)
	{
		CameraObject->ApplyCameraSetting();
	}
	
	
	LOG_INFO("SurviveGame beginGame end");

#if (OGRE_PLATFORM == OGRE_PLATFORM_WIN32 && defined(STUDIO_SERVER))
	//云服调试器就绪
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("DebuggerWriteStatus", SandboxContext(nullptr).SetData_Number("serverstatus", 1));
#endif
}

void SurviveGame::endGame(bool isreload/* =false */)
{
	GetClientGameJankReport().Stop();
	GlobalCallbacks::Get().endFrameTick.Unregister(&ClientGameJankReport::OnTick);

	GetClientGameStatistics().Stop();
	GlobalCallbacks::Get().endFrameTick.Unregister(&ClientGameStatistics::OnTick);

	MNSandbox::GetGlobalEvent().Emit<>("StatisticRainforest_PlayerExitGame");
	MNSandbox::GetGlobalEvent().Emit<>("StatisticTerrgen_PlayerExitGame");

	//存档数据保存-需在触发器释放前保存云变量
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_saveAllPlayerArchDataByLeave");

	if (m_WorldMgr) m_WorldMgr->end();

	if (!GetClientInfoProxy()->isMobile())
	{
#ifndef __PC_LINUX__			
		GetScreenManager().SetShowCursor(true);
#endif		
		GetScreenManager().SetLockCursor(CursorLockMode::kCursorNormal);
		
	}
	pauseGame();

	if (m_PlayerCtrl && m_PlayerCtrl->getWorld()) m_PlayerCtrl->getWorld()->getActorMgr()->ToCastMgr()->despawnActor(m_PlayerCtrl);

	#ifdef BUILD_MINI_EDITOR_APP
	if (editorControl)
	{
		editorControl->leaveWorld(false);
	}
	#endif //BUILD_MINI_EDITOR_APP

	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
	{
		MINIW::rmCrashTag("studioMap");
	}
}

void SurviveGame::pauseGame()
{
	//Studio工程里已经进行过一次保存
	#ifndef BUILD_MINI_EDITOR_APP
	if (m_WorldMgr) m_WorldMgr->save();
	#endif
}

//void SurviveGame::onGameEvent(GameEvent *ge)
//{
//	
//}

int SurviveGame::OnInputEvent(const Rainbow::InputEvent& event)
{
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("ClientGameUserInputEvent", MNSandbox::SandboxContext().SetData_UserObject("InputTag", event));

#ifdef DEDICATED_SERVER
	return 0;
#else
	if (g_isSurviveGameNull)  // TODO: 感觉没有必要？
		return INPUTMSG_HANDLED;

	const auto frame = GetClientApp().GetFrameCount();
	bool isEventHandle = false;
	int result = INPUTMSG_PASS;
	int ret = ClientGame::OnInputEvent(event);
	if (ret != INPUTMSG_PASS)
	{
		isEventHandle = true;
		result = ret;
	}
	else
	{
		switch (event.type)
		{
		case Rainbow::InputEvent::kKeyDown:
			if (isInModifyKey())
			{
				if (event.keycode > 0)
				{
					MINIW::ScriptVM::game()->callFunction("ModifyKey", "i", event.keycode);
					isEventHandle = true;
					result = INPUTMSG_HANDLED;
				}
			}
			else
			{
#ifdef IWORLD_DEV_BUILD
				if (event.keycode == SDLK_F12)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_F12", "");
					isEventHandle = true;
					result = INPUTMSG_HANDLED;
				}
				if (event.keycode == SDLK_F1)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_F1", "");
					isEventHandle = true;
					result = INPUTMSG_HANDLED;
				}
#endif
				// todo check (event.modifiers == Rainbow::InputEvent::kControl) it should be (event.modifiers & Rainbow::InputEvent::kControl)
				if (event.keycode == SDLK_r && event.modifiers == Rainbow::InputEvent::kControl && GetClientInfoProxy()->IsCurrentUserOuterChecker())  //?????????????????????????Ч
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_CtrlR", "");
					isEventHandle = true;
					result = INPUTMSG_HANDLED;
				}

				if ((event.keycode == SDLK_F11) || (event.keycode == SDLK_RETURN && event.modifiers == Rainbow::InputEvent::kAlt))
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_F11", "");
					isEventHandle = true;
					result = INPUTMSG_HANDLED;
				}
				if (event.keycode == SDLK_t && event.modifiers == InputEvent::kControl)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_CtrlQ", "");
				}
				if (event.keycode == SDLK_y && event.modifiers == InputEvent::kControl)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_CtrlW", "");
				}
				if (event.keycode == SDLK_u && event.modifiers == InputEvent::kControl)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_CtrlE", "");
				}
				if (event.keycode == SDLK_1 && event.modifiers == InputEvent::kControl)
				{
					MINIW::ScriptVM::game()->callFile("luascript/AIFireLord.lua");
				}
				else if (event.keycode == SDLK_i)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_I", "");
				}
				else if (event.keycode == SDLK_q)
				{
					MINIW::ScriptVM::game()->callFunction("AccelKey_Q", "");
				}
			}
			break;
		case Rainbow::InputEvent::kLostFocus:

			if (ClientGameManager::getInstance()->getCurGame()
				&& ClientGameManager::getInstance()->getCurGame()->isInGame()
				&& GetClientInfoProxy()->isPC())
				MINIW::ScriptVM::game()->callFunction("OnWindowLostFocus", "");

			break;
		case Rainbow::InputEvent::kGainFocus:

			break;
		}

		if (CameraManager::GetInstance().onInputEvent(event) == INPUTMSG_HANDLED)
		{
			isEventHandle = true;
			result = INPUTMSG_HANDLED;
		}
		//地图编辑一部分使用自己快捷键
		if (GetIWorldConfigProxy()->GetIsStartEdit() && GetIWorldConfigProxy()->onInputEvent(event) == INPUTMSG_HANDLED)
		{
			isEventHandle = true;
			result = INPUTMSG_HANDLED;
		}

		if (CustomModelPacking::GetInstance().isStartPacking() && CustomModelPacking::GetInstance().onInputEvent(event) == INPUTMSG_HANDLED)
		{
			isEventHandle = true;
			result = INPUTMSG_HANDLED;
		}
	}

	//焦点丢失与获取时的静音不应被屏蔽 By：Jeff 2022/12/26
	switch (event.type)
	{
	case Rainbow::InputEvent::kLostFocus:
		GetClientInfoProxy()->setPause(true);
		break;
	case Rainbow::InputEvent::kGainFocus:
		GetClientInfoProxy()->setPause(false);
		break;
	}

	if (GetClientInfoProxy()->isPC() && g_pPlayerCtrl && g_pPlayerCtrl->m_PCCtrl && 
		g_pPlayerCtrl->m_PCCtrl->isSightMode())
	{
		if (event.type == InputEvent::kMouseDown)
		{
			if (event.button == InputEvent::kLeftButton)
			{
				g_pPlayerCtrl->mouseEventTrigger(game::ch::PCMouseKeyType::left, game::ch::PCMouseEventType::down);
			}
			else if (event.button == InputEvent::kRightButton)
			{
				g_pPlayerCtrl->mouseEventTrigger(game::ch::PCMouseKeyType::right, game::ch::PCMouseEventType::down);
			}
			
		}
		else if (event.type == InputEvent::kMouseUp)
		{
			if (event.button == InputEvent::kLeftButton)
			{
				g_pPlayerCtrl->mouseEventTrigger(game::ch::PCMouseKeyType::left, game::ch::PCMouseEventType::up);
			}
			else if (event.button == InputEvent::kRightButton)
			{
				g_pPlayerCtrl->mouseEventTrigger(game::ch::PCMouseKeyType::right, game::ch::PCMouseEventType::up);
			}
		}
	}


	if (!g_ClientGameLive || m_PlayerCtrl == nullptr) return INPUTMSG_HANDLED;

#ifdef BUILD_MINI_EDITOR_APP
	{
#ifdef STUDIO_LINK_EDITOR
		MNSandbox::RebindEditorWindow();
#endif //STUDIO_LINK_EDITOR
		if (g_WorldMgr && g_WorldMgr->m_pEditorCbeScene && g_WorldMgr->m_pEditorCbeScene->isEnabled())
		{
			if (g_WorldMgr->m_pEditorCbeScene->onInputEvent(event) == INPUTMSG_HANDLED)
			{
				return INPUTMSG_HANDLED;
			}
		}

		if (g_WorldMgr && g_WorldMgr->m_pEditorCmScene && g_WorldMgr->m_pEditorCmScene->isEnabled())
		{
			if (g_WorldMgr->m_pEditorCmScene->onInputEvent(event) == INPUTMSG_HANDLED)
			{
				return INPUTMSG_HANDLED;
			}
		}
		/*	auto curScene = g_WorldMgr->getBluePrintCurScene();
			if (curScene != nullptr)
			{
				if (curScene->onInputEvent(event) == INPUTMSG_HANDLED)
				{
					return INPUTMSG_HANDLED;
				}
			}*/
		
		if (g_WorldMgr && g_WorldMgr->m_pEditorBluePrintScene && g_WorldMgr->m_pEditorBluePrintScene->isEnabled())
		{
			if (g_WorldMgr->m_pEditorBluePrintScene->onInputEvent(event) == INPUTMSG_HANDLED)
			{
				return INPUTMSG_HANDLED;
			}
		}

		//被老UI处理的鼠标弹起消息工具需要处理
		if (editorControl && ((ret == Rainbow::INPUTMSG_PASS) || (ret == Rainbow::INPUTMSG_HANDLED && (event.type == 1 || event.type == 0))))
		{
			if (editorControl->onInputEvent(event))
			{
				return INPUTMSG_HANDLED;
			}
		}
	}
#endif //BUILD_MINI_EDITOR_APP

	// 除UI外，不需要处理character消息
	if( (event.type == Rainbow::InputEvent::kKeyDown || event.type == Rainbow::InputEvent::kKeyUp)
		&& event.character > 0)
	{
		return result;
	}

	bool needForbiddenEvent = event.type == Rainbow::InputEvent::kKeyDown || event.type == Rainbow::InputEvent::kKeyUp
		|| event.type == Rainbow::InputEvent::kScrollWheel || event.type == Rainbow::InputEvent::kMouseDrag ||
		event.type == Rainbow::InputEvent::kMouseDown || event.type == Rainbow::InputEvent::kMouseUp;
	

	if(isEventHandle && needForbiddenEvent)
	{
		if (needForbiddenEvent)
			PlayerControl::SetAllowInput(false, event.type, event.keycode);
		PlayerControl::SetCurrentLogicFrame(frame);
		
		return result;
	}

	// 避免在一引擎帧内，存在多个游戏逻辑帧，导致的输入事件不能被正确地屏蔽(只能在第一逻辑帧被屏蔽)	
	if (frame == PlayerControl::GetCurrentLogicFrame())
	{
		//if ( !m_PlayerCtrl->GetAllowInput() )
		return m_PlayerCtrl->onInputEvent(event);
	}

	PlayerControl::SetAllowInput(true);
	return m_PlayerCtrl->onInputEvent(event);
#endif
}

void SurviveGame::ResetOperateState()
{
	m_uiHandler->ResetOperateState();
}

int SurviveGame::getCurOpenContanierIndex()
{
	if (m_PlayerCtrl == NULL)
		return -1;

	return m_PlayerCtrl->getOpenContainerBaseIndex();
}

void SurviveGame::setOperateUI(bool b)
{
	m_uiHandler->setOperateUI(b);
}

bool SurviveGame::isOperateUI()
{
	return m_uiHandler->isOperateUI();
}

int SurviveGame::getOperateUICount()
{
	return m_uiHandler->getOperateUICount();
}

void SurviveGame::sendChat(const char *content, int type/* =0 */, int targetuin/* =0 */, int language/* =1 */, const char* extend/*=""*/)
{
	m_netHandler->sendChat(content, type, targetuin, language, extend);
}

void SurviveGame::sendChatToSelf(const char* content, int type, int language)
{
	if (m_PlayerCtrl)
	{
		GetGameEventQue().postChatEvent(type, NULL, std::string(content).c_str(), m_PlayerCtrl->getUin(), language);
	}
}

bool SurviveGame::SetSigns(const char *content, int x, int y, int z)
{
	if (m_PlayerCtrl == NULL) return false;
	if (m_PlayerCtrl->getWorld() == NULL) return false;
	WCoord blockpos(x, y, z);
	m_PlayerCtrl->getWorld()->syncLoadChunk(blockpos, 5);
	WorldSignsContainer *container = dynamic_cast<WorldSignsContainer *>(m_PlayerCtrl->getWorld()->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		container->setText(content);
		return true;
	}

	return false;
}

PlayerControl *SurviveGame::getMainPlayer()
{
	return m_PlayerCtrl;
}

void SurviveGame::enableMinimap(bool b)
{
	GameSuviveUiHandler* h = dynamic_cast<GameSuviveUiHandler*>(m_uiHandler);
	if (h)
	{
		h->enableMinimap(b);
	}
}

int SurviveGame::getGameTimeHour(bool curworld)
{
	float daytime = 0;
	if (curworld)
	{
		if (m_PlayerCtrl && m_PlayerCtrl->getWorld())
		{
			daytime = m_PlayerCtrl->getWorld()->getHours();
		}
	}
	else if (GetWorldManagerPtr())
	{
		daytime = GetWorldManagerPtr()->getHours();
	}
	return int(daytime);
}

int SurviveGame::getGameTimeMinute(bool curworld)
{
	float daytime = 0;
	if (curworld)
	{
		if (m_PlayerCtrl && m_PlayerCtrl->getWorld())
		{
			daytime = m_PlayerCtrl->getWorld()->getHours();
		}
	}
	else if (GetWorldManagerPtr())
	{
		daytime = GetWorldManagerPtr()->getHours();
	}

	return	int((daytime - int(daytime)) * 60);
}

void SurviveGame::playEffect(int x, int y, int z, const char *ent, int index/* =0 */)
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	if (!m_PlayerCtrl || !m_PlayerCtrl->getWorld())
	{
		return;
	}

	char path[256];
	sprintf(path, "particles/%s", ent);
	WCoord pos = WCoord(x, y, z);

	/*WCoord pos = GetPlacePosition(m_PlayerCtrl);
	pos.y += BLOCK_SIZE/2;*/

	EffectParticle *effect = m_PlayerCtrl->getWorld()->getEffectMgr()->playParticleEffectAsync(path, pos, 0);


	if (effect) m_Effects[index] = effect;
}

void SurviveGame::playEffectVisibleBlock(int x, int y, int z, const char *ent, int index/* =0 */, bool sync2client /*= true*/, int visibledistblock /*= 16*/)
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	if (!m_PlayerCtrl || !m_PlayerCtrl->getWorld())
	{
		return;
	}

	char path[256];
	sprintf(path, "particles/%s", ent);
	WCoord pos = WCoord(x, y, z);

	/*WCoord pos = GetPlacePosition(m_PlayerCtrl);
	pos.y += BLOCK_SIZE/2;*/

	EffectParticle *effect = m_PlayerCtrl->getWorld()->getEffectMgr()->playParticleEffectAsync(path, pos, 0, 0.0f, 0.0f, sync2client, visibledistblock);


	if (effect) m_Effects[index] = effect;
}

void SurviveGame::playEffectInRoleFront(const char *ent)
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	Rainbow::Vector3f dir = Yaw2FowardDir(m_PlayerCtrl->getLocoMotion()->m_RotateYaw);
	WCoord pos = m_PlayerCtrl->getPosition() + WCoord(dir*(BLOCK_FSIZE*2.0f)) + WCoord(0, 20, 0);

	playEffect(pos.x, pos.y, pos.z, ent);
}

void SurviveGame::stopEffect(int index)
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	auto obj = m_Effects.find(index);;
	if (obj != m_Effects.end())
	{
		obj->second->setNeedClear();
		m_Effects.erase(obj);
	}
}

void SurviveGame::stopEffect()
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	//for(size_t i=0; i<m_Effects.size(); i++)
	//{
	//	m_Effects[i]->setNeedClear();
	//}
	//m_Effects.clear();

	auto iter = m_Effects.begin();
	for (; iter != m_Effects.end(); iter++)
	{
		iter->second->setNeedClear();
	}

	m_Effects.clear();
}

void SurviveGame::addmob(int mobid, const char *effect)
{
	if (!ClientGame::IsVaildGame(this))
	{
		return;
	}

	ClientActorMgr *actormgr = m_PlayerCtrl->getActorMgr();
	if (actormgr == NULL) return;

	//	WCoord pos = m_PlayerCtrl->getPosition();

	CollideAABB box;
	m_PlayerCtrl->getLocoMotion()->getCollideBox(box);
	//	box.pos.x -= 3*BLOCK_SIZE;
	//	box.pos.z += 5*BLOCK_SIZE;
	int ox = box.pos.x;
	int oz = box.pos.z;

	World *pworld = m_PlayerCtrl->getWorld();
	bool noCollision = false;
	for (int i = 10; i > 3; i--)
	{
		for (int x = -i; x <= i; x++)
		{
			box.pos.x = ox + x * BLOCK_SIZE;
			box.pos.z = oz + i * BLOCK_SIZE;

			if (pworld->checkNoCollisionBoundBox(box, m_PlayerCtrl))
			{
				noCollision = true;
				break;
			}
		}
		if (noCollision) break;
	}

	if (mobid >= 3502 && mobid <= 3504)
	{
		ActorDragon *actor = SANDBOX_NEW(ActorDragon);
		actor->init(mobid);
		actormgr->spawnActor(actor, box.pos, 0, 0);
	}
	else
	{
		ClientMob *mob = actormgr->spawnMob(box.pos, mobid, false, false);
		if (mob && effect)
		{
			mob->getBody()->playMotion(effect, 0, true);
		}

		if (mob) mob->playSaySound();
	}
}


PlayerBriefInfo *SurviveGame::getPlayerBriefInfo(int i)
{
	if (m_PlayerCtrl != nullptr) {
		myBriefInfo.teamid = m_PlayerCtrl->getTeam();
		myBriefInfo.cgamevar[0] = m_PlayerCtrl->getGameScore();
		myBriefInfo.cgamevar[1] = m_PlayerCtrl->getGameResults();
		myBriefInfo.skinid = GetClientInfoProxy()->getRoleSkinModel();
		myBriefInfo.model = GetClientInfoProxy()->getRoleModel();
		myBriefInfo.uin = GetClientInfoProxy()->getUin();
		myBriefInfo.inSpectator = m_PlayerCtrl->isInSpectatorMode();
		MyStringCpy(myBriefInfo.customjson, sizeof(myBriefInfo.customjson), GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin);
		MyStringCpy(myBriefInfo.nickname, sizeof(myBriefInfo.nickname), GetClientInfoProxy()->getNickName());
	}
	return &myBriefInfo;
}

void SurviveGame::setTeamScore(int teamid, int s)
{
	if (m_WorldMgr == NULL) return;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		m_WorldMgr->m_RuleMgr->setTeamScore(teamid, s);
	}
	m_WorldMgr->syncTeamScore(teamid);
}

int SurviveGame::getTeamScore(int teamid)
{
	if (m_WorldMgr == NULL) return 0;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		return m_WorldMgr->m_RuleMgr->getTeamScore(teamid);
	}
	else return 0;
}

void SurviveGame::setTeamResults(int teamid, int r)
{
	if (m_WorldMgr == NULL) return;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		m_WorldMgr->m_RuleMgr->setTeamResults(teamid, r);
	}
}

int SurviveGame::getTeamResults(int teamid)
{
	if (m_WorldMgr == NULL) return 0;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		return m_WorldMgr->m_RuleMgr->getTeamResults(teamid);
	}
	else return 0;
}

int SurviveGame::getNumTeam()
{
	if (m_WorldMgr == NULL) return 0;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		return m_WorldMgr->m_RuleMgr->getNumTeam();
	}
	else return 0;
}

void SurviveGame::getPlayers(std::vector<ClientPlayer *>&players, int teamid, int alive)
{
	if (m_WorldMgr == NULL) return;
	std::vector<IClientPlayer*> iplayers;
	m_WorldMgr->getAllPlayers(iplayers);

	for (auto it = iplayers.begin(); it != iplayers.end();)
	{
		auto player = (*it)->GetPlayer();
		if (teamid >= 0 || alive >= 0)
		{
			if ( (teamid >= 0 && player->getTeam() != teamid) || ( (alive == 0 && !player->isDead()) || (alive == 1 && player->isDead()) ) ) // alive(0表示存活，1表示阵亡)
			{
				it = iplayers.erase(it);
				continue;
			}
		}
		players.push_back(player);
		it++;
	}
}

ClientPlayer *SurviveGame::getRandomPlayer(int teamid, int alive)
{
	std::vector<ClientPlayer *>players;
	getPlayers(players, teamid, alive);

	if (players.empty()) return NULL;
	else return players[GenRandomInt(players.size())];
}

int SurviveGame::getNumPlayers(int teamid, int alive)
{
	std::vector<ClientPlayer *>players;
	getPlayers(players, teamid, alive);
	return players.size();
}

int SurviveGame::requireArrayOfPlayers(int teamid, int alive)
{
	m_TmpPlayers.resize(0);
	getPlayers(m_TmpPlayers, teamid, alive);
	return m_TmpPlayers.size();
}

int SurviveGame::requireArrayOfAllPlayers()
{
	if (m_WorldMgr == NULL) return 0;
	m_TmpPlayers.resize(0);
	std::vector<IClientPlayer*> iplayers;
	m_WorldMgr->getAllPlayers(iplayers);
	for (auto it = iplayers.begin(); it != iplayers.end();)
	{
		auto player = (*it)->GetPlayer();
		m_TmpPlayers.push_back(player);
		it++;
	}
	return m_TmpPlayers.size();
}

ClientPlayer *SurviveGame::getIthPlayerInArray(int i)
{
	if (i < 0 || i >= (int)m_TmpPlayers.size()) return NULL;
	else return m_TmpPlayers[i];
}

void SurviveGame::setPlayerVisibleDispayName(bool b)
{
	std::vector<ClientPlayer *>players;
	getPlayers(players, -1, -1);
	for (size_t i = 1; i < players.size(); i++)
	{
		if (players[i]->getBody())
			players[i]->getBody()->setVisibleDispayName(b);
	}
}

void SurviveGame::setPlayersResults(int teamid, int r)
{
	if (m_WorldMgr == NULL) return;
	if (m_WorldMgr->isGameMakerRunMode())
	{
		m_WorldMgr->m_RuleMgr->setPlayersResults(teamid, r);
	}
}

ClientPlayer *SurviveGame::getPlayerByUin(int uin)
{
	return m_PlayerCtrl;
}

bool SurviveGame::RentChangePlayerTeam(int uin, int teamid)
{
	//当前是租赁服房间 向服务器发送改变玩家队伍的消息
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		PB_CSChangePlayerTeamCH	   csChangePlayerTeamCH;
		csChangePlayerTeamCH.set_uin(uin);
		csChangePlayerTeamCH.set_teamid(teamid);

		GetGameNetManagerPtr()->sendToHost(PB_CLOUDSERVER_CHANGE_TEAM_CH, csChangePlayerTeamCH);
		return true;
	}
	return false;
}

bool SurviveGame::changePlayerTeam(int uin, int teamid, bool bResetAttr)
{
	if (teamid != 999 && teamid > getNumTeam())
		return false;

	ClientPlayer *player = getPlayerByUin(uin);
	if (player)
	{
		if (player->getTeam() == JUDGE_TEAM_ID && teamid != JUDGE_TEAM_ID)
		{
			UDPConnection * connect = GetGameNetManagerPtr()->getConnection();
			if (connect && connect->isMemberFull())
			{
				return false;
			}
			m_bHaveJudge = false;
			player->setTeam(teamid);
			//20210724: 触发器新API  codeby:wangshuai
			player->ClientPlayer::setSpectatorMode(SPECTATOR_MODE_NONE);

			if (player == g_pPlayerCtrl)
				MINIW::ScriptVM::game()->callFunction("SpectatorModeChange", "");

			MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", ""); //裁判不算在玩家人数里
		}
		if (teamid == JUDGE_TEAM_ID)
		{
			if (m_bHaveJudge)
				return false;
			player->ClientPlayer::setSpectatorMode(SPECTATOR_MODE_JUDGE);

			if (player == g_pPlayerCtrl)
			{
				MINIW::ScriptVM::game()->callFunction("SpectatorModeChange", "");
				player->setOperate(0); //取消挖掘状态
			}
			m_bHaveJudge = true;
			player->setTeam(teamid);

			MINIW::ScriptVM::game()->callFunction("OnChangeNumOfPlayers", ""); //裁判不算在玩家人数里
		}
		player->setTeam(teamid, bResetAttr);
		MINIW::ScriptVM::game()->callFunction("OnChangeTeam", "");

		return true;
	}

	return false;
}

void SurviveGame::resetGameRuleData()
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
	{
		GetWorldManagerPtr()->m_RuleMgr->reset();
	}
}

float SurviveGame::getRuleOptionVal(int ruleid)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->m_RuleMgr)
	{
		return GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal((GAMEMAKER_RULE)ruleid);
	}
	else return 0;
}
int SurviveGame::getPersonalRentLeftSeconds()
{
#ifdef IWORLD_SERVER_BUILD
	if (GetClientInfoProxy()->isRentType(1))
	{
		int initoktime = g_zmqMgr->GetInitokTime();
		if (initoktime > 0)
		{
			int maxSec = 3 * 3600;
			if (g_zmqMgr->personalroom_timeout_maxSec > 0)
				maxSec = g_zmqMgr->personalroom_timeout_maxSec;

			unsigned int now_ = Rainbow::Timer::getSystemTick();
			int ret_ = maxSec - (now_ / 1000 - initoktime);
			if (ret_ > 0)
				return ret_;
		}
	}
#endif	
	return 0;
}

bool SurviveGame::isLockViewMode()
{
	if (GetWorldManagerPtr()->m_RuleMgr)
	{
		return GetWorldManagerPtr()->m_RuleMgr->isLockViewMode();
	}
	
	if (m_PlayerCtrl)
		return m_PlayerCtrl->IsViewLock();

	return false;
}

void SurviveGame::showOperateUI(bool state)
{
	m_uiHandler->showOperateUI(state);
}
bool SurviveGame::getOperateUIState()
{
	return m_uiHandler->getOperateUIState();
}
void SurviveGame::HideTouchControlUi(bool bHide)
{
	m_uiHandler->HideTouchControlUi(bHide);
}

int SurviveGame::getHostUin()
{
	if (m_PlayerCtrl)
		return m_PlayerCtrl->getUin();

	return 0;
}

void SurviveGame::InviteJoinRoom(int uin, char* RoomState, char* PassWorld)
{
	GameSuviveNetHandler *handler = dynamic_cast<GameSuviveNetHandler*>(m_netHandler);
	if (handler)
	{
		handler->InviteJoinRoom(uin, RoomState, PassWorld);
	}
}

void SurviveGame::initNewShortcut(ClientPlayer *player)
{
	//新快捷栏
	PackContainer *pack1 = player->getBackPack()->getPack(SHORTCUT_START_INDEX_EDIT);
	//第1页
	pack1->addItemByCount(100, 1);
	pack1->addItemByCount(104, 1);
	pack1->addItemByCount(29, 1);
	pack1->addItemByCount(123, 1);
	pack1->addItemByCount(421, 1);
	pack1->addItemByCount(501, 1);
	pack1->addItemByCount(547, 1);
	pack1->addItemByCount(667, 1);

	//第2页
	pack1->addItemByCount(206, 1);
	pack1->addItemByCount(514, 1);
	pack1->addItemByCount(171, 1);
	pack1->addItemByCount(1374, 1);
	pack1->addItemByCount(520, 1);
	pack1->addItemByCount(1263, 1);
	pack1->addItemByCount(1305, 1);
	pack1->addItemByCount(1349, 1);

	//第3页
	pack1->addItemByCount(10500, 1);
	pack1->addItemByCount(10120, 1);
	pack1->addItemByCount(10121, 1);
	pack1->addItemByCount(10119, 1);
	pack1->addItemByCount(1000, 1);
	pack1->addItemByCount(1138, 1);
	pack1->addItemByCount(1151, 1);
	pack1->addItemByCount(10501, 1);
}

void SurviveGame::roleInit(WORLD_ID worldId, ClientPlayer *player)
{
	if (m_WorldMgr && m_WorldMgr->isGodMode())
	{
		if (!player->getBackPack()) 
		{
			return;
		}
		
		if (m_WorldMgr->isUGCEditBuildMode())
		{
			//新快捷栏
			initNewShortcut(player);

			//老快捷栏
			PackContainer *pack = player->getBackPack()->getPack(SHORTCUT_START_INDEX);
			pack->addItemByCount(104, 1);
			pack->addItemByCount(505, 1);
			pack->addItemByCount(101, 1);
			pack->addItemByCount(206, 1);
			pack->addItemByCount(207, 1);
			pack->addItemByCount(29, 1);
			pack->addItemByCount(100, 1);
			pack->addItemByCount(123, 1);
		}
		else
		{
			player->getBackPack()->addItem(104, 1);
			player->getBackPack()->addItem(505, 1);
			player->getBackPack()->addItem(101, 1);
			player->getBackPack()->addItem(206, 1);
			player->getBackPack()->addItem(207, 1);
			player->getBackPack()->addItem(29, 1);
			player->getBackPack()->addItem(100, 1);
			player->getBackPack()->addItem(123, 1);

			//新快捷栏
			initNewShortcut(player);
		}
	}
	else
	{
		if (worldId != NEWBIEWORLDID)
		{
			AddInitSurviveItems(player);
		}

		if (m_WorldMgr && m_WorldMgr->isSurviveMode() && player && player->getLivingAttrib())
		{
			player->getLivingAttrib()->addBuff(FIRSTENTERGAME_BUFF, 1);
		}
	}
}


void SurviveGame::initRoleInfo()
{
#ifdef VERSION_MINICODE
	ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_PlayerCtrl);
	WORLD_ID worldId = GetClientInfoProxy()->getCurWorldId();
	if (player && worldId != 0)
	{
		roleInit(worldId, player);
	}
#endif
}

int SurviveGame::findNearestPlayerByPos(float posx, float posy, float posz, int worldmapid)
{
	if (!m_WorldMgr)
		return 0;

	float minDist = FLT_MAX;
	int uin = 0;
	posx = posx * BLOCK_SIZE;
	posy = posy * BLOCK_SIZE;
	posz = posz * BLOCK_SIZE;

	std::vector<IClientPlayer*>players;
	m_WorldMgr->getAllPlayers(players);
	for (auto it = players.begin(); it != players.end(); ++it)
	{
		auto player = (*it)->GetPlayer();
		if(!player->getWorld() && player->getWorld()->getCurMapID() != worldmapid)
			continue;

		double dist = player->getSquareDistToPos(posx, posy, posz);
		if (dist < minDist)
			uin = (*it)->getUin();
	}

	return uin;
}

MNSandbox::AutoRef<PlayerControl> SurviveGame::createPlayerControl()
{
	//PlayerControlLua *playerControl = NULL;
	//GetCoreLuaDirector().CallFunctionM("PlayerControlLuaImpl", "CreatePlayerControl", ">u[PlayerControlLua]", &playerControl);

	//return playerControl;
	return PlayerControl::NewInstance();
}

void SurviveGame::transferToTargetMap(WCoord& pos, int mapid, bool isRocketTypeTeleport /*= false*/)
{
	if (m_PlayerCtrl)
	{
		ClientPlayer* player = dynamic_cast<ClientPlayer*>(m_PlayerCtrl);
		if (player)
		{
			m_WorldMgr->doActualTeleport(player, mapid, isRocketTypeTeleport);
			if (!isRocketTypeTeleport)
			{
				m_PlayerCtrl->gotoBlockPos(m_WorldMgr->getOrCreateWorld(mapid, player), pos, false);
			}
		}
	}
}

void SurviveGame::createNetHandler()
{
	if (!m_netHandler)
	{
		m_netHandler = ENG_NEW_LABEL(GameSuviveNetHandler,kMemGame);
		m_netHandler->setRoot(this);
	}
}

void SurviveGame::createLoadHandler()
{
	if (!m_loadHandler)
	{
		m_loadHandler = ENG_NEW_LABEL(GameSuviveLoadHandler, kMemGame);
		m_loadHandler->setRoot(this);
	}
}

void SurviveGame::createUiHandler()
{
	if (!m_uiHandler)
	{
		m_uiHandler = ENG_NEW_LABEL(GameSuviveUiHandler, kMemGame);
		m_uiHandler->setRoot(this);
	}
}

int SurviveGame::getGameLeaderUin()
{
	if (m_PlayerCtrl)
		return m_PlayerCtrl->getUin();

	return 0;
}

void SurviveGame::checkGameLeader(int playerUin, ROOM_ACTION roomAction)
{
}


bool SurviveGame::GetCoreUiIsHideUiCursor()
{
	auto gameroot = GetCurrentGameRoot();
	if (gameroot)
	{
		auto coreui = gameroot->GetServiceT<SandboxGameSetNode>().ToCast<SandboxGameSetNode>();
		if (coreui)
		{
			return coreui->GetHideUiCursor();
		}
	}

	return false;
}

void SurviveGame::OnLoaded()
{
	SANDBOX_LOG("Survive Game OnLoaded!");
	GlobalNotify::GetInstance().m_mapActive.Emit();

	long long mapId = GetWorldManagerPtr() ? GetWorldManagerPtr()->getFromWorldID() : 0;
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("mapid", mapId);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GE_SURVIVE_GAME_LOADED", sandboxContext);
}

int SurviveGame::updateLoad() //0: 继续load,  1: load完成，  -1网络错误
{
	return Super::updateLoad();
}

/**
 * @brief 获取当前游戏所有有效的队伍信息
 * 
 * @param teams_info vector<TeamInfo>& 返回值, 队伍信息
 * @return true 获取成功
 * @return false 获取失败
 */
bool SurviveGame::getEnableTeamsInfo(std::vector<TeamInfo>& teams_info)
{
	if (!m_WorldMgr ||!m_WorldMgr->m_RuleMgr)
		return false;
	
	teams_info.clear();
	std::vector<IClientPlayer *> players;
	std::map<int, vector<int>> team_map;
	m_WorldMgr->getAllPlayers(players);
	for (auto it = players.begin(); it!= players.end(); ++it)
	{
		auto player = (*it)->GetPlayer();
		team_map[player->getTeam()].push_back(player->getUin());
	}

	const auto &teams = m_WorldMgr->m_RuleMgr->getCurEnableTeams();
	for (auto it = teams.begin(); it!= teams.end(); ++it)
	{
		auto setter = dynamic_cast<TeamSetterComponent*>(m_WorldMgr->m_RuleMgr->getTeamSetter(*it));
		if (!setter)
			continue;
		teams_info.emplace_back(*it, setter->getTeamMaxPlayer(), team_map[*it]);
	}
	return true;
}

bool SurviveGame::loadAndPlaceBluePrint(const std::string& blueprintName, const WCoord& position)
{
    // 检查蓝图文件名是否有效
    if (blueprintName.empty())
        return false;

    World* currentWorld = m_WorldMgr->getWorld(0);
    
    // 构建完整的蓝图文件路径
    std::string blueprintFile = blueprintName;
	blueprintFile += ".vbp";

    // 加载蓝图
    UGCBluePrint* blueprint = ENG_NEW(UGCBluePrint)();
    bool loaded = false;
    
    // 尝试从不同路径加载蓝图
    std::string blueprintPath = "minigame/citybuild/assets/vbp/" + blueprintFile;
    blueprintPath = GetFileManager().ToFullPath(blueprintPath.c_str());
#ifdef DEDICATED_SERVER
	// TODO 临时处理
	blueprintPath = std::string("/data/rent_server/ver1.0/bin/") + blueprintPath;
#endif


    if (blueprint->Load(blueprintPath))
    {
        loaded = true;
    }

    if (!loaded)
    {
        ENG_DELETE(blueprint);
        return false;
    }
#if REPAIR_BUILD_CONTAINER
	else
	{
		std::string blueprintrewritePath = "minigame/citybuild/assets/rebuildvbp/" + blueprintFile;
		blueprint->Save(g_WorldMgr->getWorldId(), blueprintFile, ResBluePrint, true);
	}
#endif // REPAIR_BUILD_CONTAINER

	UGCBluePrintMgr::GetInstance()->DoBuildBluePrintByKeyForTrigger(position.x, position.y, position.z, blueprint, UGCBluePrintType::CustomBluePrint, 0, false, true);
    
    // 清理资源
    ENG_DELETE(blueprint);
    
    return true;
}
