#pragma once

#include "container_world.h"
#include "SandboxGame.h"

class EXPORT_SANDBOXGAME ContainerDecomposition : public WorldStorageBox //tolua_exports
{//tolua_exports
public:
    ContainerDecomposition();
    ContainerDecomposition(const WCoord& blockpos);
    virtual ~ContainerDecomposition();
    
    virtual void enterWorld(World* pworld) override;

    virtual void leaveWorld() override;

	virtual int getObjType() const override
	{
		return OBJ_TYPE_BOX;
	}

    virtual FBSave::ContainerUnion getUnionType() override
    {
        return FBSave::ContainerUnion_ContainerDecomposition;
    }

    virtual flatbuffers::Offset<FBSave::ChunkContainer> save(SAVE_BUFFER_BUILDER& builder) override;
    virtual bool load(const void* srcdata) override;

    //tolua_begin
    int GetDecompositionNum();
    int GetStorageNum();
    int GetStatus() const {
        return m_status;
    };

    void SetStatus(uint8_t val) { m_status = val; };

    float GetEfficiency() const { return m_efficiency; };
    float GetTimeinterval() const {return m_timeinterval;};

    //启动分解机
    void StartDecomposition();
    //停止分解机
    void StopDecomposition();
    //tolua_end

    void OnMessage(int type,int data);
protected:
    //一次分解
    void Process();
    void InitConfig();
    void StartTime(float delay, float interval);
    std::tuple<BackPackGrid*, int> GetFirstGrid();
    int AddItemToStorage(const GridCopyData& gridcopydata);

protected:
    uint8_t m_status;
    float m_efficiency;
    float m_timeinterval;
    uint64_t m_lasttime;

    MNSandbox::AutoRef<MNSandbox::MNTimer> m_timer;         //定时器
}; //tolua_exports