﻿#include "LockCtrlComponent.h"
#include "ClientPlayer.h"
#include "container_socdoor.h"
#include "container_socautodoor.h"
#include "world.h"
#include "GameNetManager.h"
#include "BlockDoor.h"
#include "CommonUtil.h"
#include "SandboxEventDispatcherManager.h"

#define NetType 2
#define LockCtrlAddLock 1
#define LockCtrlUpLock 2
#define LockCtrlUnLock 3
#define LockCtrlDeleLock 4
#define LockCtrlMakeKey 5

#define LockCtrlSetMainPassword 6
#define LockCtrlSetPassword 7
#define LockCtrlOpenUI 8
#define LockCtrlIsPasswdOpen 9

#define LockCtrlOnMessage 10
#define LockCtrlSetLastPasswd 11

#define KEYID 6000
#define KEYLOCK 6001
#define PASSWDLOCK 6002

IMPLEMENT_COMPONENTCLASS(LockCtrlComponent)
LockCtrlComponent::LockCtrlComponent() : _player(nullptr), _keyid(0), _haskey(false), _lastPasswd(0)
{

}

LockCtrlComponent::~LockCtrlComponent()
{

}

void LockCtrlComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);

	ClientPlayer* client_player = dynamic_cast<ClientPlayer*>(owner);
	if (!owner)
	{
		LOG_WARNING("check not owner");
		return;
	}

	_player = client_player;
}

void LockCtrlComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnLeaveOwner(owner);
}

void LockCtrlComponent::OnTick()
{

}

void LockCtrlComponent::visit(const BackPackGrid* grid)
{
	if (!grid)
		return;

	if (grid->m_ItemID != KEYID)
		return;

	try
	{
		if (stoi(grid->userdata_str) != _keyid)
			return;
	}
	catch (const std::exception&)
	{
		return;
	}

	_haskey = true;
}

bool LockCtrlComponent::TryAddLock(const WCoord& pos,int toolID)
{
	if (!Check(pos))
		return false;
	
    if (toolID == KEYLOCK)
    {
		AddLock(pos, 1);
		return true;
    }
    
    if (toolID == PASSWDLOCK)
    {
		AddLock(pos, 2);
		return true;
    }

    return false;
}

//加锁
void LockCtrlComponent::AddLock(const WCoord& pos, int type)
{
	if (!Check(pos))
		return;
	World* pWorld = _player->getWorld();

	if (!(type == 1 || type == 2))
		return;

	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlAddLock;
		obj << "lock_type" << type;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}
	
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;
	//已经上过锁了
	if (lock_data->type == 1 || lock_data->type == 2)
		return;

	//钥匙锁放置
	if (type == 1 && _player->getCurToolID() == KEYLOCK)
	{
		//消耗掉钥匙锁道具
		_player->shortcutItemUsed();

		lock_data->Reset();
		//主控是放置者
		lock_data->mainid = _player->getUin();
		//放置的时候随机生成一个id
		lock_data->lockid = rand();
		lock_data->type = type;
		UpdataBlock(pos);
		//上锁成功加个要上锁的提示
		SendClientMessage("please lock it");
	}

	// 密码锁放置
	if (type == 2 && _player->getCurToolID() == PASSWDLOCK)
	{
		//消耗掉钥匙锁道具
		_player->shortcutItemUsed();
		lock_data->Reset();
		lock_data->type = type;
		UpdataBlock(pos);
		//上锁成功加个要上锁的提示
		SendClientMessage("please lock it");
	}
}

//上锁
void LockCtrlComponent::UpLock(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlUpLock;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//钥匙锁 谁都可以上锁
	if (lock_data->type == 1)
	{
		lock_data->status = 1;
		UpdataBlock(pos);
		return;
	}

	//密码锁 只有主控列表可以上锁
	if (lock_data->type == 2 && HasMainList(pos))
	{
		lock_data->status = 1;
		UpdataBlock(pos);
		return;
	}
}

//解锁
void LockCtrlComponent::UnLock(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlUnLock;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//钥匙锁 只有有钥匙并且钥匙id和锁一致的人和主控可以解锁
	if (lock_data->type == 1)
	{
		//是主控 钥匙id和锁一致判断
		if (lock_data->mainid == _player->getUin() || HasKey(lock_data->lockid))
		{
			lock_data->status = 0;
			UpdataBlock(pos);
			return;
		}

		return;
	}

	//密码锁 只有主控列表的人可以解锁
	if (lock_data->type == 2 && HasMainList(pos))
	{
		lock_data->status = 0;
		UpdataBlock(pos);

		return;
	}
}

//移除
void LockCtrlComponent::DeleteLock(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlDeleLock;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//钥匙锁
	if (lock_data->type == 1 && lock_data->status == 0)
	{
		lock_data->Reset();
		UpdataBlock(pos);
		//把锁放背包
		if (!_player->gainItems(KEYLOCK, 1))
		{
			//TODO 失败处理
			//SendClientMessage("make key error");
		}
		return;
	}

	//密码锁
	if (lock_data->type == 2 && lock_data->status == 0)
	{
		lock_data->Reset();
		UpdataBlock(pos);
		//把锁放背包
		if (!_player->gainItems(PASSWDLOCK, 1))
		{
			//TODO 失败处理
		}
		return;
	}
}

//制作钥匙
void LockCtrlComponent::MakeKey(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	if (lock_data->type != 1)
	{
		LOG_WARNING("lock type error");
		return;
	}

	World* pWorld = _player->getWorld();
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlMakeKey;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//钥匙锁
	//没上锁都可以制作
	if (lock_data->status == 0)
	{
		//TODO 先校验材料够不够

		//制作钥匙把 id写进去
		if (_player->gainItemsUserdata(KEYID, 1, std::to_string(lock_data->lockid).c_str()) == 0)
		{
			//TODO 制作失败
			SendClientMessage("set make key error");
			return;
		}
		//TODO 成功的化消耗物品
	}
}

//设置主控密码
void LockCtrlComponent::SetMainPassword(const WCoord& pos, int password)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	if (lock_data->type != 2)
	{
		LOG_WARNING("lock type error");
		return;
	}

	World* pWorld = _player->getWorld();
	_lastPasswd = password;
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlSetMainPassword;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		obj << "passwd" << password;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	//访客密码和主控密码不能一样
	if (password == lock_data->lockpassword)
	{
		SendClientMessage("set passwd error");
		return;
	}

	//改主控密码,只有没上锁且没有主控密码和是主控列表的玩家才可以
	//没上锁没主控密码
	if (lock_data->status == 0 && lock_data->main_lockpassword == -1)
	{
		lock_data->main_lockpassword = password;
		lock_data->status = 1;
		lock_data->mainids.clear();
		lock_data->mainids.push_back(_player->getUin());
		UpdataBlock(pos);
		return;
	}

	//是主控列表玩家
	if (HasMainList(pos) && lock_data->main_lockpassword != password)
	{
		lock_data->main_lockpassword = password;
		lock_data->status = 1;
		lock_data->mainids.clear();
		lock_data->mainids.push_back(_player->getUin());
		UpdataBlock(pos);
		return;
	}
}

//设置访客密码
void LockCtrlComponent::SetPassword(const WCoord& pos, int password)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return;

	if (lock_data->type != 2)
	{
		LOG_WARNING("lock type error");
		return;
	}

	World* pWorld = _player->getWorld();
	_lastPasswd = password;
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlSetPassword;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		obj << "passwd" << password;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return;
	}

	if (!HasMainList(pos))
	{
		return;
	}
	//访客密码和主控密码不能一样
	if (password == lock_data->main_lockpassword)
	{
		SendClientMessage("set passwd error");
		return;
	}

	lock_data->lockpassword = password;
	UpdataBlock(pos);
}

void LockCtrlComponent::OpenBlock(const WCoord& pos)
{
	if (!Check(pos))
		return;
	World* pWorld = _player->getWorld();

	//1*2 2*2
	BlockSocDoor* socdoor = dynamic_cast<BlockSocDoor*>(pWorld->getBlockMaterial(pos));
	if (socdoor)
	{
		socdoor->openDoor(pWorld, pos, DIR_NOT_INIT, _player);
		return;
	}

	BlockSocAutoDoor *socautodoor = dynamic_cast<BlockSocAutoDoor*>(pWorld->getBlockMaterial(pos));
	if (socautodoor)
	{
		socautodoor->OpenDoor(pWorld, pos);
		return;
	}
}

void LockCtrlComponent::SendClientMessage(const std::string& str)
{
#ifdef IWORLD_SERVER_BUILD
	jsonxx::Object obj;
	obj << "type" << LockCtrlOnMessage;
	obj << "msg" << str;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#else
	CommonUtil::GetInstance().PostInfoTips(str);
#endif
}

void LockCtrlComponent::SetLastPasswd(int passwd)
{
	_lastPasswd = passwd;
#ifdef IWORLD_SERVER_BUILD
	jsonxx::Object obj;
	obj << "type" << LockCtrlSetLastPasswd;
	obj << "passwd" << passwd;

	PB_PlayerCustomHC protoHC;
	protoHC.set_type(NetType);

	protoHC.set_data(obj.json());

	GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#endif
}

bool LockCtrlComponent::Check(const WCoord& pos)
{
	if (!_player)
		return false;

	World* pWorld = _player->getWorld();
	if (!pWorld)
		return false;

	return true;
}

SocLock* LockCtrlComponent::GetSocLock(const WCoord& pos)
{
	if (!Check(pos))
		return nullptr;
	World* pWorld = _player->getWorld();

	SocDoorContainer* container = nullptr;

	//2*2双开门
	BlockSocDoubleDoor* socdoubledoor = dynamic_cast<BlockSocDoubleDoor*>(pWorld->getBlockMaterial(pos));
	if (socdoubledoor)
	{
		container = dynamic_cast<SocDoorContainer*>(socdoubledoor->getWorldContainer(pWorld, pos));
		if (!container)
			return nullptr;

		SocLock* lock_data = container->getLockData();
		if (!lock_data)
			return nullptr;

		return lock_data;
	}

	//1*2
	BlockSocDoor* socdoor = dynamic_cast<BlockSocDoor*>(pWorld->getBlockMaterial(pos));
	if (socdoor)
	{
		bool isupper, isopen, mirror;
		int placedir = socdoor->ParseDoorData(pWorld, pos, isupper, isopen, mirror);
		WCoord downPos = pos;
		if (isupper)
		{
			downPos = pos + WCoord(0, -1, 0);
		}

		container = dynamic_cast<SocDoorContainer*>(pWorld->getContainerMgr()->getContainer(downPos));
		if (!container)
			return nullptr;

		SocLock* lock_data = container->getLockData();
		if (!lock_data)
			return nullptr;

		return lock_data;
	}

	//1*3 扶梯门
	BlockSocAutoDoor* socautodoor = dynamic_cast<BlockSocAutoDoor*>(pWorld->getBlockMaterial(pos));
	if (socautodoor)
	{
		container = dynamic_cast<SocDoorContainer*>(pWorld->getContainerMgr()->getContainer(pos));
		if (!container)
			return nullptr;

		SocLock* lock_data = container->getLockData();
		if (!lock_data)
			return nullptr;

		return lock_data;
	}

	return nullptr;
}

bool LockCtrlComponent::IsOpen(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return false;

	World* pWorld = _player->getWorld();
	//没锁
	if (lock_data->type == 0)
	{
		return true;
	}
	//没上锁谁都可以开
	if (lock_data->status == 0)
		return true;

	//钥匙锁
	if (lock_data->type == 1)
	{
		//是主控 拥有钥匙key和锁一致的人
		if (lock_data->mainid == _player->getUin() || HasKey(lock_data->lockid))
			return true;

		return false;
	}

	//密码锁
	if (lock_data->type == 2)
	{
		//主控列表有玩家 其他情况都是要输入密码进入
		if (HasMainList(pos))
			return true;

        //打开UI
        jsonxx::Object obj;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;

        MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPasswdLockUI",
            MNSandbox::SandboxContext(nullptr)
            .SetData_String("data",obj.json()));

#ifdef IWORLD_SERVER_BUILD
		obj << "type" << LockCtrlOpenUI;

		PB_PlayerCustomHC protoHC;
		protoHC.set_type(NetType);

		protoHC.set_data(obj.json());

		GetGameNetManagerPtr()->sendToClient(_player->getUin(), PB_PLAYER_CUSTOM_HC, protoHC);
#endif

        return false;
	}

	return false;
}

bool LockCtrlComponent::IsPasswdOpen(const WCoord& pos, int passwd)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return false;
	World* pWorld = _player->getWorld();
	_lastPasswd = passwd;
	if (pWorld->isRemoteMode())
	{
		//发主机
		PB_PlayerCustomCH protoCH;
		protoCH.set_type(NetType);

		jsonxx::Object obj;
		obj << "type" << LockCtrlIsPasswdOpen;
		obj << "x" << pos.x;
		obj << "y" << pos.y;
		obj << "z" << pos.z;
		obj << "passwd" << passwd;

		protoCH.set_data(obj.json());

		GetGameNetManagerPtr()->sendToHost(PB_PLAYER_CUSTOM_CH, protoCH);

		return true;
	}

	//是主控密码,加入主控列表,下次不用在输入
	if (lock_data->main_lockpassword == passwd)
	{
		if (!HasMainList(pos))
			lock_data->mainids.push_back(_player->getUin());

		OpenBlock(pos);
		return true;
	}

	//是访客密码,可以开门
	if (lock_data->lockpassword == passwd)
	{
		OpenBlock(pos);
		return true;
	}

	SendClientMessage("passwd error");

	return false;
}

bool LockCtrlComponent::HasMainList(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return false;

	int find_uin = _player->getUin();
	auto find_it = std::find_if(lock_data->mainids.begin(), lock_data->mainids.end(), [find_uin](int uin) {
		return find_uin == uin;
	});

	return find_it != lock_data->mainids.end();
}

bool LockCtrlComponent::HasKey(int keyid)
{
	_keyid = keyid;
	_haskey = false;

	_player->getBackPack()->searchNormalPack(this);

	return _haskey;
}

bool LockCtrlComponent::IsOpenPieMenu(const WCoord& pos)
{
	SocLock* lock_data = GetSocLock(pos);
	if (!lock_data)
		return false;
		
	if (lock_data->type == 0)
	{
		return false;
	}

	return true;
}

void LockCtrlComponent::UpdataBlock(const WCoord& pos)
{
	if (!Check(pos))
		return;
	World* pWorld = _player->getWorld();

	//2*2
	BlockSocDoubleDoor* socdoubledoor = dynamic_cast<BlockSocDoubleDoor*>(pWorld->getBlockMaterial(pos));
	if (socdoubledoor)
	{
		bool isupper;
		bool isopen;
		bool mirror;
		int placedir;

		socdoubledoor->ParseDoubleDoorData(pWorld, pos, isupper, isopen, mirror, placedir);

		//同步客机4个方块状态
		pWorld->markBlockForUpdate(WCoord(pos.x, pos.y, pos.z));
		if (isupper)
		{
			pWorld->markBlockForUpdate(WCoord(pos.x, pos.y - 1, pos.z));
		}
		else
		{
			pWorld->markBlockForUpdate(WCoord(pos.x, pos.y + 1, pos.z));
		}

		if (!mirror)
		{
			WCoord left_pos = LeftOnPlaceDir(pos, placedir);
			pWorld->markBlockForUpdate(left_pos);
			if (isupper)
			{
				pWorld->markBlockForUpdate(WCoord(left_pos.x, left_pos.y - 1, left_pos.z));
			}
			else
			{
				pWorld->markBlockForUpdate(WCoord(left_pos.x, left_pos.y + 1, left_pos.z));
			}
		}
		else
		{
			WCoord right_pos = RightOnPlaceDir(pos, placedir);
			pWorld->markBlockForUpdate(right_pos);
			if (isupper)
			{
				pWorld->markBlockForUpdate(WCoord(right_pos.x, right_pos.y - 1, right_pos.z));
			}
			else
			{
				pWorld->markBlockForUpdate(WCoord(right_pos.x, right_pos.y + 1, right_pos.z));
			}
		}

		return;
	}

	//1*2
	BlockSocDoor* socdoor = dynamic_cast<BlockSocDoor*>(pWorld->getBlockMaterial(pos));
	if (socdoor)
	{
		bool isupper, isopen, mirror;
		int placedir = socdoor->ParseDoorData(pWorld, pos, isupper, isopen, mirror);

		//同步客机,目前更新的是soc 1*2
		int blockid = pWorld->getBlockID(pos.x, pos.y, pos.z);
		pWorld->setBlockAll(pos, blockid, pWorld->getBlockData(pos), 3, true);
		WCoord ng = pos + WCoord(0, isupper ? -1 : 1, 0);
		pWorld->setBlockAll(ng, blockid, pWorld->getBlockData(ng), 3, true);
		return;
	}

	BlockSocAutoDoor* socautodoor = dynamic_cast<BlockSocAutoDoor*>(pWorld->getBlockMaterial(pos));
	if (socautodoor)
	{
		pWorld->markBlockForUpdate(WCoord(pos.x, pos.y, pos.z));
		return;
	}
}

void LockCtrlComponent::OnNetMessage(const std::string& data)
{
	if (!_player)
		return;

	jsonxx::Object obj;
	obj.parse(data);

	if (obj.get<jsonxx::Number>("type") == LockCtrlAddLock)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int type = obj.get<jsonxx::Number>("lock_type");

		AddLock(WCoord(x,y,z), type);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlUpLock)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		UpLock(WCoord(x,y,z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlUnLock)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		UnLock(WCoord(x,y,z));
		return;
	}
    
	if (obj.get<jsonxx::Number>("type") == LockCtrlDeleLock)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		DeleteLock(WCoord(x,y,z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlMakeKey)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		MakeKey(WCoord(x,y,z));
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlSetMainPassword)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int passwd = obj.get<jsonxx::Number>("passwd");
		SetMainPassword(WCoord(x, y, z),passwd);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlSetPassword)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int passwd = obj.get<jsonxx::Number>("passwd");
		SetPassword(WCoord(x, y, z), passwd);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlOpenUI)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");

		jsonxx::Object obj;
		obj << "x" << x;
		obj << "y" << y;
		obj << "z" << z;

		MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("OpenPasswdLockUI",
			MNSandbox::SandboxContext(nullptr)
			.SetData_String("data", obj.json()));

		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlIsPasswdOpen)
	{
		int x = obj.get<jsonxx::Number>("x");
		int y = obj.get<jsonxx::Number>("y");
		int z = obj.get<jsonxx::Number>("z");
		int passwd = obj.get<jsonxx::Number>("passwd");
		IsPasswdOpen(WCoord(x, y, z), passwd);
		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlOnMessage)
	{
		SendClientMessage(obj.get<jsonxx::String>("msg"));

		return;
	}

	if (obj.get<jsonxx::Number>("type") == LockCtrlSetLastPasswd)
	{
		int passwd = obj.get<jsonxx::Number>("passwd");
		SetLastPasswd(passwd);
		return;
	}
}